<#if (messageFilter)??>
    {
        "from": ${messageFilter.startIndex},
        "size": ${messageFilter.count?string.computer},
        "query": {
            "bool": {
                "must": [
                    {
                        "term": {
                            "c_id": { "value": ${messageFilter.conversationId?c} }
                        }
                    }
                    <#if (messageFilter.params)??>
                    <#if (messageFilter.params.msg_type)??>,
                    {
                        "terms": {
                            "msg_type": ${messageFilter.params.msg_type}
                        }
                    }
                    </#if>
                    <#if (messageFilter.params.source)??>,                {
                    "term": {
                            "source": ${messageFilter.params.source}
                        }
                    }	
                    </#if>
                    <#if (messageFilter.params.m_id)??>,                {
                    "term": {
                            "m_id": ${messageFilter.params.m_id?c}
                        }
                    }	
                    </#if>
                    </#if>
                    <#if (messageFilter.startDate)??>,
                    {
                        "range": {
                            "cr_date": { "gte": "${messageFilter.startDate}" }
                            }
                        }
                    </#if>
                    ],
                "must_not": [
                    {
                        "terms": {
                            "messageType": [
                                "EVENTS", "ACTIVITY","REVIEW"
                            ]
                        }
                    }<#if (messageFilter.excludedSources)??>,
                    {
			          "terms": {
			            "source": [${messageFilter.excludedSources}]
			          }
                    }
                     </#if>
                ]
            }
        },
        "sort": {
            "cr_date": {
                "order":"desc"
            }
        }
    }
</#if>