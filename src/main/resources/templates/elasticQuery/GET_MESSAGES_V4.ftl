<#if (messageFilter)??>
{
    <#if messageFilter.count??>
    "size": ${messageFilter.count?string.computer},
    </#if>
    "query": {
        "bool": {
            "must": [
                {
                    "term": {
                        "messageType": "${messageFilter.params.messageType}"
                    }
                },
                {
                    "terms": {
                        "activityType": ${messageFilter.params.activityType}
                    }
                }
                 <#if (messageFilter.params.teamId)??>,
				 {
                    "term": {
                        "triggeredFor.userId": {
                            "value": ${messageFilter.params.teamId?c}
                        }
                    }
                }
                </#if>
                <#if (messageFilter.params.contactId)??>,
				 {
                    "term": {
                        "c_id": {
                            "value": ${messageFilter.params.contactId?c}
                        }
                    }
                }
                </#if>
            ]
        }
    }
}
</#if>