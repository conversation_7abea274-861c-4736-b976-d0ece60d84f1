#!/bin/bash
# ======================================================================================
# Title: inbox_health_check.sh
# Description: Inbox data flow sanity
# Author: <PERSON><PERSON><PERSON> <<EMAIL>>
# Date: 13th April, 2023
# Usage:
# 
# Dependencies:
#  -  jq
#  -  kafka-console-producer
#
# History:
# +----------+------------+------------------------------------------------------------+
# | [Date]   | [Author]   | [Modification description]                                 |
# +----------+------------+------------------------------------------------------------+
# | 13/04/23 | Tanmay P   | Intial Draft                                               |
# | 11/05/23 | Tanmay P   | - Remove calling receive email API and started using       |
# |          |            |   location email address with contact                      |
# |          |            | - Added a separate alert webhook endpoint for NEXUS flows  |
# | 05/06/23 | Tanmay P   | - Add support for facebook channel                         |
# | 09/06/23 | Tanmay P   | - Check past 15 min documents on ES index before           |
# |          |            |   publishing alert on delay                                |
# +----------+------------+------------------------------------------------------------+
#
# =======================================================================================

function usage {

	echo "Usage: "
	echo "$0 [-s <args>] |  [-f <arg>] | [-w <arg>] | [-a <arg>] | -i <args> | -k <arg>  | -h"
	echo ""
	echo "  -s <args>   Optional field, Skip list of channel from observation. E.g [-s \"SMS,EMAIL,FB,IG,GOOGLE\" ]"
	echo "  -f <arg>    Log file path"
	echo "  -w <arg>    Max wait time between retries"
	echo "  -a <arg>    Max number of attemps"
	echo "  -i <args>   List of dependencies required to install prior executing this script. E.g [ -i \"jq\"] "
	echo "  -k <arg>    Basic64 encode authorisation token, E.g. [-k c2FtcGxldG9rZW4==]"
	echo "  -h          print usage"
	exit 1
}

#------------------------------------------------
#   UTILITY FUNCTION
#------------------------------------------------

function validation {
	local commands=("jq" "kafka-console-producer")

	for command in "${commands[@]}"
	do
		if type $command >/dev/null 2>&1; then
			continue
		else
			echo "The command [${command}]  does not exist. You can install using following command :: ./$0 -i \"${command}\" "
			exit -1
		fi
	done

}

function get_commands {
	local commands=("$@") # for future reference, currently we only require `jq`

	local package_name="jq" 
	local os_name=$(uname -s)

	if [[ "$os_name" == "Linux" ]]; then
		if type apt-get >/dev/null; then
			if ! dpkg -s "$package_name" >/dev/null 2>&1; then
				sudo apt-get update && sudo apt-get install -y "$package_name"
			fi
		elif type -v yum >/dev/null; then
			if ! rpm -qa | grep -qw "$package_name"; then
				sudo yum install -y "$package_name"
			fi
		else
			error "Unsupported Linux distribution"
			exit -1
		fi
	elif [[ "$os_name" == "Darwin" ]]; then
		if ! brew ls --versions "$package_name" >/dev/null 2>&1; then
			brew install "$package_name"
		fi
	else
		error "Unsupported operating system"
		exit -1
	fi

}

function extract_data {
	local json_data="$@"
	local es_data=$(echo "${json_data}" | jq ".hits.hits[]._source" 2>/dev/null)

	local field
	if echo "$json_data" | jq 'has("messages")' 2>/dev/null | grep -q true; then
		field=$( echo "$json_data" | jq '.messages[].m_id' )
		if [[ "XX${field}" != "XXnull" ]]; then
			_M_ID=${field}
		fi
	fi
	if echo "$json_data" | jq 'has("mcId")' 2>/dev/null | grep -q true; then
		field=$(echo "$json_data "| jq ".mcId")
		if [[ "XX${field}" != "XXnull" ]]; then
			_MCID=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("b_id")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data "| jq ".b_id")
		if [[ "XX${field}" != "XXnull" ]]; then
			_BIZ_ID=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("lastUpdateDate")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".lastUpdateDate")
		if [[ "XX${field}" != "XXnull" ]]; then
			_LAST_UPDATED_TIME=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("cr_time")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".cr_time")
		if [[ "XX${field}" != "XXnull" ]]; then
			_CR_TIME=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("c_id")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".c_id")
		if [[ "XX${field}" != "XXnull" ]]; then
			_MCID=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("e_id")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".e_id")
		if [[ "XX${field}" != "XXnull" ]]; then
			_E_ID=${field}
		fi
	fi
	if echo "$es_data" | jq 'has("m_id")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".m_id")
		if [[ "XX${field}" != "XXnull" ]]; then
			_M_ID=${field}
		fi
	fi
	if echo "$json_data" | jq 'has("count")' 2>/dev/null | grep -q true; then
		field=$(echo "$json_data "| jq ".count")
		if [[ "XX${field}" != "XXnull" ]]; then
			_COUNT=${field}
		fi
	fi


	if echo "$es_data" | jq 'has("communicationDirection")' 2>/dev/null | grep -q true; then
		field=$(echo "$es_data" | jq ".communicationDirection")
		if [[ "XX${field}" != "XXnull" ]]; then
			_COM_DIRECTION=${field}
		fi
	fi

     # get-customer API handling
     if echo "$json_data" | jq 'map(has("id")) | all' 2>/dev/null | grep -q true; then
	     field=$(echo "$json_data "| jq ".[].id")
	     if [[ "XX${field}" != "XXnull" ]]; then
		     _C_ID=${field}
	     fi
     fi
     if echo "$json_data" | jq 'map(has("ecid")) | all'  2>/dev/null | grep -q true; then
	     field=$(echo "$json_data "| jq ".[].ecid")
	     if [[ "XX${field}" != "XXnull" ]]; then
		     _EC_ID=${field}
	     fi
     fi
     if echo "$json_data" | jq 'map(has("businessId")) | all'  2>/dev/null | grep -q true; then
	     field=$(echo "$json_data "| jq ".[].businessId")
	     if [[ "XX${field}" != "XXnull" ]]; then
		     _BIZ_ID=${field}
	     fi
     fi

    # mc-by-cId response handling
    if echo "$json_data" | jq 'has("id")' 2>/dev/null | grep -q true; then
	    field=$(echo "$json_data" | jq ".id")
	    if [[ "XX${field}" != "XXnull" ]]; then
		    _MCID=${field}
	    fi
    fi

}


# Define the logger function
function log {
	local level=$1
	local message=`echo $2 | tr -d '"'`
	local timestamp=$(date +"%Y-%m-%d %H:%M:%S")
	local methodname=$3

    # Output the log message to the console or a file
    if [ -z "$_LOG_FILE" ]; then
	    echo "[$timestamp] [$level] [$methodname] $message" | tee -a "$_ECHO_VAR"
	    #echo "[$timestamp] [$level] [$methodname] $message" 
    else
	    echo "[$timestamp] [$level] [$methodname] $message" | tee -a "$_ECHO_VAR" "$_LOG_FILE"
    fi
}

function debug {
	log "DEBUG" "$1" "${FUNCNAME[1]}"
}

function error {
	log "ERROR" "$1" "${FUNCNAME[1]}"
}


function __make_request {
	local url=$1
	local method=$2
	local body=$3
	local header_list=$(if [ "X$method" == "XPOST" ]; then echo "$4"; else echo "$3"; fi)

	local headers=""
	local curl_output=
	_API_RESPONSE=${curl_output}

	if ! echo "$header_list" | grep -q -i "Content-Type:"; then
		header_list+=("Content-Type: application/json")
	fi

	if [ ${#header_list[@]} -gt 1 ]; then
		for header in "${header_list[@]}"
		do
			if [ ! -z  "$header" ]; then
				headers+=$( echo -n " -H " && echo "'${header}'" )
			fi
		done
	fi

	local cmd="curl -m 10 -s --show-error -f ${headers} -X ${method} ${url}"

	if [[ ! -z ${body} && "X$method" == "XPOST" ]]; then
		cmd+=" -d '${body}' "
	fi 

	cmd+=" 2>&1 "

	curl_output=$(eval "${cmd}")

	if [ $? -ne 0 ]; then
		_API_RESPONSE="\nURL - ${url}\n Response - ${curl_output}"
		return 1
	fi 

	extract_data "${curl_output}"
	_API_RESPONSE=${curl_output}
	# echo "${curl_output}"
}

function executePost {
	__make_request "$1" "POST" "$2" "$3"
}

function executeGet {
	__make_request "${1}" "GET" "$2" 
}

function isInteger() {
	[[ $1 =~ ^[0-9]+$ ]]
}

function get_formatted_date {
	local os_name=$(uname -s)
	local epoch=$(expr $1 / 1000)

	if [[ "$os_name" == "Linux" ]]; then
		echo `TZ=GMT date -d@${epoch} +'%Y-%m-%d %H:%M:%S'`
	elif [[ "$os_name" == "Darwin" ]]; then
		echo `TZ=GMT date  -r ${epoch} +'%Y-%m-%d %H:%M:%S'`
	else
		echo "${epoch}"
	fi
}

function verify_callback_fn {
	local os_name=$(uname -s)
	local callback=$1

	case "$os_name" in 
		"Linux")
			if [ -z "$(type -t "$callback")" ] || [ "$(type -t "$callback")" != "function" ]; then
				error "Not a valid callback method - [${callback}] passed !!"
				return 1
			fi
		;;
		"Darwin")
			if [ ! "$(command -v "$callback")" ]; then
				error "Not a valid callback method - [${callback}] passed !!"
				return 1
			fi
		;;
		*)
			error "No handling done for OS - ${os_name}"
			return 1
		;;
	esac
	debug "[${callback}] is a valid callback method !!"
}

#------------------------------------------------
#   HELPER FUNCTION
#------------------------------------------------

function validate_skip_list {
	readonly search_strings=("SMS" "EMAIL" "FB" "IG" "GOOGLE" "APPLE")

	for i in "${!_SKIP_LIST[@]}"; do
		local found=false
		for j in "${search_strings[@]}"; do
			if [ "X${_SKIP_LIST[$i]}" == "X$j" ]; then
				found=true
				break
			fi
		done
		if [ "$found" = false ]; then
			error "Not a valid channel - ${_SKIP_LIST[$i]} provided for skiping, hence removing"
			unset _SKIP_LIST[$i]
		fi
		found=false
	done
}

function init_vars {
	readonly _SMS=1
	readonly _EMAIL=111
	readonly _GOOGLE=12
	readonly _IG=13
	readonly _FB=110
	readonly _APPLE=16
	readonly _ES_URL="https://prod-es.birdeye.com"
	readonly _BE_MSG_DOMAIN="http://messenger.birdeye.internal"
	readonly _BE_SOCIAL_DOMAIN="https://socialnotification.birdeye.com"
	readonly _BE_KONTACTO_DOMAIN="http://kontacto.birdeye.com"
	readonly _ACTIVITY_TYPE="Health Check Job Status"

	readonly _SMS_MC_ID=200313963

	#------Test RC Webhook -------
	# readonly _ALERT_WEBHOOK_URL="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.NDBLWzmeeCHl40UUMsBCqAWD6vAo4647unQmse5AMNs"
	# readonly _NEXUS_ALERT_WEBHOOK="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.NDBLWzmeeCHl40UUMsBCqAWD6vAo4647unQmse5AMNs"
	# readonly _SOCIAL_ALERT_WEBHOOK="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.NDBLWzmeeCHl40UUMsBCqAWD6vAo4647unQmse5AMNs"
	#-----------------------------

    #------Production RC Webhook -------
	readonly _ALERT_WEBHOOK_URL="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************.rkSvBk3OwUc0jrp-aNfBckrzKlonFnQcyiMd9wLRQH0"
    readonly _NEXUS_ALERT_WEBHOOK="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvdCI6ImMiLCJvaSI6IjEyNjAzODAxNjEiLCJpZCI6IjE5ODgxOTAyMzUifQ.NwMUql2sMkSUEppFTzz71Y704tBwIL7OGlbAo3u2YjQ"
	readonly _SOCIAL_ALERT_WEBHOOK="https://hooks.ringcentral.com/webhook/v2/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvdCI6ImMiLCJvaSI6IjEyNjAzODAxNjEiLCJpZCI6IjIwMzI0MTA2NTEifQ.cAxJ_H3wkuTHxp8hjRdmUUqdXoNQYo8dBDWni1PGqzk"
	#-----------------------------------
	readonly _SEND_API_SLA=30
    readonly _RECEIVE_API_SLA=300
    
    # Initialize variables
    _ECHO_VAR=$(mktemp)
    _SKIP_LIST=()
    _LOG_FILE=$1; shift
    _MAX_ATTEMPTS=$1; shift

    _INITIAL_WAIT=1
    _ATTEMPT=1
    _MAX_WAIT=$1; shift
    _EXPONENTIAL_FACTOR=2
    _MCID=
    _M_ID=
    _CR_TIME=
	_COM_DIRECTION=
    _LAST_UPDATED_TIME=
    _BIZ_ID=1097520
    _C_ID=
    _E_ID=
    _EC_ID=
    _COUNT=0
    _API_RESPONSE=''
    _KAFKA_SERVER="common-kafka1.birdeye.internal:9092,common-kafka2.birdeye.internal:9092,common-kafka3.birdeye.internal:9092,common-kafka4.birdeye.internal:9092,common-kafka5.birdeye.internal:9092"
    # _KAFKA_SERVER="demo-kafka-new.birdeye.internal:9092"
    _KAFKA_TOPIC="inbox-cron-monitoring"

    _ACC_ID=1089580

    _SMS_PROCESSING_DONE=false
    _EMAIL_PROCESSING_DONE=false
	_FB_PROCESSING_DONE=false
	_GMB_PROCESSING_DONE=false
	_INSTA_PROCESSING_DONE=false

    if [ "$#" -ge 1 ]; then
	    _SKIP_LIST=("$@")
	    validate_skip_list
    fi

    debug "Script is initialised with parameter passed !!"
}


function executor_wrapper {
	local attempt=1
	_ATTEMPT=1
	local wait_time=$1
	shift

	local callback=$1
	shift
	# local wait_time=$_INITIAL_WAIT
	
	verify_callback_fn $callback

	if [ $? -ne 0 ]; then
		return 0
	fi

	while true; do
		if "${callback}" "$@"; then
			debug "Operation succeeded"
			return 0
		fi

        # Check if we've exceeded the maximum number of attempts
        if [ $attempt -ge $_MAX_ATTEMPTS ]; then
            error "Operation failed after ${attempt} attempts"

            # if [ -e "$_ECHO_VAR" ]; then
            #     rm $_ECHO_VAR 2>/dev/null
            # fi 
            return 1
        fi

        # Wait before retrying
        debug "Operation failed, retrying in ${wait_time} seconds"
        sleep $wait_time

        # Increase the wait time for the next retry
        wait_time=$((wait_time * _EXPONENTIAL_FACTOR))
        echo "New wait time ${wait_time} after exponential factor ${_EXPONENTIAL_FACTOR}"
        if [ $wait_time -gt $_MAX_WAIT ]; then
            wait_time=$_MAX_WAIT
        fi

        attempt=$((attempt + 1))
        _ATTEMPT=${attempt}
    done    
}

function publishAlert {

	local event_at="NULL"
	if [ ! -z ${_CR_TIME} ]; then 
		# local date_str=$(TZ=GMT date -Iseconds -r $(expr $_CR_TIME / 1000))
		# event_at="${date_str:0:10} ${date_str:11:8}"
		event_at=$(get_formatted_date ${_CR_TIME})
	fi
	local created_at="NULL" #$(date +"%Y-%m-%d %H:%M:%S")
	if [ ! -z ${_LAST_UPDATED_TIME} ]; then 
		# local date_str=$(TZ=GMT date -Iseconds -r $(expr $_LAST_UPDATED_TIME / 1000))
		# created_at="${date_str:0:10} ${date_str:11:8}"
		created_at=$(get_formatted_date ${_LAST_UPDATED_TIME})
	fi
	local elapsed_time=0
	if [[ "X${event_at}" != "XNULL" &&  "X${created_at}" != "XNULL" ]]; then
		local time=`echo "scale=2; ($_LAST_UPDATED_TIME /1000) - ($_CR_TIME / 1000)" | bc`
		time+=" seconds"
		elapsed_time=$time
	fi
	local status="Failed"
	if [[ ! -z  "$5" ]]; then
		status="$5"
	fi

	local request="NULL"
	if [[ ! -z  "$4" ]]; then
		request=$(echo "$4" | jq tostring | cut -c 2- | rev | cut -c 2- | rev)
	fi 
	local operation=$1
	local dataflow=$2
	local result="$3"
	if echo "$result" | jq '.' >/dev/null 2>&1; then
		result=$(echo "$3" | jq tostring | cut -c 2- | rev | cut -c 2- | rev)
	fi

	local buffer=`cat "$_ECHO_VAR" |  sed '$!s/$/\\\\n/' | tr -d '\n' `
	local date=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

    local request_url="${_ALERT_WEBHOOK_URL}"
	if [[ ! -z  "$6" ]]; then
		request_url="$6"
	fi

	local body=$(cat << EndOfMessage
      {
	    "activity": "${_ACTIVITY_TYPE}",
	    "attachments": [
		{
		    "type": "AdaptiveCard",
		    "body": [
			{
			    "type": "TextBlock",
			    "size": "Large",
			    "weight": "Bolder",
			    "text": "Issue with ${operation} pipeline",
			    "spacing": "Medium",
			    "color": "Attention",
			    "fontType": "Default"
		    },
			{
			    "type": "ColumnSet",
			    "columns": [
				{
				    "type": "Column",
				    "items": [
					{
					    "type": "TextBlock",
					    "weight": "Bolder",
					    "text": "${dataflow}",
					    "wrap": true
				    },
					{
					    "type": "TextBlock",
					    "spacing": "None",
					    "text": "Published at {{TIME(${date})}}  < {{DATE(${date},SHORT)}} >",
					    "isSubtle": true,
					    "wrap": true
				    }
				    ],
				    "width": "stretch"
			    }
			    ]
		    },
			{
			    "type": "FactSet",
			    "facts": [
				{
				    "title": "Event At:",
				    "value": "${event_at}"
			    },
				{
				    "title": "ES Record Created At:",
				    "value": "${created_at}"
			    },
				{
				    "title": "Elapsed Time:",
				    "value": "${elapsed_time}"
			    },
				{
				    "title": "Status",
				    "value": "${status}"
			    }
			    ]
		    }
		    ],
		    "actions": [
			{
			    "type": "Action.ShowCard",
			    "title": "View Payload",
			    "card": {
				"type": "AdaptiveCard",
				"body": [
				    {
					"type": "TextBlock",
					"text": "**Request: **  ${request} ",
					"wrap": true
				},
				    {
					"type": "TextBlock",
					"text": "**Result: ** ${result}",
					"wrap": true
				}
				],
				"\$schema": "http://adaptivecards.io/schemas/adaptive-card.json"
			},
			    "style": "positive"
		    },
			{
			    "type": "Action.ShowCard",
			    "title": "Logs",
			    "card": {
				"type": "AdaptiveCard",
				"body": [
				    {
					"type": "TextBlock",
					"text": "** --- Console Log --- ** \n ${buffer}",
					"wrap": true
				}
				],
				"\$schema": "http://adaptivecards.io/schemas/adaptive-card.json"
			},
			    "style": "default"
		    }
		    ],
		    "\$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
		    "version": "1.5"
	    }
	    ]
    }

EndOfMessage
)

    executePost "${request_url}" "${body}"
}


function kafkaEventPublisher {
	if ! which kafka-console-producer >/dev/null; then
		debug "kafka-console-producer command does not exists"
		return 0
	fi 

	local message="{ \"message\" : \"processed\" }"
	echo "$message" |  kafka-console-producer --bootstrap-server ${_KAFKA_SERVER} --topic ${_KAFKA_TOPIC}

}



#------------------------------------------------
#   BUSINESS LOGIC
#------------------------------------------------
function getCustomer {
	local email=""
	local phone=""

	if [[ "X$1" == "XPHONE" ]]; then
		phone="${2}"
	else
		email="${2}"
	fi

	local body="{ \"phone\": \"${phone}\", \"emailId\": \"${email}\" }"

	debug "Querying Kontacto to get customer details with emailId - ${email} OR phone - ${phone} for account - ${_ACC_ID}"

	executePost "${_BE_KONTACTO_DOMAIN}/customer/get-customer/${_ACC_ID}" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"
}


function deleteContact {
	#local body="{ \"accountId\": 1089580, \"businessIds\": [ 1097520, 1089582 ], \"ecids\": [ ${_EC_ID} ]}"

	local body="{ \"id\": ${_EC_ID}}"

	debug "Going to delete contact with ecid - ${_EC_ID} and associated conversation ID - ${_MCID}"

	executePost "${_BE_KONTACTO_DOMAIN}/customer/external/deleteCustomer?accountId=1089580"  "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"
}


function resetContext {
    _MCID=
    _M_ID=
    _CR_TIME=
    _LAST_UPDATED_TIME=
    _EC_ID=
	_ATTEMPT=1
	_COUNT=0
}


function getConversationViaCid {
	executeGet "${_BE_MSG_DOMAIN}/messenger/mc-by-cId/${_C_ID}?businessId=${_ACC_ID}\&type=cId"
    if [ $? -ne 0 ]; then
        error "Fail to get conversation with CID - ${_C_ID} for account - ${_ACC_ID}"
    fi
}



function sendEmail {

	if [ "$_EMAIL_PROCESSING_DONE" = true ]; then
		debug "Send Email method already executed no need to re-run this."
		return 0;
	fi

	local body="{\"newContact\":{\"name\":\"Ring Central\",\"phone\":\"(*************\",\"emailId\":\"<EMAIL>\",\"location\":{\"id\":0,\"state\":\"\",\"city\":\"\",\"countryCode\":\"US\"}},\"sendMessage\":{\"body\":\"System Health Check | EMAIL\",\"fromBusinessId\":1097520,\"mediaUrls\":[]},\"userId\":2048688,\"source\":${_EMAIL}}"
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/add-and-send" "${body}"
	
	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Send SMS API failed}"
			publishAlert "EMAIL" "Email send pipeline blocked - Retry attempt exhausted for send API" "${response}" "${body}"
		else
			error "/messenger/add-and-send API failed with response ${_API_RESPONSE}"
			return 1
		fi
	fi

	debug "Send EMAIL to business Id - ${_BIZ_ID} and got M_ID - ${_M_ID} & MC_ID - {$_MCID}. Wait for 3 seconds now."

	sleep 3
	searchConversation "${_MCID}" "1097520"
	if [[ $? != 0 ||  -z ${_CR_TIME} ]]; then
		local response="${_API_RESPONSE:-No record found in conversation - $_MCID for message id - $_M_ID}"
		publishAlert "EMAIL" "Email send pipeline blocked - No conversation record found with M_C_ID - ${_MCID} in Business - 1097520" "${response}" "${body}"
		return 0
	fi
	debug "Send Message [EMAIL channel] for conversation - ${_MCID} and message id - ${_M_ID} seem to be ok !!"

	# we expect _CR_TIME & _LAST_UPDATED_TIME to be in milliseconds
	if [[ ! -z ${_CR_TIME} && ! -z ${_LAST_UPDATED_TIME} ]]; then
		local time_in_sec=`echo "scale=2; ($_LAST_UPDATED_TIME /1000) - ($_CR_TIME / 1000)" | bc`        
		if [[  $( echo "${time_in_sec} >= ${_SEND_API_SLA}" | bc -l ) != 0  ]]; then
			publishAlert "EMAIL" "Latency found in email send pipeline" "Elapse time [${time_in_sec}] is greater than ${_SEND_API_SLA} seconds in send channel" "${body}" "" "Pass With Exception"
		fi
	fi

	executor_wrapper ${_INITIAL_WAIT} getCustomer "EMAIL"  "<EMAIL>"
	
	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Get customer API failed}"
			publishAlert "EMAIL" "Email send pipeline blocked -  Retry attempt exhausted of API - /customer/get-customer/ for contact - <EMAIL> " "${response}" ""
			return 1	
		fi
	fi



    #Delete contacts 
    deleteContact
    debug "Deleted contact associated with location - 1097520. Reset context and wait for 60 seconds now."
    resetContext

    sleep 60
	executor_wrapper 60 getCustomer "EMAIL"  "<EMAIL>"
	
	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Get customer API failed}"
			error "Retry attempt exhausted of API - /customer/get-customer/ for contact - <EMAIL> with response - [${response}]"
			
			verifyConversationDelay "EMAIL" "RECEIVE"
			if [ $? -ne 0 ]; then
				publishAlert "EMAIL" "Email received pipeline blocked: no conversation record found in the past 15 minutes" "${response}" ""
				
				# Publish to Nexus Webhook
        		publishAlert "EMAIL" "Email received pipeline blocked: no conversation record found in the past 15 minutes " "${response}" "${body}" "" "${_NEXUS_ALERT_WEBHOOK}"
				return 0;
			fi
		fi
	fi


    # Get conversation Id from customer Id
    getConversationViaCid

    searchConversation "${_MCID}" "1089582"
    if [[ $? != 0 ||  -z ${_CR_TIME} ]]; then
	    local response="${_API_RESPONSE:-No record found in conversation - $_MCID for message id - $_M_ID}"

		verifyConversationDelay "EMAIL" "RECEIVE"
		if [ $? -ne 0 ]; then
			publishAlert "EMAIL" "Email received pipeline blocked - No conversation record <NAME_EMAIL> in Business - 1089582 " "${response}" "${body}"
        
        	# Publish to Nexus Webhook
        	publishAlert "EMAIL" "Email received pipeline blocked - No conversation record <NAME_EMAIL> in Business - 1089582 " "${response}" "${body}" "" "${_NEXUS_ALERT_WEBHOOK}"
		fi 
	   
	    return 0
    fi
    debug "Receive Message [EMAIL channel] for conversation  - ${_MCID} and message id - ${_M_ID} seem to be ok !!"

    # we expect _CR_TIME & _LAST_UPDATED_TIME to be in milliseconds
    if [[ ! -z ${_CR_TIME} && ! -z ${_LAST_UPDATED_TIME} ]]; then
        local time_in_sec=`echo "scale=2; ($_LAST_UPDATED_TIME /1000) - ($_CR_TIME / 1000)" | bc`        
        if [[  $( echo "${time_in_sec} >= ${_RECEIVE_API_SLA}" | bc -l ) != 0 ]]; then
            publishAlert "EMAIL" "Latency found in email receive pipeline" "Elapse time [${time_in_sec}] is greater than ${_RECEIVE_API_SLA} seconds in receive channel" "" "Pass With Exception"

            # Publish to Nexus Webhook 
            publishAlert "EMAIL" "Latency found in email receive pipeline" "Elapse time [${time_in_sec}] is greater than ${_RECEIVE_API_SLA} seconds in receive channel" "" "Pass With Exception" "${_NEXUS_ALERT_WEBHOOK}"
        fi
    fi

    #Delete contacts 
    deleteContact
    debug "Deleted contact associated with location - 1089582"

    resetContext
    _EMAIL_PROCESSING_DONE=true
    return 0;
}


function sendSMS {

	if [ "$_SMS_PROCESSING_DONE" = true ]; then
		debug "Send SMS method already executed no need to re-run this."
		return 0;
	fi

	# local body="{\"newContact\":{\"name\":\"Ring Central\",\"phone\":\"(*************\",\"emailId\":\"<EMAIL>\",\"location\":{\"id\":0,\"state\":\"\",\"city\":\"\",\"countryCode\":\"US\"}},\"sendMessage\":{\"body\":\"System Health Check | SMS\",\"fromBusinessId\":1097520,\"mediaUrls\":[]},\"userId\":2048688,\"source\":${_SMS}}"
	# executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/add-and-send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"
	

	# local body="{\"body\":\"System Health Check | SMS\",\"fromBusinessId\":1097520,\"mediaUrls\":[],\"ongoingPulseSurvey\":false,\"toCustomerId\":${_SMS_MC_ID},\"source\":${_SMS}}"
	# executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"


	local body="{\"newContact\":{\"name\":\"RC SMS LocA\",\"phone\":\"(*************\",\"emailId\":\"\",\"location\":{\"id\":0,\"state\":\"\",\"city\":\"\",\"countryCode\":\"US\"}},\"sendMessage\":{\"body\":\"System Health Check | SMS\",\"fromBusinessId\":1097520,\"mediaUrls\":[]},\"userId\":2048688,\"source\":${_SMS}}"
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/add-and-send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Send SMS API failed}"
			publishAlert "SMS" "SMS send pipeline blocked - Retry attempt exhausted for send API" "${response}" "${body}"
		else
			error "Send API for source [${_SMS}] failed with response ${_API_RESPONSE}"
			return 1
		fi
	fi

	debug "Send SMS to business Id - ${_BIZ_ID} and got M_ID - ${_M_ID} & MC_ID - {$_MCID}. Wait for 3 second now"

	sleep 5
	searchConversation "${_MCID}" "1097520"
	if [[ $? != 0 ||  -z ${_CR_TIME} ]]; then
		local response="${_API_RESPONSE:-No record found in conversation - $_MCID for message id - $_M_ID}"
		publishAlert "SMS" "SMS send pipeline blocked - No conversation record found with M_C_ID - ${_MCID} in Business - 1097520" "${response}" "${body}"
		return 0
	fi
	debug "Send Message [SMS channel] for conversation - ${_MCID} and message id - ${_M_ID} seem to be ok !!"

    # we expect _CR_TIME & _LAST_UPDATED_TIME to be in milliseconds
    if [[ ! -z ${_CR_TIME} && ! -z ${_LAST_UPDATED_TIME} ]]; then
	    local time_in_sec=`echo "scale=2; ($_LAST_UPDATED_TIME /1000) - ($_CR_TIME / 1000)" | bc`        
	    if [[  $( echo "${time_in_sec} >= ${_SEND_API_SLA}" | bc -l ) != 0 ]]; then
		    publishAlert "SMS" "Latency found in sms send pipeline" "Elapse time [${time_in_sec}] is greater than ${_SEND_API_SLA} seconds in send channel" "${body}" "Pass With Exception"
	    fi
    fi

	#  (*************
	executor_wrapper ${_INITIAL_WAIT} getCustomer "PHONE"  "(*************"
	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Get customer API failed}"
			publishAlert "SMS" "SMS send pipeline blocked -  Retry attempt exhausted of API - /customer/get-customer/ for contact - (************* " "${response}" ""
			return 1
		fi
	fi

    #Delete contacts 
    deleteContact
    debug "Deleted contact associated with location - 1097520. Reset context and wait for 60 seconds now"

    resetContext

    sleep 60
	# executor_wrapper 60 getCustomer "PHONE"  "(*************"
	executor_wrapper 60 getCustomer "PHONE" "(*************"
	
	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Get customer API failed}"

			error "Retry attempt exhausted of API - /customer/get-customer/ for contact - (************* with response - [${response}]"

			verifyConversationDelay "SMS" "RECEIVE"
			if [ $? -ne 0 ]; then
				publishAlert "SMS" "SMS received pipeline blocked: no conversation record found in the past 15 minutes" "${response}" ""
				
				# Publish to Nexus Webhook
        		publishAlert "SMS" "SMS received pipeline blocked: no conversation record found in the past 15 minutes " "${response}" "${body}" "" "${_NEXUS_ALERT_WEBHOOK}"
				return 0;
			fi
		fi
	fi


    # Get conversation Id from customer Id
    getConversationViaCid

    searchConversation "${_MCID}" "1089582"
    if [[ $? != 0 ||  -z ${_CR_TIME} ]]; then
	    local response="${_API_RESPONSE:-No record found in conversation - $_MCID for message id - $_M_ID}"
	   
	    verifyConversationDelay "SMS" "RECEIVE"
		if [ $? -ne 0 ]; then
			publishAlert "SMS" "SMS received pipeline blocked - No conversation record found for (************* in Business - 1089582 " "${response}" "${body}"

        	# Publish to Nexus Webhook
        	publishAlert "SMS" "SMS received pipeline blocked - No conversation record found for (************* in Business - 1089582 " "${response}" "${body}" "" "${_NEXUS_ALERT_WEBHOOK}"
		fi

	    return 0
    fi
    debug "Receive Message [SMS channel] for conversation - ${_MCID} and message id - ${_M_ID} seem to be ok !!"

      # we expect _CR_TIME & _LAST_UPDATED_TIME to be in milliseconds
      if [[ ! -z ${_CR_TIME} && ! -z ${_LAST_UPDATED_TIME} ]]; then
	      local time_in_sec=`echo "scale=2; ($_LAST_UPDATED_TIME /1000) - ($_CR_TIME / 1000)" | bc`        
	      if [[  $( echo "${time_in_sec} >= ${_RECEIVE_API_SLA}" | bc -l ) != 0 ]]; then
		      publishAlert "SMS" "Latency found in sms receive pipeline" "Elapse time [${time_in_sec}] is greater than ${_RECEIVE_API_SLA} seconds in receive channel" "" "Pass With Exception"

            # Publish to Nexus Webhook 
            publishAlert "SMS" "Latency found in sms receive pipeline" "Elapse time [${time_in_sec}] is greater than ${_RECEIVE_API_SLA} seconds in receive channel" "" "Pass With Exception" "${_NEXUS_ALERT_WEBHOOK}"
	      fi
      fi

    #Delete contacts 
    deleteContact
    debug "Deleted contact associated with location - 1089582"

    resetContext
    _SMS_PROCESSING_DONE=true
    return 0;
}

function monitorFBDataPipeline() {

	if [ "$_FB_PROCESSING_DONE" = true ]; then
		debug "Message already injected to fb webhook"
		return 0;
	fi

	checkCount "" "FACEBOOK" "1097520"
	local record_count=${_COUNT:-0}
	debug "Record count before processing - ${record_count}"

	local current_epoch=$(date +%s)
	local previous_epoch=$((current_epoch - 3))

	local randomMid=$(openssl rand -hex 32)
	local body="{\"object\":\"page\",\"entry\":[{\"id\":110088784460841,\"time\":${current_epoch},\"messaging\":[{\"timestamp\":${previous_epoch},\"message\":{\"mid\":\"mid_${randomMid}\",\"seq\":0,\"text\":\"System Health Check | Facebook\",\"attachments\":null,\"is_echo\":false,\"app_id\":null},\"sender\":{\"id\":\"9690010711039755\"},\"recipient\":{\"id\":\"110088784460841\"},\"read\":null,\"delivery\":null}],\"changes\":null}],\"event\":\"FACEBOOK_RECEIVE\"}"
	
	# Send message to FB webhook
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_SOCIAL_DOMAIN}/fb/notification/facebook" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Retry attempt exceeded for API - /fb/notification/facebook}"
			publishAlert "FB" "FB Receive pipeline blocked - Fail to publish event on social webhook" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "FB" "FB Receive pipeline blocked - Fail to publish event on social webhook" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 0;
		fi
	fi

	debug "Published FB message - [mid_${randomMid}] on social notification service. Now check count with retry"
	
	resetContext
	sleep 60

	executor_wrapper 60 checkCount "" "FACEBOOK" "1097520"
	if [ ${record_count} -eq  ${_COUNT} ]; then
		local response="${_API_RESPONSE:-FB conversation record count does not increased after publishing to social webhook}"

		debug "Going to check count within time range"
		
		checkCountWithinTimeRange "FACEBOOK" "RECEIVE" 
		if [ $? -ne 0 ]; then
			publishAlert "FB" "FB Receive pipeline blocked - No new record found" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "FB" "FB Receive pipeline blocked - No new record found" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
		fi

		return 0;
	fi

	debug "Receive path for facebook channel seems to ok !!"

    local msg_body="FB Pipeline Healthcheck | Acknowledgement for mid_${randomMid} published at $(date -u)"
    local body="{\"body\":\"${msg_body}\",\"fromBusinessId\":1097520,\"mediaUrls\":[],\"ongoingPulseSurvey\":false,\"toCustomerId\":200872625, \"businessIdentifierId\":1097520, \"userId\":2048688, \"source\":${_FB}}"
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/event/send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Send API failed and exceeded retry attempt}"
			publishAlert "FB" "FB Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 200872625 " "${response}" ""

			# Publish to Social Webhook 
            publishAlert "FB" "FB Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 200872625" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 1
		fi
	fi

	debug "Message send via Inbox to facebook with response - ${_API_RESPONSE}"
	resetContext
	_FB_PROCESSING_DONE=true
}


function monitorIGDataPipeline {

	if [ "$_INSTA_PROCESSING_DONE" = true ]; then
		debug "Message already injected to instagram webhook"
		return 0;
	fi

	checkCount "" "INSTAGRAM" "1089582"
	local record_count=${_COUNT:-0}
	debug "Record count before processing - ${record_count}"

	local current_epoch=$(date +%s)
	local previous_epoch=$((current_epoch - 3))

	local randomMid=$(openssl rand -hex 32)
	local body="{\"object\":\"instagram\",\"entry\":[{\"id\":17841461564221802,\"time\":${current_epoch},\"messaging\":[{\"timestamp\":${previous_epoch},\"message\":{\"mid\":\"mid_${randomMid}\",\"text\":\"System Health Check | Instagram\",\"attachments\":null,\"is_echo\":false},\"sender\":{\"id\":7241425589223803},\"recipient\":{\"id\":17841461564221802}}],\"changes\":null}], \"event\":\"INSTAGRAM_MSG_RECEIVE\"}"
	
	# Send message to IG webhook
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_SOCIAL_DOMAIN}/notification/instagram" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Retry attempt exceeded for API - /notification/instagram}"
			publishAlert "IG" "IG Receive pipeline blocked - Fail to publish event on social webhook" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "IG" "IG Receive pipeline blocked - Fail to publish event on social webhook" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 0;
		fi
	fi

	debug "Published IG message - [mid_${randomMid}] on social notification service. Now check count with retry"
	
	resetContext
	sleep 60

	executor_wrapper 60 checkCount "" "INSTAGRAM" "1089582"
	if [ ${record_count} -eq  ${_COUNT} ]; then
		local response="${_API_RESPONSE:-FB conversation record count does not increased after publishing to social webhook}"

		debug "Going to check count within time range"
		
		checkCountWithinTimeRange "INSTAGRAM" "RECEIVE" 
		if [ $? -ne 0 ]; then
			publishAlert "IG" "IG Receive pipeline blocked - No new record found" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "IG" "IG Receive pipeline blocked - No new record found" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
		fi

		return 0;
	fi

    # ${_BE_SOCIAL_DOMAIN}/notification/instagram


    debug "Receive path for instgram channel seems to ok !!"

	local msg_body="IG Pipeline Healthcheck | Acknowledgement for mid_${randomMid} published at $(date -u)"
    local body="{\"body\":\"${msg_body}\",\"fromBusinessId\":1089582,\"mediaUrls\":[],\"ongoingPulseSurvey\":false,\"toCustomerId\":199871490, \"businessIdentifierId\":1089582, \"userId\":2048688, \"source\":${_IG}}"
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/event/send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Send API failed and exceeded retry attempt}"
			publishAlert "IG" "IG Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 199871490 " "${response}" ""

			# Publish to Social Webhook 
            publishAlert "IG" "IG Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 199871490" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 1
		fi
	fi

	debug "Message send via Inbox to Instagram with response - ${_API_RESPONSE}"
	resetContext
	_INSTA_PROCESSING_DONE=true
}
 

function monitorGoogleDataPipeline {
	if [ "$_GMB_PROCESSING_DONE" = true ]; then
		debug "Message already injected to google webhook"
		return 0;
	fi

	checkCount "" "GOOGLE" "1089582"
	local record_count=${_COUNT:-0}
	debug "Record count before processing - ${record_count}"

	local current_epoch=$(date +%s)
	local previous_epoch=$((current_epoch - 3))

	# local randomMid=$(openssl rand -hex 32)

	local mid=$(uuidgen)-${current_epoch}

	local body="{\"sendTime\": \"$(date -u +"%Y-%m-%dT%H:%M:%S")\", \"conversationId\":\"097705b6-c71f-41ea-a74b-cca55dfad63c\",\"requestId\":\"${mid}\",\"agent\":\"brands/24874824-bacb-4c41-8b36-84dfc03626d5/agents/3ff136c9-e638-4fd4-b6bb-69839a98e6e7\",\"message\":{\"name\":\"conversations/097705b6-c71f-41ea-a74b-cca55dfad63c/messages/${mid}\",\"text\": \"System Health Check [${mid}] | Google\",\"createTime\":\"$(date -u +"%Y-%m-%dT%H:%M:%S")\",\"messageId\":\"${mid}\" },\"context\":{\"userInfo\":{\"displayName\":\"Tanmay Prakash\",\"userDeviceLocale\":\"en-GB\"},\"placeId\":\"ChIJua0gglojDTkRTuggb3ehv-o\",\"entryPoint\":\"MAPS\",\"resolvedLocale\":\"en\"},\"receipts\":null,\"surveyResponse\":null}"

	# Send message to Google webhook
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_SOCIAL_DOMAIN}/gmb/notification/message" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Retry attempt exceeded for API - /gmb/notification/message}"
			publishAlert "Google" "Google Receive pipeline blocked - Fail to publish event on social webhook" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "Google" "Google Receive pipeline blocked - Fail to publish event on social webhook" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 0;
		fi
	fi

	debug "Published Google message - [mid_${mid}] on social notification service. Now check count with retry"
	
	resetContext
	sleep 60

	executor_wrapper 60 checkCount "" "GOOGLE" "1089582"
	if [ ${record_count} -eq  ${_COUNT} ]; then
		local response="${_API_RESPONSE:-FB conversation record count does not increased after publishing to social webhook}"

		debug "Going to check count within time range"
		
		checkCountWithinTimeRange "GOOGLE" "RECEIVE" 
		if [ $? -ne 0 ]; then
			publishAlert "IG" "IG Receive pipeline blocked - No new record found" "${response}" ""

			# Publish to Social Webhook 
            publishAlert "IG" "IG Receive pipeline blocked - No new record found" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
		fi

		return 0;
	fi


    debug "Receive path for google channel seems to ok !!"


	local msg_body="Google Pipeline Healthcheck | Acknowledgement for mid_${mid} published at $(date -u)"
    local body="{\"body\":\"${msg_body}\",\"fromBusinessId\":1089582,\"mediaUrls\":[],\"ongoingPulseSurvey\":false,\"toCustomerId\":200075991, \"businessIdentifierId\":1089582, \"userId\":2048688, \"source\":${_GOOGLE}}"
	executor_wrapper ${_INITIAL_WAIT} executePost "${_BE_MSG_DOMAIN}/messenger/event/send" "${body}" "SERVICE-NAME: INBOX_HEALTH_CHECK_SCRIPT"

	if [ $? -ne 0 ]; then
		if [[ $((_ATTEMPT + 1)) -ge ${_MAX_ATTEMPTS} ]]; then
			local response="${_API_RESPONSE:-Send API failed and exceeded retry attempt}"
			publishAlert "Google" "Google Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 200075991 " "${response}" ""

			# Publish to Social Webhook 
            publishAlert "Google" "Google Send pipeline blocked -  Retry attempt exhausted of API -/messenger/event/send to customer Id - 200075991" "${response}" "" "" "${_SOCIAL_ALERT_WEBHOOK}"
			return 1
		fi
	fi

	debug "Message send via Inbox to Google with response - ${_API_RESPONSE}"
	resetContext

	_GMB_PROCESSING_DONE=true
}

function checkCount {
	local conversation_id=$1
	local channel=$2
	local business_id=$3
	local term=("{\"term\":{\"messageType\":{\"value\":\"CHAT\"}}}")

    if [ ! -z ${conversation_id} ]; then
	   term+=("{\"term\":{\"c_id\":{\"value\": ${conversation_id}}}}")
	fi 

	if [ ! -z ${channel} ]; then
	   term+=("{\"term\":{\"channel\":{\"value\":\"${channel}\"}}}")
	fi

	if [ ! -z ${business_id} ]; then
	   term+=("{\"term\":{\"b_id\":{\"value\":\"${business_id}\"}}}")
	fi 

    local must=$( echo "${term[@]}" | tr ' ' ',' )
    local body="{\"query\":{\"bool\":{\"must\":[ ${must} ]}}}"

	executePost "${_ES_URL}/inbox_conversation_detail/_count" "${body}" "Authorization: Basic ${_AUTH_KEY}"

	if [ $? -ne 0 ]; then
		return 1
	fi 
	debug "Number of conversation found is - [${_COUNT}]"
	return 0;
}

function checkCountWithinTimeRange {
	local channel=$1
	local direction=$2

	_COUNT=0

	local term=("{\"term\":{\"messageType\":{\"value\":\"CHAT\"}}}")

	local timeRange="{\"range\":{\"cr_time\":{\"gte\":\"now-15m\"}}}"

	if [ ! -z ${channel} ]; then
		term+=("{\"term\":{\"channel\":{\"value\":\"${channel}\"}}}")
	fi

	if [ ! -z ${direction} ]; then
		term+=("{\"term\":{\"communicationDirection\":{\"value\":\"${direction}\"}}}")
	fi

	local must=$( echo "${term[@]}" | tr ' ' ',' )

	local body="{\"query\":{\"bool\":{\"must\":[ ${must}, ${timeRange} ]}}}"

	executePost "${_ES_URL}/inbox_conversation_detail/_count" "${body}" "Authorization: Basic ${_AUTH_KEY}"

	if [ $? -ne 0 ]; then
		return 1
	fi 

	if [ ! ${_COUNT} -gt 1 ]; then
		return 1
	fi 

	debug "Number of conversation found within time range is - [${_COUNT}]"
	return 0;
}

function receiveEmail {
    local body="{\"event\":\"EMAIL_RECEIVE\",\"emailDTO\":{\"to\":[\"<EMAIL>\"],\"bcc\":[],\"attachments\":{},\"fileAttachementData\":[],\"subject\":\"Re: New message from Location B\",\"from\":\"<EMAIL>\",\"replyTo\":null,\"fromName\":null,\"body\":\"Health Check | Email Receive\",\"ampBody\":null,\"s3AttachementUrls\":[],\"emailAttachments\":[],\"replyToList\":null,\"textType\":\"TEXT\",\"scheduleTime\":0,\"emailReceivedTime\":1683044291912},\"emailReceivedTime\":null,\"businessNumber\":168175863157362}"
    executePost "${_BE_MSG_DOMAIN}/messenger/event/email" "${body}"
}

function verifyConversationDelay {
	resetContext
	searchConversationsWithinTimeRange "$1" "$2"

	if [ $? -ne 0 ]; then
	    debug "Failed to execute search conversation query"
		return 1
	fi

	if [[ -z ${_CR_TIME} &&  -z ${_LAST_UPDATED_TIME} ]]; then
		debug "Search query does not returned required output"
		return 1
	fi

	return 0
}

function searchConversation {

    executePost "${_ES_URL}/inbox_conversation_detail/_search"   "{\"_source\":{\"includes\":[\"cr_time\",\"lastUpdateDate\",\"c_id\",\"m_id\",\"communicationDirection\",\"b_id\"]},\"query\":{\"bool\":{\"must\":[{\"exists\":{\"field\":\"lastUpdateDate\"}},{\"exists\":{\"field\":\"cr_time\"}},{\"term\":{\"c_id\":{\"value\": ${1} }}},{\"term\":{\"b_id\":{\"value\": ${2} }}}]}},\"sort\":[{\"cr_time\":{\"order\":\"desc\"}}],\"size\":1}"  \
	    "Authorization: Basic ${_AUTH_KEY}" 

    if [ $? -ne 0 ]; then
	    return 1
    fi 
    return 0;
}


function  searchConversationsWithinTimeRange {
	local channel=$1
	local direction=$2
	local body="{\"_source\":{\"includes\":[\"cr_time\",\"communicationDirection\",\"c_id\",\"m_id\",\"lastUpdateDate\",\"channel\"]},\"query\":{\"bool\":{\"must\":[{\"term\":{\"messageType\":{\"value\":\"CHAT\"}}},{\"term\":{\"communicationDirection\":{\"value\":\"${direction}\"}}},{\"term\":{\"channel\":{\"value\":\"${channel}\"}}},{\"range\":{\"cr_time\":{\"gte\":\"now-15m\"}}}]}},\"sort\":[{\"cr_time\":{\"order\":\"desc\"}}],\"size\":1}"

    debug "Check conversation direction - [${direction}],  within 15 mins for channel - ${channel}"
	executePost "${_ES_URL}/inbox_conversation_detail/_search" "${body}"  "Authorization: Basic ${_AUTH_KEY}"

	if [ $? -ne 0 ]; then
	    return 1
    fi 
	debug "Conversation - [${_MCID}] found within time range "
    return 0;
}


function doProcess {

	if ! echo "${_SKIP_LIST[@]}" | grep -q "EMAIL"; then
		sendEmail
		debug "Email pipeline sanity done"        
	fi 
	echo " "
	sleep 2

	if ! echo "${_SKIP_LIST[@]}" | grep -q "SMS"; then
		sendSMS
		debug "SMS pipeline sanity done"
	fi
	echo " "
	sleep 2

	if ! echo "${_SKIP_LIST[@]}" | grep -q "FB"; then
		monitorFBDataPipeline
		debug "FB pipeline sanity done"
	fi
	echo " "
	sleep 2

	if ! echo "${_SKIP_LIST[@]}" | grep -q "IG"; then
	    monitorIGDataPipeline
		debug "IG pipeline sanity done"	
	fi
	echo " "
	sleep 2

	if !  echo "${_SKIP_LIST[@]}" | grep -q "GOOGLE"; then
		monitorGoogleDataPipeline
		debug "Google pipeline sanity done"	
	fi
	echo " "
}



#------------------------------------------------
#   MAIN FUNCTION
#------------------------------------------------

function __main__ {
	local skip_list=()
	local log_file
	local wait_time=140
	local attempt=5
	local install_commands=()

	validation

	while getopts ":s:f:w:a:i:k:h" opt; do
		case $opt in
			s)
				set -f 
				IFS=','
				skip_list=($OPTARG)
				;;
			f)
				log_file=$OPTARG
				;;
			w)
				if ! isInteger "$OPTARG"; then
					error "Integer value is required for wait time"
					exit -1
				fi 
				wait_time=$OPTARG
				;;
			a)
				if ! isInteger "$OPTARG"; then
					error "Integer value is required for number of attempt"
					exit -1
				fi 
				attempt=$OPTARG
				;;
			i)
				set -f 
				IFS=','
				install_commands=($OPTARG)
				get_commands "${install_commands[@]}"
				;;

			k)
				_AUTH_KEY=`echo "$OPTARG" | tr -d '[:space:]'`
				;;
			h)
				usage
				;;
			\?)
				error "Invalid option: -$OPTARG" >&2
				usage
				;;
			:)
				error "Option -$OPTARG requires an argument" >&2
				usage
				;;
		esac
	done


	if [ -z $_AUTH_KEY ]; then
		error "ES Authentication key is required !!"
		usage
	fi

    # Rest of script goes here, using $input_file and $verbose as needed
    init_vars "${log_file}" ${attempt} ${wait_time} "${skip_list[@]}"
	doProcess

    rm $_ECHO_VAR 2>/dev/null

    kafkaEventPublisher
}

__main__ "$@"
