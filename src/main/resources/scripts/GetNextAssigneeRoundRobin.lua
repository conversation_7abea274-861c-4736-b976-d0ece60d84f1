local locationAssignmentKey = KEYS[1]
local inputStr = ARGV[1]
local prevIndex = tonumber(redis.call('HGET', locationAssignmentKey, "prevAssignedIndex"))
if (not prevIndex) then
	prevIndex = -1
end
local loopIteration=0
local separatorInputStr = ","
local separatorEachItem = ":"
local firstActive = {}
for separatedStr in string.gmatch(inputStr, "([^"..separatorInputStr.."]+)") do
    local userTable={}
    for separatedUser in string.gmatch(separatedStr, "([^"..separatorEachItem.."]+)") do
        table.insert(userTable, separatedUser)
    end
    if firstActive[1] == nil and tonumber(userTable[2])==1 then
        firstActive = userTable
        table.insert(firstActive,loopIteration)
    end
    if (loopIteration > prevIndex and tonumber(userTable[2])==1) then
        	-- prevIndex = loopIteration -- SET INTO REDIS - PREVINDEX AND USER
        	redis.call('HSET', locationAssignmentKey, "prevAssignedIndex", loopIteration)
        	local userId = tonumber(userTable[1])
        	return userId
    end
    loopIteration = loopIteration + 1
end
if firstActive[1] == nil then
	return -1
else
	redis.call('HSET', locationAssignmentKey, "prevAssignedIndex", firstActive[3])
	local userId = tonumber(firstActive[1])
	return userId
end
