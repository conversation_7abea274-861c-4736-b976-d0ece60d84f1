package com.birdeye.messenger.config;

import java.util.HashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

import lombok.Data;

@ConfigurationProperties(prefix = "cache")
@Data
public class CacheConfigurationProperties {

	private long timeoutSeconds = 60;

	// Mapping of cacheNames to expira-after-write timeout in seconds
	private Map<String, Long> ttls = new HashMap<>();
}