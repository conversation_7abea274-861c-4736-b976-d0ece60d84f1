package com.birdeye.messenger.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

import lombok.RequiredArgsConstructor;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

//	@Value("${enable.secure.dev.endpoint:false}")
//	private boolean devEndpointSecurityEnabled;

	@Value("${secure.dev.endpoint.user:messenger}")
	private String username;

	@Value("${secure.dev.endpoint.password}")
	private String password;

	@Bean
	public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
		// CORS configuration
        http.cors();

        // Disable CSRF (temporarily for testing)
        http.csrf().disable();
        
		http
			.securityMatcher("/swagger-ui/**")
			.authorizeRequests()
					.requestMatchers("/actuator/**", ".*/swagger\\-ui\\.html", "/v3/api-docs")
					.permitAll()
					.anyRequest().authenticated();
		
		http
				.httpBasic(Customizer.withDefaults());

		return http.build();
	}

	@Bean
	public UserDetailsService userDetailsService() {

		UserDetails userDetails = User.withDefaultPasswordEncoder()
				.username(username)
				.password(password)
				.roles("ADMIN")
				.build();

		return new InMemoryUserDetailsManager(userDetails);
	}

}