package com.birdeye.messenger.config;

import freemarker.cache.FileTemplateLoader;
import freemarker.cache.MruCacheStorage;
import freemarker.cache.MultiTemplateLoader;
import freemarker.cache.TemplateLoader;
import freemarker.template.DefaultObjectWrapperBuilder;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.Locale;

@Configuration
@Slf4j
public class FreeMarkerConfig {

    @Bean("freeMarker")
    public static freemarker.template.Configuration getInstance() {

        freemarker.template.Configuration config = new freemarker.template.Configuration(freemarker.template.Configuration.VERSION_2_3_25);
        try {
            ClassPathResource elasticQueryResource = new ClassPathResource("templates/elasticQuery");
            FileTemplateLoader esQueryTemplateLoader = new FileTemplateLoader(elasticQueryResource.getFile());
            //ClassPathResource emailTemplateResource = new ClassPathResource("templates/email");
            //FileTemplateLoader emailTemplateLoader = new FileTemplateLoader(emailTemplateResource.getFile());
            MultiTemplateLoader multiTemplateLoader = new MultiTemplateLoader(new TemplateLoader[]{esQueryTemplateLoader});
            config.setTemplateLoader(multiTemplateLoader);
            config.setCacheStorage(new MruCacheStorage(10, 20)); //TODO:: NEED to understand how this works
            config.setObjectWrapper(new DefaultObjectWrapperBuilder(freemarker.template.Configuration.VERSION_2_3_25).build());
            config.setDefaultEncoding("UTF-8");
            config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
            config.setLocale(Locale.US);
        } catch (IOException e) {
            log.error("Failed to load template resource : ", e);
        }
        return config;
    }

}
