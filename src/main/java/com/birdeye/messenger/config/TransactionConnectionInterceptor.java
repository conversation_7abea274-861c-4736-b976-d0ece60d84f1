package com.birdeye.messenger.config;

import com.birdeye.messenger.annotations.DataSourceType;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class TransactionConnectionInterceptor implements Ordered {

    @Pointcut(value = "execution(public * *(..))")
    public void anyPublicMethod() {
    }


    @Around("@annotation(dataSourceType)")
    public Object proceed(ProceedingJoinPoint pjp, DataSourceType dataSourceType) throws Throwable {
        try {
            DbContextHolder.setDbType(dataSourceType.dataSource());
            Object result = pjp.proceed();
            DbContextHolder.clearDbType();
            return result;
        } finally {
            // restore state
            DbContextHolder.clearDbType();
        }
    }


    private int order;

    @Value("20")
    public void setOrder(int order) {
        this.order = order;
    }

    @Override
    public int getOrder() {
        return order;
    }
}
