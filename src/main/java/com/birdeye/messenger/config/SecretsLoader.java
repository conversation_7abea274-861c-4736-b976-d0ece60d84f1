package com.birdeye.messenger.config;
import java.util.HashMap;
import java.util.Map;

import jakarta.annotation.PostConstruct;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component
@ConditionalOnClass(AWSSecretsManager.class)
public class SecretsLoader {

    private static final Logger logger = LoggerFactory.getLogger(SecretsLoader.class);

    private Map<String, String> secretsMap = new HashMap<String, String>();

    @Value("${aws.secret.manager.secret.id}")
    private String bazaarifyMasterSecretName;

    @Value("${aws.secret.enabled}")
    private String enabled;

    @Autowired(required = false)
    @Qualifier("defaultSecretsManagerClient")
    private AWSSecretsManager awsSecretsManager;

    public Map<String, String> getSecretsMap() {
        return secretsMap;
    }

    @PostConstruct
    public void retrieveSecrets() {
        if (Boolean.TRUE.equals(Boolean.valueOf(enabled))) {
            getSecretValue(bazaarifyMasterSecretName, Constants.BAZAARIFY_MASTER_DB);
        }
    }

    private GetSecretValueResult getSecretValue(String secretName, String dbName) {
        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest().withSecretId(secretName);
        GetSecretValueResult getSecretValueResult = null;
        try {
            getSecretValueResult = awsSecretsManager.getSecretValue(getSecretValueRequest);
            Map<String, String> awsSecretValMap = getSecretMap(getSecretValueResult.getSecretString());
            buildAwsSecretsMap(awsSecretValMap, dbName);
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to retrive secrets ", e);
            throw new MessengerException(ErrorCode.FAILED_TO_RETRIVE_SECRET);
        }
        return getSecretValueResult;
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getSecretMap(String secretValueResult) {
        Map<String, String> awsSecretMap = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            awsSecretMap = mapper.readValue(secretValueResult, Map.class);
//            logger.info("SECRET VAL ***  " + mapper.writeValueAsString(awsSecretMap));
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to unmarshal secrets", e);
            throw new MessengerException(ErrorCode.FAILED_TO_UNMARSHAL_SECRET);
        }
        return awsSecretMap;
    }

    private Map<String, String> buildAwsSecretsMap(Map<String, String> secretVal, String dbName) {
    	if (Constants.BAZAARIFY_MASTER_DB.equals(dbName)) {
    		secretsMap.put(Constants.BAZAARIFY_MASTER_HOST_NAME, secretVal.get(Constants.HOST_NAME));
    		secretsMap.put(Constants.BAZAARIFY_MASTER_DB_PORT, secretVal.get(Constants.DB_PORT));
    		secretsMap.put(Constants.BAZAARIFY_MASTER_DB_USERNAME, secretVal.get(Constants.DB_USERNAME));
    		secretsMap.put(Constants.BAZAARIFY_MASTER_DB_PASSWORD, secretVal.get(Constants.DB_PASSWORD));
    		secretsMap.put(Constants.BAZAARIFY_MASTER_DB_NAME, secretVal.get(Constants.DB_NAME));

    		secretsMap.put(Constants.MESSENGER_MASTER_HOST_NAME, secretVal.get(Constants.MESSENGER_MASTER_HOST_NAME));
    		secretsMap.put(Constants.MESSENGER_MASTER_DB_PORT, secretVal.get(Constants.MESSENGER_MASTER_DB_PORT));
    		secretsMap.put(Constants.MESSENGER_MASTER_DB_USERNAME, secretVal.get(Constants.MESSENGER_MASTER_DB_USERNAME));
    		secretsMap.put(Constants.MESSENGER_MASTER_DB_PASSWORD, secretVal.get(Constants.MESSENGER_MASTER_DB_PASSWORD));
    		secretsMap.put(Constants.MESSENGER_MASTER_DB_NAME, secretVal.get(Constants.MESSENGER_MASTER_DB_NAME));
    	}
    	return secretsMap;
    }


}