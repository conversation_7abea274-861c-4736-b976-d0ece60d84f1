package com.birdeye.messenger.config;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.util.UriComponentsBuilder;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *         To add an identifier in the header for all external API calls
 */
@Slf4j(topic = "reqResLogger")
public class HttpRequestHeaderInterceptor implements ClientHttpRequestInterceptor {

    private static final String HEADER_IDENTIFIER = "SERVICE-NAME";
    private static final String SERVICE_IDENTIFIER = "Messenger-Service";
    private static final String FULL_EXTERNAL_URI = "full_external_uri";
    private static final String EXTERNAL_SERVICE_DOMAIN = "external_service_domain";
    private static final String EXTERNAL_SERVICE_PATH = "external_service_path";
    private static final String EXTERNAL_HTTP_METHOD = "external_http_method";
    private static final String EXTERNAL_HTTP_STATUS = "external_http_status";
    private static final String EXTERNAL_HTTP_RESPONSE_TIME = "external_http_response_time";
    private static final String EXTERNAL_HTTP_QUERY = "external_http_query";
    private static final String EXTERNAL_HTTP_HEADERS = "external_http_headers";
    private static final String EXTERNAL_HTTP_RESPONSE_BODY = "external_http_response_body";
    private static final String EXTERNAL_HTTP_PATH_PARAMS = "external_http_path_params";

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpRequestHeaderInterceptor.class);
    private static final String COMMON_LOG_LINE = "Rest call intcptd : {} {}";

    /**
     * whenever an external API call is made using the RestTemplate,
     * the interceptor will automatically add the "SERVICE-NAME" header with
     * "Messenger-Service"
     * 
     */
    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) {
        ClientHttpResponse clientHttpResponse = null;
        String url = request.getURI().toString();
        String method = request.getMethod().name();
        try {
//            log.info(COMMON_LOG_LINE, method, url);
//            LOGGER.info(COMMON_LOG_LINE, method, url);

            HttpHeaders headers = request.getHeaders();
            if (StringUtils.isEmpty(headers.getFirst(HEADER_IDENTIFIER))) {
                headers.add(HEADER_IDENTIFIER, SERVICE_IDENTIFIER);
            }
            String logRequestBody = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                    .getProperty("log.external.request.body", "true");
            if (Boolean.TRUE.equals(Boolean.valueOf(logRequestBody))) {
                logRequest(request, body);
            }
            Instant start = Instant.now();
            clientHttpResponse = execution.execute(request, body);
            Instant finish = Instant.now();
            long time = Duration.between(start, finish).toMillis();
            Map<String, Object> jsonFieldsMap = getJsonFieldsMap(request, clientHttpResponse, time);
            logResponse(jsonFieldsMap);
        } catch (Exception e) {
            log.error("error : {} occurred in intercept for url : {} and method : {}", e.getMessage(), url, method);
            throw new MessengerException(e.getMessage() + " occurred for url " + url + " and method " + method);
        }
        return clientHttpResponse;
    }

    private void logRequest(HttpRequest request, byte[] body) throws UnsupportedEncodingException {
        log.info("Ext call : {} {} hdrs : {} body : {}", request.getMethod(), request.getURI(),
                request.getHeaders(), new String(body, "UTF-8"));
    }

    private void logResponse(Map<String, Object> jsonFieldsMap) {
        try {
            if (MapUtils.isNotEmpty(jsonFieldsMap)) {
                Long responseTime = (Long) jsonFieldsMap.get(EXTERNAL_HTTP_RESPONSE_TIME);
                Integer responseStatus = (Integer) jsonFieldsMap.get(EXTERNAL_HTTP_STATUS);
                String logLine = "Ext exec time :: {} {}, pathP : {}, queryP :: {}, hdrs :: {} is {} ms & status :: {}";
                List logArgs = new LinkedList(
                        Arrays.asList(jsonFieldsMap.get(EXTERNAL_HTTP_METHOD), jsonFieldsMap.get(FULL_EXTERNAL_URI),
                                jsonFieldsMap.get(EXTERNAL_HTTP_PATH_PARAMS), jsonFieldsMap.get(EXTERNAL_HTTP_QUERY),
                                jsonFieldsMap.get(EXTERNAL_HTTP_HEADERS),
                                responseTime, responseStatus));
                String logResponseBody = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                        .getProperty("log.external.response.body", "true");
                boolean success = responseStatus >= 200 && responseStatus < 300;
                String responseBody = (String) jsonFieldsMap.get(EXTERNAL_HTTP_RESPONSE_BODY);
                logArgs.add(responseBody);
                if (!success) {
                    logLine += " and failure resp body :: {}";
                    log.error(logLine, logArgs.toArray());
                    LOGGER.error(getLogstashMarkerForExternalCalls(jsonFieldsMap), logLine, logArgs.toArray());
                } else if (success && Boolean.TRUE.equals(Boolean.valueOf(logResponseBody))) {
                    String logLineWithoutBodyAndHeaders = logLine;
                    logLineWithoutBodyAndHeaders = logLineWithoutBodyAndHeaders.replace(", hdrs :: {}", "");
                    logLine += " and success resp body :: {}";
                    log.info(logLine, logArgs.toArray());
                    logArgs.remove(responseBody);
                    logArgs.remove(jsonFieldsMap.get(EXTERNAL_HTTP_HEADERS));
                    LOGGER.info(getLogstashMarkerForExternalCalls(jsonFieldsMap), logLineWithoutBodyAndHeaders,
                            logArgs.toArray());
                }
            }
        } catch (Exception e) {
            log.error("error : {} occurred in logResponse", e.getMessage());
        }
    }

    private String buildRequestAndResponseStringUsingInputStream(InputStream inputStream) throws IOException {
        StringBuilder inputStringBuilder = new StringBuilder();
        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
        String line = bufferedReader.readLine();
        while (line != null) {
            inputStringBuilder.append(line);
            inputStringBuilder.append('\n');
            line = bufferedReader.readLine();
        }
        return inputStringBuilder.toString();
    }

    private LogstashMarker getLogstashMarkerForExternalCalls(Map<String, Object> jsonFields) {
        LogstashMarker marker = null;
        if (MapUtils.isNotEmpty(jsonFields)) {
            jsonFields.remove(EXTERNAL_HTTP_HEADERS);
            jsonFields.remove(EXTERNAL_HTTP_QUERY);
            jsonFields.remove(EXTERNAL_HTTP_RESPONSE_BODY);
            jsonFields.remove(EXTERNAL_HTTP_PATH_PARAMS);
            marker = Markers.appendEntries(jsonFields);
        }
        return marker;
    }

    private Map<String, Object> getJsonFieldsMap(HttpRequest httpRequest, ClientHttpResponse response, long time)
            throws IOException {
        Map<String, Object> jsonFieldsMap = null;
        if (!MessengerUtil.checkIfAnyValueIsNull(httpRequest, response)) {
            jsonFieldsMap = new HashMap<>();
            URI uri = httpRequest.getURI();
            String query = uri.getQuery();
            String path = uri.getPath();
            String url = uri.toString();
            UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUri(uri)
                    .replacePath(null); 
            uri = uriComponentsBuilder.build().toUri();
            boolean hostAndPathPresent = StringUtils.isNotBlank(url) && StringUtils.isNotBlank(path);
            boolean queryParamsPresent = StringUtils.isNotBlank(query);
            if (queryParamsPresent) {
                jsonFieldsMap.put(EXTERNAL_HTTP_QUERY, uri.getQuery());
                uri = uriComponentsBuilder.replaceQuery(null).build().toUri(); // removes query params from url
                url = url.replace(uri.toString(), "");
                url = url.replace("?" + query, "");
            } 
            if (hostAndPathPresent) {
                url = url.replace(uri.toString(), "");
                String regex = "-?\\d+"; // regex to check if the string contains integer only
                String pathVars[] = url.split("/");
                url = "";
                for (String pathVar : pathVars) {
                    if (Pattern.matches(regex, pathVar)) {
                        break;
                    }
                    url += pathVar + "/";
                }
            }      
            String fullExternalUri = uri.toString() + path;
            if (queryParamsPresent) {
                fullExternalUri += "?" + query;
            }
            jsonFieldsMap.put(FULL_EXTERNAL_URI, fullExternalUri);
            jsonFieldsMap.put(EXTERNAL_SERVICE_DOMAIN, uri.toString());
            jsonFieldsMap.put(EXTERNAL_SERVICE_PATH, url); // static path of api
            jsonFieldsMap.put(EXTERNAL_HTTP_PATH_PARAMS, path);
            jsonFieldsMap.put(EXTERNAL_HTTP_METHOD, httpRequest.getMethod().name());
            jsonFieldsMap.put(EXTERNAL_HTTP_STATUS, response.getRawStatusCode());
            jsonFieldsMap.put(EXTERNAL_HTTP_RESPONSE_TIME, time);
            jsonFieldsMap.put(EXTERNAL_HTTP_HEADERS, httpRequest.getHeaders().toString());
            jsonFieldsMap.put(EXTERNAL_HTTP_RESPONSE_BODY,
                    buildRequestAndResponseStringUsingInputStream(response.getBody()));
        }
        return jsonFieldsMap;
    }

}
