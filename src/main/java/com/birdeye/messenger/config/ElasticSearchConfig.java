package com.birdeye.messenger.config;

import org.apache.http.HttpHost;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestClientBuilder.HttpClientConfigCallback;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazonaws.auth.AWS4Signer;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;

@Configuration
public class ElasticSearchConfig {

	@Value("${aws.es.host}")
	private String aesEndpoint;

	@Value("${aws.es.service.name}")
	private String serviceName;

	@Value("${aws.region}")
	private String region;
	
	@Value("${elasticsearch.http.maxConnection}")
	private Integer maxTotalConnections;
	
	@Value("${elasticsearch.default.connect.timeout.millis}")
	private Integer connectTimeout;
	
	@Value("${elasticsearch.default.socket.timeout.millis}")
	private Integer socketTimeout;
	
	@Value("${aws.role}")
	private String awsRole;

	@Bean("restHighLevelClient")
	public RestHighLevelClient esClient() {
		
		AWSCredentialsProvider credentialsProvider;
		
		if(System.getenv("AWS_ACCESS_KEY_ID")!=null){
			credentialsProvider = new STSAssumeRoleSessionCredentialsProvider(awsRole);
		} else {
			credentialsProvider = new DefaultAWSCredentialsProviderChain();
		}
		
		AWS4Signer signer = new AWS4Signer();
		signer.setServiceName(serviceName);
		signer.setRegionName(region);
		HttpRequestInterceptor interceptor = new AWSRequestSigningApacheInterceptor(serviceName, signer, credentialsProvider);
		return new RestHighLevelClient(RestClient.builder(HttpHost.create(aesEndpoint))
				.setHttpClientConfigCallback(httpClientConfig(interceptor)));
	}

	private HttpClientConfigCallback httpClientConfig(HttpRequestInterceptor interceptor) {
		HttpClientConfigCallback hccc = new RestClientBuilder.HttpClientConfigCallback(){
			@Override
			public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
				httpClientBuilder.setMaxConnPerRoute(maxTotalConnections);
				httpClientBuilder.setMaxConnTotal(maxTotalConnections);
				httpClientBuilder.addInterceptorLast(interceptor);
				httpClientBuilder.setDefaultRequestConfig(httpRequestConfig());
				return httpClientBuilder;
		}};
		return hccc;
	}
	
	private RequestConfig httpRequestConfig() {
		RequestConfig.Builder requestConfigBuilder = RequestConfig.custom()
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout);
		
		
		return requestConfigBuilder.build();
	}
	
	/*
	 * Elastic Search LLC is created for HealthCheckIndicator Only.
	 * Number of threads in the pool is very low for any other use
	 */
	@Bean("restLowLevelClient")
	public RestClient getESLLClient() {
		AWSCredentialsProvider credentialsProvider;

		if(System.getenv("AWS_ACCESS_KEY_ID")!=null){
			credentialsProvider = new STSAssumeRoleSessionCredentialsProvider(awsRole);
		} else {
			credentialsProvider = new DefaultAWSCredentialsProviderChain();
		}

		AWS4Signer signer = new AWS4Signer();
		signer.setServiceName(serviceName);
		signer.setRegionName(region);
		HttpRequestInterceptor interceptor = new AWSRequestSigningApacheInterceptor(serviceName, signer, credentialsProvider);
		return RestClient.builder(HttpHost.create(aesEndpoint))
				.setHttpClientConfigCallback(httpClientConfigLLC(interceptor)).build();
	}

	private HttpClientConfigCallback httpClientConfigLLC(HttpRequestInterceptor interceptor) {
		HttpClientConfigCallback hccc = new RestClientBuilder.HttpClientConfigCallback(){
			@Override
			public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
				httpClientBuilder.setMaxConnPerRoute(2);
				httpClientBuilder.setMaxConnTotal(2);
				httpClientBuilder.addInterceptorLast(interceptor);
				httpClientBuilder.setDefaultRequestConfig(httpRequestConfig());
				return httpClientBuilder;
		}};
		return hccc;
	}
	
}