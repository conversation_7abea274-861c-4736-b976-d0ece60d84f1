package com.birdeye.messenger.config;

import java.util.concurrent.ThreadFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * <AUTHOR>
 *
 */
@Configuration
@PropertySource("classpath:threadpool.properties")
public class ThreadPoolTaskExecutorConfig {
	private static final String EXECUTOR_NAME = "MessengerExecutor";
	@Autowired
	private TaskDecorator mdcTaskDecorator;

	@Bean(name = "MessengerTaskExecutor")
	public ThreadPoolTaskExecutor threadPoolTaskExecutor(@Value("${max.pool.size}") Integer maxPoolSize,
			@Value("${keep.alive.seconds}") Integer keepAliveSeconds, @Value("${core.pool.size}") Integer corePoolSize,
			@Value("${threadpool.queue.capacity}") Integer queueCapacity) {
		ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
		ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(EXECUTOR_NAME).build();
		threadPoolTaskExecutor.setThreadFactory(threadFactory);
		threadPoolTaskExecutor.setCorePoolSize(corePoolSize);
		threadPoolTaskExecutor.setMaxPoolSize(maxPoolSize);
		threadPoolTaskExecutor.setKeepAliveSeconds(keepAliveSeconds);
		threadPoolTaskExecutor.setQueueCapacity(queueCapacity);
		threadPoolTaskExecutor.setTaskDecorator(mdcTaskDecorator);
		return threadPoolTaskExecutor;
	}

}