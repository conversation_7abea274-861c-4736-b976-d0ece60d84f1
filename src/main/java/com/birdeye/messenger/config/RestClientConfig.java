package com.birdeye.messenger.config;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.DefaultResponseErrorHandler;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class RestClientConfig {

    private int REQUEST_TIMEOUT = 10000;

    private int SOCKET_TIMEOUT = 10000;

    private int MAX_TOTAL_CONNECTIONS = 100;

    private int MAX_PER_ROUTE = 100;

    private ObjectMapper objectMapper() {
        return new ObjectMapper();
    }

    private CloseableHttpClient httpClient() {
    	
        RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(Timeout.of(REQUEST_TIMEOUT, TimeUnit.MILLISECONDS))
                .setResponseTimeout(Timeout.of(SOCKET_TIMEOUT, TimeUnit.MILLISECONDS)).build();
        
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(MAX_TOTAL_CONNECTIONS);
        connManager.setDefaultMaxPerRoute(MAX_PER_ROUTE);
        connManager.setValidateAfterInactivity(Timeout.of(300, TimeUnit.MILLISECONDS));
        return HttpClients.custom().setConnectionManager(connManager).setDefaultRequestConfig(requestConfig).build();
    }

    @Bean("restTemplate")
    public RestTemplate restTemplate() {
        RestTemplate template = new RestTemplate(buildClientHttpRequestFactory());
        template.setInterceptors(getInterceptors());
        template.setErrorHandler(new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                if (Objects.nonNull(response) && !response.getStatusCode().is2xxSuccessful()) {
                    log.error("inside ResponseErrorHandler hasError ");
                    return true;
                }
                return false;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                log.error("inside ResponseErrorHandler handleError ");

            }
        });
        for (HttpMessageConverter<?> converter : template.getMessageConverters()) {
            if (converter instanceof MappingJackson2HttpMessageConverter) {
                MappingJackson2HttpMessageConverter jsonConverter = (MappingJackson2HttpMessageConverter) converter;
                jsonConverter.setObjectMapper(objectMapper());
            }
        }
        return template;
    }

    @Bean
    public DefaultResponseErrorHandler getDefaultResponseErrorHandler() {
        DefaultResponseErrorHandler defaultResponseErrorHandler = new DefaultResponseErrorHandler();
        return defaultResponseErrorHandler;
    }
    
    /**
     * Creates ClientHttpRequestFactory using BufferingClientHttpRequestFactory
     * which can be used for logging request/response in interceptors without
     * closing the stream. Customized based on provided CloseableHttpClient for
     * different timeouts setting
     * 
     */
    @Bean
    public ClientHttpRequestFactory buildClientHttpRequestFactory() {
        ClientHttpRequestFactory factory = new BufferingClientHttpRequestFactory(
                new HttpComponentsClientHttpRequestFactory(httpClient()));
        return factory;
    }

    private List<ClientHttpRequestInterceptor> getInterceptors() {
        return Collections.singletonList(new HttpRequestHeaderInterceptor());
    }
}