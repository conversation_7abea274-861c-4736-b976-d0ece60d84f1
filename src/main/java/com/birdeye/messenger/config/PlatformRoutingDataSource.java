package com.birdeye.messenger.config;

import com.birdeye.messenger.enums.DbType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

@Slf4j
public class PlatformRoutingDataSource extends AbstractRoutingDataSource {
    @Override
    protected Object determineCurrentLookupKey() {
        DbType dbType = DbContextHolder.getDbType();
        if (dbType == null) {
            log.info("Using default DB " + DbType.MASTER);
            dbType = DbType.MASTER;
        } else {
            log.info("DbType is set to read-only DB {} ", dbType);
        }
        return dbType;
    }

    @Override
    public void setDefaultTargetDataSource(Object defaultTargetDataSource) {
        super.setDefaultTargetDataSource(defaultTargetDataSource);
    }
}
