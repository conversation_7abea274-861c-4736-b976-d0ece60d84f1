/**
 * 
 */
package com.birdeye.messenger.config;

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import com.amazonaws.services.sqs.AmazonSQSClient;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;

/**
 * <AUTHOR>
 *
 */
@Component
@Configuration
public class AWSConfig {

	@Bean
	public AmazonSQSClient amazonSQSClient() {
		return (AmazonSQSClient) AmazonSQSClientBuilder.standard().withRegion(Regions.US_WEST_2)
				.withCredentials(new DefaultAWSCredentialsProviderChain()).build();
	}

    @Value("${aws.secret.region}")
    private String region;

	@Bean("defaultSecretsManagerClient")
	@ConditionalOnProperty(prefix = "aws.secret", value = "enabled", havingValue = "true")
	public AWSSecretsManager getDefaultSecretsManagerClient() {
		return AWSSecretsManagerClientBuilder.standard().withCredentials(new DefaultAWSCredentialsProviderChain())
				.withRegion(region).build();
	}

}
