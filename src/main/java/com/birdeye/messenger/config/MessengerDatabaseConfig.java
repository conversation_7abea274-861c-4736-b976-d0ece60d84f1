package com.birdeye.messenger.config;

import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;

import com.birdeye.messenger.constant.Constants;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

/**
 * Datasource configuration class for messenger service.
 * 
 * <AUTHOR>
 *
 */
@EnableConfigurationProperties
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(entityManagerFactoryRef = "messengerEntityManagerFactory", transactionManagerRef = "messengerTransactionManager",
						basePackages = {"com.birdeye.messenger.dao.repository"})

public class MessengerDatabaseConfig {

	@Autowired(required = false)
	private SecretsLoader secretsLoader;

	@Value("${aws.secret.enabled}")
	private String enabled;
	
	@Primary
	@Bean("messengerHikariConfig")
	@ConfigurationProperties(prefix = "spring.datasource.messenger")
	public HikariConfig hikariConfig() {
		return new HikariConfig();
	}

	@Primary
	@Bean("messengerDatasource")
	public DataSource dataSource() {
		return new HikariDataSource(updateHikariConfig());
	}

	@Primary
	@Bean(name = "messengerEntityManagerFactory")
	public LocalContainerEntityManagerFactoryBean mysqlEntityManagerFactory(EntityManagerFactoryBuilder builder) {
		return builder.dataSource(dataSource()).packages("com.birdeye.messenger.dao.entity")
				.persistenceUnit("messengerPU").build();
	}

	@Primary
	@Bean(name = "messengerTransactionManager")
	public PlatformTransactionManager mysqlTransactionManager(
			@Qualifier("messengerEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
		return new JpaTransactionManager(entityManagerFactory);
	}

	private HikariConfig updateHikariConfig() {
		HikariConfig hikariConfig = hikariConfig();
		if (Boolean.valueOf(enabled) && MapUtils.isNotEmpty(secretsLoader.getSecretsMap())) {
			hikariConfig
					.setJdbcUrl(getJdbcUrl(Constants.MESSENGER_MASTER_JDBC_URL, Constants.MESSENGER_MASTER_HOST_NAME,
							Constants.MESSENGER_MASTER_DB_NAME,
							Constants.MESSENGER_MASTER_DB_PORT));
			hikariConfig.setUsername(secretsLoader.getSecretsMap().get(Constants.MESSENGER_MASTER_DB_USERNAME));
			hikariConfig.setPassword(secretsLoader.getSecretsMap().get(Constants.MESSENGER_MASTER_DB_PASSWORD));
		}
		return hikariConfig;
	}

	private String getJdbcUrl(String jdbcUrl, String host, String dbName, String port) {
		String dbHost = secretsLoader.getSecretsMap().get(host);
		String dataBaseName = secretsLoader.getSecretsMap().get(dbName);
		String dbport = secretsLoader.getSecretsMap().get(port);
		return String.format(jdbcUrl, dbHost, dbport, dataBaseName);
	}
}
