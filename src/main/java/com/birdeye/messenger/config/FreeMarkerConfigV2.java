package com.birdeye.messenger.config;
import java.io.IOException;
import java.util.Locale;

import org.springframework.core.io.ClassPathResource;

import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;

import freemarker.cache.FileTemplateLoader;
import freemarker.cache.MruCacheStorage;
import freemarker.cache.MultiTemplateLoader;
import freemarker.cache.TemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapperBuilder;
import freemarker.template.TemplateExceptionHandler;

/**
 * <AUTHOR>
 *
 */
public class FreeMarkerConfigV2 {
	
	private static freemarker.template.Configuration config = new freemarker.template.Configuration(freemarker.template.Configuration.VERSION_2_3_25);
	
	static {
		ClassPathResource elasticQueryResource = new ClassPathResource("templates/elasticQuery");
		FileTemplateLoader esQueryTemplateLoader = null;
		try {
			esQueryTemplateLoader = new FileTemplateLoader(elasticQueryResource.getFile());
		} catch (IOException e) {
			throw new MessengerException(ErrorCode.ERROR_INIT_FILE_TEMPLATE_LOADER);
		}
		MultiTemplateLoader multiTemplateLoader = new MultiTemplateLoader(
				new TemplateLoader[] { esQueryTemplateLoader });
		config.setTemplateLoader(multiTemplateLoader);
		config.setCacheStorage(new MruCacheStorage(10, 20)); 
		config.setObjectWrapper(
				new DefaultObjectWrapperBuilder(freemarker.template.Configuration.VERSION_2_3_25).build());
		config.setDefaultEncoding("UTF-8");
		config.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
		config.setLocale(Locale.US);
	}

	private FreeMarkerConfigV2() {
	}
	
	public static Configuration getInstance() throws IOException {
		return config;
	}
}