package com.birdeye.messenger.config;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.integration.redis.util.RedisLockRegistry;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Configuration
@RequiredArgsConstructor
@Slf4j
@PropertySource("classpath:cache.properties")
@EnableConfigurationProperties(CacheConfigurationProperties.class)
public class CacheConfig implements CachingConfigurer {

	private final ConfigurableEnvironment environment;
	private final RedisCacheErrorHandler redisCacheErrorHandler;

	@Bean
	public RedisTemplate<String, String> redisTemplate() {
		RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
		redisTemplate.setKeySerializer(new StringRedisSerializer());
		redisTemplate.setHashKeySerializer(new StringRedisSerializer());
		redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
		redisTemplate.setValueSerializer(new StringRedisSerializer());
		redisTemplate.setConnectionFactory(jedisConnectionFactory());
		return redisTemplate;
	}

	@Bean(name = "redisPlatformTemplate")
	public RedisTemplate<String, String> redisTemplatePlatform() {

		RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
		redisTemplate.setKeySerializer(new StringRedisSerializer());
		redisTemplate.setValueSerializer(new StringRedisSerializer());
		redisTemplate.setConnectionFactory(jedisPlatformConnectionFactory());
		return redisTemplate;
	}

	@Bean
	@Primary
	public JedisConnectionFactory jedisConnectionFactory() {
		log.info("------------- [Redis Pool Config: JedisConnectionFactory] -----------");
		JedisConnectionFactory factory = new JedisConnectionFactory(redisStandaloneConfiguration(), poolConfig());
		return factory;
	}

	 @Bean(name = "jedisPlatformConnectionFactory")
	 public JedisConnectionFactory jedisPlatformConnectionFactory() {
		 log.info("------------- [Redis Pool Config: JedisPlatformConnectionFactory] -----------");
		 JedisConnectionFactory factory = new JedisConnectionFactory(redisPlatformStandaloneConfiguration(), poolConfig());
		 return factory;
	 }
	
	@Bean
	public RedisStandaloneConfiguration redisStandaloneConfiguration() {
		RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
		redisConfig.setDatabase(Integer.parseInt(environment.getProperty("spring.data.redis.database")));
		redisConfig.setHostName(environment.getProperty("spring.data.redis.host"));
		redisConfig.setPort(Integer.parseInt(environment.getProperty("spring.data.redis.port")));
		redisConfig.setPassword(RedisPassword.of(environment.getProperty("spring.data.redis.password")));
		log.info("[Redis Database Information] Connection Properties ::" + redisConfig.getDatabase() + " "
				+ redisConfig.getHostName() + " " + redisConfig.getPort());
		return redisConfig;
	}

	@Bean(name = "redisPlatformStandAloneConfig")
	public RedisStandaloneConfiguration redisPlatformStandaloneConfiguration() {
		RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
		redisConfig.setDatabase(Integer.parseInt(environment.getProperty("spring.data.redis.platform.database")));
		redisConfig.setHostName(environment.getProperty("spring.data.redis.host"));
		redisConfig.setPort(Integer.parseInt(environment.getProperty("spring.data.redis.port")));
		redisConfig.setPassword(RedisPassword.of(environment.getProperty("spring.data.redis.password")));
		log.info("[Redis Database Information] Connection Properties ::" + redisConfig.getDatabase() + " "
				+ redisConfig.getHostName() + " " + redisConfig.getPort());
		return redisConfig;
	}

	@Bean
	public JedisClientConfiguration poolConfig() {
		JedisClientConfiguration.JedisClientConfigurationBuilder jedisClientConfigrationBuilder = JedisClientConfiguration
				.builder();
		jedisClientConfigrationBuilder
				.readTimeout(Duration.ofSeconds(environment.getProperty("spring.data.redis.timeout", Long.class)));
		Boolean redisSSLEnabled = environment.getProperty("spring.data.redis.ssl.enabled", Boolean.class);
		if (redisSSLEnabled) {
			jedisClientConfigrationBuilder.useSsl();
		}
		jedisClientConfigrationBuilder.usePooling().poolConfig(getPoolConfig());
		return jedisClientConfigrationBuilder.build();
	}

	@SuppressWarnings("rawtypes")
	@Bean
	public GenericObjectPoolConfig getPoolConfig() {
		GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
		poolConfig.setMaxTotal(Integer.parseInt(environment.getProperty("spring.data.redis.jedis.pool.max-active")));
		poolConfig.setMaxIdle(Integer.parseInt(environment.getProperty("spring.data.redis.jedis.pool.max-idle")));
		poolConfig.setMinIdle(Integer.parseInt(environment.getProperty("spring.data.redis.jedis.pool.min-idle")));
		poolConfig.setTestOnBorrow(true);
		poolConfig.setTestOnReturn(true);
		poolConfig.setTestWhileIdle(true);
		poolConfig.setNumTestsPerEvictionRun(3);
		poolConfig.setBlockWhenExhausted(true);
		log.info("[Redis Pool Config] Pool configuration ::: {}", poolConfig);
		return poolConfig;
	}

	@Bean
	public RedisCacheWriter redisCacheWriter(RedisConnectionFactory connectionFactory) {
		return RedisCacheWriter.lockingRedisCacheWriter(connectionFactory);
	}

	/**
	 * As explained at
	 * https://programmerfriend.com/ultimate-guide-to-redis-cache-with-spring-boot-2-and-spring-data-redis/
	 */

	@Bean
	@ConditionalOnExpression("'${spring.cache.type}' != 'none'")
	public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory,
			CacheConfigurationProperties properties) {
		Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

		for (Entry<String, Long> cacheNameAndTimeout : properties.getTtls().entrySet()) {
			cacheConfigurations.put(cacheNameAndTimeout.getKey(),
					createCacheConfiguration(cacheNameAndTimeout.getValue()));
		}

		return RedisCacheManager.builder(redisConnectionFactory)
				.cacheDefaults(createCacheConfiguration(properties.getTimeoutSeconds()))
				.withInitialCacheConfigurations(cacheConfigurations).build();
	}

	/**
	 * Cache Config factory with JSON as default.
	 * 
	 * @param timeoutInSeconds
	 * @return
	 */
	private static RedisCacheConfiguration createCacheConfiguration(long timeoutInSeconds) {
		// ObjectMapper mapper = new Jackson2ObjectMapperBuilder()
		// .modulesToInstall( new SimpleModule().addSerializer( new
		// NullValueSerializer(null)) )
		// .failOnEmptyBeans( false )
		// .build();
		// mapper.enableDefaultTyping( ObjectMapper.DefaultTyping.NON_FINAL,
		// JsonTypeInfo.As.PROPERTY);

		RedisSerializationContext.SerializationPair<Object> jsonSerializer = RedisSerializationContext.SerializationPair
				.fromSerializer(new GenericJackson2JsonRedisSerializer());

		return RedisCacheConfiguration.defaultCacheConfig().disableCachingNullValues()
				.entryTtl(Duration.ofSeconds(timeoutInSeconds)).serializeValuesWith(jsonSerializer);
	}

	@Bean(destroyMethod = "destroy")
	public RedisLockRegistry redisLockRegistry(RedisConnectionFactory redisConnectionFactory) {
		return new RedisLockRegistry(redisConnectionFactory, "lock", 120000);
	}

	@Override
	public CacheErrorHandler errorHandler() {
		return redisCacheErrorHandler;
	}
}
