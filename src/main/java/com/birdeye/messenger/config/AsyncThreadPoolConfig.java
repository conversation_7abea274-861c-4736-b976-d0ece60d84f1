package com.birdeye.messenger.config;

import java.util.Map;
import java.util.concurrent.Executor;

import org.slf4j.MDC;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.aop.interceptor.SimpleAsyncUncaughtExceptionHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 *
 */
@Configuration
@EnableAsync
@PropertySource("classpath:threadpool.properties")
public class AsyncThreadPoolConfig implements AsyncConfigurer {

    @Autowired
    private Environment env;

    @Override
    public Executor getAsyncExecutor() {
        return threadPoolTaskExecutor();
    }

    Executor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor threadPoolExecutor = new ThreadPoolTaskExecutor();
        threadPoolExecutor.setCorePoolSize(env.getProperty("async.core.pool.size", Integer.class, 50));
        threadPoolExecutor.setMaxPoolSize(env.getProperty("async.max.pool.size", Integer.class, 300));
        threadPoolExecutor.setQueueCapacity(env.getProperty("async.pool.queue.size", Integer.class, 2000));
        threadPoolExecutor.setKeepAliveSeconds(env.getProperty("async.keep.alive.seconds", Integer.class, 60));
        threadPoolExecutor.setThreadNamePrefix("MessengerAsyncTask-");
        threadPoolExecutor.setTaskDecorator(mdcTaskDecorator());
        threadPoolExecutor.initialize();
        return threadPoolExecutor;
    }

    @Bean
    public TaskDecorator mdcTaskDecorator() {
        return new MdcTaskDecorator();
    }

    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }

    class MdcTaskDecorator implements TaskDecorator {

        @Override
        public Runnable decorate(Runnable runnable) {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    setContext(contextMap);
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        }

        private void setContext(Map<String, String> storedContext) {
            if (storedContext == null) {
                MDC.clear();
            } else {
                MDC.setContextMap(storedContext);
            }
        }
    }
}