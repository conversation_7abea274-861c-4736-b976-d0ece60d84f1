package com.birdeye.messenger.controller;

import jakarta.validation.Valid;

import com.birdeye.messenger.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.service.LiveChatService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/messenger/livechat")
public class LivechatController {

	@Autowired
	private LiveChatService liveChatService;

	/**
	 * API to initialise/return a singleton session for the provided customer information
	 * @param request
	 * @return
	 */
	@PostMapping(path = "/init")
	ResponseEntity<LiveChatInitResponseDTO> init(@RequestBody LiveChatInitDTO request){
		return new ResponseEntity<>(liveChatService.initialiseSession(request), HttpStatus.OK);
	}

	/**
	 * API to receive incoming messages from users, may respond via chatbot as per configuration
	 * Naming convention of API kept in sync with the webchat send API, though it receives a msg
	 * @param sessionId
	 * @param liveChatMessageDTO
	 * @return
	 */
	@PostMapping(path = "/send")
	ResponseEntity<Void> receiveMessage(
			@RequestHeader(value = "x-birdeye-livechat-session-id", required = true) String sessionId,
			@RequestBody LiveChatMessageDTO liveChatMessageDTO) {
		log.debug("received live chat message : {}", liveChatMessageDTO.getMessage());
		liveChatService.receiveMessage(sessionId, liveChatMessageDTO);
		//TODO Change it to ACCEPTED and handle it at platform.
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}
	
	/**
	 * API to receive incoming suggestion from user, if messages is a faq question may respond via cache/quero.
	 * If messages matches any predefined intent it will be answered via chatbot.
	 * @param sessionId
	 * @param liveChatMessageDTO
	 * @return
	 */
	@PostMapping(path = "/receive-suggestion")
	ResponseEntity<Void> receiveSuggestion(
			@RequestHeader(value = "x-birdeye-livechat-session-id", required = true) String sessionId,
			@RequestBody LiveChatMessageDTO liveChatMessageDTO) {
		log.debug("received suggestion message : {}", liveChatMessageDTO.getMessage());
		liveChatService.receiveSuggestion(sessionId, liveChatMessageDTO);
		//TODO Change it to ACCEPTED and handle it at platform.
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}
	
	/**
	 * API to perform session close operations for live-chat, when the user terminates/close the chat window.
	 * @param sessionId
	 * @return
	 */
	@PostMapping(path = "/session/{sessionId}/close")
	ResponseEntity<Void> sessionClose(@PathVariable(name = "sessionId") String sessionId, @RequestParam(name = "source", required=false) Integer source){
		liveChatService.sessionClose(sessionId, source);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * API invoked when the user is inactive over a chat and session needs to be timeout
	 * @param sessionId
	 * @return
	 */
	@PostMapping(path = "/session/{sessionId}/timeout")
	ResponseEntity<LiveChatSessionTimeoutResponse> sessionTimeout(@PathVariable(name = "sessionId") String sessionId, @RequestParam(name = "source", required=false) Integer source, @RequestParam(name = "widgetConfigId", required=false) Integer widgetConfigId,
			@RequestParam(name = "sendAutoReply",defaultValue = "false") boolean sendAutoReply,
			@RequestParam(name = "lastMessageReplied",defaultValue = "true") boolean lastMessageReplied) {
		return new ResponseEntity<>(liveChatService.sessionTimeout(sessionId, source,widgetConfigId,sendAutoReply, lastMessageReplied), HttpStatus.OK);
	}
	
	/**
	 * API invoked in case the user has refreshed the chat window, and we return an ACTIVE session.
	 * @param sessionId
	 * @return
	 */
	@PostMapping(path = "/session/{sessionId}/refresh")
	ResponseEntity<LiveChatSessionRefreshResponseDTO> sessionRefresh(@PathVariable(name = "sessionId") String sessionId, @RequestParam(name = "widgetConfigId", required=false) Integer widgetId, @RequestParam(name = "source", required=false) Integer source){
		log.info("received request for session refresh...sessionId : {}, widgetConfigId : {}", sessionId, widgetId);
		return new ResponseEntity<>(liveChatService.sessionRefresh(sessionId, widgetId, source), HttpStatus.OK);
	}
	
	/**
	 * API invoked to denote typing event on chat window
	 * @param sessionId, type -- customer or user
	 * @return
	 */
	@PostMapping(path = "/event/typing/{type}")
	ResponseEntity<Void> customerTypingEvent(@PathVariable(name = "type") String type,
			@RequestBody ConversationStateDTO event) {
		liveChatService.pushTypingStateChangeEvent(type, event);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * Terminate the stale sessions of live-chat
	 * API invoked via cronkins in a scheduled manner
	 * @return
	 */
	@PatchMapping(path = "/stale-sessions/terminate")
	ResponseEntity<Void> terminateStaleSessions(){
		liveChatService.terminateStaleSessions();
		return new ResponseEntity<>(HttpStatus.OK);
	}


	@PostMapping(value = "/consume/sqs/message")
	ResponseEntity<Void> consumeSQSMessage(@RequestBody ConversationStateDTO propertyModel) {
		liveChatService.consumeSQSMessage(propertyModel);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(path = "/user/feedback")
	ResponseEntity<Void> receiveFeedback(
			@Valid
			@RequestHeader(value = "x-birdeye-livechat-session-id") String sessionId,
			@RequestBody FeedbackRequest feedbackRequest) {
		log.debug("received Feedback : {}", feedbackRequest.getEventId());
		liveChatService.receiveFeedback(sessionId, feedbackRequest);
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@PostMapping(path = "/feedback/message/{sourceId}")
	ResponseEntity<Void> sendFeedbackMessage(
			@RequestHeader(value = "x-birdeye-livechat-session-id") String sessionId, @PathVariable(name = "sourceId") Integer sourceId) {
		log.debug("send feedback message for livechat session: {}",sessionId);
		liveChatService.sendFeedbackMessage(sessionId, sourceId);
		return new ResponseEntity<>(HttpStatus.NO_CONTENT);
	}

	@PostMapping(path = "/webchat/update-contact-details")
	ResponseEntity<Void> updateContactDetails(@RequestBody UpdateWebchatContactDetailsRequest request) throws Exception{
		log.debug("request receive to update contact details {}",request);
 		liveChatService.updateContactDetails(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}


}
