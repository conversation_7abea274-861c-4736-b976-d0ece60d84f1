package com.birdeye.messenger.controller;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.EmailCampaignEventResponse;
import com.birdeye.messenger.service.CampaignMessageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class CampaignController {

	private final CampaignMessageService campaignService;

	/**
	 * Campaign SMS Event Consumer
	 * 
	 * @param smsEvent - SMS ID belong to successful message sent.
	 */
	@RequestMapping(value = "/event/campaign", method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public void handleCampaignSMS(@RequestBody CampaignSMSDto smsEvent) {
		log.debug("Request received to push SMS data to messenger - {} ",smsEvent);
		campaignService.processCampaignEvent(smsEvent, false);
	}


	/**
	 * Campaign Email Event Consumer
	 * 
	 * @param emailCampaignEvent
	 */
	@RequestMapping(value = "/event/campaign/email", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handleEmailCampaign(@RequestBody EmailCampaignEventResponse emailCampaignEvent) {
		log.debug("Request received to push campaign event data to messenger - {} ", emailCampaignEvent);
		campaignService.processEmailCampaignEvent(emailCampaignEvent);
	}
	
	/**
	 * Campaign - Pulse Survey SMS - Context
	 */
	@RequestMapping(value = "/event/campaign/pulse-sms", method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public void handlePulseSurveyContext(@RequestBody CampaignSMSDto smsEvent) {
		log.debug("Request received to handle PulseSurveyContext to messenger - {} ",smsEvent);
		log.debug("Sms Dto received from campaign service is : {}",smsEvent.getSmsData().getCustomerId());
		campaignService.handlePulseSurveyContext(smsEvent);
	}
	
	/**
	 *
	 * API used to save (customerId, smsEvent) to redis for promotional campaign
	 * @param smsEvent
	 */
	@RequestMapping(value = "event/campaign/redis", method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public void saveCampaignEventOnRedis(@RequestBody CampaignSMSDto smsEvent) {
		log.debug("Request received to push campaign SMS data to redis - {} ",smsEvent);
		campaignService.saveCampaignEventOnRedis(smsEvent);
	}
}
