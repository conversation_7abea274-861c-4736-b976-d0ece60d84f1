package com.birdeye.messenger.controller;

        import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ContactUsEventDTO;
import com.birdeye.messenger.dto.CustomerEventDTO;
import com.birdeye.messenger.service.CustomerEventConsumerService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
public class CustomerEventConsumerController {

    public final CustomerEventConsumerService customerEventConsumerService;

    @PostMapping("/v1/on-customer-delete/{cId}")
    public void consumeCustomerDeleteEvent(@PathVariable("cId") Integer customerId) {
        customerEventConsumerService.onCustomerDelete(customerId);
    }

   
    @RequestMapping(value = "/update-contact", method = RequestMethod.PUT, consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> updateCustomerEvent(@RequestBody CustomerEventDTO customerEventDTO) {
        customerEventConsumerService.consumeCustomerUpdateEvent(customerEventDTO, true);
        return new ResponseEntity<>(HttpStatus.OK);
    }

	/**
	 * Endpoint for customer data migration.This endpoint performs the same
	 * operations as /update-contact but with Firebase sync turned off.
	 *
	 * @param customerEventDTO The customer event data to process
	 * @return HTTP 200 OK response
	 */
	@RequestMapping(value = "/update-contact-migration", method = RequestMethod.PUT, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> updateCustomerEventForMigration(@RequestBody CustomerEventDTO customerEventDTO) {
		customerEventConsumerService.consumeCustomerUpdateEvent(customerEventDTO, false);
		return new ResponseEntity<>(HttpStatus.OK);
	}

    /**
     * Used in contact-us flow - public, topic : contact-us-request
     * @param customerEventDTO
     * @return
     */
    @RequestMapping(value = "/contact-us", method = RequestMethod.POST, consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> contactUsEvent(@RequestBody ContactUsEventDTO request) throws Exception{
        customerEventConsumerService.consumeContactUsEvent(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
