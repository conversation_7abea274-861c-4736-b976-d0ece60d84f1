package com.birdeye.messenger.controller.apple.chat;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.apple.chat.AppleQuickReplyDto;
import com.birdeye.messenger.dto.apple.chat.AppleUserMessage;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadMessengerRequest;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadResponse;
import com.birdeye.messenger.service.impl.AppleChatReceiveHandler;
import com.birdeye.messenger.service.impl.AppleSendHandler;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/messenger/apple")
@RequiredArgsConstructor
public class AppleEventsController {

	private final AppleChatReceiveHandler appleChatReceiveHandler;
	private final AppleSendHandler appleSendHandler;

	@PostMapping(value = "/message", consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> receiveAppleChatMessage(@RequestBody AppleUserMessage appleUserMessage)
			throws Exception {
		appleChatReceiveHandler.handle(appleUserMessage);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping(value = "/preUpload/{businessId}", produces = { MediaType.APPLICATION_JSON_VALUE }, consumes = {
			MediaType.APPLICATION_JSON_VALUE })
	public ResponseEntity<AttachmentPreUploadResponse> getPreUploadData(
			@RequestBody AttachmentPreUploadMessengerRequest attachmentPreUploadMessengerRequest,
			@PathVariable(value = "businessId") Integer businessId) {
		return new ResponseEntity<>(
				appleSendHandler.getPreUploadDataFromSocial(attachmentPreUploadMessengerRequest, businessId),
				HttpStatus.OK);
	}
	@PostMapping(value = "/consume/sqs/message", produces = { MediaType.APPLICATION_JSON_VALUE }, consumes = {
			MediaType.APPLICATION_JSON_VALUE })
	public ResponseEntity<AttachmentPreUploadResponse> consumeQuickReplySQSMessage(
			@RequestBody AppleQuickReplyDto appleQuickReplyDto) throws Exception {
		appleSendHandler.consumeQuickReplySQSMessage(appleQuickReplyDto);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}