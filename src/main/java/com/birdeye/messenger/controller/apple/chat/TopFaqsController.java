/**
 * 
 */
package com.birdeye.messenger.controller.apple.chat;

import java.util.HashMap;
import java.util.Map;

import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest.FaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqUpdateRequest;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.service.apple.messaging.TopFaqService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/messenger/v1/top/faqs")
public class TopFaqsController {

    private final TopFaqService topFaqService;

    @PostMapping(value = "/batch/add", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<TopFaqResponse> addTopFaqs(@RequestBody @Valid TopFaqRequest topFaqRequest,
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID, required = false) Integer userId) {
        log.debug("addTopFaqs called with request : {},accountId : {},userId : {}", topFaqRequest, accountId, userId);
        topFaqRequest.setAccountId(accountId);
        topFaqRequest.setUserId(userId);
        TopFaqResponse topFaqResponse = topFaqService.addTopFaqs(topFaqRequest);
        return new ResponseEntity<>(topFaqResponse, HttpStatus.OK);
    }
    
    @PostMapping(value = "/add",consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Map<String,Integer>> addSingleTopFaq(@RequestBody @Valid FaqRequest faqRequest,@RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID, required = false) Integer userId) {
        Integer id=topFaqService.addSingleFaq(faqRequest, accountId, userId);
        Map<String,Integer> response=new HashMap<>(1);
        response.put("id", id);
        return new ResponseEntity<>(response,HttpStatus.OK);
    }

    @PutMapping(value = "/update/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> updateTopFaq(@PathVariable(value = "id") Integer id,
            @RequestBody @Valid FaqRequest faqRequest,
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID, required = false) Integer userId) {
        topFaqService.updateFaq(id, faqRequest, accountId, userId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @DeleteMapping(value = "/delete/{id}",produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> deleteTopFaq(@PathVariable(value = "id") Integer id){
        topFaqService.deleteFaq(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping(value = "/batch/update", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> updateTopFaqs(@RequestBody @Valid TopFaqUpdateRequest topFaqUpdateRequest,
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID, required = false) Integer userId) {
        topFaqUpdateRequest.setAccountId(accountId);
        topFaqUpdateRequest.setUserId(userId);
        topFaqService.updateTopFaqs(topFaqUpdateRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value = "/get", produces = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<TopFaqGetResponse> getTopFaqsForAccount(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestParam(value = "sortBy", defaultValue = "priority") String sortBy,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "6") Integer size,
            @RequestParam(value = "order", defaultValue = "DESC") SortOrderEnum order) {
        TopFaqGetResponse topFaqGetResponse = topFaqService.getTopFAQByAccountId(accountId, page, size, sortBy, order);
        return new ResponseEntity<>(topFaqGetResponse, HttpStatus.OK);

    }



}
