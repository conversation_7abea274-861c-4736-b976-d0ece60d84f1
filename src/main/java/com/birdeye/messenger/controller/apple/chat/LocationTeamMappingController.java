/**
 * 
 */
package com.birdeye.messenger.controller.apple.chat;

import java.util.List;

import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest;
import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest.LocationTeamMapping;
import com.birdeye.messenger.service.apple.messaging.LocationTeamMappingService;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/messenger/apple/location-team-mapping", produces = { MediaType.APPLICATION_JSON_VALUE })
public class LocationTeamMappingController {

    private static final String BUSINESS_IDS = "businessIds";

    private final LocationTeamMappingService locationTeamMappingService;

    @PutMapping(value = "/save", consumes = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<List<LocationTeamMapping>> saveLocationTeamMappings(
            @RequestHeader(value = Constants.ACC_ID_HEADER) Integer accountId,
            @RequestBody @Valid AppleLocationTeamMappingRequest appleLocationTeamMappingRequest) {
        appleLocationTeamMappingRequest.setAccountId(accountId);
        return new ResponseEntity<>(
                locationTeamMappingService.saveLocationTeamMappings(appleLocationTeamMappingRequest), HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<LocationTeamMapping>> getLocationTeamMappings(
            @RequestHeader(value = Constants.ACC_ID_HEADER) Integer accountId,
            @RequestParam(value = BUSINESS_IDS, required = false) List<Integer> businessIds) {
        return new ResponseEntity<>(locationTeamMappingService.getLocationTeamMappings(accountId, businessIds),
                HttpStatus.OK);
    }

    @DeleteMapping
    public ResponseEntity<Void> deleteLocationTeamMappings(
            @RequestHeader(value = Constants.ACC_ID_HEADER) Integer accountId,
            @RequestParam(value = BUSINESS_IDS, required = false) List<Integer> businessIds) {
        locationTeamMappingService.deleteLocationTeamMappings(accountId, businessIds);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
