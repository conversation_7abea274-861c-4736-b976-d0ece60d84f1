package com.birdeye.messenger.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ConversationClosedEvent;
import com.birdeye.messenger.service.EventPublisherService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequestMapping("/messenger")
@RestController
@Slf4j
@RequiredArgsConstructor
public class EventPublisherController {

	private final EventPublisherService eventPublisherService;
    @PostMapping(value = "/publish-conversation-closed-event", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> publishConversationClosedEvent(@RequestBody ConversationClosedEvent event) {

        //-------------- validate -----------------------
        Assert.notNull(event.getAccountId(), "Account Id must be present");
        Assert.notNull(event.getConversationId(), "Conversation Id must be present");
        eventPublisherService.publishConversationClosedEvent(event);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
