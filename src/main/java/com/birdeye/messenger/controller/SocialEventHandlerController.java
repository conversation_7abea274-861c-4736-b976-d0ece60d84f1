package com.birdeye.messenger.controller;

import com.birdeye.messenger.dto.InstagramMessageDto;
import com.birdeye.messenger.dto.LikeUnlikeEventSocial;
import com.birdeye.messenger.dto.MessageDeleteEventSocial;
import com.birdeye.messenger.dto.MessageDeleteRequest;
import com.birdeye.messenger.dto.instagram.LikeUnlikeRequest;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.InstagramMessageService;
import com.birdeye.messenger.service.MessengerDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/messenger")
public class SocialEventHandlerController{
    
    @Autowired
    private MessengerDashboardService messengerDashboardService;
    
    @Autowired
    private InstagramMessageService instagramMessageService;

    @PostMapping(value = "social/like-unlike-event", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> socialLikeUnlikeEvent(@RequestBody LikeUnlikeEventSocial request){
        LikeUnlikeRequest likeUnlikeRequest = new LikeUnlikeRequest();
        likeUnlikeRequest.setLike(request.getIsLiked());
        likeUnlikeRequest.setBusinessId(request.getBusinessId());
        likeUnlikeRequest.setSource(request.getChannel());
        likeUnlikeRequest.setMessageId(request.getFeedId());
        likeUnlikeRequest.setUserId(request.getUserId());
        likeUnlikeRequest.setAccountId(request.getAccountId());
        likeUnlikeRequest.setMessengerContactId(request.getMessengerContactId());
        messengerDashboardService.updateMessageLikeUnlike(likeUnlikeRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "social/delete-message-event", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> socialDeleteMessageEvent(@RequestBody MessageDeleteEventSocial request){
        MessageDeleteRequest deleteRequest = new MessageDeleteRequest();
        deleteRequest.setDelete(request.getDeleted());
        deleteRequest.setBusinessId(request.getBusinessId());
        deleteRequest.setSource(request.getChannel());
        deleteRequest.setMessageId(request.getFeedId());
        deleteRequest.setMessengerContactId(request.getMessengerContactId());
        messengerDashboardService.deleteMessage(deleteRequest,request.getAccountId(),request.getUserId());
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @GetMapping(value = "/instagram-id-for-mid/{messageId}")
    public ResponseEntity<InstagramMessageDto> getInstagramMessageId(@PathVariable("messageId") Integer messageId){
        return new ResponseEntity<>(instagramMessageService.getInstagramMessageId(messageId),HttpStatus.OK);
    }
    
}
