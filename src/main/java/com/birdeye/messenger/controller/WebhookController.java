package com.birdeye.messenger.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.WebhookEventRequest;
import com.birdeye.messenger.dto.WebhookEventResponse;
import com.birdeye.messenger.dto.WebhookSubscriptionRequest;
import com.birdeye.messenger.dto.WebhookSubscriptionResponse;
import com.birdeye.messenger.event.MessengerKafkaMessage;
import com.birdeye.messenger.service.WebhookService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class WebhookController {

	private final WebhookService webhookService;

	@RequestMapping(value = "/v1/webhook", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<WebhookSubscriptionResponse> createWebhookSubscription(
			@RequestBody WebhookSubscriptionRequest request) {
		WebhookSubscriptionResponse response = webhookService.createWebhookSubscription(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/{businessNumber}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<WebhookSubscriptionResponse> getSubscriptionForBusiness(
			@PathVariable(name = "businessNumber") Long businessNumber) {
		WebhookSubscriptionResponse response = webhookService.getSubscription(businessNumber);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/{accountId}/{eventName}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<WebhookSubscriptionResponse> getSubscriptionForBusinessByEvent(
			@PathVariable(name = "accountId") Integer accountId, @PathVariable(name = "eventName") String eventName) {
		WebhookSubscriptionResponse response = webhookService.getSubscriptionForBusinessByEvent(accountId, eventName);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/event", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<List<WebhookEventResponse>> createWebhookEvent(@RequestBody WebhookEventRequest request) {
		List<WebhookEventResponse> response = webhookService.createWebhookEvent(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/event", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<List<WebhookEventResponse>> getAllEvents() {
		List<WebhookEventResponse> response = webhookService.getAllEvents();
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/unsubscribe", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> unsubscribeWebhookEvent(@RequestBody WebhookSubscriptionRequest request) {
		Boolean response = webhookService.unsubscribeWebhookEvent(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

	@RequestMapping(value = "/v1/webhook/publish-external-event", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public <T> ResponseEntity<Void> publishExternalWebhookEvent(@RequestBody MessengerKafkaMessage<T> request) {
		webhookService.publishExternalWebhookEvent(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
