package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ClickTrackingRequest;
import com.birdeye.messenger.dto.GetAccountMessagesRequest;
import com.birdeye.messenger.dto.GetAccountMessagesResponse;
import com.birdeye.messenger.dto.LocationLevelRequest;
import com.birdeye.messenger.dto.SpamDetectionRequestDto;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.WebchatService;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class UtilityController {

	@Autowired
	private WebchatService webchatService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private SpamDetectionService spamDetectionService;
	
	@RequestMapping(value = "/location/widget", method = RequestMethod.POST)
	public ResponseEntity<Void> getLocationLevelwidgetDetails(@RequestBody LocationLevelRequest request) {
		webchatService.addLocationLevelWidgetRequests(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@RequestMapping(value = "/fetch/messages", method = RequestMethod.POST)
	public ResponseEntity<Object> getMessages(@RequestBody GetAccountMessagesRequest request) {
		GetAccountMessagesResponse response=commonService.getAccountMessages(request);
		return new ResponseEntity<>(response,HttpStatus.OK);
	}
	
	@RequestMapping(value = "/rerun/sms", method = RequestMethod.POST)
	public ResponseEntity<Void> rerunMissedSMS() throws Exception {
		commonService.rerunMissedSMS();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@RequestMapping(value = "/spam-audit-nlp", method = RequestMethod.POST)
	public ResponseEntity<Void> auditNlpSpamAnalysis(@RequestBody SpamDetectionRequestDto dto){
		spamDetectionService.checkSpamThroughNlp(dto);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@RequestMapping(value = "/click-track", method = RequestMethod.POST)
	public ResponseEntity<Void> clickTracking(@RequestBody ClickTrackingRequest request){
		commonService.clickTracking(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
