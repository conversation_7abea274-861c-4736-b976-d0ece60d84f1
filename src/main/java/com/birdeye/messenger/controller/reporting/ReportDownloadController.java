package com.birdeye.messenger.controller.reporting;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.dto.report.GenericDownloadReportDTO;
import com.birdeye.messenger.service.reporting.DownloadReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/messenger/report/download")
@Slf4j
@RequiredArgsConstructor
public class ReportDownloadController {

    private final DownloadReportService downloadReportService;

    @PostMapping("/by-time")
    public ResponseEntity<Object> downloadByTimeReports(@RequestBody @Valid DownloadReportFilter downloadFilter) throws Exception {
        log.debug("Download Over Time Request: {}", downloadFilter);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadReportByTime(downloadFilter);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }

    @PostMapping("/by-location")
    public ResponseEntity<Object> downloadAllReportByLocation(@RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex,
                                                              @RequestParam(value = "page-size", required = false, defaultValue = "25") Integer pageSize,
                                                              @RequestBody @Valid DownloadReportFilter downloadFilter) throws Exception {
        log.debug("Download Over Location Request: {}", downloadFilter);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadReportOverLocation(downloadFilter, startIndex, pageSize);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }

    @PostMapping("/by-user")
    public ResponseEntity<Object> downloadAllReportByUser(@RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex,
                                                          @RequestParam(value = "page-size", required = false, defaultValue = "25") Integer pageSize,
                                                          @RequestBody @Valid DownloadReportFilter downloadFilter) throws Exception {
        log.debug("Download Report By User Request: {}", downloadFilter);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadReportByUser(downloadFilter, startIndex, pageSize);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }
    
    @PostMapping("v1/robin-unanswered-questions")
    public ResponseEntity<Object> downloadRobinUnansweredQuestionsV1(@RequestParam(value = "start-index", required = true) Integer startIndex,
            @RequestParam(value = "page-size", required = true) Integer pageSize,
            @RequestBody @NotNull @Valid DownloadReportFilter downloadFilter) throws Exception {
    	 log.debug("Download Robin Unaswered Questions Request: {}", downloadFilter);
         GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadRobinUnansweredQuestionsV1(downloadFilter, startIndex, pageSize);
         return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);

    }
    @PostMapping("/robin-unanswered-questions")
    public ResponseEntity<Object> downloadRobinUnansweredQuestions(@RequestParam(value = "start-index", required = true) Integer startIndex,
            @RequestParam(value = "page-size", required = true) Integer pageSize,
            @RequestBody @NotNull @Valid DownloadReportFilter downloadFilter) throws Exception {
    	 log.debug("Download Robin Unaswered Questions Request: {}", downloadFilter);
         GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadRobinUnansweredQuestions(downloadFilter, startIndex, pageSize);
         return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);

    }
    @PostMapping("/robin/response/by-time")
    public ResponseEntity<Object> downloadRobinResponseByTimeReports(@RequestBody @Valid DownloadReportFilter downloadFilter) throws Exception {
        log.debug("Download robin Over Time Request: {}", downloadFilter);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadRobinResponseByTimeReports(downloadFilter);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }
    
    @PostMapping("/by-channel/messages-received/excel")
    public ResponseEntity<Object> downloadMessageReceivedChannelWiseReport(@RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex,
                                                                           @RequestParam(value = "page-size", required = false, defaultValue = "25") Integer pageSize,
                                                                           @RequestBody @Valid DownloadReportFilter downloadFilter,
                                                                           @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                           @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId) throws Exception {
        log.debug("Download message received channelWise Report Excel: {} , Start Index {} , pageSize {}", downloadFilter, startIndex, pageSize);
        downloadFilter.setAccountId(accountId);
        downloadFilter.setAccountTimeZoneId(timeZoneId);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadMessageReceivedChannelWiseReportsExcel(downloadFilter, startIndex, pageSize);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }

    @PostMapping("/by-channel/messages-received/pdf")
    public ResponseEntity<Object> downloadMessageReceivedChannelWiseReportPdf(@RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex,
                                                                              @RequestParam(value = "page-size", required = false, defaultValue = "25") Integer pageSize,
                                                                              @RequestBody @Valid DownloadReportFilter downloadFilter,
                                                                              @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                              @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId) throws Exception {
        log.debug("Download message received channelWise Report pdf: {} , Start Index {} , pageSize {}", downloadFilter, startIndex, pageSize);
        downloadFilter.setAccountId(accountId);
        downloadFilter.setAccountTimeZoneId(timeZoneId);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadMessageReceivedChannelWiseReportsPdf(downloadFilter, startIndex, pageSize);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }

    @PostMapping("/by-channel/active-conversation/excel")
    public ResponseEntity<Object> downloadActiveConversationChannelWiseReport(@RequestParam(value = "start-index", required = false, defaultValue = "0") Integer startIndex,
                                                                           @RequestParam(value = "page-size", required = false, defaultValue = "25") Integer pageSize,
                                                                           @RequestBody @Valid DownloadReportFilter downloadFilter,
                                                                           @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                           @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId) throws Exception {
        log.debug("Download active conversation channelWise Report Excel: {} , Start Index {} , pageSize {}", downloadFilter, startIndex, pageSize);
        downloadFilter.setAccountId(accountId);
        downloadFilter.setAccountTimeZoneId(timeZoneId);
        GenericDownloadReportDTO genericDownloadReportDTO = downloadReportService.downloadActiveConversationChannelWiseReportsExcel(downloadFilter, startIndex, pageSize);
        return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);
    }
    
}
