package com.birdeye.messenger.controller.reporting;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ResellerSetupReportRequest;
import com.birdeye.messenger.service.reporting.ResellerReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
@RestController
@RequestMapping("/messenger/reseller")
@Slf4j
@RequiredArgsConstructor
public class ResellerReportController {
	private final ResellerReportService resellerReportService; 
	
	@PostMapping("/setup-report")
    public ResponseEntity<Void> publishResellerSetUpReport(@RequestBody ResellerSetupReportRequest request) throws Exception {
        log.debug("[ResellerReportService] publishResellerSetUpReport :{}", request);
        resellerReportService.publishResellerSetUpReport(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
	
	@PostMapping("/adoption-report")
    public ResponseEntity<Void> publishResellerAdoptionReport(@RequestBody ResellerSetupReportRequest request) throws Exception {
        log.debug("[ResellerReportService] publishResellerAdoptionReport :{}", request);
        resellerReportService.publishResellerAdoptionReport(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
