package com.birdeye.messenger.controller.reporting;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.ResponseTimeCalculationService;
import com.birdeye.messenger.service.reporting.ReportHelperService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.*;
import com.birdeye.messenger.service.reporting.impl.DownloadReportServiceImpl;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.validator.ReportRequestValidator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/messenger/report")
@Slf4j
@RequiredArgsConstructor
public class ReportController {

    private final List<ReportService> reportServiceList;

    private final ReportHelperService reportHelperService;

    private final ResponseTimeCalculationService responseTimeCalculationService;

    @PostMapping(value = "/active-conversation-by-time", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> activeConversationsByTime(@RequestBody @Valid ReportFilter filters) throws Exception {
    	log.debug("[Report] Active Conversation by time {}", filters);
    	ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_TIME);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping(value = "/active-conversation-by-loc", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> activeConversationsByLocation(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Active Conversation By Location :{} startDate:{}, endDate:{}", filters, filters.getStartDate(), filters.getEndDate());
        ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_LOCATION);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping(value = "/active-conversation-by-user", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> activeConversationByUser(@RequestBody @Valid ReportFilter filters) throws Exception {
    	log.debug("[Report] Active Conversation by user {}", filters);
    	ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_USER);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping(value = "/received-conversation-by-loc", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> receivedConversationByLocation(@RequestBody @Valid ReportFilter filters) throws Exception {
    	log.debug("[Report] Received Conversation by location {}", filters);
    	ReportService reportService = getReportServiceByType(ReportType.RECEIVED_CONVERSATION_BY_LOCATION);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    private ReportService getReportServiceByType(ReportType type) {
        return reportServiceList.stream()
                .filter(reportService -> type.equals(reportService.getReportType()))
                .findFirst().orElseThrow(() -> new MessengerException(ErrorCode.INVALID_REPORT_TYPE));
    }

    @PostMapping(value = "/oldest-metric-date", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EarliestMetricDateDTO> getOldestMetricDate(@RequestBody @Valid ReportFilter filter) {
        EarliestMetricDateDTO earliestMessageDate = reportHelperService.getEarliestMessageDate(filter);
        return new ResponseEntity<>(earliestMessageDate, HttpStatus.OK);
    }

    @PostMapping("/received-messages-by-time")
    public ResponseEntity<Object> getReceiveMessagesReportByTime(@RequestBody @Valid ReportFilter filters) throws Exception {
    	log.debug("[Report] Received Messages by time {}", filters);
    	ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }
    
    @PostMapping("/robin-response-by-time")
    public ResponseEntity<Object> getRobinResponseByTime(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[report] robin response by time {}", filters);
        ReportService reportService = getReportServiceByType(ReportType.ROBIN_RESPONSE_BY_TIME);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/median-response-by-time")
    public ResponseEntity<Object> getMedianResponseOverTime(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Median Response Over Time :{}", filters);

        ReportService reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_OVER_TIME);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }
    @PostMapping("/dashboard/response-over-time")
    public ResponseEntity<Object> getDashboardInboxReportOverTime(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Median Response Over Time :{}", filters);

        ReportService reportService = getReportServiceByType(ReportType.DASHBOARD_INBOX_REPORT_OVER_TIME);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }
    
    @PostMapping("/mobile/received-message-count-by-time")
    public ResponseEntity<Integer> getReceivedMessageCountOverTime(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Mobile Recevied Message Count Over Time :{}", filters);
        ReportRequestValidator.validateCommonFields(filters);
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
        Integer receivedMessageCount = reportHelperService.getReceivedMessageCountOverTime(filters, reportService,filters.getAccountId(),filters.getBusinessIds().get(0));
        return new ResponseEntity<>(receivedMessageCount, HttpStatus.OK);
    }

	
    
    @PostMapping("/median-response-time-by-user")
    public ResponseEntity<Object> getMedianResponseTimeByUser(@RequestBody @Valid ReportFilter filters) throws Exception {

        log.debug("[Report] Median Response by User {}", filters);

        ReportRequestValidator.validateUserBasedReport(filters);
        ReportService reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_USER);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping(value = "/response-time-calc-consumer", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void responseTimeCalcConsumer(@RequestBody ResponseTimeOutBox message) {
        responseTimeCalculationService.saveResponseTime(message);
    }

    @PostMapping("/median-response-time-by-location")
    public ResponseEntity<Object> getMedianResponseTimeByLocation(@RequestBody @Valid ReportFilter filters) throws Exception {

        log.debug("[Report] Median Response by Location {}", filters);

        ReportRequestValidator.validateLocationBasedReportFilter(filters);
        ReportService reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION);
        Object report = reportService.getReport(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/agg-by-type/{reportType}")
    public ResponseEntity<Object> getAggReportByType(@RequestBody @Valid DownloadReportFilter downloadReportFilter, @PathVariable("reportType") String reportType) throws Exception {

        ReportFilter filters = DownloadReportServiceImpl.filterConversion(downloadReportFilter, downloadReportFilter.getStartIndex(), downloadReportFilter.getSize());
        ConsolidatedReportData data = new ConsolidatedReportData();


        if ("inbox-report-by-time".equals(reportType)) {

            Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
            Date startDate = new Date(startTS.getTime());
            Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
            Date endDate = new Date(endTS.getTime());
            GroupByType groupByType = GroupByType.fromString(filters.getInterval());

            ReportService medianTimeService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_OVER_TIME);
            MedianResponseOverTimeDTO medianResponseOverTimeRes = (MedianResponseOverTimeDTO) medianTimeService.getReport(filters);
            List<MedianResponseOverTimeDataPoint> medianResponseOverTimeDataPoint = medianResponseOverTimeRes.getDataPoints();
            DateUtils.customizeLabels(medianResponseOverTimeDataPoint, startDate, endDate, groupByType, false);
            data.setMedianTime(medianResponseOverTimeRes);


            ReportService activeConversationService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_TIME);
            ActiveConversationByTimeResponse activeConversationByTimeResponse = (ActiveConversationByTimeResponse) activeConversationService.getReport(filters);
            List<ActiveConversationDataPoint> activeConversationByTimeDataPoints = activeConversationByTimeResponse.getDataPoints();
            DateUtils.customizeLabels(activeConversationByTimeDataPoints, startDate, endDate, groupByType, false);
            data.setActiveConversation(activeConversationByTimeResponse);

            ReportService receivedMessagesService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
            ReceivedMessagesByTimeResponse receivedMessagesByTimeResponse = (ReceivedMessagesByTimeResponse) receivedMessagesService.getReport(filters);
            List<ReceivedMessagesDataPoint> receivedMessagesByTimeDataPoints = receivedMessagesByTimeResponse.getDataPoints();
            DateUtils.customizeLabels(receivedMessagesByTimeDataPoints, startDate, endDate, groupByType, false);
            data.setReceivedMessages(receivedMessagesByTimeResponse);

        } else if ("inbox-report-by-loc".equals(reportType)) {

            ReportService medianTimeService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION);
            data.setMedianTime(medianTimeService.getReport(filters));
            ReportService activeConversationService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_LOCATION);
            data.setActiveConversation(activeConversationService.getReport(filters));
            ReportService receivedMessagesService = getReportServiceByType(ReportType.RECEIVED_CONVERSATION_BY_LOCATION);
            data.setReceivedMessages(receivedMessagesService.getReport(filters));

        } else if ("inbox-report-by-user".equals(reportType)) {

            ReportService medianTimeService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_USER);
            data.setMedianTime(medianTimeService.getReport(filters));
            ReportService activeConversationService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_USER);
            data.setActiveConversation(activeConversationService.getReport(filters));

        } else if("robin-report-by-time".equals(reportType)) {
            Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
            Date startDate = new Date(startTS.getTime());
            Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
            Date endDate = new Date(endTS.getTime());
            GroupByType groupByType = GroupByType.fromString(filters.getInterval());

            ReportService robinResponseReportService = getReportServiceByType(ReportType.ROBIN_RESPONSE_BY_TIME);
            RobinResponseByTime robinResponseData = (RobinResponseByTime) robinResponseReportService.getReport(filters);
            List<RobinResponseDataPoint> dataPoints = robinResponseData.getDataPoints();
            DateUtils.customizeLabels(dataPoints, startDate, endDate, groupByType, false);
            data.setRobinResponse(robinResponseData);
        }

        ReportWrapper<ConsolidatedReportData> wrapper = new ReportWrapper<>();
        wrapper.setPdfData(data);
        return new ResponseEntity<>(wrapper, HttpStatus.OK);
    }

    @PostMapping(value = "/v2/active-conversation-by-time", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> activeConversationsByTimeV2(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Active Conversation by time V2 {}", filters);
        ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_TIME);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/v2/received-messages-by-time")
    public ResponseEntity<Object> getReceiveMessagesReportByTimeV2(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Received Messages by time V2 {}", filters);
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/v2/robin-response-by-time")
    public ResponseEntity<Object> getRobinResponseByTimeV2(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[report] robin response by time V2 {}", filters);
        ReportService reportService = getReportServiceByType(ReportType.ROBIN_RESPONSE_BY_TIME);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/v2/median-response-by-time")
    public ResponseEntity<Object> getMedianResponseOverTimeV2(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Median Response Over Time V2 :{}", filters);

        ReportService reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_OVER_TIME);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/v2/dashboard/response-over-time")
    public ResponseEntity<Object> getDashboardInboxReportOverTimeV2(@RequestBody @Valid ReportFilter filters) throws Exception {
        log.debug("[Report] Median Response Over Time V2 :{}", filters);

        ReportService reportService = getReportServiceByType(ReportType.DASHBOARD_INBOX_REPORT_OVER_TIME);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/message-volume-profile")
    public ResponseEntity<Object> getMessageVolumeProfileReport(@RequestBody @Valid ReportFilter filters,
                                                                @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId,
                                                                @RequestHeader(Constants.USER_ID_HEADER) Integer userId) throws Exception {
        log.debug("[Report] Get Message Volume Profile Report :{}", filters);
        filters.setAccountId(accountId);
        filters.setAccountTimeZoneId(timeZoneId);
        ReportService reportService = getReportServiceByType(ReportType.MESSAGE_VOLUME_PROFILE);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/received-messages-by-channel")
    public ResponseEntity<Object> getReceiveMessagesReportByTimeChannelWise(@RequestBody @Valid ReportFilter filters,
                                                                            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                            @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId,
                                                                            @RequestHeader(value=Constants.USER_ID_HEADER,required=false) Integer userId) throws Exception {
        log.debug("[Report] Received Messages by time channel wise {}", filters);
        filters.setAccountId(accountId);
        filters.setAccountTimeZoneId(timeZoneId);
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_CHANNEL_WISE);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/active-conversation-by-channel")
    public ResponseEntity<Object> getActiveConversationReportChannelWise(@RequestBody @Valid ReportFilter filters,
                                                                         @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                                         @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId,
                                                                         @RequestHeader(value=Constants.USER_ID_HEADER,required=false) Integer userId) throws Exception {
        log.debug("[Report] Active Conversation channel wise report {}", filters);
        filters.setAccountId(accountId);
        filters.setAccountTimeZoneId(timeZoneId);
        ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_CHANNEL_WISE);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }

    @PostMapping("/inbox-roi-report")
    public ResponseEntity<Object> getInboxRoiReport(@RequestBody @Valid ReportFilter filters,
                                                    @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                    @RequestHeader(Constants.TIME_ZONE_ID) String timeZoneId,
                                                    @RequestHeader(value=Constants.USER_ID_HEADER,required=false) Integer userId) throws Exception {
        filters.setAccountId(accountId);
        filters.setAccountTimeZoneId(timeZoneId);
        ReportService reportService = getReportServiceByType(ReportType.INBOX_ROI_REPORT);
        Object report = reportService.getReportV2(filters);
        return new ResponseEntity<>(report, HttpStatus.OK);
    }
    
}
