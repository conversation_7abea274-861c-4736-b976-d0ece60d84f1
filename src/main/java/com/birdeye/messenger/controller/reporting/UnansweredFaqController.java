package com.birdeye.messenger.controller.reporting;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.UnansweredFaqAnsweredRequest;
import com.birdeye.messenger.dto.UnansweredFaqDeleteRequest;
import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.dto.report.GenericDownloadReportDTO;
import com.birdeye.messenger.service.MessageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/messenger/faq")
@Slf4j
@RequiredArgsConstructor
public class UnansweredFaqController {

	private final MessageService messageService;

	/**
	 * BIRD-45779 : Called from UI to get unanswered questions
	 * @param startIndex
	 * @param pageSize
	 * @param downloadFilter
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/robin-unanswered-questions")
	public ResponseEntity<Object> getRobinUnansweredFaq(@RequestParam(value = "start-index", required = true) Integer startIndex,
			@RequestParam(value = "page-size", required = true) Integer pageSize,
			@RequestBody @NotNull @Valid DownloadReportFilter downloadFilter) throws Exception {
		log.debug("Download Robin Unanswered Questions Request: {}, start-index:{}, page-size:{}", downloadFilter, startIndex, pageSize);
		GenericDownloadReportDTO genericDownloadReportDTO = messageService.getRobinUnansweredFaq(downloadFilter, pageSize, startIndex);
		return new ResponseEntity<>(genericDownloadReportDTO, HttpStatus.OK);

	}

	/**
	 * Called from UI for bulk delete unanswered questions : mark unansweredFaqType as DELETED
	 * @param updateRequest
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/delete-unanswered-questions")
	public ResponseEntity<Object> updateRobinUnansweredFaqDelete(@RequestBody @NotNull @Valid UnansweredFaqDeleteRequest updateRequest) throws Exception {
		log.debug("Update Robin Unaswered Questions Request: {}", updateRequest);
		messageService.updateRobinUnansweredFaqDelete(updateRequest);
		return new ResponseEntity<>(HttpStatus.OK);

	}
	
	/**
	 * API call from FAQ : Upon adding answer for a question
	 * @param updateRequest
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/update-unanswered-questions")
	public ResponseEntity<Object> updateRobinUnansweredFaq(@RequestBody @NotNull @Valid UnansweredFaqAnsweredRequest updateRequest) throws Exception {
		log.debug("Update Robin Unaswered Questions Request: {}", updateRequest);
		messageService.updateRobinUnansweredFaq(updateRequest);
		return new ResponseEntity<>(HttpStatus.OK);

	}

	@PostMapping("/migrate-unanswered-qualified")
	public ResponseEntity<Object> migrateUnansweredQualifiedQuestions() throws Exception {
		log.debug("Migrate Robin Unanswered Questions");
		messageService.migrateUnansweredQualifiedQuestions();
		return new ResponseEntity<>(HttpStatus.OK);

	}
}
