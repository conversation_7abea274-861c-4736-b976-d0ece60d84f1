package com.birdeye.messenger.controller;

import java.util.List;
import java.util.Objects;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.instagram.LikeUnlikeRequest;
import com.birdeye.messenger.util.LogUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.service.CommunicationService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.MessengerDashboardService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class MessengerDashboardController {

	private final CommunicationService communicationService;
	private final MessengerDashboardService messengerDashboardService;
	private final ConversationService conversationService;

	@RequestMapping(value = "/refresh", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Boolean> doRefresh(@RequestHeader(Constants.ACC_ID_HEADER) String accountId,
			@RequestHeader(Constants.USER_ID_HEADER) String userId, @RequestBody MessengerGlobalFilter filter) {
		boolean doRefresh = messengerDashboardService.doRefresh(accountId, filter);
		if (doRefresh) {
			log.info("refresh for new msg.");
			return ResponseEntity.ok().build();
		} else
			return new ResponseEntity<>(HttpStatus.NOT_MODIFIED);
	}

	@Deprecated
	@RequestMapping(value = "/conversations", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<Object> getConversations(@RequestBody MessengerFilter filter) {
		 // Object response = messengerDashboardService.getConversations(filter);
		Object response = messengerDashboardService.getConversationsV2(filter);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
	
    @RequestMapping(value = "/get-message", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getMessage(@RequestBody MessengerFilter messengerFilter, @RequestParam(value = "platform", defaultValue = "WEB") String platform) throws Exception {
        return new ResponseEntity<>(communicationService.getMessage(messengerFilter, platform),HttpStatus.OK);
    }

    @RequestMapping(value = "/get-notifications", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
    public ResponseEntity<MessengerNotificationMessage> getNotification(@RequestBody NotificationFilter notificationFilter,@RequestHeader(value = "request-source", required = false) String requestSource) throws Exception {
       MessengerNotificationMessage result = messengerDashboardService.getNotification(notificationFilter,requestSource);
        return new ResponseEntity<>(result,HttpStatus.OK);
    }

	@RequestMapping(value = "/update/facebook-contact/{messengerContactId}", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<ContactMergeStatus> updateContactInfo(@PathVariable("messengerContactId") Integer messengerContactId, @RequestBody CustomerDTO customerDTO) {
		ContactMergeStatus contactMergeStatus = messengerDashboardService.updateFacebookContact(customerDTO, messengerContactId);
		return new ResponseEntity<>(contactMergeStatus, HttpStatus.OK);
	}

	@RequestMapping(value = "/add-and-send", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<MessageResponse> addNewContactAndSendSms(@RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId, @RequestBody AddContactMessengerMessage message) throws Exception {
		if (Objects.nonNull(userId)) message.setUserId(userId); //Take user from header
		MessageResponse messageResponse = messengerDashboardService.addNewContactAndSendSms(accountId, message);
		return new ResponseEntity<>(messageResponse, HttpStatus.OK);
	}


	/**
	 * Conversation Tag update API 1. Open/Close 2. Mark Read/Unread
	 */
	@RequestMapping(value = "/update-tag/{id}", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void updateConversationTag(@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
			@RequestHeader(Constants.USER_ID_HEADER) Integer userId, @PathVariable("id") Integer conversationId,
			@RequestParam("tag") Integer tag) {
		conversationService.updateConversationTag(accountId, userId, conversationId, tag);
	}
	
	/**
	 * Conversation Status update API 1. Open/Close 2. Mark Read/Unread
	 */
	@RequestMapping(value = "/update-status/{id}", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void updateConversationStatus(@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
			@RequestHeader(Constants.USER_ID_HEADER) Integer userId, @PathVariable("id") Integer conversationId,
			@RequestBody ConversationStatusRequest contactMessageRequest) {
		conversationService.updateStatus(accountId, userId, conversationId, contactMessageRequest,false);
	}

	@RequestMapping(value = "/conversations-old", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<Object> getConversationsOld(@RequestBody MessengerFilter filter) {
		Object response = messengerDashboardService.getConversations(filter);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	@RequestMapping(value = "/v1/dashboard-locations", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<MessengerLocation>> getConversations(@RequestBody NotificationRequest request, @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) {
		List<MessengerLocation> dashboardLocations = messengerDashboardService.getDashboardLocations(request, userId, accountId);
        return new ResponseEntity<>(dashboardLocations, HttpStatus.OK);
    }
	
	@RequestMapping(value = "/switch-chat-location", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<MessageResponse> switchChatLocation(@RequestBody ChatTransferMessage message,
															  @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,@RequestHeader(value = "request-source", required = false) String requestSource) throws Exception {
		MessageResponse messageResponse = messengerDashboardService.switchChatLocation(message, userId, accountId, requestSource);
		return new ResponseEntity<>(messageResponse, HttpStatus.OK);
	}
	
	@RequestMapping(value = "/message-like-unlike", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	@ResponseStatus(code = HttpStatus.OK)
	public void likeUnlikeMessage(@RequestBody LikeUnlikeRequest request,
								  @RequestHeader(value=Constants.USER_ID_HEADER,required=false) Integer userId, @RequestHeader(value=Constants.ACC_ID_HEADER,required=false) Integer accountId) throws Exception {
		request.setPublishSocialEvent(true);
		if(Objects.isNull(request.getUserId())){
			request.setUserId(userId);
		}
		if(Objects.isNull(request.getAccountId()))
		{
			request.setAccountId(accountId);
		}
		messengerDashboardService.updateMessageLikeUnlike(request);
	}

	@RequestMapping(value = "/delete-message", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	@ResponseStatus(code = HttpStatus.OK)
	public void deleteMessage(@RequestBody MessageDeleteRequest request,
								  @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
		request.setPublishSocialEvent(true);
		messengerDashboardService.deleteMessage(request,accountId,userId);
	}
	@RequestMapping(value = "/move-message", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	@ResponseStatus(code = HttpStatus.OK)
	public ResponseEntity<Integer>  moveMessage(@RequestBody MessageMoveRequest request,
								  @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
		return new ResponseEntity<>(messengerDashboardService.moveMessage(request,accountId,userId), HttpStatus.OK);
	}
}
