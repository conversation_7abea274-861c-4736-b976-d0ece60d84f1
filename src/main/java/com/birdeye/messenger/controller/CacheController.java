package com.birdeye.messenger.controller;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.EnableInboxPerformance;
import com.birdeye.messenger.dto.leadgenagent.AddLeadGenAgentAccountCacheDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ErrorCodesMessageRequest;
import com.birdeye.messenger.service.CacheService;
import com.birdeye.messenger.service.RobinAutoReplyConfigSetupService;
import com.birdeye.messenger.sro.EnableAIChatbotRequest;
import com.birdeye.messenger.sro.MessengerPropertyRequest;

@RestController
@RequestMapping("/messenger")
public class CacheController {

	@Autowired
	CacheService cacheService;

	@Autowired
	private RobinAutoReplyConfigSetupService robinAutoReplyConfigSetupService;
	
	@RequestMapping(value = "/getCacheValues", method = RequestMethod.GET)
    public ResponseEntity<Object> getCacheValues() {
		Map<String, String> response = cacheService.getCacheValues();
		return new ResponseEntity<>(response, HttpStatus.OK);
    }
	
	@RequestMapping(value = "/addCacheValue", method = RequestMethod.POST)
    public ResponseEntity<Void> addCacheValue(@RequestBody MessengerPropertyRequest messengerPropertyRequest) {
		cacheService.addCacheValue(messengerPropertyRequest);
		return new ResponseEntity<>(HttpStatus.OK);
    }
	
	@RequestMapping(value = "/reloadCache", method = RequestMethod.POST)
    public ResponseEntity<String> reloadCache() {
		cacheService.loadAllCache();
		return new ResponseEntity<>("true", HttpStatus.OK);
    }

	@RequestMapping(value = "/evict-robin-autoreply-config-cache/{businessId}",method = RequestMethod.POST)
	public ResponseEntity<Void> evictRobinAutoreplyConfigCache(@RequestHeader Integer businessId){
		robinAutoReplyConfigSetupService.evictRobinAutoReplyConfigCache(businessId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@RequestMapping(value = "/addErrorCode", method = RequestMethod.POST)
	public ResponseEntity<Void> addErrorCode(@RequestBody List<ErrorCodesMessageRequest> errorCodesMessageRequest) {
		cacheService.addErrorCodes(errorCodesMessageRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@RequestMapping(value = "/deleteCacheValue", method = RequestMethod.POST)
	public ResponseEntity<Void> deleteCacheValue(@RequestBody MessengerPropertyRequest messengerPropertyRequest) {
		cacheService.deleteCacheValue(messengerPropertyRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * Enable AI chatbot - combines 4 calls - 1 inbox + 3 quero
	 * @param enableAIChatbotRequest
	 * @return
	 * @throws Exception 
	 */
	@RequestMapping(value = "/enableAIChatbot", method = RequestMethod.POST)
	public ResponseEntity<Void> enableAIChatbot(@RequestBody EnableAIChatbotRequest enableAIChatbotRequest) throws Exception {
		cacheService.enableAIChatbot(enableAIChatbotRequest);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * Api To Enable performance improvement on accounts
	 */
	@RequestMapping(value = "/enableInboxPerformance", method = RequestMethod.POST)
	public ResponseEntity<Void> enableInboxPerformance(@RequestBody EnableInboxPerformance enableInboxPerformance) throws Exception {
		cacheService.enableAIPerformance(enableInboxPerformance);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * Api to reload/add account and agent cache
	 */
	@RequestMapping(value = "/addLeadGenAgentForAccount", method = RequestMethod.POST)
	public ResponseEntity<Void> addLeadGenAgentForAccount(@RequestBody AddLeadGenAgentAccountCacheDTO request) throws Exception {
		//functionality to add lead gen agent for account
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
