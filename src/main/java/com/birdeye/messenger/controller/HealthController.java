package com.birdeye.messenger.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/messenger")
public class HealthController {

	@GetMapping(value = "/health-check")
	public ResponseEntity<Object> healthCheck() {
		Map<String, String> status = new HashMap<>();
		status.put("status", "UP");
		return new ResponseEntity<>(status, HttpStatus.OK);
	}
}