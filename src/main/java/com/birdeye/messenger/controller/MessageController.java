package com.birdeye.messenger.controller;


import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ConversationHistoryRequest;
import com.birdeye.messenger.dto.LiveChatFetchMessageRequest;
import com.birdeye.messenger.dto.MessageClientIpResponse;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.service.MessageService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
public class MessageController {

    private final MessageService messageService;

    @RequestMapping(value = "/v1/messages", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<MessageResponse> getMessages(@Valid @RequestBody MessageRequest request,
			@RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,@RequestHeader(value = "request-source", required = false) String requestSource) throws Exception {
		MessageResponse response = messageService.getMessageV2(request, false, userId, accountId, requestSource);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}

    /**
     * API to fetch the livechat messages on the livechat window to populate the historic chats of the customer
     * @param liveChatFetchMessageRequest
     * @return
     */
    @PostMapping(path = "/livechat/messages")
    public ResponseEntity<MessageResponse> getLivechatMessages(@Valid @RequestBody LiveChatFetchMessageRequest liveChatFetchMessageRequest){
    	MessageResponse response = messageService.getLiveChatMessages(liveChatFetchMessageRequest);
    	return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    @RequestMapping(value = "/conversation-history", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	  public ResponseEntity<MessageResponse> getConversationHistoryMessages(@Valid @RequestBody ConversationHistoryRequest request,
			@RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
		  MessageResponse response = messageService.getConversationHistoryMessages(request, userId, accountId);
		  return new ResponseEntity<>(response, HttpStatus.OK);
	  }
  
  /**
   * API to return ip Address for webchat/livechat messages
   */
   @RequestMapping(value = "/v1/getClientIps", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	 public ResponseEntity<MessageClientIpResponse> getClientIps(@RequestBody MessageRequest request) {
    	MessageClientIpResponse response = messageService.getClientIps(request);
      return new ResponseEntity<>(response, HttpStatus.OK);
   }
}
