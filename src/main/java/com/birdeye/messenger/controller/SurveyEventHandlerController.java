package com.birdeye.messenger.controller;


import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.service.SurveyEventHandlerService;
import com.birdeye.messenger.sro.SurveyEvent;
import com.birdeye.messenger.sro.SurveyNameChangeEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class SurveyEventHandlerController {

    @Autowired
    private SurveyEventHandlerService surveyEventHandlerService;
    //ADD/UPDATE/ surveys
    @PostMapping(value = "/survey/event")
    public void handleSurveyEvent(@RequestBody @Valid SurveyEvent surveyEvent) throws Exception {
        log.debug("Request received to create/update survey :{} ",surveyEvent);
        surveyEventHandlerService.handleSurveyEvent(surveyEvent);
    }

    //survey Name change event
    @PostMapping(value = "/survey/name/change/event")
    public void updateSurveyName(@RequestBody @Valid SurveyNameChangeEvent surveyNameChangeEvent) throws Exception {
        log.debug("Request received to update survey name :{} ",surveyNameChangeEvent);
        surveyEventHandlerService.updateSurveyName(surveyNameChangeEvent);
    }


}
