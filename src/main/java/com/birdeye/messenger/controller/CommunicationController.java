package com.birdeye.messenger.controller;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;
import static com.birdeye.messenger.constant.Constants.USER_ID_HEADER;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.service.CommunicationService;
import com.birdeye.messenger.service.MessageRetryService;
import com.birdeye.messenger.service.NotificationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class CommunicationController {

    private final NotificationService notificationService;
    private final CommunicationService communicationService;
	@Autowired
	private MessageRetryService messageRetryService;

    @RequestMapping(value = "/send-notification", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> sendNotification(@RequestBody MessengerGlobalFilter messengerGlobalFilter) {
        notificationService.sendEmailNotification(messengerGlobalFilter, new MessageDocument());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/send-notificationV2", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> sendNotificationV2(@RequestBody MessengerGlobalFilter messengerGlobalFilter) {
        notificationService.sendEmailNotificationV2(messengerGlobalFilter, new MessageDocument());
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @RequestMapping(value = "/ext-mc-id/{ecid}/{businessId}", method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    @Deprecated
    public ResponseEntity<MessengerContact> getMessengerContact(@PathVariable("ecid") Integer ecId, @PathVariable("businessId") Long businessId, @RequestParam(value = "isBizNumber", required = false) Boolean isBizNumber) {
        MessengerContact messengerContact = communicationService.getMessengerContact(ecId, businessId, Objects.isNull(isBizNumber) ? false : isBizNumber);
        return new ResponseEntity<>(messengerContact, HttpStatus.OK);
    }

	@RequestMapping(value = "/retry-sms/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<MessengerMessage.Message> retryMessage(@PathVariable("id") Integer id,
			@RequestParam(value = "mcId", required = false) Integer mcId,
			@RequestParam(value = "source", defaultValue = "1") Integer source,
			@RequestHeader(USER_ID_HEADER) Integer loggedInUserId, @RequestHeader(ACC_ID_HEADER) Integer accountId) {
		MessengerMessage.Message message = messageRetryService.retryMessage(id, mcId, accountId, loggedInUserId,
				source);
		return new ResponseEntity<>(message, HttpStatus.OK);
	}
	
	@RequestMapping(value = "/send-notification-scheduled", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> sendNotificationScheduled(@RequestBody MessengerGlobalFilter messengerGlobalFilter) {
		MessageDocument messageDocument = new MessageDocument();
		messageDocument.setSource(messengerGlobalFilter.getSource());
		notificationService.sendEmailNotificationScheduled(messengerGlobalFilter, messageDocument);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/mcid-by-review-id/{reviewId}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Object> getMcIdByReviewId(@PathVariable("reviewId") Integer reviewId,
			@RequestParam(value = "accountId") Integer accountId) {
		log.debug("Get mcid for reviewId: {}", reviewId);
		Object mcId = communicationService.getMcIdByReviewId(reviewId, accountId);
		return new ResponseEntity<>(mcId, HttpStatus.OK);
	}

	@RequestMapping(value = "/send-notification-unresponded", method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> sendNotificationUnResponded(@RequestBody MessengerGlobalFilter notificationRequest) {
		MessageDocument messageDocument = new MessageDocument();
		messageDocument.setSource(notificationRequest.getSource());
		notificationService.sendEmailNotificationUnResponded(notificationRequest, messageDocument);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
