package com.birdeye.messenger.controller;

import com.birdeye.messenger.dto.ReferralEvent;
import com.birdeye.messenger.dto.ThankYouNoteEvent;
import com.birdeye.messenger.service.ReferralLeadGenEventConsumer;
import com.birdeye.messenger.service.ReferrerThankYouNoteConsumer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/messenger/referral")
@RequiredArgsConstructor
@Slf4j
public class ReferralEventConsumerController {

    private final ReferralLeadGenEventConsumer referralLeadGenEventConsumer;
    private final ReferrerThankYouNoteConsumer referrerThankYouNoteConsumer;

    @PostMapping(value = "/referral-lead-event", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void consumeReferralLeadGenEvent(@RequestBody @Valid ReferralEvent referralEvent) {
        referralLeadGenEventConsumer.consumeEvent(referralEvent);
    }

    @PostMapping(value = "/thank-you-note-event", consumes = MediaType.APPLICATION_JSON_VALUE)
    public void consumeThankYouNoteEvent(@RequestBody @Valid ThankYouNoteEvent thankYouNoteEvent) {
        referrerThankYouNoteConsumer.consumeEvent(thankYouNoteEvent);
    }
}
