package com.birdeye.messenger.controller;

import java.util.Map;

import com.birdeye.messenger.dto.ReceptionistTextToSpeechTokenReplaceRequest;
import com.birdeye.messenger.dto.ReceptionistTextToSpeechTokenReplaceResponse;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.service.ReceptionistService;
import com.birdeye.messenger.sro.BusinessMissedCallSetting;
import com.birdeye.messenger.sro.ReceptionistConfigurationMessage;
import com.birdeye.messenger.sro.ReceptionistGreetingRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class ReceptionistController {

	private final ReceptionistService receptionistService;
	
	/**
	 * Get Receptionist configuration : Configuration are at account level
	 * @param enterpriseId
	 * @return
	 */
	@RequestMapping(value = "/receptionist/config/{enterpriseId}", method = RequestMethod.GET)
	public ResponseEntity<Map<?, ?>> getBusinessReceptionistConfiguration(@PathVariable ("enterpriseId") Long enterpriseId) throws Exception {
		log.debug("Receptionist config get request for business {} ", enterpriseId);
		Map<?, ?> receptioninstConfig = receptionistService.getReceptionistConfiguration(enterpriseId);
		return new ResponseEntity<>(receptioninstConfig, HttpStatus.OK);
	}
	
	/**
	 * Save Receptionist configuration
	 * @param enterpriseId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/receptionist/config/{enterpriseId}", method = RequestMethod.POST)
	public ResponseEntity<Void> saveBusinessReceptionistConfiguration(@PathVariable ("enterpriseId") Long enterpriseId, @RequestBody ReceptionistConfigurationMessage request) {
		log.debug("Receptionist config save request for business {} ", enterpriseId);
		receptionistService.saveOrUpdateReceptionistConfiguration(enterpriseId, request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * To be used by nexus to play greeting text on call
	 * @param businessId
	 * @return
	 */
	@RequestMapping(value = "/receptionist/greeting", method = RequestMethod.POST)
	public ResponseEntity<Map<?, ?>> getBusinessReceptionistGreeting(@RequestBody ReceptionistGreetingRequest request) throws Exception {
		Map<String, Object> greeting = receptionistService.getReceptionistGreeting(request.getBusinessPhoneNumber());
		return new ResponseEntity<>(greeting, HttpStatus.OK);
	}
	
	@RequestMapping(value = "/receptionist/cache", method = RequestMethod.GET)
	public ResponseEntity<Void> clearReceptionistCache() throws Exception {
		log.debug("Receptionist clear cache");
		receptionistService.clearReceptionistCache();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	
	@RequestMapping(value = "/missedcall/setting", method = RequestMethod.POST)
	public ResponseEntity<Void> updateMissedCallSetting(@RequestBody BusinessMissedCallSetting request) throws Exception {
		receptionistService.updateMissedCallSetting(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * To be used by nexus to replace tokens in text to speech call from dashboard
	 */
	@RequestMapping(value = "/receptionist/replace-token", method = RequestMethod.POST)
	public ResponseEntity<ReceptionistTextToSpeechTokenReplaceResponse> getBusinessReceptionistGreeting(@RequestBody ReceptionistTextToSpeechTokenReplaceRequest request) throws Exception {
		ReceptionistTextToSpeechTokenReplaceResponse response = receptionistService.replaceTokensForTextToSpeechMessage(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
}
