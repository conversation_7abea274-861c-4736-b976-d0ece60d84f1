package com.birdeye.messenger.controller;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;
import static com.birdeye.messenger.constant.Constants.USER_ID_HEADER;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.InitiateVideoConversationDTO;
import com.birdeye.messenger.dto.VideoConversationEvent;
import com.birdeye.messenger.service.VideoService;
import com.birdeye.messenger.sro.VideoConversationResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/messenger")
@Slf4j
public class VideoController {
	
	@Autowired
	private VideoService VideoService;
	
	/*
	 * Execution Path: Inbox -> Conversation -> Click on Video icon
	 * 
	 */
	@PostMapping(value = "/v1/video/conversation/{conversationId}/initiate")
	public ResponseEntity<VideoConversationResponse> initiate(@RequestHeader(USER_ID_HEADER) Integer userId, @RequestHeader(ACC_ID_HEADER) Integer accountId, @PathVariable("conversationId") Integer conversationId, @RequestParam(value = "source") Integer source) throws Exception {
		InitiateVideoConversationDTO ivr = VideoService.initiateVideo(conversationId, accountId, userId,source);
		VideoConversationResponse response = new VideoConversationResponse(ivr.getConversationId(), ivr.getSubaccountId(), ivr.getCustomerMeetingLink(), ivr.getBizUserMeetingLink(),ivr.getRoomId());
		return new ResponseEntity<VideoConversationResponse>(response,HttpStatus.OK);
	}
	
	/*
	 * EventDuringVideoConversation -> Twilio -> Nexus -> Messenger
	 */
	@PostMapping(value = "v1/video/conversation/event")
	public ResponseEntity<Void> videoConversationEvent(@RequestBody VideoConversationEvent event) {
		log.debug("[Video] Received video conversation event:{}", event);
		VideoService.handleVideoConversationEvent(event);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
