package com.birdeye.messenger.controller;

import com.birdeye.messenger.dto.robin.GetRobinReplyConfigResponse;
import com.birdeye.messenger.dto.robin.RobinAutoReplySetupRequest;
import com.birdeye.messenger.service.RobinAutoReplyConfigSetupService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/messenger")
public class RobinAutoReplyConfigController {

    private final RobinAutoReplyConfigSetupService robinAutoReplyConfigSetupService;

    /**
     * @body RobinAutoReplySetupRequest
     ** @return
     * @throws Exception
     * Api to enable/disable and update  robin and auto reply configuration at account and business level
     *
     */
    @PostMapping(value = "/configure-robin-autoreply", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> createUpdateRobinAutoReplyConfiguration(
            @RequestBody RobinAutoReplySetupRequest robinAutoReplySetupRequest) throws Exception {
        log.debug("Request received to set up robin/autoreply : {}", robinAutoReplySetupRequest);
         robinAutoReplyConfigSetupService.createUpdateRobinAutoReplyConfiguration(robinAutoReplySetupRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     *
     * @param robinAutoReplySetupRequest
     * @return
     * @throws Exception
     * Api to delete robin and auto reply at account and business level and account level
     */
    @PostMapping(value = "/delete-robin-autoreply", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> disableRobinAutoReplyConfiguration(
            @RequestBody RobinAutoReplySetupRequest robinAutoReplySetupRequest) throws Exception {
        log.debug("Request received to disable robin/autoreply : {}", robinAutoReplySetupRequest);
        robinAutoReplyConfigSetupService.deleteRobinAutoReplyConfiguration(robinAutoReplySetupRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * @param List of GetRobinReplyConfigResponse
     * @return
     * @throws Exception
     * This api is to view the robin and auto reply of a configuration at both enterprise and business level
     * this will return a list of reponse which contain business id and config for that location.
     */
    @PostMapping(value = "/get-all-robin-autoreply-config", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Map<Integer, GetRobinReplyConfigResponse>> getAllRobinAutoReplyConfig(
            @RequestBody RobinAutoReplySetupRequest request) throws Exception {
        log.debug("Request received to get all  robin/autreply config : {}", request);
        Map<Integer, GetRobinReplyConfigResponse> response = robinAutoReplyConfigSetupService.getAllRobinAutoReplyConfig(request);
        return new ResponseEntity<>(response,HttpStatus.OK);
    }

    /**
     *This api is to view the effective configuration for a business with channel
     */
    @PostMapping(value = "/get-effective-robin-autoreply-config", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<List<GetRobinReplyConfigResponse>> getEffectiveRobinAutoReplyConfig(
            @RequestBody RobinAutoReplySetupRequest request) throws Exception {
        log.debug("Request received to get effective robin/autreply config : {}", request);
        List<GetRobinReplyConfigResponse> response = robinAutoReplyConfigSetupService.getEffectiveRobinAutoReplyConfig(request);
        return new ResponseEntity<>(response,HttpStatus.OK);
    }

}
