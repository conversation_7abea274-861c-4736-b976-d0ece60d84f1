package com.birdeye.messenger.controller;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.report.ResponseTimeMigrationDto;
import com.birdeye.messenger.service.BulkUpdateService;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.sro.Conversation;
import com.birdeye.messenger.sro.CreateDeafaultCreationRequest;
import com.birdeye.messenger.sro.User;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/messenger")
public class MigrationController {

    private final MigrationService migrationService;
    
	@Autowired
    private final BulkUpdateService bulkUpdateService;

    @RequestMapping(value = "/migrate", method = RequestMethod.PUT)
    public ResponseEntity<?> migrateDataToES(@RequestBody MessengerFilter messengerFilter) {
        Integer contactsMigrated = migrationService.migrate(messengerFilter);
        return new ResponseEntity<>(contactsMigrated, HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/contact/{id}", method = RequestMethod.PUT)
    public ResponseEntity<Void> migrateDataToES(@PathVariable("id") Integer contactId,
                                                @RequestParam(value = "sms", required = false, defaultValue = "false") Boolean migrateSms) {
        migrationService.migrateByMessengerContactId(contactId, migrateSms);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/url/{id}", method = RequestMethod.GET)
    public ResponseEntity<Void> migrateDataToES(@PathVariable("id") Integer messageId) {
        migrationService.migrateImageUrl(messageId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/url/hardcoded", method = RequestMethod.GET)
    public ResponseEntity<Void> migrateDataToES() {
        migrationService.migrateImageUrl();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @RequestMapping(value = "/migrate/messenger/contact/{id}", method = RequestMethod.PUT)
    public ResponseEntity<Void> updateAndMigrateContactDataToES(@PathVariable("id") Integer messengerContactId) {
        migrationService.updateAndMigrateContactDataToES(messengerContactId);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @RequestMapping(value = "/migrate/messenger/contact", method = RequestMethod.GET)
    public ResponseEntity<Void> migrateContactDataToES() {
        migrationService.migrateContactDataToES();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @RequestMapping(value = "/migrate/messenger/contact", method = RequestMethod.POST)
    public ResponseEntity<Void> migrateContactDataToES(@RequestBody MessengerFilter messengerFilter) {
        migrationService.migrateContactDataToES(messengerFilter);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/webchat/location-widget", method = RequestMethod.POST)
    public ResponseEntity<Void> migrateLocationWidgets() {
        migrationService.migrateLocationWidgets();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @RequestMapping(value = "/bulkupdate/message", method = RequestMethod.POST)
    public ResponseEntity<Void> updateConversationMessages() {
    	bulkUpdateService.updateConversationMessages();
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @RequestMapping(value = "/bulkupdate/failed-message", method = RequestMethod.POST)
    public ResponseEntity<Void> updateFailedConversationMessages() {
    	bulkUpdateService.updateFailedConversationMessages();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/podium/data", method = RequestMethod.POST)
    public ResponseEntity<Void> migratePodiumData(@RequestBody @NotNull Conversation conversation) {
    	log.debug("Migrating podium data request: {} ",conversation);
    	migrationService.validateRequest(conversation);
        migrationService.migratePodiumData(conversation);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/podium/image", method = RequestMethod.POST)
    public ResponseEntity<Void> migratePodiumImage(@RequestBody @NotNull ImageUploadToS3Response imageUploadRequestToS3) {
        log.debug("migrating media files from podium : {} ", imageUploadRequestToS3);
        migrationService.migrateMedia(imageUploadRequestToS3);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @RequestMapping(value = "/publish/event/lastreceivedmessagesource", method = RequestMethod.POST)
    public ResponseEntity<?> publishEventForLastReceivedMessageSource(@RequestBody @NotNull @Valid MigrateLastReceivedMessageSourceRequest request) {
    	log.debug("Request to publish event to update last received messageSource in contact's");
        String scrollId=migrationService.publishEventForLastReceivedMessageSource(request);
        return new ResponseEntity<>(scrollId,HttpStatus.OK);
    }
    
    @RequestMapping(value = "/migrate/lastrecievedmessagesource", method = RequestMethod.POST)
    public ResponseEntity<Void> migrateLastRecievedMessageSource(@RequestBody MessengerFilter messengerFilter) {
    	log.info("Request to update last recieve messageSource in contact's for mcid's {}",messengerFilter.getMcIds());
        migrationService.migrateLastRecievedMessageSource(messengerFilter.getMcIds());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/publish/event/businessId", method = RequestMethod.POST)
    public ResponseEntity<?> publishEventToMigrateBusinessIdInOldMessageDocs(@RequestBody @NotNull @Valid MigrateBusinessIdInOldMessageDocumentRequest request) {
    	log.info("Publish event's to update businessId in old message docs");
        String scrollId=migrationService.publishEventToMigrateBusinessIdInOldMessageDocs(request);
        return new ResponseEntity<>(scrollId,HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/message/businessId", method = RequestMethod.POST)
    public ResponseEntity<Void> migrateBusinessIdInOldMessageDocs(@RequestBody MessengerFilter messengerFilter) {
    	log.info("Request to update businessId in old message docs for message document ids {}",messengerFilter.getMessageDocumentIds());
        migrationService.migrateBusinessIdInOldMessageDocs(messengerFilter);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/migrate/widget/config", method = RequestMethod.GET)
    public ResponseEntity<Void> migrateWidgetConfiguation(@RequestParam(value = "widgetId", required = false) Integer widgetId) {
    	log.info("Request to migrate widget configuation");
        migrationService.migrateWidgetConfiguation(widgetId);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping(value = "/migrate/lead-sources", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateLeadSources(@RequestBody User user) {
        migrationService.migrateLeadSources(user.getId());
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/migrate/lead-sources-producer")
    public ResponseEntity<Void> migrateLeadSources(@RequestParam("start") String start) throws ParseException {
        DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
        Date date = df.parse(start);
        migrationService.migrateLeadSourcesFromDate(date);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping(value = "/migrate/response-time-calc", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateResponseTime(@RequestBody MessengerContact contact) {
        migrationService.migrateResponseTime(contact);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping(value = "/migrate/response-time", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> initiateResponseTimeMigration(@RequestBody ResponseTimeMigrationDto request) {
        migrationService.initiateResponseTimeMigration(request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    
    @PutMapping(value = "/publish/event/duplicate-contacts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> publishEventToMergeDuplicateContacts(@RequestParam(value = "businessId", required = false) Integer businessId,@RequestParam(value = "customerId", required = false) Integer customerId,@RequestParam(value = "pageSize", defaultValue = "10000") Integer pageSize) {
        migrationService.publishEventToMergeDuplicateContacts(businessId,customerId,pageSize);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    @PostMapping(value = "/merge/duplicate-contacts", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> mergeDuplicateContacts(@RequestBody MergeDuplicateContactsEvent event) {
        migrationService.mergeDuplicateContacts(event);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    
    @PutMapping(value = "/publish/event/fb-gbm-id", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> publishEventToMigrateFbAndGBMId(@RequestParam(value = "businessId", required = false) Integer businessId,@RequestParam(value = "mcId", required = false) Integer mcId,@RequestParam(value = "pageSize", defaultValue = "9000") Integer pageSize) {
        migrationService.publishEventToMigrateFbAndGBMId(businessId,mcId,pageSize);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    @PostMapping(value = "/update/fb-gbm-id", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> UpdateFbAndGBMId(@RequestBody UpdateFBAndGBMIdEvents event) {
        migrationService.updateFbAndGBMId(event);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping(value = "/migrate/widgets", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateWidgets() {
        migrationService.migrateWidgets();
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping(value = "/migrate/multi-loc-widgets", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateMultiLocWidgets(@RequestBody BusinessChatWidgetConfig businessChatWidgetConfig) {
        migrationService.migrateWidgetToMultiLocWidget(businessChatWidgetConfig);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
    @PostMapping(value = "/migrate/create-default-widget", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> createDefaultWidget(@RequestBody List<CreateDeafaultCreationRequest> request) {
        migrationService.createDefaultWidget(request);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping(value = "/migrate/csv", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateCsvDataToES(@RequestBody ImportCsvMessage importChunk) throws Exception {
    	migrationService.migrateCsvDataToES(importChunk);
    	return new ResponseEntity<>(HttpStatus.OK);
    }
    
}
