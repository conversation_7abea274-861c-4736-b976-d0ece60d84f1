package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.MigrateHiddenCampaignDto;
import com.birdeye.messenger.service.CampaignDataArchiveService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/messenger")
public class CampaignDataArchiveController{
    
    
    @Autowired
    private CampaignDataArchiveService campaignDataArchiveService;

    /**
     * Api to start migration for archival of unused/hidden campaign data
     * This will create batches of account with contact documents sum smaller then equal to 1.6 million
     * and to push to kafka for further migration
     */
    @PostMapping(value = "/archive/campaign-messages")
    public ResponseEntity<Void> migrateCampaignHiddenDataPoints() {
        campaignDataArchiveService.migrateCampaignData();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This api will consume from /migrate/hidden-campaign path
     * This will find message documents and contactData for the each account of the batch
     * and further push it to kafka to reindex message documents and migrate db data
     */
    @PostMapping(value = "/archive/campaign-messages-per-account",consumes=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> processHiddenCampaignDataForMigration(@RequestBody MigrateHiddenCampaignDto request){
        campaignDataArchiveService.migrateHiddenCampaignDocsMessageDocumentsAndDbData(request.getAccountIds());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This api will consume from /migrate/process-hidden-campaign-batch path
     * This will divide contact/message data in batches of 1000
     * and push message documents for reindexing on kafka and move db data from primary tables to archived tables
     */
    @PostMapping(value = "/archive/campaign-messages-per-mcid",consumes=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> migrateHiddenCampaignMessagesEsDbDataForMigration(@RequestBody MigrateHiddenCampaignDto migrateHiddenCampaignDto){
        campaignDataArchiveService.archiveMessageData(migrateHiddenCampaignDto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This api will we used by cron job to move newly created data that should be archived
     */
    @PostMapping(value = "/archive/campaign-data-job",consumes=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> archiveCampaignDataJob(@RequestBody MigrateHiddenCampaignDto request){
        campaignDataArchiveService.archiveCampaignDataJob(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Delete conversations that were reIndexed in /archive/campaign-data-job
     * with xx Delay time assuming reIndexed is complete
     */
    @PostMapping(value = "/archive/delete-campaign-conversations-job",consumes=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> archiveCampaignDataJobDeleteConversation(@RequestBody MigrateHiddenCampaignDto request){
        campaignDataArchiveService.archiveCampaignDataJobDeleteConversation(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    

}
