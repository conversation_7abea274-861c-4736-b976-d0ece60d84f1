package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.CallReceiveDTO;
import com.birdeye.messenger.service.MessengerVoiceCallService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class VoiceCallController {

	@Autowired
	private MessengerVoiceCallService voiceCallService;

	/**
	 * We should have 3 cases 
	 * 1. Existing support where static autoreply needs to be sent. 
	 * 2. New flow where voicemail recording is received , voice message + auto reply needs to be sent
	 * 3. New flow where voicemail recording only contains instructions - Need to confirm
	 * 
	 * Single endpoint would be used to handle Received calls : Old + Receptionist flow
	 */
	@RequestMapping(value = "/call/inbound", method = RequestMethod.POST)
	public ResponseEntity<Void> handleCallBack(@RequestBody CallReceiveDTO request) {
		log.debug("Voice call request from Twilio with fromNumber {}, toNumber {} ", request.getFromNumber(),
				request.getToNumber());
		voiceCallService.handleCallBack(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

}
