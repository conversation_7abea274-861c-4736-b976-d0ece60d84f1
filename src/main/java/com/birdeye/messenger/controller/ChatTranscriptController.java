package com.birdeye.messenger.controller;

import com.birdeye.messenger.dto.ChatTranscriptRequest;
import com.birdeye.messenger.service.ChatTranscriptService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/messenger")
@RequiredArgsConstructor
public class ChatTranscriptController {
    private final ChatTranscriptService chatTranscriptService;

    /**
     * Api call to enable chat transcript for a business and user id's with for which chattranscript needed to be enabled
     * Request Body :
     * accountId : accountId for which transcript needed to be enabled.
     * transcriptFormat : format for transcript HTML, ADF
     *List<Integer> userId : list of userId's for chattranscript needed to be enabled with default
     */
    @PostMapping(value = "/enable-chat-transcript")
    ResponseEntity<String> enableChatTranscript(@RequestBody ChatTranscriptRequest chatTranscriptRequest){
        try {
            chatTranscriptService.enableChatTranscript(chatTranscriptRequest);
            return new ResponseEntity<>(HttpStatus.OK);
        }catch (Exception e){
            return new ResponseEntity<>(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * Api call to disable chat transcript for a business
     */
    @PostMapping(value = "/disable-chat-transcript")
    ResponseEntity<String> disableChatTranscript(@RequestBody ChatTranscriptRequest chatTranscriptRequest){
        try {
            chatTranscriptService.disableChatTranscript(chatTranscriptRequest);
            return new ResponseEntity<>(HttpStatus.OK);
        }catch (Exception e){
            return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
