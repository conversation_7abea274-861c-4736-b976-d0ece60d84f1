package com.birdeye.messenger.controller;

import com.birdeye.messenger.dto.MessengerContactResponse;
import com.birdeye.messenger.service.MessengerContactService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class MessengerContactController {

    private final MessengerContactService messengerContactService;

    /**
     *
     * @param customerId
     * @return MessengerContactResponse
     * Used By Platfrom to replace direct DB calls to Messenger Contact Table
     * Provides Messenger Contact Response For Given CustomerID
     */
    @RequestMapping(value = "/mc-by-cid/{customerId}", method = RequestMethod.GET)
    public ResponseEntity<MessengerContactResponse> getMessengerContactByCustomerId(@PathVariable("customerId") Integer customerId ){
        log.debug("Get Messenger Contact For CustomerId : {}",customerId);
        MessengerContactResponse messengerContactResponse = messengerContactService.findMessengerContactResponseByCustomerId(customerId);
        log.debug("Response For mc For CustomerId :{} is : {}",customerId,messengerContactResponse);
        return  new ResponseEntity<>(messengerContactResponse, HttpStatus.OK);
    }

    /**
     *
     * @param customerId
     * @return
     * Used By Platfrom to replace direct DB calls to Messenger Contact Table
     * Provides Messenger Contact Response For Given CustomerID
     * Primarily Used to Provide FaceBook Id's for Given CustomerId
     */
    @RequestMapping(value = "/facebookId-by-cid/{customerId}", method = RequestMethod.GET)
    public ResponseEntity<MessengerContactResponse> getFacebookIdsByCustomerId(@PathVariable("customerId") Integer customerId){
        log.debug("Get FacebookIds For CustomerId : {}",customerId);log.trace("Get FacebookIds For CustomerId : {}",customerId);
        MessengerContactResponse messengerContactResponse = new MessengerContactResponse();
        messengerContactResponse = messengerContactService.getFacebookIdsByCustomerId(customerId);
        log.debug("Response For FacebookIds For CustomerId :{} is : {}",customerId,messengerContactResponse);
        return new ResponseEntity<>(messengerContactResponse,HttpStatus.OK);
    }



}
