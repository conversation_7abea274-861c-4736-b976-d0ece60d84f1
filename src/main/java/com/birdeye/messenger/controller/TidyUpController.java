package com.birdeye.messenger.controller;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;
import static com.birdeye.messenger.constant.Constants.USER_ID_HEADER;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.service.TidyUpService;
import com.birdeye.messenger.sro.TidyUpConversationRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequestMapping("/messenger")
@Slf4j
@RestController
public class TidyUpController {

	@Autowired
	private TidyUpService tidyUpService;

	@PostMapping("/tidy-up")
	public ResponseEntity<Object> tidyUpConversations(@RequestBody TidyUpConversationRequest request,
			@RequestHeader(USER_ID_HEADER) Integer userId, @RequestHeader(ACC_ID_HEADER) Integer accountId) {
		log.debug("[Tidyup] Request received: {} userId: {} accountId: {}", request, userId, accountId);
		Object response = tidyUpService.tidyUpConversations(request, userId, accountId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}