
package com.birdeye.messenger.controller;


import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.enums.ReviewEventTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.UnlinkReviewResponse;
import com.birdeye.messenger.service.ReviewEventHandlerService;
import com.birdeye.messenger.sro.DeleteReviewEvent;
import com.birdeye.messenger.sro.ReviewAttributionRequest;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.ReviewEvent.Review;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class ReviewEventHandlerController {

    @Autowired
    ReviewEventHandlerService reviewEventHandlerService;
    //ADD/UPDATE/ACTION-UPDATE review
    @PostMapping(value = "/review/event")
    public void handleReviewEvent(@RequestBody @Valid ReviewEvent reviewEvent) throws Exception {
		Integer reviewId = Optional.ofNullable(reviewEvent).map(ReviewEvent::getReviewDetail).map(Review::getReviewId)
				.orElse(null);
        if(ReviewEventTypeEnum.MANUAL_LINK.equals(reviewEvent.getEventType())) {
            ReviewAttributionRequest reviewAttributionRequest = buildReviewAttributionRequest(reviewEvent);
            log.debug("Link review for reviewId:{} ",reviewId);
            reviewEventHandlerService.linkReview(reviewEvent.getUserId(), reviewEvent.getReviewDetail().getAccountId(), reviewAttributionRequest,false);
        }
        else if(ReviewEventTypeEnum.MANUAL_UNLINK.equals(reviewEvent.getEventType())) {
            log.debug("Unlink review for reviewId :{} ",reviewId);
            reviewEventHandlerService.unlinkReview(null,reviewEvent.getReviewDetail().getAccountId(), reviewEvent.getReviewDetail().getReviewId(), reviewEvent.getReviewDetail().getCustomerId());
        }
        else {
            log.debug("create/update/action-update review for reviewId :{} ",reviewId);
            reviewEventHandlerService.handleUpsertEvent(reviewEvent);
        }
    }
    
    @PostMapping(value = "/review/delete")
    public void handleReviewDeleteEvent(@RequestBody @Valid DeleteReviewEvent deleteReviewEvent) throws Exception {
        log.debug("Request received to delete review :{} ",deleteReviewEvent);
        reviewEventHandlerService.handleDeleteEvent(deleteReviewEvent);
    }

    @GetMapping(value = "/unlink-review/{reviewId}")
	public ResponseEntity<UnlinkReviewResponse> unlinkReview(@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
			@RequestHeader("user-id") Integer userId, @PathVariable("reviewId") Integer reviewId,@RequestParam("conversationId") Integer conversationId){
    	UnlinkReviewResponse unlinkReviewResponse=reviewEventHandlerService.unlinkReview(userId,accountId,reviewId,conversationId,false, true);
		return new ResponseEntity<>(unlinkReviewResponse,HttpStatus.OK);
	}

    @PostMapping(value = "/link-review")
    public ResponseEntity<Integer> linkReview(@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                              @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestBody ReviewAttributionRequest reviewAttributionRequest){
        Integer conversationId = reviewEventHandlerService.linkReview(userId,accountId,reviewAttributionRequest,true);
        return new ResponseEntity<>(conversationId,HttpStatus.OK);
    }

    private ReviewAttributionRequest buildReviewAttributionRequest(ReviewEvent reviewEvent) {
        ReviewEvent.Review reviewDetail = reviewEvent.getReviewDetail();
        Integer reviewId = reviewEvent.getReviewDetail().getReviewId();
        List<Integer> reviewIds = Collections.singletonList(reviewId);
        return ReviewAttributionRequest.builder()
                .accountId(reviewDetail.getAccountId())
                .businessId(reviewDetail.getBusinessId())
                .reviewerId(reviewDetail.getReviewerId())
                .customerId(reviewDetail.getCustomerId())
                .reviewIds(reviewIds)
                .build();
    }
}
