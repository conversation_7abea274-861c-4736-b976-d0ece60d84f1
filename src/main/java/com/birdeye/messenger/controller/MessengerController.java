package com.birdeye.messenger.controller;

import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.DailyReportRequest;
import com.birdeye.messenger.dto.GenericFirebaseMessage;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/v1/messenger")
@Slf4j
@RequiredArgsConstructor
public class MessengerController {

	private final MessengerContactService messengerContactService;
	
    private final MessengerService messengerService;

    
//    @RequestMapping(value = "/facebook", method = RequestMethod.PUT,
//            produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ScheduleAlertMessage> receiveFacebookMessage(@RequestBody FacebookMessageRequest message) throws Exception {
//    	ScheduleAlertMessage sAlert=messengerService.receiveMessage(message);
//    	return new ResponseEntity<>(sAlert, HttpStatus.OK);
//    }

	@RequestMapping(value = "/contact/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<MessengerContact> findById(@PathVariable("id") Integer id) {
		MessengerContact messengerContact = messengerContactService.findById(id);
		log.debug("Messenger Contact found with id {}", id);
		return new ResponseEntity<>(messengerContact, HttpStatus.OK);
	}

	@RequestMapping(value = "/cache/fbstatus", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> updateFbIntegratingStatusCache(@RequestBody GenericFirebaseMessage<?> facebookStatusRequest) throws Exception {
		messengerService.updateFbIntegratingStatusCache(facebookStatusRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
	
	@RequestMapping(value = "/dailyreport", method = RequestMethod.POST,
			consumes = MediaType.APPLICATION_JSON_VALUE,
			produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Map> messengerDailyReport(@RequestBody DailyReportRequest request) throws Exception {

		return new ResponseEntity<>(messengerService.messengerDailyReport(request), HttpStatus.OK);
	}
}
