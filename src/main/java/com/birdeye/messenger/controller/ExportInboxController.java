package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.ExportAllMessagesResponse;
import com.birdeye.messenger.dto.ExportInboxRequest;
import com.birdeye.messenger.dto.ExportInboxResponse;
import com.birdeye.messenger.service.ExportInboxService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/messenger")
public class ExportInboxController {
	
	@Autowired
	private ExportInboxService exportInboxService;
	
	@PostMapping(path = "/export", consumes = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE})
	ResponseEntity<ExportInboxResponse> fetchInboxMessages(@RequestBody ExportInboxRequest request){
		log.debug("Fetch messages for request: {}", request);
		return new ResponseEntity<>(exportInboxService.fetchInboxMessages(request), HttpStatus.OK);
	}
	
	@PostMapping(path = "/export/all", consumes = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE})
	ResponseEntity<ExportAllMessagesResponse> fetchAllInboxMessages(@RequestBody ExportInboxRequest request){
		log.debug("Fetch All Messages for request: {}", request);
		return new ResponseEntity<>(exportInboxService.fetchAllInboxMessages(request), HttpStatus.OK);
	}

	@PostMapping(path = "/export/nlp", consumes = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE}, produces = {MediaType.APPLICATION_JSON_VALUE,MediaType.APPLICATION_XML_VALUE})
	ResponseEntity<ExportAllMessagesResponse> fetchAllInboxMessagesNlp(@RequestBody ExportInboxRequest request){
		log.debug("Fetch All Messages for nlp request: {}", request);
		return new ResponseEntity<>(exportInboxService.fetchAllInboxMessagesNLP(request), HttpStatus.OK);
	}

}
