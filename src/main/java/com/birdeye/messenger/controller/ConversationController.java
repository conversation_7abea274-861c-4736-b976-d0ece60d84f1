package com.birdeye.messenger.controller;

import java.util.List;
import java.util.Objects;

import com.birdeye.messenger.dto.*;
import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.MessengerContactMessage;
import com.birdeye.messenger.service.ConversationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class ConversationController {

    private final ConversationService conversationService;

    @RequestMapping(value = "/v1/conversations", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getConversations(@Valid @RequestBody ConversationRequest request,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) {
        validate(request);
        ConversationResponse response = conversationService.getConversationV2(request, accountId, userId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @RequestMapping(value = "/v1/conversations/count", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getConversationsCount(@Valid @RequestBody ConversationRequest request,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
        log.debug("get conversationCount for user: {} and account: {}", userId, accountId);
        Object response = conversationService.getConversationsCount(request, accountId, userId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @RequestMapping(value = "/v2/conversations/count", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getConversationsCountV2(@Valid @RequestBody ConversationRequest request,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
        log.debug("get conversationCount for user: {} and account: {}", userId, accountId);
        Object response = conversationService.getConversationsCountV2(request, accountId, userId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @RequestMapping(value = "/dashboard/inbox/count", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getdashboardInboxCount(@Valid @RequestBody ConversationRequest request,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
        log.debug("get dashboard inbox count for user: {} and account: {}", userId, accountId);
        Object response = conversationService.getdashboardInboxCount(request, accountId, userId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @RequestMapping(value = "/v1/conversations/{id}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getConversations(@PathVariable("id") Integer id,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
            @Valid @RequestBody ConversationByIdRequest request,
            @RequestParam(value = "type", defaultValue = "mcId") String type) {
        log.debug("Get conversation by id for user: {} and account: {}", userId, accountId);
        Integer mcId = id;
        if ("cId".equals(type)) {
            MessengerContact mc = conversationService.getMcIdForCustomer(id, null, type);
            if (mc == null)
                return new ResponseEntity<>(null, HttpStatus.NO_CONTENT);
            mcId = mc.getId();
        }
        MessengerContactMessage response = conversationService.getConversationV2(mcId, accountId, userId, request);
        if (response == null)
            return new ResponseEntity<>(null, HttpStatus.NO_CONTENT);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private void validate(ConversationRequest request) {
        if (request.getFilters() != null && request.getFilters().getType() == ConversationView.REVIEW
                && (request.getAccess() == null || !request.getAccess().isReviews())) {
            throw new InputValidationException(
                    new ErrorMessageBuilder(ErrorCode.REVIEW_VIEW_NOT_PERMITTED, ComponentCodeEnum.REVIEW)
                            .message("Received a request for reviews view with a flag for not displaying reviews:{}",
                                    request));
        }
    }

    @PostMapping(value = "/conversation-status", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<ConversationStatus>> getReadUnreadStatus(@RequestBody List<Integer> cIds,
            @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) {
        List<ConversationStatus> readUnreadStatus = conversationService.getConversationStatus(userId, cIds, accountId);
        return new ResponseEntity<>(readUnreadStatus, HttpStatus.OK);
    }

    @GetMapping(value = "/mc-by-cId/{cId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessengerContact> getMcIdForCustomer(@PathVariable("cId") Integer cId,
            @RequestParam(value = "businessId") Integer businessId,
            @RequestParam(value = "type", defaultValue = "ecId") String type) {
        MessengerContact mcId = conversationService.getMcIdForCustomer(cId, businessId, type);
        return new ResponseEntity<>(mcId, HttpStatus.OK);
    }

    @PostMapping(value = "/freemium-conversation-count", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Long> getConversationCountForFreemiumAccount(
            @RequestBody ConversationCountFreemiumAccount conversationCountRequest) {
        Long count = conversationService.getConversationCountForFreemiumAccount(conversationCountRequest);
        return new ResponseEntity<>(count, HttpStatus.OK);
    }

    /**
     * This api is being used to generate closed event for tidy-up bulk close -
     * consumer -> Campaign
     * 
     * @param request
     * @param userId
     * @param accountId
     * @return
     */
    @RequestMapping(value = "/v1/tidyup-closed-automation", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> generateEventForClosedConversationTidyUp(@RequestBody TidyUpCloseEvent request) {
        conversationService.generateEventForClosedConversationTidyUp(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @RequestMapping(value = "/get-or-create-social-conversation", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getOrCreateSoicalConversation(
                                                   @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
                                                   @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
                                                   @RequestBody ConversationBySocialId request) {
        log.debug("Get conversation by SocialId for user: {} and account: {}", userId, accountId);
        MessengerContact mc = conversationService.getOrCreateMCForSocial(request);
        if (mc == null)
            return new ResponseEntity<>(null, HttpStatus.NO_CONTENT);
        Integer mcId = mc.getId();
        MessengerContactMessage response = conversationService.getConversationV2(mcId, accountId, userId, new ConversationByIdRequest(request.getAccess(),request.getBusinessIds()));
        if (response == null)
            return new ResponseEntity<>(null, HttpStatus.NO_CONTENT);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @RequestMapping(value = "/conversation/polling/count", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Object> getConversationsPollingCount(@Valid @RequestBody ConversationRequest request,
                                                          @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
                                                          @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
        log.debug("get conversationCount for user: {} and account: {}, request:{}", userId, accountId, request);
        if(Objects.nonNull(request.getFilters())) {
            request.getFilters().setConversationPollCount(true);
        }
        Object response = conversationService.getConversationsPollingCount(request, accountId, userId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
