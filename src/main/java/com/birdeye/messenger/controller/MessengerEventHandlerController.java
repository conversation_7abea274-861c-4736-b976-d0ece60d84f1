package com.birdeye.messenger.controller;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import jakarta.validation.Valid;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.twitter.TwitterMessageRequest;
import com.birdeye.messenger.service.InboxAuditService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.event.ReplyOnUAConversationEvent;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.MessagesDeleteRequest;
import com.birdeye.messenger.sro.UnassignConversationEvent;
import com.birdeye.messenger.sro.WebchatInstallationCrawlerResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class MessengerEventHandlerController {

    private final MessengerEventHandlerService messengerEventHandlerService;
    private final InboxAuditService inboxAuditService;
    
    @PostMapping(value = "/event")
    public ResponseEntity<MessengerContact> smsReceiveEvent(@RequestBody SMSMessageDTO smsMessageDTO) throws Exception {
		log.debug("smsReceiveEvent with body: {}, fromNumber: {}, toNumber: {}", smsMessageDTO.getBody(),
				smsMessageDTO.getFromNumber(), smsMessageDTO.getToNumber());
        smsMessageDTO.setEvent(MessengerEvent.SMS_RECEIVE);
        smsMessageDTO.setSource(1); // 1 for sms receive
        messengerEventHandlerService.handleEvent(smsMessageDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @RequestMapping(value = "/event/email", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> emailReceiveEvent(@RequestBody EmailMessageDTO emailMessageDTO) throws Exception {
    	String from = Optional.ofNullable(emailMessageDTO)
    	        .map(EmailMessageDTO::getEmailDTO)
    	        .map(EmailDTO::getFrom)
    	        .orElse(null);
        log.debug("emailReceiveEvent from: {}", from);
        return new ResponseEntity<>(messengerEventHandlerService.handleEvent(emailMessageDTO), HttpStatus.OK);
    }
    
    @RequestMapping(value = "/event/send", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> sendMessageEvent(@RequestBody SendMessageDTO sendMessageDTO,@RequestHeader(value = "request-source", required = false) String requestSource,
    		@RequestHeader(Constants.USER_ID_HEADER) Integer userId,
    		@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) throws Exception {
    	log.debug("sendMessageEvent: userId : {}, accountId : {}", userId, accountId);
    	sendMessageDTO.setAccountId(accountId);
    	if(StringUtils.isNotBlank(requestSource)){
    		sendMessageDTO.setRequestSource(requestSource);
    	}
    	return new ResponseEntity<>(messengerEventHandlerService.handleEvent(sendMessageDTO),HttpStatus.OK);
    }
    @RequestMapping(value = "/event/add/note", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> addNote(@RequestBody SendMessageDTO sendMessageDTO) throws Exception {
    	sendMessageDTO.setEvent(MessengerEvent.INTERNAL_NOTES);
        return new ResponseEntity<>(messengerEventHandlerService.handleEvent(sendMessageDTO),HttpStatus.OK);
    }
    
    @RequestMapping(value = "/event/facebook", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> receiveFacebookEvent(@RequestBody FacebookMessageRequest facebookMessageRequest) throws Exception {
        return new ResponseEntity<>(messengerEventHandlerService.handleEvent(facebookMessageRequest),HttpStatus.OK);
    }
    
    @RequestMapping(value = "/event/instagram", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> receiveInstagramEvent(@RequestBody InstagramMessageRequest instagramMessageRequest) throws Exception {
        return new ResponseEntity<>(messengerEventHandlerService.handleEvent(instagramMessageRequest),HttpStatus.OK);
    }
    
    @RequestMapping(value = "/event/twitter", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> receiveTwitterEvent(@RequestBody TwitterMessageRequest twitterMessageRequest) throws Exception {
        return new ResponseEntity<>(messengerEventHandlerService.handleEvent(twitterMessageRequest),HttpStatus.OK);
    }
    
    @PostMapping(value = "/event/update-tag")
    public void handleUpdateTag(@RequestBody ConversationTagChangeEvent event) {
        log.debug("Request received for the conversationTagChangeEvent for conversation id: {}",event.getConversationId());
    	messengerEventHandlerService.processConversationTagChangeEvent(event);
    }
    
    @PostMapping(value = "/event/reply-unassigned")
    public void handleReplyOnUnassignedConversation(@RequestBody ReplyOnUAConversationEvent event) {
    	messengerEventHandlerService.handleReplyOnUnassignedConversation(event);
    }

    @PostMapping(value = "/event/delete-team")
    public  ResponseEntity<Void> deleteTeam(@RequestBody DeleteTeamEvent request){
        messengerEventHandlerService.processDeleteTeamEvent(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/event/conversation/unassign")
    public  ResponseEntity<Void> unassignConversation(@RequestBody UnassignConversationEvent request){
        messengerEventHandlerService.unassignConversation(request);
        return new ResponseEntity<>(HttpStatus.OK);

    }
    @PostMapping(value = "/event/messenger-access-revoked")
    public  ResponseEntity<Void> messengerAccessRevoked(@RequestBody UserEvent userEvent){
        messengerEventHandlerService.messengerAccessRevoked(userEvent);
        return new ResponseEntity<>(HttpStatus.OK);

    }

    /*
    When business is upgraded downgraded or move. Need to delete the conversation on old routing ids.
     */
    @RequestMapping(value = "/event/delete/conversation", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteConversations(@RequestBody MessagesDeleteRequest messagesDeleteRequest) {
        log.debug("Request received to delete conversations for business id - {} ", messagesDeleteRequest.getBusinessId());
        messengerEventHandlerService.deleteConversations(messagesDeleteRequest);
    }

    @RequestMapping(value = "/event/delete/business-migrate", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteConversationsForBusinessMigration(@RequestBody MessagesDeleteRequest messagesDeleteRequest) {
        log.debug("Request received to delete conversations for business id- {} ", messagesDeleteRequest.getBusinessId());
        messengerEventHandlerService.deleteConversationsForBusinessMigration(messagesDeleteRequest);
    }

    @PostMapping(value = "/webchat/check/installation-status")
    public void checkWebchatInstallationStatus(@RequestBody Integer widgetId) {
        messengerEventHandlerService.checkWebchatInstallationStatus(widgetId);
    }

    @PostMapping(value = "/webchat/update/installation-status")
    public void updateWebchatInstallationStatus(@Valid @RequestBody WebchatInstallationCrawlerResponse response) {
        messengerEventHandlerService.updateWebchatInstallationStatus(response);
    }

    @PostMapping(value = "/kontacto/customer/sentiment/update")
    public void handleCustomerSentimentUpdate(@RequestBody @Valid  CustomerSentimentUpdateEvent customerSentimentUpdateEvent) throws  Exception {
        log.debug("Request received to update customer sentiment for customer id :{} ", customerSentimentUpdateEvent.getCids());
        messengerEventHandlerService.processCustomerSentimentUpdateEvent(customerSentimentUpdateEvent);
    }
    
	/**
	 * Wrapper to Send Pulse Survey Next Question & Handle Context Status
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "event/pulse-survey-question", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handlePulseSurveyQuestionEvent(@RequestBody PulseSurveyQuestionRequest request) throws Exception {
		log.debug("Request received to Send Message and Update PulseSurveyContext for customer id - {} ", request.getCustomerId());
		messengerEventHandlerService.handlePulseSurveyQuestionEvent(request);
	}
    
	/**
	 * Handle Event from Scheduler -> Check for Context Status -> Terminate -> Send
	 * Thank You Message if required
	 * @param message
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "event/pulse-survey-samay", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handlePulseSurveySamayEvent(@RequestBody SamayPayload payload) throws Exception {
		log.debug("Request received to handle scheduled event from Samay for contextId - {} ", payload.getContextId());
		messengerEventHandlerService.handlePulseSurveySamayEvent(payload);
	}


	@RequestMapping(value = "/event/audit", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    public void auditFailedEvents(@RequestBody String eventPayload, @RequestParam("eventType") String type) {
        inboxAuditService.auditEvent(eventPayload, type);
    }

	
	@RequestMapping(value = "/event/remove-instagram-mapping", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handleRemoveInstagramMappingEvent(@RequestBody List<UpdateInstagramMappingEvent> payload) throws Exception {
		messengerEventHandlerService.handleRemoveInstagramMappingEvent(payload);
	}
	@RequestMapping(value = "/event/add-instagram-mapping", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handleRemoveInstagramMappingEvent(@RequestBody UpdateInstagramMappingEvent payload) throws Exception {
		log.debug("Request received to remove instagaram status cache for add instagram mapping event: {} ", payload);
		messengerEventHandlerService.handleAddInstagramMappingEvent(payload);
	}
	
	@RequestMapping(value = "/event/update-invoice-number", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public void handleUpdateInvoiceNumberEvent(@Valid @RequestBody UpdateInvoiceNumberEvent payload, BindingResult bindingResult) throws Exception {
		log.debug("Request received to update invoice number for payment Id: {}", payload.getPaymentId());
		if(bindingResult.hasErrors()) {
			String message = "Validation failed in fields should not be null:".concat(bindingResult.getFieldErrors().stream().map(fe->fe.getField()).collect(Collectors.joining(",")));
			 log.error("Invoice Number updation failled : {}",message);
			 return;
		}
		messengerEventHandlerService.handleUpdateInvoiceNumberEvent(payload);
	}
	
	@PostMapping(value = "/event/filters/delete-team")
	@ResponseStatus(code = HttpStatus.OK)
	public void handleDeleteTeamEvent(@RequestBody DeleteTeamEvent payload) {
		messengerEventHandlerService.handleFiltersForDeleteTeamEvent(payload);
	}
	
	@PostMapping(value = "/event/filters/update-team-user")
	@ResponseStatus(code = HttpStatus.OK)
	public void handleUpdateTeamUserEvent(@RequestBody UpdateTeamUserEvent payload) {
		messengerEventHandlerService.handleFiltersForUpdateTeamUserEvent(payload);
	}
	
	@PostMapping(value = "/event/filters/user-event")
	@ResponseStatus(code = HttpStatus.OK)
	public void handleFiltersForUserEvent(@RequestBody UserEvent event) {
		messengerEventHandlerService.handleFiltersForUserEvent(event);
	}

    @PostMapping(value = "/event/update-media-url")
    @ResponseStatus(code = HttpStatus.OK)
    public void updateSocialChannelMediaUrl(@RequestBody UpdateBirdeyeCdnSocialAttachmentsDto event) {
        messengerEventHandlerService.updateSocialChannelMediaUrl(event);
    }

    @RequestMapping(value = "/event/whatsapp", method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessageResponse> receiveWhatsappEvent(@RequestBody WhatsappMessageRequest whatsappMessageRequest) throws Exception {
    	return new ResponseEntity<>(messengerEventHandlerService.handleEvent(whatsappMessageRequest),HttpStatus.OK);
    }
}
