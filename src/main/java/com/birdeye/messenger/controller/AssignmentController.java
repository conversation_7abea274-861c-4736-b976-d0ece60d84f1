
package com.birdeye.messenger.controller;

import com.birdeye.messenger.sro.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import static com.birdeye.messenger.constant.Constants.USER_ID_HEADER;

import jakarta.validation.Valid;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;

import com.birdeye.messenger.convertor.AssignmentRequestConvertor;
import com.birdeye.messenger.convertor.AssignmentRequestV2Convertor;
import com.birdeye.messenger.convertor.AssignmentResponseConvertor;
import com.birdeye.messenger.convertor.AssignmentResponseV2Convertor;
import com.birdeye.messenger.convertor.Convertor;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.service.AssignmentService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequestMapping(value = "/messenger")
public class AssignmentController {
	
	@Autowired
	private AssignmentService assignmentService;
	
	@PostMapping(value = "/v1/conversation/assign")
	public ResponseEntity<AssignmentResponse> assignConversation(@RequestBody AssignmentRequest request, @RequestHeader(USER_ID_HEADER) Integer userId, @RequestHeader(ACC_ID_HEADER) Integer accountId) {
		//TODO - Refactor below code to add userId and accountId in thread locals
		Convertor<AssignmentRequest, ConversationAssignmentRequestDTO> requestConvertor = new AssignmentRequestConvertor();
		
		ConversationAssignmentResponseDTO responseDTO = assignmentService.assignConversation(requestConvertor.convert(request), userId, accountId);
		
		Convertor<ConversationAssignmentResponseDTO, AssignmentResponse> responseConvertor = new AssignmentResponseConvertor();
		
		//FIXME - assignmentType is passed as blank for now
		return new ResponseEntity<AssignmentResponse>(responseConvertor.convert(responseDTO), HttpStatus.OK);
	}
	
	@PostMapping(value = "/v2/conversation/assign")
	public ResponseEntity<AssignmentResponseV2> assignConversationv2(@Valid @RequestBody AssignmentRequestV2 request, @RequestHeader(USER_ID_HEADER) Integer userId, @RequestHeader(ACC_ID_HEADER) Integer accountId) {
		
		Convertor<AssignmentRequestV2, ConversationAssignmentRequestDTO> requestConvertor = new AssignmentRequestV2Convertor();
		
		ConversationAssignmentResponseDTO responseDTO = assignmentService.assignConversation(requestConvertor.convert(request), userId, accountId);
		
		Convertor<ConversationAssignmentResponseDTO, AssignmentResponseV2> responseConvertor = new AssignmentResponseV2Convertor();
		
		AssignmentResponseV2 response = responseConvertor.convert(responseDTO);
		
		return new ResponseEntity<AssignmentResponseV2>(response, HttpStatus.OK);
	}






}
