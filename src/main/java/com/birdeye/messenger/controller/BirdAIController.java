/**
 * 
 */
package com.birdeye.messenger.controller;

import java.util.List;
import java.util.Objects;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationSummaryMessageBody;
import com.birdeye.messenger.dto.ConversationSummaryMessageBodyRequest;
import com.birdeye.messenger.dto.ConversationSummaryRequest;
import com.birdeye.messenger.dto.ConversationSummaryResponse;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.service.BirdAIService;
import com.birdeye.messenger.service.ConversationSummaryMessageBodyService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/messenger/summarise", produces = { MediaType.APPLICATION_JSON_VALUE })
@Slf4j
public class BirdAIController {

    private final BirdAIService birdAIService;

    private final ConversationSummaryMessageBodyService conversationSummaryMessageBodyService;

    @PostMapping(value = "/conversation/{id}", consumes = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<List<ConversationSummaryResponse>> getMessagesForConversationSummary(
            @RequestHeader(value = Constants.ACC_ID_HEADER) Integer accountId,
            @RequestHeader(value = Constants.USER_ID_HEADER) Integer userId, @PathVariable(value = "id") Integer mcId,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "50") Integer size,
            @RequestParam(value = "sortBy", defaultValue = "cr_time") String sortBy,
            @RequestParam(value = "sortOrder", defaultValue = "ASC") SortOrderEnum sortOrderEnum,
            @RequestBody @Valid ConversationSummaryRequest conversationSummaryRequest) {
        conversationSummaryRequest.setMcId(mcId);
        conversationSummaryRequest.setAccountId(accountId);
        conversationSummaryRequest.setUserId(userId);
        conversationSummaryRequest.setPage(page);
        conversationSummaryRequest.setSize(size);
        conversationSummaryRequest.setSortOrder(sortOrderEnum);
        conversationSummaryRequest.setOrderBy(sortBy);

        List<ConversationSummaryResponse> conversationSummaryResponses = birdAIService
                .getMessagesForConversationSummary(conversationSummaryRequest);

        return new ResponseEntity<>(conversationSummaryResponses, HttpStatus.OK);
    }

    @PostMapping(value = "/conversation/message-type/save-update-message-body", consumes = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Integer> saveOrUpdateConversationSummaryMessageBody(
            @RequestBody @Valid ConversationSummaryMessageBodyRequest conversationSummaryMessageBodyRequest) {
        ConversationSummaryMessageBody conversationSummaryMessageBody = conversationSummaryMessageBodyService
                .saveConversationSummaryMessageBody(conversationSummaryMessageBodyRequest);
        Integer id = Objects.nonNull(conversationSummaryMessageBody) ? conversationSummaryMessageBody.getId() : null;
        return new ResponseEntity<>(id, HttpStatus.OK);
    }

    @DeleteMapping(value = "/conversation/delete/message-body-by-activity-type", consumes = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> deleteConversationSummaryMessageBodyByActivityType(
            @RequestBody ActivityType activityType) {
        conversationSummaryMessageBodyService.deleteConversationSummaryMessageBody(activityType);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
