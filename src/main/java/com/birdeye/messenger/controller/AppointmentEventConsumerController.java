package com.birdeye.messenger.controller;
import com.birdeye.messenger.dto.AppointmentConfirmCreateContextRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.AppointmentEventDto;
import com.birdeye.messenger.dto.AppointmentSpecialistImageUpdateRequest;
import com.birdeye.messenger.dto.ContactAppointmentEventDTO;
import com.birdeye.messenger.service.AppointmentEventConsumerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
@Slf4j
public class AppointmentEventConsumerController {
    public final AppointmentEventConsumerService appointmentEventConsumerService;

    /**
     * Used in appointment flow, topic : appointment-request
     * @param customerEventDTO
     * @return
     */
    @RequestMapping(value = "/appointment", method = RequestMethod.POST, consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> appointmentEvent(@RequestBody ContactAppointmentEventDTO request) {
        appointmentEventConsumerService.consumeAppointmentEvent(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    /**
     * Used in appointment flow, topic : appointment_event
     * @param AppointmentEventDto
     * @return
     */
    @RequestMapping(value = "/v1/appointment", method = RequestMethod.POST, consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> appointmentEventV1(@RequestBody AppointmentEventDto request) {
        appointmentEventConsumerService.consumeAppointmentEventV1(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    /**
     * Update specialist imageUrl - event from appointment service
     * Topic : specialist_image_update
     * @param request
     * @return
     */
    @PostMapping(value = "/v1/appointment/update-image", consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> appointmentSpecialistImageUpdateEvent(@RequestBody AppointmentSpecialistImageUpdateRequest request) {
        appointmentEventConsumerService.appointmentSpecialistImageUpdate(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/appointment/create-context", consumes= MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> createAppointmentContext(@RequestBody AppointmentConfirmCreateContextRequest request) {
        appointmentEventConsumerService.createAppointmentConfirmationContext(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
