package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateUpdateWebhook;
import com.birdeye.messenger.dto.whatsapp.WhatsAppBusinessLocationStatusEvent;
import com.birdeye.messenger.dto.whatsapp.WhatsAppTemplateDto;
import com.birdeye.messenger.dto.whatsapp.WhatsAppTemplateFilterRequestDto;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/messenger/whatsapp")
public class WhatsAppTemplateController {
	
	@Autowired
	private IWhatsAppTemplateService whatsAppTemplateService;
	
	/**
	 * will receive event on this endpoint whenever any location status changed
	 * status - CONNECTED, DISCONNECTED, BANNED
	 * 
	 * @param locationStatusEvent
	 * @return
	 */
	@PostMapping("/account/onboard/event")
	public ResponseEntity<?> processWhatsAppBusinessLocationOnboardEvent(@RequestBody WhatsAppBusinessLocationStatusEvent locationStatusEvent) {
		log.debug("Received event to onboard new business location to WhatsApp: {}", locationStatusEvent);
		whatsAppTemplateService.processWhatsAppBusinessLocationOnboardEvent(locationStatusEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * get the list of templates for a given accountId
	 * 
	 * @param accountId
	 * @param request
	 * @return
	 */
	@PostMapping("/template/list/{accountId}")
	public ResponseEntity<?> whatsAppTemplateList(@PathVariable Integer accountId, @RequestBody WhatsAppTemplateFilterRequestDto request,
			@RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "25") Integer size, @RequestParam(defaultValue = "updatedOn") String sortBy, 
			@RequestParam(defaultValue = "desc") String order) {
		log.info("Received request to get whatsApp template list for an accountId: {} with request body: {}", accountId, request);
		return ResponseEntity.ok(whatsAppTemplateService.whatsAppTemplateList(accountId, request, page, size, sortBy, order));
	}
	
	/**
	 * get the template detail for a given templateId
	 * 
	 * @param templateId
	 * @return
	 */
	@GetMapping("/template/{templateId}")
	public ResponseEntity<?> getWhatsAppTemplateDetail(@PathVariable Integer templateId) {
		log.debug("Received request to get whatsApp template by templateId: {}", templateId);
		return ResponseEntity.ok(whatsAppTemplateService.getWhatsAppTemplateDetail(templateId));
	}
	
	/**
	 * will receive event on this endpoint whenever status of any
	 * template changed by WhatsApp :PENDIND -> APPROVED
	 * 
	 * @param createEvent
	 * @return
	 */
	@PostMapping("/template/status/update/event")
	public ResponseEntity<?> whatsAppTemplateStatusUpdate(@RequestBody ExternalWhatsAppTemplateUpdateWebhook createEvent) {
		log.debug("Whatsapp template status update event received: {}", createEvent);
		whatsAppTemplateService.processWhatsAppTemplateStatusUpdateEvent(createEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * will receive event on this endpoint whenever any template updated and
	 * approved by WhatsApp
	 * 
	 * @param updateEvent
	 * @return
	 */
	@PostMapping("/template/update/event")
	public ResponseEntity<?> updateWhatsAppMessageTemplate(@RequestBody ExternalWhatsAppTemplateUpdateWebhook updateEvent) {
		log.debug("Whatsapp template update event received: {}", updateEvent);
		whatsAppTemplateService.processWhatsAppTemplateMessageUpdateEvent(updateEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * save the template tokens for a given templateId
	 * 
	 * @param templateId
	 * @param templateDto
	 * @return
	 */
	@PostMapping("/template/save/tokens/{templateId}")
	public ResponseEntity<?> saveWhatsAppTemplateTokens(@PathVariable Integer templateId, @RequestBody WhatsAppTemplateDto templateDto,
			@RequestHeader("user-id") Integer userId) {
		log.info("Request received to save Whatsapp template tokens for templateId: {}, request: {} and userId: {}", templateId, templateDto, userId);
		whatsAppTemplateService.saveWhatsAppTemplateTokens(templateId, templateDto, userId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * template delete by id
	 * 
	 * @param templateId
	 * @return
	 */
	@DeleteMapping("/template/delete/{templateId}")
	public ResponseEntity<?> deleteWhatsAppTemplate(@PathVariable Integer templateId) {
		log.debug("Request received to delete Whatsapp template for templateId: {}", templateId);
		whatsAppTemplateService.deleteWhatsAppTemplate(templateId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/account/onboard/status")
	public ResponseEntity<?> whatsappAccountOnboardStatus(@RequestBody WhatsAppBusinessLocationStatusEvent request) {
		log.info("Request received whatsappAccountOnboardStatus: {}", request.getAccountId(), request.getBusinessId());
		return new ResponseEntity<>(whatsAppTemplateService.whatsappAccountOnboardStatus(request.getAccountId(), request.getBusinessId()), HttpStatus.OK);
	}
	
	/**
	 * delete WhatsApp onboarding status by id
	 * 
	 * @param id
	 * @return
	 */
	@DeleteMapping("/onboarding/delete/{id}")
	public ResponseEntity<?> deleteWhatsAppOnboardingStatus(@PathVariable Integer id) {
		log.info("Request received to delete WhatsApp onboarding status for id: {}", id);
		whatsAppTemplateService.deleteWhatsAppOnboardingStatus(id);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * will receive event on this endpoint whenever business verification status changed
	 * 
	 * @param businessStatusEvent
	 * @return
	 */
	@PostMapping("/business/status/update/event")
	public ResponseEntity<?> whatsAppBusinessVerificationUpdate(@RequestBody WhatsAppBusinessLocationStatusEvent businessVerificationEvent) {
		log.info("Received event to update business status: {}", businessVerificationEvent);
		whatsAppTemplateService.processWhatsAppBusinessVerificationUpdateEvent(businessVerificationEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * will receive event on this endpoint whenever any template category updated
	 * 
	 * @param categoryUpdateEvent
	 * @return
	 */
	@PostMapping("/template/category/update/event")
	public ResponseEntity<?> whatsAppTemplateCategoryUpdate(@RequestBody ExternalWhatsAppTemplateUpdateWebhook categoryUpdateEvent) {
		log.info("Whatsapp template category update event received: {}", categoryUpdateEvent);
		whatsAppTemplateService.processWhatsAppTemplateCategoryUpdateEvent(categoryUpdateEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * will receive event on this endpoint whenever any template quality updated
	 * 
	 * @param qualityUpdateEvent
	 * @return
	 */
	@PostMapping("/template/quality/update/event")
	public ResponseEntity<?> whatsAppTemplateQualityUpdate(@RequestBody ExternalWhatsAppTemplateUpdateWebhook qualityUpdateEvent) {
		log.info("Whatsapp template quality update event received: {}", qualityUpdateEvent);
		whatsAppTemplateService.processWhatsAppTemplateQualityUpdateEvent(qualityUpdateEvent);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * get the list of WABA names and languages for a given accountId
	 * @param accountId
	 * @return
	 */
	@GetMapping("wabaName/list/{accountId}")
	public ResponseEntity<?> getWabaNameList(@PathVariable Integer accountId) {
		log.info("Received request to get waba name list for accountId: {}", accountId);
		return ResponseEntity.ok(whatsAppTemplateService.getWabaNameListByAccountId(accountId));
	}

}
