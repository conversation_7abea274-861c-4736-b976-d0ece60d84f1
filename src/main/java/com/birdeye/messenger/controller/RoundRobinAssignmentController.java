package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dto.RoundRobinActivateRequest;
import com.birdeye.messenger.service.RoundRobinAssignmentService;

@RestController
@RequestMapping(value = "/messenger")
public class RoundRobinAssignmentController {
    @Autowired
    private RoundRobinAssignmentService roundRobinAssignmentService ;

    @PostMapping(value = "/roundrobin/activate")
    public ResponseEntity<Void> enableRoundRobin(@RequestBody RoundRobinActivateRequest roundRobinActivateRequest){
        TeamConfigForRoundRobin teamConfigForRoundRobin = roundRobinAssignmentService.getTeamConfigForRoundRobin(roundRobinActivateRequest);
        if(teamConfigForRoundRobin != null){
        	if (!teamConfigForRoundRobin.getTeamId().equals(roundRobinActivateRequest.getTeamId()))
        		roundRobinAssignmentService.updateRoundRobinConfig(roundRobinActivateRequest, teamConfigForRoundRobin);
        } else {
            roundRobinAssignmentService.createRoundRobinConfig(roundRobinActivateRequest);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value ="/roundrobin/deactivate/{accountId}")
    public ResponseEntity<Void> disableRoundRobin(@PathVariable("accountId")Integer accountId){
        roundRobinAssignmentService.disableRoundRobin(accountId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
