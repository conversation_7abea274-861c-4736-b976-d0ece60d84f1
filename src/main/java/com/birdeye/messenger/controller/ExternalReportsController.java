package com.birdeye.messenger.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.MessengerVoiceCallService;
import com.birdeye.messenger.service.WebchatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.birdeye.messenger.service.ExternalReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class ExternalReportsController {

	public final ExternalReportService externalReportService;
	@Autowired
	private MessengerVoiceCallService voiceCallService;

	@Autowired
	private WebchatService webchatService;

	/**
	 * Used for SalesForce Data
	 * @param filters
	 * @return
	 * @throws Exception
	 */
	@PostMapping("/inbox-send-report")
	public ResponseEntity<Void> getInboxSendReportByLocations(@RequestBody MessengerFilter filters) throws Exception {
		log.debug("Get Inbox Send Report by Locations {}", filters.getBusinessIds());
		externalReportService.getInboxSendReportByLocations(filters);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/send-by-channel-report")
	public ResponseEntity<Void> getSendByChannelReport(@RequestBody MessengerFilter filters) throws Exception {
		log.debug("Get Send by channel report for {}", filters.getBusinessIds()); //log.infe("Get Send by channel report for {}", filters.getAccountId());
		externalReportService.getSendByChannelReport(filters);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/send-autoreply-report")
	public ResponseEntity<Void> getSendAutoreplyReport(@RequestBody MessengerFilter filters) throws Exception {
		log.debug("Get Send Autoreply Report for {}", filters.getBusinessIds()); //log.infd("Get Send Autoreply Report for {}", filters.getAccountId());
		externalReportService.getSendAutoreplyReport(filters);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping("/get-all-messages")
	public ResponseEntity<BizAppGetAllMessagesResponse> getAllMessages(@RequestBody ExportInboxRequest filters) throws Exception {
		BizAppGetAllMessagesResponse messageDocuments = externalReportService.getAllMessages(filters);
		return new ResponseEntity<>(messageDocuments, HttpStatus.OK);
	}
	
	@PostMapping("/get-all-contacts")
	public ResponseEntity<BizAppGetAllContactsResponse> getAllContacts(@RequestBody ExportInboxRequest filters) throws Exception {
		BizAppGetAllContactsResponse contactDocuments = externalReportService.getAllContacts(filters);
		return new ResponseEntity<>(contactDocuments, HttpStatus.OK);
	}
	
	@PostMapping("/matrix/roi")
	public ResponseEntity<MatrixRoiReport> getMatrixRoiReport(@RequestBody MessengerFilter filters) throws Exception {
		log.debug("Get Matrix ROI report for {}", filters.getBusinessIds());
		MatrixRoiReport responseObject = externalReportService.getMatrixRoiReport(filters);
		return new ResponseEntity<>(responseObject, HttpStatus.OK);
	}
	
	@PostMapping("/matrix/roi/inbox")
	public ResponseEntity<Map> getRoiReportForInbox(@RequestBody ExternalReportFilter filters) throws Exception {
		log.debug("Get Matrix ROI report for Inbox, Request: {}", filters);
		Map<String, Object> responseObject = externalReportService.getRoiReportForInbox(filters);
		return new ResponseEntity<>(responseObject, HttpStatus.OK);
	}
	
	@PostMapping("/matrix/roi/webchat")
	public ResponseEntity<Map> getRoiReportForWebchat(@RequestBody ExternalReportFilter filters) throws Exception {
		log.debug("Get Matrix ROI report for Webchat, Request: {}", filters);
		Map<String, Object> responseObject = externalReportService.getRoiReportForWebchat(filters);
		return new ResponseEntity<>(responseObject, HttpStatus.OK);
	}

	/**
	 *
	 * @param request
	 * @return BizAppCountVoiceCallResponse
	 * Used By BizApp to Get VoiceCall count For BusinessId's
	 */

	@RequestMapping(value = "bizapp/call/count", method = RequestMethod.POST)
	public ResponseEntity<BizAppCountVoiceCallResponse> countVoiceCall(@RequestBody BizAppVoiceCallRequest request){
		log.debug("Biz apps request for messengertable {}", request);
		Integer response = voiceCallService.countVoiceCalls(request);
		BizAppCountVoiceCallResponse bizAppCountVoiceCallResponse = new BizAppCountVoiceCallResponse();
		bizAppCountVoiceCallResponse.setCountVoiceCall(response);
		log.debug("Response for BizApp VoiceCall Count : {}",response);
		return new ResponseEntity<>(bizAppCountVoiceCallResponse, HttpStatus.OK);
	}

	/**
	 *
	 * @param request
	 * @return BizAppCountWebChatConfigResponse
	 * Used By BizApp to Get BusinessChatWidgetConfig Count For BizAppChatWidgetConfigRequest
	 */
	@RequestMapping(value = "bizapp/webchat-config", method = RequestMethod.POST)
	public ResponseEntity<BizAppCountWebChatConfigResponse> getBusinessChatWidgetConfig(@RequestBody BizAppChatWidgetConfigRequest request){
		log.debug("Webchat Widget Config Request For BizAppChatWidgetConfigRequest : {}",request);
		BizAppCountWebChatConfigResponse bizAppCountWebChatConfigResponse = new BizAppCountWebChatConfigResponse();
		List<BusinessChatWidgetConfig> businessChatWidgetConfigs = new ArrayList<>();
		businessChatWidgetConfigs = webchatService.getBusinessChatWidgetConfigForBizApp(request);
		bizAppCountWebChatConfigResponse.setCountWebChatConfig(businessChatWidgetConfigs.size());
		log.debug("Response for Webchat Widget Config : {}",businessChatWidgetConfigs.size());
		return new ResponseEntity<>(bizAppCountWebChatConfigResponse,HttpStatus.OK);
	}

	@RequestMapping(value = "/sms-communication", method = RequestMethod.POST)
	public ResponseEntity<SmsCommunicationResponse> getSmsMessagesInGivenTimeFrame(@RequestBody SmsCommunicationRequest request){
		log.debug("Request received to get active sms-communication {}",request);
		SmsCommunicationResponse smsCommunicationResponse =  externalReportService.getSmsMessagesCount(request);
		log.debug("Response for active sms-communication : {}",smsCommunicationResponse);
		return new ResponseEntity<>(smsCommunicationResponse,HttpStatus.OK);
	}

	@RequestMapping(value = "/report/reservewithgoogle", method = RequestMethod.POST)
	public ResponseEntity<Object> getReserveWGoogleReport(@RequestBody ExternalReportFilter filter){
		log.debug("Request received to get ReserveWGoogle Report {}",filter);
		Object report =  externalReportService.getReserveWGoogleReport(filter);
		log.debug("Response for ReserveWGoogle Report : {}",report);
		return new ResponseEntity<>(report,HttpStatus.OK);
	}
}
