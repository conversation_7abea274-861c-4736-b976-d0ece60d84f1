package com.birdeye.messenger.controller;

import java.text.ParseException;
import java.util.Map;

import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.TeamAssigneeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.birdeye.messenger.service.CommonService;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/activity")
@Slf4j
public class TestController {

    private final CommonService commonService;


    @PostMapping
    public ResponseEntity<Object> getMapOfActivities(@RequestBody Map<String, String> map) {
        return new ResponseEntity<>(commonService.getActivityObject(map), HttpStatus.OK);
    }

    @RequestMapping(value = "/encoding", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<CustomerDTO> checkEncodingOfDto(@RequestBody CustomerDTO map) {
        log.info("Testing log encoding: {}", map);
        return new ResponseEntity<>(map, HttpStatus.OK);

    }
}
