package com.birdeye.messenger.controller;

import java.util.List;

import jakarta.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.service.NotificationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
@Slf4j
public class NotificationController {

    private final NotificationService notificationService;


    @RequestMapping(value = "/v1/dashboard-notifications", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<MessengerNotificationMessage> getConversations(@Valid 
            @RequestBody NotificationRequest notificationRequest, @RequestHeader(Constants.USER_ID_HEADER) Integer userId,
            @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,@RequestHeader(value = "request-source", required = false) String requestSource) throws Exception {
        log.debug("getDashboardNotification request: {} {}",userId, accountId);
        notificationRequest.setRequestSource(requestSource);
        MessengerNotificationMessage message = notificationService.getConversations(notificationRequest, userId,
                accountId);
        return new ResponseEntity<>(message, HttpStatus.OK);
    }

    
    @RequestMapping(value = "/v1/sync-conversations", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<Integer>> getRemovedConversations(@Valid @RequestBody NotificationRequest request, @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) {
    	List<Integer> removedConversations = notificationService.getRemovedConversations(request, userId, accountId);
        return new ResponseEntity<>(removedConversations, HttpStatus.OK);
    }

}
