package com.birdeye.messenger.controller.googleBusinessMessaging;

import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.service.impl.GBMReceiveHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@RestController
@RequestMapping("/messenger/google")
@Slf4j
@RequiredArgsConstructor
public class GoogleEventsController {

    private final GBMReceiveHandler receiveHandler;

    @PostMapping(value = "/message", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> receiveGoogleMessage(@RequestBody GoogleUserMessage googleUserMessage) throws Exception {

        if(Objects.nonNull(googleUserMessage.getMessage())) {
            receiveHandler.handle(googleUserMessage);
        }
        else if(Objects.nonNull(googleUserMessage.getReceipts())) {
            // handle read receipts
        }
        else if(Objects.nonNull(googleUserMessage.getUserStatus())) {
            // handle typing events
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }


}
