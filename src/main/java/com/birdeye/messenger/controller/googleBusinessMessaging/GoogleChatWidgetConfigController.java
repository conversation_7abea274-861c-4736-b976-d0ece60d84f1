package com.birdeye.messenger.controller.googleBusinessMessaging;

import com.birdeye.messenger.dao.entity.googleBusinessMessaging.GoogleChatWidgetConfig;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleChatWidgetConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/messenger/google/chat-widget")
@Slf4j
@RequiredArgsConstructor
public class GoogleChatWidgetConfigController {

    private final GoogleChatWidgetConfigService googleChatWidgetConfigService;

    @GetMapping(value = "/config/{accountId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<GoogleChatWidgetConfig> getGoogleChatWidgetConfig(@PathVariable("accountId") Integer accountId) {
        GoogleChatWidgetConfig config = googleChatWidgetConfigService.findByAccountId(accountId);
        return new ResponseEntity<>(config, HttpStatus.OK);

    }

    @PostMapping(value = "/config", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> saveGoogleChatWidgetConfig(@RequestBody GoogleChatWidgetConfig config) {
        googleChatWidgetConfigService.saveOrUpdateGoogleChatWidgetConfig(config);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }
}
