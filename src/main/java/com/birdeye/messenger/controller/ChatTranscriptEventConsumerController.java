package com.birdeye.messenger.controller;

import com.birdeye.messenger.dao.entity.ChatTranscriptAccountConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ChatTranscriptEvent;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.ChatTranscriptConsumerService;
import com.birdeye.messenger.service.ChatTranscriptRepositoryService;
import com.birdeye.messenger.service.MessengerContactService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@RequestMapping("/messenger")
@RestController
@Slf4j
@RequiredArgsConstructor
public class ChatTranscriptEventConsumerController {

    private final List<ChatTranscriptConsumerService> chatTranscriptGenerators;

    private final ContactService contactService;

    private final MessengerContactService messengerContactService;

    private final ChatTranscriptRepositoryService chatTranscriptRepositoryService;

    @PostMapping(value = "/generate-chat-transcript", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> generateAndSendChatTranscript(@RequestBody ChatTranscriptEvent event) {

        //-------------- validate -----------------------
        Assert.notNull(event.getAccountId(), "Account Id must be present");
        Assert.notNull(event.getConversationId(), "Conversation Id must be present");

        List<ChatTranscriptAccountConfig> chatTranscriptConfigs = chatTranscriptRepositoryService.getChatTranscriptAccountConfig(event.getAccountId());
        Map<Integer, String> chatTranscriptConfig = chatTranscriptConfigs.stream().collect(Collectors.toMap(ChatTranscriptAccountConfig::getAccountId, ChatTranscriptAccountConfig::getTranscriptFormat));
        log.info("chatTranscript: map {}", chatTranscriptConfig);
        if(!chatTranscriptConfig.containsKey(event.getAccountId())) {
            log.info("chatTranscript config not found for account {}", event.getAccountId());
            return null;
        }

        Optional<ChatTranscriptConsumerService> chatTranscriptGenOptional = chatTranscriptGenerators.stream().filter(generator -> generator.getFormat().equals(chatTranscriptConfig.get(event.getAccountId()))).findAny();
        if(chatTranscriptGenOptional.isPresent()) {
            ChatTranscriptConsumerService chatTranscriptConsumerService = chatTranscriptGenOptional.get();
            // custom handling for Tomwoodlund - BE-94748
            if(chatTranscriptConsumerService.getFormat().equals("ADF")) {
                Integer mcId = event.getConversationId();
                MessengerContact mc = messengerContactService.findById(mcId);
                if(Objects.isNull(mc)) {
                    log.info("generateAndSendChatTranscript: no mc found for input {}", event);
                    return null;
                }
                CustomerDTO customer = contactService.findById(mc.getCustomerId());
                if(Objects.isNull(customer) || !customer.isLead()) {
                    log.info("generateAndSendChatTranscript: no customer found or customer not lead {}", event);
                    return null;
                }
            }
            chatTranscriptConsumerService.generateAndSendChatTranscript(event.getAccountId(), event.getConversationId());
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
