
package com.birdeye.messenger.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.UpdateTeamRequest;
import com.birdeye.messenger.service.TeamService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> iqubal
 *
 */
@Slf4j
@RestController
@RequestMapping(value = "/messenger")
public class TeamController {
	@Autowired
	private TeamService teamService;

	@RequestMapping(value = "/update/team", method = RequestMethod.POST)
	public ResponseEntity<Void> updateTeam(@RequestBody UpdateTeamRequest request) {
		log.debug("Update team request {} ", request);
		teamService.updateTeam(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}