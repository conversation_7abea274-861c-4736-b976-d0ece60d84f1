package com.birdeye.messenger.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dao.repository.CustomChannelRepository;
import com.birdeye.messenger.dto.CustomChannelsResponse;
import com.birdeye.messenger.dto.TemplateCallbackRequest;
import com.birdeye.messenger.service.CustomChannelTemplateCallbackService;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
public class CustomChannelController {
    
    private final CustomChannelRepository customChannelRepository;
    private final CustomChannelTemplateCallbackService callbackService;

    @GetMapping("/custom-channels")
    public ResponseEntity<CustomChannelsResponse> getCustomChannels(@RequestHeader("account-id") Integer accountId) {
        List<String> channelList = new ArrayList<>();
        customChannelRepository.findByAccountId(accountId).ifPresent(customChannel -> {
            String channels = customChannel.getChannels();
            if (StringUtils.isNotBlank(channels)) {
                channelList.addAll(ControllerUtil.getTokensListFromString(channels));
            }
        });

        CustomChannelsResponse response = new CustomChannelsResponse();
        response.setCustomChannels(channelList);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    
	/**
	 * 
	 * Consumes from campaign-template-fetch-content topic
	 * Builds the outbound request from the callback and pushes to subscription topic
	 */
	@PostMapping("/custom-channel/template/callback")
	public ResponseEntity<?> handleTemplateCallback(@RequestBody TemplateCallbackRequest request) {
		callbackService.handleTemplateCallback(request);
		return ResponseEntity.ok().build();
	}
}