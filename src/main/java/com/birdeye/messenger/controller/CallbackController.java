package com.birdeye.messenger.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.EmailSubscribeStatus;
import com.birdeye.messenger.dto.EmailUnsubscribeStatus;
import com.birdeye.messenger.dto.SmsMessageStatus;
import com.birdeye.messenger.service.CallbackService;

import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/messenger")
@RequiredArgsConstructor
public class CallbackController {

	private final CallbackService callbackService;
	
	/**
	 * Update Messenger Delivery Status Event
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/sms/status", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> messengerSMSDeliveryStatusUpdate(@RequestBody SmsMessageStatus request) throws Exception{
		callbackService.messengerSMSDeliveryStatusUpdate(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@RequestMapping(value = "/unsubscribe/email/event", method = RequestMethod.POST)
	public ResponseEntity<?> customerUnsubscribe(@RequestBody EmailUnsubscribeStatus emailUnsubscribeStatus)
			throws Exception {
		callbackService.updateEmailStatusAfterUnsubscribe(emailUnsubscribeStatus);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * BIRD-58078 | group_resubscribe from kafka topic : customer-resubscribe-tracking
	 * @param emailSubscribeStatus
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/subscribe/email/event", method = RequestMethod.POST)
	public ResponseEntity<?> customerSubscribe(@RequestBody EmailSubscribeStatus emailSubscribeStatus)
			throws Exception {
		callbackService.updateEmailSubscribeStatus(emailSubscribeStatus);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
