package com.birdeye.messenger.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.BusinessMigrationBatchDto;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.sro.BusinessMoveEvent;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class BusinessController {

    private final MigrationService migrationService;


    /**
     *
     * @param @Body : BusinessMoveEvent
     * @return
     * Use : This is used to migrate the messenger data when business is upgraded/downgrade/move .
     * Working : This api Verify the business move request and process it to get messenger contacts in batches of five
     * and the publish them to kakfa queue , which later consumes by api /business/upgrade-downgrade-move/event
     * for further Processing .
     */
    @PostMapping(value = "/business/move")
    public ResponseEntity<Void> moveInboxData(@RequestBody BusinessMoveEvent event) {
        migrationService.moveInboxData(event);
        return new ResponseEntity<>(HttpStatus.OK);

    }

    /**
     *
     * @param @Body BusinessMigrationBatchDto
     * @return HttpStatus.OK
     * use : This api is used to further process the async sub-events of MessengerContacts received from kafka,
     *  from the flow /business/move , and updates the changes in ES and DB for MessengerContact and ContactDocument
     *  according to event.
     */
    @PostMapping(value = "/business/move/batch-migration")
    public ResponseEntity<Void> businessUpgradeDowngradeMoveEvent(@RequestBody BusinessMigrationBatchDto event){
        migrationService.businessUpgradeDowngradeMoveEvent(event);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
