package com.birdeye.messenger.controller;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.convertor.AssignmentResponseConvertor;
import com.birdeye.messenger.convertor.Convertor;
import com.birdeye.messenger.dto.AddNoteRequest;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.PublicAddAndSendContact;
import com.birdeye.messenger.dto.PublicUpdateStatusConversationRequest;
import com.birdeye.messenger.service.PublicAPIService;
import com.birdeye.messenger.sro.AssignmentResponse;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@RestController
@RequestMapping("/messenger")
public class PublicApiController {
	
	private final PublicAPIService publicAPIService;

	@RequestMapping(value = "/public/send", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<MessageResponse> addNewContactAndSendSms(@RequestBody PublicAddAndSendContact request, @RequestHeader(ACC_ID_HEADER) Integer accountId) throws Exception {
		MessageResponse messageResponse = publicAPIService.addAndSendMessage(accountId, request);
		return new ResponseEntity<>(messageResponse, HttpStatus.OK);
	}
	@RequestMapping(value = "/public/add-note", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<MessageResponse> addNote(@RequestBody AddNoteRequest request) throws Exception {
		MessageResponse messageResponse = publicAPIService.addNote(request);
		return new ResponseEntity<>(messageResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "/public/conversation/assign", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<AssignmentResponse> conversationAssign(@RequestBody ConversationAssignmentRequestDTO request,@RequestHeader(ACC_ID_HEADER) Integer accountId) throws Exception {
		ConversationAssignmentResponseDTO response = publicAPIService.assignConversation(request,accountId);
		Convertor<ConversationAssignmentResponseDTO,AssignmentResponse> responseConvertor = new AssignmentResponseConvertor();
		return new ResponseEntity<AssignmentResponse>(responseConvertor.convert(response), HttpStatus.OK);
	}
	
	@RequestMapping(value = "/public/update-status", method = RequestMethod.POST, consumes=MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE )
	public ResponseEntity<MessageResponse> updateConversationStatus(@RequestBody PublicUpdateStatusConversationRequest request, @RequestHeader(ACC_ID_HEADER) Integer accountId) throws Exception {
		publicAPIService.updateConversationStatus(accountId, request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * Public In-bound chat Mirror API
	 */
	@PostMapping(value = "/public/receive-message", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<MessageResponse> receiveCustomChannelMessage(
			@RequestBody CustomChannelReceiveRequest request,
			@RequestHeader(value = "x-business-number", required = false) Long businessNumber) throws Exception {
		MessageResponse response = publicAPIService.receiveCustomChannelMessage(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
}
