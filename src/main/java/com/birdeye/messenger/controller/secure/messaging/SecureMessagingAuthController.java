/**
 * 
 */
package com.birdeye.messenger.controller.secure.messaging;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkGenerationRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkInfoResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingRequestOTP;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPResponse;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingAuthService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/messenger/auth/secure")
public class SecureMessagingAuthController {

    private final SecureMessagingAuthService secureMessagingService;

    /*
     * API responsible for generating secure messaging link for a conversation
     */
    @PostMapping(value = "/generate-link", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<MessageResponse> generateSecureMessagingLink(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,
            @RequestBody SecureLinkGenerationRequest secureLinkGenerationRequest) throws Exception {
        log.info("generateSecureMessagingLink request received with request : {},accountId : {},userId : {}",
                secureLinkGenerationRequest, accountId, userId);
        secureLinkGenerationRequest.setAccountId(accountId);
        secureLinkGenerationRequest.setUserId(userId);
        MessageResponse messageResponse = secureMessagingService
                .generateSecureMessagingLink(secureLinkGenerationRequest);
        return new ResponseEntity<>(messageResponse, HttpStatus.OK);
    }

    /*
     * API to get business and customer info from slug
     */

    @GetMapping(value = "/get/{slug}", produces = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<SecureLinkInfoResponse> getSecureLinkInfoResponse(@PathVariable(value = "slug") String slug)
            throws Exception {
        log.info("getSecureLinkInfoResponse called with slug : {}", slug);
        SecureLinkInfoResponse secureLinkInfoResponse = secureMessagingService.getSecureLinkInfoResponse(slug);
        return new ResponseEntity<>(secureLinkInfoResponse, HttpStatus.OK);
    }

    /*
     * API to generate otp for the requested customer
     */
    @PostMapping(value = "/request-otp", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> requestOTP(
            @RequestHeader(value = RequestLoggingFilter.HEADER_SECURE_KEY) String secureKey,
            @RequestBody SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception {
        log.info("requestOTP request received with request : {},secureKey : {}",
                secureMessagingRequestOTP, secureKey);
        secureMessagingRequestOTP.setSecureKey(secureKey);
        secureMessagingService.requestOTP(secureMessagingRequestOTP);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /*
     * API to resend otp for the requested customer
     */
    @PostMapping(value = "/resend-otp", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> resendOTP(
            @RequestHeader(value = RequestLoggingFilter.HEADER_SECURE_KEY) String secureKey,
            @RequestBody SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception {
        log.info("resendOTP request received with request : {},secureKey : {}",
                secureMessagingRequestOTP, secureKey);
        secureMessagingRequestOTP.setSecureKey(secureKey);
        secureMessagingService.resendOTP(secureMessagingRequestOTP);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /*
     * API to verify otp for the requested customer
     */
    @PostMapping(value = "/verify-otp", consumes = { MediaType.APPLICATION_JSON_VALUE }, produces = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<SecureMessagingVerifyOTPResponse> verifyOTP(
            @RequestHeader(value = RequestLoggingFilter.HEADER_SECURE_KEY) String secureKey,
            @RequestBody SecureMessagingVerifyOTPRequest secureMessagingVerifyOTPRequest) throws Exception {
        log.info("verifyOTP request received with request : {},secureKey : {}",
                secureMessagingVerifyOTPRequest, secureKey);
        secureMessagingVerifyOTPRequest.setSecureKey(secureKey);
        SecureMessagingVerifyOTPResponse secureMessagingVerifyOTPResponse = secureMessagingService
                .verifyOTP(secureMessagingVerifyOTPRequest);
        return new ResponseEntity<>(secureMessagingVerifyOTPResponse, HttpStatus.OK);
    }

}
