/**
 * 
 */
package com.birdeye.messenger.controller.secure.messaging;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.secure.messaging.GetSecureMessagesResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureMessageReceiveResponse;
import com.birdeye.messenger.service.impl.SecureMessageReceiveEventHandler;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingSessionManagementService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/messenger/secure")
public class SecureMessagingMessageController {

    private final SecureMessageReceiveEventHandler secureMessageReceiveEventHandler;

    private final SecureMessagingMessageService secureMessagingMessageService;

    private final SecureMessagingSessionManagementService secureMessagingSessionManagementService;

    @PostMapping(value = "/send/message", produces = { MediaType.APPLICATION_JSON_VALUE }, consumes = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<SecureMessageReceiveResponse> receiveSecureMessage(
            @RequestAttribute(value = "mcId") Integer mcId,
            @RequestAttribute(value = "cId") Integer cId, @RequestBody SendMessageDTO sendMessageDTO)
            throws Exception {
        log.info("receiveSecureMessage for mcId : {},cId : {}, request : {}", mcId, cId, sendMessageDTO);
        sendMessageDTO.setToCustomerId(mcId.toString());
        sendMessageDTO.setCustomerId(cId);
        SecureMessageReceiveResponse secureMessageReceiveResponse = secureMessageReceiveEventHandler
                .handle(sendMessageDTO);
        return new ResponseEntity<>(secureMessageReceiveResponse, HttpStatus.OK);
    }

    @GetMapping(value = "/messages", produces = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<GetSecureMessagesResponse> getSecureMessages(
            @RequestAttribute(value = "mcId") Integer mcId,
            @RequestAttribute(value = "cId") Integer cId,
            @RequestParam(name = "page", defaultValue = "0") Integer page,
            @RequestParam(name = "size", defaultValue = "20") Integer size) throws Exception {
        log.info("getSecureMessages for mcId : {},cId : {}, page : {},size : {}", mcId, cId, page, size);
        GetSecureMessagesResponse getSecureMessagesResponse = secureMessagingMessageService
                .getSecureMessagesForContact(mcId, cId, page, size);
        return new ResponseEntity<>(getSecureMessagesResponse, HttpStatus.OK);
    }

    @PostMapping(value = "/logout", produces = { MediaType.APPLICATION_JSON_VALUE }, consumes = {
            MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> logOut(@RequestAttribute(value = "mcId") Integer mcId,
            @RequestAttribute(value = "cId") Integer cId,
            @RequestAttribute(value = "secureTokenId") Integer secureTokenId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_SECURE_MESSAGING_TOKEN) String secureMessagingSessionToken)
            throws Exception {
        log.info("logOut called for mcId : {},cId : {} secureTokenId : {} ,sessionToken : {}", mcId, cId, secureTokenId,
                secureMessagingSessionToken);
        secureMessagingSessionManagementService.logOut(mcId, cId, secureMessagingSessionToken, secureTokenId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}