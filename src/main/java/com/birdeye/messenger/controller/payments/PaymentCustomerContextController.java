package com.birdeye.messenger.controller.payments;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.payment.PaymentCustomerContextRequest;
import com.birdeye.messenger.dto.payment.PaymentCustomerContextResponse;
import com.birdeye.messenger.service.payment.PaymentCustomerService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
public class PaymentCustomerContextController {

	@Autowired
	PaymentCustomerService paymentCustomerService;

	@PostMapping(value = "/payments/customer-context")
	public ResponseEntity<PaymentCustomerContextResponse> getOrCreateCustomerContext(@RequestBody PaymentCustomerContextRequest request) {
		log.debug("Request received to get conversation Id, request:{}", request);
		return new ResponseEntity<PaymentCustomerContextResponse>(paymentCustomerService.getOrCreateConversationId(request), HttpStatus.OK);
	}
}
