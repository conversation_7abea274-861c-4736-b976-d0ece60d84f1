package com.birdeye.messenger.controller.payments;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.service.payment.PaymentCardReaderService;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/messenger")
public class PaymentCardReaderController {

    private final PaymentCardReaderService paymentCardReaderService;

    @PostMapping("/card-reader-payment-request")
    public ResponseEntity<MessageResponse> sendPaymentIntentToCardReader(@RequestBody SendMessageDTO sendMessageDTO) {
        MessageResponse messageResponse = paymentCardReaderService.handleCardReaderPayment(sendMessageDTO);
        return new ResponseEntity<>(messageResponse, HttpStatus.OK);
    }
    
}
