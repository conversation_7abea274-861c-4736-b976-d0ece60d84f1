package com.birdeye.messenger.controller.payments;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.service.payment.PaymentEventHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/messenger")
public class PaymentEventsController {

    private final List<PaymentEventHandler> paymentEventHandlers;

    @PostMapping(value = "/payment-event", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> handlePaymentEvents(@Valid @RequestBody PaymentEvent paymentEvent) {
        log.debug("Payment Event : {}", paymentEvent);
        Optional<PaymentEventHandler> paymentEventHandlerOptional = paymentEventHandlers.stream().filter(handler -> handler.getHandlerName().equals(paymentEvent.getEventName())).findFirst();
        paymentEventHandlerOptional.ifPresent(paymentEventHandler -> paymentEventHandler.handle(paymentEvent));
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
