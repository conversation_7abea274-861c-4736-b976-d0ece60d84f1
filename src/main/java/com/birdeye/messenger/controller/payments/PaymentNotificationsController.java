package com.birdeye.messenger.controller.payments;

import com.birdeye.messenger.dto.payment.PaymentNotificationRequest;
import com.birdeye.messenger.service.payment.PaymentNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/messenger")
public class PaymentNotificationsController {

    private final PaymentNotificationService paymentNotificationService;

    @PostMapping(value = "/payment/send-notification", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Void> processPaymentNotification(@RequestBody @Valid PaymentNotificationRequest req) {
        paymentNotificationService.processNotification(req);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }
}
