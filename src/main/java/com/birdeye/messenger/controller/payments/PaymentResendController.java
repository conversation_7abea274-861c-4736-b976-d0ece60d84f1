package com.birdeye.messenger.controller.payments;

import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.payment.PaymentResendRequest;
import com.birdeye.messenger.service.payment.PaymentLinkResendService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/messenger")
public class PaymentResendController {

    private final PaymentLinkResendService paymentLinkResendService;

    @PostMapping("/resend-payment-link")
    public ResponseEntity<MessageResponse> resendPaymentLink(@RequestBody PaymentResendRequest request) throws Exception {
        MessageResponse response = paymentLinkResendService.resendPaymentLink(request);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
