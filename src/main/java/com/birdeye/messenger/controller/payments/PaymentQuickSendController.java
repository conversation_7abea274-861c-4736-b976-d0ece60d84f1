package com.birdeye.messenger.controller.payments;

import static com.birdeye.messenger.constant.Constants.ACC_ID_HEADER;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.AddContactMessengerMessage;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.service.payment.PaymentQuickSendService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class PaymentQuickSendController {

	private final PaymentQuickSendService paymentQuickSendService;
	
    @PostMapping(value = "/event/quick-send")
    public ResponseEntity<MessageResponse> handleQuickSendPayment(@RequestHeader(ACC_ID_HEADER) Integer accountId, @RequestBody AddContactMessengerMessage payload) throws Exception {
        log.debug("Request received to quick send payments, event: {}", payload);
        return new ResponseEntity<MessageResponse>(paymentQuickSendService.handleQuickSendPayment(accountId, payload), HttpStatus.OK);
    }
}
