package com.birdeye.messenger.controller.payments;

import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.service.payment.PaymentManualCardEntryService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/messenger")
public class PaymentManualCardEntryController {

    private final PaymentManualCardEntryService paymentManualCardEntryService;

    @PostMapping("/manual-card-entry-request")
    public ResponseEntity<MessageResponse> manualCardEntryRequest(@RequestBody SendMessageDTO sendMessageDTO) {
        MessageResponse messageResponse = paymentManualCardEntryService.handleManualCardEntryPayment(sendMessageDTO);
        return new ResponseEntity<>(messageResponse, HttpStatus.OK);
    }
    
    @PostMapping("/card-on-file")
    public ResponseEntity<MessageResponse> cardOnFilePayment(@RequestBody SendMessageDTO sendMessageDTO) {
        MessageResponse messageResponse = paymentManualCardEntryService.handleManualCardEntryPayment(sendMessageDTO);
        return new ResponseEntity<>(messageResponse, HttpStatus.OK);
    }
}
