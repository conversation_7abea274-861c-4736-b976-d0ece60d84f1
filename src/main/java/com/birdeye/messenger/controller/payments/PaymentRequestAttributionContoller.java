package com.birdeye.messenger.controller.payments;

import java.util.stream.Collectors;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.payment.PaymentAttributionRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.service.payment.PaymentRequestAttributionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class PaymentRequestAttributionContoller {

	@Autowired
	private PaymentRequestAttributionService paymentAttributionService;
	
	@PostMapping(value = "/link-payment")
	public ResponseEntity<Integer> attributePayment(@Valid @RequestBody PaymentAttributionRequest paymentAttributionRequest, BindingResult bindingResult, @RequestHeader(Constants.USER_ID_HEADER) Integer userId, @RequestHeader(Constants.ACC_ID_HEADER) Integer accountId) {
		log.debug("Payment attribution request is made - {}", paymentAttributionRequest);
		if(bindingResult.hasErrors()) {
			String message = "Validation failed in fields:".concat(bindingResult.getFieldErrors().stream().map(fe->fe.getField()).collect(Collectors.joining(",")));
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.ONE_OR_MORE_VALIDATION_FAILURE, ComponentCodeEnum.PAYMENT)
					.message(message));
		}
		Integer mcId = paymentAttributionService.linkPayment(paymentAttributionRequest, userId, accountId);
		return new ResponseEntity<Integer>(mcId, HttpStatus.OK);
	}
}
