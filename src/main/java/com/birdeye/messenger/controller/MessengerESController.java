package com.birdeye.messenger.controller;

import java.util.Map;

import org.elasticsearch.action.search.SearchResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.service.ElasticSearchExternalService;

import lombok.extern.slf4j.Slf4j;

/**
 * For internal use, expose GET operations for messenger index.
 * 
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/messenger/es")
@Slf4j
public class MessengerESController {

	@Autowired
	private ElasticSearchExternalService esService;
	
	@RequestMapping(value = "/{type}/query", method = RequestMethod.POST)
	public SearchResponse executeQuery(@PathVariable("type") Integer type, @RequestParam(value = "bid", required = false) String accountId,
			@RequestBody String query) {
		log.info("********************* ES QUERY payload {} *********************", query);
		if (type != null) {
			if (1 == type) {
				return esService.searchConversations(query, accountId);
			} else if (2 == type) {
				return esService.searchMessages(query, accountId);

//			} else {
//				return "Invalid type " + type + " , Valid Types: 1 is for Conversations and 2 is for Messages";
			}
		}
//		return "Type is Mandatory, Valid Types: 1 is for Conversations and 2 is for Messages";
		return null;
	}

}
