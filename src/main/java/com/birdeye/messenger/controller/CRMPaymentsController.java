package com.birdeye.messenger.controller;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.dto.payment.CRMInvoiceMessage;
import com.birdeye.messenger.service.payment.CRMPaymentsService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/messenger")
@Slf4j
@RequiredArgsConstructor
public class CRMPaymentsController {

	private final CRMPaymentsService crmPaymentsService;

	@PostMapping(value = "crm/create-invoice")
	public ResponseEntity<Void> createInvoice(@RequestBody CRMInvoiceMessage inputMessage) throws Exception {
		log.debug("CRM createInvoice: {}", inputMessage);
		crmPaymentsService.handleCreateInvoiceEvent(inputMessage);
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
