package com.birdeye.messenger.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ChatTransferAuth;
import com.birdeye.messenger.dto.ContactFreeMarkerData;
import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.EmailMessageDocument;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.NexusEmailService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FreemarkerTemplateService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerDashboardService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.util.JwtUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/messenger/test")
@Slf4j
public class MessengerTestController {

	@Autowired
	private RedisHandler redisHandler;

	@Autowired
	private MessengerDashboardService messengerDashboardService;


	@Autowired
	private UserService userService;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private FreemarkerTemplateService freemarkerTemplateService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private NexusEmailService nexusEmailService;
	
	@Autowired
	private ElasticSearchExternalService elasticSearchService;

	@Autowired
	private RedisLockService redisLockService;
	
	@RequestMapping(value = "/info", method = RequestMethod.GET)
	public Object welcome() {
		log.info("********************* INFO END POINT *********************");
		StringBuilder info = new StringBuilder("MessengerNode[ ");
		Runtime rt = Runtime.getRuntime();
		info.append("Total Cores: ").append(rt.availableProcessors()).append(",");
		info.append("Max Memory: ").append(rt.maxMemory()).append(",");
		info.append("Free Memory: ").append(rt.freeMemory()).append(",");
		info.append("Total Memory: ").append(rt.totalMemory());
		info.append(" ]");
		log.info(info.toString());
		return info.toString();
	}

	@RequestMapping(value = "/lastevent", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public Object getLastEventTime(@RequestParam("accountId") String accountId) {
		return redisHandler.getLastEventFromCache(accountId);
	}

	@RequestMapping(value = "/lastevent", method = RequestMethod.POST)
	public void updateLastEventTime(@RequestParam("accountId") String accountId) {
		redisHandler.updateLastEventCache(accountId);
	}

	@RequestMapping(value = "/pushToEs", method = RequestMethod.POST)
	public void pushToEs(@RequestBody ESRequest doc) {
		log.info("ES request:{0} ", doc);
		elasticSearchService.updateDocument(doc, true);
	}

	@RequestMapping(value = "/get-messages", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public List<MessageDocument> updateLastEventTime(@RequestBody MessengerGlobalFilter messengerGlobalFilter) {
		List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(messengerGlobalFilter);
		return messagesFromES;
	}
	
	@RequestMapping(value = "/get-last-message", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Integer getDoc(@RequestParam("accountId") Integer accountId,@RequestParam("contactId") Integer contactId) {
		Integer messagesFromES = messengerContactService.getLastReceivedMessageSource(contactId, accountId);
		return messagesFromES;
	}
	
	@RequestMapping(value = "/is-last-facebookmessage", method = RequestMethod.GET, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public Boolean islastfacebookmessage(@RequestParam("accountId") Integer accountId,@RequestParam("contactId") Integer contactId) {
//		Boolean islastfacebookmessage = communicationService.isFBSendAvailable(contactId, accountId);
		return null;
	}

	@RequestMapping(value = "{id}/messages", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public MessengerMessage updateLastEventTime(@RequestHeader(Constants.ACC_ID_HEADER) Integer accountId,
			@PathVariable("id") Integer conversationId, @RequestBody MessengerFilter messengerFilter) {
		MessengerMessage msg = messengerDashboardService.getMessages(accountId, conversationId, messengerFilter);
		return msg;
	}


	@GetMapping(path = "/get-webChat-interval/{businessId}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Map<String, List<String>>> testWebChatInterval(@PathVariable("businessId") Integer businessId) {
		Map<String, List<String>> businessUserWebChatTime = userService.getBusinessUserWebChatTime(businessId);
		return new ResponseEntity<>(businessUserWebChatTime, HttpStatus.OK);
	}

	@RequestMapping(value = "/businessNo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public Object getBusinessSmsNumber(@RequestParam("locationId") Integer locationId) {
//		return pdr.getBusinessSmsNumberById(locationId);
		return businessService.getBusinessSmsNumberById(locationId);
	}

	
//	@RequestMapping(value = "/customer", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
//	public Object getCustomerInfo(@RequestParam("customerId") String customerId) {
//		//return pdr.getCustomerInfoById(customerId);
//		List<String> columnNames = Arrays.asList(PlatformDbRepository.C_EMAIL, PlatformDbRepository.C_PHONE,
//				PlatformDbRepository.C_FIRST_NAME, PlatformDbRepository.C_SMS_ENABLED,
//				PlatformDbRepository.C_UNSUBSCRIBED_REASON);
//		List<String> columnNames2 = Arrays.asList(PlatformDbRepository.C_EMAIL, PlatformDbRepository.C_PHONE,
//				PlatformDbRepository.C_FIRST_NAME);
//
//		Map<String, Object> resultWith3Columns = pdr.getCustomerInfo(PlatformDbRepository.GET_CUSTOMER, columnNames2, customerId);
//		log.info("result with 3 columns {}", resultWith3Columns);
//		return pdr.getCustomerInfo(PlatformDbRepository.GET_CUSTOMER, columnNames, customerId);
//	}

	@RequestMapping(value = "/contacts", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public List<MessengerContact> getMessengerContact(@RequestBody MessengerFilter filter) {
		Date cutOffDate = MessengerUtil.parseDate(filter.getStartDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS, TimeZone.getTimeZone("UTC"));
		return messengerContactService.findAllByUpdatedAtAfter(cutOffDate);
	}

	@RequestMapping(value = "/test-conversation-3-ftl", method = RequestMethod.POST, produces = MediaType.TEXT_PLAIN_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
	public String getMessengerContact(@RequestBody ConversationRequest request) {
		ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(request);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("data", freeMarkerData);
		return freemarkerTemplateService.processTemplate(Constants.Elastic.GET_CONVERSATIONS_V3, dataModel);
	}
	
	@RequestMapping(value = "/test-msgr-customer-cache-eviction/{customerId}", method = RequestMethod.GET)
	public void testMsgrCustomerCacheEviction(@PathVariable("customerId") Integer customerId) {
		redisHandler.evictMessengerCustomerCache(customerId);
	}


	@RequestMapping(value = "/test-msgr-customer-caching/{customerId}", method = RequestMethod.GET)
	public ResponseEntity<CustomerDTO> testMessengerCustomerCaching(@PathVariable("customerId") Integer customerId) {
		CustomerDTO customer = contactService.findById(customerId);
		return new ResponseEntity<>(customer, HttpStatus.OK);
	}

	@RequestMapping(value = "/test-email-send-time-for-non-inbox-users/{businessId}", method = RequestMethod.GET)
	public ResponseEntity<HashMap<String,ArrayList<String>>> getEmailSendTimeForNonInboxUsers(@RequestParam("businessId") Integer businessId) {
		return new ResponseEntity<>(userService.getEmailSendTimeForNonInboxUsers(businessId), HttpStatus.OK);
	}

	//referral
	@PostMapping(value = "/get-or-create-customer-test", consumes = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<CustomerDTO> getOrCreateCustomer(@RequestBody KontactoRequest req, @RequestHeader("user") Integer useId, @RequestHeader("accountId") Integer accountId) {
		CustomerDTO customerDTO = contactService.getorCreateNewCustomer(req, useId, accountId);
		return new ResponseEntity<>(customerDTO, HttpStatus.OK);
	}

	@GetMapping(value = "/get-business/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<BusinessDTO> getBusiness(@PathVariable("id")Integer businessId, @RequestParam(value = "all", defaultValue = "0", required = false) Integer includeClosed) {
		BusinessDTO dto;
		if(includeClosed.equals(1)) {
			 dto = businessService.getBusinessDTO(businessId, includeClosed);
		}
		else {
			dto = businessService.getBusinessDTO(businessId);
		}
		return new ResponseEntity<>(dto, HttpStatus.OK);

	}

	@GetMapping(value = "/test-ordering/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
	public void testOrdering(@PathVariable("id") Integer partition) {
		String testString = "Test" + Math.random();
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.TEST_ORDERING, partition, null, testString);
	}

	@GetMapping(value = "/test-adf-chat-transcript-email/")
	public void testChatTranscriptADF() throws Exception {
		Integer businessId = 100034031;
		Long businessNumber = 159169207280976L;
		EmailDTO emailDTO = new EmailDTO();
		emailDTO.setRecipientType(Constants.RECIPIENT_TYPE);
		emailDTO.setSubject("Testing ADF format Email");
		emailDTO.setReplyTo("<EMAIL>");
		emailDTO.setTo(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));
		emailDTO.setBusinessId(businessId.toString());
		emailDTO.setTextType(EmailDTO.TEXT_TYPE.TEXT);

		Map<String, Object> data = new HashMap<>();

		data.put("firstName", "Toto");
		data.put("lastName", "Wolf");
		data.put("phone", "(*************");
		data.put("businessName", "Fesla Motors");

		List<EmailMessageDocument> messages = new ArrayList<>();

		EmailMessageDocument message = new EmailMessageDocument();
		message.setMsgBody("This is a test message");
		message.setCreatedDate(new Date().toString());
		message.setSentON(new Date().toString());
		message.setCreatedBy(new MessageDocument.UserDetail(124, "Doland Trump"));

		EmailMessageDocument message2 = new EmailMessageDocument();
		message2.setMsgBody("This is a test message");
		message2.setCreatedDate(new Date().toString());
		message2.setSentON(new Date().toString());
		message2.setCreatedBy(new MessageDocument.UserDetail(124, "Doland Trump"));

		messages.add(message);
		messages.add(message2);

		data.put("messages", messages);

		nexusEmailService.sendMailV2(businessId,emailDTO,"chat_transcript_adf_format", Constants.MESSENGER_ALERT, businessNumber, data);
	}

	@GetMapping(value = "/test-payment-email-id-api", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<List<String>> testOrdering(@RequestParam("businessId") Integer businessId, @RequestParam("accountId") Integer accountId) {
		List<String> emailIdsForPaymentNotifications = userService.getEmailIdsForPaymentNotifications(accountId, businessId);
		return new ResponseEntity<>(emailIdsForPaymentNotifications, HttpStatus.OK);
	}
	
	@GetMapping(value = "/create-jwt-token", produces = MediaType.TEXT_PLAIN_VALUE)
	public ResponseEntity<Object> createJwtToken() throws Exception {
		long nowMillis = System.currentTimeMillis();
		ChatTransferAuth authInfo = ChatTransferAuth.builder()
		.accountId(1)
		.fromBusinessId(11)
		.toBusinessId(12)
		.fromMcId(21)
		.toMcId(22)
		.timeStamp(nowMillis)
		.build();
		String token = JwtUtil.createJWTAuth(authInfo, "Messenger-Service");
		return new ResponseEntity<>(token, HttpStatus.OK);
	}
	
	@GetMapping(value = "/decode-jwt-token", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Object> decodeJwtToken(@RequestParam("jwt") String jwt) {
		Object authInfo;
		try {
			authInfo = JwtUtil.decodeJWT(jwt);
		} catch (Exception e) {
			throw new NotAuthorizedException("Authorization Failed");
		}
		return new ResponseEntity<>(authInfo, HttpStatus.OK);
	}

	@PostMapping(value = "/test-redis-lock", produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> testRedisLockUnlock(@RequestParam("timeout") Long timeout) throws Exception {
		String lockKey = "lalit";
		Optional<Lock> lockOpt = Optional.empty();
		try {
			lockOpt = redisLockService.tryLock(lockKey);
			Thread.sleep(timeout);
//			redisLockService.expireUnusedOlderThan();
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@RequestMapping(value = "/check-gpt-limit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<Void> checkGPTLimit(@RequestParam("customerId") String customerId) {
		if (redisHandler.isCallingGPTAllowed(String.valueOf(customerId))) {
			log.info("GPT api call allowed");
		} else {
			log.info("GPT api call limit exceeded");
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}
}
