package com.birdeye.messenger.validator;

import org.apache.commons.validator.routines.EmailValidator;
 
/**
 * <AUTHOR>
 *
 */
public class EmailValidatorService{
 
    private EmailValidator emailValidator;

	public EmailValidatorService() {
		emailValidator = EmailValidator.getInstance();
	}
 
	  /**
	   * Validate hex with regular expression
	   * @param hex hex for validation
	   * @return true valid hex, false invalid hex
	   */
	public boolean validate(final String input) {
		
		return emailValidator.isValid(input);
		
	}
}