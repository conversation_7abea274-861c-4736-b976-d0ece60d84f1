package com.birdeye.messenger.validator;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;

import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ErrorMessageBuilder;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ReportRequestValidator {

    public static void validateUserBasedReport(ReportFilter filter) {
        validateCommonFields(filter);

        if (filter.getPage() == null || filter.getSize() == null || filter.getPage() < 0 || filter.getSize() < 0) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_PAGINATION_PARAMS, HttpStatus.BAD_REQUEST));
        }
    }

    public static void validateLocationBasedReportFilter(ReportFilter filter) {
        validateCommonFields(filter);

        if (filter.getPage() == null || filter.getSize() == null || filter.getPage() < 0 || filter.getSize() < 0) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_PAGINATION_PARAMS, HttpStatus.BAD_REQUEST));
        }
    }

    public static void validateCommonFields(ReportFilter filter) {

        if (filter.getAccountId() == null) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
        }

        if (CollectionUtils.isEmpty(filter.getBusinessIds())) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.NO_BUSINESS_ID_SPECIFIED, HttpStatus.BAD_REQUEST));
        }

        if (filter.getStartTimeEpoch() == null || filter.getStartTimeEpoch() <= 0) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_OR_MISSING_START_DATE, HttpStatus.BAD_REQUEST));
        }

        if (filter.getEndTimeEpoch() == null || filter.getEndTimeEpoch() <= 0) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_OR_MISSING_END_DATE, HttpStatus.BAD_REQUEST));
        }
    }
}
