/**
 * 
 */
package com.birdeye.messenger;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingSessionToken;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingSessionManagementService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class SecureMessagingAuthOncePerRequestFilter extends OncePerRequestFilter {

    @Autowired
    private SecureMessagingSessionManagementService secureMessagingSessionManagementService;


    @Autowired
    @Qualifier("handlerExceptionResolver")
    private HandlerExceptionResolver resolver;

    @Override
    public void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        boolean failed = false;
        try {
            log.info("==== SecureMessagingAuthInterceptor doFilterInternal called ====");
            String secureMessagingSessionToken = request
                    .getHeader(RequestLoggingFilter.HEADER_SECURE_MESSAGING_TOKEN);

            SecureMessagingSessionToken secureMessagingSessionTokenEntity = validateSecureMessagingSessionToken(
                    secureMessagingSessionToken);

            chain.doFilter(mutateHttpServletRequest(request, secureMessagingSessionTokenEntity), response);
        } catch (Exception e) {
            log.error("error occurred in fliter : {}", e.getMessage());
            failed = true;
            resolver.resolveException(request, response, null, e);
        } finally {
            log.info("=== SecureMessagingAuthInterceptor doFilterInternal failed : {} ====", failed);
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getServletPath();
        return !path.startsWith("/messenger/secure");
    }

    private HttpServletRequest mutateHttpServletRequest(HttpServletRequest servletRequest,
            SecureMessagingSessionToken secureMessagingSessionTokenEntity) {

        Integer mcId = secureMessagingSessionTokenEntity.getMcId();
        Integer cId = secureMessagingSessionTokenEntity.getCId();
        String path = servletRequest.getServletPath();
        servletRequest.setAttribute("mcId", mcId);
        servletRequest.setAttribute("cId", cId);
        if (path.startsWith("/messenger/secure/logout")) {
            log.info("logout called");
            Integer secureTokenId = secureMessagingSessionTokenEntity.getId();
            servletRequest.setAttribute("secureTokenId", secureTokenId);
        }
        return servletRequest;
    }

    private SecureMessagingSessionToken validateSecureMessagingSessionToken(String secureMessagingSessionToken) {
        log.info("validateSecureMessagingSessionToken called for : {}", secureMessagingSessionToken);
        Date apiCallDate = new Date();

        if (StringUtils.isBlank(secureMessagingSessionToken)) {
            log.error("secureMessagingSessionToken is empty");
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_EMPTY_SESSION_TOKEN);
        }
        SecureMessagingSessionToken secureMessagingSessionTokenEntity = secureMessagingSessionManagementService
                .verifySecureMessagingSessionToken(secureMessagingSessionToken);

        if (Objects.isNull(secureMessagingSessionTokenEntity)) {
            log.error("secureMessagingSessionTokenEntity empty");
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_INVALID_SESSION_TOKEN);
        }

        if (!apiCallDate.before(secureMessagingSessionTokenEntity.getExpiryTime())) {
            log.error("secureMessagingSessionToken expired");
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_EXPIRED_SESSION_TOKEN);
        }
        log.info("secureMessagingSessionTokenEntity fetched : {}", secureMessagingSessionTokenEntity);
        return secureMessagingSessionTokenEntity;
    }

}