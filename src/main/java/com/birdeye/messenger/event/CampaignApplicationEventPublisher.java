package com.birdeye.messenger.event;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.birdeye.messenger.dto.CampaignSMSDto;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CampaignApplicationEventPublisher {
    /**
     * written by <PERSON><PERSON>
     *
     */
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    public void publishCampaignEvent(CampaignSMSDto campaignSMSDto) {
        log.info("Publishing campaign event from sms receive: {}", campaignSMSDto);
        CampaignApplicationEvent campaignApplicationEvent = new CampaignApplicationEvent(this, campaignSMSDto);
        applicationEventPublisher.publishEvent(campaignApplicationEvent);
        log.debug("********** Published campaign event from sms receive **********");
    }
}