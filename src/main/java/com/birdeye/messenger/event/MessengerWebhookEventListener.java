package com.birdeye.messenger.event;

import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dao.entity.WebhookSubscription;
import com.birdeye.messenger.dao.repository.WebhookSubscriptionRepository;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.JSONUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class MessengerWebhookEventListener {

	private final KafkaService kafkaService;
	private final WebhookSubscriptionRepository subscriptionRepository;
	private final RedisHandler redisHandler;

	/**
	 * On application event.
	 * 
	 * @param <T>
	 *
	 * @param event
	 *            the event
	 */
	@EventListener
	@SuppressWarnings("unchecked")
	public <T> void onApplicationEvent(MessengerWebhookEvent event) {
		MessengerKafkaMessage<?> message = event.getKafkaMessage();
		log.info("ProcessMessengerWebhookEvent : Received conversation webhook event source: {}, account: {}",
				event.getSource(), message.getAccountId());
		String subscriptionEndpoint = getActiveSubscriptionEndpointFromRedis(message.getAccountId(),
				message.getEvent());
		if (StringUtils.isNotBlank(subscriptionEndpoint) && !subscriptionEndpoint.equalsIgnoreCase("UNSUBSCRIBED")) {
			message.setEndpoint(subscriptionEndpoint);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.WEBHOOK_EVENT, message);
			log.info("ProcessMessengerWebhookEvent : Kafka message sent {}", JSONUtils.toJSON(message));
		} else {
			log.warn("ProcessMessengerWebhookEvent : No subscriptions {} found for business : {} , event : {} ",
					subscriptionEndpoint, message.getAccountId(), message.getEvent());
		}
	}

	public String getActiveSubscriptionEndpointFromRedis(Integer businessId, String eventName) {
		String redisKey = "SUBSCRIPTION:DETAIL" + businessId + "_" + eventName;
		String endpoint = null;
		try {
			if (StringUtils.isNotBlank(redisHandler.getKeyValueFromRedis(redisKey))) {
				endpoint = redisHandler.getKeyValueFromRedis(redisKey);
			} else {
				WebhookSubscription subscriptionEndpoint = subscriptionRepository
						.findSubscriptionByBusinessIdAndEventId(businessId, eventName);
				if (null == subscriptionEndpoint) {
					log.info(
							"getActiveSubscriptionEndpointFromRedis : No active subscription found for businessId {}, event {} ",
							businessId, eventName);
					endpoint = "UNSUBSCRIBED";
				} else {
					endpoint = subscriptionEndpoint.getEndpoint();
				}
				redisHandler.setOpsForValueWithExpiry(redisKey, endpoint, 15l, TimeUnit.MINUTES);
			}
		} catch (Exception e) {
			log.error(
					"getActiveSubscriptionEndpointFromRedis : Exception while fetching value from redis for businessId {}, event {} ",
					businessId, eventName, e);
		}
		return endpoint;
	}

}
