package com.birdeye.messenger.event;

import java.io.Serializable;

import com.birdeye.messenger.sro.UserIdentity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReplyOnUAConversationEvent implements Serializable {
	private UserIdentity replier;
	private Integer mcId;
	private Integer accountId;
}
