package com.birdeye.messenger.event;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import com.birdeye.messenger.service.CampaignMessageService;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class CampaignApplicationEventListener implements ApplicationListener<CampaignApplicationEvent> {
    /**
     * written by <PERSON><PERSON>
     *
     */
    @Autowired
    private CampaignMessageService campaignMessageService;

    @Override
    public void onApplicationEvent(CampaignApplicationEvent event) {
        log.info("Received ApplicationEvent for Campaign - {}", event.getCampaignSMSDto());
        campaignMessageService.processCampaignEvent(event.getCampaignSMSDto(), true);
    }
}