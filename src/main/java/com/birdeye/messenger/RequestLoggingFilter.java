package com.birdeye.messenger;

import java.io.IOException;
import java.lang.reflect.Type;
import java.time.Duration;
import java.time.Instant;
import java.util.*;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.util.JSONUtils;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;

@Component
@Slf4j
@ControllerAdvice
public class RequestLoggingFilter extends RequestBodyAdviceAdapter implements Filter, ResponseBodyAdvice<Object> {

    private static final String URI = "uri";
    private static final String HTTP_METHOD = "http_method";
    private static final String QUERY = "query";
    private static final String REQUEST_ID = "request_id";
    private static final String CLIENT_IP = "client_ip";
    private static final String STATUS = "http_status";
    private static final String RESPONSE_TIME = "response_time";
    private static final String API_KEY = "api_key";
    public static final String HEADER_USER_ID = Constants.USER_ID_HEADER;
    public static final String HEADER_ACCOUNT_ID = Constants.ACC_ID_HEADER;
    public static final String ACCOUNT_ID = "account_id";
    public static final String USER_ID = "user_id";
    public static final String HEADER_API_KEY = "api-key";
    private static final String PATH_PARAMS = "pathParams";
    public static final String HEADER_ARMOR_REQUEST_ID = "armor-request-id";

    public static final String HEADER_SECURE_KEY = "x-unique-secure-key";
    public static final String HEADER_SECURE_MESSAGING_TOKEN = "x-secure-messaging-token";
    public static final String HEADER_SERVICE_NAME = "SERVICE-NAME";

    private static final Logger REQ_RES_LOGGER = LoggerFactory.getLogger("reqResLogger");

    @Autowired(required = false)
    private HttpServletRequest httpServletRequest;

    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
            throws IOException, ServletException {
        Instant start = Instant.now();
        HttpServletRequest httpServletRequest = (HttpServletRequest) req;
        try {
        	if (httpServletRequest.getHeader(HEADER_ARMOR_REQUEST_ID) != null) {
        		MDC.put(REQUEST_ID, httpServletRequest.getHeader(HEADER_ARMOR_REQUEST_ID));
        	} else {
        		MDC.put(REQUEST_ID, UUID.randomUUID().toString());
        	}
            MDC.put(HEADER_ARMOR_REQUEST_ID, httpServletRequest.getHeader(HEADER_ARMOR_REQUEST_ID));
            MDC.put(ACCOUNT_ID, httpServletRequest.getHeader(HEADER_ACCOUNT_ID));
            MDC.put(USER_ID, httpServletRequest.getHeader(HEADER_USER_ID));
            MDC.put(HEADER_SERVICE_NAME, httpServletRequest.getHeader(HEADER_SERVICE_NAME));
            chain.doFilter(req, resp);
            return;
        } finally {
            Instant finish = Instant.now();
            long time = Duration.between(start, finish).toMillis();
            log.info(requestWrapper(httpServletRequest, (HttpServletResponse) resp, time),
                    "Exec time : {} : is {} ms & status :: {}",
                    httpServletRequest.getRequestURI(), time, ((HttpServletResponse) resp).getStatus());
            MDC.clear();
        }
    }

    @Override
    public void destroy() {
    }

    private LogstashMarker requestWrapper(final HttpServletRequest httpServletRequest,
            final HttpServletResponse httpServletResponse, long time) {
        LogstashMarker marker = Markers.append(HTTP_METHOD, httpServletRequest.getMethod());
        marker.and(Markers.append(URI, httpServletRequest.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE)))
                .and(Markers.append(CLIENT_IP, getClientIP(httpServletRequest)))
                .and(Markers.append(STATUS, httpServletResponse.getStatus()))
                .and(Markers.append(RESPONSE_TIME, time))
                .and(Markers.append(QUERY, getQueryMap(httpServletRequest.getQueryString())))
                .and(Markers.append(PATH_PARAMS,
                        httpServletRequest.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE)))
                .and(Markers.append(API_KEY, httpServletRequest.getHeader(HEADER_API_KEY)))
                .and(Markers.append(HEADER_SECURE_KEY, httpServletRequest.getHeader(HEADER_SECURE_KEY))
                        .and(Markers.append(HEADER_SECURE_MESSAGING_TOKEN,
                                httpServletRequest.getHeader(HEADER_SECURE_MESSAGING_TOKEN))));
        return marker;
    }

    private String getClientIP(final HttpServletRequest request) {
        final String clientIP;
        if (request.getHeader("X-Forwarded-For") != null) {
            clientIP = request.getHeader("X-Forwarded-For").split(",")[0];
        } else {
            clientIP = request.getRemoteAddr();
        }
        return clientIP;
    }

    private Map<String, String> getQueryMap(String query) {
        if (StringUtils.isNotBlank(query)) {
            Map<String, String> queryParamsMap = new HashMap<>();
            String[] queryParams = query.split("&");
            for (String param : queryParams) {
                String[] keyValuePair = param.split("=");
                queryParamsMap.put(keyValuePair[0], keyValuePair[1]);
            }
            return queryParamsMap;
        }
        return null;

    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType,
            Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType,
            Class<? extends HttpMessageConverter<?>> converterType) {
        String uri = null;
        String method = null;
        if (Objects.nonNull(httpServletRequest)) {
            uri = httpServletRequest.getRequestURI();
            method = httpServletRequest.getMethod();
        }
        try {
            String logInternalRequestBody = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                    .getProperty("log.internal.request.body", "true");
            if (Boolean.TRUE.equals(Boolean.valueOf(logInternalRequestBody))) {
                REQ_RES_LOGGER.info("Api : {} {} Request body : {}",
                		method, uri, body);
            }
        } catch (Exception e) {
            REQ_RES_LOGGER.error("error : {} occurred in afterBodyRead for api : {} and method : {}", e.getMessage(),
                    uri, method);
        }
        return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    @Nullable
    public Object beforeBodyWrite(@Nullable Object body, MethodParameter returnType, MediaType selectedContentType,
            Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
            ServerHttpResponse response) {
        HttpServletRequest httpServletRequest = ((ServletServerHttpRequest) request).getServletRequest();
        HttpServletResponse httpServletResponse = ((ServletServerHttpResponse) response).getServletResponse();
        String uri = httpServletRequest.getRequestURI();
        String method = httpServletRequest.getMethod();
        try {
            String logInternalResponseBody = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                    .getProperty("log.internal.response.body", "true");
            if (Boolean.TRUE.equals(Boolean.valueOf(logInternalResponseBody))) {
                REQ_RES_LOGGER.info("Api : {} {} Response body : {} status : {}",
                		method, uri, body, httpServletResponse.getStatus());
            }

        } catch (Exception e) {
            REQ_RES_LOGGER.error("error : {} occurred in afterBodyRead for api : {} and method : {}", e.getMessage(),
                    uri, method);
        }
        return body;
    }

}
