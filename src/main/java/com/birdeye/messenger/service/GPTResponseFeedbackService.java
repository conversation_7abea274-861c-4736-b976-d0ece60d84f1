package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;

/**
 * <AUTHOR>
 *
 */
public interface GPTResponseFeedbackService {

	GPTResponseFeedbackAudit audit(Integer accountId, Integer businessId, Integer mcId, String name, String queryText);

	GPTResponseFeedbackAudit save(GPTResponseFeedbackAudit audit);

	GPTResponseFeedbackAudit findById(Integer eventId);

}
