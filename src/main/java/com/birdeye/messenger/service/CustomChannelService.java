package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.CustomChannelReceiveRequest;
import com.birdeye.messenger.dto.MessageResponse;


public interface CustomChannelService {
    
  
    MessageResponse receiveMessage(CustomChannelReceiveRequest request) throws Exception;
  
    boolean isValidChannelName(String channelName);
    
    boolean hasReachedChannelLimit(Integer accountId);
    
}
