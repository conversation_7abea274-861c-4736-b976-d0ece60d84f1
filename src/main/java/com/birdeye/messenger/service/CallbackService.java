package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.EmailSubscribeStatus;
import com.birdeye.messenger.dto.EmailUnsubscribeStatus;
import com.birdeye.messenger.dto.SmsMessageStatus;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;

/**
 * <AUTHOR>
 *
 */
public interface CallbackService {

	void messengerSMSDeliveryStatusUpdate(SmsMessageStatus request) throws Exception;

	void updateEmailStatusAfterUnsubscribe(EmailUnsubscribeStatus emailUnsubscribeStatus) throws Exception;

	void updateEmailSubscribeStatus(EmailSubscribeStatus emailSubscribeStatus);

	void updateWAMessageDeliveryStatus(WhatsappMessageRequest whatsappMessageRequest);

}
