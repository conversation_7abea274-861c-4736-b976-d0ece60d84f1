package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CallReceiveDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.MessengerData;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.VoiceCallDto;

public interface PostEventHandler {

	/**
	 * Update messenger contact for voice call message to support existing flow.
	 * @param robin 
	 */
	MessengerData handleCallAutoReply(BusinessDTO business, CustomerDTO customer, SmsDTO sms,Boolean spamStatus);

	/**
	 * Update messenger contact for voice call Auto reply.
	 * @param request 
	 */
	MessengerData handleVoiceMail(BusinessDTO business, CustomerDTO customer, VoiceCallDto voiceCall, CallReceiveDTO request);

	MessengerContact handleMissedCallWithoutAutoReply(VoiceCallDto voiceCallDto, BusinessDTO businessDTO, CustomerDTO customerDTO);

//	/**
//	 * 
//	 * 1. Insert/Update messenger Contact with last message changes
//	 *
//	 * 2. Update ES Collections for Conversation & Messages
//	 * 
//	 * @param business - Location where message is sent
//	 * @param customer - Customer reference
//	 * @param sms      - SMS reference
//	 * @return MessageData Documents pushed to ES (conversation & message)
//	 */
//	MessengerData handleWebchatMessage(BusinessDTO business, CustomerDTO customer, SmsDTO sms);
//
//	MessengerData handleCampaignMessage(BusinessDTO business, UserDTO campaignUser, CustomerDTO customer, SmsDTO sms);
}
