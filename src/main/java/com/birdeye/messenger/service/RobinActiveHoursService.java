package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.RobinActiveHours;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomChatBotHoursDto;
import com.birdeye.messenger.dto.RobinActiveHoursDto;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;

import java.util.List;

public interface RobinActiveHoursService {

    void saveAllRobinActiveHourConfig(List<RobinActiveHoursDto> robinActiveHoursDtoList);

    void deleteRobinActiveHourConfigByBusinessId(List<Integer> businessId);

    List<RobinActiveHours> getRobinHoursBusinessIdForWidgetId(Integer widgetId);

    List<RobinActiveHours> getRobinHoursForWidgetIdAndBusinessID(Integer widgetId, Integer businessId);

    List<RobinActiveHours> getRobinHoursForBusinessId(Integer businessId);

    void processChatBotHours(BusinessWebChatConfigurationRequest request, BusinessDTO businessDTO);

    public List<CustomChatBotHoursDto> getChatBotHoursByWidgetId(List<RobinActiveHours> robinActiveHours);

    public void removeChatBotHours(BusinessWebChatConfigurationRequest request, BusinessDTO businessDTO);

}
