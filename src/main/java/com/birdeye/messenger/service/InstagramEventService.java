package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendInstagramMessageResponse;
import com.birdeye.messenger.enums.InstagramMessageStatusEnum;
import com.birdeye.messenger.service.impl.InstagramSendEventHandler;

public interface InstagramEventService {

	SendInstagramMessageResponse sendIGCallToSocial(InstagramSendEventHandler handler, MessageDTO messageDTO,
			String pageId);

	Boolean isInstagramSendAvailable(MessengerContact messengerContact, Integer routeId);

	InstagramMessage saveInstagramMessage(MessageDTO messageDTO, String senderId, String recipientId, String messageId,
			InstagramMessageStatusEnum status);

	String getInstagramIntegrationStatus(Integer businessId);

	Integer getBusinessIdByInstagramPageId(String businessInstagramId);
}
