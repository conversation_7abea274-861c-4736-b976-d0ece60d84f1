package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.apple.chat.AppleInteractiveMessageData;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;

public interface AppleInteractiveEventHandler {
    AppleInteractiveMessageType getEvent();
    AppleInteractiveMessageData handle(SendMessageDTO sendMessageDTO
    		) throws Exception;
}
