package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.instagram.LikeUnlikeRequest;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.MessengerMessage;

/*
 * Service class for handling Messenger dashboard operations.
 */
public interface MessengerDashboardService {

	boolean doRefresh(String accountId, MessengerGlobalFilter notificationFilter);
	
	MessengerMessage getMessages(Integer accountId, Integer conversationId, MessengerFilter messengerFiler);

	MessengerNotificationMessage getNotification(NotificationFilter notificationFilter,String requestSource) throws Exception;

    ContactMergeStatus updateFacebookContact(CustomerDTO customerDTO, Integer messengerContactId);

    MessageResponse addNewContactAndSendSms(Integer accountId, AddContactMessengerMessage message) throws Exception;

	Object getConversations(MessengerFilter filterMessage);

	ConversationResponse getConversationsV2(MessengerFilter filterMessage);

	List<MessengerLocation> getDashboardLocations(NotificationRequest request, Integer userId, Integer accountId);

	MessageResponse switchChatLocation(ChatTransferMessage message, Integer userId, Integer accountId, String requestSource);

	void updateMessageLikeUnlike(LikeUnlikeRequest request);
	
	void deleteMessage(MessageDeleteRequest request,Integer accountId,Integer userId);

	Integer moveMessage(MessageMoveRequest request, Integer accountId, Integer userId);

	public MessengerGlobalFilter getEmailNotificationMetaData(BusinessDTO businessDTO,
															  MessengerContact messengerContact,UserDTO userDTO);
}
