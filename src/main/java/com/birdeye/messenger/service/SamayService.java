package com.birdeye.messenger.service;

import java.io.Serializable;

import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * <AUTHOR>
 *
 */
public interface SamayService {

	void submitToScheduler(PulseSurveyContext existingContext, String timeUnit, long timeUnitValue)
			throws JsonProcessingException;

	void publishToScheduler(Serializable payload, long scheduleAt, KafkaTopicEnum responseTopic);
	
}
