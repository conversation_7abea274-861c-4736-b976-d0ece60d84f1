package com.birdeye.messenger.service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.enums.ChatSessionStatus;

public interface LiveChatSessionService {

	Optional<LiveChatSessionToken> findActiveSessionBySessionId(String sessionId);

	boolean isLiveChatSessionActive(Integer businessId, Integer customerId);
	
	LiveChatSessionToken save(LiveChatSessionToken liveChatSessionToken);

	LiveChatSessionToken getSession(Integer businessId, Integer customerId, Integer accountId, ChatSessionStatus chatSessionStatus, Integer mcid, Integer sourceId, Boolean emailMandatory, Integer widgetId);
	
	Optional<LiveChatSessionToken> findBySessionId(String sessionId);
	
	List<LiveChatSessionToken> findStaleActiveSessions(Date updatedDateBefore);
	
	void updateStatusForStaleSessions(List<String> sessionIds);

	Optional<LiveChatSessionToken> getExistingSession(Integer businessId, Integer mcid);
}
