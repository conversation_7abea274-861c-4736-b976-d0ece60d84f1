package com.birdeye.messenger.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.StatusEnum;

public interface ConversationActivityService {
	
	ConversationActivity create(ActivityType type, UserDTO userDTO);

	ConversationActivity create(ActivityDto activityDto);

	void persistActivityInStoreAndMirror(LiveChatSessionToken liveChatSessionToken, Integer source, ActivityType activityType,
			TeamDto team);

	void persistActivityInStoreForWebChat(LiveChatSessionToken liveChatSessionToken, Integer source,
			ActivityType activityType, Date createdDate, TeamDto team);

	void persistActivityForChannel(ActivityDto activityDto);

	ConversationActivity persistActivityInDatabase(ActivityDto activityDto, UserDTO userDTO);

	MessageDocument persistActivityInES(ActivityDto activityDto);

	void persistActivityForChannel(MessageDTO messageDTO);

    void updateReferralActivity(List<String> activityTypes, Integer referrer, Integer lead, StatusEnum status, Date updatedDate);

    boolean checkIfReferralActivityExists(Integer referrer, Integer lead, List<String> activityTypes);

    void deleteConversationActivityUsingIds(List<Integer> id);

	void deleteConversationActivities(Map<String, List<Integer>> result, Integer customerId);

	void createBlockUnblockActivitySpamPath(ActivityType activityType, CustomerDTO customerDTO, ContactDocument contactDocument, BusinessDTO businessDTO, Date created);
	void createMissedCallActivity(ActivityType activityType, CustomerDTO customerDTO, ContactDocument contactDocument, BusinessDTO businessDTO, Date created);
    void createBlockUnblockActivity(ActivityType activityType, CustomerDTO customerDTO, ContactDocument contactDocument, Integer accountId);

}
