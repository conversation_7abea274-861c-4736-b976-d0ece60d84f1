package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.dto.UserEvent;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.UnassignConversationEvent;

/**
 * <AUTHOR>
 *
 */
public interface AssignmentService {

	ConversationAssignmentResponseDTO assignConversation(ConversationAssignmentRequestDTO request, Integer assigner, Integer accountId);

	MessengerContact updateAssignmentInPrimaryStore(ConversationAssignmentRequestDTO request, Integer assigner, Integer accountId, MessengerContact conversation);

	void triggerSecondaryActionsAsync(MessengerContact conversation, ConversationAssignmentRequestDTO request, Integer assigner,
									  Integer accountId);

	void getConversationsAssignedToTeam(DeleteTeamEvent deleteTeamEvent);

	void unassignConversation(UnassignConversationEvent event);

	void messengerAccessRevoked(UserEvent request);


}
