package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.AppointmentConfirmCreateContextRequest;
import com.birdeye.messenger.dto.AppointmentEventDto;
import com.birdeye.messenger.dto.AppointmentSpecialistImageUpdateRequest;
import com.birdeye.messenger.dto.ContactAppointmentEventDTO;

public interface AppointmentEventConsumerService {
	void consumeAppointmentEvent(ContactAppointmentEventDTO request);
	void consumeAppointmentEventV1(AppointmentEventDto request);
	void appointmentSpecialistImageUpdate(AppointmentSpecialistImageUpdateRequest request);
	void createAppointmentConfirmationContext(AppointmentConfirmCreateContextRequest request);
}
