package com.birdeye.messenger.service;


import java.util.List;

import org.springframework.data.domain.Pageable;

import com.birdeye.messenger.dto.BusinessWebchatEventDetails;

public interface BusinessWebchatEventService {

	List<String> findWebsitesByWidgetId(Integer widgetId);

    List<BusinessWebchatEventDetails> findDistinctEventsInLast24Hours(Pageable pageable);

	BusinessWebchatEventDetails findDistinctEventsInLast24HoursByWidgetId(Integer widgetId);

    Integer findDistinctEventsCountInLast24Hours();



}
