package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.event.ReplyOnUAConversationEvent;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.MessagesDeleteRequest;
import com.birdeye.messenger.sro.UnassignConversationEvent;
import com.birdeye.messenger.sro.WebchatInstallationCrawlerResponse;

public interface MessengerEventHandlerService {

    MessageResponse handleEvent(MessageDTO messageDTO) throws Exception;
    
	void processConversationTagChangeEvent(ConversationTagChangeEvent conversationTagChangeEvent);

	void handleReplyOnUnassignedConversation(ReplyOnUAConversationEvent event);

	void processDeleteTeamEvent(DeleteTeamEvent deleteTeamEvent);

	void unassignConversation(UnassignConversationEvent unassignConversationEvent);

	void messengerAccessRevoked(UserEvent request);

	public MessengerEventHandler resolveHandler(MessageDTO messageDTO);

	void processCustomerSentimentUpdateEvent(CustomerSentimentUpdateEvent customerSentimentUpdateEvent) throws Exception;

	void handlePulseSurveyQuestionEvent(PulseSurveyQuestionRequest request) throws Exception;

	void handlePulseSurveySamayEvent(SamayPayload payload);

	void checkWebchatInstallationStatus(Integer widgetId);

	void updateWebchatInstallationStatus(WebchatInstallationCrawlerResponse response);

	void deleteConversations(MessagesDeleteRequest messagesDeleteRequest);

	void handleRemoveInstagramMappingEvent(List<UpdateInstagramMappingEvent> payload);

	void handleAddInstagramMappingEvent(UpdateInstagramMappingEvent payload);
	
	void handleUpdateInvoiceNumberEvent(UpdateInvoiceNumberEvent payload);

	void deleteConversationsForBusinessMigration(MessagesDeleteRequest messagesDeleteRequest);
	
	void handleFiltersForDeleteTeamEvent(DeleteTeamEvent event);
	
	void handleFiltersForUpdateTeamUserEvent(UpdateTeamUserEvent event);
	
	void handleFiltersForUserEvent(UserEvent event);

	void updateSocialChannelMediaUrl(UpdateBirdeyeCdnSocialAttachmentsDto event);

}
