package com.birdeye.messenger.service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SmsDTO;

public interface MessengerMediaFileService {
	
    MessengerMediaFileDTO saveMedia(SmsDTO sms);

    MessengerMediaFile saveMedia(MessengerMediaFile messengerMediaFile);

    MessengerMediaFile saveMediaFile(MessengerMediaFileDTO mediaFileDTO, Integer id);
    void saveMediaFiles(List<MessengerMediaFileDTO> mediaFileDTOs, Integer id);

	List<MessengerMediaFile> findByMsgId(Integer msgId);

    Optional<MessengerMediaFile> findById(Integer id);

	List<MessengerMediaFile> findByIds(List<Integer> mediaIds);

	void saveAll(Collection<MessengerMediaFile> values);

}
