package com.birdeye.messenger.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.ActivityMigrateDTO;
import com.birdeye.messenger.dto.BusinessAPIKeyMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ClickTrackingRequest;
import com.birdeye.messenger.dto.ConversationWrapperMessage;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.GetAccountMessagesRequest;
import com.birdeye.messenger.dto.GetAccountMessagesResponse;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.SendConversationRequest;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.RobinResponseType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;
import com.birdeye.messenger.enums.ReportType;

public interface CommonService {

	SendConversationRequest getSendSMSRequestForMessenger(ConversationWrapperMessage message, Long requestId);

	void setMessageBody(SendMessageDTO smsMessage, CustomerDTO customerDTO, String source);

	Map<String, ActivityMigrateDTO> getActivityObject(Map<String, String> activitiesMap);

	String getBaseCDNImageURLForBusiness(Integer businessId);

	GetAccountMessagesResponse getAccountMessages(GetAccountMessagesRequest request);

	void rerunMissedSMS() throws Exception;

	List<Integer> getSourceIdsByInboxReportType(ReportType reportType, List<String> sources);

	RichLinkData generateRichLinkObjectFromData(String url, Integer businessId,
			String businessName);

	List<Integer> getExcludedMessageSourcesList();
	
	void updateContactFiltersInMessage(ContactDocument contactDocument,MessageDocumentDTO messageDocumentDTO);

	ConversationSummaryMessageType getConversationSummaryMessageType(MessageType messageType,
			ActivityType activityType);
	UserDTO getUserIdByEmail(String userEmail,Integer fromBusinessId);
	
	void checkUserAccountAccess(Integer userId,Integer accountId);

	CustomerDTO getActiveCustomerAndUpdateConversationState(String fromNumber, String email, Integer businessId, Integer accountId);

	CustomerDTO getActiveCustomerBySource(String fromNumber, String email, String name, Integer businessId,
			Integer accountId, String source);

	void createConversationSwitchActivities(Integer accountId, Date swatichFromActivityDate, Date swatichToActivityDate,
			ContactDocument fromConv, ContactDocument toConv);

	void markConversationActiveOnSend(MessageDTO messageDTO);

	void updateConversationActiveState(ContactDocument contactDocument, List<ContactDocument> conversations);

	CustomerDTO getActiveCustomerAndUpdateConversationStateForAppointment(CustomerDTO customerDTO,
			Integer businessId, Integer accountId, ContactDocument contactDocument);

	void updateRobinResponseTypeInMessage(MessageDocument messageDocument, RobinResponseType robinResponseType);

	void validateWidgetApiKey(Long businessNumber, String apiKey) throws Exception;

	BusinessAPIKeyMessage validateApiKey(String businessId, String apiKey) throws Exception;

	BusinessAPIKeyMessage prepareBusinessAPIKeyMessage(BusinessDTO business);

	void clickTracking(ClickTrackingRequest request);

}
