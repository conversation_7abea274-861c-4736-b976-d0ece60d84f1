package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ConversationClosedEvent;
import com.birdeye.messenger.dto.ConversationWebhookEventDTO;
import com.birdeye.messenger.dto.MessageWebhookEventDTO;
import com.birdeye.messenger.dto.WebhookEventRequest;
import com.birdeye.messenger.dto.WebhookEventResponse;
import com.birdeye.messenger.dto.WebhookSubscriptionRequest;
import com.birdeye.messenger.dto.WebhookSubscriptionResponse;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.event.MessengerKafkaMessage;

/**
 * <AUTHOR>
 *
 */
public interface WebhookService {

	WebhookSubscriptionResponse createWebhookSubscription(WebhookSubscriptionRequest request);

	WebhookSubscriptionResponse getSubscription(Long businessNumber);

	List<WebhookEventResponse> createWebhookEvent(WebhookEventRequest request);

	List<WebhookEventResponse> getAllEvents();

	WebhookSubscriptionResponse getSubscriptionForBusinessByEvent(Integer accountId, String eventName);

	<T> void publishMessengerWebhookEvent(T messagePayload, Integer accountId, String eventName, Integer businessId);

	ConversationWebhookEventDTO mapMessengerContactToConversationWebhookDTO(MessengerContact conversation);

	MessageWebhookEventDTO mapMessageDocumentToMessageWebhookDTO(MessageDocument document);

	boolean isBusinessSubsribedToClosedEvent(ConversationClosedEvent conversationClosedEvent);
	boolean unsubscribeWebhookEvent(WebhookSubscriptionRequest request);

	<T> void publishExternalWebhookEvent(MessengerKafkaMessage<T> request);
}
