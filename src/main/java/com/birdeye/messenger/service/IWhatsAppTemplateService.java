package com.birdeye.messenger.service;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppOnboardingStatus;
import com.birdeye.messenger.dto.whatsapp.*;

public interface IWhatsAppTemplateService {
	
	public void processWhatsAppBusinessLocationOnboardEvent(WhatsAppBusinessLocationStatusEvent locationStatusEvent);
	
	public Map<String, Object> whatsAppTemplateList(Integer accountId, WhatsAppTemplateFilterRequestDto request, Integer page, Integer size, String sortBy, String order);
	
	public WhatsAppTemplateDto getWhatsAppTemplateDetail(Integer templateId);
	
	public void processWhatsAppTemplateStatusUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook statusUpdateWebhook);
	
	public void processWhatsAppTemplateMessageUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook templateUpdateWebhook);

	public void saveWhatsAppTemplateTokens(Integer templateId, WhatsAppTemplateDto templateDto, Integer userId);

	Integer getWABusinessId(String businessPhoneNumberId);

	String getWAPhoneNumberIdByBusinessId(Integer businessId);

	WhatsAppTemplateDto getWhatsAppTemplateDetailByName(String templateName, Integer accountId);

	public void deleteWhatsAppTemplate(Integer templateId);
	
	List<WhatsAppOnboardingStatus> getWhatsappOnboardingStatusByBusinessIds(Integer accountId, List<Integer> businessId);

	WhatsappOnboardingStatusDto whatsappAccountOnboardStatus(Integer accountId, Integer businessId);

	public void processWhatsAppBusinessVerificationUpdateEvent(WhatsAppBusinessLocationStatusEvent businessVerificationEvent);

	public void processWhatsAppTemplateCategoryUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook categoryUpdateEvent);

	public void processWhatsAppTemplateQualityUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook qualityUpdateEvent);

	public void deleteWhatsAppOnboardingStatus(Integer id);

	public Map<String, Object> getWabaNameListByAccountId(Integer accountId);

}
