package com.birdeye.messenger.service;

import com.birdeye.messenger.enums.KafkaTopicEnum;

import java.io.Serializable;

public interface KafkaService {

    void publishToKafkaAsync(KafkaTopicEnum topic, Serializable key, Serializable value);

    void publishToKafkaAsync(KafkaTopicEnum topicEnum, Integer partition, Serializable key, Serializable value);

	void publishToKafkaAsync(KafkaTopicEnum topic, Serializable value);

    void publishToNexusKafkaAsync(KafkaTopicEnum topic, Serializable value);

    void publishToNexusKafkaAsync(KafkaTopicEnum topic, Serializable key, Serializable value);

}
