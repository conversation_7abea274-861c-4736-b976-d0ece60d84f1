package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.BizAppVoiceCallRequest;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CallReceiveDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.VoiceCallDto;
import com.birdeye.messenger.exception.MessengerException;

/**
 * Service interface for all messenger voicemail operations.
 * 
 * <AUTHOR>
 *
 */
public interface MessengerVoiceCallService {

	public void handleCallBack(CallReceiveDTO request) throws MessengerException;

	public void processCallAutoReply(VoiceCallDto dto, boolean missedCall);

	Integer countVoiceCalls(BizAppVoiceCallRequest request);

	void processCall(CallR<PERSON>eiveDTO request, BusinessDTO businessDTO, CustomerDTO customerDTO)
			throws MessengerException;

}
