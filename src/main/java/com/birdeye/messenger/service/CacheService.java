package com.birdeye.messenger.service;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.EnableInboxPerformance;
import com.birdeye.messenger.dto.ErrorCodesMessageRequest;
import com.birdeye.messenger.sro.EnableAIChatbotRequest;
import com.birdeye.messenger.sro.MessengerPropertyRequest;

/**
 * <AUTHOR>
 *
 */
public interface CacheService {
	
	public Map<String, String> getCacheValues();

	public void addCacheValue(MessengerPropertyRequest messengerPropertyRequest);

	public void loadMessengerPropertyCache();

	void loadAllCache();
	
	public void addErrorCodes(List<ErrorCodesMessageRequest> request);

	public void deleteCacheValue(MessengerPropertyRequest messengerPropertyRequest);

	public void enableAIChatbot(EnableAIChatbotRequest enableAIChatbotRequest) throws Exception;

	public void enableAIPerformance(EnableInboxPerformance enableInboxPerformance) throws Exception;


}
