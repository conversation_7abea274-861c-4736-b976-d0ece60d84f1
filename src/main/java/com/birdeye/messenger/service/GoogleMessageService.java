package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.enums.GoogleMessageStatusEnum;

import java.util.List;
import java.util.Optional;

public interface GoogleMessageService {
    Optional<GoogleMessage> findByRequestId(String requestId);
    boolean isGoogleMessagingEnabled(Integer businessId);
    void deleteByCId(Integer cId);
    
    void updateGoogleMessageBody(String messageBody,Integer messageId);
}
