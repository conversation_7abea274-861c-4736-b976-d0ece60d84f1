package com.birdeye.messenger.service.fetchDelta;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.FetchDeltaResponseType;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActiveConversationMessageResponseService implements  FetchDeltaResponseService {

    private final MessengerContactService messengerContactService;
    private final MessageService messageService;

    @Override
    public void setResponse(NotificationRequest notificationRequest, Set<String> fields, MessengerNotificationMessage notification, Integer accountId, Integer userId) {
        if(fields.contains(FetchDeltaResponseType.ACTIVE_CONVERSATION_MESSAGES.getStringValue())) {
            getMessagesForOngoingConversation(notificationRequest, accountId, userId).ifPresent(messageResponse -> setMessagesForOngoingConversation(notification, messageResponse));
        }
    }

    private Optional<MessageResponse> getMessagesForOngoingConversation(NotificationRequest notificationRequest, Integer accountId, Integer userId) {
        Integer mcIdForOngoingConversation = notificationRequest.getConversationId();
        if(Objects.nonNull(mcIdForOngoingConversation)) {
            MessengerContact mcById = messengerContactService.findById(mcIdForOngoingConversation);
            if (mcById != null) {
                MessageRequest messageRequest = new MessageRequest();
                messageRequest.setMessengerContactId(mcIdForOngoingConversation);
                messageRequest.setLastMessageTime(notificationRequest.getFilters().getLastMessageTime());
                messageRequest.setBusinessId(mcById.getBusinessId());
                messageRequest.setWeb(notificationRequest.isWeb());
                messageRequest.setShowReviews(notificationRequest.getAccess().isReviews());
                messageRequest.setAccess(notificationRequest.getAccess());
                return Optional.ofNullable(messageService.getMessageV2(messageRequest, true, userId, accountId,notificationRequest.getRequestSource()));
            }
        }
        return Optional.empty();
    }

    private void setMessagesForOngoingConversation (MessengerNotificationMessage
                                                            notificationMessage, MessageResponse messageResponse){
        Supplier<TreeSet<MessageResponse.Message>> supplier = () -> new TreeSet<>(MessageResponse.getMessageComparator());

        SortedSet<MessageResponse.Message> messages = messageResponse.getMessages();
        SortedSet<MessageResponse.Message> created = messages.stream()
                .filter(message -> MessageDocument.Type.CREATE.getType().equals(message.getType()))
                .collect(Collectors.toCollection(supplier));

        SortedSet<MessageResponse.Message> updated = messages.stream()
                .filter(message -> MessageDocument.Type.UPDATE.getType().equals(message.getType()))
                .collect(Collectors.toCollection(supplier));

        messageResponse.setMessages(created);
        notificationMessage.setUpdatedMessages(updated);
        notificationMessage.setLatestMessages(messageResponse);
    }

    @Override
    public List<String> getFetchDeltaResponseType() {
        return Collections.singletonList(FetchDeltaResponseType.ACTIVE_CONVERSATION_MESSAGES.getStringValue());
    }
}
