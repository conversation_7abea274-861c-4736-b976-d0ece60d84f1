package com.birdeye.messenger.service.fetchDelta;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.ConversationFilter;
import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.enums.FetchDeltaResponseType;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.service.ConversationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class UnreadConversationResponseService implements FetchDeltaResponseService {

    private final ConversationService conversationService;

    @Override
    public void setResponse(NotificationRequest notificationRequest, Set<String> fields, MessengerNotificationMessage notification, Integer accountId, Integer userId) {

        if(fields.contains(FetchDeltaResponseType.UNREAD_CONVERSATIONS.getStringValue()) || fields.contains(FetchDeltaResponseType.UNREAD_COUNT.getStringValue())) {
            ConversationRequest unReadConversationRequest = prepareUnreadConversationRequestForChatBadge(notificationRequest, fields);
            ConversationResponse unreadConversations = conversationService.getConversationV2(unReadConversationRequest,
                    accountId, userId);
            notification.setUnreadConversations(Objects.nonNull(unreadConversations) ? unreadConversations.getMessengerContactMessages() : null);
            if(Objects.nonNull(unReadConversationRequest.getFilters()) && Objects.nonNull(unReadConversationRequest.getFilters().getInboxSnapShotTime())){
                unReadConversationRequest.getFilters().setRenderedConversationIds(null);
                unReadConversationRequest.getFilters().setInboxSnapShotTime(null);
                unreadConversations = conversationService.getConversationV2(unReadConversationRequest,
                        accountId, userId);
            }
            notification.setUnreadCount(Objects.nonNull(unreadConversations) ? unreadConversations.getCount() : null);
        }

    }

    private ConversationRequest prepareUnreadConversationRequestForChatBadge (NotificationRequest notificationRequest, Set<String> fields){
        ConversationRequest unReadConversationRequest = new ConversationRequest();
        ConversationFilter filter = notificationRequest.getFilters();
        unReadConversationRequest.setStartIndex(0);
        unReadConversationRequest.setCount(0);
        unReadConversationRequest.setMoveConversationToTop(null);
        unReadConversationRequest.getFilters().setConversationStatus("UNREAD"); // see ContactFreeMarkerData.getTAg() method
        unReadConversationRequest.getFilters().setBusinessIds(filter.getBusinessIds());
        unReadConversationRequest.setWeb(notificationRequest.isWeb());
        unReadConversationRequest.getFilters().setTimePeriodSelected(filter.getTimePeriodSelected());
        unReadConversationRequest.setAccess(notificationRequest.getAccess());
        if(Objects.nonNull(notificationRequest.getFilters()) && Objects.nonNull(notificationRequest.getFilters().getInboxSnapShotTime())){
            if(CollectionUtils.isNotEmpty(notificationRequest.getRenderedConversationIds())){
                unReadConversationRequest.getFilters().setRenderedConversationIds(notificationRequest.getRenderedConversationIds());
            }
            unReadConversationRequest.getFilters().setInboxSnapShotTime(notificationRequest.getFilters().getInboxSnapShotTime());
        }
        if (!fields.contains(FetchDeltaResponseType.UNREAD_CONVERSATIONS.getStringValue()) && fields.contains(FetchDeltaResponseType.UNREAD_COUNT.getStringValue())) {
            return unReadConversationRequest;
        }
        unReadConversationRequest.setStartIndex(notificationRequest.getStartIndex());
        unReadConversationRequest.setCount(notificationRequest.getCount());
        return unReadConversationRequest;
    }

    @Override
    public List<String> getFetchDeltaResponseType() {
        return Arrays.asList(FetchDeltaResponseType.UNREAD_CONVERSATIONS.getStringValue(),
                FetchDeltaResponseType.UNREAD_COUNT.getStringValue());
    }

}
