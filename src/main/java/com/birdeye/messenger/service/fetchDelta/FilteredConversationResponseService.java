package com.birdeye.messenger.service.fetchDelta;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.enums.FetchDeltaResponseType;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.service.ConversationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FilteredConversationResponseService implements FetchDeltaResponseService {

    private final ConversationService conversationService;

    @Override
    public void setResponse(NotificationRequest notificationRequest, Set<String> fields, MessengerNotificationMessage notification, Integer accountId, Integer userId) {
        if(fields.contains(FetchDeltaResponseType.FILTERED_CONVERSATION.getStringValue())) {
            // Fetch the delta conversations corresponding to the current filter
            ConversationRequest conversationRequest = new ConversationRequest(notificationRequest,false);
            conversationRequest.setAssignmentType(notificationRequest.getAssignmentType());
            ConversationResponse filteredConversations = conversationService.getConversationV2(conversationRequest,
                    accountId, userId);
            if (Objects.nonNull(notificationRequest.getFilters().getLastMessageTime())) {
                notification.setFilteredConversationList(Objects.nonNull(filteredConversations) ? filteredConversations.getMessengerContactMessages(): null);
                ConversationRequest conversationRequestWithoutLMsgTime = new ConversationRequest(notificationRequest,true);
                if(Objects.nonNull(conversationRequestWithoutLMsgTime.getFilters()) && Objects.nonNull(conversationRequestWithoutLMsgTime.getFilters().getInboxSnapShotTime())) {
                    conversationRequestWithoutLMsgTime.getFilters().setInboxSnapShotTime(null);
                    conversationRequestWithoutLMsgTime.getFilters().setRenderedConversationIds(null);
                }
                conversationRequestWithoutLMsgTime.setAssignmentType(notificationRequest.getAssignmentType());
                conversationRequest.setCount(0);
                // Below is to fetch the count of all conversations without the last
                ConversationResponse conversationResponseWithoutLMsgTime = conversationService
                        .getConversationV2(conversationRequestWithoutLMsgTime, accountId, userId);
                notification.setFilteredConversationTotalCount(conversationResponseWithoutLMsgTime.getCount());
            } else {
                notification.setFilteredConversationList(Objects.nonNull(filteredConversations) ? filteredConversations.getMessengerContactMessages(): null);
                if(Objects.nonNull(conversationRequest.getFilters()) && Objects.nonNull(conversationRequest.getFilters().getInboxSnapShotTime())) {
                    conversationRequest.getFilters().setInboxSnapShotTime(null);
                    conversationRequest.getFilters().setRenderedConversationIds(null);
                    filteredConversations = conversationService.getConversationV2(conversationRequest,
                            accountId, userId);
                }
                notification.setFilteredConversationTotalCount(Objects.nonNull(filteredConversations) ? filteredConversations.getCount(): null);
            }
        }

    }

    @Override
    public List<String> getFetchDeltaResponseType() {
        return Collections.singletonList(FetchDeltaResponseType.FILTERED_CONVERSATION.getStringValue());
    }
}
