package com.birdeye.messenger.service.fetchDelta;

import java.util.List;
import java.util.Set;

import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;

public interface FetchDeltaResponseService {

    List<String> getFetchDeltaResponseType();

    void setResponse(NotificationRequest notificationRequest, Set<String> fields, MessengerNotificationMessage notification, Integer accountId, Integer userId);

}
