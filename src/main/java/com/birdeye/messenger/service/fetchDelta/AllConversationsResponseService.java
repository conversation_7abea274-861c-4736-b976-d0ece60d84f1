package com.birdeye.messenger.service.fetchDelta;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.enums.FetchDeltaResponseType;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.service.ConversationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AllConversationsResponseService implements FetchDeltaResponseService {

    private final ConversationService conversationService;

    @Override
    public void setResponse(NotificationRequest notificationRequest, Set<String> fields, MessengerNotificationMessage notification, Integer accountId, Integer userId) {
        if(fields.contains(FetchDeltaResponseType.ALL_CONVERSATIONS.getStringValue())) {
            ConversationRequest openConversationRequest = prepareOpenConversationRequestForChatBadge(notificationRequest);
            ConversationResponse allConversations = conversationService.getConversationV2(openConversationRequest,
                    accountId, userId);
            notification.setAllConversations(Objects.nonNull(allConversations) ? allConversations.getMessengerContactMessages() : null);
        }

    }

    private ConversationRequest prepareOpenConversationRequestForChatBadge(NotificationRequest notificationRequest) {
        ConversationRequest openConversationRequest = new ConversationRequest();
        openConversationRequest.setStartIndex(notificationRequest.getStartIndex());
        openConversationRequest.setCount(notificationRequest.getCount());
        openConversationRequest.getFilters().setConversationStatus("OPEN");
        openConversationRequest.setMoveConversationToTop(null);
        openConversationRequest.getFilters().setBusinessIds(notificationRequest.getFilters().getBusinessIds());
        openConversationRequest.setWeb(notificationRequest.isWeb());
        openConversationRequest.getFilters().setTimePeriodSelected(notificationRequest.getFilters().getTimePeriodSelected());
        openConversationRequest.setAccess(notificationRequest.getAccess());
        if(Objects.nonNull(notificationRequest.getFilters()) && Objects.nonNull(notificationRequest.getFilters().getInboxSnapShotTime())){
            if(CollectionUtils.isNotEmpty(notificationRequest.getRenderedConversationIds())){
                openConversationRequest.getFilters().setRenderedConversationIds(notificationRequest.getRenderedConversationIds());
            }
            openConversationRequest.getFilters().setInboxSnapShotTime(notificationRequest.getFilters().getInboxSnapShotTime());
        }
        log.debug("getDashboardNotification: OPEN conversation filter : {}", openConversationRequest);
        return openConversationRequest;
    }

    @Override
    public List<String> getFetchDeltaResponseType() {
        return Collections.singletonList(FetchDeltaResponseType.ALL_CONVERSATIONS.getStringValue());
    }
}
