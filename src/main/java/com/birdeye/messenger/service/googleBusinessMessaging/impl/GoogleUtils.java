package com.birdeye.messenger.service.googleBusinessMessaging.impl;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.SendGoogleMessageSocialDTO;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleSocialIntegrationService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class GoogleUtils {

    private final GoogleSocialIntegrationService googleSocialIntegrationService;

    public boolean isGoogleSendAvailable(MessengerContact messengerContact, Integer routingId) {
        log.info("Checking Google Send Available for : {}", routingId);
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        if (lastMessageMetaData == null) {
            return isGoogleSendAvailableHelper(messengerContact, routingId);
        } else {
            return calculateGoogleSendAvailable(lastMessageMetaData.getLastFbReceivedAt());
        }
    }

    boolean isGoogleSendAvailableHelper(MessengerContact messengerContact, Integer routingId) {
        MessengerFilter messengerFilter = new MessengerFilter();
        messengerFilter.setStartIndex(0);
        messengerFilter.setCount(1);
        messengerFilter.setConversationId(messengerContact.getId());
        messengerFilter.setAccountId(routingId);
        List<String> typeList = new ArrayList<>();
        typeList.add("SMS_RECEIVE");
        typeList.add("MMS_RECEIVE");
        Map<String, Object> params = new HashMap<>();
        params.put("msg_type", ControllerUtil.getJsonTextFromObject(typeList));
        params.put("source", Source.GOOGLE);
        messengerFilter.setParams(params);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        String lastGoogleReceivedAt = lastMessageMetadataPOJO.getLastFbReceivedAt();
        return calculateGoogleSendAvailable(lastGoogleReceivedAt);
    }

    private boolean calculateGoogleSendAvailable(String lastGoogleReceivedAt) {
        log.info("Last Google Message received on : {}", lastGoogleReceivedAt);
        boolean isLastGoogleMessage = false;
        if (StringUtils.isNotBlank(lastGoogleReceivedAt)) {
            Calendar calLastReceived = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                calLastReceived.setTime(sdf.parse(lastGoogleReceivedAt));
                calLastReceived.add(Calendar.MONTH, 1);
                if (calLastReceived.getTime().before(Calendar.getInstance().getTime())) {
                    isLastGoogleMessage = true;
                }
            } catch (Exception e) {
                log.error("exception in parsing date of calculateFBSendAvailable method: ", e);
            }
        }
        return isLastGoogleMessage;
    }

    public boolean isGoogleUserReachable(MessengerContact messengerContact) {
        Integer currentBusinessId = messengerContact.getBusinessId();
        Integer businessIdMapWithLastMessage = googleSocialIntegrationService.getBusinessIdForPlaceId(messengerContact.getGoogleConversationId());
        return currentBusinessId.equals(businessIdMapWithLastMessage);
    }

    void validateGoogleSenderId(SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO) {
        if (StringUtils.isBlank(sendGoogleMessageSocialDTO.getSenderId())) {
            throw new NotFoundException(new ErrorMessageBuilder(ErrorCode.GOOGLE_PAGE_NOT_FOUND, 
            		ComponentCodeEnum.GOOGLE, HttpStatus.NOT_FOUND));
        }
    }

}
