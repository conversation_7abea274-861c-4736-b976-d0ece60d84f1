package com.birdeye.messenger.service.googleBusinessMessaging.impl;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleChatWidgetConfig;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerService;
import com.birdeye.messenger.service.googleBusinessMessaging.AutoResponderService;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleSocialIntegrationService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.BusinessHoursUtility;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AutoResponderServiceImpl implements AutoResponderService {

    private final BusinessService businessService;
    private final GoogleSocialIntegrationService googleSocialIntegrationService;
    private final KafkaService kafkaService;
    private final MessengerService messengerService;

    @Override @Async
    public void sendAutoResponseForGoogleMessaging(SendMessageDTO sendMessageDTO, Date receivedAt,MessageDTO messageDTO) {
        Integer businessId = sendMessageDTO.getFromBusinessId();
        BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
        if(Objects.nonNull(businessDTO.getEnterpriseId())) {
            businessDTO = businessService.getBusinessDTO(businessDTO.getAccountId());
        }
        GoogleUserMessage event = (GoogleUserMessage) messageDTO;
        BusinessTimingDTO businessTimings = businessService.getBusinessTimings(businessId,false);
        if(Objects.isNull(businessTimings)) {
            log.info("sendAutoResponseForGoogleMessaging: no auto - response sent as business timing not found for {}", businessId);
            return;
        }
        Boolean receivedDuringBusinessHr = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimings, receivedAt);
        boolean hasAgentResponedOnOutsideBH=false;
        if(StringUtils.isNotBlank(sendMessageDTO.getToCustomerId())) {
            hasAgentResponedOnOutsideBH=messengerService.hasAgentRespondedOnOutsideBH(businessDTO.getRoutingId(), Integer.valueOf(sendMessageDTO.getToCustomerId()), receivedDuringBusinessHr);
        }
        if (!receivedDuringBusinessHr){
            GoogleChatWidgetConfig chatWidgetConfig = googleSocialIntegrationService.getGoogleChatWidgetConfig(new GoogleAgentName(event.getAgent()));
            //GoogleChatWidgetConfig chatWidgetConfig = new GoogleChatWidgetConfig(); chatWidgetConfig.setOfflineMessage("this is a hardcoded offline text test");
            if(chatWidgetConfig != null && StringUtils.isNotBlank(chatWidgetConfig.getOfflineMessage()) && !hasAgentResponedOnOutsideBH) {
                    sendMessageDTO.setBody(chatWidgetConfig.getOfflineMessage());
                    sendMessageDTO.setBotName(chatWidgetConfig.getBrandName());
                    kafkaService.publishToKafkaAsync(KafkaTopicEnum.SEND_AUTO_RESPONSE, sendMessageDTO);
                    return;
            }
            log.info("sendAutoResponseForGoogleMessaging: no auto - response sent as no out of business hour message found configured for accountId {}", businessDTO.getAccountId());
            return;
        }
        log.info("sendAutoResponseForGoogleMessaging: message received during business hour for accountId {} at time {}", businessId, receivedAt);
    }


}
