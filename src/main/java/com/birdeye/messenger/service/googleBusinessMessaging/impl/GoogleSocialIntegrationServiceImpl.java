package com.birdeye.messenger.service.googleBusinessMessaging.impl;

import java.util.Collections;
import java.util.Map;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.util.LogUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleChatWidgetConfig;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleSocialIntegrationService;
import com.birdeye.messenger.sro.GetEnterpriseIdSocialRequest;
import com.birdeye.messenger.sro.GetEnterpriseIdSocialResponse;
import com.birdeye.messenger.util.ControllerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoogleSocialIntegrationServiceImpl implements GoogleSocialIntegrationService {

	@Value("${social.base.url}")
	private String socialBaseUrl;

	@Value("${social.messenger.url}")
	private String socialUrl;

	private final RestTemplate restTemplate;

	private final RedisHandler redisHandler;

	@Override
	public void sendMessageToGoogle(GoogleSendSocialRequest request) {
		String url = this.socialUrl + "/google/send";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<GoogleSendSocialRequest> httpEntity = new HttpEntity<>(request, headers);
		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
		if (!response.getStatusCode().is2xxSuccessful()) {
			log.error("sendMessageToGoogle: failed to send message {} to google. Error Response received {}", request,
					response);
			throw new MessengerException(
					"Failed to send message to google via social-service integration" + request);
		}
		log.info("Send Message to Google Via Social with request : {} Success with response : {}", request, response);
	}

	@Override
	public Integer getBusinessIdForPlaceId(String googlePlaceId) {
		String url = this.socialBaseUrl + "/social/gmb/gmb-location?placeId=" + googlePlaceId;
		return restTemplate.getForObject(url, Integer.class);
	}

	@Override
	public String getGoogleIntegrationStatus(Integer businessId) {

		log.info("getGoogleIntegrationStatus: for businessId {}", businessId);
		String integrationStatus = redisHandler.getCachedGoogleStatus(String.valueOf(businessId));
		if (StringUtils.isNotBlank(integrationStatus)) {
			Map objectFromJsonText = ControllerUtil.getObjectFromJsonText(integrationStatus, Map.class);
			if (MapUtils.isNotEmpty(objectFromJsonText) && objectFromJsonText.containsKey("status")) {
				return objectFromJsonText.get("status").toString();
			}
		}

		log.info("getGoogleIntegrationStatus: cache miss for businessId {}", businessId);

		String url = this.socialBaseUrl + "/social/gmb/status/" + businessId;
		Map<?, ?> responseFromSocial = restTemplate.getForObject(url, Map.class);
		if (MapUtils.isNotEmpty(responseFromSocial)) {
			Object status = responseFromSocial.get("status");
			if (status != null) {
				integrationStatus = responseFromSocial.get("status").toString();
				try {
					redisHandler.updateGMBStatusCache(String.valueOf(businessId), responseFromSocial);
				} catch (JsonProcessingException e) {
					log.info("getGoogleIntegrationStatus: failed to cache for {}", businessId);
				}
			}
		}
		log.info("GoogleIntegrationStatus is {} for BusinessId {} ", businessId, integrationStatus);
		return integrationStatus;
	}

	@Override
	public GoogleChatWidgetConfig getGoogleChatWidgetConfig(GoogleAgentName googleAgentName) {
		log.info("get google chat widget config from social for agentName : {}",googleAgentName);
		String url = this.socialBaseUrl + "/social/account/agent";
		HttpHeaders headers = new HttpHeaders();
		headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> entity = new HttpEntity<>(googleAgentName,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<GoogleChatWidgetConfig> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity,GoogleChatWidgetConfig.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getGoogleChatWidgetConfig", startTime, endTime);
		if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
			log.info("Error in response from social service to get GoogleChatWidgetConfig");
			return null;
		}
		return responseEntity.getBody();
	}

	@Override
	public void sendGoogleCallToSocial(SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO, boolean isBot) {

		MessageDTO messageDTO = sendGoogleMessageSocialDTO.getMessageDTO();
		String body = ((SendMessageDTO) messageDTO).getBody();
		validateGoogleMessage(sendGoogleMessageSocialDTO);
		GoogleSendSocialRequest message = prepareGoogleSocialRequest(sendGoogleMessageSocialDTO, isBot);

		if (StringUtils.isNotEmpty(body)) {
			message.setText(((SendMessageDTO) messageDTO).getBody());
			sendMessageToGoogle(message);
		} else if (!((SendMessageDTO) messageDTO).getMediaurl().isEmpty()) {
			message.setText(((SendMessageDTO) messageDTO).getMediaurl());
			sendMessageToGoogle(message);
		}
	}

	GoogleSendSocialRequest prepareGoogleSocialRequest(SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO,
			boolean isBot) {
		GoogleSendSocialRequest message = new GoogleSendSocialRequest();
		message.setName(sendGoogleMessageSocialDTO.getConversationId(), sendGoogleMessageSocialDTO.getMessageId());
		message.setMessageId(sendGoogleMessageSocialDTO.getMessageId());
		GoogleSendSocialRequest.Representative representative = new GoogleSendSocialRequest.Representative();
		UserDTO userDto = sendGoogleMessageSocialDTO.getMessageDTO().getUserDTO();
		representative.setDisplayName(userDto.getName());
		if (isBot) {
			String botName = sendGoogleMessageSocialDTO.getMessageDTO().getBotName();
			botName = StringUtils.isNotBlank(botName) ? botName : "Auto-Reply";
			representative.setDisplayName(botName);
			representative.setRepresentativeType("Human");
		} else {
			representative.setRepresentativeType("Human");
		}
		message.setRepresentative(representative);
		return message;
	}

	private void validateGoogleMessage(SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO) {
		if (sendGoogleMessageSocialDTO.getConversationId().isEmpty()) {
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.MESSENGER_CONTACT_NOT_EXIST, ComponentCodeEnum.GOOGLE)
					.message("Validation Failure [conversationId is missing], request - {}", sendGoogleMessageSocialDTO));
		}
	}

	@Override
	public GetEnterpriseIdSocialResponse getAccountIdByAgentId(String agentId) {
		String url = this.socialBaseUrl + "/social/account/account-agent/";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		GetEnterpriseIdSocialRequest socialESSRequest = new GetEnterpriseIdSocialRequest(agentId);
		HttpEntity<GetEnterpriseIdSocialRequest> httpEntity = new HttpEntity<>(socialESSRequest, headers);
		ResponseEntity<GetEnterpriseIdSocialResponse> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity,
				GetEnterpriseIdSocialResponse.class);
		if (response.getStatusCode().is2xxSuccessful() && response.getBody()!= null) {
			Long enterpriseId = response.getBody().getEnterpriseId();
			log.info("enterpriseId : {} fetched for agentId : {}", enterpriseId, agentId);
			return response.getBody();

		} else {
			log.error("sendMessageToGoogle: failed to send message {} to google. Error Response received {}",
					socialESSRequest, response);
			throw new MessengerException(
					"Failed to send message to google via social-service integration" + socialESSRequest);
		}

	}
}
