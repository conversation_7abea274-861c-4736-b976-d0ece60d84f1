package com.birdeye.messenger.service.googleBusinessMessaging;

import com.birdeye.messenger.dto.GoogleAgentName;
import com.birdeye.messenger.dto.GoogleSendSocialRequest;
import com.birdeye.messenger.dto.SendGoogleMessageSocialDTO;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleChatWidgetConfig;
import com.birdeye.messenger.sro.GetEnterpriseIdSocialResponse;

public interface GoogleSocialIntegrationService {

	void sendMessageToGoogle(GoogleSendSocialRequest request);

	Integer getBusinessIdForPlaceId(String googlePlaceId);

	String getGoogleIntegrationStatus(Integer businessId);

	GoogleChatWidgetConfig getGoogleChatWidgetConfig(GoogleAgentName googleAgentName);

	void sendGoogleCallToSocial(SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO, boolean isHuman);

	GetEnterpriseIdSocialResponse getAccountIdByAgentId(String agentId);

}
