package com.birdeye.messenger.service.googleBusinessMessaging.impl;

import com.birdeye.messenger.dao.entity.googleBusinessMessaging.GoogleChatWidgetConfig;
import com.birdeye.messenger.dao.repository.googleBusinessMessaging.GoogleChatWidgetConfigRepository;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleChatWidgetConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class GoogleChatWidgetConfigServiceImpl implements GoogleChatWidgetConfigService {

    private final GoogleChatWidgetConfigRepository googleChatWidgetConfigRepository;

    @Override
    public GoogleChatWidgetConfig findByAccountId(Integer accountId) {
        Optional<GoogleChatWidgetConfig> config = googleChatWidgetConfigRepository.findByAccountId(accountId);
        if(config.isPresent()) return config.get();
        log.info("google chat widget not found for accountId {} ", accountId);
        return new GoogleChatWidgetConfig();
    }

    @Override
    public void saveOrUpdateGoogleChatWidgetConfig(GoogleChatWidgetConfig googleChatWidgetConfig) {
        googleChatWidgetConfigRepository.save(googleChatWidgetConfig);
    }
}
