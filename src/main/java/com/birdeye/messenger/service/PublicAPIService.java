package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.AddNoteRequest;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.PublicAddAndSendContact;
import com.birdeye.messenger.dto.PublicUpdateStatusConversationRequest;

public interface PublicAPIService {
	 MessageResponse addAndSendMessage(Integer accountId, PublicAddAndSendContact message) throws Exception;

	MessageResponse addNote(AddNoteRequest request) throws Exception;

	ConversationAssignmentResponseDTO assignConversation(ConversationAssignmentRequestDTO request,Integer accountId);

	void updateConversationStatus(Integer accountId, PublicUpdateStatusConversationRequest request);

	MessageResponse receiveCustomChannelMessage(CustomChannelReceiveRequest request) throws Exception;
}
