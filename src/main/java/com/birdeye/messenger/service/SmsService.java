package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dao.entity.Sms;

public interface SmsService {

	Sms saveSMS(SendMessageDTO sendMessageDTO);

	List<Sms> findByCustomerId(Integer customerId);

	/**
	 * Save SMS - From customer. From Phone - Customer, ToPhone - Business Twilio
	 * API requires phone number to be of E164 format
	 *
	 * WEBCHAT, LIVECHAT, VOICE CALL , Customr RECEIVE
	 *
	 * @param smsDTO
	 * @return SMS entity
	 */
	Sms saveSmsFromCustomer(SmsDTO smsDTO);

	/**
	 * Save SMS - To customer. From Phone - Business, ToPhone - Customer Twilio API
	 * requires phone number to be of E164 format
	 *
	 * Autoreply to customer (WEBCHAT, LIVECHAT, VOICE CALL), Dashboard SEND
	 *
	 * @param smsDTO
	 * @return SMS entity
	 */
	public Sms saveSmsToCustomer(SmsDTO smsDTO);

	Sms saveSms(SmsDTO smsDTO);

	Sms saveAndSend(SmsDTO smsDTO,BusinessDTO businessDTO);

	Sms saveSmsForChatbotReply(SmsDTO smsDTO);

	Sms saveSms(SMSMessageDTO smsMessageDTO);

	Sms findById(Integer id);

    List<Sms> findByMessageSid(String messageSid);

	void saveSMS(Sms sms);

	String getFormattedBusinessNumber(Integer businessId);

	String getFormattedBusinessNumber(Integer businessId, String phoneNumber);

	void encrypt(Sms sms);

	void deleteSmsUsingCustomerId(Integer customerId);

	Sms saveSms(SmsDataDto smsDataDto);

	Sms findByReviewRequestId(Long reviewRequestId);

	Sms findByReviewRequestIdAndType(String externalUid, String smsType);

	void updateSmsMessageBody(String messageBody,Integer messageId);


}
