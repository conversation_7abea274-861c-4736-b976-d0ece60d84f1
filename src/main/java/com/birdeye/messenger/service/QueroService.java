/**
 * 
 */
package com.birdeye.messenger.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.QueroFAQResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public interface QueroService {

    QueroFAQResponse getFAQsByIds(List<Integer> faqIds, Integer accountId, Boolean answers);

	void queroRobinIngestion(Integer accountId, Integer userId, boolean isSMB);

	void queroDefaultFAQCreation(Integer accountId, Integer userId, boolean isSMB);

	void queroProfileLinkAndZeroStateMigration(Integer accountId, Integer userId, boolean isSMB);

}
