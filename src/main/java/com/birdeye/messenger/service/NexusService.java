package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.*;

public interface NexusService {

	void updateFirebaseDb(GenericFirebaseMessage genericFirebaseMessage);
	
	/**
	 * Send SMS via Nexus
	 * 
	 */
	
	void sendSMS(ConversationWrapperMessage conversation);

	VideoConversationDTO initiateVideo(Integer bid, Integer conversationId);

	void terminateRoom(String roomId);

	void deleteFromFireBaseDB(String topicName);

	Boolean checkSpamFromTwilio(CheckSpamRequestDto checkSpamRequestDto);
	
	TenDlcStatusDTO checkTenDlcStatus(Long enterpriseNumber);
}
