/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.secure.messaging.SecureMessagingAuthDAO;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.secure.messaging.GetSecureMessagesResponse;
import com.birdeye.messenger.dto.secure.messaging.GetSecureMessagesResponse.SecureMessageResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkResponseDTO;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class SecureMessagingMessageServiceImpl implements SecureMessagingMessageService {

    @Autowired
    private ConversationActivityService conversationActivityService;

    @Autowired
    @Lazy
    private SecureMessagingAuthDAO secureMessagingAuthDAO;

    @Autowired
    private MessengerContactService messengerContactService;

    @Autowired
    private SecureMessagingCommonService secureMessagingCommonService;

    @Autowired
    private BusinessService businessService;

    /*
     * publish secure message initiated/started/ended activity
     */
    @Override
    public void publishSecureMessagingActivity(MessageDTO messageDTO, ActivityType activityType) {
        log.info("SecureMessagingMessageServiceImpl publishSecureMessagingActivity called with actityType : {}",
                activityType);
        addActivityIfChannelChanged(messageDTO, activityType);
        if (messageDTO.getActivityMessage() != null) {
            conversationActivityService.persistActivityForChannel(messageDTO);
        }

    }

    @Override
    public void addActivityIfChannelChanged(MessageDTO messageDTO, ActivityType activityType) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        ActivityDto activityDto = null;
//        if (ActivityType.SECURE_CHAT_INITIATED == activityType) {
//            log.info("generating secure messaging initiated activity");
//            activityDto = ActivityDto.builder().mcId(messengerContact.getId())
//                    .created(new Date(new Date().getTime() - 1000))
//                    .actorId(messengerContact.getCustomerId()).activityType(ActivityType.SECURE_CHAT_INITIATED)
//                    .from(messengerContact.getId())
//                    .to(messengerContact.getBusinessId()).accountId(businessDTO.getAccountId())
//                    .source(messageDTO.getSource()).build();
//
//        } else {
            LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
            String lastChannel = lastMessageMetadataPOJO.getLastMessageChannel();
            log.info("[addActivityIfChannelChanged] channel present {}", lastChannel);
            if (ActivityType.SECURE_CHAT_INITIATED == activityType || ActivityType.SECURE_CHAT_ENDED == activityType
                    || lastChannel == null
                    || !MessageDocument.Channel.SECURE_MESSAGE.name().equals(lastChannel)) {
                activityDto = ActivityDto.builder().mcId(messengerContact.getId())
                        .created(new Date())
                        .actorId(messengerContact.getCustomerId()).activityType(activityType)
                        .from(messengerContact.getId())
                        .to(messengerContact.getBusinessId()).accountId(businessDTO.getAccountId())
                        .source(Source.SECURE_MESSAGE.getSourceId()).build();
            }
        messageDTO.setActivityMessage(activityDto);
        log.info("[addActivityIfChannelChanged] Activity added {}", messageDTO.getActivityMessage());
    }

    @Override
    public GetSecureMessagesResponse getSecureMessagesForContact(Integer mcId, Integer cId, Integer page,
            Integer size) {
        log.info("getSecureMessagesForContact called for mcId : {},cId : {},page : {},size : {}", mcId, cId, page,
                size);
        SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO = secureMessagingAuthDAO
                .getMessengerContactSecureMessagingLinkByMcIdAndCId(mcId, cId);
        if (Objects.isNull(secureMessagingLinkResponseDTO)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_WHILE_DECRYPTION);
        }
        log.info("secureMessagingLinkResponseDTO fetched : {}", secureMessagingLinkResponseDTO);
        /*
         * TO Do - add check for secure messaging enabled for business
         */
        MessengerContact messengerContact = messengerContactService.findMessengerContactByMcIdAndCustomerId(mcId, cId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);

        log.info("messengerContact fetched : {}", messengerContact);
        secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(messengerContact, null, null);

        ElasticData<MessageDocument> messageData = getSecureMessagesDataFromES(mcId, page, size,
                secureMessagingLinkResponseDTO);

        return getSecureMessagesResponse(messageData);
    }

    private ElasticData<MessageDocument> getSecureMessagesDataFromES(Integer mcId, Integer page, Integer size,
            SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO) {
        MessengerFilter esQueryData = new MessengerFilter();
        esQueryData.setConversationId(mcId);
        esQueryData.setStartIndex(page * size);
        esQueryData.setQueryFile(Constants.Elastic.GET_SECURE_MESSAGES);
        esQueryData.setCount(size);
        esQueryData.setAccountId(secureMessagingLinkResponseDTO.getAccountId());
        Map<String, Object> params = new HashMap<>(1);
        params.put("bId", secureMessagingLinkResponseDTO.getBusinessId());
        esQueryData.setParams(params);
        return messengerContactService.getMessageData(esQueryData);
    }

    private GetSecureMessagesResponse getSecureMessagesResponse(ElasticData<MessageDocument> messageData) {
        if (!messageData.isSucceeded()) {
            return null;
        }
        List<MessageDocument> secureMessageDocuments = messageData.getResults();
        Long total = messageData.getTotal();
        log.info("secureMessageDocuments fetched from ES  size: {} and total : {}", secureMessageDocuments.size(),
                total);
        List<SecureMessageResponse> secureMessageResponses = CollectionUtils.isNotEmpty(secureMessageDocuments)
                ? secureMessageDocuments.stream()
                        .map(SecureMessageResponse::new).collect(Collectors.toList())
                : new ArrayList<>(0);
        log.info("secureMessageResponses size: {}", secureMessageResponses.size());
        return new GetSecureMessagesResponse(secureMessageResponses, total);
    }

    @Async
    @Override
    public void generateSecureMessagingJoinedActivity(MessengerContact messengerContact) {
        log.info("generateSecureMessagingJoinedActivity for messengerContact : {}", messengerContact);
        BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
        if (checkIfLastActivityIsSecureMessagingJoinedActivity(businessDTO, messengerContact.getId()))
            return;
        SendMessageDTO sendMessageDTO = new SendMessageDTO();
        sendMessageDTO.setMessengerContact(messengerContact);
        sendMessageDTO.setBusinessDTO(businessDTO);
        publishSecureMessagingActivity(sendMessageDTO, ActivityType.SECURE_CHAT_INITIATED);
    }

    @Override
    @Async
    public void generateSecureMessagingEndActivity(MessengerContact messengerContact) {
        log.info("generateSecureMessagingEndActivity called ");
        if (!messengerContact.getBlocked()) {
            BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
            SendMessageDTO sendMessageDTO = new SendMessageDTO();
            sendMessageDTO.setMessengerContact(messengerContact);
            sendMessageDTO.setBusinessDTO(businessDTO);
            publishSecureMessagingActivity(sendMessageDTO, ActivityType.SECURE_CHAT_ENDED);
        }
    }

    @SuppressWarnings("rawtypes")
    private boolean checkIfLastActivityIsSecureMessagingJoinedActivity(BusinessDTO businessDTO, Integer mcId) {
        log.info("checkIfLastActivityIsSecureMessagingJoinedActivity with mcId : {}", mcId);
        MessengerFilter esQueryData = new MessengerFilter();
        esQueryData.setConversationId(mcId);
        esQueryData.setStartIndex(0);
        esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
        esQueryData.setCount(1);
        esQueryData.setAccountId(businessDTO.getRoutingId());

        ElasticData messageData = messengerContactService.getMessageData(esQueryData);
        if (!messageData.isSucceeded())
            return false;
        List<MessageDocument> messageDocuments = messageData.getResults();

        // get last message and check if its already initiated activity
        if (CollectionUtils.isNotEmpty(messageDocuments)) {
            MessageDocument messageDocument = messageDocuments.get(0);
            if (Objects.nonNull(messageDocument) && messageDocument.getMessageType() == MessageType.ACTIVITY
                    && messageDocument.getActivityType() == ActivityType.SECURE_CHAT_INITIATED) {
                log.info("last message is secure activity inititiated");
                return true;
            }
        }
        log.info("last message is not secure activity inititiated");
        return false;
    }
}
