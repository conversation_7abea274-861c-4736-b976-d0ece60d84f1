/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.CustomerDTO;

/**
 * <AUTHOR>
 *
 */
public interface SecureMessagingCommonService {

    void checkIfMessengerContactOrCustomerBlocked(MessengerContact messengerContact, CustomerDTO customerDTO,String type);

    String generateUUIDString();

    void deleteOTPAndSessionToken(Integer secureMessagingLinkId, String phoneNumber, Integer otpId, Integer mcId,
            Integer cId, String sessionToken);

    void validateMessengerContact(MessengerContact messengerContact);
    
    void deleteAllEntitiesForSecureMessaging(Integer mcId);

}
