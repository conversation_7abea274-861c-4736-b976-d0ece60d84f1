/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import org.hashids.Hashids;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.service.secure.messaging.SlugService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SlugServiceImpl implements SlugService {

    private static final Hashids SECURE_MESSAGE_SLUG_GENERATOR = new Hashids(
            Constants.SECURE_MESSAGING_LINK_ENCRYPTION_KEY, 4);

    @Override
    public String generateSlug(Integer length, Integer id) {
        log.info("generateSlug called with length : {},id : {}", length,id);
        String slug = SECURE_MESSAGE_SLUG_GENERATOR.encode((long) id);
        log.info("slug generated successfully : {}", slug);
        return slug;
    }

    @Override
    public Integer decodeSlug(String slug) {
        log.info("decodeSlug called : {}", slug);
        long ids[] = SECURE_MESSAGE_SLUG_GENERATOR.decode(slug);
        log.info("slug decoded mcId and cId: {}", ids);
        return (int) ids[0];
    }

}
