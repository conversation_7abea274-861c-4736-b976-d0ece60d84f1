/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import java.math.BigInteger;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import jakarta.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessageOTP;
import com.birdeye.messenger.dao.repository.SecureMessageOTPRepository;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.service.secure.messaging.OTPService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class OTPServiceImpl implements OTPService {

    @Autowired
    private SecureMessageOTPRepository secureMessageOTPRepository;

    @Autowired
    @Lazy
    private OTPService otpService;

    @Lazy
    @Autowired
    private SecureMessagingCommonService secureMessagingCommonService;

    @Override
    public String generateOtp(Integer length) {
        log.info("generateOtp called with length : {}", length);
        // StringBuilder generatedOTP = new StringBuilder();
        // SecureRandom secureRandom = new SecureRandom();
        // try {
        // secureRandom = SecureRandom.getInstance(secureRandom.getAlgorithm());
        // for (int i = 0; i < length; i++) {
        // generatedOTP.append(secureRandom.nextInt(9));
        // }
        // } catch (NoSuchAlgorithmException e) {
        // log.error("error occurred in generating otp : {}", e.getMessage());
        // throw new
        // SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_GENERATION_ERROR);
        // }
        try {
            String UUIDString = secureMessagingCommonService.generateUUIDString();
            String generateUUIDNo = String.format("%010d",
                    new BigInteger(UUIDString.replace("-", ""), 16));
            String otp = generateUUIDNo.substring(generateUUIDNo.length() - length);
            log.info("otp generated successfully : {}", otp);
            return otp;
        } catch (Exception e) {
            log.error("error occurred in generating otp : {}", e.getMessage());
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_GENERATION_ERROR);
        }
    }

    @Override
    @Transactional
    @CachePut(cacheNames = Constants.SECURE_MESSAGING_OTP_CACHE, key = "#secureMessageOTP.secureMessagingLinkId+'-'+#phoneNumber", unless = "#result == null")
    public SecureMessageOTP saveSecureMessageOTP(SecureMessageOTP secureMessageOTP, String phoneNumber) {
        try {
            secureMessageOTPRepository.save(secureMessageOTP);
            log.info("saveSecureMessageOTP saved successfully : {}", secureMessageOTP);
            return secureMessageOTP;
        } catch (Exception e) {
            log.error("error saving otp : {}", e.getMessage());
            return updateExistingSecureMessageOTP(secureMessageOTP);
        }
    }

    @Override
    public void checkIfOTPAlreadyExists(Integer secureMessagingLinkId, String phoneNumber) {
        log.info("checkIfOTPAlreadyExists called with secureMessagingLinkId : {},phoneNumber : {}",
                secureMessagingLinkId, phoneNumber);
        SecureMessageOTP secureMessageOtp = otpService.getExistingSecureMessageOTP(secureMessagingLinkId, phoneNumber);
        log.info("checkIfOTPAlreadyExists secureMessageOtp fetched : {}", secureMessageOtp);
        if (Objects.nonNull(secureMessageOtp)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_REQUEST_IN_PROCESS);
        }
    }

    @Override
    public Integer verifyOTP(String otp, Integer secureMessagingLinkId, String phoneNumber, Date apiCallDate) {
        log.info("verifyOTP called with otp : {},secureMessagingLinkId : {} and phoneNumber : {}", otp,
                secureMessagingLinkId, phoneNumber);
        SecureMessageOTP secureMessageOTP = getSecureMessageOTP(secureMessagingLinkId, phoneNumber, otp);
        if (!otp.equals(secureMessageOTP.getOtp())) {
            log.error("verify otp failed due to Invalid otp : {}, correct otp : {}", otp, secureMessageOTP.getOtp());
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_NOT_VALID);
        }
        Date otpExpiryTime = secureMessageOTP.getExpiryTime();
        if (!apiCallDate.before(otpExpiryTime)) {
            log.error("verifyOTP failed fue to expired on : {}", otpExpiryTime);
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_EXPIRED_OTP);
        }
        log.info("verifyOTP successfully");
        return secureMessageOTP.getId();
    }

    private SecureMessageOTP getSecureMessageOTP(Integer secureMessagingLinkId, String phoneNumber, String otp) {
        log.info("getSecureMessageOTP called with secureMessagingLinkId : {} and phoneNumber : {}",
                secureMessagingLinkId);
        SecureMessageOTP secureMessageExistingOtp = otpService.getExistingSecureMessageOTP(secureMessagingLinkId,
                phoneNumber);
        if (Objects.nonNull(secureMessageExistingOtp)) {
            log.info("secureMessageExistingOtp present : {}", secureMessageExistingOtp);
            return secureMessageExistingOtp;
        } else {
            Optional<SecureMessageOTP> secureMessageOTP = secureMessageOTPRepository
                    .findSecureMessageOTPBySecureMessagingLinkId(secureMessagingLinkId, otp);
            if (!secureMessageOTP.isPresent()) {
                log.error("Invalid otp sent : {} for secureMessagingLinkId : {}", otp, secureMessagingLinkId);
                throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_NOT_VALID);
            }
            return secureMessageOTP.get();
        }
    }

    @Override
    @Cacheable(cacheNames = Constants.SECURE_MESSAGING_OTP_CACHE, key = "#secureMessagingLinkId+'-'+#phoneNumber", unless = "#result == null")
    public SecureMessageOTP getExistingSecureMessageOTP(Integer secureMessagingLinkId, String phoneNumber) {
        return null;
    }

    @Override
    @CacheEvict(cacheNames = Constants.SECURE_MESSAGING_OTP_CACHE, key = "#secureMessagingLinkId +'-'+ #phoneNumber")
    public void deleteOTP(Integer secureMessagingLinkId, String phoneNumber, Integer otpId) {
        log.info("deleteOTP called with secureMessagingLinkId : {},phoneNumber : {} ,otpId : {}", secureMessagingLinkId,
                phoneNumber, otpId);
        secureMessageOTPRepository.deleteById(otpId);
        log.info("otp deleted succeessully : {}", otpId);
    }

    private SecureMessageOTP updateExistingSecureMessageOTP(SecureMessageOTP secureMessageOTP) {
        Integer secureMessagingLinkId = secureMessageOTP.getSecureMessagingLinkId();
        String otp = secureMessageOTP.getOtp();
        Optional<SecureMessageOTP> secureMessageExistingOTP = secureMessageOTPRepository
                .findSecureMessageOTPBySecureMessagingLinkId(secureMessagingLinkId, otp);
        if (secureMessageExistingOTP.isPresent()) {
            SecureMessageOTP secureMessageOTPOpt = secureMessageExistingOTP.get();
            log.info("secureMessageExistingOTP present : {}", secureMessageOTPOpt);
            Date createdDate = new Date();
            Date expiryTime = DateUtils.addTimeToDate(createdDate, Constants.SECURE_MESSAGING_OTP_EXPIRY_SEC,
                    Calendar.SECOND);
            secureMessageOTPOpt.setCreatedAt(createdDate);
            secureMessageOTPOpt.setExpiryTime(expiryTime);
            secureMessageOTPRepository.save(secureMessageOTPOpt);
            log.info("updated secureMessageExistingOTP successfully");
            return secureMessageOTPOpt;
        }
        throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_GENERATION_ERROR);
    }
}

    // @Override
    // public void updateExpiryTimeForPreviousOTPs(Integer secureMessagingLinkId,
    // Date pastDate) {
    //
    // }

