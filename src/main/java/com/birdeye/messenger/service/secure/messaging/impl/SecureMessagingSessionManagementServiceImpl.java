/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingSessionToken;
import com.birdeye.messenger.dao.repository.SecureMessagingSessionTokenRepository;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingSessionManagementService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class SecureMessagingSessionManagementServiceImpl implements SecureMessagingSessionManagementService {


    @Autowired
    private SecureMessagingSessionTokenRepository secureMessagingSessionTokenRepository;

    @Autowired
    private RedisHandler redisHandler;

    @Autowired
    private MessengerContactService messengerContactService;

    @Autowired
    @Lazy
    private SecureMessagingCommonService secureMessagingCommonService;

    @Autowired
    private SecureMessagingMessageService secureMessagingMessageService;

    @Override
    @CachePut(cacheNames = Constants.SECURE_MESSAGING_SESSION_TOKENS_CACHE, key = "#sessionToken", unless = "#result == null")
    public SecureMessagingSessionToken generateSecureMessagingSessionTokenForMcIdAndCId(Integer mcId, Integer cId,
            String sessionToken) {
        log.info("generateSecureMessagingSessionTokenForMcIdAndCId called for mcId : {}, cId : {}", mcId, cId);
        try {
            SecureMessagingSessionToken secureMessagingSessionToken = new SecureMessagingSessionToken(mcId, cId,
                    sessionToken);
            secureMessagingSessionTokenRepository.save(secureMessagingSessionToken);
            log.info(
                    "generateSecureMessagingSessionTokenForMcIdAndCId secureMessagingSessionToken saved successfully : {}",
                    secureMessagingSessionToken);

            return secureMessagingSessionToken;
        } catch (Exception e) {
            log.error("generateSecureMessagingSessionTokenForMcIdAndCId error occured : {}", e.getMessage());
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_WHILE_GENERATING_TOKEN);
        }
    }

    @Override
    @Cacheable(cacheNames = Constants.SECURE_MESSAGING_SESSION_TOKENS_CACHE, key = "#sessionToken", unless = "#result == null")
    public SecureMessagingSessionToken verifySecureMessagingSessionToken(String sessionToken) {
        log.info("verifySecureMessagingSessionToken called with sessionToken : {}", sessionToken);
        Optional<SecureMessagingSessionToken> secureMessagingSessionTokenOptional = secureMessagingSessionTokenRepository
                .getSecureMessagingSessionTokenBySessionToken(sessionToken);
        if (!secureMessagingSessionTokenOptional.isPresent()) {
            log.error("verifySecureMessagingSessionToken invalid session token");
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_INVALID_SESSION_TOKEN);
        }
        SecureMessagingSessionToken secureMessagingSessionToken = secureMessagingSessionTokenOptional.get();
        log.info("verifySecureMessagingSessionToken session token fetched from db : {}", secureMessagingSessionToken);
        return secureMessagingSessionToken;
    }

    @Override
    public void deleteSessionTokens(Integer mcId, Integer cId, String sessionToken) {
        log.info("deleteSessionTokens called with mcId : {}, cId : {},sessionToken : {}", mcId, cId, sessionToken);
        List<String> sessionTokens = secureMessagingSessionTokenRepository
                .getSecureMessagingSessionTokenByMcIdAndCId(mcId, cId, sessionToken).stream()
                .map(SecureMessagingSessionToken::getSessionToken).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sessionTokens)) {
            secureMessagingSessionTokenRepository.deleteSessionTokens(sessionTokens);
            sessionTokens = sessionTokens.stream()
                    .map(st -> Constants.SECURE_MESSAGING_SESSION_TOKENS_CACHE + "::" + st)
                    .collect(Collectors.toList());
            redisHandler.evictWebChatCache(sessionTokens);
            log.info("session tokens deleted from cache and db successfully : {}", sessionTokens.size());
        }

    }

    @Override
    @CacheEvict(cacheNames = Constants.SECURE_MESSAGING_SESSION_TOKENS_CACHE, key = "#sessionToken")
    public Void logOut(Integer mcId, Integer cId, String sessionToken, Integer secureTokenId) {
        log.info("logOut called with sessionToken : {}, secureTokenId : {}", sessionToken, secureTokenId);
        MessengerContact messengerContact = messengerContactService.findMessengerContactByMcIdAndCustomerId(mcId, cId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);

        log.info("messengerContact fetched for id : {}", messengerContact.getId());

        secureMessagingMessageService.generateSecureMessagingEndActivity(messengerContact);

        secureMessagingSessionTokenRepository.deleteById(secureTokenId);
        log.info("customer logout successfully");
        return null;
    }

}