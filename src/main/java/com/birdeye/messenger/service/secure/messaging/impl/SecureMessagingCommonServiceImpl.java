/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import jakarta.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.birdeye.messenger.dao.repository.SecureMessageOTPRepository;
import com.birdeye.messenger.dao.repository.SecureMessageRepository;
import com.birdeye.messenger.dao.repository.SecureMessagingLinkRepository;
import com.birdeye.messenger.dao.repository.SecureMessagingSessionTokenRepository;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.service.secure.messaging.OTPService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingSessionManagementService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class SecureMessagingCommonServiceImpl implements SecureMessagingCommonService {

    @Lazy
    @Autowired
    private OTPService otpService;

    @Autowired
    @Lazy
    private SecureMessagingSessionManagementService secureMessagingSessionManagementService;
    
    @Autowired
    private SecureMessageRepository secureMessageRepository;
    
    @Autowired
    private SecureMessageOTPRepository secureMessageOTPRepository;
    
    @Autowired
    private SecureMessagingLinkRepository secureMessagingLinkRepository;
    
    @Autowired
    private SecureMessagingSessionTokenRepository secureMessagingSessionTokenRepository;

    @Override
    public void checkIfMessengerContactOrCustomerBlocked(MessengerContact messengerContact, CustomerDTO customerDTO,String type) {
        if ((Objects.nonNull(messengerContact) && Boolean.TRUE.equals(messengerContact.getBlocked()))
                || (Objects.nonNull(customerDTO) && Boolean.TRUE.equals(customerDTO.getBlocked()))) {
            log.error("secure messaging contact is blocked");
            throw new SecureMessagingException(
                    "otp".equals(type) ? ErrorCode.SECURE_MESSAGING_CONTACT_BLOCKED : ErrorCode.CONTACT_IS_BLOCKED);
        }
    }

    @Override
    public String generateUUIDString() {
        return UUID.randomUUID().toString();
    }

    @Override
    @Async
    @Transactional
    public void deleteOTPAndSessionToken(Integer secureMessagingLinkId, String phoneNumber, Integer otpId, Integer mcId,
            Integer cId, String sessionToken) {
        log.info("deleteOTPAndSessionToken called");
        otpService.deleteOTP(secureMessagingLinkId, phoneNumber, otpId);
        secureMessagingSessionManagementService.deleteSessionTokens(mcId, cId, sessionToken);
        log.info("secure messaging otp and session tokens deleted successfully");
    }

    @Override
    public void validateMessengerContact(MessengerContact messengerContact) {
        if (Objects.isNull(messengerContact)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_CONTACT_DELETED);
        }
    }

    @Override
    @Transactional
    public void deleteAllEntitiesForSecureMessaging(Integer mcId) {
        log.info("deleteAllEntitiesForSecureMessaging called with mcId : {}", mcId);
        try {
            secureMessageRepository.deleteByMcId(mcId);
            secureMessagingSessionTokenRepository.deleteByMCId(mcId);
            List<SecureMessagingLink> secureMessagingLinks = secureMessagingLinkRepository.deleteBymcId(mcId);
            if (CollectionUtils.isNotEmpty(secureMessagingLinks)) {
                secureMessageOTPRepository.deleteOtpByLinkId(secureMessagingLinks.get(0).getId());
            }
        } catch (Exception e) {
            log.error("error : {} occurred in deleteAllEntitiesForSecureMessaging", e.getMessage());
        }

    }

}
