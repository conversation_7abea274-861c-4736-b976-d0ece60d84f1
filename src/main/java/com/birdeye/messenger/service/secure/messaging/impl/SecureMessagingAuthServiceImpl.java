/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging.impl;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessageOTP;
import com.birdeye.messenger.dao.secure.messaging.SecureMessagingAuthDAO;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationWrapperMessage;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendConversationRequest;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkGenerationRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkInfoResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkDTO;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingLinkResponseDTO;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingRequestOTP;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.impl.SMSSendEventHandler;
import com.birdeye.messenger.service.secure.messaging.OTPService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingAuthService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingSessionManagementService;
import com.birdeye.messenger.service.secure.messaging.SlugService;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.PhoneNoValidator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SecureMessagingAuthServiceImpl implements SecureMessagingAuthService {

    @Value("${secure.messaging.channels}")
    private List<Integer> secureMessagingChannels;

    @Value("${secure.message.base.url}")
    private String secureChatBaseUrl;

    private final MessengerContactService messengerContactService;

    private final BusinessService businessService;

    private final ContactService contactService;

    private final SecureMessagingAuthDAO secureMessagingDAO;

    private final SMSSendEventHandler smsSendEventHandler;

    private final OTPService otpService;

    private final CommonService commonService;

    private final KafkaService kafkaService;

    private final SmsService smsService;

    private final SecureMessagingCommonService secureMessagingCommonService;

    private final SecureMessagingSessionManagementService secureMessagingSessionManagementService;

    @Autowired
    private SecureMessagingMessageService secureMessagingMessageService;
    
    @Autowired
    private SlugService slugService;


    /*
     * Service handler for generating secure messaging link
     */
    @Override
    public MessageResponse generateSecureMessagingLink(SecureLinkGenerationRequest secureLinkGenerationRequest)
            throws Exception {
        log.info("generateSecureMessagingLink called with request : {}", secureLinkGenerationRequest);
        validateSecureLinkGenerationRequest(secureLinkGenerationRequest);

        Integer sourceId = secureLinkGenerationRequest.getSource();
        Integer mcId = secureLinkGenerationRequest.getMcId();

        MessengerContact messengerContact = messengerContactService.findById(mcId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);

        Integer cId = messengerContact.getCustomerId();
        Integer bId = messengerContact.getBusinessId();
        Integer userId = secureLinkGenerationRequest.getUserId();

        SecureMessagingLinkResponseDTO messagingLinkResponseDTO = secureMessagingDAO
                .getMessengerContactSecureMessagingLinkByMcIdAndCId(mcId, cId);

        CustomerDTO customerDTO = contactService.findByIdNoCaching(cId);
        secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(messengerContact, customerDTO,null);
        BusinessDTO businessDTO = businessService.getBusinessDTO(bId);
        boolean sentThroughAddAndSend = secureLinkGenerationRequest.isSentThroughAddAndSend();
        if (Objects.nonNull(messagingLinkResponseDTO)) {
            log.info("messagingLinkResponseDTO : {} fetched for messengerContact : {} and customer : {}",
                    messagingLinkResponseDTO, messengerContact, customerDTO);
            String encryptedSecureKey = slugService.generateSlug(4,messagingLinkResponseDTO.getId());
            return sendSecureMessageLinkToCustomer(messagingLinkResponseDTO, customerDTO, businessDTO, sourceId, userId,
                    messengerContact, sentThroughAddAndSend,encryptedSecureKey);
        }
        String customerPhone = StringUtils.isNotBlank(customerDTO.getPhone()) ? customerDTO.getPhone()
                : customerDTO.getPhoneE164();
        if (StringUtils.isBlank(customerPhone)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_PHONE_NUMBER);
        }

        SecureMessagingLinkDTO messengerContactSecureMessagingLinkDTO = new SecureMessagingLinkDTO(secureLinkGenerationRequest, messengerContact);
        messagingLinkResponseDTO = secureMessagingDAO.saveMessengerContactSecureMessagingLink(mcId, cId,
                messengerContactSecureMessagingLinkDTO);
        String encryptedSecureKey = slugService.generateSlug(4,messagingLinkResponseDTO.getId());

        return sendSecureMessageLinkToCustomer(messagingLinkResponseDTO, customerDTO, businessDTO, sourceId, userId,
                messengerContact, sentThroughAddAndSend,encryptedSecureKey);

    }

    private void validateSecureLinkGenerationRequest(SecureLinkGenerationRequest secureLinkGenerationRequest) {
        Integer sourceId = secureLinkGenerationRequest.getSource();
        if (!secureMessagingChannels.contains(sourceId)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_SOURCE_NOT_SUPPORTED,
                    String.format(ErrorCode.SECURE_MESSAGING_SOURCE_NOT_SUPPORTED.getErrorMessage(), sourceId));
        }
    }

    /*
     * Send secure message link via sms to the customer
     */
    private MessageResponse sendSecureMessageLinkToCustomer(
            SecureMessagingLinkResponseDTO messagingLinkResponseDTO, CustomerDTO customerDTO,
            BusinessDTO businessDTO, Integer sourceId, Integer userId, MessengerContact messengerContact,
            boolean sentThroughAddAndSend,String secureKey)
            throws Exception {
        log.info(
                "sendSecureMessageLinkToCustomer called with messagingLinkResponseDTO : {},customerDTO : {},businessDTO : {},sourceId : {},userId : {},messengerContact : {}",
                messagingLinkResponseDTO, customerDTO, businessDTO, sourceId, userId, messengerContact);
        String secureLink = secureChatBaseUrl + secureKey;
        SendMessageDTO sendMessageDTO = new SendMessageDTO();
        Integer customerId = customerDTO.getId();
        String bId = String.valueOf(businessDTO.getBusinessId());
        //String customerName = customerDTO.getDisplayName();
        customerDTO.setLastName(null);
        String customerName = MessengerUtil.buildCustomerName(customerDTO);
        if (StringUtils.isBlank(customerName)) {
            customerName = customerDTO.getDisplayName();
        }
        String businessName = businessDTO.getBusinessName();
        //sendMessageDTO
          //      .setBody(String.format(Constants.SECURE_LINK_DEFAULT_BODY, customerName, businessName, secureLink));
        String enterpriseName = businessDTO.getEnterpriseName();
        if (StringUtils.isNotBlank(enterpriseName)) {
             enterpriseName = enterpriseName + ", " + businessName;
         }else {
         	enterpriseName=businessName;
        }
        sendMessageDTO.setBody(String.format(Constants.SECURE_LINK_DEFAULT_BODY, customerName, enterpriseName, secureLink));
        sendMessageDTO.setBusinessDTO(businessDTO);
        sendMessageDTO.setBusinessIdentifierId(bId);
        sendMessageDTO.setFromBusinessId(Integer.valueOf(bId));
        sendMessageDTO.setToCustomerId(String.valueOf(customerId));
        sendMessageDTO.setCustomerDTO(customerDTO);
        sendMessageDTO.setMessengerContact(messengerContact);
        sendMessageDTO.setUserId(userId);
        sendMessageDTO.setSource(sourceId);
        sendMessageDTO.setCustomerId(customerId);
        sendMessageDTO.setPartOfExistingMessage(sentThroughAddAndSend);
        // if (messagingLinkResponseDTO.isCreated()) {
//        secureMessagingMessageService.publishSecureMessagingActivity(sendMessageDTO,
//                ActivityType.SECURE_CHAT_INITIATED);
//        }
        return smsSendEventHandler.handle(sendMessageDTO);
    }

    @Override
    public SecureLinkInfoResponse getSecureLinkInfoResponse(String secureKey) throws Exception {
        SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO = getSecureMessagingLinkResponseDTOFromSecureKey(
                secureKey);

        Integer mcId = secureMessagingLinkResponseDTO.getMcId();
        Integer cId = secureMessagingLinkResponseDTO.getCId();

        MessengerContact messengerContact = messengerContactService.findById(mcId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);

        CustomerDTO customerDTO = contactService.findByIdNoCaching(cId);

        Integer bId = secureMessagingLinkResponseDTO.getBusinessId();

        Map<String, Object> businessProfileMap = businessService.getBusinessData(bId);
        String businessName = (String) businessProfileMap.get("name");
        String logoUrl = (String) businessProfileMap.get("logoUrl");
        if (StringUtils.isBlank(logoUrl) || StringUtils.isBlank(businessName)) {
            Integer accountId = secureMessagingLinkResponseDTO.getAccountId();
            Map<String, Object> enterpriseProfileMap = businessService.getBusinessData(accountId);
            logoUrl = StringUtils.isBlank(logoUrl) ? (String) enterpriseProfileMap.get("logoUrl") : logoUrl;
            businessName = StringUtils.isBlank(businessName) ? (String) enterpriseProfileMap.get("name") : businessName;
        }
        String customerPhone = getMaskedPhoneNumber(customerDTO.getPhoneE164());
        log.info("getSecureLinkInfoResponse : {}", secureMessagingLinkResponseDTO);
        return new SecureLinkInfoResponse(businessName, logoUrl, customerPhone);
    }

    @Override
    public Void requestOTP(SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception {
        requestAndResendOTPCommon(secureMessagingRequestOTP, true);
        return null;
    }

    private SecureMessagingLinkResponseDTO getSecureMessagingLinkResponseDTOFromSecureKey(String secureKey)
            throws Exception {
        try {
            Integer id = slugService.decodeSlug(secureKey);

        log.info("getSecureMessagingLinkResponseDTOFromSecureKey called for id : {}", id);
        SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO = secureMessagingDAO
                .getMessengerContactSecureMessagingLinkById(id);

        if (Objects.isNull(secureMessagingLinkResponseDTO)) {
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_WHILE_DECRYPTION);
        }
        return secureMessagingLinkResponseDTO;
    } catch (Exception e) {
        log.error("error in getSecureMessagingLinkResponseDTOFromSecureKey : {}", e.getMessage());
        throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_WHILE_DECRYPTION);
    }
    }

    private void sendOTPToCustomer(String otp, BusinessDTO businessDTO, CustomerDTO customerDTO, String otpId) {
        ConversationWrapperMessage conversationKafkaWrapper = new ConversationWrapperMessage();
        String businessName = businessDTO.getBusinessName();
        conversationKafkaWrapper.setBody(String.format(Constants.SECURE_MESSAGING_OTP_MESSAGE_BODY, otp,
                StringUtils.isNotBlank(businessName) ? businessName : businessDTO.getEnterpriseName(),
                Constants.SECURE_MESSAGING_OTP_EXPIRY_SEC));
        conversationKafkaWrapper.setFromBusinessId(businessDTO.getBusinessId().toString());
        conversationKafkaWrapper.setToCustomerId(customerDTO.getId().toString());
        conversationKafkaWrapper.setType("secure-messaging");
        conversationKafkaWrapper.setSubType("otp-request");
        conversationKafkaWrapper.setToPhone(customerDTO.getPhoneE164());
        conversationKafkaWrapper.setFromPhone(businessDTO.getSmsPhoneNumber());
        conversationKafkaWrapper.setExternalUID(otpId);
        conversationKafkaWrapper.setAccountId(businessDTO.getAccountId().toString());

        SendConversationRequest request = commonService.getSendSMSRequestForMessenger(conversationKafkaWrapper,
                      null);
        request.setCountryCode(customerDTO.getCountryCode());
        request.setParams(Collections.singletonMap("rType", "otp"));
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.SMS_OUTBOUND_OTP_NEXUS, request);
        log.info("otp request published to kafka successfully with request : {}", request);
    }

    @Override
    public Void resendOTP(SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception {
        requestAndResendOTPCommon(secureMessagingRequestOTP, true);
        return null;
    }

    private void requestAndResendOTPCommon(SecureMessagingRequestOTP secureMessagingRequestOTP,
            boolean checkIfOTPAlreadyPresent) throws Exception {
        String secureKey = secureMessagingRequestOTP.getSecureKey();
        SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO = getSecureMessagingLinkResponseDTOFromSecureKey(
                secureKey);

        Integer mcId = secureMessagingLinkResponseDTO.getMcId();
        Integer cId = secureMessagingLinkResponseDTO.getCId();

        MessengerContact messengerContact = messengerContactService.findById(mcId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);

        CustomerDTO customerDTO = contactService.findByIdNoCaching(cId);
        secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(messengerContact, customerDTO,"otp");
        String customerPhoneNumber = secureMessagingRequestOTP.getCustomerPhoneNumber();
        validatePhoneNumber(customerPhoneNumber,customerDTO.getPhoneE164());
        
        if (checkIfOTPAlreadyPresent) {
            Integer secureMessagingLinkId = secureMessagingLinkResponseDTO.getId();
            otpService.checkIfOTPAlreadyExists(secureMessagingLinkId, customerPhoneNumber);
        }

        generateAndSendOTPToCustomer(mcId, cId, secureKey, secureMessagingLinkResponseDTO, customerPhoneNumber,
                customerDTO);

    }


    private SecureMessageOTP generateSecureMessageOTPEntity(
            SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO, String otp) {
        Integer secureMessageLinkId = secureMessagingLinkResponseDTO.getId();
        Date createdDate = new Date();
        Date expiryTime = DateUtils.addTimeToDate(createdDate, Constants.SECURE_MESSAGING_OTP_EXPIRY_SEC,
                Calendar.SECOND);
        SecureMessageOTP secureMessageOTP = new SecureMessageOTP(otp, createdDate, expiryTime, secureMessageLinkId);
        return secureMessageOTP;
    }

    private void generateAndSendOTPToCustomer(Integer mcId, Integer cId, String secureKey,
            SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO, String customerPhoneNumber,
            CustomerDTO customerDTO) {

        log.info("generateAndSendOTPToCustomer called with mcId : {},cId : {}", mcId, cId);
            String otp = otpService.generateOtp(4);
            SecureMessageOTP secureMessageOTP = generateSecureMessageOTPEntity(secureMessagingLinkResponseDTO, otp);

            BusinessDTO businessDTO = businessService.getBusinessDTO(secureMessagingLinkResponseDTO.getBusinessId());

            String fromPhoneNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
            if (StringUtils.isBlank(fromPhoneNumber)) {
                log.error("business texting number not present : {}",businessDTO.getBusinessId());
                throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_OTP_GENERATION_ERROR);
            }
            businessDTO.setSmsPhoneNumber(fromPhoneNumber);

            otpService.saveSecureMessageOTP(secureMessageOTP, customerPhoneNumber);
            
            sendOTPToCustomer(otp, businessDTO, customerDTO, secureMessageOTP.getId().toString());

    }

    @Override
    public SecureMessagingVerifyOTPResponse verifyOTP(SecureMessagingVerifyOTPRequest secureMessagingVerifyOTPRequest)
            throws Exception {
        String secureKey = secureMessagingVerifyOTPRequest.getSecureKey();
        SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO = getSecureMessagingLinkResponseDTOFromSecureKey(
                secureKey);

        Integer mcId = secureMessagingLinkResponseDTO.getMcId();
        Integer cId = secureMessagingLinkResponseDTO.getCId();

        MessengerContact messengerContact = messengerContactService.findById(mcId);
        secureMessagingCommonService.validateMessengerContact(messengerContact);
        CustomerDTO customerDTO = contactService.findByIdNoCaching(cId);
        secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(messengerContact, customerDTO,"otp");

        String customerPhoneNumber = secureMessagingVerifyOTPRequest.getCustomerPhoneNumber();
        validatePhoneNumber(customerPhoneNumber,customerDTO.getPhoneE164());

        String sessionToken = verifyOTPAndGenerateSessionToken(secureMessagingLinkResponseDTO,
                secureMessagingVerifyOTPRequest, customerPhoneNumber, mcId, cId, messengerContact);

        return new SecureMessagingVerifyOTPResponse(sessionToken, mcId, cId);
    }

    private String verifyOTPAndGenerateSessionToken(SecureMessagingLinkResponseDTO secureMessagingLinkResponseDTO,
            SecureMessagingVerifyOTPRequest secureMessagingVerifyOTPRequest, String customerPhoneNumber, Integer mcId,
            Integer cId, MessengerContact messengerContact) {
        Integer secureMessagingLinkId = secureMessagingLinkResponseDTO.getId();
        Integer secureMessageOtpId = otpService.verifyOTP(secureMessagingVerifyOTPRequest.getOtp(),
                secureMessagingLinkId, customerPhoneNumber, secureMessagingVerifyOTPRequest.getApiCallTime());
        if (Objects.isNull(secureMessageOtpId)) {
            log.error("secureMessageOtpId is null");
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_INVALID_OR_EXPIRED_OTP);
        }
        String sessionToken = secureMessagingCommonService.generateUUIDString();
        secureMessagingSessionManagementService.generateSecureMessagingSessionTokenForMcIdAndCId(mcId, cId,
                sessionToken);
        secureMessagingCommonService.deleteOTPAndSessionToken(secureMessagingLinkId, customerPhoneNumber,
                secureMessageOtpId,
                mcId, cId, sessionToken);
        secureMessagingMessageService.generateSecureMessagingJoinedActivity(messengerContact);
        return sessionToken;
    }
    
    private String getMaskedPhoneNumber(String customerPhone) {
        String customerPhoneNumberWithoutCountryCode = PhoneNoValidator.removeCountryCodeFromPhoneNumber(customerPhone);
        if (StringUtils.isBlank(customerPhoneNumberWithoutCountryCode)) {
            log.error("customerPhoneNumberWithoutCountryCode is empty : {}", customerPhone);
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_ERROR_WHILE_DECRYPTION);
        }
        customerPhone = PhoneNoValidator.maskPhoneNumber(customerPhoneNumberWithoutCountryCode,
                Constants.SECURE_MESSAGING_PHONE_MASKING_CHARACTER, Constants.MASKING_LENGTH);
        return customerPhone;
    }
    
    private void validatePhoneNumber(String requestedPhoneNumber,String actualPhoneNumber) {
        String customerPhoneNumber = requestedPhoneNumber;
        String maskedPhoneNumber=getMaskedPhoneNumber(actualPhoneNumber);
        if (!customerPhoneNumber.equals(maskedPhoneNumber)) {
            log.error("requestAndResendOTPCommon failed for masked phone : {},actual phone : {}", customerPhoneNumber,
                    actualPhoneNumber);
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_CONTACT_NUMBER_ERROR);
        }

    }

}
