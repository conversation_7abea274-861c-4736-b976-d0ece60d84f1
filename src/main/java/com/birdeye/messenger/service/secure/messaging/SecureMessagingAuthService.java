/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging;

import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkGenerationRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkInfoResponse;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingRequestOTP;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureMessagingVerifyOTPResponse;

/**
 * <AUTHOR>
 *
 */
public interface SecureMessagingAuthService {

    MessageResponse generateSecureMessagingLink(SecureLinkGenerationRequest secureLinkGenerationRequest)
            throws Exception;

    SecureLinkInfoResponse getSecureLinkInfoResponse(String secureKey) throws Exception;

    Void requestOTP(SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception;

    Void resendOTP(SecureMessagingRequestOTP secureMessagingRequestOTP) throws Exception;

    SecureMessagingVerifyOTPResponse verifyOTP(SecureMessagingVerifyOTPRequest secureMessagingVerifyOTPRequest)
            throws Exception;

}
