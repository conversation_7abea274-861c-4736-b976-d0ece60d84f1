/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingSessionToken;

/**
 * <AUTHOR>
 *
 */
public interface SecureMessagingSessionManagementService {

    SecureMessagingSessionToken generateSecureMessagingSessionTokenForMcIdAndCId(Integer mcId, Integer cId,
            String sessionToken);

    SecureMessagingSessionToken verifySecureMessagingSessionToken(String sessionToken);

    void deleteSessionTokens(Integer mcId, Integer cId, String sessionToken);

    Void logOut(Integer mcId, Integer cId, String sessionToken, Integer secureTokenId);

}
