/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.secure.messaging.GetSecureMessagesResponse;
import com.birdeye.messenger.enums.ActivityType;

/**
 * <AUTHOR>
 *
 */
public interface SecureMessagingMessageService {

    void publishSecureMessagingActivity(MessageDTO messageDTO, ActivityType activityType);

    void addActivityIfChannelChanged(MessageDTO messageDTO, ActivityType activityType);

    GetSecureMessagesResponse getSecureMessagesForContact(Integer mcId, Integer cId, Integer page, Integer size);

    void generateSecureMessagingJoinedActivity(MessengerContact messengerContact);

    void generateSecureMessagingEndActivity(MessengerContact messengerContact);

}
