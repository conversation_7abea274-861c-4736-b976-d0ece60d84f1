/**
 * 
 */
package com.birdeye.messenger.service.secure.messaging;

import java.util.Date;

import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessageOTP;

/**
 * <AUTHOR>
 *
 */

public interface OTPService {

    String generateOtp(Integer length);

    SecureMessageOTP saveSecureMessageOTP(SecureMessageOTP secureMessageOTP, String phoneNumber);

    void checkIfOTPAlreadyExists(Integer secureMessagingLinkId, String phoneNumber);

    Integer verifyOTP(String otp, Integer secureMessagingLinkId, String phoneNumber, Date apiCallTime);

    void deleteOTP(Integer secureMessagingLinkId, String phoneNumber, Integer otpId);

    SecureMessageOTP getExistingSecureMessageOTP(Integer secureMessagingLinkId, String phoneNumber);
}
