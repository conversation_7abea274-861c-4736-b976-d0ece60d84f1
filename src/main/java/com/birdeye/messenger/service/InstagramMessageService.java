package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.InstagramMessageDto;
import com.birdeye.messenger.dto.MessageDTO;

public interface InstagramMessageService {

	 void deleteInstagramMessagesByMcId(Integer mcId);
	 
	 InstagramMessage findByMessageId(String messageId);

	InstagramMessage findById(Integer id);

	void updateInstagramMessageBody(String messageBody,Integer messageId);

	void checkAndPushLikeUnlikeEvent(MessageDTO messageDTO,BusinessDTO businessDTO);

	InstagramMessageDto getInstagramMessageId(Integer messageId);

}
