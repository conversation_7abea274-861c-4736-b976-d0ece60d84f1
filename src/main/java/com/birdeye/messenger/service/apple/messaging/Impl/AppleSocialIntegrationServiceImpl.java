package com.birdeye.messenger.service.apple.messaging.Impl;

import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.SendAppleMessageSocialDto;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreDownloadRequest;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadResponse;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadSocialRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.apple.messaging.AppleSocialIntegrationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppleSocialIntegrationServiceImpl implements AppleSocialIntegrationService {

    @Value("${social.base.url}")
    private String socialBaseUrl;

    @Value("${social.messenger.url}")
    private String socialUrl;

    private final RestTemplate restTemplate;

    @Override
    public void sendMessageToApple(SendAppleMessageSocialDto request) {
        String url = this.socialBaseUrl + "/social/apple/send";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SendAppleMessageSocialDto> httpEntity = new HttpEntity<>(request, headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("sendMessageToApple: failed to send message {} to apple. Error Response received {}", request, response);
            throw new MessengerException("Failed to send message to apple via social-service integration" + request);
        }
        log.info("Send Message to Apple Via Social with request : {} Success with response : {}", request, response);
    }

    @Override
    public Integer getBusinessIdForPageId(String destinationId, String intent,boolean isSMB) {
        String url = this.socialBaseUrl + "/social/apple/businessId?destinationId=" + destinationId+"&intent="+intent+"&isSMB="+isSMB;
        return restTemplate.getForObject(url, Integer.class);
    }

    @Override
	public String getAppleIntegrationStatus(Integer businessId) {
		log.info("getAppleIntegrationStatus: for businessId {}", businessId);
		String url = this.socialBaseUrl + "/social/apple/integration/status?businessId=" + businessId;
		return restTemplate.getForObject(url, String.class);
	}

	@Override
	public String getApplePageIdByBusinessId(Integer businessId, Long enterpriseNumber, boolean isSMB) {

		String pageId = null;
		String url = this.socialBaseUrl + "/social/apple/pageId?businessId=" + businessId + "&enterpriseNumber="
				+ enterpriseNumber + "&isSMB=" + isSMB;
		pageId = restTemplate.getForObject(url, String.class);
		if (StringUtils.isBlank(pageId)) {
			throw new NotFoundException(new ErrorMessageBuilder(ErrorCode.APPLE_PAGE_NOT_FOUND, HttpStatus.NOT_FOUND)
					.message("No Apple page found for business - {}", businessId));
		}

		return pageId;

	}

	@Override
	public String getApplePreDownloadUrl(AttachmentPreDownloadRequest request) {
		String url = this.socialBaseUrl + "/social/apple/preDownload";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<AttachmentPreDownloadRequest> httpEntity = new HttpEntity<>(request, headers);
		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
		if (!response.getStatusCode().is2xxSuccessful()) {
			log.error("getApplePreDownloadUrl: preDownload failed for rquest {} . Error Response received {}", request,
					response);
			throw new MessengerException("PreDowload failed for rquest");
		}
		return response.getBody();
	}

	@Override
	public AttachmentPreUploadResponse getPreUploadDataFromSocial(
			AttachmentPreUploadSocialRequest attachmentPreUploadSocialRequest) {
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<AttachmentPreUploadSocialRequest> request = new HttpEntity<>(attachmentPreUploadSocialRequest,
				headers);
		String url = this.socialBaseUrl + "/social/apple/preUpload";
		ResponseEntity<AttachmentPreUploadResponse> response = null;
		try {
			log.info("getPreUploadDataFromSocial called  with request : {}", attachmentPreUploadSocialRequest);
			response = restTemplate.exchange(url, HttpMethod.POST, request, AttachmentPreUploadResponse.class);
			log.info("Pre upload data fetched successfully for request : {}", request);
			return response.getBody() != null ? response.getBody() : null;
		} catch (Exception e) {
			log.error("Error occured while fetch pre upload data from social : {},request: {}", e, request);
			throw new MessengerException("PreUpload failed for rquest");
		}

	}
}
