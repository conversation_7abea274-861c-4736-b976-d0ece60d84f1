package com.birdeye.messenger.service.apple.messaging;

import com.birdeye.messenger.dto.SendAppleMessageSocialDto;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreDownloadRequest;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadResponse;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadSocialRequest;

public interface AppleSocialIntegrationService {

    void sendMessageToApple(SendAppleMessageSocialDto request);
	String getAppleIntegrationStatus(Integer businessId);
	String getApplePageIdByBusinessId(Integer businessId, Long enterpriseNumber, boolean isSMB);
	Integer getBusinessIdForPageId(String destinationId, String intent, boolean isSMB);
	String getApplePreDownloadUrl(AttachmentPreDownloadRequest request);
	AttachmentPreUploadResponse getPreUploadDataFromSocial(AttachmentPreUploadSocialRequest attachmentPreUploadSocialRequest);

}