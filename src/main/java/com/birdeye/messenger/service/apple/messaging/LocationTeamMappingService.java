/**
 * 
 */
package com.birdeye.messenger.service.apple.messaging;

import java.util.List;

import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest;
import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest.LocationTeamMapping;

/**
 * <AUTHOR>
 *
 */
public interface LocationTeamMappingService {

    List<LocationTeamMapping> saveLocationTeamMappings(AppleLocationTeamMappingRequest locationTeamMappingRequest);

    List<LocationTeamMapping> getLocationTeamMappings(Integer accountId, List<Integer> businessIds);

    void deleteLocationTeamMappings(Integer accountId, List<Integer> businessIds);

    Integer getLocationTeamMapping(Integer accountId, Integer businessId);

}
