/**
 * 
 */
package com.birdeye.messenger.service.apple.messaging;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest.FaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqUpdateRequest;
import com.birdeye.messenger.enums.SortOrderEnum;

/**
 * <AUTHOR>
 *
 */
@Service
public interface TopFaqService {

    TopFaqResponse addTopFaqs(TopFaqRequest topFaqRequest);

    Integer addSingleFaq(FaqRequest faqRequest, Integer accountId, Integer userId);

    void updateFaq(Integer id, FaqRequest faqRequest, Integer accountId, Integer userId);

    void deleteFaq(Integer id);

    void updateTopFaqs(TopFaqUpdateRequest topFaqUpdateRequest);

    TopFaqGetResponse getTopFAQByAccountId(Integer accountId, Integer page, Integer size, String sortBy,
            SortOrderEnum sortOrderEnum);

}
