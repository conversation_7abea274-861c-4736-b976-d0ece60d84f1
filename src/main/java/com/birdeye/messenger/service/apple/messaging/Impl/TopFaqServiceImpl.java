/**
 * 
 */
package com.birdeye.messenger.service.apple.messaging.Impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.TopFAQs;
import com.birdeye.messenger.dao.repository.TopFaqsRespository;
import com.birdeye.messenger.dto.QueroFAQResponse;
import com.birdeye.messenger.dto.QueroFAQResponse.FAQ;
import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse.GetFaqResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqRequest.FaqRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqUpdateRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqUpdateRequest.FaqUpdateRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.IntentType;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.QueroService;
import com.birdeye.messenger.service.apple.messaging.TopFaqService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TopFaqServiceImpl implements TopFaqService {

    private final QueroService queroService;

    private final TopFaqsRespository topFaqsRespository;

    @Override
    @Transactional
    public TopFaqResponse addTopFaqs(TopFaqRequest topFaqRequest) {
        log.info("addTopFaqs called with request : {}", topFaqRequest);
        Integer accountId = topFaqRequest.getAccountId();
        List<FaqRequest> faqRequests = topFaqRequest.getFaqRequests();
        FaqRequest faqRequest = faqRequests.stream().filter(
                faq -> Objects.nonNull(faq.getFaqId()) && Objects.nonNull(faq.getIntentType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(faqRequest)) {
            log.error("Faq id and intent type both are present in : {}", faqRequest);
            throw new MessengerException(ErrorCode.UNKNOWN, "Faq id and intent type both are present in " + faqRequest);
        }
        FaqRequest faqRequest2 = faqRequests.stream().filter(
                faq -> Objects.isNull(faq.getFaqId()) && Objects.isNull(faq.getIntentType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(faqRequest2)) {
            log.error("Faq id and intent type both are not present in : {}", faqRequest2);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are not present in " + faqRequest2);
        }
        Integer count = topFaqsRespository.getCountOfFAQByAccountId(accountId);
        if (count >= MessengerConstants.MAX_LIMIT_TOP_FAQS
                || (count + faqRequests.size()) > MessengerConstants.MAX_LIMIT_TOP_FAQS) {
            log.error("Already added : {} number of FAQs", count);
            throw new MessengerException(ErrorCode.UNKNOWN, "Limit reached for adding Top FAQs");
        }

        TopFaqResponse topFaqResponse = null;
        Integer userId = topFaqRequest.getUserId();
        Set<Integer> faqIds = faqRequests.stream().filter(faq -> faq.getFaqId() != null).map(FaqRequest::getFaqId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(faqIds)) {
            QueroFAQResponse queroFAQResponse = queroService.getFAQsByIds(new ArrayList<>(faqIds), accountId, false);
            if (faqIds.size() != queroFAQResponse.getQnAs().size()) {
                log.error("Invalid faq ids sent in request : {}", faqIds);
                throw new MessengerException(ErrorCode.UNKNOWN, "Invalid faq ids sent in request" + faqIds);
            }
            List<FAQ> faqWithNotAllLocations = queroFAQResponse.getQnAs().stream()
                    .filter(faq -> Objects.nonNull(faq) && !faq.getAllLocations())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(faqWithNotAllLocations)) {
                List<Integer> ids = faqWithNotAllLocations.stream().map(faq -> faq.getQuestion().getId())
                        .collect(Collectors.toList());
                log.error("FAQs with ids : {} are not enabled for all locations", ids);
                throw new MessengerException(ErrorCode.UNKNOWN,
                        "FAQ with ids " + ids + " are not enabled for all locations");
            }

        }
        List<TopFAQs> topFAQs = faqRequests.stream().map(faq -> faq.getTopFAQs(accountId, userId))
                .collect(Collectors.toList());
        try {

            log.info("topFaqs entities before save : {}", topFAQs);
            topFAQs = topFaqsRespository.saveAll(topFAQs);
            log.info("topFaqs entities after save : {}", topFAQs);
        } catch (Exception e) {
            log.error("Error occurred while saving in DB : {}", e.getMessage());
            throw new MessengerException(ErrorCode.UNKNOWN, "Error occurred while saving in DB " + e.getMessage());
        }
        Set<Integer> topFaqIds = topFAQs.stream().map(TopFAQs::getId).collect(Collectors.toSet());
        topFaqResponse = new TopFaqResponse(new ArrayList<>(topFaqIds), faqIds.size() == topFaqIds.size());
        return topFaqResponse;
    }

    @Override
    @Transactional
    public Integer addSingleFaq(FaqRequest faqRequest, Integer accountId, Integer userId) {
        log.info("addSingleFaq called with request : {},accountId : {},userId : {}", faqRequest, accountId, userId);
        Integer faqId = faqRequest.getFaqId();
        IntentType intentType = faqRequest.getIntentType();
        if (Objects.nonNull(intentType) && Objects.nonNull(faqId)) {
            log.error("Faq id and intent type both are present in : {}", faqRequest);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are present in request " + faqRequest);
        }

        if (Objects.isNull(intentType) && Objects.isNull(faqId)) {
            log.error("Faq id and intent type both are not present in : {}", faqRequest);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are not present in request " + faqRequest);
        }
        Integer count = topFaqsRespository.getCountOfFAQByAccountId(accountId);
        if (count >= MessengerConstants.MAX_LIMIT_TOP_FAQS) {
            log.error("Already added : {} number of FAQs", count);
            throw new MessengerException(ErrorCode.UNKNOWN, "Limit reached for adding Top FAQs");
        }
        if (Objects.nonNull(faqId)) {

            QueroFAQResponse queroFAQResponse = queroService.getFAQsByIds(Collections.singletonList(faqId), accountId,
                    false);
            if (queroFAQResponse.getQnAs().size() <= 0) {
                log.error("Invalid faq id : {}", faqId);
                throw new MessengerException(ErrorCode.UNKNOWN, "Invalid faq id " + faqId);
            }
            FAQ faqWithNotAllLocations = queroFAQResponse.getQnAs().stream()
                    .filter(faq -> Objects.nonNull(faq) && !faq.getAllLocations())
                    .findFirst().orElse(null);
            if (Objects.nonNull(faqWithNotAllLocations)) {
                log.error("FAQ : {} is not enabled for all locations", faqId);
                throw new MessengerException(ErrorCode.UNKNOWN,
                        "FAQ with id " + faqId + " is not enabled for all locations");
            }
        }
        TopFAQs topFAQs = faqRequest.getTopFAQs(accountId, userId);
        try {

            log.info("topFaqs entity before save : {}", topFAQs);
            topFAQs = topFaqsRespository.save(topFAQs);
            log.info("topFaqs entity after save : {}", topFAQs);
        } catch (Exception e) {
            log.error("Error occurred while saving in DB : {}", e.getMessage());
            throw new MessengerException(ErrorCode.UNKNOWN, "Error occurred while saving in DB " + e.getMessage());
        }
        return topFAQs.getId();
    }

    @Override
    @Transactional
    public void updateFaq(Integer id, FaqRequest faqRequest, Integer accountId, Integer userId) {
        log.info("update SingleFaq called with id : {}, request : {},accountId : {},userId : {}", id, faqRequest,
                accountId, userId);
        Integer faqId = faqRequest.getFaqId();
        IntentType intentType = faqRequest.getIntentType();
        if (Objects.nonNull(intentType) && Objects.nonNull(faqId)) {
            log.error("Faq id and intent type both are present in : {}", faqRequest);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are present in request " + faqRequest);
        }

        if (Objects.isNull(intentType) && Objects.isNull(faqId)) {
            log.error("Faq id and intent type both are not present in : {}", faqRequest);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are not present in request " + faqRequest);
        }
        if (Objects.nonNull(faqId)) {

            QueroFAQResponse queroFAQResponse = queroService.getFAQsByIds(Collections.singletonList(faqId), accountId,
                    false);
            if (queroFAQResponse.getQnAs().size() <= 0) {
                log.error("Invalid faq id : {}", faqId);
                throw new MessengerException(ErrorCode.UNKNOWN, "Invalid faq id " + faqId);

            }
            FAQ faqWithNotAllLocations = queroFAQResponse.getQnAs().stream()
                    .filter(faq -> Objects.nonNull(faq) && !faq.getAllLocations())
                    .findFirst().orElse(null);
            if (Objects.nonNull(faqWithNotAllLocations)) {
                log.error("FAQ : {} is not enabled for all locations", faqId);
                throw new MessengerException(ErrorCode.UNKNOWN,
                        "FAQ with id " + faqId + " is not enabled for all locations");
            }
        }

        Optional<TopFAQs> topFAQs = topFaqsRespository.findById(id);
        if (!topFAQs.isPresent()) {
            log.error("No top faq present with id : {}", id);
            throw new MessengerException(ErrorCode.UNKNOWN, "No top faq present with id " + id);
        }
        TopFAQs tFaQs = topFAQs.get();
        try {
            log.info("Top faq before update : {}", tFaQs);
            faqRequest.updateTopFAQ(tFaQs, accountId, userId);
            tFaQs = topFaqsRespository.saveAndFlush(tFaQs);
            log.info("Top faq after update : {}", tFaQs);

        } catch (Exception e) {
            log.error("Error occurred while saving in DB : {}", e.getMessage());
            throw new MessengerException(ErrorCode.UNKNOWN, "Error occurred while saving in DB " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteFaq(Integer id) {
        log.info("deleteFaq called with id : {}", id);
        try {
            topFaqsRespository.deleteById(id);
        } catch (Exception e) {
            log.error("Error occured while deleting : {}", e.getMessage());
            throw new MessengerException(ErrorCode.UNKNOWN, "Error occured while deleting " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateTopFaqs(TopFaqUpdateRequest topFaqUpdateRequest) {
        log.info("updateTopFaqs called with request : {}", topFaqUpdateRequest);
        List<FaqUpdateRequest> faqUpdateRequests = topFaqUpdateRequest.getFaqUpdateRequests();
        FaqUpdateRequest faqUpdateRequest = faqUpdateRequests.stream()
                .filter(faq -> Objects.nonNull(faq.getFaqId()) && Objects.nonNull(faq.getIntentType())).findFirst()
                .orElse(null);
        if (Objects.nonNull(faqUpdateRequest)) {
            log.error("Faq id and intent type both present in request : {}", faqUpdateRequest);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both present in " + faqUpdateRequest);
        }
        FaqUpdateRequest faqRequest2 = faqUpdateRequests.stream().filter(
                faq -> Objects.isNull(faq.getFaqId()) && Objects.isNull(faq.getIntentType()))
                .findFirst().orElse(null);
        if (Objects.nonNull(faqRequest2)) {
            log.error("Faq id and intent type both are not present in : {}", faqRequest2);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "Faq id and intent type both are not present in " + faqRequest2);
        }
        Integer accountId = topFaqUpdateRequest.getAccountId();
        Set<Integer> faqIds = faqUpdateRequests.stream().map(FaqUpdateRequest::getFaqId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(faqIds)) {
        QueroFAQResponse queroFAQResponse = queroService.getFAQsByIds(new ArrayList<>(faqIds), accountId, false);
        if (faqIds.size() != queroFAQResponse.getQnAs().size()) {
            log.error("Invalid faq ids sent in request : {}", faqIds);
            throw new MessengerException(ErrorCode.UNKNOWN, "Invalid faq ids sent in request" + faqIds);
        }
        List<FAQ> faqWithNotAllLocations = queroFAQResponse.getQnAs().stream()
                .filter(faq -> Objects.nonNull(faq) && !faq.getAllLocations())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(faqWithNotAllLocations)) {
            List<Integer> ids = faqWithNotAllLocations.stream().map(faq -> faq.getQuestion().getId())
                    .collect(Collectors.toList());
            log.error("FAQs with ids : {} are not enabled for all locations", ids);
            throw new MessengerException(ErrorCode.UNKNOWN,
                    "FAQ with ids " + ids + " are not enabled for all locations");
        }
    }
        Integer userId = topFaqUpdateRequest.getUserId();
        List<Integer> ids = faqUpdateRequests.stream().map(FaqUpdateRequest::getId).collect(Collectors.toList());
        List<TopFAQs> topFAQs = topFaqsRespository.findByIdsAndAccountId(ids, accountId);
        if (ids.size() != topFAQs.size()) {
            log.error("Invalid ids send in request : {}", ids);
            throw new MessengerException(ErrorCode.UNKNOWN, "Invalid ids send in request" + ids);
        }
        Map<Integer, TopFAQs> topFaqsMap = topFAQs.stream().collect(Collectors.toMap(TopFAQs::getId, v -> v));
        List<TopFAQs> updatedTopFAQs = faqUpdateRequests.stream().map(faq -> {
            TopFAQs topFAQ = topFaqsMap.get(faq.getId());
            faq.updateFaq(topFAQ, accountId, userId);
            return topFAQ;
        }).collect(Collectors.toList());
        try {
            topFaqsRespository.saveAll(updatedTopFAQs);
            topFaqsRespository.flush();
        } catch (Exception e) {
            log.error("Error occurred while saving in DB : {}", e.getMessage());
            throw new MessengerException(ErrorCode.UNKNOWN, "Error occurred while saving in DB " + e.getMessage());
        }

    }

    @Override
    public TopFaqGetResponse getTopFAQByAccountId(Integer accountId, Integer page, Integer size, String sortBy,
            SortOrderEnum sortOrderEnum) {
        log.info("getTopFAQByAccountId called with accountId : {},page : {},size : {},sortBy : {},order : {}",
                accountId, page, size, sortBy, sortOrderEnum);
        TopFaqGetResponse topFaqGetResponse = null;
        Direction direction = sortOrderEnum == SortOrderEnum.ASC ? Direction.ASC : Direction.DESC;
        PageRequest pageRequest = PageRequest.of(page, size, direction, sortBy);
        List<TopFAQs> topFAQs = topFaqsRespository.findAllByAccountId(accountId, pageRequest);
        log.info("topFaqs fetched from db : {}", topFAQs);
        List<GetFaqResponse> getFaqResponses = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(topFAQs)) {
            getFaqResponses = topFAQs.stream().map(GetFaqResponse::new).collect(Collectors.toList());
        }
        Integer count = topFaqsRespository.getCountOfFAQByAccountId(accountId);
        topFaqGetResponse = new TopFaqGetResponse(getFaqResponses, accountId, count);
        log.info("topFaqGetResponse created : {}", topFaqGetResponse);
        return topFaqGetResponse;
    }

}
