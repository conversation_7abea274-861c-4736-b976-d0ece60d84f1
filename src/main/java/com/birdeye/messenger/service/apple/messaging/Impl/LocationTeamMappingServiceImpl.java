/**
 * 
 */
package com.birdeye.messenger.service.apple.messaging.Impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.AppleLocationTeamMapping;
import com.birdeye.messenger.dao.repository.AppleLocationTeamMappingRepository;
import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest;
import com.birdeye.messenger.dto.apple.chat.AppleLocationTeamMappingRequest.LocationTeamMapping;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.AppleMessagingException;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.apple.messaging.LocationTeamMappingService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LocationTeamMappingServiceImpl implements LocationTeamMappingService {

    private final AppleLocationTeamMappingRepository appleLocationTeamMappingRepository;

    private final RedisHandler redisHandler;

    @Override
    public List<LocationTeamMapping> saveLocationTeamMappings(
            AppleLocationTeamMappingRequest locationTeamMappingRequest) {

        Integer accountId = locationTeamMappingRequest.getAccountId();
        List<LocationTeamMapping> locationTeamMappings = locationTeamMappingRequest.getLocationTeamMappings();

        if (CollectionUtils.isEmpty(locationTeamMappings)) {
            log.error("received empty locationTeamMappings");
            throw new AppleMessagingException(ErrorCode.LOCATION_TEAM_MAPPING_REQUEST_EMPTY);
        }

        List<AppleLocationTeamMapping> appleLocationTeamMappings = null;
        try {
            appleLocationTeamMappings = locationTeamMappings.stream()
                    .map(locationTeamMapping -> locationTeamMapping.getAppleLocationTeamMapping(accountId))
                    .collect(Collectors.toList());
            appleLocationTeamMappingRepository.saveAll(appleLocationTeamMappings);

        } catch (Exception e) {
            log.error("error : {} occurred in saveLocationTeamMappings", e.getMessage());
            Set<Integer> businessIds = locationTeamMappings.stream().map(LocationTeamMapping::getBusinessId)
                    .collect(Collectors.toSet());

            Map<Integer, AppleLocationTeamMapping> locationTeamMappingsMap = appleLocationTeamMappingRepository
                    .getAppleLocationTeamMappingsByAccountIdAndBusinessIds(accountId, new ArrayList<>(businessIds))
                    .stream().collect(Collectors.toMap(AppleLocationTeamMapping::getBusinessId, v -> v));

            appleLocationTeamMappings = locationTeamMappings.stream().map(locationTeamMapping -> {
                AppleLocationTeamMapping appleLocationTeamMapping = locationTeamMappingsMap
                        .get(locationTeamMapping.getBusinessId());

                if (Objects.nonNull(appleLocationTeamMapping)) {
                    appleLocationTeamMapping.setTeamId(locationTeamMapping.getTeamId());
                    appleLocationTeamMapping.setUpdatedAt(new Date());
                } else {
                    appleLocationTeamMapping = locationTeamMapping.getAppleLocationTeamMapping(accountId);
                }
                return appleLocationTeamMapping;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            appleLocationTeamMappingRepository.saveAll(appleLocationTeamMappings);
        }

        log.info("saveLocationTeamMappings successfully");
        locationTeamMappings = appleLocationTeamMappings.stream().map(LocationTeamMapping::new)
                .collect(Collectors.toList());
        Map<String, String> locationTeamMappingsMap = locationTeamMappings.stream()
                .collect(
                        Collectors.toMap(
                                (k) -> Constants.APPLE_LOCATION_TEAM_MAPPING_CACHE + "::"
                                        + generateCacheKey(accountId, k.getBusinessId()),
                                (v) -> String.valueOf(v.getTeamId())));
        redisHandler.addMultipleKeyValuePairsInCache(locationTeamMappingsMap);
        return locationTeamMappings;
    }

    @Override
    public List<LocationTeamMapping> getLocationTeamMappings(Integer accountId, List<Integer> businessIds) {
        List<AppleLocationTeamMapping> appleLocationTeamMappings = null;
        if (CollectionUtils.isEmpty(businessIds)) {
            log.info("getLocationTeamMappings called with empty businessIds");
            appleLocationTeamMappings = appleLocationTeamMappingRepository
                    .getAppleLocationTeamMappingsByAccountId(accountId);
            return appleLocationTeamMappings.stream().map(LocationTeamMapping::new).collect(Collectors.toList());
        }
        log.info("getLocationTeamMappings called with businessIds : {}", businessIds);
        appleLocationTeamMappings = appleLocationTeamMappingRepository
                .getAppleLocationTeamMappingsByAccountIdAndBusinessIds(accountId, businessIds);
        return appleLocationTeamMappings.stream().map(LocationTeamMapping::new).collect(Collectors.toList());
    }

    @Override
    public void deleteLocationTeamMappings(Integer accountId, List<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            log.info("deleteLocationTeamMappings called with empty businessIds");
            appleLocationTeamMappingRepository.deleteAppleLocationTeamMappingsByAccountId(accountId);
            String redisKeysPattern = Constants.APPLE_LOCATION_TEAM_MAPPING_CACHE + "::" + accountId + "*";
            redisHandler.deletePatternMatchingKeysFromRedis(redisKeysPattern);

        } else {
            log.info("deleteLocationTeamMappings called with businessIds : {}", businessIds);
            appleLocationTeamMappingRepository.deleteAppleLocationTeamMappingsByAccountIdAndBusinessIds(accountId,
                    businessIds);
            List<String> redisKeys = businessIds.stream()
                    .map(businessId -> Constants.APPLE_LOCATION_TEAM_MAPPING_CACHE + "::"
                            + generateCacheKey(accountId, businessId))
                    .collect(Collectors.toList());
            redisHandler.evictWebChatCache(redisKeys);
        }
        log.info("deleteLocationTeamMappings successfully");
    }

    @Cacheable(cacheNames = Constants.APPLE_LOCATION_TEAM_MAPPING_CACHE, key = "#accountId+'-'+#businessId", unless = "#result == null")
    @Override
    public Integer getLocationTeamMapping(Integer accountId, Integer businessId) {
        log.info("getLocationTeamMapping called with accountId : {} and businessId : {}", accountId, businessId);
        Integer teamId = null;
        Optional<AppleLocationTeamMapping> teamMapping = appleLocationTeamMappingRepository
                .getAppleLocationTeamMappingByAccountIdAndBusinessId(accountId, businessId);
        if (teamMapping.isPresent()) {
            log.info("teamMapping present");
            AppleLocationTeamMapping appleLocationTeamMapping = teamMapping.get();
            teamId = appleLocationTeamMapping.getTeamId();
        }
        log.info("getLocationTeamMapping teamId returned : {}", teamId);
        return teamId;
    }

    private String generateCacheKey(Integer accountId, Integer businessId) {
        return accountId + "-" + businessId;
    }

}
