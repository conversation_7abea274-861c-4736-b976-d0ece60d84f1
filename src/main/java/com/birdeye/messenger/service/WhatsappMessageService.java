package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.WhatsappMessage;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendWAMessageResponse;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.WhatsappMessageStatusEnum;

/**
 * <AUTHOR>
 *
 */
public interface WhatsappMessageService {

	WhatsappMessage saveWhatsappMessage(MessageDTO messageDTO, String senderId, String recipientId, String messageId, WhatsappMessageStatusEnum status);

	WARestrictedFlags isWAFreeflowSendAvailable(MessengerContact messengerContact, int routeId);

	SendWAMessageResponse sendWACallToSocial(MessageDTO messageDTO, String waPhoneNoId);

	void updateWAMessageBody(String messageBody, Integer messageId);

	WhatsappMessage findWAMessageById(String messageId);

	void saveWAMsg(WhatsappMessage waMsg);

	boolean isWAUserReachable(MessengerContact messengerContact, Integer accountId);
}
