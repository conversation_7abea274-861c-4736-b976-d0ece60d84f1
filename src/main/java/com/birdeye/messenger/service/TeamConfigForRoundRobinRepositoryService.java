package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;

public interface TeamConfigForRoundRobinRepositoryService {

	TeamConfigForRoundRobin saveTeamConfigForRoundRobin(Integer accountId, TeamConfigForRoundRobin teamConfigForRoundRobin);

    void deleteTeamConfigForRoundRobin(Integer accountId);

    TeamConfigForRoundRobin findTeamConfigForRoundRobin(Integer accountId,Integer teamId);

    TeamConfigForRoundRobin findTeamConfigForRoundRobinByAccountId(Integer accountId);

	void evictTeamConfigCache(Integer accountId);

}
