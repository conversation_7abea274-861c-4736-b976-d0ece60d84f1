package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dto.robin.GetRobinReplyConfigResponse;
import com.birdeye.messenger.dto.robin.RobinAutoReplySetupRequest;
import com.birdeye.messenger.enums.Source;

import java.util.List;
import java.util.Map;

public interface RobinAutoReplyConfigRepositoryService {
    void createUpdateRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest);

    RobinAutoReplyConfig getRobinAutoReplyConfigForBusiness(Integer businessId,Integer enterpriseId, Source source);

    void deleteRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest);

    Map<Integer, GetRobinReplyConfigResponse> getAllRobinAutoReplyConfig(RobinAutoReplySetupRequest request);

    void updateAllLocation(RobinAutoReplySetupRequest request);

    List<GetRobinReplyConfigResponse> getRobinAutoReplyEffectiveConfig(RobinAutoReplySetupRequest request);

    public void evictConfigCache(Integer enterpriseId);
}
