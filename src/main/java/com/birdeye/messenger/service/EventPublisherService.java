package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.ConversationClosedEvent;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.enums.ActivityType;

import java.util.List;

public interface EventPublisherService {

    void produceEvent(ActivityType activityType, Integer accountId, List<Integer> conversationIds,
    		ContactDocument contactDocument);

	void publishConversationClosedEvent(ConversationClosedEvent event);

}
