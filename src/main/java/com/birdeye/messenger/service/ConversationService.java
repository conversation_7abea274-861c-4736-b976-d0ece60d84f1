package com.birdeye.messenger.service;

import java.text.ParseException;
import java.util.List;
import java.util.concurrent.ExecutionException;

import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.enums.BulkOp;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.MessengerContactMessage;


public interface ConversationService {
	
    ConversationDetails getCustomerConversationDetailsFromElastic(Integer messageId, Integer customerId, BusinessDTO businessDTO) throws ParseException;
	
	ContactMessageDTO getContactMessage(Integer messageId, String messageType);

    ConversationResponse getConversationV2(ConversationRequest conversationRequest, Integer accountId, Integer userId);
	
	public void updateConversationTag(Integer accountId, Integer userId, Integer conversationId, Integer tag);

	Object getConversationsCount(ConversationRequest request,Integer accountId, Integer userId) throws InterruptedException, ExecutionException;

    MessengerContactMessage getConversationV2(Integer messengerContactId, Integer accountId, Integer userId, ConversationByIdRequest request);

	Object getConversationsCountV2(ConversationRequest conversationRequest, Integer accountId, Integer userId)
			throws InterruptedException, ExecutionException;
	
	void updateStatus(Integer accountId, Integer userId, Integer conversationId, ConversationStatusRequest contactMessageRequest,Boolean publicApi);

	boolean checkForDateIntersectionAndSetApplicableDates(ConversationRequest conversationRequest);

	void updateMultipleConversationsInES(List<Integer> conversationIds, BulkOp op, Integer accountId,Integer userId);

    boolean deleteConversation(Integer mcId, Integer routingId,RefreshPolicy refreshPolicy);

	void removeReviewFromOldConversation(ContactDocument conversation, Integer accountId, Integer reviewId);

	Long getLastReviewOrSurveyOn(ContactDocument conversation, Integer accountId, Integer msgId, String msgType);

    List<ConversationStatus> getConversationStatus(Integer userId, List<Integer> cIds, Integer accountId);

	MessengerContact getMcIdForCustomer(Integer cId, Integer businessId, String type);

	boolean deleteConversations(List<Integer> mcIds, Integer routingId);

	Object getdashboardInboxCount(ConversationRequest request, Integer accountId, Integer userId) throws InterruptedException, ExecutionException;

	Long getConversationCountForFreemiumAccount(ConversationCountFreemiumAccount conversationCountRequest);

	void generateEventForClosedConversationTidyUp(TidyUpCloseEvent request);

	void updateConversationStatus(MessengerContact messengerContact, Integer status, List<Integer> viewedByUsersFromES,
			Integer accountId, MessageRequest messageRequest);

	MessengerContact getOrCreateMCForSocial(ConversationBySocialId request);

	Object getConversationsPollingCount(ConversationRequest conversationRequest, Integer accountId, Integer userId)
			throws InterruptedException, ExecutionException;

}
