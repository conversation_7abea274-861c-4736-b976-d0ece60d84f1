package com.birdeye.messenger.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;

public interface NotificationService {

    void sendEmailNotification(MessengerGlobalFilter notificationRequest, MessageDocument messageDocument);


    Set<String> emailIdsToNotify(Map<String,String> userIdEmailIdMapDTO, MessengerGlobalFilter notificationRequest);

    void processUnreadMessageNotification(BusinessDTO businessDTO, MessageDocument messageDocument);
    void scheduleJobForNotification(MessengerGlobalFilter notificationRequest, String Interval);

	void sendEmailNotificationV2(MessengerGlobalFilter messengerGlobalFilter, MessageDocument messageDocument);

	Set<String> getSendNotificationEmailIds(Map<String,Map<String,Map<String,String>>> messengerUserNotificationMap,
			MessengerGlobalFilter notificationRequest);

	void processMessageNotification(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO,
			MessageDocument messageDocument);

	List<Integer> getRemovedConversations(NotificationRequest request, Integer userId,
			Integer accountId);

	void sendEmailNotificationScheduled(MessengerGlobalFilter messengerGlobalFilter, MessageDocument messageDocument);

    MessengerNotificationMessage getConversations(NotificationRequest notificationRequest, Integer userId,
            Integer accountId) throws Exception;

	void sendEmailNotificationUnResponded(MessengerGlobalFilter messengerGlobalFilter, MessageDocument messageDocument);


}
