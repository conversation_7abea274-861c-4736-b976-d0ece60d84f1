package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.PulseSurveyQuestionRequest;
import com.birdeye.messenger.dto.SMSMessageDTO;

/**
 * <AUTHOR>
 *
 */
public interface PulseSurveyService {

	PulseSurveyContext pulseContextCheckSmsReceive(SMSMessageDTO smsMessageDTO, CustomerDTO customerDTO, BusinessDTO businessDTO) throws Exception;

	PulseSurveyContext handlePulseSurveyContext(CampaignSMSDto smsEvent, CustomerDTO customer, BusinessDTO businessDto) throws Exception;

	PulseSurveyContext updatePulseSurveyContextForSurveyEvent(PulseSurveyQuestionRequest request, BusinessDTO businessDto, String responseStatus);

	PulseSurveyContext getPulseSurveyContextById(Integer contextId);

	void updatePulseSurveyContextStatus(PulseSurveyContext existingContext, String name);

	void clearPulseSurveyContextCache(Integer customerId);

//	PulseSurveyContext findPulseSurveyContextBySmsId(Integer smsId);

}
