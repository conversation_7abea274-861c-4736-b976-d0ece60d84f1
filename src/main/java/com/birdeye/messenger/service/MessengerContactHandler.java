package com.birdeye.messenger.service;

import java.util.Date;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.external.service.ContactService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.impl.CommunicationHelperService;
import com.birdeye.messenger.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * Service class to handle messenger contact related operations
 *
 */
@Service
@Slf4j
public class MessengerContactHandler implements PostEventHandler {

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private FirebaseService fcmService;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private CommunicationHelperService communicationHelperService;

	@Autowired
	private ConversationActivityService conversationActivityService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private SpamDetectionService spamDetectionService;

	@Override
	public MessengerData handleCallAutoReply(BusinessDTO business, CustomerDTO customer, SmsDTO sms,Boolean spamStatus) {

		Integer source = sms.getSource(); // MissedCall, LiveChat, AutoReply
		MessengerContact messengerContact = null;
		sms.setSpam(spamStatus);
		//BIRDEYE-66320 :  Updating messengerContact last message only for MissedCall AutoReply
		if(source == Source.MISSED_CALL_AUTOREPLY.getSourceId()) {
			messengerContact = messengerContactService
					.updateLastMessage(createDtoForCallAutoReply(sms, business, customer));
		} else {
			messengerContact = messengerContactService
					.getOrCreateMessengerContact(createDtoForCallAutoReply(sms, business, customer));
		}
		UserDTO userDTO=new UserDTO();
    		userDTO.setId(MessengerConstants.VOICECALL_USER);

    		boolean liveChatSource = (source == Source.LIVE_CHAT_SEND.getSourceId());

    		if (liveChatSource) {
            	userDTO  = communicationHelperService.getUserDTO(MessengerConstants.ROBIN_REPLY_USER);
            	sms.setSource(Source.VOICE_CALL_AUTOREPLY.getSourceId());
    		}

    		else if (source == Source.MISSED_CALL_AUTOREPLY.getSourceId())
		    	userDTO.setId(MessengerConstants.AUTO_REPLY_USER);

		// ES update for Conversation. Ideally should be done separately in a separate task.
    		//BIRDEYE-66320 : Do not update LastMessage in ContactDocument for VoiceMail/LiveChat AutoReply source
    		ContactDocument conversation = null;
    		if(source != Source.MISSED_CALL_AUTOREPLY.getSourceId()) {
    			conversation = messengerContactService.contactDocumentBuilder(messengerContact, customer, business,
    					MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);
    		} else {
    			conversation = messengerContactService.updateContactOnES(messengerContact, customer, business,
    					MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);
    		}

		// No media support for auto reply
		MessengerMediaFileDTO messengerMediaFileDTO = null;

		// Message doc is using 0 to identify
		if (!liveChatSource)
			userDTO.setId(0);
		// ES update for new message.
		//Add 2sec to message TS : For managing Message ordering : for autoreply messages
		sms.setSentOn(DateUtils.Add2SecondToAutoreplyMsg(sms.getSentOn()));
		sms.setCreateDate(DateUtils.Add2SecondToAutoreplyMsg(sms.getCreateDate()));

		MessageDocument messageDocument = messengerContactService.andNewMessageOnEs(
				new MessageDocumentDTO(sms, conversation.getM_c_id()), messengerMediaFileDTO, userDTO,
				business, MessengerEvent.SMS_SEND);
		log.info("[Voicemail Auto Reply] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
				messageDocument.getC_id(), messageDocument.getM_id());


		// Only mirroring is required for auto reply message, no email notification
		if (Boolean.FALSE.equals(messengerContact.getSpam())) {
			fcmService.pushToFireBase(conversation, messageDocument, false);
		}

		// Messenger Message Audit.
		messengerMessageService.saveMessengerMessage(messageDocument);

		MessengerData data = new MessengerData();
		data.setContactDocument(conversation);
		data.setMessageDocument(messageDocument);
		return  data;

	}

	private MessengerContactDto createDtoForCallAutoReply(SmsDTO voiceCallSms, BusinessDTO business, CustomerDTO customer) {
		MessengerContactDto dto = new MessengerContactDto();
		dto.setBusinessDTO(business);
		dto.setCustomerDTO(customer);
		dto.setTag(MessageTag.INBOX.getCode());
		if  (voiceCallSms.getRecordingDuration()!=null && voiceCallSms.getRecordingDuration().intValue() > 5)
			dto.setLastMessage(voiceCallSms.getMessageBodyUnencrypted());
		if (voiceCallSms.getSource() != Source.MISSED_CALL_AUTOREPLY.getSourceId() && (voiceCallSms.getRecordingDuration()!=null && voiceCallSms.getRecordingDuration().intValue() > 5)) {
			dto.setTag(MessageTag.UNREAD.getCode());
		} else {
			//BIRDEYE-66320 : Missed Call : Set body : String
			String name = getCustomerNameForVoiceMail(customer);
			dto.setLastMessage(name + MessengerConstants.MISSEDCALL_CONVERSATION_MESSAGE);
		}
		dto.setEncrypt(true);

		dto.setLastUpatedAt(voiceCallSms.getSentOn());
		dto.setLastActivity(new Date());
		LastMessageMetaData lastMessageMetaData = new LastMessageMetaData();
		// TODO: Add more fields.
		lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.SEND.toString());
		//To show VoiceCall: before received message, lastMessageType should be receive
		if (voiceCallSms.getSource() == Source.MISSED_CALL_AUTOREPLY.getSourceId()) {
			lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
			lastMessageMetaData.setLastReceivedMessageSource(Source.MISSED_CALL_AUTOREPLY.getSourceId());
			lastMessageMetaData.setLastMessageSource(Source.MISSED_CALL_AUTOREPLY.getSourceId());
		}
		dto.setLastMessageMetaData(lastMessageMetaData);
		// Last message meta data
//    	LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
//        lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.SEND.toString());
//        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
		dto.setSpam(voiceCallSms.getSpam());
		dto.setCreateDate(voiceCallSms.getCreateDate());
		return dto;

	}

	private String getCustomerNameForVoiceMail(CustomerDTO customer) {
		String name = "";
		if(StringUtils.isNotBlank(customer.getFirstName())) {
			name += customer.getFirstName();
		}
		if(StringUtils.isNotBlank(customer.getLastName())) {
			name += " " + customer.getLastName();
		}
		return StringUtils.isNotBlank(name) ? name : customer.getPhone();
	}

	@Override
	public MessengerData handleVoiceMail(BusinessDTO business, CustomerDTO customer, VoiceCallDto voiceCall,CallReceiveDTO request) {

		MessengerContact messengerContact = messengerContactService
				.updateLastMessage(createDtoForVoiceMail(voiceCall, business, customer));

		UserDTO userDTO = new UserDTO();
		userDTO.setId(MessengerContactService.VOICEMAIL_USER);
		//Spam detection
		if(StringUtils.isNotBlank(request.getTranscription())) {
			SMSMessageDTO messageDTO = new SMSMessageDTO();
			messageDTO.setBusinessDTO(business);
			messageDTO.setCustomerDTO(customer);
			messageDTO.setMessengerContact(messengerContact);
			messageDTO.setBody(voiceCall.getTranscription());
			messageDTO.setSource(Source.VOICE_CALL.getSourceId());
			MessageTag messageTag = MessageTag.getMessageTagById(messengerContact.getTag());
			spamDetectionService.spamDetectionAllChannels(messageDTO, messengerContact, customer, messageTag);
			customer=messageDTO.getCustomerDTO();
		}
		// ES update for Conversation.
		ContactDocument conversation = messengerContactService.updateContactOnES(messengerContact, customer, business,
				MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);

		MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO();
		messengerMediaFileDTO.setContentType(MessengerMediaFileDTO.VOICEMAIL_TYPE);
		messengerMediaFileDTO.setUrl(voiceCall.getRecordingUrl());
		boolean isSpam = false;
		if (conversation.isHide() || Boolean.TRUE.equals(customer.getBlocked())) {
			isSpam = true;
		}
		// ES update for new message. Audio file support needs to be added.
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(voiceCall, conversation.getM_c_id());
        commonService.updateContactFiltersInMessage(conversation, messageDocumentDTO);
    	MessageDocument messageDocument = messengerContactService.andNewMessageOnEs(messageDocumentDTO,messengerMediaFileDTO, userDTO, business,
				MessengerEvent.SMS_RECEIVE, isSpam);

		log.info("[Voicemail message] ES data push for Messages with m_c_id#{} and m_id#{} is done.",
				messageDocument.getC_id(), messageDocument.getM_id());

		// Messenger Message Audit.
		messengerMessageService.saveMessengerMessage(messageDocument);

		MessengerData data = new MessengerData();
		data.setContactDocument(conversation);
		data.setMessageDocument(messageDocument);

		
		// Mirroring & PushNotification true
		// No Notification for Unknown Callers Voice mail
		if (voiceCall.getDuration() != null && voiceCall.getDuration().intValue() <= 5
				&& customer.getFirstName() == null && customer.getLastName() == null) {
			return data;
		}
		Integer callSuppressDuration = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("voicecall_suppress_duration_seconds", 16));
		if (voiceCall.getDuration() != null && voiceCall.getDuration() < callSuppressDuration
				&& customer.getFirstName() == null && customer.getLastName() == null && voiceCall.getTranscription() == MessengerConstants.NO_TRANSCRIPTION_MESSAGE) {
			return data;
		}
		if (Boolean.TRUE.equals(customer.getBlocked())){
			return  data;
		}
		// ****** Mail notification wherever applicable.****** //
		notificationService.processUnreadMessageNotification(business, messageDocument);
		fcmService.pushToFireBase(conversation, messageDocument, true);
		return data;
	}

	MessengerContactDto createDtoForVoiceMail(VoiceCallDto voiceCall, BusinessDTO business, CustomerDTO customer) {
		MessengerContactDto dto = new MessengerContactDto();
		dto.setBusinessDTO(business);
		dto.setCustomerDTO(customer);
		dto.setTag(MessageTag.UNREAD.getCode());
		dto.setEncrypt(true);
		dto.setLastMessage(voiceCall.getTranscription());
		dto.setLastUpatedAt(new Date());
		dto.setLastActivity(new Date());
		LastMessageMetaData lastMessageMetaData = new LastMessageMetaData();
		// TODO: Add more fields.
		lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
		lastMessageMetaData.setLastReceivedMessageSource(Source.VOICE_CALL.getSourceId());
		lastMessageMetaData.setLastMessageChannel(MessageDocument.Channel.VOICE_CALL.name());
		lastMessageMetaData.setLastMessageSource(Source.VOICE_CALL.getSourceId());
		dto.setLastMessageMetaData(lastMessageMetaData);
		dto.setRecordingDuration(voiceCall.getDuration());
		dto.setCreateDate(voiceCall.getCreateDate());
		// Last message meta data
//		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
//		lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.SEND.toString());
//		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
//		updateContactWithVoiceMail(messengerContact, business, customer, voiceCall);
		dto.setSpam(voiceCall.getSpam());
		return dto;

	}

	@Override
	public MessengerContact handleMissedCallWithoutAutoReply(VoiceCallDto voiceCallDto, BusinessDTO businessDTO, CustomerDTO customerDTO) {
		MessengerContactDto messengerContactDto= createDtoForMissedCall(voiceCallDto,businessDTO,customerDTO);
		String name = getCustomerNameForVoiceMail(customerDTO);
		messengerContactDto.setLastMessage(name + MessengerConstants.MISSEDCALL_CONVERSATION_MESSAGE);
		messengerContactDto.setLastActivity(voiceCallDto.getCreateDate());
		MessengerContact messengerContact = messengerContactService.handleMissedCall(messengerContactDto);
		MessageTag messageTag = MessageTag.getMessageTagById(messengerContactDto.getTag()); // can be remove it ?
		UserDTO userDTO = new UserDTO();
		ContactDocument contactDocument = messengerContactService.contactDocumentBuilder(messengerContact, customerDTO, businessDTO, messageTag,
				userDTO);
		contactDocument.setIs_encrypted(0);
		messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, businessDTO.getRoutingId());
		if(messengerContactDto.getHide()){
			messengerContact.setLastMessage(null);
		}
		log.info("Contact document for missed call is {} ",contactDocument);
		customerDTO.setUserId(Constants.INBOX_DEFENDER_USER);
		conversationActivityService.createMissedCallActivity(ActivityType.MISSED_CALL,customerDTO,contactDocument,businessDTO,voiceCallDto.getCreateDate());
		return messengerContact;
	}

	private MessengerContactDto createDtoForMissedCall(VoiceCallDto voiceCallDto,BusinessDTO businessDTO,CustomerDTO customerDTO){
		MessengerContactDto dto = new MessengerContactDto();
		dto.setBusinessDTO(businessDTO);
		dto.setCustomerDTO(customerDTO);
		dto.setTag(MessageTag.INBOX.getCode());
		dto.setLastActivity(new Date());
		LastMessageMetaData lastMessageMetaData = new LastMessageMetaData();
		dto.setLastMessageMetaData(lastMessageMetaData);
		dto.setSpam(voiceCallDto.getSpam());
		dto.setCreateDate(voiceCallDto.getCreateDate());
		return dto;
	}
}
