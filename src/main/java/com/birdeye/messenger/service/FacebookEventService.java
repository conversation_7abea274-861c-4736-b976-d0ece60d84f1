package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendFacebookMessageResponse;
import com.birdeye.messenger.dto.facebook.Messaging;
import com.birdeye.messenger.enums.FacebookMessageStatusEnum;
import com.birdeye.messenger.service.impl.FacebookSendEventHandler;

public interface FacebookEventService {

	SendFacebookMessageResponse sendFBCallToSocial(FacebookSendEventHandler handler, MessageDTO messageDTO,
			String pageId);

	Boolean isFBSendAvailable(MessengerContact messengerContact, Integer routeId);

	FacebookMessage saveFacebookMessage(MessageDTO messageDTO, String senderId, String recipientId, String messageId,
			FacebookMessageStatusEnum status);

	void updateFbMessageStatus(Messaging messaging,Integer mcId);

	boolean isFacebookUserReachable(MessengerContact messengerContact, Integer accountId);

	String getFacebookIntegrationStatus(Integer selectedBusinessId, Integer messengerEnabled);
}
