package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.InitiateVideoConversationDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.VideoConversationEvent;

/**
 * <AUTHOR>
 *
 */
public interface VideoService {

	InitiateVideoConversationDTO initiateVideo(Integer conversationId, Integer accountId, Integer loggedInUserId, Integer source) throws Exception;

	void handleVideoConversationEvent(VideoConversationEvent event);

	void sendMessageToCustomer(MessengerContact conversation, CustomerDTO customer,
			InitiateVideoConversationDTO response, UserDTO loggedInUserDetails, Integer source) throws Exception;

}
