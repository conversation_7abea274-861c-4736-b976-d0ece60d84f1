package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.ChatTranscriptAccountConfig;
import com.birdeye.messenger.dto.ChatTranscriptRequest;

import java.util.List;

public interface ChatTranscriptRepositoryService {

    void enableChatTranscript(ChatTranscriptRequest chatTranscriptRequest);

    List<ChatTranscriptAccountConfig> getChatTranscriptAccountConfig(Integer accountId);

    void disableChatTranscript(ChatTranscriptRequest chatTranscriptRequest);

    void evictChatTranscriptConfigCache(ChatTranscriptRequest chatTranscriptRequest);

}
