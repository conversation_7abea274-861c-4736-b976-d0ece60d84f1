package com.birdeye.messenger.service;

import java.util.Map;

import com.birdeye.messenger.dto.DailyReportRequest;
import com.birdeye.messenger.dto.GenericFirebaseMessage;

public interface MessengerService {

	<T> Void updateFbIntegratingStatusCache(GenericFirebaseMessage<?> facebookStatusRequest);

	Map messengerDailyReport(DailyReportRequest request);

	boolean hasAgentRespondedOnOutsideBH(Integer accountId, Integer mcId, Boolean isReceivedDuringBusinessHr);

	boolean hasAgentRespondedWithinConfiguredTimeWindow(Integer accountId, Integer mcId);

//	ScheduleAlertMessage receiveMessage(FacebookMessageRequest message) throws Exception;

}
