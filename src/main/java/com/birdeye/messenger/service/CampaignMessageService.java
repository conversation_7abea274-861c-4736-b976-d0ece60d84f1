package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.EmailCampaignEventResponse;

/**
 * <AUTHOR>
 *
 */
public interface CampaignMessageService {

	void processCampaignEvent(CampaignSMSDto smsEvent, boolean campaignEventFoundInRedis);
	
	void processEmailCampaignEvent(EmailCampaignEventResponse emailCampaignEvent);

	void handlePulseSurveyContext(CampaignSMSDto smsEvent);

	void saveCampaignEventOnRedis(CampaignSMSDto campaignSMSDto);
}
