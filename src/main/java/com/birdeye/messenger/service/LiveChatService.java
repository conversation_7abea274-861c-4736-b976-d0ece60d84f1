package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.ActivityType;

/**
 * 
 * <AUTHOR>
 *
 */
public interface LiveChatService {
	
	LiveChatInitResponseDTO initialiseSession(LiveChatInitDTO liveChatInitDTO);
	
	void receiveMessage(String sessionId, LiveChatMessageDTO liveChatMessageDTO);
	
	void sessionClose(String sessionId, Integer source);
	
	LiveChatSessionTimeoutResponse sessionTimeout(String sessionId, Integer source,Integer widgetConfigId, boolean sendAutoReply, boolean lastMessageReplied);
	
	LiveChatSessionRefreshResponseDTO sessionRefresh(String sessionId, Integer widgetId, Integer source);

	void pushTypingStateChangeEvent(String type, ConversationStateDTO event);
	
	void terminateStaleSessions();

	public void consumeSQSMessage(ConversationStateDTO event);

	public void updateFirebaseMessageForTypingEvent(ConversationStateDTO event, boolean typingStopped);
	
	public void updateConvStateOnMessageSend(SendMessageDTO message);
	
	public LiveChatSessionToken sessionTerminated(String sessionId, Integer source, ActivityType activityType);

	void receiveSuggestion(String sessionId, LiveChatMessageDTO liveChatMessageDTO);
	void receiveFeedback(String sessionId, FeedbackRequest feedbackRequest);
	void sendFeedbackMessage(String sessionId, Integer sourceId);

	void addTypingEventForRobin(ConversationStateDTO event);

	void updateFirebaseMessageForAppleTypingEvent(ConversationStateDTO event, Integer businessId, Integer mcId,
			boolean customerTyping);
	
	void updateContactDetails(UpdateWebchatContactDetailsRequest request) throws Exception;
}
