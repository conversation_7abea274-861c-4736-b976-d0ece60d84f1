package com.birdeye.messenger.service;


import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessengerFilter;

public interface CommunicationService {

	Object getMessage(MessengerFilter messengerFilter, String platform) throws Exception;

	Boolean isFBSendAvailable(Integer messengerContactId, Integer routeId, LastMessageMetaData lastMessageMetaData);

	Boolean isFBSendAvailable(MessengerContact messengerContact, Integer routeId,
							  LastMessageMetaData lastMessageMetaData);

	Boolean getUserReachableStatus(Integer customerId, BusinessDTO business) throws Exception;

	MessengerContact getMessengerContact(Integer ecId, Long businessId, boolean isBizNumber);

	Object getMcIdByReviewId(Integer reviewId, Integer accountId);

}
