package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.CampaignSMSDto;

/**
 * <AUTHOR>
 *
 */
public interface PulseSurveyContextService {

	PulseSurveyContext getPulseSurveyContext(Integer customerId, List<String> status);

	PulseSurveyContext createPulseSurveyContext(CampaignSMSDto smsEvent, Integer businessId, Integer customerId);

	void updatePulseSurveyContextStatus(PulseSurveyContext existingContext, String status);

	void savePulseSurveyContext(PulseSurveyContext context);

	void clearPulseSurveyContextCache(Integer customerId);

	PulseSurveyContext getPulseSurveyContextById(Integer id);

//	PulseSurveyContext getPulseSurveyContextBySmsId(Integer smsId);

	PulseSurveyContext getLastPulseSurveyContext(Integer customerId);

}