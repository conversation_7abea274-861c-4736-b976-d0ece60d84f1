package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.enums.MessengerEvent;

public interface SendMessageService {

    void saveMediaFile(MessengerMediaFileDTO mediaFileDTO, Integer id);

    void pushSendRequestToKafka(SendMessageDTO sendMessageDTO, MessengerEvent event, UserDTO userDTO, boolean sendSms);

    void sendEmail(EmailDTO emailDTO);

    void audit(SendMessageDTO sendMessageDTO, MessengerEvent event, UserDTO userDTO);

    void pushSendRequestToKafkaSendSmsRetry(Send<PERSON>essageDTO sendMessageDTO,MessengerEvent event,UserDTO userDTO,boolean sendSms);


    }
