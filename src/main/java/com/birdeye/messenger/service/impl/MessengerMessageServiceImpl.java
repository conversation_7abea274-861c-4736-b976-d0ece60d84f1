package com.birdeye.messenger.service.impl;

import java.util.AbstractMap;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dao.entity.AppleMessage;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;
import com.birdeye.messenger.dao.repository.AppleMessageRepository;
import com.birdeye.messenger.dao.repository.GoogleMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerMessageRepository;
import com.birdeye.messenger.dao.repository.SecureMessageRepository;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomChannelDTO;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.LogUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MessengerMessageServiceImpl implements MessengerMessageService {

	@Autowired
	private MessengerMessageRepository messengerMessageRepository;

	@Autowired
	private GoogleMessageRepository googleMessageRepository;
	
	@Autowired
	private AppleMessageRepository appleMessageRepository;

	@Autowired
	private MessengerMediaFileService messengerMediaFileService;

    @Autowired
    private SecureMessageRepository secureMessageRepository;

	@Override
	public void saveMessengerMessage(ConversationDTO conversationDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = convertDtoTOEntity(conversationDTO, userDTO);
		messengerMessageRepository.saveAndFlush(messengerMessage);
	}

	@Override
	public void saveMessengerMessage(SmsDTO smsDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = convertDtoTOEntity(smsDTO, userDTO);
		long startTime = System.currentTimeMillis();
		messengerMessageRepository.saveAndFlush(messengerMessage);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("saveMessengerMessage", startTime, endTime);
	}

	@Override
	public void saveMessengerMessage(MessageDocument document) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setMessageType(document.getMsg_type());
		messengerMessage.setMessageId(Integer.parseInt(document.getM_id()));
		messengerMessage.setMessengerContactId(Integer.parseInt(document.getC_id()));
		messengerMessage.setAccountId(document.getE_id());
		messengerMessage.setChannel(document.getChannel().name());
		messengerMessage.setCommunicationDirection(document.getCommunicationDirection().name());
		// TODO: Revisit
		messengerMessage.setCreatedDate(new Date());
		messengerMessageRepository.saveAndFlush(messengerMessage);
	}

	@Override
    @Transactional
	public List<MessengerMessage> deleteByMCId(Integer mcId) {
        try {
            return messengerMessageRepository.deleteByMessengerContactId(mcId);
        } catch (Exception e) {
            log.error("error : {} occurred in deleteByMCId", e.getMessage());
            return null;
        }
    }

	@Override
	public void saveMessengerActivity(ConversationActivity activity, Integer conversationId, UserDTO userDTO) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setMessageType(activity.getActivityType());
		messengerMessage.setMessageId(activity.getId());
		messengerMessage.setCreatedDate(activity.getCreatedDate());
		messengerMessage.setMessengerContactId(conversationId);
		if (userDTO != null) {
			messengerMessage.setCreatedBy(userDTO.getId());
		}
		long startTime = System.currentTimeMillis();
		messengerMessageRepository.saveAndFlush(messengerMessage);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("saveMessengerActivity", startTime, endTime);
	}

	private MessengerMessage convertDtoTOEntity(SmsDTO smsDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setAccountId(smsDTO.getBusinessId());
		messengerMessage.setChannel(smsDTO.getChannel().name());
		messengerMessage.setCommunicationDirection(smsDTO.getCommunicationDirection().name());
		messengerMessage.setCreatedDate(smsDTO.getSentOn());
		if (userDTO != null) {
			messengerMessage.setCreatedBy(userDTO.getId());
		}
		if (smsDTO.getSentThrough() != null) {
			messengerMessage.setSentThrough(smsDTO.getSentThrough().name());
		}
		messengerMessage.setMessageId(smsDTO.getSmsId());
		messengerMessage.setMessageType(smsDTO.getMessageType().name());
		messengerMessage.setMessengerContactId(smsDTO.getMessengerContactId());
		messengerMessage.setPageUrlTrackedByGoogleAnalytics(smsDTO.getPageUrlTrackedByGoogleAnalytics());
		return messengerMessage;
	}

	@Override
	public void saveMessengerMessage(CustomChannelDTO customChannelDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = convertDtoTOEntity(customChannelDTO, userDTO);
		long startTime = System.currentTimeMillis();
		messengerMessageRepository.saveAndFlush(messengerMessage);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("saveCustomChannelMessage", startTime, endTime);
	}

	private MessengerMessage convertDtoTOEntity(CustomChannelDTO customChannelDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setAccountId(customChannelDTO.getBusinessId());
		messengerMessage.setChannel(customChannelDTO.getChannel().name());
		messengerMessage.setCommunicationDirection(customChannelDTO.getCommunicationDirection().name());
		messengerMessage.setCreatedDate(customChannelDTO.getSentOn());
		if (userDTO != null) {
			messengerMessage.setCreatedBy(userDTO.getId());
		}
		if (customChannelDTO.getSentThrough() != null) {
			messengerMessage.setSentThrough(customChannelDTO.getSentThrough().name());
		}
		messengerMessage.setMessageId(customChannelDTO.getId());
		messengerMessage.setMessageType(customChannelDTO.getMessageType().name());
		messengerMessage.setMessengerContactId(customChannelDTO.getMessengerContactId());
		return messengerMessage;
	}

	private MessengerMessage convertDtoTOEntity(ConversationDTO conversationDTO, UserDTO userDTO) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setAccountId(conversationDTO.getBusinessId());
		messengerMessage.setChannel(conversationDTO.getChannel().name());
		messengerMessage.setCommunicationDirection(conversationDTO.getCommunicationDirection().name());
		messengerMessage.setCreatedDate(conversationDTO.getSentOn());
		if (userDTO != null) {
			messengerMessage.setCreatedBy(userDTO.getId());
		}
		if (conversationDTO.getSentThrough() != null) {
			messengerMessage.setSentThrough(conversationDTO.getSentThrough().name());
		}
		messengerMessage.setMessageId(conversationDTO.getId());
		messengerMessage.setMessageType(conversationDTO.getMessageType().name());
		messengerMessage.setMessengerContactId(conversationDTO.getMessengerContactId());
		return messengerMessage;
	}

//	@Override
////	public void saveMessengerMessageReviewDetails(ReviewEvent reviewEvent, Integer mcId) {
////		MessengerMessage messengerMessage = new MessengerMessage();
////		messengerMessage.setMessageType(MessageDocument.MessageType.REVIEW.toString());
////		messengerMessage.setMessageId(reviewEvent.getReviewDetail().getReviewId());
////		messengerMessage.setMessengerContactId(mcId);
////		messengerMessage.setAccountId(reviewEvent.getReviewDetail().getAccountId());
////		messengerMessage.setReviewId(reviewEvent.getReviewDetail().getReviewId());
////		// TODO: Revisit
////		messengerMessage.setCreatedDate(new Date());
////		messengerMessageRepository.saveAndFlush(messengerMessage);
////	}

	@Override
	public void saveMessengerMessageReviewDetails(ReviewEvent event, Integer mcId) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setMessageType(MessageDocument.MessageType.REVIEW.toString());
		messengerMessage.setChannel(MessageDocument.Channel.REVIEW.toString());
		messengerMessage.setMessageId(event.getReviewDetail().getReviewId());
		messengerMessage.setMessengerContactId(mcId);
		messengerMessage.setAccountId(event.getReviewDetail().getAccountId());
		messengerMessage.setReviewId(event.getReviewDetail().getReviewId());
		messengerMessage.setCreatedDate(new Date());
		messengerMessageRepository.saveAndFlush(messengerMessage);
	}

	@Override
	public MessengerMessage findByAccountIdAndReviewId(Integer accountId, Integer reviewId) {
		return messengerMessageRepository.findByAccountIdAndReviewId(accountId, reviewId);
	}

	@Override
	public void updateMessengerMessage(MessengerMessage messengerMessage) {
		messengerMessageRepository.saveAndFlush(messengerMessage);
		return;
	}

	@Override
	public List<MessengerMessage> deleteByMessengerContactIdAndReviewId(Integer mcId, Integer reviewId) {
		return messengerMessageRepository.deleteByMessengerContactIdAndReviewId(mcId, reviewId);
	}

	@Override
	public List<MessengerMessage> findByMessengerContactId(Integer mcId) {
		return messengerMessageRepository.findByMessengerContactId(mcId);
	}

	@Override
	public void updateMessengerMessage(Integer fromMcId, Integer toMcId) {
		messengerMessageRepository.updateMessages(fromMcId, toMcId);
	}

	@Override
	public MessengerMessage findByMessengerContactIdAndReviewId(Integer mcId, Integer reviewId) {
		return messengerMessageRepository.findByMessengerContactIdAndReviewId(mcId, reviewId);
	}

	@Override
	public void deleteAllExceptReviewByMCId(Integer conversationId) {
		messengerMessageRepository.deleteAllExceptReviewByMCId(conversationId);
	}

	@Override
	public void saveMessengerMessageSurveyDetails(SurveyEvent.After event, Integer mcId, Integer accountId) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setMessageType(MessageDocument.MessageType.SURVEY_RESPONSE.toString());
		messengerMessage.setChannel(MessageDocument.Channel.SURVEY_RESPONSE.toString());
		messengerMessage.setMessageId(event.getId());
		messengerMessage.setMessengerContactId(mcId);
		messengerMessage.setAccountId(accountId);
		messengerMessage.setSurveyResponseId(event.getId());
		messengerMessage.setCreatedDate(new Date());
		messengerMessageRepository.saveAndFlush(messengerMessage);
	}

	@Override
	public MessengerMessage findByAccountIdAndSurveyResponseId(Integer accountId, Integer surveyResponseId) {
		return messengerMessageRepository.findByAccountIdAndSurveyResponseId(accountId,surveyResponseId);
	}

	@Override
	public void deleteBySurveyResponseId(Integer surveyResponseId) {
		messengerMessageRepository.deleteBySurveyResponseId(surveyResponseId);
	}

	@Override
	@Transactional
	public Map.Entry<MessengerMessage, GoogleMessage> saveGoogleMessage(MessengerMessage msg, GoogleMessage gMsg, Optional<MessengerMediaFile> mediaFile) {
		if(StringUtils.isNotBlank(gMsg.getMessageBody())) { // it will be blank in case of image upload
			try {
				String encryptedMsgBody = EncryptionUtil.encrypt(gMsg.getMessageBody(), StringUtils.join(gMsg.getSenderId(),gMsg.getRecipientId()), StringUtils.join(gMsg.getRecipientId(),gMsg.getSenderId()));
				gMsg.setMessageBody(encryptedMsgBody);
				gMsg.setEncrypted(1);
			} catch (Exception e) {
				log.error("saveGoogleMessage: Failed to encrypt the mess");
			}
		}
		gMsg = googleMessageRepository.save(gMsg);
		msg.setMessageId(gMsg.getId());
		if(mediaFile.isPresent()) {
			MessengerMediaFile messengerMediaFile = mediaFile.get();
			messengerMediaFile.setMessageId(gMsg.getId());
			messengerMediaFile = messengerMediaFileService.saveMedia(messengerMediaFile);
			gMsg.setMediaId(messengerMediaFile.getId());
		}
		msg = messengerMessageRepository.save(msg);
		return new AbstractMap.SimpleEntry<>(msg, gMsg);
	}

	@Override
	public void saveMessengerMessage(LiveChatMessageObject liveChatMessageObject, UserDTO userDTO) {
		MessengerMessage messengerMessage = convertDtoTOEntity(liveChatMessageObject, userDTO);
		messengerMessageRepository.saveAndFlush(messengerMessage);
		
	}

	private MessengerMessage convertDtoTOEntity(LiveChatMessageObject liveChatMessageObject, UserDTO userDTO) {
		MessengerMessage messengerMessage = new MessengerMessage();
		messengerMessage.setAccountId(liveChatMessageObject.getBusinessId());
		messengerMessage.setChannel(liveChatMessageObject.getChannel().name());
		messengerMessage.setCommunicationDirection(liveChatMessageObject.getCommunicationDirection().name());
		messengerMessage.setCreatedDate(liveChatMessageObject.getSentOn());
		if (userDTO != null) {
			messengerMessage.setCreatedBy(userDTO.getId());
		}
		if (liveChatMessageObject.getSentThrough() != null) {
			messengerMessage.setSentThrough(liveChatMessageObject.getSentThrough().name());
		}
		messengerMessage.setMessageId(liveChatMessageObject.getId());
		messengerMessage.setMessageType(liveChatMessageObject.getMessageType().name());
		messengerMessage.setMessengerContactId(liveChatMessageObject.getMessengerContactId());
		messengerMessage.setPageUrlTrackedByGoogleAnalytics(liveChatMessageObject.getPageUrlTrackedByGoogleAnalytics());
		return messengerMessage;
	}

	@Override
	public Entry<MessengerMessage, AppleMessage> saveAppleMessage(MessengerMessage msg, AppleMessage aMsg) {
		if(StringUtils.isNotBlank(aMsg.getMessageBody())) { // it will be blank in case of image upload
			try {
				String encryptedMsgBody = EncryptionUtil.encrypt(aMsg.getMessageBody(), StringUtils.join(aMsg.getSenderId(),aMsg.getRecipientId()), StringUtils.join(aMsg.getRecipientId(),aMsg.getSenderId()));
				aMsg.setMessageBody(encryptedMsgBody);
				aMsg.setEncrypted(1);
			} catch (Exception e) {
				log.error("saveGoogleMessage: Failed to encrypt the mess");
			}
		}
		aMsg = appleMessageRepository.save(aMsg);
		msg.setMessageId(aMsg.getId());
		msg = messengerMessageRepository.save(msg);
		return new AbstractMap.SimpleEntry<>(msg, aMsg);
	
	}	
	@Override
	public void updateMessengerMessageMcId(Integer fromMcId, Integer toMcId) {
		messengerMessageRepository.updateMessengerContactId(toMcId, Collections.singletonList(fromMcId));
	}

    @Override
    public Entry<MessengerMessage, SecureMessage> saveSecureMessage(MessengerMessage msg, SecureMessage secureMessage) {
        log.info("saveSecureMessage called with msg : {} , secureMessage : {}", msg, secureMessage);
        if (StringUtils.isNotBlank(secureMessage.getMessageBody())) { // it will be blank in case of image upload
            try {
                String encryptedMsgBody = CommunicationDirection.RECEIVE.name().equals(msg.getCommunicationDirection())
                        || "received".equals(secureMessage.getStatus())
                                ? EncryptionUtil.encrypt(secureMessage.getMessageBody(),
                        StringUtils.join(secureMessage.getCustomerId(), secureMessage.getBusinessId()),
                                        StringUtils.join(secureMessage.getBusinessId(), secureMessage.getCustomerId()))
                                : EncryptionUtil.encrypt(secureMessage.getMessageBody(),
                                        StringUtils.join(secureMessage.getBusinessId(), secureMessage.getCustomerId()),
                                        StringUtils.join(secureMessage.getCustomerId(), secureMessage.getBusinessId()));
                secureMessage.setMessageBody(encryptedMsgBody);
                secureMessage.setEncrypted(1);
            } catch (Exception e) {
                log.error("saveSecureMessage: Failed to encrypt the mess");
            }
        }
        secureMessage = secureMessageRepository.save(secureMessage);
        msg.setMessageId(secureMessage.getId());
        msg = messengerMessageRepository.save(msg);
        log.info("saveSecureMessage successfull");
        return new AbstractMap.SimpleEntry<>(msg, secureMessage);
    }

    @Override
    public MessengerMessage findByReviewId(Integer reviewId) {
        return messengerMessageRepository.findByReviewId(reviewId);
    }

    @Override
    public void updateMessengerMessagesInBusinessUpgradeOrDowngrade(Integer targetAccountId, Integer sourceAccountId,
            List<Integer> mcIds) {
        log.info(
                "updateMessengerMessagesInBusinessUpgradeOrDowngrade called with targetAccountId : {} and sourceAccountId : {} and mcIds : {}",
                targetAccountId, sourceAccountId, mcIds);
        Integer count = messengerMessageRepository.updateMessengerMessageInBusinessUpgradeOrDowngrade(targetAccountId,
                sourceAccountId, mcIds);
        log.info("{} messenger messages updated successfully", count);
    }

	@Override
	@Transactional
	public void updateMessengerMessagesForConversation(Integer fromMcId,Integer toMcId){
			messengerMessageRepository.updateAnonymousMessages(fromMcId, toMcId);
	}
}