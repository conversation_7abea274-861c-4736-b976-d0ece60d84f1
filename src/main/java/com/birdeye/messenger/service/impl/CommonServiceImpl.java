package com.birdeye.messenger.service.impl;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest.OpType;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ClickTrackingEvent;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.ClickTrackingEventsRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.ActivityMigrateDTO;
import com.birdeye.messenger.dto.BusinessAPIKeyMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ClickTrackingRequest;
import com.birdeye.messenger.dto.ConversationWrapperMessage;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.GetAccountMessagesRequest;
import com.birdeye.messenger.dto.GetAccountMessagesResponse;
import com.birdeye.messenger.dto.GetMessengerContactDTO;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.SendConversationRequest;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UsersResponse;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.apple.chat.RichLinkData.Assets;
import com.birdeye.messenger.dto.apple.chat.RichLinkData.ImageAsset;
import com.birdeye.messenger.dto.apple.chat.RichLinkData.VideoAsset;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.CustomerInfo;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.ReportMessageDocument;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.ReviewRequestResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.CampaignReviewRequest;
import com.birdeye.messenger.external.service.CampaignService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.PDFGenService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MediaUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CommonServiceImpl implements CommonService {

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private CampaignService campaignService;

	@Autowired
	protected CommunicationHelperService communicationHelperService;

	private final ElasticSearchExternalService elasticSearchService;
	
	private final ObjectMapper objectMapper;

	private final BusinessService businessService;

	private final PDFGenService pdfGenService;
	
	private final ContactService contactService;

	@Autowired
	private UserService userService;
	
	@Autowired
	private FirebaseService firebaseService;
	
	@Autowired
	private ConversationActivityService conversationActivityService;
	
	@Autowired
	private RedisHandler redisHandler;
	
	@Autowired
	protected ClickTrackingEventsRepository clickTrackingEventsRepository;
	
	// Nexus request would be common
	@Override
	public SendConversationRequest getSendSMSRequestForMessenger(ConversationWrapperMessage message, Long requestId) {
		if (message == null) {
			return null;
		}
		SendConversationRequest sendConversationRequest = new SendConversationRequest();
		sendConversationRequest.setExternalUID(message.getExternalUID());
		sendConversationRequest.setBody(message.getBody());
		sendConversationRequest.setFromPhone(message.getFromPhone());
		sendConversationRequest.setToPhone(message.getToPhone());
		if (message.getToCustomerId() != null) {
			sendConversationRequest.setToCustomerId(Integer.parseInt(message.getToCustomerId()));
		}
		sendConversationRequest.setFromBusinessId(Integer.parseInt(message.getFromBusinessId()));
		if (message.getToBusinessUserId() != null) {
			sendConversationRequest.setToBusinessUserId(Integer.parseInt(message.getToBusinessUserId()));
		}
		sendConversationRequest.setType(message.getType());
		sendConversationRequest.setSubType(message.getSubType());
		sendConversationRequest.setMediaUrl(message.getMediaUrl());
		// TODO: Consider merging review request path, current processing would be done
		// based on messenger type which has duplicate code.
		sendConversationRequest.setParams(getConversationParamsMap(requestId, message.getType()));
		sendConversationRequest.setIsRetryAckRequired(1);
		if(Objects.nonNull(message.getAccountId())){
			sendConversationRequest.setAccountId(Integer.parseInt(message.getAccountId()));
		}
		return sendConversationRequest;
	}

	/**
	 * Get SMS Parameters Map
	 *
	 * @param requestId
	 * @param rType
	 * @return
	 */
	private static Map<String, Serializable> getConversationParamsMap(Long requestId, String rType) {
		Map<String, Serializable> params = new HashMap<String, Serializable>();
		if (requestId != null) {
			params.put("rid", requestId);
		}
		if (StringUtils.isNotBlank(rType)) {
			params.put("rType", rType);
		}
		return params;
	}

	@Override
	public void setMessageBody(SendMessageDTO smsMessage, CustomerDTO customerDTO, String source) {
		String smsBody = smsMessage.getBody();

		// check for template usage
		if (smsMessage.getTemplateId() != null) {
			UserDTO userDto = userService.getUserDTO(smsMessage.getUserId());
			MessengerContact messengerContact = null;

			if (smsMessage.getToCustomerId() != null) {
				// TODO: We should cache static attributes
				messengerContact = messengerContactService.findById(Integer.valueOf(smsMessage.getToCustomerId()));
			}
			if (messengerContact == null) {
				throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
			}

			// RR/CX/Survey Templates, Added support for promotion sms in same path,
			// Campaign will return promotionId
			Integer customerId = messengerContact.getCustomerId();
			CampaignReviewRequest campaignReviewRequest = new CampaignReviewRequest(customerId, source,
					smsMessage.getTemplateId(), smsMessage.getBody(), smsMessage.getSurveyId(), userDto.getEmailId(), smsMessage.getIsGlobalTemplate(),userDto.getId());
			if (smsMessage.getSurveyType() != null) {
				campaignReviewRequest.setSurveyType(smsMessage.getSurveyType());
			}
			log.info("Calling campaign service to add bitly Url in request body for template Id: {}",
					smsMessage.getTemplateId());
			ReviewRequestResponse reviewRequestResponse = campaignService
					.createReviewRequestAndBitlyUrl(campaignReviewRequest);
			if (reviewRequestResponse.getFailureReason() == null) {
				smsBody = reviewRequestResponse.getBody();
				smsMessage.setRequestId(reviewRequestResponse.getId());
				smsMessage.setTemplateId(reviewRequestResponse.getTemplateId());
			} else {
				log.error(
						"Exception from campaign to Get Message body with bitly url: customerId {} businessId {} IS {}",
						customerId, smsMessage.getTemplateId(), reviewRequestResponse.getFailureReason());
			}
		}
		smsMessage.setBody(smsBody);
	}

	@Override
	public Map<String, ActivityMigrateDTO> getActivityObject(Map<String, String> activitiesMap) {
		Map<String, ActivityMigrateDTO> activityMigrateMap = new HashMap<>();
		activitiesMap.forEach((key, value) -> {
			value = removeMultiSpace(value);
			if (value.contains(Constants.ActivityTypes.CLOSED_BY)) {
				String createdBy = getTextAfter(value, Constants.ActivityTypes.CLOSED_BY);
				activityMigrateMap.put(key,
						new ActivityMigrateDTO(createdBy, null, ActivityType.CLOSED));
			} else if (value.contains(Constants.ActivityTypes.ASSIGNED)) {
				String createdBy = getTextBefore(value, Constants.ActivityTypes.ASSIGNED);
				String triggeredFor = getTextAfter(value, Constants.ActivityTypes.ASSIGNED);
				activityMigrateMap.put(key,
						new ActivityMigrateDTO(createdBy, triggeredFor, ActivityType.ASSIGNED));
			} else if (value.contains(Constants.ActivityTypes.UNASSIGNED)) {
				String createdBy = getTextBefore(value, Constants.ActivityTypes.UNASSIGNED);
				activityMigrateMap.put(key,
						new ActivityMigrateDTO(createdBy, null, ActivityType.UNASSIGNED));
			} else if (value.contains(Constants.ActivityTypes.CONVERSATION_OPEN)) {
				String createdBy = getTextAfter(value, Constants.ActivityTypes.CONVERSATION_OPEN);
				activityMigrateMap.put(key,
						new ActivityMigrateDTO(createdBy, null, ActivityType.REOPENED));
			} else if (value.contains(Constants.ActivityTypes.NOTE_ADDED)) {
				String createdBy = getTextBefore(value, Constants.ActivityTypes.NOTE_ADDED);
				activityMigrateMap.put(key,
						new ActivityMigrateDTO(createdBy, null, ActivityType.ADD_NOTE));
			} else {
				activityMigrateMap.put(key, new ActivityMigrateDTO());
			}
		});
		return activityMigrateMap;
	}

	private String getTextBefore(String input,String subString){
		String[] splittedString = input.split(subString);
		String str=splittedString[0];
		if(StringUtils.isNotBlank(str)) {
			str=str.trim();
		}
		return str;
    }

	private String getTextAfter(String input, String subString) {
		String[] splittedString = input.split(subString);
		String str = splittedString[1];
		if (StringUtils.isNotBlank(str)) {
			str = str.trim();
		}
		return str;
	}

	private String removeMultiSpace(String input) {
		input = input.trim();
		String output = "";
		String[] words = input.split("\\s+");
		for (String word : words) {
			output = output.concat(word).concat(" ");
		}
		return output;
	}

	@Override
	public String getBaseCDNImageURLForBusiness(Integer businessId) {
		String cdnImageBaseURL = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("cdn_image_base_url", "cdn_image_base_url");
		String baseURL = null;
		if (StringUtils.isNotBlank(cdnImageBaseURL)) {
			baseURL = cdnImageBaseURL + "/" + businessId;
		}
		return baseURL;
	}

	@Override
	public GetAccountMessagesResponse getAccountMessages(GetAccountMessagesRequest request) {
		validateInboxDataRequest(request);
		SearchResponse searchResponse = null;
		List<MessageDocument> messageDocuments = new ArrayList<>();
		String scrollId = request.getScrollId();
		if (StringUtils.isNotBlank(scrollId) && "START".equals(scrollId)) {
			searchResponse = getSearchResult(request);
		} else {
			searchResponse = elasticSearchService.readMoreFromSearch(scrollId, request.getSessionTime().trim());
			if (searchResponse != null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getSearchResult(request);
			}
		}
		if (java.util.Objects.nonNull(searchResponse)) {
			messageDocuments = getMessagesListFromResponse(searchResponse);
		}
		if (CollectionUtils.isEmpty(messageDocuments)) {
			return null;
		}
		scrollId = searchResponse.getScrollId();
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			populateWithOldFields(messageDocuments);
		}

		List<ReportMessageDocument> reportDocuments = new ArrayList<>();
		List<Integer> cId = new ArrayList<>();
		messageDocuments.stream().forEach(message -> {
			cId.add(Integer.parseInt(message.getC_id()));
		});
		List<GetMessengerContactDTO> messengerContacts = messengerContactService.findByIdIn(cId);
		Map<Integer, Integer> mcBusinessIdMap = messengerContacts.stream().collect(Collectors
				.toMap(GetMessengerContactDTO::getId, GetMessengerContactDTO::getBusinessId, (key1, key2) -> key1));

		messageDocuments.stream().forEach(message -> {
			Integer businessId = mcBusinessIdMap.get(Integer.parseInt(message.getC_id()));
			ReportMessageDocument docs = new ReportMessageDocument(message);
			if (businessId != null && message.getB_id() != null)
				docs.setBusinessId(businessId);
			else
				docs.setBusinessId(0);

			reportDocuments.add(docs);
		});

		GetAccountMessagesResponse accountMessagesResponse = new GetAccountMessagesResponse(reportDocuments, scrollId);
		return accountMessagesResponse;
	}

	private void validateInboxDataRequest(GetAccountMessagesRequest request) {
		Assert.isTrue(DateUtils.isValidDate(request.getStartDateGMT(), "dd/MM/yyyy"),
				"StartDate not in expected format, dd/MM/yyyy");
		Assert.isTrue(DateUtils.isValidDate(request.getEndDateGMT(), "dd/MM/yyyy"),
				"EndDate not in expected format, dd/MM/yyyy");
		Long daysInterval = DateUtils.daysBetweenTwoDates(request.getStartDateGMT(), request.getEndDateGMT());
		Assert.isTrue(daysInterval < 90, "The difference between StartDate and EndDate cannot be greater than 90days.");
	}

	private List<MessageDocument> getMessagesListFromResponse(SearchResponse searchResponse) {
		List<MessageDocument> msgList = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> msgList
						.add(objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class)));
			}
		}
		return msgList;
	}

	private void populateWithOldFields(List<MessageDocument> messageDocuments) {
		for (MessageDocument messageDocument : messageDocuments) {
			if (java.util.Objects.isNull(messageDocument.getMessageType())) {
				messageDocument.setMessageType(MessageDocument.MessageType.CHAT);
				// handle old values of SMS_RECEIVE, MMS_RECEIVE, SMS_SEND and MMS_SEND
				if ("SMS_RECEIVE".equals(messageDocument.getMsg_type())
						|| "MMS_RECEIVE".equals(messageDocument.getMsg_type())) {
					messageDocument.setMessageType(MessageDocument.MessageType.CHAT);
					messageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);
				}
				if ("SMS_SEND".equals(messageDocument.getMsg_type())
						|| "MMS_SEND".equals(messageDocument.getMsg_type())) {
					messageDocument.setMessageType(MessageDocument.MessageType.CHAT);
					messageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
				}
				if (Source.FACEBOOK.getSourceId().equals(messageDocument.getSource())) {
					messageDocument.setChannel(MessageDocument.Channel.FACEBOOK);
				}
				if (Source.SMS.getSourceId().equals(messageDocument.getSource())) {
					messageDocument.setChannel(MessageDocument.Channel.SMS);
				}
				if (Source.EMAIL.getSourceId().equals(messageDocument.getSource())) {
					messageDocument.setChannel(MessageDocument.Channel.EMAIL);
				}
				if (Source.WEB_CHAT.getSourceId().equals(messageDocument.getSource())
						|| Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId().equals(messageDocument.getSource())) {
					messageDocument.setChannel(MessageDocument.Channel.WEB_CHAT);
				}
			}
		}
	}

	private SearchResponse getSearchResult(GetAccountMessagesRequest request) {
		SearchResponse searchResponse;
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("count", request.getEsFetchSize());
		messageFilter.put("accountId", request.getAccountId());
		if (BooleanUtils.isTrue(request.isIncludeActivity())) {
			messageFilter.put("includeActivity", true);
		}
		DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
		Date startDate = null;
		Date endDate = null;
		;
		try {
			startDate = df.parse(request.getStartDateGMT());
			endDate = df.parse(request.getEndDateGMT());
		} catch (ParseException e) {
			log.error("Date Format Exception dd/MM/yyyy");
			return null;
		}

		long startTime = startDate.getTime();
		long endTime = endDate.getTime();
		messageFilter.put("startTime", startTime);
		messageFilter.put("endTime", endTime);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addScroll(request.getSessionTime().trim())
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_BY_ACCOUNT_ID, dataModel).build();
		searchResponse = elasticSearchService.getSearchResult(esRequest);
		return searchResponse;
	}

	@Override
	public void rerunMissedSMS() throws Exception {
		String line = "";
		String splitBy = ",";
		try {
			BufferedReader br = new BufferedReader(new FileReader("/Users/<USER>/Downloads/MissedSMS.csv"));
			br.readLine();
			while ((line = br.readLine()) != null) {
				String[] employee = line.split(splitBy);
				SMSMessageDTO receiveDTO = new SMSMessageDTO();
				receiveDTO.setToNumber("+" + employee[6]);
				receiveDTO.setFromNumber("+" + employee[7]);
				receiveDTO.setBody(employee[8]);
				receiveDTO.setTwilioSMSId(employee[1]);

				// List<Media> media = null;
				// Ignoring Media SMS for now
				if (employee[16] != null && !employee[16].equalsIgnoreCase("NULL")) {
					continue;

				}
				// receiveDTO.setMedia(media);
				String decodedBody = EncryptionUtil.decrypt(receiveDTO.getBody(),
						StringUtils.join(receiveDTO.getFromNumber(), receiveDTO.getToNumber()),
						StringUtils.join(receiveDTO.getToNumber(), receiveDTO.getFromNumber()), true);
				receiveDTO.setBody(decodedBody);
				System.out.println(receiveDTO);

				String url = "http://messenger.birdeye.internal/messenger/event";
				HttpHeaders headers = new HttpHeaders();
				headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
				headers.setContentType(MediaType.APPLICATION_JSON);
				HttpEntity<SMSMessageDTO> httpEntity = new HttpEntity<>(receiveDTO, headers);
				ResponseEntity<Object> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Object.class);
				if (!response.getStatusCode().is2xxSuccessful()) {
					System.out.println("processed missed SMS: Failed for request: " + receiveDTO);
				}
			}
			br.close();
		} catch (IOException e) {
			e.printStackTrace();
		}

	}

	@Override
	public List<Integer> getSourceIdsByInboxReportType(ReportType reportType, List<String> sourceIds) {
		List<Integer> result = new ArrayList<Integer>();
		if (CollectionUtils.isNotEmpty(sourceIds)) {
			sourceIds.forEach(source -> {
				if ("TEXT".equals(source.toUpperCase())) {
					result.add(Source.SMS.getSourceId());
				}
				if (Source.FACEBOOK.name().equals(source.toUpperCase())) {
					result.add(Source.FACEBOOK.getSourceId());
				}
				if (Source.INSTAGRAM.name().equals(source.toUpperCase())) {
					result.add(Source.INSTAGRAM.getSourceId());
				}
				if (Source.EMAIL.name().equals(source.toUpperCase())) {
					result.add(Source.EMAIL.getSourceId());
				}
				if (Source.WEB_CHAT.name().equals(source.toUpperCase())) {
					if (ReportType.ACTIVE_CONVERSATION_BY_LOCATION.equals(reportType)
							|| ReportType.ACTIVE_CONVERSATION_BY_TIME.equals(reportType)
							|| ReportType.ACTIVE_CONVERSATION_BY_USER.equals(reportType)
							|| ReportType.ACTIVE_CONVERSATION_CHANNEL_WISE.equals(reportType)
							|| ReportType.INBOX_ROI_REPORT.equals(reportType)) {
						result.add(Source.WEB_CHAT.getSourceId());
						result.add(Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId());
						result.add(Source.LIVE_CHAT_RECEIVE.getSourceId());
						result.add(Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId());
						result.add(Source.LIVE_CHAT_SEND.getSourceId());
						result.add(Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId());
					}
					if (ReportType.RECEIVED_CONVERSATION_BY_LOCATION.equals(reportType)
							|| ReportType.RECEIVED_MESSAGES_BY_TIME.equals(reportType)
							|| ReportType.RECEIVED_MESSAGES_CHANNEL_WISE.equals(reportType)) {
						result.add(Source.WEB_CHAT.getSourceId());
						result.add(Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId());
						result.add(Source.LIVE_CHAT_RECEIVE.getSourceId());
						result.add(Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId());
					}
					if (ReportType.MEDIAN_RESPONSE_OVER_TIME.equals(reportType)
							|| ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION.equals(reportType)
							|| ReportType.MEDIAN_RESPONSE_TIME_BY_USER.equals(reportType)) {
						result.add(Source.LIVE_CHAT_SEND.getSourceId());
						result.add(Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId());
					}
				}
				if ("VOICECALL".equals(source)) {
					result.add(Source.VOICE_CALL.getSourceId());
				}
				if (Source.GOOGLE.name().equals(source.toUpperCase())) {
					result.add(Source.GOOGLE.getSourceId());
				}
				if (Source.APPLE.name().equals(source.toUpperCase())) {
					result.add(Source.APPLE.getSourceId());
				}
				if (Source.CONTACT_US.name().equals(source)) {
					result.add(Source.CONTACT_US.getSourceId());
				}
				if (Source.APPOINTMENT.name().equals(source)) {
					result.add(Source.APPOINTMENT.getSourceId());
				}
				if (Source.SECURE_MESSAGE.name().equals(source)) {
					result.add(Source.SECURE_MESSAGE.getSourceId());
				}
                if (Source.TWITTER.name().equals(source.toUpperCase())) {
                    result.add(Source.TWITTER.getSourceId());
                }
                if (Source.WHATSAPP.name().equals(source.toUpperCase())) {
                    result.add(Source.WHATSAPP.getSourceId());
                }

			});
		}
		return result;
	}

	@Override
	public RichLinkData generateRichLinkObjectFromData(String url, Integer businessId,
													   String businessName) {
		RichLinkData richLinkData = new RichLinkData();
		if (url.startsWith(Constants.APPLE_MAPS_BASE_URL)) {
			int indexOfQuestionMark = url.indexOf("?");
			String stringTillLastSlash = url.substring(0, indexOfQuestionMark);
			int indexOfLastSlash = stringTillLastSlash.lastIndexOf("/");
			url = url.substring(0, indexOfLastSlash) + "/place" + url.substring(indexOfQuestionMark);
		}
		Map<String, Object> richLinkMetaDataFromUrl = pdfGenService.getRichLinkDataFromUrl(url);
		Assets assets = new Assets();
		String finalUrl = Objects.nonNull(richLinkMetaDataFromUrl)
				&& StringUtils.isNotBlank(Objects.toString(richLinkMetaDataFromUrl.get("url"), null))
				? String.valueOf(richLinkMetaDataFromUrl.get("url"))
				: url;
		richLinkData.setUrl(finalUrl);
		String title = Objects.nonNull(richLinkMetaDataFromUrl)
				&& StringUtils.isNotBlank(Objects.toString(richLinkMetaDataFromUrl.get("title"), null))
				? String.valueOf(richLinkMetaDataFromUrl.get("title"))
				: businessName;
		richLinkData.setTitle(title);
		Object videos = Objects.nonNull(richLinkMetaDataFromUrl) ? richLinkMetaDataFromUrl.get("videos") : null;
		List<?> videosList = MediaUtils.convertObjectToList(videos);
		if (CollectionUtils.isNotEmpty(videosList)) {
			String videoUrl = StringUtils.isNotEmpty(Objects.toString(videosList.get(0), null))
					? String.valueOf(videosList.get(0))
					: null;
			VideoAsset videoAsset = new VideoAsset();
			videoAsset.setUrl(videoUrl);
			videoAsset.setMimeType("video/" + FilenameUtils.getExtension(videoUrl));
			if (videoAsset.getUrl() != null && videoAsset.getMimeType() != null) {
				assets.setVideo(videoAsset);
			}
		}
		Object images = Objects.nonNull(richLinkMetaDataFromUrl) ? richLinkMetaDataFromUrl.get("images") : null;
		List<?> imagesList = MediaUtils.convertObjectToList(images);
		if (CollectionUtils.isNotEmpty(imagesList)) {
			String imageUrl = StringUtils.isNotEmpty(Objects.toString(imagesList.get(0), null))
					? String.valueOf(imagesList.get(0))
					: null;
			ImageAsset imageAsset = new ImageAsset();
			imageAsset.setData(imageUrl);
			imageAsset.setMimeType("image/" + FilenameUtils.getExtension(imageUrl));
			if (imageAsset.getData() != null && imageAsset.getMimeType() != null) {
				assets.setImage(imageAsset);
			}
		}
		// Object faviconImages = Objects.nonNull(richLinkMetaDataFromUrl) ?
		// richLinkMetaDataFromUrl.get("favicons")
		// : null;
		// List<?> faviconImagesList = MediaUtils.convertObjectToList(faviconImages);
		// if (Objects.isNull(assets.getImage()) &&
		// CollectionUtils.isNotEmpty(faviconImagesList)) {
		// String imageUrl =
		// StringUtils.isNotEmpty(Objects.toString(faviconImagesList.get(0), null))
		// ? String.valueOf(faviconImagesList.get(0))
		// : null;
		// ImageAsset imageAsset = new ImageAsset();
		// imageAsset.setData(imageUrl);
		// imageAsset.setMimeType("image/" + FilenameUtils.getExtension(imageUrl));
		// if (imageAsset.getData() != null && imageAsset.getMimeType() != null) {
		// assets.setImage(imageAsset);
		// }
		// }
		if (MediaUtils.isImage(url)) {
			ImageAsset imageAsset = new ImageAsset(url, "image/" + FilenameUtils.getExtension(url));
			if (imageAsset.getData() != null && imageAsset.getMimeType() != null) {
				assets.setImage(imageAsset);
			}
		}
		if ("pdf".equals(url.substring(url.lastIndexOf(".") + 1))) {
			ImageAsset imageAsset = new ImageAsset(Constants.RICHLINK_DEFAULT_IMAGE_URL,
					"image/" + FilenameUtils.getExtension(Constants.RICHLINK_DEFAULT_IMAGE_URL));
			if (imageAsset.getData() != null && imageAsset.getMimeType() != null) {
				assets.setImage(imageAsset);
			}
		}
		richLinkData.setAssets(assets);

		if (Objects.isNull(assets.getImage())) {
			Map<String, Object> getBusinessDataForRichLink = businessService.getBusinessData(businessId);
			String logoUrl = (String) getBusinessDataForRichLink.get("logoUrl");
			String coverImageUrl = (String) getBusinessDataForRichLink.get("coverImageUrl");
			String imageUrl = StringUtils.isNotBlank(logoUrl) ? logoUrl
					: StringUtils.isNotBlank(coverImageUrl) ? coverImageUrl : Constants.RICHLINK_DEFAULT_IMAGE_URL;
			ImageAsset imageAsset = new ImageAsset(imageUrl, "image/" + FilenameUtils.getExtension(imageUrl));
			assets.setImage(imageAsset);
			richLinkData.setAssets(assets);

		}
		// if (richLinkData.getUrl().equals(richLinkData.getTitle())) {
		// if (StringUtils.isNotEmpty(businessName)) {
		// richLinkData.setTitle(businessName);
		// }
		// }
		return richLinkData;

	}

	@Override
	public List<Integer> getExcludedMessageSourcesList() {
		List<Integer> excludedSources = new ArrayList<Integer>();
		excludedSources.add(Source.SECURE_MESSAGE.getSourceId());
		return excludedSources;
	}

	@Override
	public void updateContactFiltersInMessage(ContactDocument contactDocument, MessageDocumentDTO messageDocumentDTO) {
		log.info("updateContactFiltersInMessage called with conversation : {} and message : {}", contactDocument,
				messageDocumentDTO);
        if (!MessengerUtil.checkIfAnyValueIsNull(contactDocument, messageDocumentDTO)
                && !Constants.EXCLUDED_SOURCE_IN_UPDATE_FILTERS.contains(messageDocumentDTO.getSource())
                && !Constants.EXCLUDED_MESSAGE_TYPE_IN_UPDATE_FILTERS.contains(messageDocumentDTO.getMessageType())) {
			log.info("inside updateContactFiltersInMessage");
			ContactState customerType = contactDocument.getContactState();
			if (customerType == ContactState.CONVERTED_LEAD) {
				customerType = ContactState.CUSTOMER;
			}
			if (ContactState.LEAD == customerType || ContactState.CUSTOMER == customerType) {
				messageDocumentDTO.setCustomerType(customerType);
			}
			messageDocumentDTO.setExperienceScore(contactDocument.getSentimentScore());

			messageDocumentDTO.setSurveyScore(contactDocument.getLatestSurveyScore());
			log.info("messageDocumentDTO updated : {}", JSONUtils.toJSON(messageDocumentDTO));
		}

	}

	@Override
    public ConversationSummaryMessageType getConversationSummaryMessageType(MessageType messageType,
            ActivityType activityType) {
        if (MessageType.ACTIVITY == messageType) {
            return Constants.PAYMENT_ACTIVITY_TYPES.contains(activityType) ? ConversationSummaryMessageType.PAYMENT
                    : Constants.APPOINTMENT_ACTIVITY_TYPES.contains(activityType)
                            ? ConversationSummaryMessageType.APPOINTMENT
                            : null;
        }

        return ConversationSummaryMessageType.getConversationSummaryMessageTypeFromMessageType(messageType);
    }
	@Override
	public UserDTO getUserIdByEmail(String userEmail, Integer fromBusinessId) {
		Integer userId=null;
		UsersResponse userResponse =   userService.getUsers(null, Collections.singletonList(userEmail));
		if (Objects.nonNull(userResponse) && CollectionUtils.isNotEmpty(userResponse.getUsers())) {
			UserDTO user = userService.getUserAndBusinessAssociation(userResponse.getUsers().get(0).getId(), fromBusinessId);
			if (Objects.nonNull(user)) {
				userId=userResponse.getUsers().get(0).getId();
			}
		}
		if (Objects.isNull(userId)) {
			throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
		}
		return userResponse.getUsers().get(0);
	}

	@Override
	public void checkUserAccountAccess(Integer userId,Integer accountId){
		try{
			UserDTO user = userService.getUserAndBusinessAssociation(userId,accountId);
			if(Objects.isNull(user)){
				log.info("saveMultiLocWidget: User {} doesn't have access to the account {}",userId,accountId);
				throw new NotAuthorizedException(ErrorCode.USR_HAS_NO_LOC_ACCESS,ErrorCode.USR_HAS_NO_LOC_ACCESS.getErrorMessage());
			}
		}catch(Exception e){
			log.info("saveMultiLocWidget: User {} doesn't have access to the account {}", userId, accountId);
			throw new NotAuthorizedException(ErrorCode.USR_HAS_NO_LOC_ACCESS, ErrorCode.USR_HAS_NO_LOC_ACCESS.getErrorMessage());
		}
	}

	@Override
	public CustomerDTO getActiveCustomerAndUpdateConversationState(String fromNumber, String email, Integer businessId, Integer accountId) {
		List<CustomerDTO> customers = contactService.findAllCustomer(fromNumber, email, businessId, accountId);
		CustomerDTO customerDTO = null;
		if (CollectionUtils.isNotEmpty(customers)) {
			if (customers.size() == 1) {
				customerDTO = customers.get(0);
			} else {
				Map<Integer, CustomerDTO> customerMap = customers.stream()
						.collect(Collectors.toMap(CustomerDTO::getId, c -> c));
				BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
				boolQueryBuilder.must(QueryBuilders.termsQuery("c_id", customerMap.keySet()));
				ESQueryBuilderRequest<ContactDocument> esQueryBuilderRequest = ESQueryBuilderRequest
						.buildESQueryBuilderRequest(0, 100, ContactDocument.class, accountId,
								Constants.Elastic.CONTACT_INDEX, boolQueryBuilder, null, null, null);
				List<ContactDocument> conversations = elasticSearchService
						.getResultUsingQueryBuilder(esQueryBuilderRequest);
				if (CollectionUtils.isEmpty(conversations)) {
					customerDTO = customers.get(0);
				} else {
					AtomicInteger customerId = new AtomicInteger();
					if (conversations.size() == 1) {
						customerDTO = customerMap.get(conversations.get(0).getC_id());
					} else {
						Optional<ContactDocument> convOpt = conversations.stream()
								.filter(c -> BooleanUtils.isTrue(c.getIsActive())).findFirst();
						if (convOpt.isPresent()) {
							customerDTO = customerMap.get(convOpt.get().getC_id());
						} else {
							customerDTO = customerMap.get(conversations.get(0).getC_id());
						}
						customerId.set(customerDTO.getId());
						List<ContactDocument> contactDocuments=conversations.stream()
						.filter(c ->!c.getId().equals(customerId.get())).collect(Collectors.toList());
						updateConversationActiveState(accountId, contactDocuments,false);
					}
				}
				customerDTO.setIsActive(true);
			}
		}
		return customerDTO;
	}
	
	@Override
	public CustomerDTO getActiveCustomerBySource(String fromNumber, String email,String name, Integer businessId, Integer accountId,String source) {
		List<CustomerDTO> customers = contactService.findAllCustomer(fromNumber, email, businessId, accountId);
		CustomerDTO customerDTO = null;
		if (CollectionUtils.isNotEmpty(customers)) {
			if (customers.size() == 1) {
				customerDTO = customers.get(0);
			} else {
				Map<Integer, CustomerDTO> customerMap = customers.stream()
						.collect(Collectors.toMap(CustomerDTO::getId, c -> c));
				BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
				boolQueryBuilder.must(QueryBuilders.termsQuery("c_id", customerMap.keySet()));
				 SortBuilder<?> sortBuilder = SortBuilders.fieldSort("l_msg_on").order(SortOrder.DESC);
				ESQueryBuilderRequest<ContactDocument> esQueryBuilderRequest = ESQueryBuilderRequest
						.buildESQueryBuilderRequest(0, 100, ContactDocument.class, accountId,
								Constants.Elastic.CONTACT_INDEX, boolQueryBuilder, sortBuilder, null, null);
				List<ContactDocument> conversations = elasticSearchService
						.getResultUsingQueryBuilder(esQueryBuilderRequest);
				if (CollectionUtils.isEmpty(conversations)) {
					customerDTO = customers.get(0);
				} else {
					if (conversations.size() == 1) {
						customerDTO = customerMap.get(conversations.get(0).getC_id());
					} else {
						Optional<ContactDocument> convOpt = Optional.empty();
						if (Source.EMAIL.name().equals(source)) {
							convOpt = conversations.stream()
									.filter(msg -> CommunicationDirection.SEND.name().equals(msg.getLastMessageType()))
									.findFirst();
						} else if (StringUtils.isNotBlank(name)
								&& (Source.CONTACT_US.name().equals(source) || Source.WEB_CHAT.name().equals(source))) {
							convOpt = conversations.stream().filter(msg -> name.equals(msg.getC_name())).findFirst();
						}
						if (convOpt.isPresent()) {
							customerDTO = customerMap.get(convOpt.get().getC_id());
						} else {
							Optional<ContactDocument> activeOpt = conversations.stream()
									.filter(c -> BooleanUtils.isTrue(c.getIsActive())).findFirst();
							if (activeOpt.isPresent()) {
								customerDTO = customerMap.get(activeOpt.get().getC_id());
							} else {
								customerDTO = customerMap.get(conversations.get(0).getC_id());
							}
						}
					}
				}
			}
		}
		return customerDTO;
	}
	
	//After contact upgrade ph1 release multiple conversation might exists with phone/email
	//once a conversation is marked as active we are marking other conversation of the group is inactive.
	private void updateConversationActiveState(Integer accountId, List<ContactDocument> contactDocuments, boolean isActive) {
		List<ContactDocument> contactDocumentToBeUpserted = new ArrayList<>();
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		for (ContactDocument doc: contactDocuments) {
			if(Objects.isNull(doc.getIsActive()) || Boolean.TRUE.equals(doc.getIsActive())) {
				ContactDocument contactForES = new ContactDocument();
				contactForES.setM_c_id(doc.getM_c_id());
				contactForES.setUpdatedAt(df.format(new Date()));
				contactForES.setIsActive(isActive);
				contactDocumentToBeUpserted.add(contactForES);
			}
		}
		if (CollectionUtils.isNotEmpty(contactDocumentToBeUpserted)) {
			long start = System.currentTimeMillis();
			log.info("updating conversation active state in bulk");
			BulkUpsertPayload<ContactDocument> bulkUpsertContacts = new BulkUpsertPayload<>(contactDocumentToBeUpserted,
					accountId, accountId, Constants.Elastic.CONTACT_INDEX);
			try {
				elasticSearchService.performBulkRequestWithRefresh(bulkUpsertContacts, OpType.UPDATE,
						RefreshPolicy.NONE);
			} catch (Exception e) {
				log.error("Exception while updating conversations active state in bulk", e);
			}
			long elapsedTime = System.currentTimeMillis() - start;
			log.info("Profiled time taken to execute updating conversations active state in bulk: {} milliseconds",
					elapsedTime);
		}
	}
	
	@Override
	@Async
	public void markConversationActiveOnSend(MessageDTO messageDTO) {
		MessageDocument messageDocument = messageDTO.getMessageDocument();
		ContactDocument contactDocument = messageDTO.getContactDocument();
		List<Integer> acceptableSources = Arrays.asList(new Integer[] { Source.SMS.getSourceId(),
				Source.LIVE_CHAT_SEND.getSourceId(), Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId(),
				Source.EMAIL.getSourceId(), Source.SECURE_MESSAGE.getSourceId() });
		contactDocument=messengerContactService.getContact(contactDocument.getE_id(),contactDocument.getM_c_id());
		if (!BooleanUtils.isTrue(contactDocument.getIsActive()) && CommunicationDirection.SEND.equals(messageDocument.getCommunicationDirection()) && !messageDTO.isBOT()) {
			if (Objects.nonNull(messageDocument.getSource())
					&& acceptableSources.contains(messageDocument.getSource())) {
				Optional<ContactDocument> activeConv=Optional.empty();
				List<CustomerDTO> customers = contactService.findAllCustomer(contactDocument.getC_phone(),
						contactDocument.getC_email(), contactDocument.getB_id(), contactDocument.getE_id());
				if (CollectionUtils.isNotEmpty(customers) && customers.size() > 1) {
					Map<Integer, CustomerDTO> customerMap = customers.stream()
							.collect(Collectors.toMap(CustomerDTO::getId, c -> c));
					BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
					boolQueryBuilder.must(QueryBuilders.termsQuery("c_id", customerMap.keySet()));
					ESQueryBuilderRequest<ContactDocument> esQueryBuilderRequest = ESQueryBuilderRequest
							.buildESQueryBuilderRequest(0, 100, ContactDocument.class, contactDocument.getE_id(),
									Constants.Elastic.CONTACT_INDEX, boolQueryBuilder, null, null, null);
					List<ContactDocument> conversations = elasticSearchService
							.getResultUsingQueryBuilder(esQueryBuilderRequest);
					if(CollectionUtils.isNotEmpty(conversations)) {
						activeConv=conversations.stream().filter(c->BooleanUtils.isTrue(c.getIsActive())).findFirst();
					}
					updateConversationActiveState(contactDocument, conversations);
					if(activeConv.isPresent()) {
						createConversationSwitchActivities(contactDocument.getE_id(), new Date(),new Date(),activeConv.get(), contactDocument);
					}
					firebaseService.twinOnWeb(contactDocument.getE_id(),contactDocument.getB_id(), contactDocument.getM_c_id());
				}
			}
		}
			
	}
	public void updateConversationActiveState(ContactDocument contactDocument, List<ContactDocument> conversations) {
		if (CollectionUtils.isNotEmpty(conversations) && !BooleanUtils.isTrue(contactDocument.getIsActive())) {
			List<ContactDocument> contactDocumentToBeUpserted = new ArrayList<>();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			for (ContactDocument doc : conversations) {
				ContactDocument contactForES = new ContactDocument();
				contactForES.setM_c_id(doc.getM_c_id());
				contactForES.setUpdatedAt(df.format(new Date()));
				contactForES.setIsActive(false);
				if (doc.getC_id().equals(contactDocument.getC_id())) {
					contactForES.setIsActive(true);
				}
				contactDocumentToBeUpserted.add(contactForES);
			}
			if (CollectionUtils.isNotEmpty(contactDocumentToBeUpserted)) {
				long start = System.currentTimeMillis();
				log.info("updating conversation active state in bulk");
				BulkUpsertPayload<ContactDocument> bulkUpsertContacts = new BulkUpsertPayload<>(
						contactDocumentToBeUpserted, contactDocument.getE_id(), contactDocument.getE_id(),
						Constants.Elastic.CONTACT_INDEX);
				try {
					elasticSearchService.performBulkRequestWithRefresh(bulkUpsertContacts, OpType.UPDATE,
							RefreshPolicy.IMMEDIATE);
				} catch (Exception e) {
					log.error("Exception while updating conversations active state in bulk", e);
				}
				long elapsedTime = System.currentTimeMillis() - start;
				log.info(
						"Profiled time taken to execute updating conversations active state in bulk: {} milliseconds",
						elapsedTime);
			}
		}
	}

	@Override
	public void createConversationSwitchActivities(Integer accountId, Date swatichFromActivityDate, Date swatichToActivityDate,
			ContactDocument fromConv, ContactDocument toConv) {
		ActivityDto switchedFromActivity = ActivityDto.builder().mcId(toConv.getM_c_id()).created(swatichFromActivityDate)
				.updated(swatichFromActivityDate).activityType(ActivityType.CONVERSATION_SWITCHED_FROM)
				.activityMeantFor(ActivityMeantFor.BUSINESS_USER)
				.conversationSwitchedFrom(
						new CustomerInfo(fromConv.getC_id(), fromConv.getC_name(), fromConv.getM_c_id()))
				.accountId(accountId).businessId(toConv.getB_id()).build();
		saveActivity(switchedFromActivity);

		// create conversation switched to activity
		ActivityDto switchedToActivity = ActivityDto.builder().mcId(fromConv.getM_c_id()).created(swatichToActivityDate)
				.updated(swatichToActivityDate).activityType(ActivityType.CONVERSATION_SWITCHED_TO)
				.activityMeantFor(ActivityMeantFor.BUSINESS_USER)
				.conversationSwitchedTo(new CustomerInfo(toConv.getC_id(), toConv.getC_name(), toConv.getM_c_id()))
				.accountId(accountId).businessId(fromConv.getB_id()).build();
		saveActivity(switchedToActivity);
	}
	private MessageDocument saveActivity(ActivityDto activity) {
		conversationActivityService.persistActivityInDatabase(activity, null);
		return conversationActivityService.persistActivityInES(activity);
	}

	@Override
	public CustomerDTO getActiveCustomerAndUpdateConversationStateForAppointment(CustomerDTO customerDTO,
			Integer businessId, Integer accountId, ContactDocument contactDocument) {
		String fromNumber = customerDTO.getPhoneE164();
		String email = customerDTO.getEmailId();
		List<CustomerDTO> customers = contactService.findAllCustomer(fromNumber, email, businessId, accountId);
		if (CollectionUtils.isNotEmpty(customers) && customers.size() > 1) {
			Optional<ContactDocument> activeConv=Optional.empty();
			Map<Integer, CustomerDTO> customerMap = customers.stream()
					.collect(Collectors.toMap(CustomerDTO::getId, c -> c));
			BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
			boolQueryBuilder.must(QueryBuilders.termsQuery("c_id", customerMap.keySet()));
			ESQueryBuilderRequest<ContactDocument> esQueryBuilderRequest = ESQueryBuilderRequest
					.buildESQueryBuilderRequest(0, 100, ContactDocument.class, accountId,
							Constants.Elastic.CONTACT_INDEX, boolQueryBuilder, null, null, null);
			List<ContactDocument> conversations = elasticSearchService
					.getResultUsingQueryBuilder(esQueryBuilderRequest);
			if(CollectionUtils.isNotEmpty(conversations)) {
				activeConv=conversations.stream().filter(c->BooleanUtils.isTrue(c.getIsActive())).findFirst();
			}
			updateConversationActiveState(contactDocument, conversations);
			if(activeConv.isPresent()) {
				createConversationSwitchActivities(contactDocument.getE_id(), new Date(),new Date(),activeConv.get(), contactDocument);
			}
//			firebaseService.twinOnWeb(contactDocument.getE_id(),contactDocument.getB_id());
		}
		return customerDTO;
	}
	
	@Override
	public void updateRobinResponseTypeInMessage(MessageDocument messageDocument,
			MessageDocument.RobinResponseType robinResponseType) {
		if (robinResponseType != null) {
			if (MessageDocument.RobinResponseType.DEFAULT.equals(robinResponseType)) {
				String msgBody = MessengerUtil.decryptMessage(messageDocument);
				messageDocument.setUnansweredFaqType(MessageDocument.UnansweredFaqType.UNANSWERED);
				if (StringUtils.isNotEmpty(msgBody) && StringUtils.endsWith(msgBody, "?")
						&& MessengerUtil.isWordCountWithinRange(msgBody)) {
					messageDocument.setUnansweredFaqType(MessageDocument.UnansweredFaqType.UNANSWERED_QUALIFIED);
				}
			} else {
				messageDocument.setUnansweredFaqType(MessageDocument.UnansweredFaqType.ANSWERED);
			}
			messageDocument.setRobinResponseType(robinResponseType);
			try {
				messengerContactService.updateMessageOnES(messageDocument, messageDocument.getE_id());
			} catch (Exception e) {
				log.error("Exception while updating robin response type in message doc c_id :{}, exception :{}",
						messageDocument.getC_id(), e);
			}
		}
	}
	
	@Override
	public void validateWidgetApiKey(Long businessNumber, String apiKey) throws Exception {
		if(apiKey == null || apiKey.isEmpty()){
			throw new NotFoundException(ErrorCode.API_KEY_IS_NULL);
		}

		BusinessDTO business = businessService.getBusinessByBusinessNumber(businessNumber);
		if (business == null) {
			log.info("validateWidgetApiKey: business not found by :: {}", businessNumber);
			throw new NotFoundException(ErrorCode.INVALID_CHAT_SEND_ENTERPRISE_BUSINESS_NO);
		}

		Integer accountId = business.getBusinessId();
		if(business.getEnterpriseId() != null) {
			accountId = business.getEnterpriseId();
		}
		//Adding widget_api_key validation with old_api_key for rollins - Due to account split into 3, widget_api_key got change
		String accountForOldWidgetValidation=CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("account_for_old_widget_api_key","1427828");
		Set<Integer> accountForOldWidgetValidationList=ControllerUtil.getTokensListIntegerFromString(accountForOldWidgetValidation);
		if (CollectionUtils.isNotEmpty(accountForOldWidgetValidationList) &&
				accountForOldWidgetValidationList.contains(accountId)) {
			if(!businessService.validateOldWidgetApiKey(accountId, apiKey)) {
				log.info("validateOldWidgetApiKey: validation failed for business :: {} and apiKey :: {} ", accountId, apiKey);
				if(!businessService.validateWidgetApiKey(accountId, apiKey)) {
					log.info("validateWidgetApiKey: validation failed for business :: {} and apiKey :: {} ", accountId, apiKey);
					throw new NotAuthorizedException(ErrorCode.INVALID_API_KEY);
				}
			}
		} else {
			if(!businessService.validateWidgetApiKey(accountId, apiKey)) {
				log.info("validateWidgetApiKey: validation failed for business :: {} and apiKey :: {} ", accountId, apiKey);
				throw new NotAuthorizedException(ErrorCode.INVALID_API_KEY);
			}
		}
	}
	
	@Override
	public BusinessAPIKeyMessage validateApiKey(String businessId, String apiKey) throws Exception
	{
		String fieldName = businessId + "#apiKey";
		Object apiKeyMessageValue=redisHandler.getData(buildWebChatKey(businessId),
				fieldName);
		if(Objects.isNull(apiKeyMessageValue))
		{
			//validate it thoriugh DB and save
			//Business business = partnerAPIController.getBusinessIfValid(apiKey, businessId);
			BusinessDTO business =businessService.validateWidgetApiKey(businessId,apiKey,false);
			return addApiKeyDataToCache(apiKey, business);
		}
		else
		{
			// validate it from Redis cache
			BusinessAPIKeyMessage apiKeyMsg=ControllerUtil.getObjectFromJsonText(apiKeyMessageValue.toString(), BusinessAPIKeyMessage.class);
			if(apiKeyMsg!=null && !apiKey.equalsIgnoreCase(apiKeyMsg.getApiKey()))
			{
				// invalid API key
				throw new NotAuthorizedException(ErrorCode.INVALID_API_KEY);
			}
			return apiKeyMsg;
		}
	}
	
	private String buildWebChatKey(String businessId) {
		return String.format("%s:%s", Constants.WEBCHAT_KEY_PREFIX, businessId);
	}
	
	private BusinessAPIKeyMessage addApiKeyDataToCache(String apiKey, BusinessDTO business) {
		String fieldName = business.getBusinessNumber() + "#apiKey";
		BusinessAPIKeyMessage apiKeyMessage = prepareBusinessAPIKeyMessage(business);
		if (apiKeyMessage != null) {
			apiKeyMessage.setApiKey(apiKey);
		}
		if (business.getEnterpriseId() != null) {
			BusinessDTO enterpriseDto = businessService.getBusinessDTO(business.getEnterpriseId());
			apiKeyMessage.setEnterprise(prepareBusinessAPIKeyMessage(enterpriseDto));
		}
		if (apiKeyMessage != null) {
			redisHandler.putData(buildWebChatKey(business.getBusinessNumber().toString()),
					fieldName, ControllerUtil.getJsonTextFromObject(apiKeyMessage));
		}
		return apiKeyMessage;
	}
	
	@Override
	public BusinessAPIKeyMessage prepareBusinessAPIKeyMessage(BusinessDTO business) {
		BusinessAPIKeyMessage apiKeyMessage = null;
		if (business != null) {
			apiKeyMessage = new BusinessAPIKeyMessage();
			apiKeyMessage.setId(business.getBusinessId());
			apiKeyMessage.setAccountType(business.getAccountType());
			apiKeyMessage.setType(business.getType());
			apiKeyMessage.setBusinessId(business.getBusinessNumber());
			apiKeyMessage.setName(business.getBusinessName());
			apiKeyMessage.setAlias1(business.getBusinessAlias());
		}
		return apiKeyMessage;
	}

	@Override
	public void clickTracking(ClickTrackingRequest request) {
		log.info("Audit clickTracking event : {}", request);
		ClickTrackingEvent event = new ClickTrackingEvent(request.getElementId(), request.getAccountId(), request.getClickType(), request.getSubType());
		clickTrackingEventsRepository.save(event);
	}
	
}
