package com.birdeye.messenger.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.facebook.Attachments;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.facebook.GetUserDetailsMessage;
import com.birdeye.messenger.dto.facebook.Messaging;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.enums.FacebookMessageStatusEnum;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.SUPPORTED_UPLOAD_FILES;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.service.BusinessServiceImpl;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FacebookReceiveEventHandler extends MessageEventHandlerAbstract {

    private final MessengerEvent EVENT = MessengerEvent.FACEBOOK_RECEIVE;

    @Autowired
    protected MessengerContactRepository messengerContactRepository;

    @Autowired
    protected FacebookMessageRepository facebookMessageRepository;

    @Autowired
    protected SocialService socialService;

    @Autowired
    protected FacebookEventService facebookEventService;

    @Autowired
    private ContactService contactService;

    @Autowired
    private BusinessServiceImpl businessServiceImpl;

    @Autowired
    private MessengerMessageService messengerMessageService;

    @Autowired
    private MessengerMediaFileService messengerMediaFileService;

    @Autowired
    private PulseSurveyService pulseSurveyService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private SpamDetectionService spamDetectionService;
    
    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
            Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
            String businessFacebookId;
            if (msg.getMessage() != null && msg.getMessage().getIs_echo()) {
                businessFacebookId = msg.getSender().getId();
            } else {
                businessFacebookId = msg.getRecipient().getId();
            }
            // Integer businessId =
            // platformDbRepository.getBusinessIdByFacebookPageId(businessFacebookId);
            Integer businessId = socialService.getBusinessIdByFacebookPageId(businessFacebookId);
            if (businessId != null) {
                businessDTO = communicationHelperService.getBusinessDTO(businessId);
            } else {
                businessDTO = null;
                log.warn("[Facebook Receive Event]: No valid business mapping found for facebook page: {} ",
                        businessFacebookId);
            }
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
		Messaging messaging = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
	    CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (Objects.isNull(customerDTO)) {
			MessengerContact contact = getMessengerContact(messageDTO);
			if (contact.getCustomerId() != null && Objects.isNull(messageDTO.getCustomerDTO())) {
				customerDTO = contactService.findById(contact.getCustomerId());
				messageDTO.setCustomerDTO(customerDTO);
			}
			if (Objects.isNull(customerDTO)) {
				customerDTO = messageDTO.getCustomerDTO();
			}
			MessageTag messageTag = getMessageTag(messageDTO);
			if (Objects.nonNull(messaging) && Objects.nonNull(messaging.getMessage()) && !messaging.getMessage().getIs_echo()) {
				spamDetectionService.spamDetectionAllChannels(messageDTO, contact, customerDTO, messageTag);
			}
		}
        return customerDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
        Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
        if (msg.getMessage() != null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
            return MessageTag.INBOX;
        } else if (dto.getSendEmailNotification()) {
            return MessageTag.UNREAD;
        } else {
            return MessageTag.INBOX;
        }
    }

    /*
     * Setting reciepientId as from and senderId as To bcz it is being set this way
     * in Platform.
     * Fixing this would require additional migration
     * Data is correctly persisted in DB
     * 
     */
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(),
                getMessengerContact(messageDTO).getId());
        if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
            messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
                    .map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
                            messengerMediaFile.getExtension()))
                    .collect(
                            Collectors.toList()));
        }
        messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
        messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        if (messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)) {
            messageDocumentDTO.setSpam(true);
        } else {
            messageDocumentDTO.setSpam(false);
        }
        return messageDocumentDTO;
    }

    @Override
    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setMsgId(dto.getConversationDTO().getId());
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        if (dto.getConversationDTO().getCreateDate() != null) {
            notificationRequest.setLastMsgTime(dto.getConversationDTO().getCreateDate().getTime());
        } else {
            notificationRequest.setLastMsgTime(new Date().getTime());
            log.info("onReceiveSMS: Both sms sentOn and createDate found null for businessId {} smsID {} customer {}",
                    businessDTO.getBusinessId(), dto.getConversationDTO().getId(), dto.getCustomerDTO().getPhone());
        }
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.FACEBOOK.name());
        lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.FACEBOOK.getSourceId());
        lastMessageMetadataPOJO.setLastMessageSource(Source.FACEBOOK.getSourceId());
        FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
        Messaging messaging = dto.getEntry().get(0).getMessaging().get(0);
        Date lastMsgOn = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(lastMsgOn);
        if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {

            lastMessageMetadataPOJO.setLastFbSentAt(date);
        } else {
            lastMessageMetadataPOJO.setLastFbReceivedAt(date);
        }
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        // TODO: remove this comment once all SEND and RECEIVE api migrated to
        // messenger-service
        /*
         * LastMessageMetaData lastMessageMetaData =
         * MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
         * lastMessageMetaData.setLastMessageSource(Source.FACEBOOK.getSourceId());
         * lastMessageMetaData.setLastReceivedMessageSource(Source.FACEBOOK.getSourceId(
         * )); SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
         * lastMessageMetaData.setLastFbSentAt(sdf.format(new Date()));
         * messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(
         * lastMessageMetaData));
         */
        if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {
            messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        }
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
        messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
        Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
            if (messageDTO.getMessengerMediaFiles().size() > 1) {
                messengerContact.setLastMessage("Received attachments");
                if (msg.getMessage() != null && msg.getMessage().getIs_echo()) {
                    messengerContact.setLastMessage("Sent attachments");
                }
            } else {
                messengerContact.setLastMessage("Received an attachment");
                if (msg.getMessage() != null && msg.getMessage().getIs_echo()) {
                    messengerContact.setLastMessage("Sent an attachment");
                }
            }

        } else {
            // messengerContact.setLastMessage(dto.getConversationDTO().getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact,
                dto.getConversationDTO().getEncrypted(), messengerContact.getFacebookId(),
                businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }

    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
            Messaging messaging = dto.getEntry().get(0).getMessaging().get(0);
            String customerFacebookId;
            String pageId = messaging.getRecipient().getId();
            String userId = messaging.getSender().getId();
            if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {
                customerFacebookId = messaging.getRecipient().getId();
                userId = customerFacebookId;
                pageId = messaging.getSender().getId();
            } else {
                customerFacebookId = messaging.getSender().getId();
                userId = customerFacebookId;
                pageId = messaging.getRecipient().getId();
            }
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactRepository.findByFacebookId(customerFacebookId,
                    businessDTO.getBusinessId());

            if (messengerContact == null) {
                // not found in db then create new
                // if(messaging.getMessage().getIs_echo()){
                // // send event but messenger contact is not present
                // log.error("contact not found for send message with echo flag:
                // "+messaging.getMessage().getIs_echo());
                // // throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
                // } else {
                // receive event make new messenger contact
                log.info("fetching user details from social for facebook user Id {}: ", userId);
                UserDetailsMessage userDetailsMessage = socialService
                        .getFacebookUserInfo(new GetUserDetailsMessage(pageId,
                                userId));
                KontactoRequest kontactoRequest = createKontactoRequest(userDetailsMessage, userId,
                        businessDTO.getCountryCode());
                kontactoRequest.setBusinessId(businessDTO.getBusinessId());
                log.info("Requesting contact service to get/create customer for facebook user Id {}", userId);
                CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
                        businessDTO.getRoutingId(),
                        -8);
                log.info("Customer retrieved Id {} from contact service for facebook user Id {}", customerDTO.getId(),
                        userId);

                messageDTO.setCustomerDTO(customerDTO);
                messengerContact = createMessengerContactFromFacebook(userDetailsMessage,
                        userId, messageDTO);
            }
        }
        messageDTO.setMessengerContact(messengerContact);

        return messengerContact;
    }

    private KontactoRequest createKontactoRequest(UserDetailsMessage userDetailsMessage, String facebookUserId,
            String countryCode) {
        KontactoRequest kontactoRequest = new KontactoRequest();
        String custName = ControllerUtil.truncateLongContactName(userDetailsMessage.getFirst_name() + " " + userDetailsMessage.getLast_name());
        kontactoRequest.setName(custName);
        kontactoRequest.setEmailId(facebookUserId + "@fb.com");
        kontactoRequest.setSource(KontactoRequest.FACEBOOK);
        LocationInfo locationInfo = new LocationInfo();
        locationInfo.setCountryCode(countryCode);
        kontactoRequest.setLocation(locationInfo);
        return kontactoRequest;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if (Objects.isNull(messageId)) {
            FacebookMessageRequest dto = (FacebookMessageRequest) messageDTO;
            messageId = dto.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        messageDTO.setMsgTypeForResTimeCalc("R"); // default - since its a receive handler. See
                                                  // processFBReceivedMessage() for actual type
        messageDTO.setSource(Source.FACEBOOK.getSourceId());
        FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
        Messaging messaging = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
        log.info("Facebook recieve request body: {} ", facebookMessageRequest);
        if (isUnsupportedWebhookEvent(messaging)) {
            log.info("Ignoring unsupported event for FB receive");
            return null;
        }

        Optional<Lock> lockOpt = Optional.empty();
        try {
            String fbUserId = messaging.getSender().getId();
            if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {
                fbUserId = messaging.getRecipient().getId();
            }
            String lockKey = Constants.FB_USER_ID_PREFIX + fbUserId;
            lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
            if (!lockOpt.isPresent()) {
                log.info("Lock is already acquired for the key:{}", lockKey);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.FACEBOOK_RECEIVE_EVENT_DELAYED_QUEUE,
                        facebookMessageRequest);
                return null;
            }
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            if (Objects.isNull(businessDTO) || !BusinessDTO.isActive(businessDTO)) {
                log.error("BusinessDTO returned null by core-service for request {} ", facebookMessageRequest);
                return null;
            }
            CustomerDTO customerDTO = getCustomerDTO(messageDTO);
            MessengerContact messengerContact = getMessengerContact(messageDTO);
            if (messaging.getDelivery() != null || messaging.getRead() != null) {
                // update facebook read and delivery status
                facebookEventService.updateFbMessageStatus(messaging,messengerContact.getId());
                return null;
            } else {
                FacebookMessageStatusEnum facebookMessageStatus = FacebookMessageStatusEnum.received;
                if (messaging.getMessage().getIs_echo())
                    facebookMessageStatus = FacebookMessageStatusEnum.sent;
                // BusinessDTO businessDTO=getBusinessDTO(messageDTO);
                if (messageDTO.getBusinessDTO() != null && messageDTO.getBusinessDTO().getBusinessId() != null
                        && BusinessDTO.isActive(messageDTO.getBusinessDTO())) {
                    messageDTO.setBusinessDTO(businessDTO);
                } else {
                    log.error("BusinessDTO returned is null or inactive by core-service for request {} ",
                            facebookMessageRequest);
                    return null;
                }
                Integer isMessengerEnabled = businessServiceImpl
                        .isMessengerEnabled(messageDTO.getBusinessDTO().getAccountId());
                if (!Integer.valueOf(0).equals(isMessengerEnabled)) {
                    processFBReceivedMessage(messageDTO, facebookMessageStatus);
                    if (facebookMessageRequest.getConversationDTO() == null
                            || facebookMessageRequest.getConversationDTO().getId() == null) {
                        log.error("Error in saving facebook message for request {} ", facebookMessageRequest);
                        return null;
                    }
                    // handle PulseSurveyContext
                    PulseSurveyContext context = null;
                    try {
                        context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO,
                                messageDTO.getBusinessDTO());
                        if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())) {
                            customerDTO.setOngoingPulseSurvey(
                                    PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
                        } else {
                            customerDTO.setOngoingPulseSurvey(false);
                        }
                        messageDTO.setCustomerDTO(customerDTO);
                    } catch (Exception ex) {
                        log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
                    }
                    super.handle(messageDTO);
                } else {
                    log.info(
                            "Message Discarded. Messenger event received but Facebook Messenger in not enabled for business {}",
                            messageDTO.getBusinessDTO().getBusinessId());
                }
            }
            return null;
        } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }
        }
    }

    private boolean isUnsupportedWebhookEvent(Messaging messaging) {
        if (messaging != null && messaging.getMessage() != null
                && CollectionUtils.isNotEmpty(messaging.getMessage().getAttachments())) {
            List<Attachments> attachments = messaging.getMessage().getAttachments();
            for (Attachments attachment : attachments) {
                boolean isPayloadInvalid = (attachment.getPayload() == null || attachment.getPayload().getUrl() == null);
                if (isPayloadInvalid) {
                    String messageText = messaging.getMessage().getText();
                    if (StringUtils.isNotEmpty(messageText)) {
                        // Attachment is invalid but text is present, clean attachments and treat as supported
                        messaging.getMessage().setAttachments(null);
                        return false;
                    }
                    // No valid payload and no text => unsupported
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isNotUnknownType(Attachments attachment) {
        // For Fallback attachments - Url contains : https://l.facebook.com/l.php
        // For Marketplace attachments - Url contains :
        // https://www.facebook.com/marketplace/item/3136722169750132/
        boolean unknown = false;
        if (attachment.getPayload() != null && attachment.getPayload().getUrl() != null)
            unknown = attachment.getPayload().getUrl().contains("https://l.facebook.com/l.php")
                    || attachment.getPayload().getUrl().contains("https://www.facebook.com/marketplace/item");
        if (unknown)
            attachment.setType("template");

        return !unknown;
    }

    private boolean isNotTemplateMessage(Attachments attachment) {
        return !"template".equalsIgnoreCase(attachment.getType());
    }

    private void updateMessengerMediaFIleDTO(MessengerMediaFileDTO messengerMediaFileDTO) {
        for (SUPPORTED_UPLOAD_FILES filetype : SUPPORTED_UPLOAD_FILES.values()) {
            if (messengerMediaFileDTO.getUrl() != null
                    && messengerMediaFileDTO.getUrl().contains(filetype.getExtension())) {
                messengerMediaFileDTO.setFileExtension(filetype.getExtension());
                if (messengerMediaFileDTO.getFileExtension().equalsIgnoreCase("mp4")) {
                    messengerMediaFileDTO.setContentType("video/mp4");
                    break;
                } else {
                    File file = new File(messengerMediaFileDTO.getUrl());
                    URLConnection connection = null;
                    try {
                        connection = file.toURL().openConnection();
                    } catch (IOException e) {
                        log.error("Error in obtaining file mime type for received attachment. Error message : {}",
                                e.getMessage());
                    }
                    String mimeType = connection.getContentType();
                    messengerMediaFileDTO.setContentType(mimeType);
                    messengerMediaFileDTO.setContentSize(String.valueOf(connection.getContentLength()));
                    break;
                }
            } else {
                messengerMediaFileDTO.setFileExtension("others");
            }
        }
    }

    private MessengerContact createMessengerContactFromFacebook(UserDetailsMessage userDetailsMessage, String userId,
            MessageDTO messageDTO) {
        // get facebook user details
        MessengerContact messengerContact = messengerContactRepository.findByFacebookId(userId,
                messageDTO.getBusinessDTO().getBusinessId());
        if (Objects.isNull(messengerContact)) {
            messengerContact = new MessengerContact();
            messengerContact.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
            messengerContact.setFacebookId(userId);
            messengerContact.setImageUrl(userDetailsMessage.getProfile_pic());
            messengerContact.setCustomerId(messageDTO.getCustomerDTO().getId());
            messengerContact.setCreatedAt(new Date());
            messengerContactRepository.saveAndFlush(messengerContact);
        }
        return messengerContact;
    }

    private MessageDTO processFBReceivedMessage(MessageDTO messageDTO, FacebookMessageStatusEnum facebookMessageStatus)
            throws Exception {
        if (FacebookMessageStatusEnum.sent.equals(facebookMessageStatus))
            messageDTO.setMsgTypeForResTimeCalc("S");
        FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
        Messaging messaging = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
        messageDTO.setMessengerContact(getMessengerContact(messageDTO));
        FacebookMessage facebookMessage = facebookEventService.saveFacebookMessage(messageDTO,
                messaging.getSender().getId(), messaging.getRecipient().getId(), messaging.getMessage().getMid(),
                facebookMessageStatus);
        log.debug("Facebook message Id:{} for Messenger contact Id {}", facebookMessage.getId(),
                messageDTO.getMessengerContact().getId());
        if (facebookMessage.getId() != null) {
            // Event with attachment
            saveMediaFiles(messageDTO, messaging, facebookMessage.getId());
            MessengerMessageMetaData messageMetaData = new MessengerMessageMetaData();
            messageMetaData.setCommunicationDirection(CommunicationDirection.RECEIVE);
            if (FacebookMessageStatusEnum.sent.equals(facebookMessageStatus)) {
                messageMetaData.setCommunicationDirection(CommunicationDirection.SEND);
            }
            ConversationDTO conversationDTO = new ConversationDTO(facebookMessage, messageMetaData);
            facebookMessageRequest.setConversationDTO(conversationDTO);
            messengerMessageService.saveMessengerMessage(conversationDTO, null);
            messageDTO.setSendEmailNotification(true);
            if (FacebookMessageStatusEnum.sent.equals(facebookMessageStatus))
                messageDTO.setSendEmailNotification(false);
            return messageDTO;
        }
        return null;
    }

    private void saveMediaFiles(MessageDTO messageDTO, Messaging messaging, Integer messageId) {
        List<Attachments> attachments = messaging.getMessage().getAttachments();
        List<MessengerMediaFile> messengerMediaFiles = new ArrayList<MessengerMediaFile>();
        Map<String, String> urlExtensionMap = new HashMap<String, String>();
        if (CollectionUtils.isNotEmpty(attachments)) {
            for (Attachments attachment : attachments) {
                MessengerMediaFile mediaFile = null;
                if (isNotUnknownType(attachment) && isNotTemplateMessage(attachment)) {
                    MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(
                            new MessengerMediaFile(attachment));
                    updateMessengerMediaFIleDTO(messengerMediaFileDTO);
                    mediaFile = new MessengerMediaFile(messengerMediaFileDTO);
                    mediaFile.setMessageId(messageId);
                    messengerMediaFiles.add(mediaFile);
                    urlExtensionMap.put(mediaFile.getUrl(), messengerMediaFileDTO.getFileExtension());
                }
            }
            if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
                messengerMediaFileService.saveAll(messengerMediaFiles);
                messengerMediaFiles.forEach(media -> {
                    media.setExtension(urlExtensionMap.get(media.getUrl()));
                });
                messageDTO.setMessengerMediaFiles(messengerMediaFiles);
            }
        }
    }

    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
            Messaging msg = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
            if (msg.getMessage() != null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
                userDTO = communicationHelperService.getUserDTO(Constants.FACEBOOK_DUMMY_USER);
                facebookMessageRequest.setUserDTO(userDTO);
            }
        }
        return userDTO;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        // Do Nothing
    }
}