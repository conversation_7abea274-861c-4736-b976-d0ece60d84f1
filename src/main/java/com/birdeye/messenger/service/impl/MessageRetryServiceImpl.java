/**
 * 
 */
package com.birdeye.messenger.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.MessageRetry;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.dto.MessengerMessage.Message;
import com.birdeye.messenger.service.MessageRetryClient;
import com.birdeye.messenger.service.MessageRetryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MessageRetryServiceImpl implements MessageRetryService {

	@Autowired
	private SmsMessageRetryClient smsMessageRetryClient;

	@Autowired
	private EmailmessageRetryClient emailmessageRetryClient;

	@Override
	public Message retryMessage(Integer id, Integer mcId, Integer accountId, Integer userId, Integer sourceId) {

		MessageRetry retryMessage = getRetryReuest(id, mcId, accountId, userId, sourceId);
		log.info("MessageRetry request parameters {}", retryMessage.toString());
		MessageRetryClient client = getMessageClient(retryMessage.getSourceId());
		return client.retrySendMessage(retryMessage);
	}

	private MessageRetryClient getMessageClient(Integer sourceId) {
		Source source = Source.getValue(sourceId);
		log.debug("Incoming source : {}", source);
		MessageRetryClient client = null;
		switch (source) {
		case SMS:
			client = smsMessageRetryClient;
			break;
		case EMAIL:
			client = emailmessageRetryClient;
			break;
		default:
			client = smsMessageRetryClient;
			break;
		}
		return client;
	}
	
	private MessageRetry getRetryReuest(Integer id, Integer mcId, Integer accountId, Integer userId,
			Integer sourceId) {
		return new MessageRetry(id, mcId, accountId, userId, sourceId);
	}

}
