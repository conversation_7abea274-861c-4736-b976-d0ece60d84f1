package com.birdeye.messenger.service.impl;

import java.io.Serializable;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.MessagingScheduleRequest;
import com.birdeye.messenger.dto.SamayPayload;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.SamayService;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.JSONUtils;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SamayServiceImpl implements SamayService {

	private final KafkaService kafkaService;
	
	@Override
	public void submitToScheduler(PulseSurveyContext existingContext, String timeUnit, long timeUnitValue) throws JsonProcessingException {
		log.info("Submit to Scheduler for contextId {}, timeUnit: {} and value: {}", existingContext.getId(), timeUnit, timeUnitValue);
		Long expiry = DateUtils.samayExpiryDateCalculation(timeUnit, timeUnitValue);
		
		SamayPayload samayPayload = new SamayPayload(existingContext.getId(), existingContext.getStatus(), expiry);
		
		String payload = JSONUtils.toJSON(samayPayload);
		
		MessagingScheduleRequest schedulingRequest = new MessagingScheduleRequest();
		schedulingRequest.setApplication("Messenger");
		schedulingRequest.setExpiry(expiry);
		schedulingRequest.setPayload(payload);
		schedulingRequest.setTopic(KafkaTopicEnum.RECEIVE_SAMAY_EVENT.getTopicValue());
		
		try {
			log.info("Submit to Scheduler, Payload: {}", payload);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SAMAY_SCHEDULE, null, schedulingRequest);
		} catch (Exception e) {
            log.error("Exception while submitToScheduler for contextId :: {} {}", existingContext.getId(), e);
        }
	}
	
	@Override
	public void publishToScheduler(Serializable payload, long scheduleAt, KafkaTopicEnum responseTopic) {
		String samayPayload = JSONUtils.toJSON(payload);
		MessagingScheduleRequest schedulingRequest = new MessagingScheduleRequest();
		schedulingRequest.setApplication("Messenger");
		schedulingRequest.setExpiry(scheduleAt);
		schedulingRequest.setPayload(samayPayload);
		schedulingRequest.setTopic(responseTopic.getTopicValue());
		try {
			log.info("Publish to Scheduler, Payload: {}", payload);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SAMAY_SCHEDULE, null, schedulingRequest);
		} catch (Exception e) {
			log.error("Exception while publishToScheduler for payload :: {} {}", payload, e);
		}
	}
}
