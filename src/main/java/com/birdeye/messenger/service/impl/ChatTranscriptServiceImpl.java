package com.birdeye.messenger.service.impl;

import com.amazonaws.util.CollectionUtils;
import com.birdeye.messenger.dto.ChatTranscriptRequest;
import com.birdeye.messenger.enums.ChatTranscriptFormatEnum;
import com.birdeye.messenger.enums.ChatTranscriptTypeEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.dto.UpdateUserTranscriptConfigRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ChatTranscriptRepositoryService;
import com.birdeye.messenger.service.ChatTranscriptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.util.CollectionUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;

import jakarta.transaction.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatTranscriptServiceImpl implements ChatTranscriptService {

    private final ChatTranscriptRepositoryService chatTranscriptRepositoryService;

    private final BusinessService businessService;

    @Override
    @Transactional
    public void enableChatTranscript(ChatTranscriptRequest chatTranscriptRequest) {
         if(validateRequest(chatTranscriptRequest)){
             log.info("Invalid chatTranscriptRequest {}",chatTranscriptRequest);
             throw new BadRequestException(ErrorCode.INVALID_CHAT_TRANSCRIPT_REQUEST,ErrorCode.INVALID_CHAT_TRANSCRIPT_REQUEST.getErrorMessage());
         }
         chatTranscriptRepositoryService.enableChatTranscript(chatTranscriptRequest);
         chatTranscriptRepositoryService.evictChatTranscriptConfigCache(chatTranscriptRequest);
         if(!CollectionUtils.isNullOrEmpty(chatTranscriptRequest.getUserTranscriptConfigs())){
            businessService.updateUserTranscriptConfig(chatTranscriptRequest);
         }
    }

    private boolean validateRequest(ChatTranscriptRequest chatTranscriptRequest) {
        if(!CollectionUtils.isNullOrEmpty(chatTranscriptRequest.getUserTranscriptConfigs())){
            for (ChatTranscriptRequest.UserTranscriptConfig userTranscriptConfig : chatTranscriptRequest.getUserTranscriptConfigs()) {
                UpdateUserTranscriptConfigRequest.UserTranscriptConfig config = new UpdateUserTranscriptConfigRequest.UserTranscriptConfig();
                if (!userTranscriptConfig.getTranscriptType().equals(ChatTranscriptTypeEnum.ALL.getName())
                        && !userTranscriptConfig.getTranscriptType().equals(ChatTranscriptTypeEnum.ASSIGNED_TO_ME.getName())) {
                    return true;
                }
            }
        }
        if (!chatTranscriptRequest.getTranscriptFormat().equals(ChatTranscriptFormatEnum.HTML.getName())
                   &&!chatTranscriptRequest.getTranscriptFormat().equals(ChatTranscriptFormatEnum.ADF.getName())){
            return true;
        }
        return false;
    }

    @Override
    public void disableChatTranscript(ChatTranscriptRequest chatTranscriptRequest) {
         chatTranscriptRepositoryService.disableChatTranscript(chatTranscriptRequest);
         chatTranscriptRepositoryService.evictChatTranscriptConfigCache(chatTranscriptRequest);
    }
}
