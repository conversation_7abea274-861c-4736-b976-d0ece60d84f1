package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class AdfChatTranscriptServiceImpl extends ChatTranscriptConsumerServiceImpl {

    @Override
    public List<String> getMessageTypesToBeExcluded(Integer accountId) {
        return Arrays.asList(MessageDocument.MessageType.EVENTS.toString(), MessageDocument.MessageType.ACTIVITY.toString(), MessageDocument.MessageType.SURVEY_RESPONSE.toString(), MessageDocument.MessageType.REVIEW.toString());
    }

    protected List<EmailMessageDocument> prepareFormattedEmailMessages(List<MessageDocument> messageDocuments, ContactDocument contactDocument, String timeZoneId) {
        List<EmailMessageDocument> emailMessageDocuments = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();
        for (MessageDocument messageDocument : messageDocuments) {
            EmailMessageDocument emailMessageDocument = new EmailMessageDocument();
            emailMessageDocument.setCreatedDate(MessengerUtil.formatMessageCreatedTime(messageDocument.getCr_date(), timeZoneId));
            emailMessageDocument.setSentON(emailMessageDocument.getCreatedDate());
            emailMessageDocument.setCreatedBy(messageDocument.getCreatedBy());

            String decryptedMessage = MessengerUtil.decryptMessage(messageDocument);
            String msgBody = Objects.nonNull(decryptedMessage) ? decryptedMessage : "";

            if (MessageDocument.MessageType.RICH_CONTENT_CHAT.equals(messageDocument.getMessageType())) {
                try {
                    JsonNode jsonNode = mapper.readTree(msgBody);
                    JsonNode data = jsonNode.get("data");
                    msgBody = data.toString();
                    emailMessageDocument.setMsgBody(msgBody);
                } catch (Exception e) {

                }
                if(StringUtils.isNotBlank(msgBody)) emailMessageDocuments.add(emailMessageDocument);
                continue;
            }

            if (StringUtils.isNotBlank(messageDocument.getVoiceMailUrl())) {
                msgBody = msgBody + "  Voice-mail URL:" + messageDocument.getVoiceMailUrl();
            }

            if (CollectionUtils.isNotEmpty(messageDocument.getMediaFiles())) {
                List<MessageDocument.MediaFile> mediaFiles = NotificationServiceImpl.truncateAttachmentName(messageDocument.getMediaFiles());
                StringJoiner sj = new StringJoiner(" \n");
                mediaFiles.forEach(mediaFile -> {
                    sj.add(mediaFile.getA_url());
                });
                msgBody = msgBody + sj.toString();
            }
            if (MessageDocument.MessageType.CHAT.equals(messageDocument.getMessageType()) && MessageDocument.CommunicationDirection.RECEIVE.equals(messageDocument.getCommunicationDirection())) {
                emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
            }

            emailMessageDocument.setMsgBody(msgBody);
            if(StringUtils.isNotBlank(msgBody)) emailMessageDocuments.add(emailMessageDocument);

        }
        Collections.sort(emailMessageDocuments);
        return emailMessageDocuments;
    }

    @Override
    protected Map<String, Object> prepareFtlDataMap(BusinessDTO businessDTO, MessengerContact messengerContact, ContactDocument contact, List<EmailMessageDocument> emailMessageDocuments) {
        Map<String, Object> data = new HashMap<>();
        CustomerDTO customer = contactService.findByIdNoCaching(contact.getC_id());
        String firstName = StringUtils.isNotBlank(customer.getFirstName()) ? customer.getFirstName() : "unknown";
        String lastName = StringUtils.isNotBlank(customer.getLastName()) ? customer.getLastName() : "unknown";
        String leadSource = StringUtils.isNotBlank(customer.getSource()) ? customer.getSource() : "N/A";
        data.put("firstName", firstName);
        data.put("lastName", lastName);
        data.put("phone", contact.getC_phone());
        data.put("businessName", StringUtils.isBlank(businessDTO.getBusinessName()) ? businessDTO.getBusinessAlias() : businessDTO.getBusinessName());
        data.put("email", contact.getC_email());
        data.put("messages", emailMessageDocuments);
        data.put("leadSource", leadSource);
        return data;
    }

    @Override
    public String getFormat() {
        return "ADF";
    }

    @Override
    protected EmailDTO prepareEmailDto(BusinessDTO businessDTO, ContactDocument contactDocument) {
        EmailDTO emailDTO = super.prepareEmailDto(businessDTO, contactDocument);
        emailDTO.setTextType(EmailDTO.TEXT_TYPE.TEXT);
        return emailDTO;
    }

    @Override
    protected String getTemplateName() {
        return "chat_transcript_adf_format";
    }


}
