package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.ext.sro.NexusTerminateRoomRequest;
import com.birdeye.messenger.ext.sro.NexusTerminateRoomResponse;
import com.birdeye.messenger.ext.sro.NexusVideoRequest;
import com.birdeye.messenger.ext.sro.NexusVideoResponse;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.util.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class NexusServiceImpl implements NexusService{

	@Value("${nexus.url}")
	private String nexusUrl;

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private KafkaService kafkaService;

	@Override
	public void updateFirebaseDb(GenericFirebaseMessage genericFirebaseMessage){
		String url = this.nexusUrl + "/notification/firebase/db/update";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<GenericFirebaseMessage> httpEntity = new HttpEntity<>(genericFirebaseMessage, headers);
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
		if (!res.getStatusCode().is2xxSuccessful())
			throw new MessengerException(ErrorCode.HTTP_CONNECTION_ERROR,
					"Error response from Nexus api updateFirebaseDb");
	}

	@Override
	public void sendSMS(ConversationWrapperMessage conversation) {
		SendConversationRequest request = getSendSMSRequestForMessenger(conversation);
		kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.COMM_SMS_SEND, conversation.getFromBusinessId(), request);
	}

	@Override
	public VideoConversationDTO initiateVideo(Integer bid, Integer conversationId) {
		String url = this.nexusUrl + "/video/create-room";

		HttpEntity<NexusVideoRequest> httpEntity = new HttpEntity<>(new NexusVideoRequest(bid, conversationId), getBaseHeaders());

		ResponseEntity<NexusVideoResponse> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, NexusVideoResponse.class);

		if (!res.getStatusCode().is2xxSuccessful()) {
			throw new MessengerException(ErrorCode.ERROR_CREATING_VIDEO_ROOM);
		}

		return new VideoConversationDTO(res.getBody());
	}

	@Override
	public void terminateRoom(String roomId) {
		Assert.notNull(roomId, "roomId cannot be null");
		log.info("[Video] Terminating video for roomId:{}",roomId);
		String url = this.nexusUrl + "/video/terminate-room";
		NexusTerminateRoomRequest request = new NexusTerminateRoomRequest(roomId);
		HttpEntity<NexusTerminateRoomRequest> httpEntity = new HttpEntity<>(request, getBaseHeaders());	
		ResponseEntity<NexusTerminateRoomResponse> response = null;
		try {
			response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, NexusTerminateRoomResponse.class);
		} catch (Exception e) {
			log.error("[Video] Error occured while terminating video room",e);
			throw new MessengerException(ErrorCode.ERROR_TERMINATING_VIDEO_ROOM);
		}
		if(!response.getStatusCode().is2xxSuccessful() && !response.getBody().isSuccess()) {
			log.error("[Video] Error occured while terminating video room:{}",roomId);
			throw new MessengerException(ErrorCode.ERROR_TERMINATING_VIDEO_ROOM);
		}
	}

	private HttpHeaders getBaseHeaders() {
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		return headers;
	}

	//TODO: Move to DTO converter
	SendConversationRequest getSendSMSRequestForMessenger(ConversationWrapperMessage message){
		if(message == null){
			return null;
		}
		SendConversationRequest sendConversationRequest = new SendConversationRequest();
		sendConversationRequest.setExternalUID(message.getExternalUID());
		sendConversationRequest.setBody(message.getBody());
		sendConversationRequest.setFromPhone(message.getFromPhone());
		sendConversationRequest.setToPhone(message.getToPhone());
		if (message.getToCustomerId() != null) {
			sendConversationRequest.setToCustomerId(Integer.parseInt(message.getToCustomerId()));
		}
		sendConversationRequest.setFromBusinessId(Integer.parseInt(message.getFromBusinessId()));
		if (message.getToBusinessUserId() != null) {
			sendConversationRequest.setToBusinessUserId(Integer.parseInt(message.getToBusinessUserId()));
		}
		sendConversationRequest.setType(message.getType());
		sendConversationRequest.setSubType(message.getSubType());
		sendConversationRequest.setMediaUrl(message.getMediaUrl());
		//TODO: Consider merging review request path, current processing would be done based on messenger type which has duplicate code.
		sendConversationRequest.setParams(getConversationParamsMap(null, message.getType()));
		sendConversationRequest.setIsRetryAckRequired(1);
		if(Objects.nonNull(message.getAccountId())){
			sendConversationRequest.setAccountId(Integer.parseInt(message.getAccountId()));
		}
		return sendConversationRequest;
	}

	/**
	 * Get SMS Parameters Map
	 * @param requestId
	 * @param rType
	 * @return
	 */
	private Map<String, Serializable> getConversationParamsMap(Long requestId, String rType) {
		Map<String, Serializable> params = new HashMap<String, Serializable>();
		if (requestId != null) {
			params.put("rid", requestId);
		}
		if (StringUtils.isNotBlank(rType)) {
			params.put("rType", rType);
		}
		return params;
	}

	@Override
	public void deleteFromFireBaseDB(String topicName) {
		String url = this.nexusUrl +  "/notification/firebase/db/remove";
		HttpHeaders httpHeaders = new HttpHeaders();
		httpHeaders.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<String> request = new HttpEntity<String>(topicName, httpHeaders);
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
		if (!res.getStatusCode().is2xxSuccessful())
			throw new MessengerException(ErrorCode.HTTP_CONNECTION_ERROR,
					"Error response from Nexus api while removing topic");
	}

	@Override
	public Boolean checkSpamFromTwilio(CheckSpamRequestDto checkSpamRequestDto) {
		log.info("Check Spam for the sms Number : {}", checkSpamRequestDto);
		String url = this.nexusUrl + "/sms/lookup-number";
		HttpHeaders headers = new HttpHeaders();
		headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
		headers.set("account-id",checkSpamRequestDto.getAccountId().toString());
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> entity = new HttpEntity<>(checkSpamRequestDto,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<CheckSpamResponseDto> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity,CheckSpamResponseDto.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("checkSpamFromTwilio", startTime, endTime);
		if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
			log.info("Error in response from nexus service to check spam from twilio");
			return null;
		}
		log.info("Response from Nexus from tiwlio : {}",responseEntity.getBody());
		return responseEntity.getBody().getSpam();
	}

	@Override
	@Cacheable(cacheNames = Constants.TEN_DLC_STATUS, key="'ACCID-'.concat(#enterpriseNumber)", unless="#result == null")
	public TenDlcStatusDTO checkTenDlcStatus(Long enterpriseNumber){
		log.info("Check Ten Dlc Status for the enterprise number : {}", enterpriseNumber);
		String url = this.nexusUrl + "/a2p/10DLC/reg-status/dashboard/business/" + enterpriseNumber.toString();
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> entity = new HttpEntity<>(headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<TenDlcStatusDTO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity,TenDlcStatusDTO.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("checkTenDlcStatus", startTime, endTime);
		if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
			log.info("Error in response from nexus service to check Ten Dlc Status");
			return null;
		}
		log.info("Response from Nexus for enterprise ten dlc status : {}",responseEntity.getBody());
		return responseEntity.getBody();
	}
}
