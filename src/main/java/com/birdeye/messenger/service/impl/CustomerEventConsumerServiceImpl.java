package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerEventConsumerServiceImpl implements CustomerEventConsumerService {

    private final MessengerContactService messengerContactService;
    private final MessageService messageService;
    private final BusinessService businessService;
    private final RedisHandler redisHandler;
    private final FirebaseService fcmService;
    private final SmsService smsService;
    private final ConversationActivityService conversationActivityService;
    private final ContactService contactService;
    private final ReviewEventHandlerService reviewEventHandlerService;
    private final NotificationService notificationService;
    private final RedisLockService redisLockService;
    private final KafkaService kafkaService;
    private final ElasticSearchExternalService elasticSearchService;
    private final SpamDetectionService spamDetectionService;
	private final RobinService robinService;
    /**
     * @param customerId - Customer to delete
     */
    @Override
    public void onCustomerDelete(Integer customerId) {
        log.debug("Deleting Customer :{}", customerId);
        try {
            MessengerContact messengerContact = messengerContactService.findByCustomerId(customerId);
            if (Objects.nonNull(messengerContact)) {
            	BusinessDTO businessLite =
                        businessService.getBusinessLiteDTO(messengerContact.getBusinessId());
                Objects.requireNonNull(businessLite);
                //Unlink reviews from conversation
                Integer mcId=messengerContact.getId();
                unlinkReviews(mcId, businessLite);
                //1. Delete Data From Contact Index ( Must not delete from DB tables )
                ESDeleteByIdRequest esDeleteByIdRequest=ESDeleteByIdRequest.builder().id(String.valueOf(mcId)).index(Constants.Elastic.CONTACT_INDEX).routingId(String.valueOf(businessLite.getAccountId())).refreshPolicy(RefreshPolicy.IMMEDIATE).build();
                String id=elasticSearchService.deleteDocumentById(esDeleteByIdRequest);
                if (StringUtils.isNotBlank(id)){
                    //2. Delete From MessengerContact from DB
                    boolean deleted = messengerContactService.deleteMessengerContact(mcId);
                    // 3. Remove data from MessageIndex
                    messageService.deleteMessagesByMcIdWithRefresh(mcId,
                            businessLite.getRoutingId(), false);
                    if (deleted) {
                        messageService.deleteMessagesDataFromDBOnCustomerDelete(mcId, customerId);
                    }
                    redisHandler.evictMessengerCustomerCache(customerId);
                    //14. Push Change Event to FireBase
                    FirebaseDto firebaseDto = new FirebaseDto();
                    firebaseDto.setAccountId(businessLite.getAccountId());
                    firebaseDto.setBusinessId(businessLite.getBusinessId());
                    firebaseDto.setMcId(mcId);
                    fcmService.mirrorOnWeb(firebaseDto);
                } else {
                    log.info("onCustomerDelete: Some Error Occurred {}", customerId);
                }
            } else {
                log.info("onCustomerDelete: Invalid customer id {}", customerId);
            }

        }catch (Exception e){
            log.info("Error in Customer Deletion Customer Id : {} with Exception : {}", customerId, e);
        }
    }

	private void unlinkReviews(Integer mcId, BusinessDTO businessLite) {
        ESFindByIdRequest<ContactDocument> esFindByIdRequest = ESFindByIdRequest.<ContactDocument>builder()
                .id(String.valueOf(mcId)).documentType(ContactDocument.class)
                .index(Constants.Elastic.CONTACT_INDEX).routingId(String.valueOf(businessLite.getAccountId())).build();
        ContactDocument conversation = elasticSearchService.findDocumentById(esFindByIdRequest);
		if(conversation!=null && CollectionUtils.isNotEmpty(conversation.getReviews())) {
			List<Review> reviews=conversation.getReviews();
			reviews.forEach(r->reviewEventHandlerService.unlinkReview(null, businessLite.getRoutingId(), r.getId(), mcId,true, false));
		}
	}

    /**
     * If below code looks complicated, don't blame me for that. Poor planning.
     * @param customerEventDTO
     */
    @Override
    public void consumeCustomerUpdateEvent(CustomerEventDTO customerEventDTO, boolean enableFirebaseSync) {
		log.debug("updateContactDocumentOnES: event data {}", customerEventDTO);
		if(Objects.nonNull(customerEventDTO) && Objects.nonNull(customerEventDTO.getBlockEvent()) && Boolean.FALSE.equals(customerEventDTO.getBlockEvent())){
			log.info("Duplicate of spam and block event");
			return;
		}
    	if (Objects.nonNull(customerEventDTO.getCustomerData())) {
    		log.info("updateContactDocumentOnES: customerEventDTO {}", customerEventDTO.getCustomerData());
    		if(Objects.isNull(customerEventDTO.getDelta()) || Objects.isNull(customerEventDTO.getDelta().getBlocked())) MessengerUtil.validateCustomerDTOForUpdate(customerEventDTO.getCustomerDTO());
    	}
    	if (CollectionUtils.isEmpty(customerEventDTO.getEditCustomerIds())) {
    		log.info("updateContactDocumentOnES : no ids found for update");
    		return;
    	}

    	if (customerEventDTO.getEditCustomerIds().size() > 1) {
    		List<Integer> customerIdList = customerEventDTO.getEditCustomerIds();
    		for (Integer cId : customerIdList) {
    			customerEventDTO.setEditCustomerIds(Arrays.asList(cId));
				KafkaTopicEnum topic = getUpdateContactTopic(enableFirebaseSync,
						Objects.nonNull(customerEventDTO) && Objects.nonNull(customerEventDTO.getBlockEvent())
								&& Boolean.TRUE.equals(customerEventDTO.getBlockEvent()));
				kafkaService.publishToKafkaAsync(topic, customerEventDTO);
    		}
    		return;
    	}

    	
    	if (customerEventDTO.getEnterpriseId() == null) {
    		log.error("No business found for businessId : {}", customerEventDTO.getEnterpriseId());
    		return;
    	}
    	
    	String lockKey = "";
    	Optional<Lock> lockOpt = Optional.empty();
    	try {
    		lockKey = Constants.CUSTOMER_ID_PREFIX + customerEventDTO.getEditCustomerIds().get(0);
    		lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
    		if (!lockOpt.isPresent()) {
    			log.info("Lock is already acquired for the key:{}",lockKey);
    			kafkaService.publishToKafkaAsync(KafkaTopicEnum.UPDATE_CONTACT_EVENT_DELAYED_QUEUE,customerEventDTO);
    			return;
    		}
            ContactState contactState=null;
            ContactDocument contactFromES=null;
    		MessengerContact messengerContact = messengerContactService.findByCustomerId(customerEventDTO.getEditCustomerIds().get(0));
    		if (!ObjectUtils.isEmpty(messengerContact)) {
    		    Integer mcId=messengerContact.getId();
    		    Integer routingId=customerEventDTO.getEnterpriseId();
                ESFindByIdRequest<ContactDocument> esFindByIdRequest = ESFindByIdRequest.<ContactDocument>builder()
                        .id(String.valueOf(mcId)).documentType(ContactDocument.class)
                        .index(Constants.Elastic.CONTACT_INDEX).routingId(String.valueOf(routingId)).build();
    			contactFromES=elasticSearchService.findDocumentById(esFindByIdRequest);
    			if (Objects.nonNull(contactFromES)) {
    				CustomerDTO customerDTO = customerEventDTO.getCustomerDTO();
                    contactState = contactFromES.getContactState();
    				customerDTO.setId(messengerContact.getCustomerId()); //since all data is same except id and businessId. Customer Id is supplied as list inside customerEventDTO
    				customerDTO.setBusinessId(messengerContact.getBusinessId());
    				boolean isCustomerDataPresent = false;
    				if (Objects.nonNull(customerEventDTO.getCustomerData())) {
    					isCustomerDataPresent = true;
    				}
    				boolean prevBlockedState = contactFromES.getBlocked() == null ? false : contactFromES.getBlocked();
    				boolean subscriptionStatusChanged = false;
    				if(Objects.nonNull(customerEventDTO.getDelta())) {
    					subscriptionStatusChanged =customerEventDTO.getDelta().getSubStatusChanged()!=null ? customerEventDTO.getDelta().getSubStatusChanged() : customerEventDTO.getDelta().getWhatsAppOptinChanged() != null ? customerEventDTO.getDelta().getWhatsAppOptinChanged() : false;
    				}
    				boolean changedDetected = updateChange(contactFromES, customerDTO, routingId, isCustomerDataPresent, messengerContact,subscriptionStatusChanged); //caution : updateChange method also contains update customer activity ES code (side effect).
    				if (changedDetected) {
    					messengerContactService.saveOrUpdateMessengerContact(messengerContact);
    					SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
    					contactFromES.setUpdatedAt(simpleDateFormat.format(new Date()));
                        ESUpsertRequest<ContactDocument> esUpsertRequest = ESUpsertRequest.<ContactDocument>builder()
                                .id(String.valueOf(mcId)).index(Constants.Elastic.CONTACT_INDEX)
                                .refreshPolicy(RefreshPolicy.NONE).routingId(String.valueOf(routingId)).upsert(true)
                                .document(contactFromES).build();
                        elasticSearchService.upsertESDocument(esUpsertRequest);
    					boolean nextBlockState = contactFromES.getBlocked() == null ? false : contactFromES.getBlocked();
    					log.info("prevBlockState {} and nextBlockState {}", prevBlockedState, nextBlockState);
    					if(enableFirebaseSync && (prevBlockedState != nextBlockState || subscriptionStatusChanged)) {
    						//Firebase sync
    						FirebaseDto firebaseDto = DtoToEntityConverter.fromContactDoc(contactFromES);
    						firebaseDto.setTimestamp(new Date().getTime());
    						fcmService.mirrorOnWeb(firebaseDto);
    					}
    					log.info("updateContactDocumentOnES: messengerContactId {} updated with {} ", messengerContact.getId(), customerDTO);
    					// Clear customer cache.
    					contactService.onCustomerDataChange(customerDTO.getId());
    				} else {
    					log.info("updateContactDocumentOnES: no change detected in {} and ES contact {}", customerDTO, contactFromES);
    				}
    			} else {
    				log.info("updateContactDocumentOnES: no contact document found on ES for mcId {}", messengerContact.getId());
    			}

    			if (Objects.nonNull(contactFromES) && contactState!=contactFromES.getContactState()) {
    				CustomerDTO customerDTO = customerEventDTO.getCustomerDTO();
    				ContactState customerType = Objects.nonNull(customerDTO)
    						? ((customerDTO.getContactState() == ContactState.CONVERTED_LEAD
    						|| customerDTO.getContactState() == ContactState.CUSTOMER) ? ContactState.CUSTOMER
    								: ContactState.LEAD)
    								: null;
    				if (ContactState.LEAD == customerType || ContactState.CUSTOMER == customerType) {
    					updateMessagesForContactStateChangedForMCID(mcId, customerType, routingId);
    				}
    			}
    			return;
    		}
    	} finally {
    		if (lockOpt.isPresent()) {
    			redisLockService.unlock(lockOpt.get());
    		}
    	}
    	log.info("updateContactDocumentOnES: no messengerContactId found for customer Ids {}", customerEventDTO.getEditCustomerIds());
    }


    /**
     * Method to update only changed values.
     * Not a good code but given time constraints and requirement had to to it.
     * @param contactFromES
     * @param customerDTO
     * @return
     */
	private boolean updateChange(ContactDocument contactFromES, CustomerDTO customerDTO, Integer accountId,
			boolean isCustomerDataPresent, MessengerContact messengerContact, boolean subscriptionStatusChanged) {
	    boolean changeDetected = false;
        boolean isBlocked = Objects.nonNull(contactFromES.getBlocked()) && contactFromES.getBlocked();
        if (isCustomerDataPresent && !StringUtils.equals(contactFromES.getC_phone(), customerDTO.getPhone())) {
            //TODO 1. decrypt and re-encrypt last message (*************
            //TODO 2. update phone and last message in messengerContact and ES
            contactFromES.setC_phone(StringUtils.isBlank(customerDTO.getPhone()) ? "" : customerDTO.getPhone());
            changeDetected = true;
        }
        if (isCustomerDataPresent && !StringUtils.equals(contactFromES.getC_email(), customerDTO.getEmailId())) {
            contactFromES.setC_email(StringUtils.isBlank(customerDTO.getEmailId()) ? "" : customerDTO.getEmailId());
            changeDetected = true;
        }
		// Comm preferences change
		if (subscriptionStatusChanged) {
			contactFromES.setCommPreferences(customerDTO.getCommPreferences());
			changeDetected = true;
		}
		if (Objects.nonNull(contactFromES.getCommPreferences().getIsPhoneAndWhatsAppNumberSame()) &&
				Objects.nonNull(customerDTO.getCommPreferences().getIsPhoneAndWhatsAppNumberSame()) && 
				!contactFromES.getCommPreferences().getIsPhoneAndWhatsAppNumberSame() == customerDTO.getCommPreferences().getIsPhoneAndWhatsAppNumberSame()) {
			contactFromES.getCommPreferences().setIsPhoneAndWhatsAppNumberSame(customerDTO.getCommPreferences().getIsPhoneAndWhatsAppNumberSame());
			changeDetected = true;
		}
        if (StringUtils.isNotBlank(customerDTO.getFirstName()) || StringUtils.isNotBlank(customerDTO.getLastName())) {
            changeDetected = true;
            contactFromES.setC_name(MessengerUtil.buildCustomerName(customerDTO));
        }
        if(StringUtils.isNotBlank(customerDTO.getState()) && !customerDTO.getContactState().equals(contactFromES.getContactState())) {
            changeDetected = true;
            contactFromES.setContactState(customerDTO.getContactState());
            contactFromES.setLead(customerDTO.isLead());
            contactFromES.setLeadOrigin(customerDTO.getLeadSource());
            messengerContact.setContactState(customerDTO.getContactState());
            messengerContact.setLead(customerDTO.isLead());
            messengerContact.setLeadSource(customerDTO.getLeadSource());
        }
        /**
         * changes needed for update-contact
         * check who is blocking and unblocking and update accordingly
         */
        if(!isBlocked && BooleanUtils.isTrue(customerDTO.getBlocked()) && Objects.nonNull(customerDTO.getUserId()) && customerDTO.getUserId() != -1) {
            messengerContact.setBlocked(true);
            contactFromES.setBlocked(true);
            messengerContact.setSpam(true);
            contactFromES.setSpam(true);
            messengerContact.setRtmPauseTagging(true);
            messengerContact.setSpamMarkedBy(customerDTO.getUserId());
            contactFromES.setSpamStatusMarkedBy(customerDTO.getUserId());
            conversationActivityService.createBlockUnblockActivity(ActivityType.CONTACT_BLOCKED, customerDTO, contactFromES, accountId);
            changeDetected = true;
        }
        else if(isBlocked && BooleanUtils.isFalse(customerDTO.getBlocked()) && Objects.nonNull(customerDTO.getUserId()) && customerDTO.getUserId() != -1) {
            messengerContact.setBlocked(false);
            contactFromES.setBlocked(false);
            messengerContact.setSpam(false);
            contactFromES.setSpam(false);
            messengerContact.setRtmPauseTagging(false);
            messengerContact.setSpamMarkedBy(customerDTO.getUserId());
            contactFromES.setSpamStatusMarkedBy(customerDTO.getUserId());
            conversationActivityService.createBlockUnblockActivity(ActivityType.CONTACT_UNBLOCKED, customerDTO, contactFromES, accountId);
            changeDetected = true;
        }
        return changeDetected;
    }
    
	@Override
	public void consumeContactUsEvent(ContactUsEventDTO request) throws Exception{
		log.info("consumeContactUsEvent: event data {}", request);
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(request.getBid());
		Objects.requireNonNull(businessDTO);
		CustomerDTO customerDTO = contactService.findById(request.getCid());
		String customerName = customerDTO.getFirstName();
		if (ObjectUtils.isEmpty(request.getCustomerComment())) {
			request.setCustomerComment(customerName.concat(" would like to get in touch"));
		}
		//below messageDTO to be used for activity creation of spam/not spam
		SMSMessageDTO messageDTO = new SMSMessageDTO();
		messageDTO.setBusinessDTO(businessDTO);
		messageDTO.setCustomerDTO(customerDTO);
		messageDTO.setBody(request.getCustomerComment());
		SmsDTO smsDto = new SmsDTO();
		smsDto.setCreateDate(new Date(request.getRequestDate()));
		messageDTO.setSmsDTO(smsDto);
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CONTACTUS_RECEIVE_EVENT_DELAYED_QUEUE,
						request);
				return;
			}
			// fetch business for given location in request
			MessengerContact messengerContact = messengerContactService.findOrCreate(businessDTO, MessageTag.UNREAD.getCode(), customerDTO);
			if (messengerContact.getTag() != MessageTag.UNREAD.getCode())
				messengerContact.setTag(MessageTag.UNREAD.getCode());
			Date receivedDate = new Date(request.getRequestDate());
			// Last message meta data
			LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
			lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
			lastMessageMetaData.setLastReceivedMessageSource(Source.CONTACT_US.getSourceId());
			lastMessageMetaData.setLastMessageSource(Source.CONTACT_US.getSourceId());
			messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
			// setting isRead false and viewedBy to null as a new message is received
			messengerContact.setIsRead(false);
			messengerContact.setViewedBy(null);
			if (request.isEmailBodyParsed()) {
				messengerContact.setLastMessage(request.getCustomerComment());
			} else {
				messengerContact.setLastMessage(MessengerConstants.CONTACT_US_LEAD_DEFAULT_MSG);
			}

			messengerContact.setCreatedAt(receivedDate);
			messengerContact.setLastMsgOn(receivedDate);
			messengerContact.setUpdatedAt(receivedDate);
			boolean encrypted = false;
			String lastMessage = messengerContact.getLastMessage();
			if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotEmpty(messengerContact.getLastMessage())) {
				try {
					String customerPhone = customerDTO.getPhone();
					Long businessNumber = businessDTO.getBusinessNumber();
					String encryptedMessage = EncryptionUtil.encrypt(lastMessage,
							StringUtils.join(businessNumber, customerPhone),
							StringUtils.join(customerPhone, businessNumber));
					messengerContact.setLastMessage(encryptedMessage);
					messengerContact.setEncrypted(1);
					encrypted = true;
				} catch (Exception e) {
					log.error("Failed to encrypt the message for BID{} , CID{} saving as unencrypted {}", businessDTO.getBusinessNumber(),
							customerDTO.getId(), lastMessage);
				}
			}
			// If encryption is not required or result in failure, set unencrypted text
			if (!encrypted) {
				messengerContact.setEncrypted(0);
				messengerContact.setLastMessage(lastMessage);
			}
			messengerContact = messengerContactService.saveOrUpdateMessengerContact(messengerContact);
            //set lastIncomingMessageTime for Performance
			messengerContact.setLastIncomingMessageTime(receivedDate.getTime());

			//Spam detection
			messageDTO.setSource(Source.CONTACT_US.getSourceId());
			messageDTO.setMessengerContact(messengerContact);
			MessageTag messageTag = MessageTag.getMessageTagById(messengerContact.getTag());
			spamDetectionService.spamDetectionAllChannels(messageDTO, messengerContact, customerDTO, messageTag);
			UserDTO userDTO = new UserDTO();
			// ES update for Conversation
			ContactDocument contactDocument = messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO,
					messageTag, userDTO);

			String phoneNumber  = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
			SmsDTO contactUsSmsDto= SmsDTO.getSmsDTOContactUs(customerDTO, request, phoneNumber, Source.CONTACT_US);

			Sms sms = smsService.saveSmsFromCustomer(contactUsSmsDto);
			log.info("consumeContactUsEvent sms saved {}", contactUsSmsDto.getSmsId());
			// ES update for new message.
			MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(contactUsSmsDto, messengerContact.getId());
			MessageDocument messageDocument = messengerContactService.andNewMessageOnEs(messageDocumentDTO,
					null, userDTO, businessDTO, MessengerEvent.SMS_RECEIVE);
			// Mirroring + Email Notification
			MessengerGlobalFilter notificationMetaData = getEmailNotificationMetaData(contactUsSmsDto.getCreateDate(), messageDocumentDTO, businessDTO);
			if (!BooleanUtils.isTrue(messageDTO.getCustomerDTO().getBlocked())) {
				notificationService.processMessageNotification(notificationMetaData, businessDTO, messageDocument);
				fcmService.pushToFireBase(contactDocument, messageDocument, true);
			}
			messageDTO.setMessageDocument(messageDocument);
			messageDTO.setContactDocument(contactDocument);
			if(StringUtils.isNotBlank(request.getPhone())){
				messageDTO.setMsgTypeForResTimeCalc("R");
				robinService.sendRobinReply(messageDTO,lastMessage);
			}
			return;
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}


	public MessengerGlobalFilter getEmailNotificationMetaData(Date crDate, MessageDocumentDTO messageDocumentDTO, BusinessDTO business) {
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(business.getBusinessId());
        notificationRequest.setBusinessName(business.getBusinessName());
        notificationRequest.setBusinessAlias(business.getBusinessAlias());
        notificationRequest.setEnterpriseName(business.getEnterpriseName());
        notificationRequest.setBusinessNumber(business.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(business.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(business.getEnterpriseId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setTimeZone(business.getTimeZoneId());
		notificationRequest.setMsgId(Integer.valueOf(messageDocumentDTO.getM_id()));
        notificationRequest.setProductName(business.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        notificationRequest.setLastMsgTime(crDate.getTime());
        notificationRequest.setConversationId(messageDocumentDTO.getMcid());
        return notificationRequest;

    }
	
    private void updateMessagesForContactStateChangedForMCID(Integer mcId,
            ContactState customerType, Integer routingId) {
        log.info("updateMessagesForContactStateChangedForMCID called for mcId : {} and contactState : {}",
                mcId, customerType);
        Map<String, Object> messageFilter = new HashMap<>();
        messageFilter.put("excludedSources",
                ControllerUtil.toCommaSeparatedString(Constants.EXCLUDED_SOURCE_IN_UPDATE_FILTERS));
        messageFilter.put("excludedMessageTypes",
                JSONUtils.toJSON(Constants.EXCLUDED_MESSAGE_TYPE_IN_UPDATE_FILTERS));
        Map<String, Object> data = new HashMap<>();
            messageFilter.put("mcId", mcId);
            data.put("data", messageFilter);
            ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
            builder.index(Constants.Elastic.MESSAGE_INDEX)
                    .queryTemplateFile(Constants.Elastic.GET_MESSAGES_FOR_UPDATING_CUSTOMER_FILTERS)
                    .freeMarkerDataModel(data).routingId(routingId);

            Map<String, Object> scriptData = new HashMap<>();
            scriptData.put("inline",
                    "ctx._source.customerType=params.customerType;ctx._source.lastUpdateDate=params.lastUpdateDate;ctx._source.u_time=params.lastUpdateDate");
            builder.scriptParam(scriptData);

            Map<String, Object> params = new HashMap<>();
            params.put("customerType", customerType);
            params.put("lastUpdateDate", (new Date()).getTime());

            builder.params(params);

            boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(),false);
            if (!updateByQueryResponse) {
                log.error("Error in updateMessagesForContactStateChangedForMCIDS for mcId:{} and customerType : {}",
                        mcId, customerType);
            }
            log.info("updateMessagesForContactStateChangedForMCIDS completed successfully");
    }

    private KafkaTopicEnum getUpdateContactTopic(boolean enableFirebaseSync, boolean isBlockEvent) {
        if (isBlockEvent) {
            return KafkaTopicEnum.UPDATE_CONTACT_SPAM_AND_BLOCK_MULTI_QUEUE;
        }
        return enableFirebaseSync ? 
            KafkaTopicEnum.UPDATE_CONTACT_EVENT_MULTI_QUEUE : 
            KafkaTopicEnum.UPDATE_CONTACT_MIGRATION_EVENT_MULTI_QUEUE;		//Migration pipeline
    }
}
