/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.QueroFAQResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.QueroService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QueroServiceImpl implements QueroService {

    private final RestTemplate restTemplate;

    @Value("${quero.service.url}")
    private String queroUrl;

    @Override
    public QueroFAQResponse getFAQsByIds(List<Integer> faqIds, Integer accountId, Boolean answers) {
        log.info("getFAQsByIds called with ids : {},accountId : {},answersRequired : {}", faqIds, accountId, answers);
        ResponseEntity<QueroFAQResponse> responseEntity = null;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        String url = queroUrl + "/" + accountId + "/get-qna-by-ids?isAnswerRequired=" + answers;
        HttpEntity<List<Integer>> requestBody = new HttpEntity<>(faqIds, headers);
        responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestBody, QueroFAQResponse.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("Successfully retrieved FAQs : {}", responseEntity);
            return responseEntity.getBody();
        }
        log.error("Error occurred while fetching FAQs : {} url :{}", responseEntity, url);
        throw new MessengerException(ErrorCode.UNKNOWN, "Error from quero service");
    }

    @Override
    public void queroRobinIngestion(Integer accountId, Integer userId, boolean isSMB) {
    	log.info("queroRobinIngestion called with accountId : {}, userId : {}, isSMB : {}", accountId, userId, isSMB);
    	ResponseEntity<Void> responseEntity = null;
    	HttpHeaders headers = new HttpHeaders();
    	headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    	headers.setContentType(MediaType.APPLICATION_JSON);
    	headers.set("user-id", String.valueOf(userId));
    	
    	String url = queroUrl + "/" + accountId + "/robin-ingestion?isSmb="+isSMB;
    	
    	HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
    	responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
    	if (responseEntity.getStatusCode().is2xxSuccessful()) {
    		log.info("Successfully executed Quero Robin Ingestion : {}", responseEntity);
    		return;
    	}
    	log.error("Error occurred while calling queroRobinIngestion : {} url :{}", responseEntity, url);
    	throw new MessengerException(ErrorCode.UNKNOWN, "Error from quero service");
    }
    
    @Override
    public void queroDefaultFAQCreation(Integer accountId, Integer userId, boolean isSMB) {
    	log.info("queroDefaultFAQCreation called with accountId : {}, userId : {}, isSMB : {}", accountId, userId, isSMB);
    	ResponseEntity<Void> responseEntity = null;
    	HttpHeaders headers = new HttpHeaders();
    	headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    	headers.setContentType(MediaType.APPLICATION_JSON);
    	
    	Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("businessId", accountId);
        requestBody.put("userId", userId);
        requestBody.put("isSMB", isSMB);
        
    	String url = queroUrl + "/migrate/faqs";
    	
    	HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
    	responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
    	if (responseEntity.getStatusCode().is2xxSuccessful()) {
    		log.info("Successfully executed Quero DefaultFAQCreation : {}", responseEntity);
    		return;
    	}
    	log.error("Error occurred while calling queroDefaultFAQCreation : {} url :{}", responseEntity, url);
    	throw new MessengerException(ErrorCode.UNKNOWN, "Error from quero service");
    }
    
    @Override
    public void queroProfileLinkAndZeroStateMigration(Integer accountId, Integer userId, boolean isSMB) {
    	log.info("queroProfileLinkAndZeroStateMigration called with accountId : {}, userId : {}, isSMB : {}", accountId, userId, isSMB);
    	ResponseEntity<Void> responseEntity = null;
    	HttpHeaders headers = new HttpHeaders();
    	headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
    	headers.setContentType(MediaType.APPLICATION_JSON);
    	
    	Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("accountId", accountId);
        requestBody.put("userId", userId);
        requestBody.put("smb", isSMB);
        
    	String url = queroUrl + "/migrate/profile/link";
    	
    	HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
    	responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
    	if (responseEntity.getStatusCode().is2xxSuccessful()) {
    		log.info("Successfully executed Quero Profile Link And Zero State Migration : {}", responseEntity);
    		return;
    	}
    	log.error("Error occurred while calling queroProfileLinkAndZeroStateMigration : {} url :{}", responseEntity, url);
    	throw new MessengerException(ErrorCode.UNKNOWN, "Error from quero service");
    }
}
