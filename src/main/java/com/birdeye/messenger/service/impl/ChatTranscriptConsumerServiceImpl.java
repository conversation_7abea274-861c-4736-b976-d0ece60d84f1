package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.EmailMessageConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ChatTranscriptBusinessUserConfig;
import com.birdeye.messenger.dto.ChatTranscriptUserInfo;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.EmailMessageDocument;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NexusEmailService;
import com.birdeye.messenger.service.ChatTranscriptConsumerService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ChatTranscriptConsumerServiceImpl implements ChatTranscriptConsumerService {

    @Autowired
    MessengerContactService messengerContactService;

    @Autowired
    BusinessService businessService;

    @Autowired
    CommonService commonService;

    @Autowired
    NexusEmailService nexusEmailService;

    @Autowired
    ContactService contactService;

    @Override
    public void generateAndSendChatTranscript(Integer accountId, Integer mcId) {

        MessengerContact messengerContact = messengerContactService.findById(mcId);
        if (Objects.isNull(messengerContact)) {
            log.info("generateAndSendChatTranscript: no records found for mcId: {}", mcId);
            return;
        }
        BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
        if (Objects.isNull(businessDTO) || !businessDTO.getAccountId().equals(accountId)) {
            log.info("generateAndSendChatTranscript: contact with Id {} does not belong to account with Id {}", mcId, accountId);
            return;
        }
        // ----------------------- validation code ends here ------------------------------------------------------

        ContactDocument contactDocument = getContact(businessDTO.getAccountId(), mcId);
        List<String> messageTypesToBeExcluded = getMessageTypesToBeExcluded(accountId);
        List<MessageDocument> messages = getLastNDaysMessages(businessDTO.getAccountId(), mcId, 7L, messageTypesToBeExcluded);
        if (CollectionUtils.isEmpty(messages) || Objects.isNull(contactDocument)) {
            log.info("generateAndSendChatTranscript: no messages or contact found for contact with Id {} and account with Id {}", mcId, accountId);
            return;
        }
        List<EmailMessageDocument> emailMessageDocuments = prepareFormattedEmailMessages(messages, contactDocument, businessDTO.getTimeZoneId()!= null ? businessDTO.getTimeZoneId() : "PST");
        Map<String, Object> ftlDataMap = prepareFtlDataMap(businessDTO, messengerContact, contactDocument, emailMessageDocuments);
        EmailDTO emailDTO = prepareEmailDto(businessDTO, contactDocument);
        try {
           nexusEmailService.sendMailV2(businessDTO.getBusinessId(), emailDTO, getTemplateName(), Constants.MESSENGER_ALERT + "_" + messengerContact.getId(), businessDTO.getBusinessNumber(), ftlDataMap);
        } catch (Exception e) {
            log.error("error while pushing email data to kafka on topic : {}", e.getMessage());
        }

    }

    protected List<String> getMessageTypesToBeExcluded(Integer accountId) {
        return Arrays.asList(MessageDocument.MessageType.EVENTS.toString(), MessageDocument.MessageType.ACTIVITY.toString(),MessageDocument.MessageType.RICH_CONTENT_CHAT.toString());
    }

    protected EmailDTO prepareEmailDto(BusinessDTO businessDTO, ContactDocument contactDocument) {
        EmailDTO emailDTO = new EmailDTO();
        emailDTO.setBusinessId(businessDTO.getBusinessId().toString());
        List<String> subscriberEmailIds = getSubscriberEmailIds(businessDTO.getBusinessId(), contactDocument.getCr_asgn_id());
        emailDTO.setTo(subscriberEmailIds);
        emailDTO.setRecipientType(Constants.RECIPIENT_TYPE);
        emailDTO.setSubject("Transcript of closed conversation with " + NotificationServiceImpl.getNameForEmail(contactDocument.getC_name(), "a customer"));
        emailDTO.setReplyTo("<EMAIL>");
        return emailDTO;
    }

    protected Map<String, Object> prepareFtlDataMap(BusinessDTO businessDTO, MessengerContact messengerContact, ContactDocument contact, List<EmailMessageDocument> emailMessageDocuments) {
        Boolean messengerEnabled = !Integer.valueOf(0)
                .equals(businessService.isMessengerEnabled(businessDTO.getAccountId())) ? Boolean.TRUE : Boolean.FALSE;
        String locationName = "";
        String businessName;
        if(StringUtils.isNotBlank(businessDTO.getEnterpriseName())) {
            businessName = businessDTO.getEnterpriseName();
            locationName = businessDTO.getBusinessAlias() != null ? businessDTO.getBusinessAlias() : businessDTO.getBusinessName();
        }
        else {
            businessName = businessDTO.getBusinessAlias() != null ? businessDTO.getBusinessAlias() : businessDTO.getBusinessName();
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("businessName", businessName);
        dataMap.put("businessId", businessDTO.getBusinessId().toString());
        dataMap.put("enterpriseId", businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId().toString() : null);
        dataMap.put("locationName", locationName);
        dataMap.put("mcId", String.valueOf(messengerContact.getId()));
        dataMap.put("messengerEnabled", messengerEnabled);
        dataMap.put("accountType", businessDTO.getAccountType().toLowerCase());
        dataMap.put("isAccountWhitelabled", businessDTO.isBusinessWhitelabled());
        dataMap.put("title", EmailMessageConstants.CHAT_TRANSCRIPT);
        String serverBaseURL = businessService.getWebsiteDomain(businessDTO.getBusinessId());
        log.debug("Server Base URL : {}, for business Id : {}, for conversation Id : {}", serverBaseURL, businessDTO.getBusinessId(), messengerContact.getId());
        dataMap.put("serverBaseURL", serverBaseURL + "/");
        if (StringUtils.isNotBlank(contact.getC_phone())) {
            dataMap.put("customerPhoneNumber", contact.getC_phone());
        }
        if (contact.getC_email() != null && !StringUtils.endsWith(contact.getC_email(), "@fb.com")) {
            dataMap.put("customerEmailId", contact.getC_email());
        }
        dataMap.put("customerName", NotificationServiceImpl.getNameForEmail(contact.getC_name(), "Customer"));
        //dataMap.put("isMessage", false);
        String emailDate = new SimpleDateFormat("yyMMdd").format(new Date());
        dataMap.put("emailDate", emailDate);
        dataMap.put("emailNotificationMessages", emailMessageDocuments);
        //dataMap.put("callback", false);
        return dataMap;
    }

    private ContactDocument getContact(Integer accountId, Integer mcId) {
        MessangerBaseFilter filter = new MessangerBaseFilter(accountId, mcId, 1);
        List<ContactDocument> contactFromES = messengerContactService.getContactFromES(filter);
        if(CollectionUtils.isNotEmpty(contactFromES)) return contactFromES.get(0);
        return null;
    }

    private List<String> getSubscriberEmailIds(Integer businessId, Integer assignedToUserId) {
        List<String> userEmailIds = new ArrayList<>();
        ChatTranscriptBusinessUserConfig chatTranscriptBusinessUserConfig = businessService.getChatTranscriptBusinessUserConfig(businessId);
        if(Objects.nonNull(chatTranscriptBusinessUserConfig)) {
            if(CollectionUtils.isNotEmpty(chatTranscriptBusinessUserConfig.getAll())) {
                List<String> emailIds = chatTranscriptBusinessUserConfig.getAll()
                        .stream()
                        .map(ChatTranscriptUserInfo::getEmail)
                        .collect(Collectors.toList());
                userEmailIds.addAll(emailIds);
            }
            if(CollectionUtils.isNotEmpty(chatTranscriptBusinessUserConfig.getAssignedToMe())) {
                chatTranscriptBusinessUserConfig.getAssignedToMe()
                        .stream()
                        .filter(config -> config.getUserId().equals(assignedToUserId))
                        .findAny()
                        .ifPresent(user -> userEmailIds.add(user.getEmail()));
            }
        }
        return userEmailIds;
    }

    protected List<EmailMessageDocument> prepareFormattedEmailMessages(List<MessageDocument> messageDocuments, ContactDocument contactDocument, String timeZone) {
        List<EmailMessageDocument> emailMessageDocuments = new ArrayList<>();
        for (MessageDocument messageDocument : messageDocuments) {
            EmailMessageDocument emailMessageDocument = new EmailMessageDocument();
            emailMessageDocument.setSentON(messageDocument.getCr_date());
            emailMessageDocument.setCreatedDate(MessengerUtil.formatMessageCreatedTime(messageDocument.getCr_date(), timeZone));
            emailMessageDocument.setMsgBody(MessengerUtil.decryptMessage(messageDocument));

            // handled for email notifications formating
            if (StringUtils.isNotBlank(emailMessageDocument.getMsgBody())) {
                emailMessageDocument.setMsgBody(StringUtils.replace(emailMessageDocument.getMsgBody(), "\n", "<br />"));
            }
            //Voicemail Message changes
            if (StringUtils.isNotBlank(messageDocument.getVoiceMailUrl())) {
                if (emailMessageDocument.getMsgBody() == null) {
                    emailMessageDocument.setMsgBody("Voicemail transcription is not available");
                }
                MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile("mp3", messageDocument.getVoiceMailUrl(), "-1", "voicemail", "audio/mpeg");
                List<MessageDocument.MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                emailMessageDocument.setMediaFiles(mediaFiles);
            }
            if (Objects.isNull(messageDocument.getMessageType())) {
                emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(messageDocument.getU_id(), messageDocument.getU_name()));
                if (StringUtils.isNotBlank(messageDocument.getA_url())) {
                    MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile(messageDocument.getA_ext(), messageDocument.getA_url(), messageDocument.getA_size(), messageDocument.getA_name(), messageDocument.getA_contype());
                    List<MessageDocument.MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                    mediaFiles = NotificationServiceImpl.truncateAttachmentName(mediaFiles);
                    emailMessageDocument.setMediaFiles(mediaFiles);
                }
                if (("SMS_RECEIVE".equals(messageDocument.getMsg_type())
                        || "MMS_RECEIVE".equals(messageDocument.getMsg_type()))) {
                    emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                    emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                    emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);
                }
                if ("SMS_SEND".equals(messageDocument.getMsg_type())
                        || "MMS_SEND".equals(messageDocument.getMsg_type())) {
                    emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                    emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
                }
            } else {
                emailMessageDocument.setMessageType(messageDocument.getMessageType());
                emailMessageDocument.setCommunicationDirection(messageDocument.getCommunicationDirection());
                if (CollectionUtils.isNotEmpty(messageDocument.getMediaFiles())) {
                    List<MessageDocument.MediaFile> mediaFiles = NotificationServiceImpl.truncateAttachmentName(messageDocument.getMediaFiles());
                    emailMessageDocument.setMediaFiles(mediaFiles);
                }
                emailMessageDocument.setCreatedBy(messageDocument.getCreatedBy());
                if (MessageDocument.MessageType.CHAT.equals(messageDocument.getMessageType())
                        && MessageDocument.CommunicationDirection.RECEIVE
                        .equals(messageDocument.getCommunicationDirection())) {
                    emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                }
            }
            emailMessageDocuments.add(emailMessageDocument);
            // Create separate message which contains both, Media Files & Message Body.
            // JIRA : BIRDEYE-77920
            if (StringUtils.isNotBlank(emailMessageDocument.getMsgBody()) && CollectionUtils.isNotEmpty(emailMessageDocument.getMediaFiles())){
                EmailMessageDocument separatedOne = new EmailMessageDocument();

                separatedOne.setMessageType(emailMessageDocument.getMessageType());
                separatedOne.setCreatedBy(emailMessageDocument.getCreatedBy());
                separatedOne.setMsgBody(emailMessageDocument.getMsgBody());
                separatedOne.setCommunicationDirection(emailMessageDocument.getCommunicationDirection());
                separatedOne.setSentON(emailMessageDocument.getSentON());
                separatedOne.setCreatedDate(emailMessageDocument.getCreatedDate());
                emailMessageDocument.setMsgBody(null);

                emailMessageDocuments.add(separatedOne);
            }
        }
        Collections.sort(emailMessageDocuments);
        return emailMessageDocuments;
    }

    @SuppressWarnings("unchecked")
    private List<MessageDocument> getLastNDaysMessages(Integer accountId, Integer mcId, Long pastNdays, List<String> excludedMessageTypes) {
        List<String> excludeThese = excludedMessageTypes.stream().map(type -> "\"" + type + "\"").collect(Collectors.toList());
        String excluded = ControllerUtil.toCommaSeparatedString(excludeThese);
        MessengerFilter filter = new MessengerFilter();
        filter.setCount(100);
        filter.setAccountId(accountId);
        LocalDateTime sevenDaysFromNow = LocalDateTime.now().minusDays(pastNdays);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        filter.setStartDate(sevenDaysFromNow.format(dateTimeFormatter));
        //long startDate = sevenDaysFromNow.toEpochSecond(ZoneOffset.UTC);
        //filter.setStartDate(String.valueOf(startDate));
        Map<String, Object> params = new HashMap<>();
        params.put("contactId", mcId);
        //params.put("messageType", "\"ACTIVITY\", \"RICH_CONTENT_CHAT\", \"EVENT\"");
        params.put("messageType", excluded);
        params.put("excludedSources", ControllerUtil.toCommaSeparatedString(commonService.getExcludedMessageSourcesList()));
        filter.setQueryFile(Constants.Elastic.GET_MESSAGE_FOR_CHAT_TRANSCRIPT);
        filter.setParams(params);
        ElasticData<MessageDocument> messageData = messengerContactService.getMessageData(filter);
        if(messageData.isSucceeded() && messageData.getTotal() > 0) {
            return messageData.getResults();
        }
        return Collections.emptyList();
    }




    @Override
    public String getFormat() {
        return "HTML";
    }

    protected String getTemplateName() {
        return Constants.WEBCHAT_ALERT_V1;
    }

}
