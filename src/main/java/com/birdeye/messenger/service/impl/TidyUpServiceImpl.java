package com.birdeye.messenger.service.impl;

import static com.birdeye.messenger.util.ActivityBuilder.activity;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.birdeye.messenger.external.service.BusinessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest.OpType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.annotations.TrackExecutionTime;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dto.ContactFreeMarkerData;
import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.GetMessengerContactDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ConversationIds;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BulkOp;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.TidyUpResponse;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.TidyUpService;
import com.birdeye.messenger.sro.TidyUpConversationRequest;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TidyUpServiceImpl implements TidyUpService {

	private final UserService userService;

	private final MessengerContactService messengerContactService;

	private final MessengerMessageService messengerMessageService;

	private final ConversationActivityService conversationActivityService;

	private final ConversationService conversationService;

	private final FirebaseService firebaseService;
	
	private final KafkaService kafkaService;
	
	private final CommunicationHelperService communicationHelperService;
	
	private final ElasticSearchExternalService elasticSearchService;

	private final BusinessService businessService;
	
	@Autowired
	@Lazy
	private TidyUpService tidyUpService;

	@Override
    @TrackExecutionTime(maximumTimeThreshold = 100)
	public TidyUpResponse tidyUpConversations(TidyUpConversationRequest request, Integer userId, Integer accountId) {

		validateTidyUpConversationsRequest(request);
		
		ConversationRequest conversationRequestClone = SerializationUtils.clone(request.getQuery());
		
		if (ObjectUtils.isEmpty(request.getConversationIds())) {
			fetchAndSetApplicableConversationIds(request, userId, accountId);
		}

		if(!ObjectUtils.isEmpty(request.getConversationIds()) && !ObjectUtils.isEmpty(request.getExcludeConversationIds())) {
			request.getConversationIds().removeAll(request.getExcludeConversationIds());
		}

		TidyUpResponse response = new TidyUpResponse();
		if (CollectionUtils.isNotEmpty(request.getConversationIds())) {
			log.info("Applicable conversations for performing tidy-up is: {} userId:{} accountId:{}",request.getConversationIds().size(), userId, accountId);
			triggerTidyUpOperation(request, userId, accountId);
			response.setStatus("completed");
		}
		
		ConversationResponse conversationResponse = getConversationsForTidyUpResponse(conversationRequestClone,accountId,userId);
		response.setConversations(conversationResponse);
		return response;
	}

	private ConversationResponse getConversationsForTidyUpResponse(ConversationRequest conversationRequestClone,Integer accountId,Integer userId) {
		conversationRequestClone.setStartIndex(0);
		conversationRequestClone.setCount(20);
		conversationRequestClone.setMoveConversationToTop(null);
		conversationRequestClone.setTidyUpFilters(null);
        return conversationService.getConversationV2(conversationRequestClone, accountId, userId);
	}

	private void validateTidyUpConversationsRequest(TidyUpConversationRequest request) {
		if (StringUtils.isEmpty(request.getOperation()) && BulkOp.valueOf(request.getOperation())!=null){
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_OP_FOR_TIDY_UP, HttpStatus.BAD_REQUEST));
		}
		
		if (request.getQuery() == null){
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_TIDY_UP_REQUEST, HttpStatus.BAD_REQUEST));
		}
	}

	private void fetchAndSetApplicableConversationIds(TidyUpConversationRequest request, Integer userId, Integer accountId) {
		// Get conversationIds from ES based on filter
		long start = System.currentTimeMillis();
		String readStatus = request.getQuery().getTidyUpFilters() != null ? request.getQuery().getTidyUpFilters().getReadStatus() : null;
		if ("UNREAD".equalsIgnoreCase(readStatus)) {
			if(request.getQuery().isWeb()) {
				request.getQuery().getFilters().setUserId(String.valueOf(userId));
				request.getQuery().getFilters().setIsRead(true);
				request.getQuery().getFilters().setReadStatus("UNREAD");
			}
		} else if ("READ".equalsIgnoreCase(readStatus)) {
			if(request.getQuery().isWeb()) {
				request.getQuery().getFilters().setUserId(String.valueOf(userId));
				request.getQuery().getFilters().setIsRead(true);
				request.getQuery().getFilters().setReadStatus("READ");
			}
		}
		
		Boolean dateRangeIntersects = false;
		
		if (request.getQuery().getTidyUpFilters() != null) {
			dateRangeIntersects = conversationService.checkForDateIntersectionAndSetApplicableDates(request.getQuery());
			if (!dateRangeIntersects)
				return;
		}
		
		//https://birdeye.atlassian.net/browse/MSG-57 : 5000 limit
		Integer originalCount = request.getQuery().getCount();
		request.getQuery().setCount(5000);
		ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(request.getQuery());
		freeMarkerData.setCount("5000");
		freeMarkerData.setSource("\"m_c_id\"");
		freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V3);
		log.debug("tidyUpConversations: request {}", freeMarkerData);
		request.getQuery().setCount(originalCount);
		//TODO: This method can be made generic
		ElasticData conversationData = messengerContactService.getConversationIdsFromES(freeMarkerData, accountId);
		
		if (!conversationData.isSucceeded()) {
			log.error("tidyUpConversations: Failed to fetch data from ES");
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		
        boolean hasMore = request.getQuery().getStartIndex() + request.getQuery().getCount() < conversationData.getTotal();
        if (hasMore)
        		log.debug("[Warning] tidyUpConversations: More conversations remaining");
        
		List<ConversationIds> conversationIds = conversationData.getResults();
		
		if(CollectionUtils.isNotEmpty(conversationIds)) {
			request.setConversationIds(conversationIds.stream().map(ConversationIds::getM_c_id).collect(Collectors.toList()));
		}
		long elapsedTime = System.currentTimeMillis() - start;
		log.info("Profiled time taken to execute fetchAndSetApplicableConversationIds: {} milliseconds", elapsedTime);
	}
	
	private void triggerTidyUpOperation(TidyUpConversationRequest request, Integer userId,
			Integer accountId) {
		if (!ObjectUtils.isEmpty(request.getConversationIds())) {
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			Integer status = null;
			List<GetMessengerContactDTO> messengerContacts = messengerContactService.findByIdIn(request.getConversationIds());
			Map<Boolean, List<GetMessengerContactDTO>> partitions = messengerContacts.stream().collect(Collectors.partitioningBy(messengerContact -> messengerContact.getIsRead() != null && messengerContact.getIsRead() == true));

			List<GetMessengerContactDTO> messengerContactsIsReadTrue= partitions.get(true);
			List<GetMessengerContactDTO> messengerContactsIsReadFalse= partitions.get(false);
			
			if (BulkOp.CLOSE.name().equalsIgnoreCase(request.getOperation())) {
				log.info("Performing bulk CLOSE userId:{} accountId:{}",userId, accountId);
				// perform bulk close operation db
				messengerContactService.updateConversationTag(MessengerTagEnum.DONE.getId(), request.getConversationIds());
				// perform bulk read operation es
				conversationService.updateMultipleConversationsInES(request.getConversationIds(), BulkOp.CLOSE, accountId, userId);
				status = MessengerTagEnum.DONE.getId();
				
				//Push to bulk action bucket - Refresh in Dashboard
				firebaseService.bulkActionRefreshStatusOnWeb(accountId, userId);
			} else {
				if (BulkOp.READ.name().equalsIgnoreCase(request.getOperation())) {
					// NO need to make any change for c_read = true conversations (Existing Conversations), As they are already read for all.
					// For c_read = false (New Conversations), Append current user to viewedBy list.
					if (CollectionUtils.isNotEmpty(messengerContactsIsReadFalse)) {
						log.info("Performing bulk READ userId:{} accountId:{} count:{}",userId, accountId, messengerContactsIsReadFalse.size());
						List<Integer> conversationIdsReadFalse = messengerContactsIsReadFalse.stream().map(GetMessengerContactDTO::getId).collect(Collectors.toList());
						// perform bulk read operation db
						messengerContactService.updateViewedByRead(userId, conversationIdsReadFalse);
						// perform bulk read operation es
						conversationService.updateMultipleConversationsInES(conversationIdsReadFalse, BulkOp.READ, accountId, userId);
					}
					status = MessengerTagEnum.INBOX.getId();
				} else if (BulkOp.UNREAD.name().equalsIgnoreCase(request.getOperation())) {
					log.info("Performing bulk UNREAD userId:{} accountId:{} count:{}",userId, accountId, messengerContactsIsReadFalse.size());
					//For c_read = false (New Conversations), Remove current user from viewedBy list.
					if (CollectionUtils.isNotEmpty(messengerContactsIsReadFalse)) {
						List<Integer> conversationIdsReadFalse = messengerContactsIsReadFalse.stream().map(GetMessengerContactDTO::getId).collect(Collectors.toList());
						// perform bulk unread operation db
						messengerContactService.updateViewedByUnread(userId, conversationIdsReadFalse);
						// perform bulk unread operation es
						conversationService.updateMultipleConversationsInES(conversationIdsReadFalse, BulkOp.UNREAD, accountId, userId);
					}
					//For c_read = true, get User details for multiple locations, Remove current user from result
					//Update in db grouped by conversation per location (n calls for n location's)
					//Bulk upsert ES (single call)
					if (CollectionUtils.isNotEmpty(messengerContactsIsReadTrue)) {
						triggerTidyUpUnreadOperation(messengerContactsIsReadTrue, userId, accountId);
					}
					List<GetMessengerContactDTO> messengerContactsViewedBy = messengerContactService.findViewedByByIdIn(request.getConversationIds());
					Map<Boolean, List<GetMessengerContactDTO>> viewedByPartitions = messengerContactsViewedBy.stream().collect(Collectors.partitioningBy(messengerContact -> CollectionUtils.isNotEmpty(messengerContact.getViewedByList())));
					List<GetMessengerContactDTO> messengerContactsViewedByNull= viewedByPartitions.get(false);
					
					if (CollectionUtils.isNotEmpty(messengerContactsViewedByNull)) {
						//update tag DB
						List<Integer> conversationIdsViewedByNull = messengerContactsViewedByNull.stream().map(GetMessengerContactDTO::getId).collect(Collectors.toList());
						log.info("Bulk UNREAD, Update tag for contacts : {}", conversationIdsViewedByNull);
						messengerContactService.updateConversationTag(MessengerTagEnum.UNREAD.getId(), conversationIdsViewedByNull);
						//update tag ES
						conversationService.updateMultipleConversationsInES(conversationIdsViewedByNull, BulkOp.TAG, accountId, userId);
					}
					status = MessengerTagEnum.UNREAD.getId();
				}
			}
			//async task
			tidyUpService.generateActivityAndMirror(messengerContacts, userId, accountId, df, status, request.getBucket());
		}
	}

	@Override
	@Async
	public void generateActivityAndMirror(List<GetMessengerContactDTO> messengerContacts, Integer userId, Integer accountId,
			DateFormat df, Integer status, String bucket) {
		log.info("performing generateActivityAndMirror userId:{} and accountId:{}", userId, accountId);
		UserDTO userDTO = userService.getUserDTO(userId);

		List<MessageDocument> messageDocuments = new ArrayList<>();
		Map<Integer, Integer> convIdBusinessIdMap = messengerContacts.stream().collect(Collectors.toMap(GetMessengerContactDTO::getId, 
				GetMessengerContactDTO::getBusinessId));

		Map<Integer, List<Integer>> mobileMirrorData = new HashMap<>();

		for (GetMessengerContactDTO messengerContact : messengerContacts) {
			Integer conversationId = messengerContact.getId();
			Integer oldTag = messengerContact.getTag();
			// Activity for CLOSED conversations
			if (null != status && MessengerTagEnum.DONE.getId() == status.intValue() && !(oldTag.equals(status))) {
				ConversationActivity activity = conversationActivityService.create(ActivityType.CLOSED, userDTO);
				messengerMessageService.saveMessengerActivity(activity, conversationId, userDTO);

				MessageDocument activityDoc = activity()
						.withActivityTypeAndMId(ActivityType.CLOSED, String.valueOf(activity.getId()))
						.createdBy(
								new MessageDocument.UserDetail(userDTO.getId(), MessengerUtil.buildUserName(userDTO)))
						.at(activity.getCreatedDate())
						.forContactIdAndAccountId(String.valueOf(conversationId), accountId, messengerContact.getBusinessId()).build();

				messageDocuments.add(activityDoc);
			}

			// Mobile Mirroring
			Integer businessId = convIdBusinessIdMap.containsKey(conversationId) ? convIdBusinessIdMap.get(conversationId) : null;
			if (businessId != null)
				mobileMirrorData.computeIfAbsent(businessId, data->new ArrayList<Integer>()).add(conversationId);

			// Event for Assignee Update
			try {
				String currentAssignmentType = messengerContact.getAssignmentType() == null
						? Constants.Assignment.USER_ASSIGNED
								: messengerContact.getAssignmentType().name();
				IdentityDTO assignee = new IdentityDTO(messengerContact.getCurrentAssignee(),
						messengerContact.getCurrentAssigneeName(), currentAssignmentType,
						messengerContact.getCurrentAssigneeEmailId());

				ConversationTagChangeEvent conversationChangeTagEvent = new ConversationTagChangeEvent(accountId,
						messengerContact.getBusinessId(), oldTag, status, userId, messengerContact.getId(),
						assignee, true);

				kafkaService.publishToKafkaAsync(KafkaTopicEnum.TAG_UPDATE, messengerContact.getId(), conversationChangeTagEvent);
			} catch (Exception e) {
				log.error("Some error occured while pushing tag update event", e);
			}
		}

		if(CollectionUtils.isNotEmpty(messageDocuments)) {
			BulkUpsertPayload<MessageDocument> bulkUpsertActivities = new BulkUpsertPayload<>(messageDocuments,
					accountId, accountId, Constants.Elastic.MESSAGE_INDEX);
			try {
				elasticSearchService.bulkUpsert(bulkUpsertActivities);
			} catch (Exception e) {
				log.error("Exception while publishing bulk close activity", e);
				throw new MessengerException(ErrorCode.TIDY_UP_FAILED);
			}
		}

		firebaseService.mirrorOnWeb(accountId, null);

		if(MapUtils.isNotEmpty(mobileMirrorData)) {
			firebaseService.mirrorOnMobileTidyUpConversations(mobileMirrorData, accountId);
		}
	}
	
	@Async
	@Override
	public void triggerAsyncUnreadOperation(TidyUpConversationRequest request, List<GetMessengerContactDTO> messengerContactsIsReadTrue, Integer userId, Integer accountId) {
		try {
			triggerTidyUpUnreadOperation(messengerContactsIsReadTrue, userId, accountId);
		} catch (Exception ex) {
			throw ex;
		}
	}

	private void triggerTidyUpUnreadOperation(List<GetMessengerContactDTO> messengerContactsIsReadTrue, Integer userId,
			Integer accountId) {
		log.info("triggerTidyUpUnreadOperation for accountId:{} and userID:{}", accountId, userId);
		long start = System.currentTimeMillis();
		//mcId, businessId
		Map<Integer, Integer> convIdBusinessIdMap = messengerContactsIsReadTrue.stream().collect(Collectors.toMap(GetMessengerContactDTO::getId, 
				GetMessengerContactDTO::getBusinessId));

		Set<Integer> businessIds = messengerContactsIsReadTrue.stream().map(GetMessengerContactDTO::getBusinessId).collect(Collectors.toSet());
		// businessId , List<UserIds>
		Map<Integer, List<Integer>> viewedByPerBusinessList = communicationHelperService.getValidUserDTOs(userId, accountId, businessIds);
		
		// Remove the current user
		viewedByPerBusinessList.values().forEach(users -> {
			if(CollectionUtils.isNotEmpty(users)) {
				users.remove(Integer.valueOf(userId));
			}
		});

		//n calls for n business
		String viewedBy = null;
		for (Integer businessId : businessIds) {
			List<Integer> usersList = viewedByPerBusinessList.get(businessId);
			if(CollectionUtils.isNotEmpty(usersList)) {
				viewedBy = StringUtils.join(usersList, ',');
			} else {
				viewedBy = "";
			}
			// perform bulk unread operation db
			messengerContactService.updateViewedByUnreadForBusiness(businessId, viewedBy);
		}
		// perform bulk unread operation es : single call bulk upsert
		List<ContactDocument> contactDocumentToBeUpserted = new ArrayList<>();
		for (Integer conversationId: convIdBusinessIdMap.keySet()) {
			Integer businessId = convIdBusinessIdMap.containsKey(conversationId) ? convIdBusinessIdMap.get(conversationId) : null;
			List<Integer> viewedByUsers = viewedByPerBusinessList.containsKey(businessId) ? viewedByPerBusinessList.get(businessId) : new ArrayList<Integer>();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			ContactDocument contactForES = new ContactDocument();
			contactForES.setM_c_id(conversationId);
			contactForES.setViewedBy(viewedByUsers);
			contactForES.setC_read(false);
//			contactForES.setC_tag(MessageTag.UNREAD.getCode());
			contactForES.setUpdatedAt(df.format(new Date()));
			contactDocumentToBeUpserted.add(contactForES);
		}

		if(CollectionUtils.isNotEmpty(contactDocumentToBeUpserted)) {
			BulkUpsertPayload<ContactDocument> bulkUpsertContacts = new BulkUpsertPayload<>(contactDocumentToBeUpserted,
					accountId, accountId, Constants.Elastic.CONTACT_INDEX);
			try {
				elasticSearchService.performBulkRequest(bulkUpsertContacts,OpType.UPDATE);
			} catch (Exception e) {
				log.error("Exception while publishing bulk close activity", e);
				throw new MessengerException(ErrorCode.TIDY_UP_FAILED);
			}
		}
		
		long elapsedTime = System.currentTimeMillis() - start;
		log.info("Profiled time taken to execute triggerTidyUpUnreadOperation: {} milliseconds", elapsedTime);
	}

}
