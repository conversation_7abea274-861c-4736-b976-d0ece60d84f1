package com.birdeye.messenger.service.impl;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.ReceiveMsgBypassInboxAudit;
import com.birdeye.messenger.dao.repository.ReceiveMsgBypassInboxRepository;
import com.birdeye.messenger.service.ReceiveMsgBypassInboxAuditService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReceiveMsgBypassInboxAuditServiceImpl implements ReceiveMsgBypassInboxAuditService {
	
	private final ReceiveMsgBypassInboxRepository receiveMsgBypassInboxRepository;
	
	@Override
	public void createReceiveMsgBypassInboxAudit(Integer acccountId, Integer businessId,
			Integer customerId, String type, String channel) {
		log.info("log createReceiveMsgBypassInboxAudit for businessId : {} customerId : {}",businessId, customerId);
		ReceiveMsgBypassInboxAudit audit = new ReceiveMsgBypassInboxAudit(acccountId, businessId, customerId, type, channel);
		receiveMsgBypassInboxRepository.saveAndFlush(audit);
	}

}
