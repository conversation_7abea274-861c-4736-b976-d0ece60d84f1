package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.InstagramMessageRepository;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendInstagramMessage;
import com.birdeye.messenger.dto.SendInstagramMessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.instagram.Messaging;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.InstagramMessageStatusEnum;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InstagramException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.InstagramEventService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class InstagramEventServiceImpl implements InstagramEventService {

	private final SocialService socialService;
	private final InstagramMessageRepository instagramMessageRepository;
	private final MessengerContactService messengerContactService;
	private final RedisHandler redisHandler;

	@Override
	public InstagramMessage saveInstagramMessage(MessageDTO messageDTO, String senderId, String recipientId,
			String messageId, InstagramMessageStatusEnum status) {

		Integer encrypted = 0;
		String message;
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		if (messageDTO instanceof SendMessageDTO) {
			message = ((SendMessageDTO) messageDTO).getBody();
		} else {
			InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
			Messaging messaging = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
			message = messaging.getMessage().getText();
			messengerContact.setLastMessage(message);
			if (StringUtils.isNotBlank(message) && messaging.getMessage().getIs_echo()) {
				messengerContact.setLastMessage("You: " + message);
			}
		}

		try {
			if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotBlank(message)) {
				message = EncryptionUtil.encrypt(message, StringUtils.join(senderId, recipientId),
						StringUtils.join(recipientId, senderId));
				encrypted = 1;
			}
		} catch (Exception e) {
			log.info("Encryption for recieved Facebook message failed: {}", e);
		}
		InstagramMessage igMessage = new InstagramMessage();
		igMessage.setCreateDate(new Date());
		igMessage.setMessengerContactId(messengerContact.getId());
		igMessage.setRecipientInstagramId(recipientId);
		igMessage.setSenderInstagramId(senderId);
		if (StringUtils.isNotBlank(message)) {
			igMessage.setMessageBody(message);
		}
		if (messageId.substring(0, 2).equalsIgnoreCase("m_"))
			igMessage.setMessageId(messageId.subSequence(2, messageId.length()).toString());
		else
			igMessage.setMessageId(messageId);
		igMessage.setSentOn(new Date());
		igMessage.setEncrypted(encrypted);
		igMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
		if (messageDTO.getMessengerMediaFileDTO() != null
				&& StringUtils.isNotBlank(messageDTO.getMessengerMediaFileDTO().getUrl())) {
			igMessage.setMediaURL(messageDTO.getMessengerMediaFileDTO().getUrl());
		}
		try {
			instagramMessageRepository.saveAndFlush(igMessage);
		} catch (Exception e) {
			log.error("Duplicate instagram message message id: {}: ", messageId);
			// throw new DuplicateEntryException(ErrorCode.DUPLICATE_MESSAGE);
		}
		return igMessage;

	}

	@Override
	public SendInstagramMessageResponse sendIGCallToSocial(InstagramSendEventHandler handler, MessageDTO messageDTO,
			String pageId) {
		SendInstagramMessage sendIgMessage = null;
		String body = ((SendMessageDTO) messageDTO).getBody();
		if (StringUtils.isBlank(pageId)) {
			throw new NotFoundException(ErrorCode.INSTAGRAM_PAGE_NOT_FOUND);
		}
		// TODO get senderId from businessSMS table
		ResponseEntity<String> res = null;
		MessengerMediaFileDTO messengerMediaFileDTO = messageDTO.getMessengerMediaFileDTO();

		String receiverId = handler.getMessengerContact(messageDTO).getInstagramConversationId();
		if (messengerMediaFileDTO != null) {
			String fileType = "image";
			sendIgMessage = new SendInstagramMessage(fileType, null, pageId, receiverId,
					messengerMediaFileDTO.getUrl(),((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId());
			// send message to instagram
			res = socialService.sendInstagramMessage(sendIgMessage);
			if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
				String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
				if (responseBodyAsString.contains("2534014"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_USER_NOT_FOUND, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				if (responseBodyAsString.contains("2534037"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_ACTION, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				if (responseBodyAsString.contains("2534025"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_PRIVATE_REPLY, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534015"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.INVALID_MESSAGE_DATA, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2018320"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_PRODUCT_ID, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534013"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.PAGE_IS_NOT_LINKED_TO_INSTAGRAM, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534029"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SEND_BLOCKED_FOR_BUSINESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534022"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_OUTSIDE_ALLOWED_WINDOW, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534040"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_RATE_LIMIT_EXCEEDS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534041"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_DISABLED_MESSAGE_ACCESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534038"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_TEXT_SIZE_EXCEEDS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("errorCode: 190"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.PAGE_MESSAGING_PERMISSION, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2018001"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_CUSTOMER_NOT_AVAILABLE, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534001"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_MISSING_USER_MESSAGING_ACCESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("3003"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_BUSINESS_BLOCKED_CUSTOMER, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if(responseBodyAsString.contains("2534023"))
					throw new InstagramException((new ErrorMessageBuilder(ErrorCode.COMMENT_YOU_ARE_TRYING_TO_REPLY_TO_ALREADY_REPLIED, ComponentCodeEnum.IG,HttpStatus.BAD_REQUEST)));
				else
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_API_ERROR, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
			}
		}
		if (StringUtils.isNotEmpty(body)) {
			sendIgMessage = new SendInstagramMessage(null, body, pageId, receiverId, null,((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId());
			// send message to instagram
			res = socialService.sendInstagramMessage(sendIgMessage);
			if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
				String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
				if (responseBodyAsString.contains("2534014"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_USER_NOT_FOUND, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				if (responseBodyAsString.contains("2534037"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_ACTION, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				if (responseBodyAsString.contains("2534025"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_PRIVATE_REPLY, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534015"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.INVALID_MESSAGE_DATA, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2018320"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_INVALID_PRODUCT_ID, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534013"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.PAGE_IS_NOT_LINKED_TO_INSTAGRAM, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534029"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SEND_BLOCKED_FOR_BUSINESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534022"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_OUTSIDE_ALLOWED_WINDOW, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534040"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_RATE_LIMIT_EXCEEDS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534041"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_DISABLED_MESSAGE_ACCESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534038"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_SENT_TEXT_SIZE_EXCEEDS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("errorCode: 190"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.PAGE_MESSAGING_PERMISSION, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2018001"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_CUSTOMER_NOT_AVAILABLE, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("2534001"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_MISSING_USER_MESSAGING_ACCESS, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if (responseBodyAsString.contains("3003"))
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_BUSINESS_BLOCKED_CUSTOMER, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
				else if(responseBodyAsString.contains("2534023"))
					throw new InstagramException((new ErrorMessageBuilder(ErrorCode.COMMENT_YOU_ARE_TRYING_TO_REPLY_TO_ALREADY_REPLIED, ComponentCodeEnum.IG,HttpStatus.BAD_REQUEST)));
				else
					throw new InstagramException(new ErrorMessageBuilder(ErrorCode.IG_API_ERROR, ComponentCodeEnum.IG, HttpStatus.BAD_REQUEST));
			}
		}
		return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),
				SendInstagramMessageResponse.class);
	}

	@Override
	public Boolean isInstagramSendAvailable(MessengerContact messengerContact, Integer routeId) {
		// Getting last message metadata
		log.info("isInstagramSendAvailable for mcId: {}", messengerContact.getId());
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		log.info("isInstagramSendAvailable lastMessageMetaData: {}",lastMessageMetaData);
		if (lastMessageMetaData == null || lastMessageMetaData.getLastIgReceivedAt() == null) {
			// fallback get from es
			log.info("isInstagramSendAvailable : fallback");
			return isIGSendAvailableHelper(messengerContact, routeId);
		} else {
			log.info("isInstagramSendAvailable : calculate");
			return calculateIGSendAvailable(messengerContact.getLeadSource(),
					lastMessageMetaData.getLastIgReceivedAt());
		}
	}

	private Boolean calculateIGSendAvailable(LeadSource leadSource, String lastIgReceivedAt) {
		boolean isLastInstagramMessage = false;
		if (leadSource == LeadSource.INSTAGRAM && StringUtils.isBlank(lastIgReceivedAt)) {
			return true;
		} else if (StringUtils.isNotBlank(lastIgReceivedAt)) {
			Calendar calLastReceived = Calendar.getInstance();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				calLastReceived.setTime(sdf.parse(lastIgReceivedAt));
				calLastReceived.add(Calendar.HOUR, 24 * 7);

				if (calLastReceived.getTime().before(Calendar.getInstance().getTime())) {
					isLastInstagramMessage = true;
				}

			} catch (Exception e) {
				log.error("exception in parsing date of calculateIGSendAvailable method: ", e);
			}
		}
		return isLastInstagramMessage;
	}

	private Boolean isIGSendAvailableHelper(MessengerContact messengerContact, Integer routeId) {
		MessengerFilter messengerFilter = new MessengerFilter();
		messengerFilter.setStartIndex(0);
		messengerFilter.setCount(1);
		messengerFilter.setConversationId(messengerContact.getId());
		messengerFilter.setAccountId(routeId);
		List<String> typeList = new ArrayList<>();
		typeList.add("SMS_RECEIVE");
		typeList.add("MMS_RECEIVE");
		Map<String, Object> params = new HashMap<>();
		params.put("msg_type", ControllerUtil.getJsonTextFromObject(typeList));
		params.put("source", "13");
		messengerFilter.setParams(params);
		String lastIgReceivedAt = "";
		// last facebook Received message
		List<MessageDocument> messages = messengerContactService.getMessagesFromES(messengerFilter);
		if (CollectionUtils.isNotEmpty(messages)) {
			lastIgReceivedAt = messages.get(0).getCr_date();
		}
		// TODO: remove this comment once all SEND and RECEIVE api migrated to
		// messenger-service
		// messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
		// messengerContactRepository.save(messengerContact);
		return calculateIGSendAvailable(messengerContact.getLeadSource(), lastIgReceivedAt);
	}

	@Override
	public String getInstagramIntegrationStatus(Integer businessId) {
		String response = redisHandler.getIgStatusFromCache(String.valueOf(businessId));
		if (StringUtils.isNotBlank(response)) {
			return ControllerUtil.getObjectFromJsonText(response, Map.class).get("status").toString();
		}
		Map<?, ?> responseFromSocial = null;
		responseFromSocial = socialService.getInstagramIntegrationStatusFromSocial(businessId);
		if (MapUtils.isNotEmpty(responseFromSocial)) {
			Object status = responseFromSocial.get("status");
			if (status != null) {
				response = responseFromSocial.get("status").toString();
				try {
					redisHandler.updateIgStatusCache(String.valueOf(businessId), responseFromSocial);
				} catch (JsonProcessingException e) {

				}
			}
		}
		return response;
	}

	@Override
	public Integer getBusinessIdByInstagramPageId(String businessInstagramId) {
		Integer businessId = null;
		Map<?, ?> responseFromSocial = null;
		responseFromSocial = socialService.getBusinessIdByInstagramPageId(businessInstagramId);
		if (MapUtils.isNotEmpty(responseFromSocial)) {
			businessId = (Integer) responseFromSocial.get("businessId");
		}
		return businessId;
	}

}
