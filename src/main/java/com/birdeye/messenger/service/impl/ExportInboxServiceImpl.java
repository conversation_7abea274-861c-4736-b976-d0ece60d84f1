package com.birdeye.messenger.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.ExportAllMessagesResponse;
import com.birdeye.messenger.dto.ExportInboxConversationData;
import com.birdeye.messenger.dto.ExportInboxRequest;
import com.birdeye.messenger.dto.ExportInboxResponse;
import com.birdeye.messenger.dto.MessengerFilterForExportInbox;
import com.birdeye.messenger.dto.Responder;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.ExportInboxService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.ControllerUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ExportInboxServiceImpl implements ExportInboxService {

	private final MessengerContactService messengerContactService;
	private final BusinessService businessService;
	private final ElasticSearchExternalService elasticSearchService;
	private final ObjectMapper objectMapper;

	@Override
	@SuppressWarnings("unchecked")
	public ExportInboxResponse fetchInboxMessages(ExportInboxRequest request) {
		if (request.getSize() > 100) {
			log.error("fetchInboxMessages: Page size can not be more than 100 : {} ", request.getBusinessId());
			throw new InputValidationException(ErrorCode.PAGE_SIZE_EXCEEDS, "Page size can not be more than 100");
		}
		BusinessDTO businessDTO = fetchBusinessDTOFromCore(request);

		// Set Formatted dates in request
		setFormattedStartEndDates(request);

		ExportInboxResponse exportInboxResponse = null;
		Integer totalConvCount = 0;
		Integer totalMsgsCount = 0;
		boolean hasMore = false;
		ElasticData messageData = null;
		ElasticData conversationData = null;
		if (Objects.nonNull(request.getContact()) && (StringUtils.isNotBlank(request.getContact().getEmailId())
				|| StringUtils.isNotBlank(request.getContact().getPhone()))) {
			// Fetch conversations by customer ID
			MessengerFilterForExportInbox esQueryDataForConversations = new MessengerFilterForExportInbox(request,
					businessDTO.getRoutingId());
			conversationData = messengerContactService.getConversationDataForExport(esQueryDataForConversations,false);
			if (!conversationData.isSucceeded() || CollectionUtils.isEmpty(conversationData.getResults())) {
				log.warn(
						"fetchInboxMessages: Failure in fetching conversation data from ES for business : {} , contact : {} ",
						request.getBusinessId(), request.getContact().getId());
				return new ExportInboxResponse();
			}
			List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();
			List<Integer> contactIds = contactDocuments.stream().map(ContactDocument::getM_c_id)
					.collect(Collectors.toList());

			// Fetch messages for the above conversations by mc_id(s)
			MessengerFilterForExportInbox messengerFilter = getMessageDataFromEs(request, businessDTO, contactIds);
			messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_BY_CONTACTS_EXPORT);
			messageData = messengerContactService.getMessageDataForExport(messengerFilter);
			if (!messageData.isSucceeded()) {
				log.warn(
						"fetchInboxMessages: Failure in fetching message data from ES for business : {} , contact : {} ",
						request.getBusinessId(), request.getContact().getId());
				return new ExportInboxResponse();
			}
			List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
			Map<String, List<MessageDocument>> messagesPerContact = messageDocuments.stream()
					.collect(Collectors.groupingBy(MessageDocument::getC_id));
			if (MapUtils.isEmpty(messagesPerContact)) {
				log.warn("No conversations found for business {} in date range : {} to {} ", request.getBusinessId(),
						request.getStartDate(), request.getEndDate());
				return new ExportInboxResponse();
			}
			exportInboxResponse = new ExportInboxResponse(contactDocuments, messagesPerContact,
					businessDTO.getTimeZoneId());
		} else {
			// Fetch messages by business id/enterprise id
			MessengerFilterForExportInbox messengerFilter = getMessageDataFromEs(request, businessDTO, null);
			messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_FOR_EXPORT);
			messageData = messengerContactService.getMessageDataForExport(messengerFilter);
			if (!messageData.isSucceeded()) {
				log.warn("fetchInboxMessages: Failure in fetching message data from ES for business : {} ",
						request.getBusinessId());
				return new ExportInboxResponse();
			}
			List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
			log.info("fetchInboxMessages, size of messages :{} ", messageDocuments.size());
			Map<String, List<MessageDocument>> messagesPerContact = messageDocuments.stream()
					.collect(Collectors.groupingBy(MessageDocument::getC_id));
			if (MapUtils.isEmpty(messagesPerContact)) {
				log.warn("No conversations found for business {} in date range : {} to {} ", request.getBusinessId(),
						request.getStartDate(), request.getEndDate());
				return new ExportInboxResponse();
			}
			MessengerFilterForExportInbox esQueryDataForConversations = new MessengerFilterForExportInbox(
					messagesPerContact.keySet(), businessDTO.getRoutingId());
			conversationData = messengerContactService.getConversationDataForExport(esQueryDataForConversations,false);
			if (!conversationData.isSucceeded()) {
				log.warn("fetchInboxMessages: Failure in fetching conversation data from ES for business : {} ",
						request.getBusinessId());
				return new ExportInboxResponse();
			}
			List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();
			log.info("fetchInboxMessages, size of contacts :{} ", contactDocuments.size());
			exportInboxResponse = new ExportInboxResponse(contactDocuments, messagesPerContact,
					businessDTO.getTimeZoneId());
		}
		addResponderInMessages(exportInboxResponse);
		totalMsgsCount = messageData.getTotal().intValue();
		totalConvCount = conversationData.getTotal().intValue();
		hasMore = request.getOffset() + request.getSize() < totalMsgsCount;
		exportInboxResponse.setHasMore(hasMore);
		exportInboxResponse.setTotalMessages(totalMsgsCount);
		exportInboxResponse.setTotalConversations(totalConvCount);
		return exportInboxResponse;
	}

	private void addResponderInMessages(ExportInboxResponse exportInboxResponse) {
		if (CollectionUtils.isNotEmpty(exportInboxResponse.getConversations())) {
			Set<Integer> userIds = new HashSet<Integer>();
			exportInboxResponse.getConversations().forEach(conv -> {
				populateSenderUserIds(conv, userIds);

			});
			if (CollectionUtils.isNotEmpty(userIds)) {
				List<User> users = businessService.getUsersByIds(new ArrayList<Integer>(userIds));
				Map<Integer, User> idUserMap = getIdUserMap(users);
				if (MapUtils.isNotEmpty(idUserMap)) {
					exportInboxResponse.getConversations().forEach(conv -> {
						if (CollectionUtils.isNotEmpty(conv.getMessages())) {
							conv.getMessages().forEach(msg -> {
								if (CommunicationDirection.SEND.equals(msg.getDirection()) && msg.getUserId() != null) {
									User user = idUserMap.get(msg.getUserId());
									if (user != null) {
										Responder responder = getResponder(user);
										msg.setResponder(responder);
									}
								}
							});
						}
					});
				}
			}
		}
	}

	private Responder getResponder(User user) {
		Responder responder=new Responder();
		responder.setId(user.getId());
		responder.setName(user.getName());
		responder.setEmailId(user.getEmailId());
		return responder;
	}

	private Map<Integer, User> getIdUserMap(List<User> users) {
		 Map<Integer, User> idUserMap=new HashMap<Integer,User>();
		 if(CollectionUtils.isNotEmpty(users)) {
			 users.forEach(user->{
				 idUserMap.put(user.getId(),user);
			 });
		 }
		return idUserMap;
	}

	/**
	 * @param request
	 * @return
	 */
	private BusinessDTO fetchBusinessDTOFromCore(ExportInboxRequest request) {
		BusinessDTO businessDTO = null;
		if (null != request.getBusinessId()) {
			businessDTO = businessService.getBusinessDTO(request.getBusinessId());
		} else {
			businessDTO = businessService.getBusinessDTO(request.getEnterpriseId());
		}
		if (Objects.isNull(businessDTO)) {
			log.warn("getMessageV2: businessDTO returned null from core-service for businessId {}",
					request.getBusinessId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		return businessDTO;
	}

	/**
	 * @param request
	 * @param businessDTO
	 * @return
	 */
	private MessengerFilterForExportInbox getMessageDataFromEs(ExportInboxRequest request, BusinessDTO businessDTO,
			List<Integer> mcIds) {
		MessengerFilterForExportInbox messengerFilter = new MessengerFilterForExportInbox();
		if(businessDTO != null){
			messengerFilter.setAccountId(businessDTO.getRoutingId());
		}
		messengerFilter.setBusinessId(request.getBusinessId());
		messengerFilter.setEnterpriseId(request.getEnterpriseId());
		messengerFilter.setFrom(request.getOffset());
		messengerFilter.setSize(request.getSize());
		messengerFilter.setStartDate(request.getStartDate());
		messengerFilter.setEndDate(request.getEndDate());
		messengerFilter.setContactIds(ControllerUtil.toCommaSeparatedString(mcIds));
		if (request.getSessionTime() != null)
			messengerFilter.setSessionTime(request.getSessionTime().trim());
		if (request.getExcludeCampaignMessages() != null && request.getExcludeCampaignMessages() ==1)
			messengerFilter.setExcludeCampaignMessages(request.getExcludeCampaignMessages());
		return messengerFilter;
	}

	/**
	 * @param request
	 */
	private void setFormattedStartEndDates(ExportInboxRequest request) {
		try {
			LocalDate startTime = LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern("MM/dd/yyyy"));
			LocalDate endTime = LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern("MM/dd/yyyy"));
			String from = LocalDateTime.of(startTime, LocalTime.MIN)
					.format(DateTimeFormatter.ofPattern(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
			String to = LocalDateTime.of(endTime, LocalTime.MAX)
					.format(DateTimeFormatter.ofPattern(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
			request.setStartDate(from);
			request.setEndDate(to);
		} catch (Exception e) {
			log.error("Error in date parsing for business {}, startDate : {}, endDate : {}", request.getBusinessId(),
					request.getStartDate(), request.getEndDate(), e);
			throw new InputValidationException(
					"Error in parsing start and end dates. Please enter in correct format MM/dd/yyyy ");
		}
	}
	private void populateSenderUserIds(ExportInboxConversationData conversation, Set<Integer> userIds) {
		if (CollectionUtils.isNotEmpty(conversation.getMessages())) {
			conversation.getMessages().forEach(msg -> {
				if (CommunicationDirection.SEND.equals(msg.getDirection()) && msg.getUserId() != null && msg.getUserId()!=0) {
					userIds.add(msg.getUserId());
				}
			});
		}
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public ExportAllMessagesResponse fetchAllInboxMessages(ExportInboxRequest request) {
		if (request.getSize() > 5000) {
			log.error("fetchInboxMessages: Page size can not be more than 5000 : {} ", request.getBusinessId());
			throw new InputValidationException(ErrorCode.PAGE_SIZE_EXCEEDS, "Page size can not be more than 5000");
		}
		BusinessDTO businessDTO = fetchBusinessDTOFromCore(request);

		// Set Formatted dates in request
		setFormattedStartEndDates(request);
		
		SearchResponse searchResponse = null;
		String scrollId = request.getScrollId();
		if (StringUtils.isNotBlank(scrollId) && "START".equals(scrollId)) {
			searchResponse = getSearchResult(request, businessDTO);
		} else {
			log.info("fetchAllInboxMessages: scrollId : {} ", scrollId);
			searchResponse = elasticSearchService.readMoreFromSearch(scrollId, request.getSessionTime().trim());
			if (searchResponse!=null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getSearchResult(request, businessDTO);
			}
		}
		ElasticData conversationData = null;
		ExportAllMessagesResponse exportInboxResponse = null;
		if (java.util.Objects.nonNull(searchResponse)) {
			List<MessageDocument> inboxMessages = getMessagesListFromResponse(searchResponse);
			Map<String, List<MessageDocument>> messagesPerContact = inboxMessages.stream()
					.collect(Collectors.groupingBy(MessageDocument::getC_id));
			if (MapUtils.isEmpty(messagesPerContact)) {
				log.warn("No conversations found for business {} in date range : {} to {} ", request.getBusinessId(),
						request.getStartDate(), request.getEndDate());
				return new ExportAllMessagesResponse();
			}
			MessengerFilterForExportInbox esQueryDataForConversations = new MessengerFilterForExportInbox(
					messagesPerContact.keySet(), businessDTO.getRoutingId());
			conversationData = messengerContactService.getConversationDataForExport(esQueryDataForConversations,false);
			if (!conversationData.isSucceeded()) {
				log.warn("fetchInboxMessages: Failure in fetching conversation data from ES for business : {} ",
						request.getBusinessId());
				return new ExportAllMessagesResponse();
			}
			List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();
			Map<Integer, ContactDocument> idVsContactMap = contactDocuments.stream()
					.collect(Collectors.toMap(ContactDocument::getM_c_id, Function.identity()));
			exportInboxResponse = new ExportAllMessagesResponse(inboxMessages, idVsContactMap,
					businessDTO.getTimeZoneId());
		}
		scrollId = searchResponse.getScrollId();
		exportInboxResponse.setScrollId(scrollId);
		return exportInboxResponse;
	}

	private List<MessageDocument> getMessagesListFromResponse(SearchResponse searchResponse) {
		List<MessageDocument> msgList = new ArrayList<>();
		if(Objects.nonNull(searchResponse)) {
        	SearchHit[] searchHit = searchResponse.getHits().getHits();

    		if (searchHit.length > 0) {
    			Arrays.stream(searchHit).forEach(hit -> msgList
    			.add(objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class)));
    		}
        }
		return msgList;
	}

	private SearchResponse getSearchResult(ExportInboxRequest request, BusinessDTO businessDTO) {
		MessengerFilterForExportInbox messengerFilter = getMessageDataFromEs(request, businessDTO, null);
		messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_FOR_EXPORT);
		return messengerContactService.getAllMessageDataForExport(messengerFilter);
	}

	public ExportAllMessagesResponse fetchAllInboxMessagesNLP(ExportInboxRequest request) {
		if (request.getSize() > 5000) {
			log.error("fetchInboxMessages: Page size can not be more than 5000 : {} ", request.getBusinessId());
			throw new InputValidationException(ErrorCode.PAGE_SIZE_EXCEEDS, "Page size can not be more than 5000");
		}
		BusinessDTO businessDTO = fetchBusinessDTOFromCore(request);

		// Set Formatted dates in request
		setFormattedStartEndDates(request);

		SearchResponse searchResponse = null;
		String scrollId = request.getScrollId();
		if (StringUtils.isNotBlank(scrollId) && "START".equals(scrollId)) {
			searchResponse = getSearchResultNLP(request, businessDTO);
		} else {
			log.info("fetchAllInboxMessages: scrollId : {} ", scrollId);
			searchResponse = elasticSearchService.readMoreFromSearch(scrollId, "10m");
			if (searchResponse!=null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getSearchResultNLP(request, businessDTO);
			}
		}
		ElasticData conversationData = null;
		ExportAllMessagesResponse exportInboxResponse = null;
		if (java.util.Objects.nonNull(searchResponse)) {
			List<MessageDocument> inboxMessages = getMessagesListFromResponse(searchResponse);
			Map<String, List<MessageDocument>> messagesPerContact = inboxMessages.stream()
					.collect(Collectors.groupingBy(MessageDocument::getC_id));
			if (MapUtils.isEmpty(messagesPerContact)) {
				log.warn("No conversations found for business {} in date range : {} to {} ", request.getBusinessId(),
						request.getStartDate(), request.getEndDate());
				return new ExportAllMessagesResponse();
			}
			MessengerFilterForExportInbox esQueryDataForConversations = null;
			esQueryDataForConversations = new MessengerFilterForExportInbox(
						messagesPerContact.keySet());


			conversationData = messengerContactService.getConversationDataForExport(esQueryDataForConversations,request.isFilterCampaign());
			if (!conversationData.isSucceeded()) {
				log.warn("fetchInboxMessages: Failure in fetching conversation data from ES for business : {} ",
						request.getBusinessId());
				return new ExportAllMessagesResponse();
			}
			List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();

			Map<String, ContactDocument> idVsContactMap = contactDocuments.stream()
					.collect(Collectors.toMap(c-> c.getM_c_id()+"-"+c.getE_id(), Function.identity()));
			exportInboxResponse = new ExportAllMessagesResponse(inboxMessages, idVsContactMap);

		}
		scrollId = searchResponse.getScrollId();
		exportInboxResponse.setScrollId(scrollId);
		return exportInboxResponse;
	}

	private SearchResponse getSearchResultNLP(ExportInboxRequest request, BusinessDTO businessDTO) {
		MessengerFilterForExportInbox messengerFilter = getMessageDataFromEs(request, businessDTO, null);
		messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_FOR_EXPORT);
		messengerFilter.setSessionTime("10m");
		return messengerContactService.getAllMessageDataForExport(messengerFilter);
	}
}