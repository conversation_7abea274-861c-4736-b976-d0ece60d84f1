package com.birdeye.messenger.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.repository.BusinessWebchatEventRepository;
import com.birdeye.messenger.dto.BusinessWebchatEventDetails;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.BusinessWebchatEventService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class BusinessWebchatEventServiceImpl implements BusinessWebchatEventService {

    @Autowired
    BusinessWebchatEventRepository businessWebchatEventRepository;

    @Autowired
    BusinessChatWidgetConfigService businessChatWidgetConfigService;


    @Override
    public List<BusinessWebchatEventDetails> findDistinctEventsInLast24Hours(Pageable pageable) {
        log.info("findDistinctEventsInLast24Hours called with pagerequest : {}", pageable);
        List<Object[]> objects = businessWebchatEventRepository.findDistinctEventsInLast24Hours(pageable);
        log.info("objects found : {}", objects);
        List<BusinessWebchatEventDetails> businessWebchatEventDetailsList = objects.stream()
                .map(BusinessWebchatEventDetails::new).collect(Collectors.toList());
        return businessWebchatEventDetailsList;
    }

    @Override
    public List<String> findWebsitesByWidgetId(Integer widgetId) {
        List<String> websites = businessWebchatEventRepository.findWebsitesByWidgetId(widgetId);
        return websites;
    }

    @Override
    public BusinessWebchatEventDetails findDistinctEventsInLast24HoursByWidgetId(Integer widgetId){
        List<Object[]> objects = businessWebchatEventRepository.findDistinctEventsInLast24HoursByWidgetId(widgetId);
        BusinessWebchatEventDetails businessWebchatEventDetails = new BusinessWebchatEventDetails();
        for (Object[] object : objects) {
            businessWebchatEventDetails.setWidgetId((Integer) object[0]);
            businessWebchatEventDetails.setWebsites((String) object[1]);
        }
        return businessWebchatEventDetails;
    }

    @Override
    public Integer findDistinctEventsCountInLast24Hours() {
        return businessWebchatEventRepository.findDistinctEventsCountInLast24Hours();
    }

}
