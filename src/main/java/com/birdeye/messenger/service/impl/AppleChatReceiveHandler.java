package com.birdeye.messenger.service.impl;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.AppleMessage;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dto.QueroFAQResponse.FAQ;
import com.birdeye.messenger.dto.QueroFAQResponse.Question;
import com.birdeye.messenger.dto.apple.chat.AppleQuickReplyDto;
import com.birdeye.messenger.dto.apple.chat.AppleUploadAttachmentRequest;
import com.birdeye.messenger.dto.apple.chat.AppleUserMessage;
import com.birdeye.messenger.dto.apple.chat.AppleUserMessage.Attachment;
import com.birdeye.messenger.dto.apple.chat.AppleUserMessage.UserInfo;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreDownloadRequest;
import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse;
import com.birdeye.messenger.dto.apple.chat.TopFaqGetResponse.GetFaqResponse;
import com.birdeye.messenger.dto.elastic.AppleContextDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.RobinResponseType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.AppleChatSessionEnum;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;
import com.birdeye.messenger.enums.AppleMessageStatusEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.IntentType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.AppleMessagingException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.dto.ChatbotReplyDTO;
import com.birdeye.messenger.external.dto.Suggestion;
import com.birdeye.messenger.external.dto.Suggestion.Type;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.external.service.ChatbotService.QueryChannel;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.LiveChatService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.QueroService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.RobinService;
import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.service.apple.messaging.AppleSocialIntegrationService;
import com.birdeye.messenger.service.apple.messaging.LocationTeamMappingService;
import com.birdeye.messenger.service.apple.messaging.TopFaqService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.AmazonSQSUtility;
import com.birdeye.messenger.util.BusinessHoursUtility;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AppleChatReceiveHandler extends MessageEventHandlerAbstract {

    private final AppleSocialIntegrationService appleSocialIntegrationService;
    
    @Autowired
    private ConversationActivityService conversationActivityService;

    private final BusinessService businessService;
    
    private final AppleSendHandler appleSendHandler;
    
    private final MessengerMessageService messengerMessageService;
    
    private final LiveChatService liveChatService;
   
    private final MessengerMediaFileService messengerMediaFileService;

    private final PulseSurveyService pulseSurveyService;
    
	private final ChatbotService chatbotService;

	private final RobinService robinService;
	
	@Value("${amazonSQS.apple.quick.reply.event.queue}")
	private String sqsQueueUrl;
	
	@Autowired
	private AmazonSQSUtility amazonSQSUtility;
	
	@Autowired
    private RedisLockService redisLockService;
	
	private final MessengerEvent EVENT = MessengerEvent.APPLE_RECEIVE;

    private final TopFaqService topFaqService;

    private final QueroService queroService;
    
    private final LocationTeamMappingService locationTeamMappingService;
    
    private final WebchatService webchatService;
    
    private final CommonService commonService;

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
    	AppleUserMessage event = (AppleUserMessage) messageDTO;
    	log.info("Apple: receive handler called, request data {}", event);
    	setMessageBodyForInterActiveMessage(event);
    	if (Integer.valueOf(0).equals(businessService.isMessengerEnabled(getBusinessDTO(messageDTO).getAccountId()))) {
    		log.info("AppleReceiveHandler: Messenger disabled for business {}", getBusinessDTO(messageDTO).getAccountId());
    		return new MessageResponse();
    	}
    	CustomerDTO customerDTO = getOrCreateCustomerDTO(messageDTO);
    	Optional<Lock> lockOpt = Optional.empty();
    	try {
    		String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
    		lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
    		if (!lockOpt.isPresent()) {
    			log.info("Lock is already acquired for the key:{}", lockKey);
    			kafkaService.publishToKafkaAsync(KafkaTopicEnum.APPLECHAT_RECEIVE_EVENT_DELAYED_QUEUE,
    					event);
    			return null;
    		}
    		if ("close".equals(event.getType())) {
    			handleCustomerOptOut(messageDTO);
    			return null;
    		}
            Optional<MessengerContact> optMessengerContact = messengerContactService.getByAppleConversationIdAndSubaccountId(event.getSourceId(), getBusinessDTO(messageDTO).getBusinessId());
            
    		if (Constants.APPLE_TYPING_END_EVENT.equals(event.getType()) || Constants.APPLE_TYPING_START_EVENT.equals(event.getType())) {
    			if(optMessengerContact.isPresent()) {
    				liveChatService.updateFirebaseMessageForAppleTypingEvent(null,
    						getBusinessDTO(messageDTO).getBusinessId(), optMessengerContact.get().getId(),
    						BooleanUtils.isTrue(Constants.APPLE_TYPING_START_EVENT.equals(event.getType())));
    			}
    			return null;
    		}
            if (optMessengerContact.isPresent()) {
                liveChatService.updateFirebaseMessageForAppleTypingEvent(null,
                        getBusinessDTO(messageDTO).getBusinessId(), optMessengerContact.get().getId(), false);
            }

    		//1. Set type to save last message state for response time calculation
    		messageDTO.setMsgTypeForResTimeCalc("R");
    		//2.Drop the message if received from same requestId

			//3.Name all user's as Annonymous Apple user
			event.setUserInfo(new UserInfo());
			event.getUserInfo().setDisplayName(Constants.ANNONYMOUS_APPLE_USER + " "
					+ event.getSourceId().substring(event.getSourceId().length() - 4));
			//4. Save back up message in mySQl
			MessageDocumentDTO messageDocumentDTO = saveInSecondaryStorage(messageDTO);
			messageDTO.setMessageDocumentDTO(messageDocumentDTO);
			saveAttachments(messageDTO,Integer.valueOf(messageDocumentDTO.getM_id()));
			messageDTO.setSource(Source.APPLE.getSourceId());
			String oldLmsgOn = getOldLmsgOn(messageDTO);
			PulseSurveyContext context = null;
			try {
				context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
					customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					customerDTO.setOngoingPulseSurvey(false);
				}
				messageDTO.setCustomerDTO(customerDTO);
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}
			super.handle(messageDTO);
	        ContactDocument contactDocument = messengerContactService.getContact(messageDTO.getBusinessDTO().getAccountId(), messageDTO.getMessengerContact().getId());
    		createOrUpdateAppleContext(messageDTO.getMessengerContact().getAppleConversationId(),contactDocument, event);
    		robinReply(messageDTO, contactDocument, event, oldLmsgOn);
    		return null;
    	}  finally {
    		if (lockOpt.isPresent()) {
    			redisLockService.unlock(lockOpt.get());
    		}
    	}
    }

	private String getOldLmsgOn(MessageDTO messageDTO) {
		ContactDocument contactDocument = null;
		try {
			contactDocument = messengerContactService.getContact(messageDTO.getBusinessDTO().getAccountId(),
					messageDTO.getMessengerContact().getId());
			return contactDocument.getL_msg_on();
		} catch (Exception e) {
			return null;
		}

	}

	private void createOrUpdateAppleContext(String  appleConversationId, ContactDocument contactDocument, AppleUserMessage event) {
        if(contactDocument.getAppleContextDocument()==null) {
        	contactDocument.setAppleContextDocument(new AppleContextDocument());
        	contactDocument.getAppleContextDocument().setAppleChatSession(AppleChatSessionEnum.AUTO);
        	contactDocument.getAppleContextDocument().setApple_conversation_id(appleConversationId);
        }
        contactDocument.getAppleContextDocument().setAppleOptOut(false);
        contactDocument.getAppleContextDocument().setCapabilityList(event.getCapabilityList());
        messengerContactService.updateContactDocumentOnES(contactDocument, contactDocument.getId().toString(), contactDocument.getE_id());
	}

	private void setMessageBodyForInterActiveMessage(AppleUserMessage event) {
		if(event.getInteractiveData()!=null && event.getInteractiveData().getData()!=null && event.getInteractiveData().getData().getReplyMessage()!=null && StringUtils.isNotBlank(event.getInteractiveData().getData().getReplyMessage().getTitle())) {
			event.setBody(event.getInteractiveData().getData().getReplyMessage().getTitle());
            event.setAttachments(null);
		}
		if (Objects.nonNull(event.getInteractiveData()) && Objects.nonNull(event.getInteractiveData().getData())
				&& Objects.nonNull(event.getInteractiveData().getData().getQuickReply())) {
			String selectedIdentifier = event.getInteractiveData().getData().getQuickReply().getSelectedIdentifier();
			if (StringUtils.isNotBlank(selectedIdentifier) && selectedIdentifier.equals("1")) { // when customer choose
																								// // yes in feedback
																								// reply
				event.setBody(MessengerConstants.FEEDBACK_MESSAGE_YES);
			} else {
				event.setBody(MessengerConstants.FEEDBACK_MESSAGE_NO);
			}
		}
	}

	private void robinReply(MessageDTO messageDTO, ContactDocument contactDocument, AppleUserMessage event, String oldLmsgOn) throws Exception {
		String body = event.getBody();
		boolean tapback = checkBodyForTapback(body);
		if (tapback) {
			log.info("Text matches the tapback pattern. No autoreply.");
			return;
		}
		if(Objects.nonNull(contactDocument.getBlocked()) && BooleanUtils.isTrue(contactDocument.getBlocked())){
			log.info("contact is blocked robin/auto reply cannot be send for customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
			return ;
		}
		RobinAutoReplyConfig robinAutoReplyConfig = robinService.getRobinAutoReplyConfigForBusiness(contactDocument.getB_id(),contactDocument.getE_id(),Source.APPLE);
		if(robinAutoReplyConfig != null && BooleanUtils.isFalse(robinAutoReplyConfig.isEnableRobin())){
			log.info("robin/auto reply config not enabled for customerId {} and businessId {}",contactDocument.getC_id(),contactDocument.getB_id());
			return;
		}
		BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(contactDocument.getB_id(), false);
		Boolean isReceivedDuringBusinessHour = null;
        Boolean welcomeMessageSent = false;
		if (businessTimingDTO != null) {
			isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
					new Date());
		}
		if (Objects.nonNull(event.getInteractiveData()) && Objects.nonNull(event.getInteractiveData().getData())
				&& Objects.nonNull(event.getInteractiveData().getData().getQuickReply())) {
			String selectedIdentifier = event.getInteractiveData().getData().getQuickReply().getSelectedIdentifier();
			if (StringUtils.isNotBlank(selectedIdentifier) && selectedIdentifier.equals("1")) { // when customer choose
																								// yes in feedback
																								// response
				String message = MessengerConstants.FEEDBACK_RESPONSE_YES;
				appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), message, null, null, false, null);
			} else { // when customer chooses no in response
				String message = MessengerConstants.OKAY_MESSAGE;
				appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), message, null, null, false, null);
				message = MessengerConstants.APPLE_ROBIN_ONLINE_MESSAGE;
				if (BooleanUtils.isFalse(isReceivedDuringBusinessHour)) {
					message = MessengerConstants.APPLE_ROBIN_OFFLINE_MESSAGE;
				}
				appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), message, null, null, false, null);
				updateContactDocument(contactDocument, null, AppleChatSessionEnum.MANUAL);
				notificationService.processUnreadMessageNotification(messageDTO.getBusinessDTO(),
						messageDTO.getMessageDocument());
			}
			return;
		}
		String welcomeMessage = MessengerConstants.APPLE_ROBIN_WELCOME_MESSAGE;
		//Update session to MANUAL if user want to talk to an agent
		if(StringUtils.isNotBlank(event.getBody()) && MessengerConstants.TALK_TO_EXECUTIVE.equals(event.getBody())) {
			String message = MessengerConstants.APPLE_ROBIN_ONLINE_MESSAGE;
			if (BooleanUtils.isFalse(isReceivedDuringBusinessHour)) {
				message = MessengerConstants.APPLE_ROBIN_OFFLINE_MESSAGE;
			}
			appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), message, null, null, false, null);
			updateContactDocument(contactDocument,null,AppleChatSessionEnum.MANUAL);
			notificationService.processUnreadMessageNotification(messageDTO.getBusinessDTO(), messageDTO.getMessageDocument());
		}else if (contactDocument.getAppleContextDocument() != null	&& AppleChatSessionEnum.AUTO.equals(contactDocument.getAppleContextDocument().getAppleChatSession())) {
			Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
				log.info("[AppleChatReceiveHandler] receive message chatsessionstatus is AUTO for businessId:{}", contactDocument.getB_id());
				chatbotQueryResponseOptional = chatbotService
						.getQueryResponse(contactDocument.getB_id(), contactDocument.getE_id(), event.getBody(), QueryChannel.LIVECHAT,
						contactDocument.getC_id(), contactDocument.getM_c_id(), isReceivedDuringBusinessHour, new LivechatAnonymousConversationDataDto(), null);
				if (contactDocument.getAppleContextDocument() != null
						&& (!BooleanUtils.isTrue(contactDocument.getAppleContextDocument().getWelcomeMessageSent()))) {
					appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), welcomeMessage, null, null, true,
							null);
					updateContactDocument(contactDocument, true, null);
                    welcomeMessageSent = true;
                }
				sendRobinReply(event, contactDocument, messageDTO.getMessageDocument(),
                        chatbotQueryResponseOptional, event.getBody(), isReceivedDuringBusinessHour,
                        welcomeMessageSent);
		}else if(AppleChatSessionEnum.MANUAL.equals(contactDocument.getAppleContextDocument().getAppleChatSession())) {
			//handover to robin if no message is exchanged in last 24 hours
			if (ControllerUtil.getTimeDifferenceInHours(oldLmsgOn,
					Constants.FORMAT_YYYY_MM_DD_HH_MM_SS) >= 24) {

				changeSessionStatus(messageDTO, event, contactDocument, welcomeMessage, isReceivedDuringBusinessHour);
			} else {
				if (BooleanUtils.isFalse(isReceivedDuringBusinessHour)) {
					String closingMessage = MessengerConstants.APPLE_ROBIN_OFFLINE_MESSAGE;
					appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), closingMessage, null, null, false,
							null);
				}
				notificationService.processUnreadMessageNotification(messageDTO.getBusinessDTO(),
						messageDTO.getMessageDocument());
			}
		}
	}

	private boolean checkBodyForTapback(String body) {
		if (StringUtils.isEmpty(body)) {
			return false;
		}

		String regex = "^(Loved|Liked|Disliked|Laughed at|Emphasised|Questioned)\\s*“[^“”]+”$";

		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(body);

		if (matcher.matches()) {
			return true;
		}
		return false;
	}

	private void changeSessionStatus(MessageDTO messageDTO, AppleUserMessage event, ContactDocument contactDocument,
			String welcomeMessage, Boolean isReceivedDuringBusinessHour) throws Exception {
		Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
		log.info("[AppleChatReceiveHandler] receive message chatsessionstatus is AUTO for businessId:{}", contactDocument.getB_id());
		chatbotQueryResponseOptional = chatbotService
				.getQueryResponse(contactDocument.getB_id(), contactDocument.getE_id(), event.getBody(), QueryChannel.LIVECHAT,
				contactDocument.getC_id(),contactDocument.getM_c_id(), isReceivedDuringBusinessHour, new LivechatAnonymousConversationDataDto(), null);
		AppleChatSessionEnum appleChatSessionEnum=AppleChatSessionEnum.AUTO;
		appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), welcomeMessage, null, null, true, null);
			sendRobinReply(event, contactDocument, messageDTO.getMessageDocument(), chatbotQueryResponseOptional,
                    event.getBody(), isReceivedDuringBusinessHour, true);
			if(chatbotQueryResponseOptional.isPresent()  && chatbotQueryResponseOptional.get().getSuggestionHolder()!=null){
				if(!contactDocument.getAppleContextDocument().getCapabilityList().contains(MessengerConstants.APPLE_LIST_PIKER)) {
					appleChatSessionEnum=AppleChatSessionEnum.MANUAL;
				}
			}
			updateContactDocument(contactDocument, true, appleChatSessionEnum);
	}

	private void updateContactDocument(ContactDocument contactDocument, Boolean welcomeMessageSent,
			AppleChatSessionEnum appleChatSession) {
		ContactDocument doc = messengerContactService.getContact(contactDocument.getE_id(),
				contactDocument.getM_c_id());
		if(BooleanUtils.isTrue(welcomeMessageSent)) {
			doc.getAppleContextDocument().setWelcomeMessageSent(welcomeMessageSent);
		}
		if (appleChatSession != null) {
			doc.getAppleContextDocument().setAppleChatSession(appleChatSession);
		}
		messengerContactService.updateContactDocumentOnES(doc, doc.getId().toString(), doc.getE_id());
	}

	private void sendRobinReply(AppleUserMessage event, ContactDocument contactDocument,
            MessageDocument messageDocument, Optional<ChatbotQueryResponse> chatbotQueryResponse, String queryText,
            Boolean isReceivedDuringBusinessHour, Boolean welcomeMessageSent) throws Exception {
		Integer businessId = contactDocument.getB_id();
		BusinessDTO business=businessService.getBusinessDTO(businessId);
		sendIntentBasedChatbotReply(contactDocument, messageDocument, business, chatbotQueryResponse,
                queryText, isReceivedDuringBusinessHour, welcomeMessageSent);
	}

	private void sendIntentBasedChatbotReply(ContactDocument contactDocument, MessageDocument messageDocument, 
            BusinessDTO business, Optional<ChatbotQueryResponse> chatbotQueryResponseOpt, String queryText,
            Boolean isReceivedDuringBusinessHour, Boolean welcomeMessageSent) throws Exception {
		MessageDocument.RobinResponseType robinResponseType=null;
        String intentType = chatbotQueryResponseOpt.isPresent() ? chatbotQueryResponseOpt.get().getIntentType() : null;
        if ((welcomeMessageSent && (StringUtils.isBlank(intentType) || "DEFAULT_FALLBACK".equals(intentType)))) {
            if (!contactDocument.getAppleContextDocument().getCapabilityList()
                    .contains(MessengerConstants.APPLE_LIST_PIKER)) {
                appleSend(
                        contactDocument.getB_id(), contactDocument.getM_c_id(), "Thank you for contacting "
                                + contactDocument.getB_name() + "!" + " Our team member will reach out shortly",
                        null, null, false, robinResponseType);
                updateContactDocument(contactDocument, null, AppleChatSessionEnum.MANUAL);
                notificationService.processUnreadMessageNotification(business, messageDocument);
            } else {
                SuggestionHolder suggestionHolder = getTopFAQsSuggestionHolder(business);
                if (Objects.nonNull(suggestionHolder)) {
                String robinSuggestionHeader = MessengerConstants.APPLE_ROBIN_SUGGESTION_HEADER;
                String robinSuggestionSubHeader = MessengerConstants.APPLE_ROBIN_SUGGESTION_SUB_HEADER;
                suggestionHolder.setQueryText(robinSuggestionHeader);
                suggestionHolder.setSubHeader(robinSuggestionSubHeader);
                String stringifiedChatbotReply = JSONUtils.toJSON(suggestionHolder);
                appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), stringifiedChatbotReply,
                        null, AppleInteractiveMessageType.LIST_PICKER, true, robinResponseType);
                robinResponseType = MessageDocument.RobinResponseType.SUGGESTION;
		}
            }
        } else if (chatbotQueryResponseOpt.isPresent() && "DEFAULT_FALLBACK".equals(intentType)) {
			String message = MessengerConstants.APPLE_ROBIN_ONLINE_MESSAGE;
			if (BooleanUtils.isFalse(isReceivedDuringBusinessHour)) {
				message =MessengerConstants.APPLE_ROBIN_DEFAULT_OFFLINE_MESSAGE; 
			}
			robinResponseType = MessageDocument.RobinResponseType.DEFAULT;
			appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), message, null, null, false,
					robinResponseType);
            updateContactDocument(contactDocument, null, AppleChatSessionEnum.MANUAL);
			notificationService.processUnreadMessageNotification(business, messageDocument);
		} else if (chatbotQueryResponseOpt.isPresent()  && chatbotQueryResponseOpt.get().getSuggestionHolder() == null) {
			Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = Optional.empty();
			log.info("[AppleChatReceiveHandler] receive message chatsessionstatus is AUTO for businessId:{}",
					contactDocument.getB_id());
			chatbotQueryResponseOptional = chatbotService.getQueryResponse(contactDocument.getB_id(),
					contactDocument.getE_id(), queryText, QueryChannel.VOICE_CALL, contactDocument.getC_id(), contactDocument.getM_c_id(),
					isReceivedDuringBusinessHour, new LivechatAnonymousConversationDataDto(), null);
			String plainTextRobinResponse = null;
			if (chatbotQueryResponseOptional.isPresent()) {
				if (MapUtils.isNotEmpty(chatbotQueryResponseOptional.get().getData())) {
					plainTextRobinResponse = (String) chatbotQueryResponseOptional.get().getData().get("response");
					ChatbotReplyDTO chatbotReplyDTO = new ChatbotReplyDTO();
					chatbotReplyDTO.setIntentType(chatbotQueryResponseOpt.get().getIntentType());
					chatbotReplyDTO.setType("intent");
					chatbotReplyDTO.setData(chatbotQueryResponseOpt.get().getData());
					String stringifiedChatbotReply = JSONUtils.toJSON(chatbotReplyDTO);
					robinResponseType = MessageDocument.RobinResponseType.INTENT;
					if ("FAQ".equals(chatbotQueryResponseOpt.get().getIntentType())) {
						robinResponseType = MessageDocument.RobinResponseType.FAQ;
					}
					appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), stringifiedChatbotReply,
							plainTextRobinResponse, null, true, robinResponseType);
					contactDocument = messengerContactService.getContact(contactDocument.getE_id(),
							contactDocument.getM_c_id());
					AppleQuickReplyDto event = new AppleQuickReplyDto(contactDocument.getM_c_id(),
							contactDocument.getB_id(), contactDocument.getE_id(),
							new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)
									.parse(contactDocument.getL_msg_on()).getTime());
							if(contactDocument.getAppleContextDocument().getCapabilityList().contains(MessengerConstants.APPLE_QUICK_REPLY)) {
						pushMessageToSQSQueue(event, 30);
					}
				}
			}
		}else if(chatbotQueryResponseOpt.isPresent()  && chatbotQueryResponseOpt.get().getSuggestionHolder()!=null){
			if(!contactDocument.getAppleContextDocument().getCapabilityList().contains(MessengerConstants.APPLE_LIST_PIKER)) {
				appleSend(
						contactDocument.getB_id(), contactDocument.getM_c_id(), "Thank you for contacting "
								+ contactDocument.getB_name() + "!" + " Our team member will reach out shortly",
						null, null, false, robinResponseType);
				updateContactDocument(contactDocument, null, AppleChatSessionEnum.MANUAL);
                notificationService.processUnreadMessageNotification(business, messageDocument);
			} else {
				SuggestionHolder suggestionHolder = chatbotQueryResponseOpt.get().getSuggestionHolder();
				String robinSuggestionHeader = MessengerConstants.APPLE_ROBIN_SUGGESTION_HEADER;
				String robinSuggestionSubHeader = MessengerConstants.APPLE_ROBIN_SUGGESTION_SUB_HEADER;
				suggestionHolder.setQueryText(robinSuggestionHeader);
				suggestionHolder.setSubHeader(robinSuggestionSubHeader);
                List<Suggestion> suggestionsWithImages = suggestionHolder.getSuggestions().stream().map(
                        suggestion -> new Suggestion(suggestion.getType(), suggestion.getKey(), suggestion.getValue()))
                        .collect(Collectors.toList());
                suggestionHolder.setSuggestions(suggestionsWithImages);
                String stringifiedChatbotReply = JSONUtils.toJSON(chatbotQueryResponseOpt.get().getSuggestionHolder());
				appleSend(contactDocument.getB_id(), contactDocument.getM_c_id(), stringifiedChatbotReply,
						null, AppleInteractiveMessageType.LIST_PICKER, true, robinResponseType);
				robinResponseType = MessageDocument.RobinResponseType.SUGGESTION;
			}
		}
		if(robinResponseType!=null) {
			commonService.updateRobinResponseTypeInMessage(messageDocument,robinResponseType);
		}
	}

	private void appleSend(Integer businessId, Integer mcId, String message, String plainTextRobinResponse,
			AppleInteractiveMessageType appleInteractiveMessageType, Boolean updateLastResponseAt,
			RobinResponseType responseType) throws Exception {
		SendMessageDTO sendMessageDTO=new SendMessageDTO();
		sendMessageDTO.setBody(message);
		sendMessageDTO.setSentThrough(SentThrough.WEB);
		sendMessageDTO.setFromBusinessId(businessId);
		sendMessageDTO.setBusinessIdentifierId(businessId.toString());
		sendMessageDTO.setToCustomerId(mcId.toString());
		sendMessageDTO.setSource(Source.APPLE.getSourceId());
		sendMessageDTO.setUserId(MessengerConstants.ROBIN_REPLY_USER);
		sendMessageDTO.setPlainTextRobinResponse(plainTextRobinResponse);
		sendMessageDTO.setAppleInteractiveMessageType(appleInteractiveMessageType);
		sendMessageDTO.setUpdateLastResponseAt(updateLastResponseAt);
		sendTypingEventInCaseOfRobinReply(sendMessageDTO);
		handleRichLinksInFAQs(sendMessageDTO, responseType);
		appleSendHandler.handle(sendMessageDTO);
	}

	private void saveAttachments(MessageDTO messageDTO,Integer messageId) {
		 AppleUserMessage event = (AppleUserMessage) messageDTO;
		 List<Integer> mediaIds=new ArrayList<Integer>();
		 List<MessageDocument.MediaFile> messageDocumentMediaFiles=new ArrayList<MessageDocument.MediaFile>();
		 if(CollectionUtils.isNotEmpty(event.getAttachments())) {
			 List<AppleUploadAttachmentRequest> appleUploadAttachmentRequests=new ArrayList<AppleUploadAttachmentRequest>();
			 event.getAttachments().forEach(attachment->{
				 MessengerMediaFile messengerMediaFile=new MessengerMediaFile(attachment,messageId);
				 messengerMediaFile.setUrl(getApplePreDownloadUrl(attachment,event.getDestinationId()));
				 messengerMediaFileService.saveMedia(messengerMediaFile);
				 mediaIds.add(messengerMediaFile.getId());
				 appleUploadAttachmentRequests.add(new AppleUploadAttachmentRequest(messengerMediaFile.getId(),messengerMediaFile.getUrl(),attachment.getKey(),"apple",attachment.getSize(),attachment.getName(),attachment.getMimeType()));	
		     });
			 List<MessengerMediaFile> mediaFiles=messengerMediaFileService.findByIds(mediaIds);
			 Map<Integer, MessengerMediaFile> mediaFilesMap = mediaFiles.stream().collect(
		                Collectors.toMap(media -> media.getId(), media -> media));
				BusinessDTO businessDTO = getBusinessDTO(messageDTO);
				for (AppleUploadAttachmentRequest appleUploadAttachmentRequest : appleUploadAttachmentRequests) {
					List<AppleUploadAttachmentRequest> singleAppleUploadAttachmentRequests = new ArrayList<>(1);
					singleAppleUploadAttachmentRequests.add(appleUploadAttachmentRequest);
					Map<Integer, String> cdnUrlMap = businessService.uploadAppleImagesInCDN(
							singleAppleUploadAttachmentRequests, businessDTO.getBusinessId(),
							MessageDocument.Channel.APPLE);

			 if(MapUtils.isNotEmpty(mediaFilesMap) && MapUtils.isNotEmpty(cdnUrlMap)) {
				 mediaFilesMap.entrySet().forEach(entry->{
					 if(entry.getValue()!=null && entry.getKey()!=null && cdnUrlMap.get(entry.getKey())!=null) {
						 entry.getValue().setUrl(cdnUrlMap.get(entry.getKey()));
						 MessageDocument.MediaFile messageDocumentMediaFile=new MessageDocument.MediaFile(entry.getValue());
						 messageDocumentMediaFiles.add(messageDocumentMediaFile);
					 }
				 });
				}
			}
				 messengerMediaFileService.saveAll(mediaFilesMap.values());
				 messageDTO.getMessageDocumentDTO().setMediaFiles(messageDocumentMediaFiles);

		 }
	}
	private String getApplePreDownloadUrl(Attachment attachment,String destinationId) {
		AttachmentPreDownloadRequest request=new AttachmentPreDownloadRequest();
		request.setOwner(attachment.getOwner());
		request.setSignature(attachment.getSignature());
		request.setSourceId(destinationId);
		request.setUrl(attachment.getUrl());
		return appleSocialIntegrationService.getApplePreDownloadUrl(request);
	}

	private void handleCustomerOptOut(MessageDTO messageDTO) throws Exception {
		AppleUserMessage event = (AppleUserMessage) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		Optional<MessengerContact> optMessengerContact = messengerContactService.getByAppleConversationIdAndSubaccountId(event.getSourceId(), businessDTO.getBusinessId());
        if(!optMessengerContact.isPresent()) {
        	  log.info("contact not found for sourceId: {}", event.getSourceId());
        	  throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        MessengerContact messengerContact=optMessengerContact.get();
		MessangerBaseFilter messengerFilter = new MessangerBaseFilter();
		messengerFilter.setStartIndex(0);
		messengerFilter.setCount(1);
		messengerFilter.setConversationId(messengerContact.getId());
		if (Objects.nonNull(businessDTO)) {
			messengerFilter.setAccountId(businessDTO.getRoutingId());
			List<ContactDocument> contactsFromES = messengerContactService.getContactFromES(messengerFilter);
			if (CollectionUtils.isNotEmpty(contactsFromES)) {
				ContactDocument contactFromES = contactsFromES.get(0);
				contactFromES.getAppleContextDocument().setAppleOptOut(true);
				messengerContactService.updateContactOnES(messengerContact.getId(), contactFromES, businessDTO.getRoutingId());
				log.info("Apple Chat: mcId : {} opt out of messages", messengerContact.getId());
				addOptOutActivity(messageDTO);
			}
		}
		messageDTO.setSendEmailNotification(false);
		super.handle(messageDTO);
	}

	private void addOptOutActivity(MessageDTO messageDTO) {
		messageDTO.setMsgTypeForResTimeCalc("");
		messageDTO.setSendEmailNotification(false);
		ActivityDto activityDTO = ActivityDto.builder().activityType(ActivityType.APPLE_OPT_OUT)
				.created(new Date()).mcId(getMessengerContact(messageDTO).getId()).build();
		ConversationActivity activity = conversationActivityService.create(activityDTO);
		activityDTO.setId(activity.getId());
		messageDTO.setActivityDto(activityDTO);
		UserDTO userDTO = new UserDTO();
		userDTO.setId(-7);
		messageDTO.setUserDTO(userDTO);
		messageDTO.setUpdateTag(false);
		messageDTO.setUpdateLastMessage(false);
		messengerMessageService.saveMessengerActivity(activity, activityDTO.getMcId(), userDTO);	
		MessageDocumentDTO messageDocumentDTO=new MessageDocumentDTO(activityDTO);
		messageDocumentDTO.setSource(16);
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
	}

    private MessageDocumentDTO saveInSecondaryStorage(MessageDTO messageDTO) {
        MessengerMessage msg = getMsg(messageDTO);
        AppleMessage aMsg = getAMsg(messageDTO);
        //Save all in single transaction
        Map.Entry<MessengerMessage, AppleMessage> entry = messengerMessageService.saveAppleMessage(msg, aMsg);

		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(entry.getValue(), entry.getKey());

		MessengerContact messengerContact = getMessengerContact(messageDTO);
		if( messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)){
			messageDocumentDTO.setSpam(true);
		}else{
			messageDocumentDTO.setSpam(false);
		}

		return messageDocumentDTO;
    }

 
    @Override
    public void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.APPLE.name());
        lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.APPLE.getSourceId());
        lastMessageMetadataPOJO.setLastMessageSource(Source.APPLE.getSourceId());
        Date lastMsgOn = new Date();
        messengerContact.setLastMsgOn(lastMsgOn);
        messengerContact.setUpdatedAt(lastMsgOn);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //revisit below line 
        lastMessageMetadataPOJO.setLastFbReceivedAt(sdf.format(lastMsgOn));
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
    }

    @Override
    public void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact mc = getMessengerContact(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        AppleUserMessage event = (AppleUserMessage) messageDTO;
        if(CollectionUtils.isNotEmpty(event.getAttachments())) {
            mc.setLastMessage("Received an attachment.");
        }
        else {
            mc.setLastMessage(event.getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(mc, 1, mc.getAppleConversationId(), businessDTO.getBusinessNumber());
        mc.setEncrypted(isEncrypted ? 1 : 0);
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        return null;
    }
   
    @Override
    void publishEvent(MessageDTO messageDTO) {
    }

    @Override
    public MessengerContact getMessengerContact(MessageDTO messageDTO) {
    	AppleUserMessage event = (AppleUserMessage) messageDTO;
        if(Objects.isNull(messageDTO.getMessengerContact())) {
            String conversationId = event.getSourceId();
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            Optional<MessengerContact> optMessengerContact = messengerContactService.getByAppleConversationIdAndSubaccountId(conversationId, businessDTO.getBusinessId());
            if(optMessengerContact.isPresent()) {
                messageDTO.setMessengerContact(optMessengerContact.get());
            }
            else {
				UserInfo userInfo = event.getUserInfo();
				KontactoRequest kontactoRequest = new KontactoRequest();
				kontactoRequest.setBusinessId(businessDTO.getBusinessId());
				if (userInfo != null) {
					kontactoRequest.setName(userInfo.getDisplayName());
				}
				kontactoRequest.setEmailId(getUUID(event.getSourceId()) + "@appledummy.com");
				kontactoRequest.setSource(KontactoRequest.APPLE);
				KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
				locationInfo.setCountryCode(businessDTO.getCountryCode());
				kontactoRequest.setLocation(locationInfo);
				CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
						businessDTO.getAccountId(), -1);
				if (Objects.isNull(customerDTO)) {
					log.error("AppleMessageReceiveHandler: failed to create/get customer for event {}", event);
					throw new MessengerException(
							"Failed to get or create customer for conversationId" + event.getSourceId());
				}
				messageDTO.setCustomerDTO(customerDTO);
			    MessengerContact mc = messengerContactService.getOrCreateMessengerContactForAppleContact(conversationId, businessDTO.getBusinessId(), customerDTO.getId());
                messageDTO.setMessengerContact(mc);
            }
            assignConversationToTeam(messageDTO);
        }

        return messageDTO.getMessengerContact();
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
        	MessengerContact contact= getMessengerContact(messageDTO);
        	if(contact.getCustomerId()!=null) {
            customerDTO = contactService.findById(contact.getCustomerId());
            messageDTO.setCustomerDTO(customerDTO);
        	}
        }
        return customerDTO;
    }

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        if(Objects.isNull(messageDTO.getBusinessDTO())) {
            AppleUserMessage event = (AppleUserMessage) messageDTO;
            String destinationId = event.getDestinationId();
            String intent = event.getIntent();
            boolean isSMB=StringUtils.isBlank(intent);
            Integer businessId = appleSocialIntegrationService.getBusinessIdForPageId(destinationId,intent,isSMB);
            if (businessId == null) {
    			log.error("No valid business mapping found for apple page: destinationId: {} and intent {} " + destinationId,intent);
    			throw new NotFoundException(ErrorCode.APPLE_PAGE_IS_NOT_LINKED_TO_BUSINESS,
    					"No valid business mapping found for apple page: destinationId:"+destinationId+" and intent: " +intent);
    		}
            BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
            if (Objects.isNull(businessDTO)) {
                log.error("No business found with id : {}", businessId);
                throw new NotFoundException("No business found with id " + businessId);
            }
            messageDTO.setBusinessDTO(businessDTO);
        }
        return messageDTO.getBusinessDTO();
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if (getSendEmailNotification(messageDTO)) {
            return MessageTag.UNREAD;
        } else return MessageTag.INBOX;
    }

    @Override
    protected boolean getSendEmailNotification(MessageDTO messageDTO) {
//		ContactDocument contactDocument=messageDTO.getContactDocument();
//		if (contactDocument.getLastMessageUserId()!=null && contactDocument.getLastMessageUserId()==-10 && contactDocument.getAppleContextDocument()!=null && AppleChatSessionEnum.MANUAL.equals(contactDocument.getAppleContextDocument().getAppleChatSession())) {
//			return false;
//		}
        return false;
		
    }

    @Override
    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setMsgId(Integer.valueOf(messageDTO.getMessageDocumentDTO().getM_id()));
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        notificationRequest.setLastMsgTime(messageDTO.getMessageDocumentDTO().getCr_time());
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }

     private MessengerMessage getMsg(MessageDTO messageDTO) {
        MessengerMessage msg = new MessengerMessage();
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        msg.setAccountId(businessDTO.getAccountId());
        msg.setChannel(MessageDocument.Channel.APPLE.name());
        msg.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE.name());
        msg.setMessageType(MessageDocument.MessageType.CHAT.name());
        msg.setMessengerContactId(getMessengerContact(messageDTO).getId());
        msg.setCreatedDate(new Date());
        return msg;
    }

    private AppleMessage getAMsg(MessageDTO messageDTO) {
        AppleUserMessage event = (AppleUserMessage) messageDTO;
        AppleMessage aMsg = new AppleMessage();
        //The message text must have one Unicode object replacement character (\uFFFC) for each attachment in the attachments array. For example, if the message includes two attachments, the message text must contain two Unicode object replacement characters
        String body=event.getBody();
		log.info("body before replace : {}", body);
        if(CollectionUtils.isNotEmpty(event.getAttachments())) {
			body = body.replace('\ufffc', '\u0000').replace("\u0000", "");
			body = body.replace("�", "");
		}
		log.info("body after replace : {}", body);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        aMsg.setCustomerId(customerDTO.getId());
        aMsg.setRecipientId(event.getDestinationId());
        aMsg.setSenderId(event.getSourceId());
        aMsg.setMessageBody(body);
        aMsg.setMessageType(event.getType());
        aMsg.setMessageId(event.getId());
        aMsg.setStatus(AppleMessageStatusEnum.RECEIVED.name());
        aMsg.setSentOn(new Date());
        aMsg.setEncrypted(0);
        aMsg.setCreateDate(new Date());
        aMsg.setBusinessId(businessDTO.getBusinessId());
        aMsg.setLocale(event.getLocale());
        aMsg.setVersion(event.getV().toString());
        return aMsg;
    }

    public String getUUID(String input) {
		try {
			return UUID.nameUUIDFromBytes(input.getBytes("UTF-8")).toString();
		} catch (UnsupportedEncodingException e) {
			log.error("Apple chat Receieve Handler: Error in generating UUId",e.getMessage());
		}
		return UUID.randomUUID().toString();
    }
    public String pushMessageToSQSQueue(AppleQuickReplyDto event, Integer delay) {
		log.info("Going to produce SQS message for businessId {} and mc_id {}", event.getBusinessId(),
				event.getMcId());
		String sqsMessageId = amazonSQSUtility.publishMessageToSQS(JSONUtils.toJSON(event), sqsQueueUrl,
				delay);
		log.info("SQS messageID {} for businessId {} and mc_id {}", sqsMessageId, event.getBusinessId(),
				event.getMcId());
		return sqsMessageId;
	}

	private void sendTypingEventInCaseOfRobinReply(SendMessageDTO sendMessageDTO) {
		BusinessDTO businessDTO = businessService.getBusinessDTO(sendMessageDTO.getFromBusinessId());
		sendMessageDTO.setBusinessDTO(businessDTO);
		ConversationStateDTO conversationStateDTO = new ConversationStateDTO(sendMessageDTO);
		liveChatService.addTypingEventForRobin(conversationStateDTO);
	}

	private void handleRichLinksInFAQs(SendMessageDTO sendMessageDTO, RobinResponseType responseType) {
		if (MessageDocument.RobinResponseType.FAQ.equals(responseType)) {
			String body = sendMessageDTO.getPlainTextRobinResponse();
			if (!MessengerUtil.checkIfStringContainsURL(body))
				return;
			List<String> multiMessages = generateMultiMessages(body);
			sendMessageDTO.setMultiMessages(multiMessages);
			sendMessageDTO.setBody(null);
			sendMessageDTO.setPlainTextRobinResponse(null);

		}
	}


	private static List<String> generateMultiMessages(String body) {
		List<String> multiMessages = new ArrayList<>();
		String newline = System.getProperty("line.separator");
		String words[] = body.split(" ");
		String newWord = "";
		for (int i = 0; i < words.length; i++) {
			if (words[i] != null && words[i].contains(newline)) {
				String newLineWords[] = words[i].split(newline);
				for (int j = 0; j < newLineWords.length; j++) {
					if (MessengerUtil.isValidURL(newLineWords[j].trim())) {
						if (!(newWord.length() == 1 && newWord.charAt(0) == '\n')) {
							multiMessages.add(newWord.trim());
							}
						multiMessages.add(newLineWords[j].trim());
						newWord = "";
					}
					else {
                        if (j == newLineWords.length - 1) {
                            newWord += newLineWords[j] + " ";
                            continue;
                        }

                        newWord += newLineWords[j] + newline;
                    }
					}
			}
			else {
				if (MessengerUtil.isValidURL(words[i].trim())) {
					if (newWord.length() > 0) {
						multiMessages.add(newWord.trim());
					}
					multiMessages.add(words[i].trim());
					newWord = "";
				} else {
					newWord += words[i] + " ";
				}
			}
		}
		if (newWord.length() > 0) {
			multiMessages.add(newWord);
		}
		return multiMessages;

	}
	
	public CustomerDTO getOrCreateCustomerDTO(MessageDTO messageDTO) {
    	AppleUserMessage event = (AppleUserMessage) messageDTO;
        if(Objects.isNull(messageDTO.getMessengerContact())) {
            String conversationId = event.getSourceId();
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            Optional<MessengerContact> optMessengerContact = messengerContactService.getByAppleConversationIdAndSubaccountId(conversationId, businessDTO.getBusinessId());
            if(optMessengerContact.isPresent()) {
                messageDTO.setMessengerContact(optMessengerContact.get());
                messageDTO.setCustomerDTO(getCustomerDTO(messageDTO));
                assignConversationToTeam(messageDTO);
            } else {
                UserInfo userInfo = event.getUserInfo();
                KontactoRequest kontactoRequest = new KontactoRequest();
                kontactoRequest.setBusinessId(businessDTO.getBusinessId());
                if (userInfo != null) {
                  kontactoRequest.setName(userInfo.getDisplayName());
				    }
				kontactoRequest.setEmailId(getUUID(event.getSourceId()) + "@appledummy.com");
				kontactoRequest.setSource(KontactoRequest.APPLE);
				KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
				locationInfo.setCountryCode(businessDTO.getCountryCode());
				kontactoRequest.setLocation(locationInfo);
				CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
						businessDTO.getAccountId(), -1);
				if (Objects.isNull(customerDTO)) {
					log.error("AppleMessageReceiveHandler: failed to create/get customer for event {}", event);
					throw new AppleMessagingException(
							"Failed to get or create customer for conversationId" + event.getSourceId());
				}
				messageDTO.setCustomerDTO(customerDTO);
            }
        }
        return messageDTO.getCustomerDTO();
    }

    private SuggestionHolder getTopFAQsSuggestionHolder(BusinessDTO business) {
        Integer accountId = business.getAccountId();
        TopFaqGetResponse topFaqResponse = topFaqService.getTopFAQByAccountId(accountId, 0, 6, "priority",
                SortOrderEnum.DESC);
        List<GetFaqResponse> getFaqResponses = topFaqResponse.getTopFaqs();
        SuggestionHolder suggestionHolder = null;
        if (CollectionUtils.isNotEmpty(getFaqResponses)) {
            suggestionHolder = new SuggestionHolder();
            List<Integer> faqIds = getFaqResponses.stream().map(GetFaqResponse::getFaqId).collect(Collectors.toList());
            Map<Integer, String> faqQuestions = new HashMap<>();
            if (CollectionUtils.isNotEmpty(faqIds)) {
                QueroFAQResponse queroFAQResponse = queroService.getFAQsByIds(faqIds, accountId, false);
                List<Question> faqs = queroFAQResponse.getQnAs().stream().filter(faq -> faq.getAllLocations())
                        .map(FAQ::getQuestion)
                        .collect(Collectors.toList());
                faqQuestions = faqs.stream().collect(Collectors.toMap(Question::getId, Question::getText));
            }
            List<Suggestion> suggestions = new ArrayList<>();
            for (GetFaqResponse faqResponse : getFaqResponses) {
                Integer id = faqResponse.getFaqId();
                IntentType intentType = faqResponse.getIntentType();
                Suggestion suggestion = null;
                if (Objects.nonNull(id)) {
                    String question = faqQuestions.get(id);
                    if (StringUtils.isNotBlank(question)) {
                        suggestion = new Suggestion(Type.FAQ, question, question);

                    }
                } else {
                    suggestion = new Suggestion(Type.INTENT, intentType.getKey(), intentType.getQueryText());
                }
                if (Objects.nonNull(suggestion)) {
                    suggestions.add(suggestion);
                }
            }
            suggestionHolder.setSuggestions(suggestions);
        }
        return suggestionHolder;
    }
    
    private void assignConversationToTeam(MessageDTO messageDTO) {
        log.info("assignConversationToTeam called with: [ {} ] ", messageDTO);
        Integer assignedUser = messageDTO.getMessengerContact().getCurrentAssignee();
        if ((Objects.nonNull(assignedUser) && assignedUser > 0)) {
            log.info("assignConversationToTeam: conversation already assigned to user {}",
                    assignedUser);
            return;
        }

        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        Integer accountId = businessDTO.getAccountId();
        Integer businessId = businessDTO.getBusinessId();
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        Integer teamId = locationTeamMappingService.getLocationTeamMapping(accountId, businessId);

        if (Objects.nonNull(teamId)) {
            log.info("Assigning conversation to team : {}", teamId);
            TeamDto teamDto = webchatService.validateTeamId(teamId);
            if (Objects.nonNull(teamDto)) {
                webchatService.assignConversationToTeamWithDBAndESCall(teamDto, messengerContact.getId(), businessDTO,
                        false, messengerContact);
                log.info("assignConversationToTeam successfully");
            }
        }
    }
}
