package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerData;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.ThankYouNoteEvent;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.facebook.Messaging;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkResponse;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.dto.whatsapp.Message;
import com.birdeye.messenger.dto.whatsapp.Value;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.PaymentAutomationUser;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WhatsappMsgType;
import com.birdeye.messenger.event.ReplyOnUAConversationEvent;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandler;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.NotificationService;
import com.birdeye.messenger.service.ReferrerThankYouNoteEventProducer;
import com.birdeye.messenger.service.ResponseTimeCalculationService;
import com.birdeye.messenger.service.RobinService;
import com.birdeye.messenger.sro.UserIdentity;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * This class is intended to be sub-classed whenever there is a new message communication mode
 */
@Slf4j
public abstract class MessageEventHandlerAbstract implements MessengerEventHandler {

    @Autowired
    protected CommunicationHelperService communicationHelperService;
    @Autowired
    protected MessengerContactService messengerContactService;
    @Autowired
    @Lazy
    protected NotificationService notificationService;
    @Autowired
    protected NexusService nexusService;
    @Autowired
    protected FirebaseService fcmService;
    @Autowired
    protected KafkaService kafkaService;
	@Autowired
	protected ConversationActivityService conversationActivityService;
	@Autowired
    protected ReferrerThankYouNoteEventProducer thankYouNoteEventProducer;
	@Autowired
    protected ContactService contactService;
	@Autowired
    protected ResponseTimeCalculationService responseTimeCalculationService;
	
	@Autowired
	private CommonService commonService;

    @Autowired
    protected RobinService robinService;
    
    @Autowired
    private SocialService socialService;
    
    @Autowired
    private SpamDetectionService spamDetectionService;

    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        String oldLmsgOn = getOldLmsgOn(messageDTO);
        MessengerData messengerData = updateMessenger(messageDTO);
        messageDTO.setMessageDocument(messengerData.getMessageDocument());
        messageDTO.setContactDocument(messengerData.getContactDocument());
        // Below check for getSendEmailNotification is to check for the basic cases. e.g IF START or STOP messages are received from mobile,then no email notifications should be sent
        //customer block status is checked so that notification triggering can be stopped for blocked contacts
        if (getSendEmailNotification(messageDTO) && (messageDTO.getCustomerDTO()==null || !BooleanUtils.isTrue(messageDTO.getCustomerDTO().getBlocked()))) {
            MessengerGlobalFilter notificationMetaData = getEmailNotificationMetaData(messageDTO);
            notificationService.processMessageNotification(notificationMetaData, getBusinessDTO(messageDTO), messengerData.getMessageDocument());
        }
        if(isFireBaseSyncEnabled(messageDTO) && (messageDTO.getCustomerDTO()==null || !BooleanUtils.isTrue(messageDTO.getCustomerDTO().getBlocked()))) {
            messengerData.getMessageDocument().setIsAppCompatible(messageDTO.getIsAppCompatible());
            fcmService.pushToFireBase(messengerData.getContactDocument(), messengerData.getMessageDocument(),messageDTO.getUserId(),null);
            publishEvent(messageDTO);
        }
        sendThankYouNoteIfApplicable(messageDTO);
        if(BooleanUtils.isTrue(messageDTO.getTriggerRobinAutoReplies())) {
            robinService.sendRobinReply(messageDTO, oldLmsgOn);
        }
        if (("R").equals(messageDTO.getMsgTypeForResTimeCalc()) || ("S").equals(messageDTO.getMsgTypeForResTimeCalc()) || messageDTO.isBOT()) {
        	socialService.pushReceiveEventToSocial(messageDTO);
        }
        List<Integer> accountIds=contactService.getAllContactUpgradeEnabledAccounts();
        if(CollectionUtils.isNotEmpty(accountIds)&& messageDTO.getBusinessDTO()!=null && accountIds.contains(messageDTO.getBusinessDTO().getAccountId())){
        	commonService.markConversationActiveOnSend(messageDTO);
        }
        spamDetectionService.checkAndAuditSpamForOutboundMessage(messageDTO);
        return null;
    }
    
    private MessengerData updateMessenger(MessageDTO messageDTO) {
        updateMessengerContact(messageDTO);
        ContactDocument contactDocument = null;
        if (!messageDTO.isBOT()) {
        	contactDocument = updateContactOnES(messageDTO);
        } else {
        	contactDocument = messengerContactService.getContact(messageDTO.getBusinessDTO().getRoutingId(), messageDTO.getMessengerContact().getId());
        }
        messageDTO.setContactDocument(contactDocument);
        MessageDocument messageDocument = updateMessageOnES(messageDTO);
        MessengerData messengerData = new MessengerData();
        messengerData.setMessengerContact(messageDTO.getMessengerContact());
        messengerData.setMessageDocument(messageDocument);
        messengerData.setContactDocument(contactDocument);
        return messengerData;
    }

    private void updateMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        if(messageDTO.isUpdateTag()) {
        	messengerContact.setTag(getMessageTag(messageDTO).getCode());
        }
        updateMessengerContactViewedByList(messageDTO, messengerContact);
        
		if(messageDTO.isUpdateLastMessage()) {
        	alterAndUpdateLastMessage(messageDTO);
            updateLastMessageMetaData(messageDTO);
        }
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		if (Objects.nonNull(customerDTO)) {
			messengerContact.setLead(customerDTO.isLead());
			messengerContact.setLeadSource(customerDTO.getLeadSource());
			messengerContact.setContactState(customerDTO.getContactState());
			messengerContact.setBlocked(customerDTO.getBlocked());
			messengerContact.setSpam(customerDTO.getBlocked());
		}
        /**
         * RtmPauseTagging if true to not calculate response time
         */
		// ------------------------- response time calculation -------------------
        ResponseTimeOutBox responseTimeOutBox = null;

		//------------ set Payment info in last message meta data----------------
        setPaymentInfoInConversation(messageDTO, messengerContact);
        // --------------------------------------------------------------------------------------------

        Long lastMessageIncomingTime = messengerContact.getLastIncomingMessageTime();

        if(Objects.isNull(messengerContact.getRtmPauseTagging()) || Boolean.FALSE.equals(messengerContact.getRtmPauseTagging())) {
            responseTimeOutBox = processResponseTimeIfApplicable(messageDTO); // not applicable in case of notes and activities
            MessengerData messengerData = messengerContactService.saveOrUpdateMessengerContact(messengerContact, responseTimeOutBox);
            messengerContact = messengerData.getMessengerContact();
            responseTimeOutBox = messengerData.getResponseTimeOutBox();
            if(Objects.nonNull(responseTimeOutBox)) kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESPONSE_TIME_CALC, responseTimeOutBox);
        }
        else {
            messengerContact = messengerContactService.saveOrUpdateMessengerContact(messengerContact);
        }
        if(Objects.nonNull(lastMessageIncomingTime)) {
            messengerContact.setLastIncomingMessageTime(lastMessageIncomingTime);
        }
        messageDTO.setMessengerContact(messengerContact);
    }

	private void updateMessengerContactViewedByList(MessageDTO messageDTO, MessengerContact messengerContact) {
		UserDTO userDTO = getUserDTO(messageDTO);
        boolean resetViewedBy=true;
		if (messageDTO.getUserId() != null) {
			SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
			if (sendMessageDTO != null && sendMessageDTO.getConversationDTO() != null && CommunicationDirection.SEND
					.equals(sendMessageDTO.getConversationDTO().getCommunicationDirection())) {
				List<Integer> viewedByUsers = StringUtils.isNotBlank(messengerContact.getViewedBy())
						? MessengerUtil.convertCommaSeparatedStringToList(messengerContact.getViewedBy())
						: new ArrayList<>();
				viewedByUsers.add(messageDTO.getUserId());
				messengerContact.setViewedBy(StringUtils.join(viewedByUsers, ','));
				resetViewedBy = false;
			}
		} else if(userDTO!=null  && Constants.FACEBOOK_DUMMY_USER.equals(userDTO.getId())) {
			FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
			if (facebookMessageRequest != null && facebookMessageRequest.getConversationDTO() != null
					&& CommunicationDirection.SEND
							.equals(facebookMessageRequest.getConversationDTO().getCommunicationDirection())) {
				messengerContact.setIsRead(false);
				resetViewedBy = false;
			}
		} else if(userDTO!=null  && Constants.INSTAGRAM_DUMMY_USER.equals(userDTO.getId())) {
			InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
			if (instagramMessageRequest != null && instagramMessageRequest.getConversationDTO() != null
					&& CommunicationDirection.SEND
							.equals(instagramMessageRequest.getConversationDTO().getCommunicationDirection())) {
				messengerContact.setIsRead(false);
				resetViewedBy = false;
			}
		}else if(MessengerEvent.SMS_RECEIVE.equals(messageDTO.getEvent()) && messageDTO.getActivityDto()!=null && (ActivityType.SMS_SUBSCRIBE.equals(messageDTO.getActivityDto().getActivityType()) || ActivityType.SMS_UNSUBSCRIBE.equals(messageDTO.getActivityDto().getActivityType()))) {
				resetViewedBy = false;
		}
        
        if(resetViewedBy) {
        	messengerContact.setIsRead(false);
        	messengerContact.setViewedBy(messageDTO.getUserId() != null ? String.valueOf(messageDTO.getUserId()) : null);
        }
	}

    private ContactDocument updateContactOnES(MessageDTO messageDTO) {
    	MessageTag messageTag = null;
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        if(messageDTO.isUpdateTag()) {
        	messageTag = getMessageTag(messageDTO);	
        }
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        return messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, messageTag, userDTO);
    }

    private Optional<ContactDocument.Payment> getPaymentInfo(MessageDTO messageDTO) {
        if(Objects.isNull(messageDTO.getPaymentRequest())) return Optional.empty();
        PaymentUiRequest paymentRequest = messageDTO.getPaymentRequest();
        CreatePaymentLinkResponse createPaymentLinkResponse = messageDTO.getCreatePaymentLinkResponse();
        ContactDocument.Payment payment = new ContactDocument.Payment();
        payment.setPaymentId(createPaymentLinkResponse.getPaymentRequestId());
        payment.setStatus(PaymentStatus.NOT_PAID);
        payment.setCreatedAt(createPaymentLinkResponse.getLinkCreatedAt());
        payment.setUpdatedAt(createPaymentLinkResponse.getLinkCreatedAt());
        payment.setAmount(paymentRequest.getItems().get(0).getAmount());
        payment.setCurrency(createPaymentLinkResponse.getCurrency());
        
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        payment.setText(dto.getBody());
        return Optional.of(payment);
    }

    private MessageDocument updateMessageOnES(MessageDTO messageDTO) {
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		MessageDocument document = null;
        UserDTO userDTO = getUserDTO(messageDTO);
        MessageDocumentDTO messageDocumentDTO = getMessageDocumentDTO(messageDTO);
        MessengerMediaFileDTO messengerMediaFileDTO = getMessengerMediaFile(messageDTO);
        List<MediaFile> mediaList = messageDocumentDTO.getMediaFiles();
        MessengerEvent messengerEvent=getEvent();
        messengerEvent = modifyMessageEvent(messengerEvent,messageDTO);
        if(messageDTO.isThankYouNote()) {
            messageDocumentDTO.setReferredLead(messageDTO.getReferredLead());
            messageDocumentDTO.setReferrer(messageDTO.getReferrer());
        }
        setPaymentInfoInMessage(messageDTO, messageDocumentDTO);
        commonService.updateContactFiltersInMessage(messageDTO.getContactDocument(),
                messageDocumentDTO);
        //Create activity for GMB Auto-select location
        if (messageDTO.isActivityForGmbAutoselect()) {
        	GoogleUserMessage event = (GoogleUserMessage) messageDTO;
        	Date created = new Date(event.getSendTime().getTime() - 1000);
    		ActivityDto gmbAutoLocationSelectActivity = ActivityDto.builder().mcId(messageDTO.getMessengerContact().getId())
    				.created(created).updated(created)
    				.activityType(ActivityType.GMB_AUTO_LOCATION_SELECT)
    				.activityMeantFor(ActivityMeantFor.CUSTOMER)
    				.source(Source.GOOGLE.getSourceId())
    				.accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
    				.build();
    		saveActivity(gmbAutoLocationSelectActivity);
        }
        if (CollectionUtils.isNotEmpty(mediaList)) {
			document = messengerContactService.addMessageOnES(messageDocumentDTO, messengerMediaFileDTO, userDTO,
					businessDTO, messengerEvent, mediaList);
		} else {
			document = messengerContactService.andNewMessageOnEs(messageDocumentDTO, messengerMediaFileDTO, userDTO,
					businessDTO, messengerEvent);
		}
		return document;
    }

    protected void setPaymentInfoInMessage(MessageDTO messageDTO, MessageDocumentDTO messageDocumentDTO) {
        if(Objects.nonNull(messageDTO.getPaymentRequest())){
            PaymentUiRequest paymentRequest = messageDTO.getPaymentRequest();
            CreatePaymentLinkResponse createPaymentLinkResponse = messageDTO.getCreatePaymentLinkResponse();
            PaymentInfo paymentInfo = new PaymentInfo();
            paymentInfo.setId(createPaymentLinkResponse.getPaymentRequestId());
            paymentInfo.setTransactionId(createPaymentLinkResponse.getTransactionId());
            paymentInfo.setAmount(paymentRequest.getItems().get(0).getAmount());
            paymentInfo.setLink(createPaymentLinkResponse.getPaymentLink());
            paymentInfo.setStatus(PaymentStatus.NOT_PAID);
            paymentInfo.setInvoiceNumber(paymentRequest.getInvoiceNumber());
            paymentInfo.setItemDesc(paymentRequest.getItems().get(0).getItemDesc());
            paymentInfo.setMethod(paymentRequest.getMethod());
            UserDTO userDTO = getUserDTO(messageDTO);

            paymentInfo.setReqCreatedById(userDTO.getId());
            paymentInfo.setReqCreatedByName(userDTO.getName());
            paymentInfo.setReqCreatedAt(createPaymentLinkResponse.getLinkCreatedAt());
            paymentInfo.setSubscription(Objects.nonNull(paymentRequest.getSubscriptionRequest()));
            paymentInfo.setCurrency(createPaymentLinkResponse.getCurrency());
            messageDocumentDTO.setPaymentInfo(paymentInfo);
        }
    }

    private void setPaymentInfoInConversation(MessageDTO messageDTO, MessengerContact messengerContact) {
        Optional<ContactDocument.Payment> paymentOptional = getPaymentInfo(messageDTO);
        if(paymentOptional.isPresent()) {
            LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
            List<ContactDocument.Payment> payments = lastMessageMetadataPOJO.getPayment();
            if(CollectionUtils.isEmpty(payments)) payments = new ArrayList<>();
            ContactDocument.Payment currPayment = paymentOptional.get();
            payments.add(currPayment);
            //lastMessageMetadataPOJO.setL_payment_on(currPayment.getCreatedAt());
            lastMessageMetadataPOJO.setL_payment_on(messengerContact.getLastMsgOn().getTime());
            lastMessageMetadataPOJO.setPayment(payments);
            messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        }
    }

    private MessengerEvent modifyMessageEvent(MessengerEvent messengerEvent,MessageDTO messageDTO) {
	    	if(MessengerEvent.FACEBOOK_RECEIVE.equals(messengerEvent)) {
	    		FacebookMessageRequest facebookMessageRequest=(FacebookMessageRequest)messageDTO;
	    		Messaging msg = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
	    		messengerEvent=MessengerEvent.SMS_RECEIVE;
	    		if(CollectionUtils.isNotEmpty(msg.getMessage().getAttachments())) {
	    			messengerEvent=MessengerEvent.MMS_RECEIVE;
	    		}
	
	    		if(msg.getMessage()!=null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
	    			messengerEvent=MessengerEvent.SMS_SEND;
	    			if(CollectionUtils.isNotEmpty(msg.getMessage().getAttachments())) {
	    				messengerEvent=MessengerEvent.MMS_SEND;
	    			}
	    		}
	    	} else if (MessengerEvent.FACEBOOK_SEND.equals(messengerEvent)) {
	    		SendMessageDTO messageRequest=(SendMessageDTO)messageDTO;
	    		messengerEvent=MessengerEvent.SMS_SEND;
	    		if(messageRequest.getMediaUrls().size() > 0) {
	    			messengerEvent=MessengerEvent.MMS_SEND;
	    		}
	    	}else if(MessengerEvent.INSTAGRAM_MSG_RECEIVE.equals(messengerEvent)) {
	    		InstagramMessageRequest instagramMessageRequest=(InstagramMessageRequest)messageDTO;
	    		com.birdeye.messenger.dto.instagram.Messaging msg = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
	    		messengerEvent=MessengerEvent.SMS_RECEIVE;
	    		if(CollectionUtils.isNotEmpty(msg.getMessage().getAttachments())) {
	    			messengerEvent=MessengerEvent.MMS_RECEIVE;
	    		}
	    		if(msg.getMessage()!=null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
	    			messengerEvent=MessengerEvent.SMS_SEND;
	    			if(CollectionUtils.isNotEmpty(msg.getMessage().getAttachments())) {
	    				messengerEvent=MessengerEvent.MMS_SEND;
	    			}
	    		}
	    	} else if (MessengerEvent.INSTAGRAM_SEND.equals(messengerEvent)) {
	    		SendMessageDTO messageRequest=(SendMessageDTO)messageDTO;
	    		messengerEvent=MessengerEvent.SMS_SEND;
	    		if(messageRequest.getMediaUrls().size() > 0) {
	    			messengerEvent=MessengerEvent.MMS_SEND;
	    		}
	    	} else if(MessengerEvent.WHATSAPP_RECEIVE.equals(messengerEvent)) {
	    		WhatsappMessageRequest whatsappMessageRequest = (WhatsappMessageRequest) messageDTO;
	    		Value value = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue();
	    		Message waMessage = value.getMessages().get(0);
	    		messengerEvent=MessengerEvent.SMS_RECEIVE;
	    		
	    		if (WhatsappMsgType.image.name().equalsIgnoreCase(waMessage.getType()) || WhatsappMsgType.video.name().equalsIgnoreCase(waMessage.getType())
	    				|| WhatsappMsgType.document.name().equalsIgnoreCase(waMessage.getType()) || WhatsappMsgType.audio.name().equalsIgnoreCase(waMessage.getType())) {
	    			messengerEvent=MessengerEvent.MMS_RECEIVE;
	    		}
	    	} else if (MessengerEvent.WHATSAPP_SEND.equals(messengerEvent)) {
	    		SendMessageDTO messageRequest=(SendMessageDTO)messageDTO;
	    		messengerEvent=MessengerEvent.SMS_SEND;
	    		if(messageRequest.getMediaUrls().size() > 0) {
	    			messengerEvent=MessengerEvent.MMS_SEND;
	    		}
	    	}
	    	else if (MessengerEvent.SMS_RECEIVE.equals(messengerEvent)) {
	    		SMSMessageDTO messageRequest=(SMSMessageDTO)messageDTO;
	    		if(messageRequest.getSmsDTO()!=null && messageRequest.getSmsDTO().getMediaURL() != null) {
	    			messengerEvent=MessengerEvent.MMS_RECEIVE;
	    		}
	    	}else if (MessengerEvent.SMS_SEND.equals(messengerEvent)) {
	    		SendMessageDTO messageRequest=(SendMessageDTO)messageDTO;
	    		if(messageRequest.getMediaUrls().size() > 0) {
	    			messengerEvent=MessengerEvent.MMS_SEND;
	    		}
	    	}
	
	    	return messengerEvent;
	}

    private void pushMessageTagToFireBase(MessageDTO messageDTO) {
        Integer mcId = getMessengerContact(messageDTO).getId();
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        Integer accountId = businessDTO.getRoutingId();
        Integer locationId = businessDTO.getBusinessId();
        Map<String,Integer> param = new HashMap<>();
        param.put("tag", getMessageTag(messageDTO).getCode());
        String topicName = "messenger/" + accountId + "/" + locationId + "/" + mcId;
        log.info("pushMessageTagToFireBase: topic {} data {} ", topicName, param);
        //nexusService.updateFirebaseDb(new GenericFirebaseMessage<Map>(param, topicName));
    }

    protected boolean getSendEmailNotification(MessageDTO messageDTO) {
        return messageDTO.getSendEmailNotification();
    }

    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        return messageDTO.getBusinessDTO();
    }

     CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        return messageDTO.getCustomerDTO();
    }

    UserDTO getUserDTO(MessageDTO messageDTO) {
        return messageDTO.getUserDTO();
    }

    MessageTag getMessageTag(MessageDTO messageDTO) {
        return messageDTO.getMessageTag();
    }

    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        MessageDocumentDTO messageDocumentDTO = messageDTO.getMessageDocumentDTO();
        if( messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)){
            messageDocumentDTO.setSpam(true);
        }else{
            messageDocumentDTO.setSpam(false);
        }
        return messageDocumentDTO;
    }

    MessengerMediaFileDTO getMessengerMediaFile(MessageDTO messageDTO) {
        return messageDTO.getMessengerMediaFileDTO();
    }

	List<MessengerMediaFileDTO> getMessengerMediaFileList(MessageDTO messageDTO) {
		return messageDTO.getMessengerMediaFileList();
	}

    protected void publishEventIfRepliedOnUnassignedConversation(MessageDTO messageDTO) {
    	log.info("publishEventIfRepliedOnUnassignedConversation: [ {} ] ", messageDTO.toString());
		if(messageDTO.isBOT()) {
            log.info("AutoReply:{}", messageDTO.toString());
            return;
        }
    	if(messageDTO.getMessengerContact().getCurrentAssignee() != null &&  messageDTO.getMessengerContact().getCurrentAssignee() > 0) {
			// Conversation already assigned
            log.info("publishEventIfRepliedOnUnassignedConversation: conversation already assigned to {} ", messageDTO.getMessengerContact().getCurrentAssignee());
			return;
		}	
		
    	if(PaymentAutomationUser.getPaymentAutomationUserByUserId(messageDTO.getUserId()) != null) {
    		log.info("publishEventIfRepliedOnUnassignedConversation: Conversation generated by payment automation users hence returning");
			return;
    	}
    	
    	if(messageDTO.getUserId().equals(Constants.INBOX_USER_ID)) {
    		log.info("publishEventIfRepliedOnUnassignedConversation: Conversation generated by inbox user hence returning");
    		return;
    	}
    	
    	if(messageDTO.isPartOfExistingMessage()) {
            log.info("publishEventIfRepliedOnUnassignedConversation: part of existing message");
    		// Method is called again for a given message . Multiple attachments
    		return;
    	}
    	
		if (messageDTO.getUserId().equals(MessengerConstants.ROBIN_REPLY_USER)) {
			// not sending event for robin reply
			return;
		}
		try {
            log.info("publishEventIfRepliedOnUnassignedConversation: conversation:{} and replier:{}",
                    messageDTO.getMessengerContact().getId(), messageDTO.getUserId());
			UserDTO userDTO = messageDTO.getUserDTO();
			Integer accountId = messageDTO.getBusinessDTO().getEnterpriseId() != null
					? messageDTO.getBusinessDTO().getEnterpriseId()
					: messageDTO.getBusinessDTO().getBusinessId();
			UserIdentity replier = new UserIdentity(userDTO.getId(), userDTO.getName(), userDTO.getEmailId());
			ReplyOnUAConversationEvent event = new ReplyOnUAConversationEvent(replier,
					messageDTO.getMessengerContact().getId(), accountId);
            log.info("publishEventIfRepliedOnUnassignedConversation: event data {}", event);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.UNASSIGNED_CONVERSATION_REPLY,
					messageDTO.getMessengerContact().getId(), event);
		} catch (Exception e) {
			log.error("Some error occurred while pushing SMS send event for unreplied conversations", e);
		}
	}

	protected boolean isFireBaseSyncEnabled(MessageDTO messageDTO) {
        return Boolean.TRUE; // Override this in respective handler to toggle this.
    }

    abstract void updateLastMessageMetaData(MessageDTO messageDTO);

    abstract void alterAndUpdateLastMessage(MessageDTO messageDTO);

    abstract MessengerContact getMessengerContact(MessageDTO messageDTO);

     abstract MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO);

	 abstract Integer getMessageId(MessageDTO messageDTO);
    
     abstract void publishEvent(MessageDTO messageDTO);

    private void sendThankYouNoteIfApplicable(MessageDTO messageDTO) {
        if (messageDTO.isThankYouNote()) {

            MessengerContact referrerMc = messageDTO.getMessengerContact();
            MessengerContact leadMc = messengerContactService.findById(messageDTO.getReferredLead().getMcId());
            ThankYouNoteEvent event = new ThankYouNoteEvent();
            event.setThankyouNoteSent(1);
            event.setThankyouNoteSentDate(new Date());
            event.setLocationId(messageDTO.getBusinessDTO().getBusinessId());
            event.setEnterpriseId(messageDTO.getBusinessDTO().getAccountId());
            event.setReferrerCid(referrerMc.getCustomerId());
            event.setCid(leadMc.getCustomerId());
            thankYouNoteEventProducer.produceEvent(event);
        }
    }

    private ResponseTimeOutBox processResponseTimeIfApplicable(MessageDTO messageDTO) {
        String currMsgType = messageDTO.getMsgTypeForResTimeCalc();
        if(StringUtils.isBlank(currMsgType)) return null;
        ResponseTimeOutBox responseTimeOutBox = null;
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        MessageDocumentDTO messageDocumentDTO = getMessageDocumentDTO(messageDTO);
        String currMsgId = messageDocumentDTO.getM_id() + MessengerUtil.getMessageTypeSuffix(messageDocumentDTO.getMessageType(), messageDocumentDTO.getSource());
        responseTimeOutBox = responseTimeCalculationService.processResTimeMessage(messengerContact, currMsgId, messageDocumentDTO.getCr_time(), messageDocumentDTO.getSource(), messageDTO.getMsgTypeForResTimeCalc());
        return responseTimeOutBox;
    }

    private String getOldLmsgOn(MessageDTO messageDTO) {
        try {
            return messageDTO.getMessengerContact().getLastMsgOn().toString();
        } catch (Exception e) {
            return null;
        }
    }

    private MessageDocument saveActivity(ActivityDto activity) {
      conversationActivityService.persistActivityInDatabase(activity, null);
      return conversationActivityService.persistActivityInES(activity);
    }
    
}
