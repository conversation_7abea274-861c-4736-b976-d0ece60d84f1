package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.MessageVolumeProfileReportDataPoint;
import com.birdeye.messenger.dto.MessageVolumeProfileReportResponse;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.report.InboxRoiReportResponseDto;
import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ReportDataPoint;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.stereotype.Service;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class InboxRoiReport implements ReportService {

    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    public InboxRoiReportResponseDto getReport(ReportFilter filter) {
        return buildESRequestForReceiveMessages(filter);
    }

    @Override
    public Enum getReportType() {
        return ReportType.INBOX_ROI_REPORT;
    }

    private InboxRoiReportResponseDto buildESRequestForReceiveMessages(ReportFilter filter) {

        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.INBOX_ROI_REPORT,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
//        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
//        templateData.put("customerType",filter.getCustomerType());
//        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
//        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        templateData.put("accountTimeZoneId", filter.getAccountTimeZoneId());
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.INBOX_ROI_REPORT, data)
                .addSize(0)
                .build();

        SearchResponse searchResult = elasticSearchService.getSearchResult(request);


        if (searchResult.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch Message volume Profile Report");
        } else {
            InboxRoiReportResponseDto response = new InboxRoiReportResponseDto();
            List <InboxRoiReportResponseDto.DataPoints> dataPoints = new ArrayList<>();
            ParsedStringTerms totalSendReceiveMessageAggregation = searchResult.getAggregations().get("communicationDirection_aggregation");
            Long sendMessages = 0L;
            Long receiveMessages = 0L;
            if(CollectionUtils.isNotEmpty(totalSendReceiveMessageAggregation.getBuckets())){
                for(int i = 0; i < totalSendReceiveMessageAggregation.getBuckets().size(); i++){
                    if("SEND".equals(totalSendReceiveMessageAggregation.getBuckets().get(i).getKeyAsString())){
                        sendMessages = totalSendReceiveMessageAggregation.getBuckets().get(i).getDocCount();
                    }else if("RECEIVE".equals(totalSendReceiveMessageAggregation.getBuckets().get(i).getKeyAsString())){
                        receiveMessages = totalSendReceiveMessageAggregation.getBuckets().get(i).getDocCount();
                    }
                }
            }

            for(int i = 0; i < filter.getWidgetsNeeded().size(); i++){
                if("messages_sent".equals(filter.getWidgetsNeeded().get(i))){
                    InboxRoiReportResponseDto.DataPoints dataPoint = new InboxRoiReportResponseDto.DataPoints();
                    dataPoint.setValue(Double.valueOf(sendMessages));
                    dataPoint.setWidgetName("messages_sent");
                    dataPoint.setType("Double");
                    dataPoints.add(dataPoint);
                }else if("messages_received".equals(filter.getWidgetsNeeded().get(i))){
                    InboxRoiReportResponseDto.DataPoints dataPoint = new InboxRoiReportResponseDto.DataPoints();
                    dataPoint.setValue(Double.valueOf(receiveMessages));
                    dataPoint.setWidgetName("messages_received");
                    dataPoint.setType("Double");
                    dataPoints.add(dataPoint);
                }
            }

            response.setDataPoints(dataPoints);
            return response;
        }
    }


    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        InboxRoiReportResponseDto inboxRoiReportResponse =  getReport(filters);
        return  inboxRoiReportResponse;
    }

}
