package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.MessengerNote;
import com.birdeye.messenger.dao.repository.MessengerMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerNoteRepository;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.service.AddNoteService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class AddNoteServiceImpl implements AddNoteService {
	
	@Autowired
	private MessengerNoteRepository messengerNoteRepository;
	
	@Autowired
	private MessengerMessageRepository messengerMessageRepository;

	@Override
	@Transactional
	public ConversationDTO saveNote(SendMessageDTO sendMessageDTO) {
		log.debug("Persisting messenger note for contact {}",sendMessageDTO.getToCustomerId());
		MessengerNote note=convertDtoToNoteEntity(sendMessageDTO);
		note=messengerNoteRepository.saveAndFlush(note);
		MessengerMessage messengerMessage=convertDtoToMessageEnity(sendMessageDTO,note.getId());
		messengerMessage.setMessageId(note.getId());
		messengerMessage=messengerMessageRepository.saveAndFlush(messengerMessage);
		log.info("Note is persisted in DB for conatct {}",sendMessageDTO.getToCustomerId());
		ConversationDTO conversationDTO=new ConversationDTO(messengerMessage,note);
		conversationDTO.setSentThrough(sendMessageDTO.getSentThrough());
		return conversationDTO;
	}

	private MessengerMessage convertDtoToMessageEnity(SendMessageDTO sendMessageDTO, Integer noteId) {
		MessengerMessage message=new MessengerMessage();
		message.setAccountId(sendMessageDTO.getFromBusinessId());
		message.setMessageId(noteId);
		message.setCreatedDate(new Date());
		message.setCreatedBy(sendMessageDTO.getUserId());
		message.setSentThrough(sendMessageDTO.getSentThrough().name());
		message.setMessageType(MessageDocument.MessageType.INTERNAL_NOTES.name());
		message.setMessengerContactId(sendMessageDTO.getMessengerContact().getId());
		return message;
	}

	private MessengerNote convertDtoToNoteEntity(SendMessageDTO sendMessageDTO) {
		MessengerNote messengerNote=new MessengerNote();
		messengerNote.setContent(sendMessageDTO.getBody());
		messengerNote.setCreatedBy(sendMessageDTO.getUserId());
		messengerNote.setNoteStatus(MessageDocument.NoteState.CREATED.name());
		messengerNote.setNoteType(MessageDocument.NoteType.INFO.name());
		messengerNote.setCreatedDate(new Date());
		return messengerNote;
	}

	@Override
  public void deleteMessengerNoteUsingIds(List<Integer> messageIds){
	    messengerNoteRepository.deleteByIdIn(messageIds);
  }


	@Override
	@Transactional
	public void deleteConversationNotes(Map<String, List<Integer>> result, Integer customerId) {
	    try {
            if (MapUtils.isNotEmpty(result)
                    && CollectionUtils.isNotEmpty(result.get(MessageDocument.MessageType.INTERNAL_NOTES.name()))) {
                List<Integer> noteIds = result.get(MessageDocument.MessageType.INTERNAL_NOTES.name());
                deleteMessengerNoteUsingIds(noteIds);
                log.info("Deleting messenger notes for ids: {} and customer id: {}", noteIds, customerId);
            }
        } catch (Exception e) {
            log.error("error : {} occurred in deleteConversationNotes", e.getMessage());
        }
	}
	
}
