package com.birdeye.messenger.service.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dao.repository.ResponseTimeOutBoxRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessHoursDTO;
import com.birdeye.messenger.dto.ConvStateForResTimeCalc;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.ResponseTimeCalculationService;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ResponseTimeCalculationServiceImpl implements ResponseTimeCalculationService {

    private final BusinessService businessService;
    private final ResponseTimeOutBoxRepository responseTimeOutBoxRepository;
    private final ElasticSearchExternalService elasticSearchService;

    @Override
    public ResponseTimeOutBox processResTimeMessage(MessengerContact messengerContact, String currMsgId, Long currMsgTime, Integer source, String currMsgType) {
        if(StringUtils.isBlank(currMsgType)) return null;
        ResponseTimeOutBox responseTimeOutBox = null;
        ConvStateForResTimeCalc prevState = messengerContact.getCurrentStateForResTimeCalc();
        ConvStateForResTimeCalc nextState = ConvStateForResTimeCalc.copyState(prevState);
        String prevMsgType = StringUtils.isNotBlank(prevState.getRtmLastMsgType()) ? prevState.getRtmLastMsgType() : "E";

        if("R".equalsIgnoreCase(prevMsgType) && "S".equalsIgnoreCase(currMsgType)) {
            responseTimeOutBox = prepareMessageForResponseTimeCalculation(messengerContact,
                    currMsgId,
                    prevState.getRtmLastMsgEpoch(),
                    currMsgTime,
                    source,
                    prevState.getTagAsFirstResponse() == null ? true : prevState.getTagAsFirstResponse(), prevState.getRtmLastMsgId());
            nextState.setTagAsFirstResponse(false);
        }
        nextState.setRtmLastMsgId(currMsgId);
        nextState.setRtmLastMsgType(currMsgType);
        nextState.setRtmLastMsgEpoch(currMsgTime);
        messengerContact.setCurrentStateForResTimeCalc(nextState);
        return responseTimeOutBox;

    }

    @Override
    @SuppressWarnings("unchecked")
    public void saveResponseTime(ResponseTimeOutBox responseTimeOutBox) {

        if(responseTimeOutBox.getReceiveTimeEpoch() > responseTimeOutBox.getSentTimeEpoch()) {
            updateLog(responseTimeOutBox, "FAILED", "receiveTime cannot be greater than sentTime");
            return;
        }
        //1. get message by id from elastic search
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("_id", responseTimeOutBox.getMsgId());
        BusinessDTO businessDTO = businessService.getBusinessDTO(responseTimeOutBox.getBusinessId(), 1);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(businessDTO.getAccountId())
                .addSize(1)
                .addTemplateAndDataModel(Constants.Elastic.MESSAGE_BY_ID_V2, dataModel).build();
        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(request, MessageDocument.class);
        if(!dataFromElastic.isSucceeded()) updateLog(responseTimeOutBox, "FAILED", "Failed to fetch data from elastic-search");
        else if(CollectionUtils.isEmpty(dataFromElastic.getResults())) updateLog(responseTimeOutBox, "FAILED", "Document not found corresponding to msgId");
        else {
            MessageDocument message = dataFromElastic.getResults().stream().findFirst().get();
            BusinessHoursDTO businessHours = businessService.getBusinessHours(businessDTO.getBusinessId());
            calculateAndSetResponseTime(responseTimeOutBox, message, businessHours);
            //3. push to elastic search & update log file ( make it idempotent )
            ESRequest.Upsert<MessageDocument> updateRequest = new ESRequest.Upsert<>(message, responseTimeOutBox.getMsgId());
            ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                    .addRoutingId(businessDTO.getAccountId()).addPayloadForUpsert(updateRequest)
                    .build();
            boolean updated = elasticSearchService.updateDocument(esRequest, false);
            if(updated) {
                updateLog(responseTimeOutBox, "SUCCESS", null);
            }
        }
    }

    private void calculateAndSetResponseTime(ResponseTimeOutBox responseTimeOutBox, MessageDocument message, BusinessHoursDTO businessHours) {
        //calculate the response time absolute and relative
        Long sentTime = responseTimeOutBox.getSentTimeEpoch();
        Long receiveTime = responseTimeOutBox.getReceiveTimeEpoch();
        Long absoluteResponseTime = sentTime - receiveTime;
        Long relativeResponseTime = null;
        if(businessHours.getContactStatus() == 1 || businessHours.getContactStatus() == 2) {
            relativeResponseTime = absoluteResponseTime;
        }
        else {
            relativeResponseTime = calculateResTimeWrtBH(receiveTime, sentTime, businessHours);
        }
        message.setFirstResponse(responseTimeOutBox.getFirstResponse());
        message.setAbsoluteResponseTime(absoluteResponseTime);
        message.setRelativeResponseTime(relativeResponseTime);
        message.setRefMsgId(responseTimeOutBox.getReceivedMsgId());
        message.setType(MessageDocument.Type.UPDATE.getType());
        message.setU_time(new Date().getTime());
    }

    /*
     For detailed explanation read : https://birdeye.atlassian.net/browse/BIRDEYE-76854
     BH : Business Hour
     Special Handling for Central Standard/Daylight Time and British Summer Time
     */
    public Long calculateResTimeWrtBH(Long receiveTime, Long sentTime, BusinessHoursDTO businessHours) {
        String timeZone = StringUtils.isBlank(businessHours.getTimeZoneId())? "PST" : businessHours.getTimeZoneId();
        Calendar rCal = Calendar.getInstance();
        rCal.setTimeInMillis(receiveTime);
        Calendar sCal = Calendar.getInstance();
        sCal.setTimeInMillis(sentTime);
//        if(timeZone.equalsIgnoreCase("British Summer Time")) {
//            rCal.add(Calendar.HOUR_OF_DAY, 1);
//            sCal.add(Calendar.HOUR_OF_DAY, 1);
//        }
//        else {
//            if(timeZone.equalsIgnoreCase("Central Standard Time") || timeZone.equalsIgnoreCase("Central Daylight Time")) {
//                //BIRDEYE-58105 issue regarding timezone, previous similar issue - BIRDEYE-17004
//                // Central Standard Time is not unique, many Timezone ids use this.
//                // https://docs.oracle.com/middleware/12212/wcs/tag-ref/MISC/TimeZones.html
//                timeZone = "CST";
//            }
            TimeZone businessTimeZone = TimeZone.getTimeZone(businessHours.getTimeZoneId());
            rCal = TimeZoneUtil.convertToBusinessTimeZoneWithDaylight(rCal.getTime(), businessTimeZone);
            sCal = TimeZoneUtil.convertToBusinessTimeZoneWithDaylight(sCal.getTime(), businessTimeZone);
//        }

        // round off to next minute
        int rSec = rCal.get(Calendar.SECOND);
        if(rSec  >= 30) {
            rCal.add(Calendar.SECOND, 60-rSec);
        }
        int sSec = sCal.get(Calendar.SECOND);
        if(sSec >= 30) {
            sCal.add(Calendar.SECOND, 60-sSec);
        }
        // at BirdEye we take Monday as day 0 and sunday as day 6 but in calender api monday is day 2 and sunday as day 0 hence below operation
        int rDay = rCal.get(Calendar.DAY_OF_WEEK) - 2 < 0 ? 6 : rCal.get(Calendar.DAY_OF_WEEK) - 2 ;
        int sDay = sCal.get(Calendar.DAY_OF_WEEK) - 2 < 0 ? 6 : sCal.get(Calendar.DAY_OF_WEEK) - 2;
        int rHour = rCal.get(Calendar.HOUR_OF_DAY);
        int rMinute = rCal.get(Calendar.MINUTE);
        int sHour = sCal.get(Calendar.HOUR_OF_DAY);
        int sMinute = sCal.get(Calendar.MINUTE);

        String rTime = rHour + ":" + rMinute;
        String sTime = sHour + ":" + sMinute;
        long responseTimeInMin = 0;

        rCal.set(Calendar.HOUR_OF_DAY, 0);
        rCal.set(Calendar.MINUTE, 0);
        rCal.set(Calendar.SECOND, 0);
        rCal.set(Calendar.MILLISECOND, 0);

        sCal.set(Calendar.HOUR_OF_DAY, 0);
        sCal.set(Calendar.MINUTE, 0);
        sCal.set(Calendar.SECOND, 0);
        sCal.set(Calendar.MILLISECOND, 0);

        Map<Integer, List<BusinessHoursDTO.OperationHours>> dailyWorkingHours = businessHours.getDailyWorkingHours();
        long daysInclusiveOfRS = TimeUnit.DAYS.convert(sCal.getTimeInMillis() - rCal.getTimeInMillis(), TimeUnit.MILLISECONDS) + 1;
        for (int i = 0; i < daysInclusiveOfRS; i++, rDay++) {
            int dayAsPerBH = rDay % 7;
            List<BusinessHoursDTO.OperationHours> workingHoursForCurrDay = dailyWorkingHours.get(dayAsPerBH);
            if (Objects.isNull(workingHoursForCurrDay)) continue;
            // the day when message was received
            if (i == 0) {
                for (BusinessHoursDTO.OperationHours oh : workingHoursForCurrDay) {
                    if (oh.getIsOpen() != 0) {
                        // amazon prime same day delivery
                        if (sDay == rDay) {
                            if (MessengerUtil.compareTime(rTime, oh.getStartHour()) < 0 && MessengerUtil.compareTime(sTime, oh.getEndHour()) > 0) {
                                responseTimeInMin += oh.getTimeInMin();
                            } else if (MessengerUtil.compareTime(rTime, oh.getStartHour()) >= 0 && MessengerUtil.compareTime(sTime, oh.getEndHour()) <= 0) {
                                responseTimeInMin += MessengerUtil.getTimeInMin(rTime, sTime);
                            } else if (MessengerUtil.compareTime(rTime, oh.getStartHour()) < 0 && MessengerUtil.compareTime(sTime, oh.getStartHour()) >= 0 && MessengerUtil.compareTime(sTime, oh.getEndHour()) <= 0) {
                                responseTimeInMin += MessengerUtil.getTimeInMin(oh.getStartHour(), sTime);
                            } else if (MessengerUtil.compareTime(rTime, oh.getStartHour()) >= 0 && MessengerUtil.compareTime(rTime, oh.getEndHour()) <= 0 && MessengerUtil.compareTime(sTime, oh.getEndHour()) > 0) {
                                responseTimeInMin += MessengerUtil.getTimeInMin(rTime, oh.getEndHour());
                            }
                        } else {
                            if (MessengerUtil.compareTime(rTime, oh.getStartHour()) < 0) {
                                responseTimeInMin += oh.getTimeInMin();
                            } else if (MessengerUtil.compareTime(oh.getStartHour(), rTime) <= 0 && MessengerUtil.compareTime(oh.getEndHour(), rTime) >= 0) {
                                responseTimeInMin += MessengerUtil.getTimeInMin(rTime, oh.getEndHour());
                            }
                        }
                    }
                }
            }
            // the day a response was sent
            else if (i == daysInclusiveOfRS - 1) {
                for (BusinessHoursDTO.OperationHours oh : workingHoursForCurrDay) {
                    if (oh.getIsOpen() != 0) {
                        if (MessengerUtil.compareTime(oh.getEndHour(), sTime) < 0) {
                            responseTimeInMin += oh.getTimeInMin();
                        } else if (MessengerUtil.compareTime(sTime, oh.getStartHour()) >= 0 && MessengerUtil.compareTime(sTime, oh.getEndHour()) <= 0) {
                            responseTimeInMin += MessengerUtil.getTimeInMin(oh.getStartHour(), sTime);
                        }
                    }
                }
            } else {
                // intermediate Days
                Long workingTimeInMin = workingHoursForCurrDay.stream()
                        .filter(operationHour -> operationHour.getIsOpen() == 1)
                        .map(BusinessHoursDTO.OperationHours::getTimeInMin)
                        .reduce(0L, Long::sum);
                responseTimeInMin += workingTimeInMin;
            }
        }
        return responseTimeInMin * 60 * 1000;
    }

    private void updateLog(ResponseTimeOutBox responseTimeOutBox, String status, String message) {
        responseTimeOutBox.setUpdated(new Date());
        responseTimeOutBox.setStatus(status);
        responseTimeOutBox.setMessage(message);
        responseTimeOutBoxRepository.save(responseTimeOutBox);
    }

    private ResponseTimeOutBox prepareMessageForResponseTimeCalculation(MessengerContact contact, String msgId, Long receiveTime, Long sendTime, Integer source, Boolean rtmFirstResTimeToCalc, String receivedMsgId) {
        ResponseTimeOutBox responseTimeOutBox = new ResponseTimeOutBox();
        responseTimeOutBox.setBusinessId(contact.getBusinessId());
        responseTimeOutBox.setMcId(contact.getId());
        responseTimeOutBox.setFirstResponse(rtmFirstResTimeToCalc);
        responseTimeOutBox.setMsgId(msgId);
        responseTimeOutBox.setReceivedMsgId(receivedMsgId);
        responseTimeOutBox.setReceiveTimeEpoch(receiveTime);
        responseTimeOutBox.setSentTimeEpoch(sendTime);
        responseTimeOutBox.setSource(source);
        responseTimeOutBox.setStatus("PENDING");
        return responseTimeOutBox;
    }

    public static void main(String[] args) {

    }
}