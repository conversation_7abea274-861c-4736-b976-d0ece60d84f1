package com.birdeye.messenger.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilters;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilters.ParsedBucket;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedTDigestPercentiles;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dto.BizAppGetAllContactsResponse;
import com.birdeye.messenger.dto.BizAppGetAllMessagesResponse;
import com.birdeye.messenger.dto.BizAppMessageCountOutput;
import com.birdeye.messenger.dto.BizAppMessageCountOutput.BucketsData;
import com.birdeye.messenger.dto.BizAppMessageCountOutput.EGroup;
import com.birdeye.messenger.dto.BizAppMessageCountOutput.MessageCountData;
import com.birdeye.messenger.dto.BizAppMessageCountOutput.UniqueInvoiceid;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ExportInboxRequest;
import com.birdeye.messenger.dto.ExternalReportFilter;
import com.birdeye.messenger.dto.InboxSendMessageReportResponse;
import com.birdeye.messenger.dto.InboxSendMessageReportResponse.ChannelCount;
import com.birdeye.messenger.dto.InboxSendMessageReportResponse.ChannelCountWithBusiness;
import com.birdeye.messenger.dto.MatrixRoiReport;
import com.birdeye.messenger.dto.MatrixRoiReport.CountWithBusiness;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerFilterForExportInbox;
import com.birdeye.messenger.dto.ReserveWGoogleResponse;
import com.birdeye.messenger.dto.ReserveWGoogleResponseReport;
import com.birdeye.messenger.dto.SmsCommunicationRequest;
import com.birdeye.messenger.dto.SmsCommunicationResponse;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.ExternalReportService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExternalReportServiceImpl implements ExternalReportService {

	private final KafkaService kafkaService;
	private final ElasticSearchExternalService esSearchService;
	private final MessengerContactService messengerContactService;
	private final ObjectMapper objectMapper;
	private final BusinessService businessService;
	private final BusinessChatWidgetConfigService businessChatWidgetConfigService;
	
	@Override
	public void getInboxSendReportByLocations(MessengerFilter filters) {
		List<Integer> businessIds = filters.getBusinessIds();
		SearchResponse searchResponse = null;
		InboxSendMessageReportResponse responseObject = new InboxSendMessageReportResponse();
		if (businessIds!=null) {
			searchResponse = getInboxSendMessage(businessIds);
			if (searchResponse.status().getStatus() != 200) {
				log.error("GetInboxSendMessage failed");
				return;	
			}
			
//			//Get SEND MESSAGE aggregation
			List<ChannelCountWithBusiness> statsFor30DaysResponseObject = new ArrayList<>();
			ParsedFilters filteredAggregations = searchResponse.getAggregations().get("count_30_days");
			ParsedBucket thirtydaysbucket = filteredAggregations.getBucketByKey("count_30_days");
			ParsedLongTerms parsedbucket = thirtydaysbucket.getAggregations().get("count_30_days");
			for (Integer bId : businessIds) {
				Terms.Bucket elasticBucket = parsedbucket.getBucketByKey(String.valueOf(bId));
				if ( elasticBucket == null)
					continue;
				Terms sendMessageAggregations = elasticBucket.getAggregations().get("count_30_days");
				ChannelCountWithBusiness businessChannelCount = new ChannelCountWithBusiness();
				businessChannelCount.setBusinessId(Integer.parseInt(elasticBucket.getKey().toString()));
				List<ChannelCount> listChannelCount = getChannelStatList(sendMessageAggregations.getBuckets());
				businessChannelCount.setSendStats(listChannelCount);
				statsFor30DaysResponseObject.add(businessChannelCount);
			}
			responseObject.setLast30DaysSendStats(statsFor30DaysResponseObject);
			
			//Get all_time_message_count aggregation
			List<ChannelCountWithBusiness> statsForAllTimeResponseObject = new ArrayList<>();
			Terms allTimeAggregations = searchResponse.getAggregations().get("all_time_message_count");
			for (Integer bId : businessIds) {
				Terms.Bucket elasticBucket = allTimeAggregations.getBucketByKey(String.valueOf(bId));
				if ( elasticBucket == null)
					continue;
				Terms allTimeSubAggregations = elasticBucket.getAggregations().get("all_time_message_count");
				ChannelCountWithBusiness businessChannelCount = new ChannelCountWithBusiness();
				businessChannelCount.setBusinessId(Integer.parseInt(elasticBucket.getKey().toString()));
				List<ChannelCount> listChannelCount = getChannelStatList(allTimeSubAggregations.getBuckets());
				businessChannelCount.setSendStats(listChannelCount);
				statsForAllTimeResponseObject.add(businessChannelCount);
			}
			responseObject.setAllTimeSendStats(statsForAllTimeResponseObject);
		}
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.INBOX_SEND_REPORT, responseObject);
	}

	private SearchResponse getInboxSendMessage(List<Integer> businessIds) {
		SearchResponse searchResult;
		
		Date startDate = new Date();
		LocalDateTime ldt = LocalDateTime.ofInstant(startDate.toInstant(), ZoneId.systemDefault());
		LocalDateTime startLocalDateTime = ldt.minus(30, ChronoUnit.DAYS);
		Date out = Date.from(startLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());

		
		long startTime = out.getTime(); //past 30 days
		long endTime = new Date().getTime();
		
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
		messageFilter.put("startDate", startTime);
		messageFilter.put("endDate", endTime);
		log.info("GetInboxSendMessage for startTime: {} endTime: {}", startTime, endTime);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("filterMessage", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_INBOX_SEND_MESSAGE_REPORT, dataModel).build();
		searchResult = esSearchService.getSearchResult(esRequest);
		return searchResult;
	}
	
	private List<ChannelCount> getChannelStatList(List<? extends Bucket> list) {
		List<ChannelCount> listChannelCount = list.stream().map(channelBucket -> {
			ChannelCount channelCount = new InboxSendMessageReportResponse.ChannelCount();
			Source source = Source.getValue(Integer.parseInt(channelBucket.getKey().toString()));
			channelCount.setChannel(source.name());
			channelCount.setCount(channelBucket.getDocCount());
		    return channelCount;
		}).collect(Collectors.toList());
		return listChannelCount;
	}

	@Override
	public void getSendByChannelReport(MessengerFilter filters) {
		Integer accountId = filters.getAccountId();
		SearchResponse searchResult = null;
		if (accountId != null) {
			String queryTemplate = Constants.Elastic.BIZAPP_GET_SEND_BY_CHANNEL;
			searchResult = getInboxSendMessagesResult(filters, accountId, queryTemplate);
			if (searchResult.status().getStatus() != 200) {
				log.error("GetInboxSendMessage failed");
				return;
			}
		}
		BizAppMessageCountOutput response = new BizAppMessageCountOutput();
		Map<String,MessageCountData> dataOutputMap = new HashMap<>();
		
		ParsedFilter receivedViaTwilioAggregations = searchResult.getAggregations().get("received_via_twilio");
		ParsedLongTerms twilio = receivedViaTwilioAggregations.getAggregations().get("e_group");

		if (Objects.nonNull(twilio)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(twilio);
			dataOutputMap.put("received_via_twilio", data);
		}

		ParsedFilter webchatAggregations = searchResult.getAggregations().get("received_via_webchat");
		ParsedLongTerms webchat = webchatAggregations.getAggregations().get("e_group");

		if (Objects.nonNull(webchat)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(webchat);
			dataOutputMap.put("received_via_webchat", data);
		}
		
		ParsedFilter micrositeAggregations = searchResult.getAggregations().get("received_via_microsite_webchat");
		ParsedLongTerms microsite = micrositeAggregations.getAggregations().get("e_group");

		if (Objects.nonNull(microsite)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(microsite);
			dataOutputMap.put("received_via_microsite_webchat", data);
		}
		
		ParsedFilter receiveFbAggregations = searchResult.getAggregations().get("received_fb");
		ParsedLongTerms receiveFb = receiveFbAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(receiveFb)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(receiveFb);
			dataOutputMap.put("received_fb", data);
		}
		
		ParsedFilter mailreplyAggregations = searchResult.getAggregations().get("send_viamailreply");
		ParsedLongTerms mail = mailreplyAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(mail)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(mail);
			dataOutputMap.put("send_viamailreply", data);
		}
		
		ParsedFilter msgrAggregations = searchResult.getAggregations().get("send_msgr");
		ParsedLongTerms msgr = msgrAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(msgr)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(msgr);
			dataOutputMap.put("send_msgr", data);
		}
		
		ParsedFilter sendFbAggregations = searchResult.getAggregations().get("send_fb");
		ParsedLongTerms sendFb = sendFbAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(sendFb)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(sendFb);
			dataOutputMap.put("send_fb", data);
		}
		
		ParsedFilter sendMobAggregations = searchResult.getAggregations().get("send_mob");
		ParsedLongTerms sendMob = sendMobAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(sendMob)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(sendMob);
			dataOutputMap.put("send_mob", data);
		}
		
		ParsedFilter sendCampaignAggregations = searchResult.getAggregations().get("send_via_campaign");
		ParsedLongTerms sendCampaign = sendCampaignAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(sendCampaign)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(sendCampaign);
			dataOutputMap.put("send_via_campaign", data);
		}
		
		ParsedFilter autoreplyAggregations = searchResult.getAggregations().get("auto_reply_twilio");
		ParsedLongTerms autoreply = autoreplyAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(autoreply)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(autoreply);
			dataOutputMap.put("auto_reply_twilio", data);
		}
		
		ParsedFilter msgrAutoreplyAggregations = searchResult.getAggregations().get("send_msgr_with_auto_reply");
		ParsedLongTerms msgrAutoreply = msgrAutoreplyAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(msgrAutoreply)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(msgrAutoreply);
			dataOutputMap.put("send_msgr_with_auto_reply", data);
		}
		
		ParsedFilter livechatAggregations = searchResult.getAggregations().get("send_via_livechat");
		ParsedLongTerms livechat = livechatAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(livechat)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(livechat);
			dataOutputMap.put("send_via_livechat", data);
		}
		
		ParsedFilter sendEmailAggregations = searchResult.getAggregations().get("send_email_via_inbox");
		ParsedLongTerms sendEmail = sendEmailAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(sendEmail)) {
			MessageCountData data = transformAggregationToResponseObjectByChannel(sendEmail);
			dataOutputMap.put("send_email_via_inbox", data);
		}
		
		response.setAggregations(dataOutputMap);
		response.setAccountId(accountId);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.BIZAPP_SEND_BY_CHANNEL_REPORT, response);
	}

	private SearchResponse getInboxSendMessagesResult(MessengerFilter filters, Integer accountId, String queryTemplate) {
		SearchResponse searchResult;
		String startDate = filters.getStartDate();
		
		LocalDate localDateStartTime = LocalDate.parse(startDate, DateTimeFormatter.ofPattern("MM/dd/yyyy"));
		String startTime = LocalDateTime.of(localDateStartTime, LocalTime.MIN)
				.format(DateTimeFormatter.ofPattern(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
		
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("accountId", accountId);
		messageFilter.put("startDate", startTime);

		log.info("GetInboxSendAutoreplyMessage for accountId : {} and startTime: {}", accountId, startTime);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("filterMessage", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(queryTemplate, dataModel).build();
		searchResult = esSearchService.getSearchResult(esRequest);
		return searchResult;
	}
	
	private MessageCountData transformAggregationToResponseObjectByChannel(ParsedLongTerms termsAggregation) {
		MessageCountData data  = new MessageCountData();
		if (Objects.nonNull(termsAggregation)) {
			List<? extends Terms.Bucket> buckets = termsAggregation.getBuckets();
			if (CollectionUtils.isNotEmpty(buckets)) {
				data.setDoc_count(buckets.get(0).getDocCount());
			}
		}
		return data;
	}
	
	@Override
	public void getSendAutoreplyReport(MessengerFilter filters) {
		Integer accountId = filters.getAccountId();
		SearchResponse searchResult = null;
		if (accountId != null) {
			String queryTemplate = Constants.Elastic.BIZAPP_SEND_AUTOREPLY;
			searchResult = getInboxSendMessagesResult(filters, accountId, queryTemplate);
			if (searchResult.status().getStatus() != 200) {
				log.error("GetInboxSendMessage failed");
				return;
			}
		}
		
		BizAppMessageCountOutput response = new BizAppMessageCountOutput();
		Map<String,MessageCountData> dataOutputMap = new HashMap<>();
		ParsedFilter webchatAggregations = searchResult.getAggregations().get("received_via_webchat");
		ParsedLongTerms webchat = webchatAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(webchat)) {
            MessageCountData data = transformAggregationToResponseObject(webchat);
            dataOutputMap.put("received_via_webchat", data);
		}

		ParsedFilter autoreplyAggregations = searchResult.getAggregations().get("send_msgr_with_auto_reply");
		ParsedLongTerms autoreply = autoreplyAggregations.getAggregations().get("e_group");
		if (Objects.nonNull(autoreply)) {
			MessageCountData data = transformAggregationToResponseObject(webchat);
            dataOutputMap.put("send_msgr_with_auto_reply", data);
		}
		response.setAggregations(dataOutputMap);
		response.setAccountId(accountId);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.BIZAPP_SEND_AUTOREPLY_REPORT, response);
	}

	private MessageCountData transformAggregationToResponseObject(ParsedLongTerms termsAggregation) {
		List<? extends Terms.Bucket> buckets = termsAggregation.getBuckets();
		ParsedCardinality convCount = buckets.get(0).getAggregations().get("unique_cid");;
		MessageCountData data  = new MessageCountData();
		data.setDoc_count(buckets.get(0).getDocCount());
		
		UniqueInvoiceid UII = new UniqueInvoiceid(convCount.getValue());
		BucketsData bucketsData = new BucketsData(UII);
		List<BucketsData> bucketsDataList = new ArrayList<>();
		bucketsDataList.add(bucketsData);
		
		data.setE_group(new EGroup(bucketsDataList));
		return data;
	}

	@Override
	public MatrixRoiReport getMatrixRoiReport(MessengerFilter filters) {
		List<Integer> businessIds = filters.getBusinessIds(); //accountIds
		String startDate = filters.getStartDate();
		String endDate = filters.getEndDate();
		
		MatrixRoiReport responseObject = new MatrixRoiReport();
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
		messageFilter.put("startDate", startDate);
		messageFilter.put("endDate", endDate);
		log.info("getMatrixRoiReport for startDate: {} endDate: {}", startDate, endDate);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("filterMessage", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_MATRIX_ROI_REPORT, dataModel).build();
		SearchResponse searchResponse = esSearchService.getSearchResult(esRequest);
		ParsedFilter webchatAggregations = searchResponse.getAggregations().get("webchat_received");
		ParsedLongTerms wcTerms = webchatAggregations.getAggregations().get("total_count");
		List<CountWithBusiness> statsResponseObject = new ArrayList<>();
		CountWithBusiness stats = new CountWithBusiness();
		Map<String, Long> output = new HashMap<>();
		for (Integer bId : businessIds) {
			Terms.Bucket elasticBucket = wcTerms.getBucketByKey(String.valueOf(bId));
			if ( elasticBucket == null)
				continue;
			stats.setBusinessId(bId);
			output.put("webchat_received", elasticBucket.getDocCount());
			stats.setSendStats(output);
		}
		ParsedFilter messengerSentAggregations = searchResponse.getAggregations().get("messenger_sent");
		ParsedLongTerms messengerTerms = messengerSentAggregations.getAggregations().get("total_count");
		for (Integer bId : businessIds) {
			Terms.Bucket elasticBucket = messengerTerms.getBucketByKey(String.valueOf(bId));
			if ( elasticBucket == null)
				continue;
			stats.setBusinessId(bId);
			output.put("messenger_sent", elasticBucket.getDocCount());
			stats.setSendStats(output);
		}
		statsResponseObject.add(stats);
		responseObject.setRoiStats(statsResponseObject);
		return responseObject;
	}

	@Override
	public BizAppGetAllMessagesResponse getAllMessages(ExportInboxRequest filters) {
		SearchResponse searchResponse = null;
		String scrollId = filters.getScrollId();
		if (StringUtils.isNotBlank(scrollId) && "START".equals(scrollId)) {
			searchResponse = getMessagesResult(filters);
		} else {
			log.info("getAllMessages: scrollId : {} ", scrollId);
			searchResponse = esSearchService.readMoreFromSearch(scrollId, filters.getSessionTime().trim());
			if (searchResponse!=null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getMessagesResult(filters);
			}
		}
		List<MessageDocument> inboxMessages = null;
		if (java.util.Objects.nonNull(searchResponse)) {
			inboxMessages = getMessagesListFromResponse(searchResponse);
		}
		BizAppGetAllMessagesResponse response = new BizAppGetAllMessagesResponse();
		response.setScrollId(searchResponse.getScrollId());
		response.setMessages(inboxMessages);
		return response;
	}

	private SearchResponse getMessagesResult(ExportInboxRequest filters) {
		MessengerFilterForExportInbox messengerFilter = createFilterForEs(filters);
		messengerFilter.setQueryFile(Constants.Elastic.BIZAPP_GET_ALL_MESSAGES);
		return messengerContactService.getAllMessageDataForExport(messengerFilter);
	}
	
	private MessengerFilterForExportInbox createFilterForEs(ExportInboxRequest request) {
		MessengerFilterForExportInbox messengerFilter = new MessengerFilterForExportInbox();
		messengerFilter.setSize(request.getSize());
		messengerFilter.setStartDate(request.getStartDate());
		messengerFilter.setEndDate(request.getEndDate());
		if (request.getSessionTime() != null)
			messengerFilter.setSessionTime(request.getSessionTime().trim());
		return messengerFilter;
	}
	
	private List<MessageDocument> getMessagesListFromResponse(SearchResponse searchResponse) {
		List<MessageDocument> msgList = new ArrayList<>();
		if(Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();
			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> msgList
						.add(objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class)));
			}
		}
		return msgList;
	}
	
	@Override
	public BizAppGetAllContactsResponse getAllContacts(ExportInboxRequest filters) {
		SearchResponse searchResponse = null;
		String scrollId = filters.getScrollId();
		if (StringUtils.isNotBlank(scrollId) && "START".equals(scrollId)) {
			searchResponse = getContactsResult(filters);
		} else {
			log.info("getAllContacts: scrollId : {} ", scrollId);
			searchResponse = esSearchService.readMoreFromSearch(scrollId, filters.getSessionTime().trim());
			if (searchResponse!=null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getContactsResult(filters);
			}
		}
		List<ContactDocument> inboxContacts = null;
		if (java.util.Objects.nonNull(searchResponse)) {
			inboxContacts = getContactsListFromResponse(searchResponse);
		}
		BizAppGetAllContactsResponse response = new BizAppGetAllContactsResponse();
		response.setScrollId(searchResponse.getScrollId());
		response.setContacts(inboxContacts);
		return response;
	}

	private SearchResponse getContactsResult(ExportInboxRequest filters) {
		MessengerFilterForExportInbox messengerFilter = createFilterForEs(filters);
		messengerFilter.setQueryFile(Constants.Elastic.BIZAPP_GET_ALL_CONTACTS);
		return messengerContactService.getAllContactsDataForExport(messengerFilter);
	}
	
	private List<ContactDocument> getContactsListFromResponse(SearchResponse searchResponse) {
		List<ContactDocument> contactsList = new ArrayList<>();
		if(Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();
			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> contactsList
						.add(objectMapper.convertValue(hit.getSourceAsMap(), ContactDocument.class)));
			}
		}
		return contactsList;
	}

	@Override
	public Map<String, Object> getRoiReportForInbox(ExternalReportFilter filter) {
		validateRequest(filter);
		Integer accountId = filter.getEnterpriseId();
		List<Integer> businessIds = filter.getBusinessIds(); //locationIds
		long startDate = filter.getStartDate().getTime();
		long endDate = filter.getEndDate().getTime();

		//Active Conversation Count
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("accountId", accountId);
		messageFilter.put("businessId", ControllerUtil.toCommaSeparatedString(businessIds));
		messageFilter.put("startTime", startDate);
		messageFilter.put("endTime", endDate);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("data", messageFilter);
		ESRequest esRequestAC = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_ACTIVE_CONVERSATION_ROI, dataModel)
				.addRoutingId(accountId).build();
		SearchResponse searchResponseActiveCon = esSearchService.getSearchResult(esRequestAC);
		Map<String, Object> response = new HashMap<>();
		Aggregations activeConAggregations = searchResponseActiveCon.getAggregations();
		ParsedCardinality activeConvCount= activeConAggregations.get("active_conv_count");
		response.put("activeConversationCount", activeConvCount.getValue());


		ESRequest esRequestMRT = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_MEDIAN_RESPONSE_TIME_ROI, dataModel)
				.addRoutingId(accountId).build();
		SearchResponse searchResponseMRT = esSearchService.getSearchResult(esRequestMRT);
		Aggregations resTimeAggregations = searchResponseMRT.getAggregations();
		Range withinDateRange = resTimeAggregations.get("median_response_time");
		if (Objects.nonNull(withinDateRange)) {
			List<? extends Range.Bucket> oneMonthBucket = withinDateRange.getBuckets();
			oneMonthBucket.stream().findAny().ifPresent(bucket -> {
				ParsedTDigestPercentiles percentilesAggregation = bucket
						.getAggregations().get("mrt");
				Double aDouble = percentilesAggregation.percentile(50d);
				Double thisMonthTotal;
				Double formattedTime = null;
				if (aDouble != null && !Double.isNaN(aDouble)) {
					thisMonthTotal = aDouble / Constants.MStoMin;
					formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
				}
				response.put("medianResponseTime", formattedTime);
			});
		}
		return response;
	}

	private void validateRequest(ExternalReportFilter filter) {
		Assert.notNull(filter.getEnterpriseId(), "Account Id must be present");
		Assert.notNull(filter.getBusinessIds(), "Business Id must be present");
		Assert.notNull(filter.getStartDate(), "StartDate must be present");
		Assert.notNull(filter.getEndDate(), "EndDate must be present");
	}

	@Override
	public Map<String, Object> getRoiReportForWebchat(ExternalReportFilter filter) {
		validateRequest(filter);
		Integer accountId = filter.getEnterpriseId();
		List<Integer> businessIds = filter.getBusinessIds(); //locationIds
		long startDate = filter.getStartDate().getTime();
		long endDate = filter.getEndDate().getTime();
		
		BusinessDTO business = businessService.getBusinessLiteDTO(filter.getBusinessIds().get(0));
		boolean installationStatus = false;
		//Active Conversation Count
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("accountId", accountId);
		messageFilter.put("businessId", ControllerUtil.toCommaSeparatedString(businessIds));
		messageFilter.put("startTime", startDate);
		messageFilter.put("endTime", endDate);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("data", messageFilter);
		ESRequest esRequestAC = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_WEBCHAT_CONVERSATIONS_ROI, dataModel)
				.addRoutingId(accountId).build();
		SearchResponse searchResponseActiveCon = esSearchService.getSearchResult(esRequestAC);
		Map<String, Object> response = new HashMap<>();
		Aggregations activeConAggregations = searchResponseActiveCon.getAggregations();
		ParsedCardinality activeConvCount= activeConAggregations.get("webchat_conv_count");
		response.put("webchatConversationCount", activeConvCount.getValue());
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService.getBusinessChatWidgetConfig(business);
		if (businessChatWidgetConfig!=null && businessChatWidgetConfig.getInstalled()!=null && businessChatWidgetConfig.getInstalled()==1) {
			installationStatus = true;
		}
		response.put("installationStatus", installationStatus);
		return response;
	}

	@Override
	public SmsCommunicationResponse getSmsMessagesCount(SmsCommunicationRequest request){
		String source = String.valueOf(CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class).getProperty("sms_communication_paths",
						"0,1,5,101"));
		Map<String, Object> templateData = new HashMap<>();
		templateData.put("e_id", request.getEnterpriseId().toString());
		templateData.put("gte", request.getFromTime().toString());
		templateData.put("lte", request.getToTime().toString());
		templateData.put("source",source);
		Map<String, Object> data = new HashMap<>();
		data.put("data", templateData);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(request.getEnterpriseId())
				.addTemplateAndDataModel(Constants.Elastic.GET_SMS_SEND_MESSAGES_FOR_ACCOUNT_IN_TIME_FRAME, data)
				.addSize(0)
				.build();
		SearchResponse searchResponse = esSearchService.getSearchResult(esRequest);
		SmsCommunicationResponse response = new SmsCommunicationResponse();
		if (searchResponse.status().getStatus() != 200 || (int) searchResponse.getHits().getTotalHits().value == 0) return response;
		response.setMessagesPresent(true);
		response.setCount((int) searchResponse.getHits().getTotalHits().value);
		return response;
	}

	@Override
	public Object getReserveWGoogleReport(ExternalReportFilter filter) {
		ReserveWGoogleResponseReport responseReport = new ReserveWGoogleResponseReport();
		Map<String, Object> dataModel = new HashMap<>();
		Map<String, Object> messageFilter = new HashMap<>();
		if (filter.getStartDate() != null && filter.getEndDate() != null) {
			long startTime = filter.getStartDate().getTime();
			long endTime = filter.getEndDate().getTime();
			messageFilter.put("startDate", startTime);
			messageFilter.put("endDate", endTime);

			log.info("getReserveWGoogleReport for startTime: {} endTime: {}", startTime, endTime);
		}
		dataModel.put("data", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_RESERVE_W_GOOGLE, dataModel).build();
		SearchResponse searchResponse = esSearchService.getSearchResult(esRequest);
		List<MessageDocument> messageDocumentsReserveWgoogle = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();
			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> messageDocumentsReserveWgoogle
						.add(objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class)));
			}
		}
		
		List<String> mcIds = messageDocumentsReserveWgoogle.stream().map(messageDocument -> messageDocument.getC_id()).collect(Collectors.toList());
		Set<Integer> accountIds = messageDocumentsReserveWgoogle.stream().map(messageDocument -> messageDocument.getE_id()).collect(Collectors.toSet());
		Set<Integer> businessIds = messageDocumentsReserveWgoogle.stream().map(messageDocument -> messageDocument.getB_id()).collect(Collectors.toSet());
		
		Map<Integer, BizLite> accountBizLite = businessService.getBizLite(new ArrayList<>(accountIds), null);
		Map<Integer, BizLite> locationBizLite = businessService.getBizLite(new ArrayList<>(businessIds), null);
		
		dataModel = new HashMap<>();;
		dataModel.put("conversationIds", ControllerUtil.toCommaSeparatedString(mcIds));
		ESRequest esRequest2 = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_LAST_RESPONSE_MSG_RESERVE_W_GOOGLE, dataModel).build();
		List<MessageDocument> messageDocumentsLastResponse = new ArrayList<>();
		try {
			messageDocumentsLastResponse = getAggregatedMessageDocument(esRequest2);
		} catch (Exception e) {
			log.error("Error in serializing aggregation response to MessageDocument");
			return null;
		}
		Map<Integer, Integer> bIdCountMapForResponse = new HashMap<>();
		Set<Long> appointmentIds = new HashSet<>();
		for (MessageDocument reservewGoogle : messageDocumentsReserveWgoogle) {
			if (appointmentIds.contains(reservewGoogle.getAppointmentInfo().getAppointmentId())) {
			    continue;
			} else {
				appointmentIds.add(reservewGoogle.getAppointmentInfo().getAppointmentId());
				for (MessageDocument lastResponse : messageDocumentsLastResponse) {
					if (reservewGoogle.getC_id().equals(lastResponse.getC_id())
							&& reservewGoogle.getCr_time() < lastResponse.getCr_time()) {
						bIdCountMapForResponse.compute(reservewGoogle.getB_id(), (k, v) -> (v == null) ? 1 : v + 1);
						break;
					}
				}
			}
		}
		
		List<ReserveWGoogleResponse> result = new ArrayList<>();
		ParsedLongTerms eGroupTerms = searchResponse.getAggregations().get("e_group");
		for (Terms.Bucket eGroupBucket : eGroupTerms.getBuckets()) {
			String eGroupKey = eGroupBucket.getKeyAsString();
			ParsedLongTerms bGroupTerms = eGroupBucket.getAggregations().get("b_group");
			for (Terms.Bucket bGroupBucket : bGroupTerms.getBuckets()) {
				String bGroupKey = bGroupBucket.getKeyAsString();
				ParsedCardinality appCount = bGroupBucket.getAggregations().get("unique_appointments");
				long docCount = appCount != null ? appCount.getValue():0l;
				long responseCount = bIdCountMapForResponse.get(Integer.valueOf(bGroupKey))!= null ? bIdCountMapForResponse.get(Integer.valueOf(bGroupKey)) : 0;
				BizLite accountDetail = accountBizLite.get(Integer.valueOf(eGroupKey));
				BizLite locationDetail = locationBizLite.get(Integer.valueOf(bGroupKey));
				long accNum = Objects.nonNull(accountDetail) && Objects.nonNull(accountDetail.getBusinessNumber()) ? accountDetail.getBusinessNumber() : null;
				long bussNum = Objects.nonNull(locationDetail) && Objects.nonNull(locationDetail.getBusinessNumber()) ? locationDetail.getBusinessNumber() : null;
				String accName = Objects.nonNull(accountDetail) && Objects.nonNull(accountDetail.getName()) ? accountDetail.getName() : accountDetail.getAlias1();
				String busiName = Objects.nonNull(locationDetail) && Objects.nonNull(locationDetail.getAlias1()) ? locationDetail.getAlias1() : locationDetail.getName();
				result.add(new ReserveWGoogleResponse(accNum, bussNum, accName, busiName, docCount, responseCount));
			}
		}
		responseReport.setData(result);
		return responseReport;
	}
	
	private List<MessageDocument> getAggregatedMessageDocument(ESRequest esRequest)
			throws Exception {
		SearchResponse response = esSearchService.getSearchResult(esRequest);
		String jsonResponse = response.toString();
		ObjectMapper objectMapper = new ObjectMapper();
		JsonNode rootNode = objectMapper.readTree(jsonResponse);
		List<MessageDocument> messageDocuments = new ArrayList<>();
		if (rootNode.has("aggregations") && rootNode.get("aggregations").has("sterms#conversations_last_response")
				&& rootNode.get("aggregations").get("sterms#conversations_last_response").has("buckets")
				&& rootNode.get("aggregations").get("sterms#conversations_last_response").get("buckets").isArray() ) {
			JsonNode bucketsArray = rootNode.get("aggregations").get("sterms#conversations_last_response").get("buckets");

			// Iterate over the array elements
			for (JsonNode bucketNode : bucketsArray) {
				System.out.println(bucketNode.get("top_hits#top_hit").get("hits").get("hits").get(0).get("_source"));
				MessageDocument messageDocument = objectMapper.convertValue(bucketNode.get("top_hits#top_hit").get("hits").get("hits").get(0).get("_source"), MessageDocument.class);
				messageDocuments.add(messageDocument);
			}
		}
		return messageDocuments;
	}
}
