package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.UserDetail;
import com.birdeye.messenger.dto.secure.messaging.GetSecureMessagesResponse.SecureMessageResponse;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.ReceptionistService;
import com.birdeye.messenger.sro.ReviewEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class FirebaseServiceImpl implements FirebaseService {

    // Old mobile firebase db sync
    private static final String MOBILE_DB_NAME = "webchat"; // ConversationDetails

    // New db for mobile & live chat
    private static final String CONVERSATION_DB_NAME = "conversation";

    private static final String WEBCHAT_SESSION = "webchat-session";

    private static final String BULK_ACTION_DB_NAME = "inbox-bulk-action";
    
    private static final String TIDY_UP_NODE_NAME = "tidy-up";

    private final BusinessService businessService;

    // New object similar to sendResponse
    private final KafkaService kafkaService;

    private final ReceptionistService receptionistService;

    private final CommunicationHelperService communicationHelperService;

    @Value("${secure.messaging.base.firebase.bucket}")
    private String secureMessagingBaseFirebaseBucket;
    // TODO: PIYUSH in case of multiple messages (attachment case), we need to pass
    // list of message documents.

    @Override
    @Async
    public void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, boolean notification) {
        pushToFireBase(contactDocument, messageDocument, null, null, notification);
    }

    @Override
    @Async
    public void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId, MessageDocument lastMessage) {
    	pushToFireBase(contactDocument, messageDocument, loggedInUserId, lastMessage, true);
    }
    
    @Override
    public void pushToFireBaseSync(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId, MessageDocument lastMessage,boolean notification) {
    	 log.debug("pushFireBaseNotification DEBUG: {}  ----------- {}", messageDocument, contactDocument);
    	 MessageDocument messageDocumentNotification = null;
    	 MessageDocument lastMessageNotification = null;
    	 
    	 if (messageDocument != null) {
    		 try {
    			 messageDocumentNotification = (MessageDocument) messageDocument.clone();
    		 } catch (Exception e) {
    			 log.error("Exception while cloning messageDocument:{}", e);
    			 throw new MessengerException(ErrorCode.ERROR_WHILE_CLONING);
    		 }
    		 messageDocumentNotification.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
    		 messageDocumentNotification.setIs_encrypt(0);
    	 }
         if (lastMessage != null) {
        	 try {
        		 lastMessageNotification = (MessageDocument) lastMessage.clone();
        	 } catch (Exception e) {
        		 log.error("Exception while cloning lastMessage:{}", e);
        		 throw new MessengerException(ErrorCode.ERROR_WHILE_CLONING);
        	 }
        	 lastMessageNotification.setMsg_body(MessengerUtil.decryptMessage(lastMessage));
        	 lastMessageNotification.setIs_encrypt(0);
         }
         BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
         // Some cases would require conditional logic (other than message type) to
         // suppress push/chrome notifications
         // In case chatbot replies for incoming messages, we dont want push
         // notifications.
         if (notification) {
             pushFCMNotificationForMobile(contactDocument, messageDocumentNotification, businessDTO, loggedInUserId, lastMessageNotification);
             pushFCMNotificationForWEB(contactDocument, messageDocumentNotification, businessDTO, loggedInUserId, lastMessageNotification,null);
         }
         // Mirroring is not optional for any case.
         mirrorOnWeb(DtoToEntityConverter.fromContactDoc(contactDocument));
         mirrorOnMobile(contactDocument, messageDocumentNotification);
     }

    @Override
    @Async
    public void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId, MessageDocument lastMessage, boolean notification) {
        log.debug("pushFireBaseNotification DEBUG: {}  ----------- {}", messageDocument, contactDocument);

        if (messageDocument != null) {
            messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
            messageDocument.setIs_encrypt(0);
        }
        if (lastMessage != null) {
            lastMessage.setMsg_body(MessengerUtil.decryptMessage(lastMessage));
            lastMessage.setIs_encrypt(0);
        }
        BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
        // Some cases would require conditional logic (other than message type) to
        // suppress push/chrome notifications
        // In case chatbot replies for incoming messages, we dont want push
        // notifications.

        if (notification ) {
            pushFCMNotificationForMobile(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage);
            pushFCMNotificationForWEB(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage,null);
        }
        // Mirroring is not optional for any case.
        //mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
        mirrorOnSecureChat(contactDocument, messageDocument);
        mirrorOnWeb(DtoToEntityConverter.fromContactDoc(contactDocument));
        mirrorOnMobile(contactDocument, messageDocument);
    }

    /**
     * Firebase DB SYNC will happen 1. Old mobile db - "webchat" - existing payload.
     * ConversationDetails 2. New Mobile db - "conversations" - New payload similar
     * to SendResponse. It will also be used by LIVECHAT consumer.
     */
    @Override
    public void mirrorOnMobile(ContactDocument contactDocument, MessageDocument messageDocument) {
        BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
        if (StringUtils.isBlank(businessDTO.getTimeZoneId())) {
            businessDTO.setTimeZoneId("PST");
        }
        // Old mobile only messages
        if (messageDocument!=null && messageDocument.getMessageType()!=null && messageDocument.getMessageType().equals(MessageType.CHAT)) {
            try {
                ConversationDetails conversationDetails =
                    new ConversationDetails(contactDocument, messageDocument, businessDTO);
                String topicName = StringUtils.join(MOBILE_DB_NAME
                    + "/", String.valueOf(businessDTO.getBusinessId()), "/", String.valueOf(contactDocument.getM_c_id()));
                log.info("MOBILE-OLD-TOPIC-FIREBASE : [ {} ]", topicName);
                FirebaseDatabaseInsertOrUpdateRequest<ConversationDetails> request =
                    new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, conversationDetails);
                kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
            } catch (Exception e) {
            	log.error("[MirroringMobile] Error occured while mirroring for old apps",e);
            }
        }
        // New Mobile & LIVECHAT
        List<MessageDocument> messageDocs = messageDocument!=null?Collections.singletonList(messageDocument):new LinkedList<MessageDocument>();
        SendResponse sendResponse =
            new SendResponse(messageDocs, contactDocument, businessDTO.getTimeZoneId());
        setViewedByForFirebase(contactDocument, businessDTO, sendResponse);
        String topicName = StringUtils.join(CONVERSATION_DB_NAME
            + "/", String.valueOf(businessDTO.getBusinessId()), "/", String.valueOf(contactDocument.getM_c_id()));
        log.info("MOBILE-NEW-TOPIC-FIREBASE : [ {} ]", topicName);

        log.info("### Send Response LIVECHAT : {}", JSONUtils.toJSON(sendResponse));
        FirebaseDatabaseInsertOrUpdateRequest<SendResponse> request =
            new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, sendResponse);
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, businessDTO.getRoutingId(), request);
    }
    
    @Override
    public void pushToFirebase(ContactDocument contactDocument, SuggestionHolder suggestionHolder,Boolean isReceivedDuringBusinessHours,Boolean showTimer) {
        BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
        if (StringUtils.isBlank(businessDTO.getTimeZoneId())) {
            businessDTO.setTimeZoneId("PST");
        }
        // New Mobile & LIVECHAT
		if (Objects.nonNull(suggestionHolder)) {
			Date sentOn = new Date();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			String sentOnTime = df.format(sentOn);
			suggestionHolder.setSentOn(sentOnTime);
			suggestionHolder.setCreatedBy(getUserDetail(-10));
		}
        SendResponse sendResponse =
            new SendResponse(null, contactDocument, businessDTO.getTimeZoneId());
        sendResponse.setSuggestionHolder(suggestionHolder);
        sendResponse.setIsReceivedDuringBusinessHours(isReceivedDuringBusinessHours);
        sendResponse.setShowTimer(showTimer);
        String topicName = StringUtils.join(CONVERSATION_DB_NAME
            + "/", String.valueOf(businessDTO.getBusinessId()), "/", String.valueOf(contactDocument.getM_c_id()));
        log.info("MOBILE-NEW-TOPIC-FIREBASE : [ {} ]", topicName);

        log.info("### Send Response LIVECHAT : {}", JSONUtils.toJSON(sendResponse));
        FirebaseDatabaseInsertOrUpdateRequest<SendResponse> request =
            new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, sendResponse);
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, businessDTO.getRoutingId(), request);
    }

	private void setViewedByForFirebase(ContactDocument contactDocument, BusinessDTO businessDTO,
			SendResponse sendResponse) {
		List<Integer> viewedBy  = contactDocument.getViewedBy();
        Map<Integer, String> viewedBySendResponse = new HashMap<>();
        if(CollectionUtils.isNotEmpty(viewedBy)) {
        	Map<Integer, TeamAssigneeDto> userDetails = communicationHelperService.getValidUserDTOs(viewedBy.get(0),
     				businessDTO.getRoutingId(), businessDTO.getBusinessId());
     		viewedBy.forEach(id -> {
     			if (Objects.nonNull(userDetails.get(id))) {
     				TeamAssigneeDto userDTO = userDetails.get(id);
					String domain = StringUtils.isNotBlank(userDTO.getEmailId())
     						? userDTO.getEmailId().substring(userDTO.getEmailId().indexOf("@") + 1)
     						: null;
     				if (StringUtils.isNotBlank(domain) && !domain.contains("birdeye")) {
     					viewedBySendResponse.put(userDTO.getValue(), userDTO.getLabel());
     				}
     			}
     		});
     		LastMessageMetaData lastMessageMetaData = contactDocument.getLastMessageMetaData();
     		if (null != lastMessageMetaData && null != lastMessageMetaData.getLastMessageUserId()
    				&& !"RECEIVE".equalsIgnoreCase(lastMessageMetaData.getLastMessageType())) {
        		viewedBySendResponse.remove(lastMessageMetaData.getLastMessageUserId());
    		}
        }
        sendResponse.setViewedBy(viewedBySendResponse);
	}
    
    @Override
    public void mirrorOnMobile(ContactDocument conversation) {
    	Assert.notNull(conversation, "contactDocument shouldn't be null");
    	mirrorOnMobile(conversation, null);
        mirrorOnSecureChat(conversation, null);
    }

    /**
     * publish 'typing' event to firebase event has two states (typing = true/false)
     */
    @Override
    public void pushTypingEventToFirebase(ConversationStateDTO event) {
        String topicName = StringUtils.join(WEBCHAT_SESSION
            + "/", String.valueOf(event.getBusinessId()), "/", String.valueOf(event.getMc_id()));
        log.info("Publishing typing event to Topic {} and typingEvent {}", topicName, event.isCustomerTyping());
        PresenceResponse presenceEvent =
            new PresenceResponse(event.getMc_id(), event.getBusinessId(), event.getSentAt(), event.getTypingUsers(), event.isCustomerTyping(), event.getState());
        FirebaseDatabaseInsertOrUpdateRequest<PresenceResponse> request =
            new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, presenceEvent);
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
    }

    /**
     * Based on event and current conversation context, following are rules
     * <p>
     * <p>
     * 1. NEW RECEIVE MESSAGE - a) If Conversation is unassigned, Account ID for WEB
     * & Location ID for Mobile. b) If Conversation is assigned, User ID for WEB &
     * Mobile 2. Add NOTE a) If Conversation is unassigned, Account ID for WEB &
     * Location ID for Mobile b) If Conversation is assigned, User ID for WEB &
     * Mobile 3. ASSIGNED TO a) If Conversation is assigned, User ID for WEB &
     * Mobile. WHOM conversation is being assigned.
     * <p>
     * "messenger-<locationid>" topic would be used for unassigned conversations
     * <p>
     * "messenger-user-<userid>" topic would be used for assigned conversations
     * <p>
     * NOTE: Payload for WEB & Mobile are different
     */

    private void pushFCMNotificationForMobile(ContactDocument contactDocument, MessageDocument messageDocument, BusinessDTO businessDTO, Integer loggedInUserId, MessageDocument lasMessage) {
		try {
			//Adding this check for supressing push notification for non-texting locations on mobile
			String textingNumber = businessService.getBusinessSMSNumber(businessDTO.getBusinessId());
			if (StringUtils.isAllEmpty(textingNumber)) {
				log.info("Push notification not applicable for mobile. No texting number found : {}", businessDTO.getBusinessId());
				return;
			}
			AppUserNotificationSetting appUserNotificationSetting = businessService
					.getAppUsersNotificationSettings(businessDTO.getBusinessId());
			if (Objects.nonNull(appUserNotificationSetting) && CollectionUtils.isNotEmpty(appUserNotificationSetting.getUsers())) {
				List<String> fbTopics=getAllUserTopicsForMobilePushNotification(contactDocument, messageDocument, appUserNotificationSetting, businessDTO);
				if (CollectionUtils.isNotEmpty(fbTopics)) {
					MessageType type = messageDocument.getMessageType();
					ConversationDetails conversationDetails = new ConversationDetails(contactDocument, messageDocument,
							businessDTO);
					conversationDetails.setType("messenger");
					Optional<Notification> notificationDataOptional = getNotificationData(type, messageDocument,
							contactDocument, loggedInUserId, lasMessage, null);

					if (!notificationDataOptional.isPresent()) {
						return;
					}

					// Firebase call via nexus. Nexus adds "messenger-" prefix in topic name
					// TODO : SEND CALL kafkaService in BULK
					Integer accountId = businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId()
							: businessDTO.getBusinessId();
					Map<String, Object> customData = new HashMap<>();
					customData.put("data", JSONUtils.toJSON(conversationDetails));
					customData.put("businessId", businessDTO.getBusinessId());
					customData.put("type", "messenger");
					customData.put("userId", loggedInUserId);
					customData.put("timestamp", new Date().getTime());
					customData.put("enterpriseId", accountId);
                    FCMNotificationMetaData fcmNotificationMetaData = new FCMNotificationMetaData(messageDocument.getM_id(), Instant.now().toEpochMilli(),businessDTO.getBusinessId(),"messenger");
                    FCMNotificationConfig fcmNotificationConfig = new FCMNotificationConfig();
                    List<List<String>> topicsList=ListUtils.partition(fbTopics,Constants.MOBILE_FCM_NOTIFICATIONS_BATCH_SIZE);
					for (List<String> topics:topicsList) {
						FCMNotificationMessageInBulk mobileNotification = new FCMNotificationMessageInBulk(topics,
								notificationDataOptional.get(), customData);
                        FCMNotificationInBulkDto fcmNotificationMobile = new FCMNotificationInBulkDto(mobileNotification,fcmNotificationMetaData,fcmNotificationConfig);
                        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.FCM_NOTIFICATION_MOBILE, null,
                                fcmNotificationMobile);
						log.debug("pushMobileNotification : for business {} ", businessDTO.getBusinessId());
					}
					
				}else {
					log.info("pushMobileNotification: No topics found for business {} ", businessDTO.getBusinessId());
				}
			}

		} catch (Exception e) {
            log.error("pushMobileNotification: Error while sending Firebase notification business {} : ", businessDTO.getBusinessId(), e);
        }

        // New Mobile. Ashutosh will confirm if we need separate topic for old mobile.
    }

    private List<String> getAllUserTopicsForMobilePushNotification(ContactDocument contactDocument, MessageDocument messageDocument,
    		AppUserNotificationSetting appUserNotificationSetting, BusinessDTO businessDTO) {
    	Integer convAssigneeId = contactDocument.getCr_asgn_id();
    	String assignmentType = contactDocument.getAssignmentType();
    	boolean isConversationAssignToTeam = conversationAssignToTeam(contactDocument);
		boolean isConversationAssignToUser = conversationAssignToUser(contactDocument);
		List<Integer> allUsersOfTeams=new ArrayList<>();
		if (isConversationAssignToTeam) {
			allUsersOfTeams = businessService.getAllUsersOfTeams(contactDocument.getTeam_id(),
					businessDTO.getBusinessId());
		} 
		MessageType messageType = messageDocument.getMessageType();
    	List<String> topics=new ArrayList<String>();
    	final List<Integer> finalUsersOfTeams = allUsersOfTeams;
    	appUserNotificationSetting.getUsers().forEach(user->{
    		if(isConversationAssignToTeam) {
    			if(finalUsersOfTeams.contains(user.getUserId()) && user.getInboxNotification().isAssign_to_me()) {
    				topics.add("Messenger-user-" + user.getUserId());
    			}
    		}else if(isConversationAssignToUser) {
    			if(messageType != null && MessageType.CHAT.equals(messageType) &&!user.getUserId().equals(convAssigneeId) && user.getInboxNotification().isAssign_to_others()) {
    				topics.add("Messenger-user-" + user.getUserId());
    			}else if(user.getUserId().equals(convAssigneeId) && user.getInboxNotification().isAssign_to_me()) {
    				topics.add("Messenger-user-" + user.getUserId());
    			}
    		}else {
    			if(Constants.Elastic.UNASSIGNED_ID.equals(convAssigneeId) && "U".equals(assignmentType) && user.getInboxNotification().isUnassign()) {
    				topics.add("Messenger-user-" + user.getUserId());
    			}
    		}
    	});
    	return topics;
	}

	/**
     * Push new message notification alert to firebase realtime DB 1. Push Message
     * to kafka, consume from kafka via NIFI and then nexus
     *
     * @param accountId
     */
    @Override
    @Deprecated
    public void mirrorOnWeb(Integer accountId, Integer businessId) {
        // TODO - There should be a re-trial mechanism for realTime SYNC
        try {
            String topicName = StringUtils.join("messenger/", String.valueOf(accountId));
            log.debug("WEB-TOPIC-FIREBASE : [ {} ]", topicName);
            Map<String, Object> data = new HashMap<>();
            data.put(businessId + "|_t", new Date().getTime());
            FirebaseDatabaseInsertOrUpdateRequest<Map<String, Object>> request =
                new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, data);
            kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
        } catch (Exception e) {
            log.error("Exception while mirrorOnWeb response for business id ::"
                + accountId, e);
        }
    }
    
    @Override
    public void twinOnWeb(Integer accountId, Integer businessId, Integer mcId) {
        FirebaseDto firebaseDto = new FirebaseDto();
        firebaseDto.setAccountId(accountId);
        firebaseDto.setBusinessId(businessId);
        firebaseDto.setMcId(mcId);
        mirrorOnWeb(firebaseDto);
    }
    
    @Override
    public void mirrorOnWeb(FirebaseDto firebaseDto) {
        try {
            String topicName = StringUtils.join("messenger/", String.valueOf(firebaseDto.getAccountId()));
            String inboxPerformanceImprovementEnabledAccount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("accounts_with_inbox_performance_improvement", "261968");
            List<String> inboxPerformanceImprovementEnabledAccountList = ControllerUtil.getTokensListFromString(inboxPerformanceImprovementEnabledAccount);
            if (firebaseDto.getBusinessId()!=null && firebaseDto.getMcId()!=null && inboxPerformanceImprovementEnabledAccountList.contains(firebaseDto.getAccountId().toString())) {
            	topicName = StringUtils.join(topicName, "/", String.valueOf(firebaseDto.getBusinessId()), "/", String.valueOf(firebaseDto.getMcId()));
            }
            log.info("WEB-TOPIC-FIREBASE : [ {} ]", topicName);
            FirebaseDatabaseInsertOrUpdateRequest<FirebaseDto> request = new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, firebaseDto);
            kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
        } catch (Exception e) {
            log.error("Exception while mirrorOnWeb response for business id ::" + firebaseDto.getAccountId(), e);
        }
    }

    /*
     * "messenger-web-<accountid>" topic would be used for unassigned conversations
     *
     * "messenger-webuser-<userid>" topic would be used for assigned conversations
     */
    @Override
    public void pushFCMNotificationForWEB(ContactDocument contactDocument, MessageDocument messageDocument, BusinessDTO businessDTO, Integer loggedInUserId, MessageDocument lastMessage, ReviewEvent.Review review) {
        try {
            Integer accountId = businessDTO.getEnterpriseId() == null ?
                businessDTO.getBusinessId() :
                businessDTO.getEnterpriseId();
            List<String> fbTopics = new ArrayList<>();
            Integer assignee = contactDocument.getCr_asgn_id();
            boolean isConversationAssignToTeam = conversationAssignToTeam(contactDocument);
            boolean isConversationAssignToUser = conversationAssignToUser(contactDocument);

            if (isConversationAssignToTeam) {
                log.debug("Push Notification for team");
                log.debug("Exclude login user : {}",loggedInUserId);
                List<Integer> allUsersOfTeams =
                    businessService.getAllUsersOfTeams(contactDocument.getTeam_id(), businessDTO.getBusinessId());
                    allUsersOfTeams.forEach((userId) -> {
                    log.debug("Logged in user : {}",loggedInUserId);
                    if (!userId.equals(loggedInUserId)) {
                        fbTopics.add("Messenger-webuser-" + userId);
                    }
                });
            } else if (isConversationAssignToUser) {
                log.debug("Push Notification for user :{}",assignee);
                fbTopics.add("Messenger-webuser-" + assignee);
            } else {
                log.debug("Push Notification for account :{}",accountId);
                fbTopics.add("Messenger-web-" + accountId);
            }

            MessageType type = messageDocument.getMessageType();
            long starTime = System.currentTimeMillis();
            Optional<Notification> notificationOptional =
                getNotificationData(type, messageDocument, contactDocument, loggedInUserId, lastMessage,review);
            long endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("getNotificationData", starTime, endTime);
            if (!notificationOptional.isPresent()) {
                return;
            }

            Map<String, Object> webNotificationDataMap = new HashMap<>();
            webNotificationDataMap.put("conversationId", contactDocument.getM_c_id());
            webNotificationDataMap.put("isConversationAssigned", (isConversationAssignToTeam || isConversationAssignToUser));
            webNotificationDataMap.put("messageSourceId", messageDocument.getSource());
            webNotificationDataMap.put("businessId", businessDTO.getBusinessId());
            webNotificationDataMap.put("baseUrl", businessService.getWebsiteDomain(businessDTO.getBusinessId()));
            webNotificationDataMap.put("filter", "all");
            webNotificationDataMap.put("type", "messenger"); // Module for desktop notification.
            webNotificationDataMap.put("notification", notificationOptional.get());
            webNotificationDataMap.put("timestamp", new Date().getTime());
            if (Objects.nonNull(review)){
                if (Objects.nonNull(review.getSourceId())){
                    // this will be required to show icon for review source
                    webNotificationDataMap.put("sourceId", review.getSourceId());
                }
                webNotificationDataMap.put("notificationType", "reviews");
            }

            FCMNotificationMessage fcmNotificationMessage = new FCMNotificationMessage();
            fcmNotificationMessage.setData(webNotificationDataMap);
            FCMNotificationMetaData fcmNotificationMetaData = new FCMNotificationMetaData(messageDocument.getId().toString(), Instant.now().toEpochMilli(),businessDTO.getBusinessId(),"messenger");
            FCMNotificationConfig fcmNotificationConfig = new FCMNotificationConfig();
            fbTopics.forEach(perTopic -> {
                fcmNotificationMessage.setTo(perTopic);
                FCMNotificationDto webNotification =
                        new FCMNotificationDto(fcmNotificationMessage,fcmNotificationMetaData,fcmNotificationConfig);
                kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.FIREBASE_NOTIFICATION_PUSH_V1, null, webNotification);
                log.info("pushWebNotification : to {} data {} ", perTopic, webNotification);
            });
        } catch (Exception e) {
            log.error("pushMobileNotification: Error while sending Firebase notification business {} : ", businessDTO.getBusinessId(), e);
        }
    }

    private Optional<Notification> getNotificationData(MessageType messageType, MessageDocument messageDocument, ContactDocument contactDocument, Integer loggedInUserId, MessageDocument lastMessage,ReviewEvent.Review review) {
        switch (messageType) {
            case CHAT:
                if (!MessageDocument.CommunicationDirection.RECEIVE.equals(messageDocument.getCommunicationDirection())) {
                    log.info("Push notification is not applicable for {}", messageDocument.getCommunicationDirection());
                    return Optional.empty();
                }
                break;
            // IF conversation Assignee and Assigned to are different.
            // Only then trigger the notification.
            case ACTIVITY:
                if(ActivityType.REFERRAL_LEAD_GEN.equals(messageDocument.getActivityType())) {
                    String title = "New Referral Lead";
                    String body = StringUtils.isNotBlank(contactDocument.getC_name()) ? contactDocument.getC_name() : "Unnamed";
                    return Optional.of(new Notification(title, body));
                }
                //need to be updated when exact title and body received
                else if(ActivityType.BOOKED.equals(messageDocument.getActivityType())) {
                    //when alias is null then business name
                    String locationAlias = contactDocument.getB_alias() != null ? contactDocument.getB_alias() : contactDocument.getB_name();
                    String date = DateUtils.convertDate(new Date(messageDocument.getAppointmentInfo().getUpdatedAt()));
                    String title = "New appointment booking received on " +locationAlias ;
                    String body = messageDocument.getAppointmentInfo().getServiceName()+ " : " +messageDocument.getAppointmentInfo().getFirstName()+ " on " +date+ " with " + messageDocument.getAppointmentInfo().getSpecialistName();
                    return Optional.of(new Notification(title, body));
                }
                else if (ActivityType.CHAT_LOCATION_TRANSFER.equals(messageDocument.getActivityType())) {
                	String actorName = (Objects.nonNull(messageDocument.getCreatedBy())
                            && StringUtils.isNotEmpty(messageDocument.getCreatedBy().getUserName())) ? messageDocument.getCreatedBy().getUserName() : "Unnamed";
                	StringBuilder sb=new StringBuilder();
                    String title = actorName;
                    sb.append("added a conversation");
                    if(StringUtils.isNotBlank(contactDocument.getC_name())) {
                		sb.append(" with ").append(contactDocument.getC_name());
                	}
                    sb.append(" from ").append(messageDocument.getFromBusinessDetail().getBusinessName())
            		.append(" to ").append(messageDocument.getToBusinessDetail().getBusinessName());
                	String body = sb.toString();
                	body=trimMessageBody(body);
                    return Optional.of(new Notification(title, body));
                }
                else if (messageDocument.getActivityType() != null
                    && messageDocument.getActivityType() != ActivityType.ASSIGNED
                    && messageDocument.getActivityType() != ActivityType.REASSIGNED
                    && messageDocument.getActivityType() != ActivityType.TEAM_ASSIGN) {
                    return Optional.empty();
                } else if (loggedInUserId == null) {
                    return Optional.empty();
                } else if (messageDocument.getTriggeredFor() != null
                    && messageDocument.getTriggeredFor().getUserId().equals(loggedInUserId)) {
                    log.info("[DoNotNotify] Conversation :{} assigned to self,", contactDocument.getM_c_id());
                    return Optional.empty();
                }
                break;
            // IF Actor (Who added the note) and Person(To whom conversation is assigned)
            // are different.
            // Only then trigger the notification.
            case INTERNAL_NOTES:
                if (loggedInUserId == null) {
                    return Optional.empty();
                } else if (loggedInUserId.equals(contactDocument.getCr_asgn_id())) {
                    log.info("Note added to self assigned conversation :{}", contactDocument.getM_c_id());
                    return Optional.empty();
                }
                break;
            case REVIEW:
                break;
            default:
                return Optional.empty();
        }

        boolean isMessageForTeam =
            contactDocument.getAssignmentType().equals("T") && contactDocument.getTeam_id() != null;

        String title = getTitle(contactDocument, messageDocument,messageType,isMessageForTeam,review);
        String messageBody = getBody(contactDocument, messageDocument, messageType,isMessageForTeam, lastMessage,review);

        return Optional.of(new Notification(title, messageBody));
    }

    private String getTitle(ContactDocument contactDocument, MessageDocument messageDocument,MessageType messageType,boolean isMessageForTeam,ReviewEvent.Review review) {
        String title = "New message received";
        switch (messageType) {
            case CHAT:
              if(isMessageForTeam && StringUtils.isNotBlank(contactDocument.getC_name())) {
            	  title =contactDocument.getC_name()+" to "+ contactDocument.getTeam_name();
              }else {
            	  if (StringUtils.isNotBlank(contactDocument.getC_name())) {
                      title =contactDocument.getC_name();
                 }
              }
                break;
            case ACTIVITY:
            	if (Objects.nonNull(messageDocument.getCreatedBy())
                    && StringUtils.isNotBlank(messageDocument.getCreatedBy().getUserName())) {
                    title = messageDocument.getCreatedBy().getUserName();
                } else {
                    title = "Activity assigned to you";
                }
                break;
            case INTERNAL_NOTES:
                if (Objects.nonNull(messageDocument.getCreatedBy())
                    && StringUtils.isNotBlank(messageDocument.getCreatedBy().getUserName())) {
                    title =messageDocument.getCreatedBy().getUserName();
                } else {
                    title = "New note added";
                }
                break;
            case REVIEW:
                if (Objects.nonNull(review) && StringUtils.isNotEmpty(review.getReviewerName())){
                    title = "New review from " + review.getReviewerName();
                }
            default:
                break;
        }
        return title;
    }

    private String getBody(ContactDocument contactDocument, MessageDocument messageDocument, MessageType messageType, boolean isMessageForTeam, MessageDocument lastMessage,ReviewEvent.Review review) {
        String body = "";
        //We are not showing secure message content in push notifications
        if(Source.SECURE_MESSAGE.getSourceId().equals(messageDocument.getSource())){
        	return body;
        }
        switch (messageType) {
            case CHAT:
                if (StringUtils.isNotEmpty(messageDocument.getMsg_body())) {
                    //1. Body for text messages
                    body = messageDocument.getMsg_body();
                    //2. Body for VoiceMail messages with transcription
                    if (StringUtils.isNotEmpty(messageDocument.getVoiceMailUrl())) {
                        body = "Voicemail: " + messageDocument.getMsg_body();
                    }
                } else {
                    //3. Body for VoiceMail messages without transcription
                    if (StringUtils.isNotEmpty(messageDocument.getVoiceMailUrl())) {
                        String cFirstName = StringUtils.isNotEmpty(contactDocument.getC_name()) ?
                            receptionistService.extractFirstNameOnly(contactDocument.getC_name()) :
                            contactDocument.getC_phone();
                        body = cFirstName + " called and left a voicemail";
                    } else {
                        body = "Attachment received";
                    }
                }
                break;
        
                case ACTIVITY:
            	if (lastMessage != null) {
            		StringBuilder sb=new StringBuilder();
            		sb.append("assigned a conversation");
            		if(isMessageForTeam) {
            			if(StringUtils.isNotBlank(contactDocument.getC_name())) {
            				sb.append(" with ").append(contactDocument.getC_name()).append(" to ").append(contactDocument.getTeam_name());
            			}else {
            				sb.append(" to ").append(contactDocument.getTeam_name());
            			}
            		}else {
            			if(StringUtils.isNotBlank(contactDocument.getC_name())) {
            				sb.append(" with ").append(contactDocument.getC_name()).append(" to you");
            			}else {
            				sb.append(" to you");
            			}
            		}
            		body=sb.toString();
            	}
                break;

            case INTERNAL_NOTES:
            	 if (StringUtils.isNotBlank(messageDocument.getMsg_body())) {
            		 StringBuilder sb=new StringBuilder();
            		 sb.append("added an internal note to a conversation");
            		 if(StringUtils.isNotBlank(contactDocument.getC_name())) {
            			 sb.append(" with ").append(contactDocument.getC_name()); 
            		 }
            		 body=sb.toString();
            	 }
                break;

            case REVIEW:
                if (Objects.nonNull(review)) {
                    if (StringUtils.isNotEmpty(review.getComment())) {
                        body = review.getComment();
                    }
                    if (Objects.nonNull(review.getRating())){
                        String rating = "";
                        if (review.getRating() > 1){
                            rating = review.getRating().intValue() +  " stars"  ;
                        }else {
                            rating = review.getRating().intValue() +  " star" ;
                        }
                        if (StringUtils.isNotEmpty(body) && StringUtils.isNotEmpty(rating)){
                            body = rating +" - " + body;
                        }else if (StringUtils.isNotEmpty(rating) && StringUtils.isEmpty(body)){
                            body = rating;
                        }
                    }else{
                        if (Objects.nonNull(review.getRecommended())) {
                            String recommendation = review.getRecommended()? "Recommended.":"Not recommended."  ;
                            body = recommendation + " " + body;
                        }
                    }

                }
                break;
            default:
                break;
        }
        body = trimMessageBody(body);
        return body;
    }

	private String trimMessageBody(String body) {
		if (!StringUtils.isEmpty(body) && body.length() > Constants.NOTIFICATION_BODY_MAX_LENGTH) {
            body = body.substring(0, Constants.NOTIFICATION_BODY_MAX_LENGTH - 3) + "...";
        }
		return body;
	}

	private boolean conversationAssignToUser(ContactDocument contactDocument) {
        if (contactDocument == null)
            return false;
        return StringUtils.isEmpty(contactDocument.getAssignmentType())
            || contactDocument.getAssignmentType().equals("U")
            && contactDocument.getCr_asgn_id() > 0;
    }

    private boolean conversationAssignToTeam(ContactDocument contactDocument) {
        if (contactDocument == null)
            return false;
        return "T".equals(contactDocument.getAssignmentType())
            && contactDocument.getTeam_id() != null;
    }
    
    @Override
    public void bulkActionRefreshStatusOnWeb(Integer accountId, Integer userId) {
        try {
        	String topicName = StringUtils.join(BULK_ACTION_DB_NAME, "/", String.valueOf(accountId));
            Map<String, Object> data = new HashMap<>();
            data.put("val",userId+"|"+new Date().getTime());
            log.info("TidyUp-Topic-Firebase : {} with data {}", topicName, data);
            FirebaseDatabaseInsertOrUpdateRequest<Map<String, Object>> request =
                new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, data);
            kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
        } catch (Exception e) {
            log.error("Exception while bulkActionRefreshStatusOnWeb for accountId :: {} {}", accountId, e);
        }
    }

	@Override
	public void mirrorOnMobileTidyUpConversations(Map<Integer, List<Integer>> mobileMirrorData, Integer accountId) {
		try {
			for (Map.Entry<Integer, List<Integer>> entry : mobileMirrorData.entrySet()) {
				Map<String, Object> data = new HashMap<>();
				data.put("tidyUpConversations", entry.getValue());

				// conversation/123/tidy-up
				String topicName = StringUtils.join(CONVERSATION_DB_NAME
						+ "/", String.valueOf(entry.getKey()) + "/",TIDY_UP_NODE_NAME);
				log.info("mirrorOnMobileTidyUpConversations MOBILE-NEW-TOPIC-FIREBASE : [ {} ]", topicName);

				FirebaseDatabaseInsertOrUpdateRequest<Map<String, Object>> request =
						new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, data);
				kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
			}
		} catch (Exception e) {
            log.error("Exception while pushing to kafka in mirrorOnMobileTidyUpConversations for businessId ::"
                + accountId, e);
        }
	}



    @Override
    public void pushInstallationStatusToFirebase(Integer widgetId, FirebaseWebchatInstallationResponse response) {
        String topicName = StringUtils.join(Constants.WEBCHAT_INSTALLATION_STATUS
                + "/", String.valueOf(widgetId));
        log.info("Publishing webchat installation event to Topic", topicName);

        FirebaseDatabaseInsertOrUpdateRequest<FirebaseWebchatInstallationResponse> request =
                new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, response);
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null, request);
    }
	
	private UserDetail getUserDetail(Integer userId) {
		UserDTO userDTO = null;
		UserDetail userDetail=null;
		if (Objects.nonNull(userId)) {
			userDTO = communicationHelperService.getUserDTO(userId);
		}
		if(Objects.nonNull(userDTO)) {
			userDetail=new UserDetail(userDTO.getId(),userDTO.getName());
		}
		return userDetail;
	}

	@Override
	@Async
	public void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument,
			boolean sendWebNotification, boolean sendMobileNotification, Integer loggedInUserId,MessageDocument lastMessage) {
		 log.debug("pushFireBaseNotification DEBUG: {}  ----------- {}", messageDocument, contactDocument);

	        if (messageDocument != null) {
	            messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
	            messageDocument.setIs_encrypt(0);
	        }
	        if (lastMessage != null) {
	            lastMessage.setMsg_body(MessengerUtil.decryptMessage(lastMessage));
	            lastMessage.setIs_encrypt(0);
	        }
	        BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
	        // Some cases would require conditional logic (other than message type) to
	        // suppress push/chrome notifications
	        // In case chatbot replies for incoming messages, we dont want push
	        // notifications.

	        if (sendWebNotification ) {
	            pushFCMNotificationForWEB(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage,null);
	        }
	        if (sendMobileNotification) {
	            pushFCMNotificationForMobile(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage);
	        }
	        // Mirroring is not optional for any case.
	        //mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
	        mirrorOnWeb(DtoToEntityConverter.fromContactDoc(contactDocument));
	        mirrorOnMobile(contactDocument, messageDocument);
            mirrorOnSecureChat(contactDocument, messageDocument);

	}

        @Override
        public void mirrorOnSecureChat(ContactDocument contactDocument, MessageDocument messageDocument) {
            if (messageDocument != null && Integer.valueOf(17).equals(messageDocument.getSource())
                    && messageDocument.getMessageType() != null
                    && messageDocument.getMessageType().equals(MessageType.CHAT)) {
                try {
                    SecureMessageResponse secureMessageResponse = new SecureMessageResponse(messageDocument);
                    String topicName = StringUtils.join(secureMessagingBaseFirebaseBucket, contactDocument.getM_c_id(),
                            "-", contactDocument.getC_id());
                    log.info("Pushed to SECURE-CHAT-FIREBASE  topic: [ {} ]", topicName);
                    FirebaseDatabaseInsertOrUpdateRequest<SecureMessageResponse> request = new FirebaseDatabaseInsertOrUpdateRequest<>(
                            topicName, secureMessageResponse, true);
                    kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, null,
                            request);
                } catch (Exception e) {
                    log.error("[MirroringMobile] Error occured while mirroring for secure chat apps", e.getMessage());
                }
            }

        }


    @Override
    @Async
    public void pushToFireBaseAccountBucket(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId, MessageDocument lastMessage, boolean notification) {
        log.debug("pushFireBaseNotification DEBUG: {}  ----------- {}", messageDocument, contactDocument);

        if (messageDocument != null) {
            messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
            messageDocument.setIs_encrypt(0);
        }
        if (lastMessage != null) {
            lastMessage.setMsg_body(MessengerUtil.decryptMessage(lastMessage));
            lastMessage.setIs_encrypt(0);
        }
        BusinessDTO businessDTO = businessService.getBusinessDTO(contactDocument.getB_id());
        // Some cases would require conditional logic (other than message type) to
        // suppress push/chrome notifications
        // In case chatbot replies for incoming messages, we dont want push
        // notifications.

        if (notification ) {
            pushFCMNotificationForMobile(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage);
            pushFCMNotificationForWEB(contactDocument, messageDocument, businessDTO, loggedInUserId, lastMessage,null);
        }
        // Mirroring is not optional for any case.
        //mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
        mirrorOnSecureChat(contactDocument, messageDocument);
        FirebaseDto firebaseDto = DtoToEntityConverter.fromContactDoc(contactDocument);
//        firebaseDto.setBusinessId(null);
        mirrorOnWeb(firebaseDto);
        mirrorOnMobile(contactDocument, messageDocument);
    }

    @Override
    public void pushSwitchWebchatConversationEventToFirebase(SwitchConversationFirebaseEvent switchConversationFirebaseEvent){
        BusinessDTO businessDTO = businessService.getBusinessDTO(switchConversationFirebaseEvent.getBusinessId());
        if (StringUtils.isBlank(businessDTO.getTimeZoneId())) {
            businessDTO.setTimeZoneId("PST");
        }

        String topicName = StringUtils.join(CONVERSATION_DB_NAME
                + "/", String.valueOf(businessDTO.getBusinessId()), "/", String.valueOf(switchConversationFirebaseEvent.getOldMcid()));

        log.info("Switch Webchat Conversation Event To Livechat: {}", JSONUtils.toJSON(switchConversationFirebaseEvent));
        FirebaseDatabaseInsertOrUpdateRequest<SwitchConversationFirebaseEvent> request =
                new FirebaseDatabaseInsertOrUpdateRequest<>(topicName, switchConversationFirebaseEvent);
        kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_FIREBASE_DB_INSERT_UPDATE, businessDTO.getRoutingId(), request);
    }
}

