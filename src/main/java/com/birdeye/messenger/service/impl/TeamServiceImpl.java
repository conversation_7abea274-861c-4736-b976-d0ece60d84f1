package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.UpdateTeamRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.TeamService;

import lombok.extern.slf4j.Slf4j;
@Service
@Slf4j
public class TeamServiceImpl implements TeamService {
	
	@Autowired
	private ElasticSearchExternalService esService;
	

	@Override
	public void updateTeam(UpdateTeamRequest updateTeamRequest) {
		MessengerFilter filter=new MessengerFilter();
		filter.setTeamId(updateTeamRequest.getTeamId());
		filter.setCount(10000);
		updateContactDocs(updateTeamRequest,filter);
		updateMessageDocs(updateTeamRequest,filter);
	}

	private void updateMessageDocs(UpdateTeamRequest updateTeamRequest, MessengerFilter filter) {
		log.info("updating team in message document for account: {} and teamId {}",updateTeamRequest.getAccountId(),updateTeamRequest.getTeamId());
		Map<String, Object> dataModel = new HashMap<>();
		String newTeamName=updateTeamRequest.getNewTeamName();
		List<String> activityTypesList=new ArrayList<String>();
		activityTypesList.add(ActivityType.TEAM_ASSIGN.name());
		activityTypesList.add(ActivityType.LIVECHAT_START_WITH_TEAM.name());
		activityTypesList.add(ActivityType.WEBCHAT_START_WITH_TEAM.name());
		dataModel.put("messageType", MessageDocument.MessageType.ACTIVITY.name());
		String activityTypes= String.join(",", activityTypesList.stream().map(name -> ("\"" + name + "\"")).collect(Collectors.toList()));
		dataModel.put("activityTypes", activityTypes);
		filter.setParams(dataModel);
		Map<String, Object> data = new HashMap<>();
		data.put("data", filter);
        Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_V3).freeMarkerDataModel(data);
		Map<String, Object> scriptData = new HashMap<>();
			scriptData.put("inline", "ctx._source.triggeredFor.userName="+"'"+newTeamName+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
		builder.scriptParam(scriptData);
		builder.routingId(updateTeamRequest.getAccountId());
		boolean response = esService.updateByQuery(builder.build());
		if(!response) {
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
	}

	private void updateContactDocs(UpdateTeamRequest updateTeamRequest, MessengerFilter filter) {
		log.info("updating team in contact document for account: {} and teamId {}",updateTeamRequest.getAccountId(),updateTeamRequest.getTeamId());
		String newTeamName=updateTeamRequest.getNewTeamName();
		Map<String, Object> data = new HashMap<>();
		data.put("data", filter);
        Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.CONTACT_INDEX).queryTemplateFile(Constants.Elastic.GET_CONTACT_V1).freeMarkerDataModel(data);
		Map<String, Object> scriptData = new HashMap<>();
			scriptData.put("inline", "ctx._source.team_name="+"'"+newTeamName+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
		builder.scriptParam(scriptData);
		builder.routingId(updateTeamRequest.getAccountId());
		boolean response = esService.updateByQuery(builder.build());
		if(!response) {
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
		
	}

}
