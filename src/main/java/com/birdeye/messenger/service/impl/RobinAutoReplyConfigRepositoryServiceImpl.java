package com.birdeye.messenger.service.impl;

import com.amazonaws.util.CollectionUtils;
import com.amazonaws.util.StringUtils;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dao.repository.RobinAutoReplyConfigRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLite;
import com.birdeye.messenger.dto.robin.GetRobinReplyConfigResponse;
import com.birdeye.messenger.dto.robin.RobinAutoReplySetupRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RobinAutoReplyConfigRepositoryService;
import com.birdeye.messenger.util.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import jakarta.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobinAutoReplyConfigRepositoryServiceImpl implements RobinAutoReplyConfigRepositoryService {

    @Autowired
    RobinAutoReplyConfigRepository robinAutoReplyConfigRepository;

    @Autowired
    RedisHandler redisHandler;

    private final BusinessService businessService;

    @Override
    public void createUpdateRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest) {
        Map<Integer, BizLite> businessDataForMessenger = businessService.getBizLite(robinAutoReplySetupRequest.getBusinessId(), null);
        businessDataForMessenger.values().parallelStream().forEach(business -> {
            robinAutoReplySetupRequest.getChannel().parallelStream().forEach(channel -> {
                if(business.getClosed().equals(0)){
                    if(isEnterpriseAccount(business.getType())){
                        log.info("creating robin/autoreply config at enterprise level for enterpriseId: {}",business.getAccountId());
                        evictConfigCache(business.getAccountId());
                    }
                    long startTime=System.currentTimeMillis();
                    List<RobinAutoReplyConfig> robinAutoReplyConfigList=robinAutoReplyConfigRepository.findByBusinessIdAndChannel(business.getId(),channel);
                    long endTime=System.currentTimeMillis();
                    LogUtil.logExecutionTime("findByBusinessIdAndChannel",startTime,endTime);
                    if(CollectionUtils.isNullOrEmpty(robinAutoReplyConfigList)){
                        createConfig(channel,robinAutoReplySetupRequest,business.getId(),business.getAccountId());
                    }else{
                        updateConfig(channel,robinAutoReplySetupRequest,robinAutoReplyConfigList.get(0),business.getId(),business.getAccountId());
                    }
                }
            });
        });
    }


    private void createConfig(String channel,RobinAutoReplySetupRequest robinAutoReplySetupRequest,Integer businessId,Integer enterpriseId){
        RobinAutoReplyConfig robinAutoReplyConfig = new RobinAutoReplyConfig();
        robinAutoReplyConfig = setConfig(channel,robinAutoReplySetupRequest,robinAutoReplyConfig,businessId);
        evictConfigCache(enterpriseId);
        robinAutoReplyConfigRepository.save(robinAutoReplyConfig);
    }
    
    private void updateConfig(String channel, RobinAutoReplySetupRequest robinAutoReplySetupRequest, RobinAutoReplyConfig robinAutoReplyConfig,Integer businessId,Integer enterpriseId) {
        RobinAutoReplyConfig robinAutoReplyConfigNew = setConfig(channel,robinAutoReplySetupRequest,robinAutoReplyConfig,businessId);
        evictConfigCache(enterpriseId);
        robinAutoReplyConfigRepository.save(robinAutoReplyConfigNew);
    }

    private RobinAutoReplyConfig setConfig(String channel,RobinAutoReplySetupRequest robinAutoReplySetupRequest,RobinAutoReplyConfig robinAutoReplyConfig,Integer businessId){
        robinAutoReplyConfig.setBusinessId(businessId);
        robinAutoReplyConfig.setChannel(channel);
        if(Objects.nonNull(robinAutoReplySetupRequest.isEnableRobin())){
            robinAutoReplyConfig.setEnableRobin(robinAutoReplySetupRequest.isEnableRobin());
        }
        if(Objects.nonNull(robinAutoReplySetupRequest.isEnableAutoReplyInsideBusinessHours())){
            robinAutoReplyConfig.setEnableAutoReplyInsideBusinessHours(robinAutoReplySetupRequest.isEnableAutoReplyInsideBusinessHours());
        }
        if(Objects.nonNull(robinAutoReplySetupRequest.isEnableAutoReplyOutsideBusinessHours())){
            robinAutoReplyConfig.setEnableAutoReplyOutsideBusinessHours(robinAutoReplySetupRequest.isEnableAutoReplyOutsideBusinessHours());
        }
        if(Objects.nonNull(robinAutoReplySetupRequest.getAutoReplyInsideBusinessHours())){
            robinAutoReplyConfig.setAutoReplyInsideBusinessHours(robinAutoReplySetupRequest.getAutoReplyInsideBusinessHours());
        }
        if(Objects.nonNull(robinAutoReplySetupRequest.getAutoReplyOutsideBusinessHours())){
            robinAutoReplyConfig.setAutoReplyOutsideBusinessHours(robinAutoReplySetupRequest.getAutoReplyOutsideBusinessHours());
        }
        if(robinAutoReplyConfig.isEnableAutoReplyInsideBusinessHours() && StringUtils.isNullOrEmpty(robinAutoReplyConfig.getAutoReplyInsideBusinessHours())){
            robinAutoReplyConfig.setAutoReplyInsideBusinessHours(MessengerConstants.AUTO_REPLY_INSIDE_BUSINESS_HOURS);
        }
        if(robinAutoReplyConfig.isEnableAutoReplyOutsideBusinessHours() && StringUtils.isNullOrEmpty(robinAutoReplyConfig.getAutoReplyOutsideBusinessHours())){
            robinAutoReplyConfig.setAutoReplyOutsideBusinessHours(MessengerConstants.AUTO_REPLY_OUTSIDE_BUSINESS_HOURS);
        }
        robinAutoReplyConfig.setCreatedAt(new Date());
        robinAutoReplyConfig.setUpdatedAt(new Date());
        return robinAutoReplyConfig;
    }

    @Override
    public void evictConfigCache(Integer enterpriseId){
        redisHandler.deleteKey(Constants.ROBIN_AUTOREPLY_CONFIGURATION.concat(enterpriseId.toString()));
    }
    
    @Override
    public RobinAutoReplyConfig getRobinAutoReplyConfigForBusiness(Integer businessId,Integer enterpriseId, Source channel) {
        // Search for RobinAutoReplyConfig in order of priority
        log.info("Get Robin auto-reply config for businessId : {}, enterpriseId : {}, source : {}",businessId,enterpriseId,channel.name());
        //channel
        RobinAutoReplyConfig config = getConfigurationFromCache(businessId,enterpriseId,channel.name());
        if (config != null){
            return config;
        }
        List<RobinAutoReplyConfig> robinAutoReplyConfigs = robinAutoReplyConfigRepository.findByBusinessIdAndEnterpriseId(businessId, enterpriseId,channel.name());
        if(CollectionUtils.isNullOrEmpty(robinAutoReplyConfigs)){
            return null;
        }

        List<Map<Integer, String>> conditions = populatePriorityOrder(businessId, enterpriseId, channel);
        for (Map<Integer, String> condition : conditions) {
            for (RobinAutoReplyConfig autoReplyConfig : robinAutoReplyConfigs) {
                Integer conditionKey = condition.keySet().iterator().next();
                String conditionValue = condition.get(conditionKey);
                if (autoReplyConfig.getBusinessId().equals(conditionKey) && autoReplyConfig.getChannel().equals(conditionValue)) {
                    log.info("matched RobinAutoReplyConfig for key : {} value : {}", conditionKey, conditionValue);
                    config = autoReplyConfig;
                    redisHandler.putData(Constants.ROBIN_AUTOREPLY_CONFIGURATION.concat(enterpriseId.toString()),
                            businessId.toString().concat("-").concat(channel.name()), autoReplyConfig);
                    return config;
                }
            }
        }
        // If no config found, return null
        return null;
    }

    private RobinAutoReplyConfig getConfigurationFromCache(Integer businessId,Integer enterpriseId,String channel){
        RobinAutoReplyConfig robinAutoReplyConfig = (RobinAutoReplyConfig) redisHandler.getOpsForHash(Constants.ROBIN_AUTOREPLY_CONFIGURATION.concat(enterpriseId.toString()),businessId.toString().concat("-").concat(channel));
        return robinAutoReplyConfig;
    }

    @Override
    @Transactional
    public void deleteRobinAutoReplyConfiguration(RobinAutoReplySetupRequest request) {
        if (request.getChannel().contains("ALL") && request.isAllConfig()) {
            List<String> channels = new ArrayList<>();
            channels.add(Source.SMS.name());
            channels.add(Source.INSTAGRAM.name());
            channels.add(Source.FACEBOOK.name());
            channels.add(Source.GOOGLE.name());
            channels.add(Source.APPLE.name());
            channels.add(Source.VOICE_CALL.name());
            channels.add(Source.TWITTER.name());
            channels.add(Source.CONTACT_US.name());
            request.getChannel().addAll(channels);
        }
        for (Integer businessId : request.getBusinessId()) {
            BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
            if (businessDTO == null) {
                log.info("No business found to disable robin/autoreply config for businessId : {}", businessId);
                throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
            }
            List<Integer> businessIds = new ArrayList<>();
            if(request.isAllConfig()) {
                businessIds = businessService.getBusinessLiteObjects(businessId)
                        .stream()
                        .map(BusinessLite::getBusinessId)
                        .collect(Collectors.toList());
            }
            businessIds.add(businessId);
            for (String channel : request.getChannel()) {
                robinAutoReplyConfigRepository.deleteAllByBusinessIdInAndChannel(businessIds,channel);
            }
            evictConfigCache(businessDTO.getAccountId());
        }
    }

    private boolean isEnterpriseAccount(String type) {
        return "Enterprise-Location".equals(type) || "Enterprise-Product".equals(type);
    }

    @Override
    public Map<Integer, GetRobinReplyConfigResponse> getAllRobinAutoReplyConfig(RobinAutoReplySetupRequest request) {
        List<GetRobinReplyConfigResponse> finalResponse = new ArrayList<>();
        for(Integer businessId : request.getBusinessId()){
            GetRobinReplyConfigResponse configResponse = new GetRobinReplyConfigResponse();
            BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
            if(businessDTO == null){
                log.info("No business found to get robin/auto-reply config for businessId :{}",businessId);
                throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
            }
            configResponse.setBusinessId(businessId);
            List<Integer> businessIds = businessService.getBusinessLiteObjects(businessId)
                    .stream()
                    .map(BusinessLite::getBusinessId)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isNullOrEmpty(businessIds)) {
                int batchSize = Constants.UPDATE_ROBIN_CONFIGURATION_LIMIT; // Set the batch size to 1000
                int offset = 0;
                List<Integer> batchIds = businessIds.subList(offset, Math.min(offset + batchSize, businessIds.size()));
                while (!batchIds.isEmpty()) {
                    List<RobinAutoReplyConfig> robinAutoReplyConfigs = robinAutoReplyConfigRepository.findAllByBusinessIdInOrderByChannelAtDesc(batchIds);
                    if (!CollectionUtils.isNullOrEmpty(robinAutoReplyConfigs)) {
                        List<GetRobinReplyConfigResponse> responseList = robinAutoReplyConfigs.stream()
                                .collect(Collectors.groupingBy(RobinAutoReplyConfig::getBusinessId))
                                .entrySet()
                                .stream()
                                .map(entry -> {
                                    GetRobinReplyConfigResponse response = new GetRobinReplyConfigResponse();
                                    response.setBusinessId(entry.getKey());
                                    response.setRobinAutoReplyConfig(entry.getValue());
                                    return response;
                                })
                                .collect(Collectors.toList());
                        finalResponse.addAll(responseList);
                    }
                    offset += batchIds.size();
                    batchIds = businessIds.subList(offset, Math.min(offset + batchSize, businessIds.size()));
                }
            }
        List<RobinAutoReplyConfig> robinAutoReplyConfigs = robinAutoReplyConfigRepository.findAllByBusinessIdInOrderByChannelAtDesc(Collections.singletonList(businessId));
        GetRobinReplyConfigResponse businessResponse = new GetRobinReplyConfigResponse(businessId,robinAutoReplyConfigs);
        finalResponse.add(businessResponse);
    }
    Map<Integer, GetRobinReplyConfigResponse> heiarachyResponse =  finalResponse.stream()
            .collect(Collectors.toMap(GetRobinReplyConfigResponse::getBusinessId, response -> response,
                    (a, b) -> a, LinkedHashMap::new));
    return heiarachyResponse;
}
    
    @Override
    @Transactional
    public void updateAllLocation(RobinAutoReplySetupRequest request) {
        if (request.getChannel().contains("ALL")) {
            List<String> channels = new ArrayList<>();
            channels.add(Source.SMS.name());
            channels.add(Source.INSTAGRAM.name());
            channels.add(Source.FACEBOOK.name());
            channels.add(Source.GOOGLE.name());
            channels.add(Source.APPLE.name());
            channels.add(Source.VOICE_CALL.name());
            channels.add(Source.TWITTER.name());
            channels.add(Source.CONTACT_US.name());
            request.getChannel().addAll(channels);
        }
        int batchSize = Constants.UPDATE_ROBIN_CONFIGURATION_LIMIT; // Set the batch size to 1000
        for (Integer businessId : request.getBusinessId()) {
            List<Integer> businessIds = businessService.getBusinessLiteObjects(businessId)
                    .stream()
                    .map(BusinessLite::getBusinessId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNullOrEmpty(businessIds)) {
                log.info("Location Id not found for account to create update robin/autoreply config for businessId : {}", businessId);
                throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
            }
            for (String channel : request.getChannel()) {
                int offset = 0;
                List<Integer> batchIds = businessIds.subList(offset, Math.min(offset + batchSize, businessIds.size()));
                while (!batchIds.isEmpty()) {
                    List<RobinAutoReplyConfig> robinAutoReplyConfigs = robinAutoReplyConfigRepository.findAllByBusinessIdsAndChannel(batchIds, channel);
                    if (!CollectionUtils.isNullOrEmpty(robinAutoReplyConfigs)) {
                        for (RobinAutoReplyConfig robinAutoReplyConfig : robinAutoReplyConfigs) {
                            robinAutoReplyConfig = setConfig(channel, request, robinAutoReplyConfig, robinAutoReplyConfig.getBusinessId());
                        }
                        robinAutoReplyConfigRepository.saveAll(robinAutoReplyConfigs);
                    }
                    offset += batchIds.size();
                    batchIds = businessIds.subList(offset, Math.min(offset + batchSize, businessIds.size()));
                }
            }
            evictConfigCache(businessId);
        }
    }

    @Override
    public List<GetRobinReplyConfigResponse> getRobinAutoReplyEffectiveConfig(RobinAutoReplySetupRequest request) {
        List<GetRobinReplyConfigResponse> response = new ArrayList<>();
        for(Integer businessId : request.getBusinessId()){
            GetRobinReplyConfigResponse configResponse = new GetRobinReplyConfigResponse();
            List<RobinAutoReplyConfig> replyConfigList = new ArrayList<>();
            BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
            if(businessDTO == null){
                log.info("No business found to get robin/auto-reply config for businessId :{}",businessId);
                throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
            }
            configResponse.setBusinessId(businessId);
            for(String channel : request.getChannel()) {
                List<RobinAutoReplyConfig> configAtBusinessLevel = Collections.singletonList(getRobinAutoReplyConfigForBusiness(businessDTO.getBusinessId(), businessDTO.getAccountId(), Source.getByName(channel)));
                if (!CollectionUtils.isNullOrEmpty(configAtBusinessLevel)) {
                    replyConfigList.addAll(configAtBusinessLevel);
                }
            }
            if(!CollectionUtils.isNullOrEmpty(replyConfigList)){
                configResponse.setRobinAutoReplyConfig(replyConfigList);
            }
            response.add(configResponse);
        }
        return response;
    }

    private List<Map<Integer, String>> populatePriorityOrder(Integer businessId, Integer enterpriseId, Source source) {
    	//priority order
    	//businessid + channel
    	//businessid + all
    	//entrpriseid + channel
    	//enterpirseid + all

    	List<Map<Integer, String>> priorityOrder = new ArrayList<>();

    	Map<Integer, String> condition1 = new HashMap<>();
    	condition1.put(businessId, source.name());
    	priorityOrder.add(condition1);

    	Map<Integer, String> condition2 = new HashMap<>();
    	condition2.put(businessId, "ALL");
    	priorityOrder.add(condition2);

    	Map<Integer, String> condition3 = new HashMap<>();
    	condition3.put(enterpriseId, source.name());
    	priorityOrder.add(condition3);

    	Map<Integer, String> condition4 = new HashMap<>();
    	condition4.put(enterpriseId, "ALL");
    	priorityOrder.add(condition4);

    	return priorityOrder;
    }

}
