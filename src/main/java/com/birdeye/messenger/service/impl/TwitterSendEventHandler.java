package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.*;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.*;
import com.birdeye.messenger.service.impl.MessageEventHandlerAbstract;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;

@Service
@Slf4j
public class TwitterSendEventHandler extends MessageEventHandlerAbstract{
    private final MessengerEvent EVENT = MessengerEvent.TWITTER_SEND;
    
    @Autowired
    protected TwitterEventService twitterEventService;
    @Autowired
    protected SendMessageService sendMessageService;
    @Autowired
    protected MessengerMediaFileService messengerMediaFileService;
    @Autowired
    protected MessengerContactRepository messengerContactRepository;
    @Autowired
    protected MessengerMessageService messengerMessageService;
    @Autowired
    protected CommonService commonService;
    @Autowired
    protected KafkaService kafkaService;
    @Autowired
    protected PulseSurveyService pulseSurveyService;
    @Autowired
    protected SocialService socialService;

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = communicationHelperService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            ///customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
            customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
            dto.setCustomerDTO(customerDTO);
        }
        return customerDTO;
    }

    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        return MessageTag.INBOX;
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        MessageDocumentDTO messageDocumentDTO= new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
        messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
        messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("SEND");
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.TWITTER.name());
        lastMessageMetadataPOJO.setLastMessageSource(Source.TWITTER.getSourceId());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        SendMessageDTO  dto = (SendMessageDTO) messageDTO;
        messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
            messengerContact.setLastMessage("Sent an attachment");
        } else {
            messengerContact.setLastMessage(sendMessageDTO.getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), messengerContact.getInstagramConversationId(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }

    @Override
    public MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
                    businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        if(messengerContact!=null && BooleanUtils.isTrue(messengerContact.getBlocked())) {
            throw new BadRequestException(ErrorCode.CONTACT_IS_BLOCKED, ErrorCode.CONTACT_IS_BLOCKED.getErrorMessage());
        }
        return messengerContact;
    }

    @Override
    MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
            SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    public SendResponse handle(MessageDTO messageDTO) throws Exception {
        if(!messageDTO.isBOT()) messageDTO.setMsgTypeForResTimeCalc("S");
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        messageDTO.setBusinessDTO(businessDTO);
        MessengerContact messengerContact=getMessengerContact(messageDTO);
        messageDTO.setMessengerContact(messengerContact);
        SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
        // TODO set flag with updated approach
        SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages);
        sendResponse.setLastMsgSource(messageDTO.getSource());
        sendResponse.setMessages(messages);
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        // handle PulseSurveyContext
        PulseSurveyContext context = null;
        try {
            context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
        } catch (Exception ex) {
            log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
        }
        ContactDocument contactDocument = new ContactDocument();
        if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
            contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
        } else {
            contactDocument.setOngoingPulseSurvey(false);
        }
        messengerContactService.updateContactOnES(messageDTO.getMessengerContact().getId(), contactDocument, messageDTO.getBusinessDTO().getAccountId());
        return sendResponse;
    }

    private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
        // TODO: Refactor : Avoid casting and have separate dto & request.
        getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
        commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO),"twitter"); //why small here ?
        sendMessageDTO.setSource(Source.TWITTER.getSourceId());
        sendMessageDTO.setFromBusinessId(getMessengerContact(sendMessageDTO).getBusinessId());
        MessengerEvent event = MessengerEvent.SMS_SEND;
        SortedSet<SendResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
        if (StringUtils.isNotBlank(sendMessageDTO.getMediaurl())
                || CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
            event = MessengerEvent.MMS_SEND;
        }
        if (event.equals(MessengerEvent.MMS_SEND)) {
            log.info("Processing Twitter attachment SEND for contact {} ",sendMessageDTO.getToCustomerId());
            // Process mms data.
            String text=sendMessageDTO.getBody();
            for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
                MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
                sendMessageDTO.setMediaurl(mediaFile.getUrl());
                sendMessageDTO.setMediaId(sendMessageDTO.getMediaIds().get(media));
                if (media < sendMessageDTO.getMediaUrls().size()) {
                    sendMessageDTO.setBody(null);
                }
                MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
                messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
                sendMessageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
                if(media!=0) {
                    messageDTO.setPartOfExistingMessage(true);
                }
                if(StringUtils.isNotEmpty(text) && media == sendMessageDTO.getMediaUrls().size() - 1){
                    sendMessageDTO.setBody(text);
                    text = null;
                }
                messages.add(sendTwitterMessage(sendMessageDTO, event));
            }
            // Process Text body if present.
            if(StringUtils.isNotEmpty(text)){
                log.info("Preparing Text message for Twitter attachment SEND for contact {} ",sendMessageDTO.getToCustomerId());
                sendMessageDTO.setMessengerMediaFileDTO(null);
                sendMessageDTO.setBody(text);
                messages.add(sendTwitterMessage(sendMessageDTO,event));
            }

        } else {
            log.info("Processing Twitter TEXT SEND for contact {} ",sendMessageDTO.getToCustomerId());
            messages.add(sendTwitterMessage(sendMessageDTO, event));
        }
        return messages;
    }

    private SendResponse.Message sendTwitterMessage(MessageDTO messageDTO,MessengerEvent event) throws Exception {
        String pageId =socialService.getTwitterPageIdByBusinessId(getBusinessDTO(messageDTO).getBusinessId());
        //1. send Twitter message using soical service
        SendTwitterMessageResponse sendTwitterMessageResponse =twitterEventService.sendTwitterCallToSocial(this,messageDTO,pageId);
        //2. save messge in db
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        UserDTO userDTO=getUserDTO(messageDTO);
        TwitterMessage twitterMessage=twitterEventService.saveTwitterMessage(messageDTO,pageId,getMessengerContact(sendMessageDTO).getTwitterConversationId(), sendTwitterMessageResponse.getMessage_id(), TwitterMessageStatusEnum.sent);
        MessengerMessageMetaData messageMetaData=new MessengerMessageMetaData();
        messageMetaData.setSentThrough(MessageDocument.SentThrough.WEB);
        messageMetaData.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
        ConversationDTO conversationDTO = new ConversationDTO(twitterMessage,messageMetaData);
        log.info("Messenger contact Id {} ,conversation Id: {}  ", messageDTO.getMessengerContact().getId(),conversationDTO.getId());
        sendMessageDTO.setConversationDTO(conversationDTO);
        messengerMessageService.saveMessengerMessage(conversationDTO,userDTO);
        sendMessageService.pushSendRequestToKafka(sendMessageDTO,event,userDTO,false);
        messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), twitterMessage.getId());
        super.handle(sendMessageDTO);
        MessageResponse.Message message = new SendResponse.Message(userDTO, conversationDTO, new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO()), messageDTO.getBusinessDTO().getTimeZoneId());
        message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
        robinService.updateChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), userDTO,Source.TWITTER.name());
        return message;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        publishEventIfRepliedOnUnassignedConversation(messageDTO);
    }

}
