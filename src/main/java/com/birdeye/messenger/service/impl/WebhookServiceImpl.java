package com.birdeye.messenger.service.impl;

import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.UrlValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.WebhookEvent;
import com.birdeye.messenger.dao.entity.WebhookSubscription;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dao.repository.WebhookEventRepository;
import com.birdeye.messenger.dao.repository.WebhookSubscriptionRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationClosedEvent;
import com.birdeye.messenger.dto.ConversationWebhookEventDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.MessageWebhookEventDTO;
import com.birdeye.messenger.dto.WebhookEventRequest;
import com.birdeye.messenger.dto.WebhookEventResponse;
import com.birdeye.messenger.dto.WebhookSubscriptionRequest;
import com.birdeye.messenger.dto.WebhookSubscriptionResponse;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.WebhookEventEnum;
import com.birdeye.messenger.event.MessengerKafkaMessage;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.WebhookService;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebhookServiceImpl implements WebhookService {

	private final BusinessService businessService;
	private final WebhookEventRepository eventRepository;
	private final WebhookSubscriptionRepository subscriptionRepository;
	private final CommunicationHelperService communicationHelperService;
	private final MessengerContactRepository messengerContactRepository;
	private final KafkaService kafkaService;
	private final RedisHandler redisHandler;

	@Autowired
	private RestTemplate restTemplate;

	@Override
	public WebhookSubscriptionResponse createWebhookSubscription(WebhookSubscriptionRequest request) {

		// Validate Request? https, validate url(not malformed)
		if (!validateEndpointURL(request)) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_ENDPOINT_URL, HttpStatus.BAD_REQUEST));
		}

		BusinessDTO business = businessService.getBusinessByBusinessNumber(request.getBusinessNumber());
		if (business == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.BUSINESS_NOT_FOUND, HttpStatus.BAD_REQUEST)
					.message("No business found for business number - {}", request.getBusinessNumber()));
		}

		Set<WebhookEvent> validEvents = new HashSet<>();
		for (String eventName : request.getEvents()) {
			WebhookEvent event = eventRepository.findByName(eventName);
			if (event != null) {
				validEvents.add(event);
			}
		}
		if (CollectionUtils.isEmpty(validEvents)) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.NO_VALID_EVENT,  HttpStatus.BAD_REQUEST));
		}
		WebhookSubscription subscription = subscriptionRepository.findByBusinessId(business.getAccountId());
		if (subscription != null) {
			// Validate endpoint
			if (!subscription.getEndpoint().equalsIgnoreCase(request.getEndpoint())) {
				log.warn("Updating old webhook endpoint {} for business {} ", subscription.getEndpoint(),
						business.getBusinessId());
				subscription.setEndpoint(request.getEndpoint());
			}
			Set<WebhookEvent> existingEvents = null != subscription.getEvents() ? subscription.getEvents()
					: new HashSet<>();
			existingEvents.addAll(validEvents);
			subscription.setUpdatedAt(new Date());
		} else {
			subscription = new WebhookSubscription();
			subscription.setBusinessId(business.getBusinessId());
			subscription.setEndpoint(request.getEndpoint());
			subscription.setEvents(validEvents);
			subscription.setCreatedAt(new Date());
		}
		if(validEvents.stream().anyMatch(event->event.getName().equals("conversation.closed"))) {
			subscription.setChatTranscriptContentType(request.getChatTranscriptContentType());
		}
		subscription = subscriptionRepository.save(subscription);
		List<String> events = subscription.getEvents().stream().map(WebhookEvent::getName).collect(Collectors.toList());
		return new WebhookSubscriptionResponse(subscription.getId(), business.getBusinessId(), events);
	}

	/**
	 * @param request
	 * @return
	 */
	private boolean validateEndpointURL(WebhookSubscriptionRequest request) {
		try {
			URL url = new URL(request.getEndpoint());
			url.toURI();
			if (StringUtils.isNotBlank(url.getQuery())) {
				return false;
			}
			String[] customSchemes = { "https" };
			UrlValidator urlValidator = new UrlValidator(customSchemes);
			return urlValidator.isValid(request.getEndpoint());
		} catch (Exception e) {
			log.error("createWebhookSubscription : Webhook endpoint provided is not valid for account : {} ",
					request.getBusinessNumber(), e);
			throw new BadRequestException(ErrorCode.INVALID_ENDPOINT_URL);
		}
	}

	@Override
	public WebhookSubscriptionResponse getSubscription(Long businessNumber) {
		BusinessDTO business = businessService.getBusinessByBusinessNumber(businessNumber);
		if (business == null) {
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		WebhookSubscription subscription = subscriptionRepository.findByBusinessId(business.getAccountId());
		if (null == subscription) {
			throw new NotFoundException(ErrorCode.NO_SUBSCRIPTION_FOUND);
		}
		List<String> events = subscription.getEvents().stream().map(WebhookEvent::getName).collect(Collectors.toList());
		return new WebhookSubscriptionResponse(subscription.getId(), business.getBusinessId(), events);
	}

	@Override
	public List<WebhookEventResponse> createWebhookEvent(WebhookEventRequest request) {
		List<WebhookEvent> events = new ArrayList<>();
		for (String eventName : request.getEvents()) {
			WebhookEventEnum eventEnum = WebhookEventEnum.getWebhookEventByName(eventName);
			WebhookEvent savedEvent = eventRepository.findByName(eventName);
			// Save new event only if it does not exist in db
			if (eventEnum != null && savedEvent == null) {
				WebhookEvent event = new WebhookEvent();
				event.setName(eventName);
				event.setCreatedAt(new Date());
				events.add(event);
			}
		}
		events = eventRepository.saveAll(events);
		if (null == events) {
			return null;
		}
		List<WebhookEventResponse> responseEvents = events.stream().map(event -> {
			return new WebhookEventResponse(event.getId(), event.getName());
		}).collect(Collectors.toList());
		return responseEvents;
	}

	@Override
	public List<WebhookEventResponse> getAllEvents() {
		List<WebhookEvent> savedEvents = eventRepository.findAll();
		if (null == savedEvents) {
			return null;
		}
		return savedEvents.stream().map(event -> {
			return new WebhookEventResponse(event.getId(), event.getName());
		}).collect(Collectors.toList());
	}

	@Override
	public WebhookSubscriptionResponse getSubscriptionForBusinessByEvent(Integer accountId, String eventName) {
		WebhookSubscription subscription = subscriptionRepository.findSubscriptionByBusinessIdAndEventId(accountId,
				eventName);
		if (null == subscription) {
			throw new NotFoundException(ErrorCode.NO_SUBSCRIPTION_FOUND);
		}
		List<String> events = subscription.getEvents().stream().map(WebhookEvent::getName).collect(Collectors.toList());
		return new WebhookSubscriptionResponse(subscription.getId(), accountId, events);
	}

	@Override
	@Async
	public <T> void publishMessengerWebhookEvent(T messagePayload, Integer accountId, String eventName,
			Integer businessId) {
		Long accountNumber=null;
		BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(businessId);
		if (businessDTO != null) {
			if (WebhookEventEnum.CONVERSATION_CLOSED.getName().equals(eventName)) {
				accountNumber = businessDTO.getEnterpriseNumber();
			}
			MessengerKafkaMessage<T> message = new MessengerKafkaMessage<T>(eventName, accountId,
					businessDTO.getBusinessNumber(), businessDTO.getBusinessName(), null, accountNumber,
					messagePayload);
//			applicationEventPublisher.publishEvent(new MessengerWebhookEvent(this, kafkaMessage));
//			MessengerKafkaMessage<?> message = event.getKafkaMessage();
			log.info("ProcessMessengerWebhookEvent : Received conversation webhook event source: {}, account: {}",
					eventName, message.getAccountId());
			String subscriptionEndpoint = getActiveSubscriptionEndpointFromRedis(message.getAccountId(),
					message.getEvent());
			if (StringUtils.isNotBlank(subscriptionEndpoint) && !subscriptionEndpoint.equalsIgnoreCase("UNSUBSCRIBED")) {
				message.setEndpoint(subscriptionEndpoint);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.WEBHOOK_EVENT, message);
				log.info("ProcessMessengerWebhookEvent : Kafka message sent {}", JSONUtils.toJSON(message));
			} else {
				log.warn("ProcessMessengerWebhookEvent : No subscriptions {} found for business : {} , event : {} ",
						subscriptionEndpoint, message.getAccountId(), message.getEvent());
			}
		}	
	}

	public String getActiveSubscriptionEndpointFromRedis(Integer businessId, String eventName) {
		String redisKey = "SUBSCRIPTION:DETAIL" + businessId + "_" + eventName;
		String endpoint = null;
		try {
			if (StringUtils.isNotBlank(redisHandler.getKeyValueFromRedis(redisKey))) {
				endpoint = redisHandler.getKeyValueFromRedis(redisKey);
			} else {
				WebhookSubscription subscriptionEndpoint = subscriptionRepository
						.findSubscriptionByBusinessIdAndEventId(businessId, eventName);
				if (null == subscriptionEndpoint) {
					log.info(
							"getActiveSubscriptionEndpointFromRedis : No active subscription found for businessId {}, event {} ",
							businessId, eventName);
					endpoint = "UNSUBSCRIBED";
				} else {
					endpoint = subscriptionEndpoint.getEndpoint();
				}
				redisHandler.setOpsForValueWithExpiry(redisKey, endpoint, 15l, TimeUnit.MINUTES);
			}
		} catch (Exception e) {
			log.error(
					"getActiveSubscriptionEndpointFromRedis : Exception while fetching value from redis for businessId {}, event {} ",
					businessId, eventName, e);
		}
		return endpoint;
	}
	
	@Override
	public ConversationWebhookEventDTO mapMessengerContactToConversationWebhookDTO(MessengerContact conversation) {
		String currentAssignmentType = conversation.getAssignmentType() == null ? Constants.Assignment.USER_ASSIGNED
				: conversation.getAssignmentType().name();
		IdentityDTO assignee = null;
		if (Constants.Assignment.USER_ASSIGNED.equals(currentAssignmentType)) {
			assignee = new IdentityDTO(conversation.getCurrentAssignee(), conversation.getCurrentAssigneeName(),
					currentAssignmentType, conversation.getCurrentAssigneeEmailId());
		} else {
			assignee = new IdentityDTO(conversation.getTeamId(), conversation.getTeamName(), currentAssignmentType,
					null);
		}
		ConversationWebhookEventDTO conversationWebhookEventDTO = new ConversationWebhookEventDTO(conversation.getId(),
				MessageTag.ARCHIVED.getCode() == conversation.getTag() ? "CLOSED" : "OPEN",
						conversation.getCustomerId(), conversation.getCreatedAt().getTime(),
						null != conversation.getUpdatedAt() ? conversation.getUpdatedAt().getTime() : null, assignee);
		try {
			if (null != conversation.getCustomerId()) {
				CustomerDTO customer = communicationHelperService.getCustomerDTO(conversation.getCustomerId());
				setCustomerName(customer, conversationWebhookEventDTO);
				conversationWebhookEventDTO.setCustomerEmail(customer.getEmailId());
				conversationWebhookEventDTO.setCustomerPhone(customer.getPhone());
			}
		} catch (Exception e) {
			log.error(
					"mapMessengerContactToConversationWebhookDTO : exception occurred while fetching contact for conversation {}, business {} ",
					conversation.getId(), conversation.getBusinessId(), e);
		}
		return conversationWebhookEventDTO;
	}

	@Override
	public MessageWebhookEventDTO mapMessageDocumentToMessageWebhookDTO(MessageDocument messageDocument) {
		MessengerContact messengerContact = messengerContactRepository
				.findById(Integer.parseInt(messageDocument.getC_id())).orElse(null);
		ConversationWebhookEventDTO conversationDTO = null;
		if (null != messengerContact) {
			conversationDTO = mapMessengerContactToConversationWebhookDTO(messengerContact);
		}
		if (StringUtils.isNotEmpty(messageDocument.getPageUrlTrackedByGoogleAnalytics())) {
			conversationDTO.setOriginPageUrl(messageDocument.getPageUrlTrackedByGoogleAnalytics());
		}
		String direction = null != messageDocument.getCommunicationDirection()
				? messageDocument.getCommunicationDirection().name()
				: null;
		String channel = null != messageDocument.getChannel() ? messageDocument.getChannel().name() : null;
		String messageBody = MessengerUtil.decryptMessage(messageDocument);
		MessageWebhookEventDTO messageData = new MessageWebhookEventDTO(messageDocument.getM_id(), messageBody,
				direction, messageDocument.getCr_date(), channel, messageDocument.getMediaFiles(), conversationDTO);
		return messageData;
	}

	public static void setCustomerName(CustomerDTO customer, ConversationWebhookEventDTO contact) {
		contact.setCustomerName(customer.getFirstName());
		if (StringUtils.isBlank(contact.getCustomerName()) && StringUtils.isNotBlank(customer.getLastName())) {
			contact.setCustomerName(customer.getLastName());
		} else if (StringUtils.isNotBlank(customer.getLastName())) {
			contact.setCustomerName(contact.getCustomerName()
					+ (StringUtils.isNotBlank(customer.getLastName()) ? (" " + customer.getLastName()) : ""));
		}
	}

	@Override
	public boolean isBusinessSubsribedToClosedEvent(ConversationClosedEvent conversationClosedEvent) {
		boolean isSubsribedToClosedEvent=false;
		WebhookSubscription subscription = subscriptionRepository.findByBusinessId(conversationClosedEvent.getAccountId());
		if (subscription != null && CollectionUtils.isNotEmpty(subscription.getEvents())) {
			if(subscription.getEvents().stream().anyMatch(event->event.getName().equals("conversation.closed"))) {
				isSubsribedToClosedEvent=true;
				conversationClosedEvent.setChatTranscriptContentType(subscription.getChatTranscriptContentType());
			}
		}
		return isSubsribedToClosedEvent;
	}
	@Override
	public boolean unsubscribeWebhookEvent(WebhookSubscriptionRequest request) {
		log.info("unsubscribeWebhookEvent in request - {}", request);
		boolean unsubscribed = false;
		if (request.getBusinessNumber() == null) {
			log.error("Business number not found in request - {}", request.getBusinessNumber());
			throw new MessengerException(ErrorCode.NO_BUSINESS_ID_SPECIFIED);
		}
		BusinessDTO business = businessService.getBusinessByBusinessNumber(request.getBusinessNumber());
		if (business == null) {
			log.error("No business found for business number in request - {}", request.getBusinessNumber());
			throw new MessengerException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		WebhookSubscription subscription = subscriptionRepository.findByBusinessId(business.getAccountId());
		if (subscription != null) {
			log.info("subscription found for business number - {}", request.getBusinessNumber());
			Set<WebhookEvent> validEvents = new HashSet<>();
			for (String eventName : request.getEvents()) {
				WebhookEvent event = eventRepository.findByName(eventName);
				if (event != null) {
					log.info("Event having name - {} is a valid event for business number - {}", eventName, request.getBusinessNumber());
					validEvents.add(event);
				}
			}
			if (CollectionUtils.isEmpty(validEvents)) {
				log.error("No valid event found for business number - {} and subscription id - {}", request.getBusinessNumber(), subscription.getId());
				throw new MessengerException(ErrorCode.NO_VALID_EVENT);
			}
			Set<WebhookEvent> existingEvents = null != subscription.getEvents() ? subscription.getEvents() : null;
			if (existingEvents != null) {
				for (WebhookEvent event : validEvents) {
					if (existingEvents.contains(event)) {
						subscriptionRepository.deleteEventFromSubscription(subscription.getId(), event.getId());
						log.info("Event - {} is deleted from subscription for business number - {}, and subcription id - {}", event.getName(), request.getBusinessNumber(), subscription.getId());
						unsubscribed = true;
					}
				}
				if (existingEvents.containsAll(validEvents) && existingEvents.size() == validEvents.size()) {
					subscriptionRepository.deleteById(subscription.getId());
					log.info("Subcription having id - {} is deleted from memory for business number - {}", subscription.getId(), request.getBusinessNumber());
					unsubscribed = true;
				}
			}
		} else {
			log.error("No subscription found for business number - {}", request.getBusinessNumber());
			throw new NotFoundException(ErrorCode.NO_SUBSCRIPTION_FOUND);
		}
		return unsubscribed;
	}

	@Override
	public <T> void publishExternalWebhookEvent(MessengerKafkaMessage<T> request) {
		if (StringUtils.isNotEmpty(request.getEndpoint()) && StringUtils.isNotEmpty(request.getEvent())) {
			log.info("publishExternalWebhookEvent, request : {}", request);
			String endpointUrl = request.getEndpoint().concat("?event=").concat(request.getEvent());
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<Object> httpEntity = new HttpEntity<>(request, headers);
			long startTime = System.currentTimeMillis();
			ResponseEntity<String> res = restTemplate.exchange(endpointUrl, HttpMethod.POST, httpEntity, String.class);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("publishExternalWebhookEvent", startTime, endTime);
			if (!res.getStatusCode().is2xxSuccessful()) {
				log.info("error in publishExternalWebhookEvent");
			}
		} else {
			log.info("Endpoint / Event cannot be blank");
		}
	}

}
