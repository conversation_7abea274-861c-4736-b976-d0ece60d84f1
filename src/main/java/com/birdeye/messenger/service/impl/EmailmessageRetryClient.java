/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.birdeye.messenger.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.EmailMessageConstants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.NexusEmailDTO.EmailFileAttachementData;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.CampaignService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.MessageRetryClient;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmailmessageRetryClient extends MessageRetryClient {

	private final BusinessService businessService;
	private final MessengerContactService messengerContactService;
	private final ContactService contactService;
	private final UserService userService;
	private final SendMessageService sendMessageService;
	private final FirebaseService fcmService;
	private final MessengerMediaFileService messengerMediaFileService;

	@Autowired
	private EmailService emailService;

	@Autowired
	private CampaignService campaignService;

	@Value("${email.reply.domain}")
	private String emailReplyDomain;

	private final String BUSINESS_DIRECTORY_LOCK = "business_directory";

	@Override
	public MessengerMessage.Message retrySendMessage(MessageRetry messageRetry) {

		Email email = emailService.findById(messageRetry.getId());
		BusinessDTO businessDTO = businessService.getBusinessDTO(email.getBusinessId());

		List<MessengerMediaFile> mediaFiles = messengerMediaFileService
				.findByMsgId(messageRetry.getId());

		String lastMessage = email.getMessageBody();
		String message = email.getMessageBody();
		if (StringUtils.isBlank(email.getMessageBody())) {
			message = "";
			lastMessage = "Sent an attachment";
		}
		boolean decryptionFailed = Boolean.FALSE;
		if (StringUtils.isNotBlank(email.getMessageBody()) && Objects.nonNull(email.getEncrypted())
				&& email.getEncrypted().equals(1)) {
			try {
				lastMessage = EncryptionUtil.decrypt(email.getMessageBody(),
						StringUtils.join(String.valueOf(businessDTO.getBusinessNumber()),
								String.valueOf(messageRetry.getMcId())),
						StringUtils.join(String.valueOf(messageRetry.getMcId()),
								String.valueOf(businessDTO.getBusinessNumber())),
						false);
				message = lastMessage;
				if (StringUtils.equals(lastMessage, ""))
					throw new Exception("Decryption Failed");
			} catch (Exception e) {
				log.error(
						"emailMessageRetryClient: decryption failed request info: emailId {}, mcId {}, accountId {}, userId {}, source {}",
						email.getId(), messageRetry.getMcId(), messageRetry.getAccountId(), messageRetry.getUserId(),
						messageRetry.getSourceId(), e);
				decryptionFailed = Boolean.TRUE;
			}
		}
		if (!decryptionFailed) {
			MessengerContact messengerContact = null;
			Date sentTime = new Date();
			if (messageRetry.getMcId() != null) {
				messengerContact = messengerContactService.findById(messageRetry.getMcId());
			}
			if (messengerContact == null) {
				throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
			}

			CustomerDTO customerDTO = contactService.findByIdNoCaching(messengerContact.getCustomerId());

			if (Objects.isNull(messengerContact) || Objects.isNull(businessDTO) || Objects.isNull(customerDTO)) {
				log.error(
						"retryEmailSend: MessengerContact {} or Business {} or Customer {} not found. Request info: emailId {}, mcId {}, accountId {}, userId {}, source {}",
						messengerContact, businessDTO, customerDTO, messageRetry.getId(), messageRetry.getMcId(),
						messageRetry.getAccountId(), messageRetry.getUserId(), messageRetry.getSourceId());
				return null;
			}

			// update email
			email.setSentOn(sentTime);
			email.setCreateDate(sentTime);
			email.setFailureReason(null);
			email.setToEmailId(customerDTO.getEmailId());
			String encryptedMessage = StringUtils.EMPTY;
			if (StringUtils.isNotBlank(message)) {
				try {
					encryptedMessage = EncryptionUtil.encrypt(message,
							StringUtils.join(String.valueOf(businessDTO.getBusinessNumber()),
									String.valueOf(messageRetry.getMcId())),
							StringUtils.join(String.valueOf(messageRetry.getMcId()),
									String.valueOf(businessDTO.getBusinessNumber())));
					email.setEncrypted(1);
				} catch (Exception e) {
					log.error(
							"emailMessageRetryClient: decryption failed request info: emailId {}, mcId {}, accountId {}, userId {}, source {}",
							email.getId(), messageRetry.getMcId(), messageRetry.getAccountId(),
							messageRetry.getUserId(), messageRetry.getSourceId(), e);
					return null;
				}
			}
			email.setMessageBody(encryptedMessage);

			UserDTO userDTO = userService.getUserDTO(messageRetry.getUserId());
			String lastMessageUserName = MessengerUtil.buildUserName(userDTO);

			// update messengerContact
			messengerContact.setLastResponseAt(sentTime);
			messengerContact.setUpdatedAt(sentTime);
			messengerContact.setLastMessage(encryptedMessage);
			LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
			lastMessageMetadataPOJO.setLastMessageType(MessageDocument.CommunicationDirection.SEND.name());
			lastMessageMetadataPOJO.setLastMessageUserName(lastMessageUserName);
			lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
			messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetadataPOJO));

			// DB save calls
			emailService.saveEmail(email);
			messengerContactService.saveOrUpdateMessengerContact(messengerContact);
			String encryptedLastMessage = messengerContact.getLastMessage();

			// update Contact Document
			ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
			contactBuilder.addLastMessageSenderDetail(userDTO, lastMessageMetadataPOJO.getLastMessageType());
			ContactDocument contactDocument = contactBuilder.build();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			String sentAtTime = df.format(sentTime);
			contactDocument.setUpdatedAt(sentAtTime);
			contactDocument.setLastMessageType(MessageDocument.CommunicationDirection.SEND.name());
			contactDocument.setL_msg(encryptedLastMessage); // It should be encrypted
			contactDocument.setLastMessageMetaData(lastMessageMetadataPOJO);
			contactDocument.setL_msg_on(sentAtTime);
			contactDocument.setEmailUnsubcribedReason(StringUtils.EMPTY);  // TODO check this field once

			MessageDocument.SentThrough sentThrough = messageRetry.getSourceId().equals(111)
					? MessageDocument.SentThrough.WEB
					: MessageDocument.SentThrough.MOBILE;

			// update Message Document
			MessageDocument messageDocument = new MessageDocument();
			messageDocument.setCr_date(sentAtTime);
			messageDocument.setUpdatedAt(sentAtTime);
			messageDocument.setMsg_status("success"); // Assuming email has successfully sent
			messageDocument.setType(MessageDocument.Type.UPDATE.getType());
			messageDocument.setSentThrough(sentThrough);
			messageDocument.setCreatedBy(new MessageDocument.UserDetail(userDTO.getId(), lastMessageUserName));
			messageDocument.setSource(Source.EMAIL.getSourceId());
			messageDocument.setIs_encrypt(email.getEncrypted());
			messageDocument.setMsg_body(encryptedMessage);
			messageDocument.setFrom(String.valueOf(businessDTO.getBusinessNumber()));
			messageDocument.setTo(String.valueOf(messageRetry.getMcId()));
			messageDocument.setU_id(messageRetry.getUserId());
			messageDocument.setU_name(lastMessageUserName);

			// ES save calls
			messengerContactService.updateContactDocumentOnES(contactDocument, String.valueOf(messengerContact.getId()),
					businessDTO.getRoutingId());
			messengerContactService.updateMessageDocument(messageDocument, String.valueOf(messageRetry.getId() + "_e"),
					businessDTO.getRoutingId());
			
			SendMessageDTO sendMessageDTO = new SendMessageDTO();
			ConversationDTO conversationDTO = null;
			sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
			sendMessageDTO.setCustomerId(customerDTO.getId());
			sendMessageDTO.setBody(encryptedLastMessage);
			sendMessageDTO.setUserDTO(userDTO);
			
			if (email.getTemplateId() == null) {
				EmailDTO emailDTO = populateEmailDto(userDTO, businessDTO, customerDTO, message,
						String.valueOf(messageRetry.getMcId()), String.valueOf(messageRetry.getId()), mediaFiles);
				String key="email-via-inbox-custom-template-"+businessDTO.getBusinessId();
				Integer isCustomTemplate = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(key, 0);
				emailDTO.setCustomTemplate(BooleanUtils.toBoolean(isCustomTemplate));
				sendMessageService.sendEmail(emailDTO);
				conversationDTO = new ConversationDTO(email, emailDTO);
				conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
				conversationDTO.setRecipient(String.valueOf(messageRetry.getMcId()));
				conversationDTO.setSentThrough(sentThrough);
				conversationDTO.setMessengerContactId(messengerContact.getId());
				sendMessageDTO.setConversationDTO(conversationDTO);
			} else {
				conversationDTO = new ConversationDTO(email);
				conversationDTO.setSentThrough(sentThrough);
				conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
				conversationDTO.setRecipient(String.valueOf(messageRetry.getMcId()));
				conversationDTO.setMessengerContactId(messengerContact.getId());
				conversationDTO.setTemplateId(sendMessageDTO.getTemplateId());
				sendMessageDTO.setConversationDTO(conversationDTO);
				EmailCampaignRequest campaignRequest = createCampaignRequest(email, getFromNameFromCoreService(
						businessDTO.getBusinessId(), businessDTO.getBusinessName(), userDTO.getName()),
						fetchFromEmailId(businessDTO.getBusinessName(), businessDTO.getBusinessId()),
						getReplyTo(businessDTO),userDTO.getEmailId());
				campaignService.createEmailCampaignRequest(campaignRequest);
			}
			sendMessageService.pushSendRequestToKafka(sendMessageDTO, MessengerEvent.EMAIL_SEND, userDTO, false);
			//fcmService.mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
			FirebaseDto firebaseDto = new FirebaseDto();
			firebaseDto.setAccountId(businessDTO.getAccountId());
			firebaseDto.setBusinessId(businessDTO.getBusinessId());
			firebaseDto.setMcId(messengerContact.getId());
			fcmService.mirrorOnWeb(firebaseDto);
			return new MessengerMessage.Message(userDTO, conversationDTO, MessengerEvent.EMAIL_SEND.name(), mediaFiles,
					businessDTO.getTimeZoneId());
		}
		log.info("retryEmailSend: could not decrypt the message to be sent");
		return null;

	}

	public EmailDTO populateEmailDto(UserDTO userDTO, BusinessDTO businessDTO, CustomerDTO customerDTO,
			String emailBody, String mcId,
			String emailId, List<MessengerMediaFile> mediaFiles) {
		EmailDTO emailDTO = new EmailDTO();
		emailDTO.setBusinessId(String.valueOf(businessDTO.getBusinessId()));
		emailDTO.setSubject(getEmailSubject(businessDTO.getBusinessName()));
		emailDTO.clearAndAddTo(customerDTO.getEmailId());
		emailDTO.setFrom(fetchFromEmailId(businessDTO.getBusinessName(), businessDTO.getBusinessId()));
		emailDTO.setReplyTo(getReplyTo(businessDTO));
		emailDTO.setRequestType(Constants.EMAIL_VIA_INBOX);
		emailDTO.setRequestSubType(Constants.EMAIL_VIA_INBOX);
		emailDTO.setEmailCategory(Constants.MARKETING);
		emailDTO.setFromName(getFromNameFromCoreService(businessDTO.getBusinessId(), businessDTO.getBusinessName(),
				userDTO.getName()));
		emailDTO.setBusinessNumber(businessDTO.getBusinessNumber());
		emailDTO.setS3AttachementUrls(getAttachmentS3Urls(mediaFiles));
		emailDTO.setFileAttachementData(getFileAttachment(mediaFiles));
		emailDTO.setExternalUid(emailId);
		emailDTO.setDataObject(buildSendEmailTemplate(emailBody, businessDTO.getBusinessName()));
		emailDTO.setTempBody(emailBody);
		emailDTO.setRecipientType(Constants.CUSTOM_RECIPIENT_TYPE);
		emailDTO.setIsCustomerEmail(1);
		emailDTO.setCustomerId(customerDTO.getId());
		return emailDTO;
	}

	private String getEmailSubject(String businessName) {
		return EmailMessageConstants.DEFAULT_SUBJECT.concat(businessName);
	}


	private String getfromId(String businessName) {
		String bname = MessengerUtil.formatBusinessNameWOCaseChange(businessName);
		return bname.concat(EmailMessageConstants.BIRDEYE_EMAIL_SUFFIX);
	}

	private String getReplyTo(BusinessDTO business) {
		
		BusinessOptionResponse businessOptionResponse = null;
		if(business.getBusinessId() != null) {
			businessOptionResponse=businessService.getBusinessOptionsConfig(business.getBusinessId(), true);
		}
	
		String replyDomain = businessOptionResponse == null || StringUtils.isBlank(businessOptionResponse.getInboxEmailReplyDomain()) ? emailReplyDomain : businessOptionResponse.getInboxEmailReplyDomain();
	
		String bname = MessengerUtil.formatBusinessNameWOCaseChange(business.getBusinessName());
		return bname.concat("-").concat(String.valueOf(business.getBusinessNumber())).concat(replyDomain);	
	}

	private String getFromName(String businessName, String userName) {
		String fromName = userName.concat(EmailMessageConstants.SPACE).concat(businessName)
				.concat(EmailMessageConstants.FROM_NAME_SUFFIX);
		return fromName;
	}

	private List<String> getAttachmentS3Urls(List<MessengerMediaFile> mediaFiles) {
		List<String> attachmentS3Urls = new ArrayList<String>();
		if (CollectionUtils.isNotEmpty(mediaFiles)) {
			for (MessengerMediaFile mediaFile : mediaFiles) {
				attachmentS3Urls.add(mediaFile.getUrl());
			}
		}
		return attachmentS3Urls;
	}

	private List<EmailFileAttachementData> getFileAttachment(List<MessengerMediaFile> mediaFiles) {
		List<EmailFileAttachementData> fileattachments = new ArrayList<EmailFileAttachementData>();
		if (CollectionUtils.isNotEmpty(mediaFiles)) {
			for (MessengerMediaFile mediaFile : mediaFiles) {
				EmailFileAttachementData file = new EmailFileAttachementData();
				byte[] encodedSBase64Byte = getFileByteArray(mediaFile);
				if (encodedSBase64Byte != null) {
					file.setContent(Base64.getDecoder().decode(encodedSBase64Byte));
				}
				file.setFileName(mediaFile.getName());
				file.setType(file.getType());
				fileattachments.add(file);
			}
		}
		return fileattachments;
	}

	private Map<String, String> buildSendEmailTemplate(String emailBody, String businessName) {
		Map<String, String> dataModel = new HashMap<>();
		String formattedBody = StringUtils.replace(emailBody, "\n", "<br />");
		dataModel.put("messageBody", formattedBody);
		dataModel.put("businessName", businessName);
		return dataModel;
	}

	private byte[] getFileByteArray(MessengerMediaFile mediaFile) {
		byte[] encodedSByteArray = StringUtils.EMPTY.getBytes();
		String s3Url = mediaFile.getUrl();
		String fileName = mediaFile.getName();
		String randomString = getRandomNumber(mediaFile.getMessageId());
		// creating temporary folder
		String localPath = getLocalPath(fileName, mediaFile.getMessageId(), randomString);
		encodedSByteArray = genrateByteArrayFromS3Url(s3Url, localPath, mediaFile.getMessageId(), randomString);
		// Deleting temporary created folder.
		if (encodedSByteArray != null) {
			deleteTempFolder(mediaFile.getMessageId(), randomString);
		}
		return encodedSByteArray;
	}

	private byte[] genrateByteArrayFromS3Url(String s3Url, String localPath, Integer msgId, String randomString) {
		byte[] encodedByteArray = null;
		try {
		URL url = new URL(s3Url);
		File file = new File(localPath);
		FileUtils.copyURLToFile(url, file);
		byte[] encodedString = Files.readAllBytes(Paths.get(localPath));
			encodedByteArray = Base64.getEncoder().encode(encodedString);
		} catch (Exception ex) {
			log.error("Exception occured while fetching file from s3Url for msgId {}.", msgId,
					ex);
		}
		return encodedByteArray;
	}

	private void deleteTempFolder(Integer msgId, String randomString) {
		try {
		String businessFolderName = MessengerConstants.TMP_FOLDER + msgId + "-" + randomString;
		FileUtils.deleteDirectory(new File(businessFolderName));
		log.info("Local directory deleted sucessfully for {}", businessFolderName);
		} catch (Exception ex) {
			log.error("Exception occured while fetching file deleting file for msgId {}.", msgId, ex);
		}
	}

	private String getRandomNumber(Integer mcId) {
		long currentTimeStamp = System.currentTimeMillis();
		return String.valueOf(mcId.longValue() + currentTimeStamp);
	}

	public String getLocalPath(String fileName, Integer msgId, String randomString) {
		String businessFolderName = MessengerConstants.TMP_FOLDER + msgId + "-" + randomString;
		File businessDirectory = new File(businessFolderName);
		String lockObject = BUSINESS_DIRECTORY_LOCK + msgId;
		if (!businessDirectory.exists()) {
			synchronized (lockObject.intern()) {
				if (!businessDirectory.exists()) {
					businessDirectory.mkdir();
					businessDirectory.setWritable(true, false);
				}
			}
		}
		log.info("Local directory created sucessfully at {}", businessFolderName);
		return businessFolderName + "/" + fileName;
	}

	private EmailCampaignRequest createCampaignRequest(Email email, String fromName,
			String fromEmailId, String replyToEmailId, String employeeEmailId) {
		EmailCampaignRequest campaignRequest = new EmailCampaignRequest(email.getTemplateId(),
				email.getBusinessId(), email.getId(), email.getCustomerId(),
				email.getSurveyId() != null ? email.getSurveyId() : null, fromName,
				replyToEmailId, StringUtils.isNotBlank(email.getTemplateType()) ? email.getTemplateType() : null,
				fromEmailId, employeeEmailId);
		return campaignRequest;
	}

	private EmailSenderDto getEmailSendDto(Integer businessId) {
		EmailSenderDto emailSenderInfo = businessService.getSenderEmailInfo(businessId);
		return emailSenderInfo;
	}

	private String getFromNameFromCoreService(Integer businessId, String businessName, String userName) {
		String fromName = userName.concat(EmailMessageConstants.SPACE).concat(businessName)
				.concat(EmailMessageConstants.FROM_NAME_SUFFIX);
		EmailSenderDto emailSenderInfo = getEmailSendDto(businessId);
		if (emailSenderInfo != null) {
			List<String> resellers = Arrays.asList("Cobranded", "Whitelabel");
			if (resellers.contains(emailSenderInfo.getAccountType())) {
				fromName = userName.concat(EmailMessageConstants.SPACE).concat(businessName).concat(" via ")
						.concat(emailSenderInfo.getSenderName());
			}
		}
		return fromName;
	}

	private String fetchFromEmailId(String businessName, Integer businessId) {
		EmailSenderDto emailSenderInfo = getEmailSendDto(businessId);
		String bname = MessengerUtil.formatBusinessNameWOCaseChange(businessName);
		String fromEmail = bname.concat(EmailMessageConstants.BIRDEYE_EMAIL_SUFFIX);
		if (emailSenderInfo != null) {
			List<String> resellers = Arrays.asList("Cobranded", "Whitelabel");
			if (resellers.contains(emailSenderInfo.getAccountType())) {
				String[] resellerEmailId = emailSenderInfo.getSenderEmail().split("@");
				fromEmail = bname.concat(resellerEmailId[1]);
			}
		}
		return fromEmail;
	}
}
