package com.birdeye.messenger.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.EnableInboxPerformance;
import com.birdeye.messenger.util.ControllerUtil;
import jakarta.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dao.entity.ErrorCodesMessage;
import com.birdeye.messenger.dao.entity.MessengerProperty;
import com.birdeye.messenger.dao.repository.ErrorCodesMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerPropertyRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ErrorCodesMessageRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CacheService;
import com.birdeye.messenger.service.QueroService;
import com.birdeye.messenger.sro.EnableAIChatbotRequest;
import com.birdeye.messenger.sro.MessengerPropertyRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service("cacheServiceimpl")
@Slf4j
public class CacheServiceImpl implements CacheService {

	@Autowired
	MessengerPropertyRepository messengerPropertyRepository;

	@Autowired
	ErrorCodesMessageRepository errorCodesMessageRepository;

	@Autowired
	private QueroService queroService;

	@Autowired
	private BusinessService businessService;

	// At second :00 of minute :51 of every hour
	private static final String AUTO_RELOAD_CACHE_CRON_PATTERN= "0 51 * * * *";

	@Override
	public Map<String, String> getCacheValues() {
		log.info("Get All MessengerProperty from Cache");
		Map<String, String> cacheValueMap = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAllProperty();
		return cacheValueMap;
	}

	@Override
	public void addCacheValue(MessengerPropertyRequest messengerPropertyRequest) {
		MessengerProperty existingProperty = messengerPropertyRepository.findByName(messengerPropertyRequest.getName());
		if (ObjectUtils.isEmpty(existingProperty)) {
			//create new messenger_property
			log.info("Create New MessengerProperty. No MessengerProperty found in messenger_property table {}", messengerPropertyRequest.getName());
			MessengerProperty newProperty = new MessengerProperty(messengerPropertyRequest.getName(), messengerPropertyRequest.getValue());
			messengerPropertyRepository.saveAndFlush(newProperty);
		} else {
			//update value of existing messenger_property
			log.info("Update MessengerProperty. MessengerProperty found in messenger_property table {}", messengerPropertyRequest.getName());
			if (!existingProperty.getValue().contains(messengerPropertyRequest.getValue())) {
				String value = existingProperty.getValue().concat(",").concat(messengerPropertyRequest.getValue());
				existingProperty.setValue(value);
				existingProperty.setUpdatedAt(new Date());
				messengerPropertyRepository.saveAndFlush(existingProperty);
			} else {
				log.info("Update MessengerProperty. Value already present : {}", messengerPropertyRequest.getName());
			}
		}
		loadMessengerPropertyCache();
	}

	@PostConstruct
	public void init() {
		loadMessengerPropertyCache();
	}

	@Scheduled(cron = AUTO_RELOAD_CACHE_CRON_PATTERN)
	@Override
	public void loadMessengerPropertyCache() {
		log.info("STARTED loading MessengerProperty and ErrorCodes cache from messenger_property and error_codes_message table");
		List<MessengerProperty> systemPropertyroperties = messengerPropertyRepository.findAll();
		List<ErrorCodesMessage> errorCodesMessages = errorCodesMessageRepository.findAll();
		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertyroperties.stream().filter(systemProperty -> systemProperty.getName() != null && systemProperty.getValue() != null)
				.forEach(systemProperty -> systemPropertiesCache.addProperty(systemProperty.getName(), systemProperty.getValue()));
		errorCodesMessages.stream().filter(errorCodesMessage -> errorCodesMessage.getErrorCode() != null && errorCodesMessage.getErrorMessage() != null)
				.forEach(errorCodesMessage -> systemPropertiesCache.addProperty(errorCodesMessage.getErrorCode(), errorCodesMessage.getErrorMessage()));
		CacheManager.getInstance().setCache(systemPropertiesCache);
		log.info("ENDED loading MessengerProperty and ErrorCodes cache");
	}


	@Override
	public void loadAllCache() {
		loadMessengerPropertyCache();
	}

	@Override
	public void addErrorCodes(List<ErrorCodesMessageRequest> request){
		for(ErrorCodesMessageRequest errorCode : request){
			ErrorCodesMessage existingErrorCode = errorCodesMessageRepository.findByErrorCode(errorCode.getErrorCode());
			if(ObjectUtils.isEmpty(existingErrorCode)){
				//add new errorCode
				log.info("add New errorCode. No errorMessage found for errorCode {}",errorCode.getErrorCode());
				ErrorCodesMessage errorCodesMessage=new ErrorCodesMessage(errorCode.getErrorCode(),errorCode.getErrorMessage());
				errorCodesMessageRepository.saveAndFlush(errorCodesMessage);
			}else{
				//update message of existing errorCode
				log.info("Update errorCode. errorMessage found in error_codes_message table {}",errorCode.getErrorCode());
				existingErrorCode.setErrorMessage(errorCode.getErrorMessage());
				existingErrorCode.setUpdatedAt(new Date());
				errorCodesMessageRepository.saveAndFlush(existingErrorCode);
			}
		}
		loadMessengerPropertyCache();
	}

	@Override
	public void deleteCacheValue(MessengerPropertyRequest messengerPropertyRequest) {
		MessengerProperty existingProperty = messengerPropertyRepository.findByName(messengerPropertyRequest.getName());
		if (!ObjectUtils.isEmpty(existingProperty)) {
			//create new messenger_property
			log.info("deleteCacheValue {}", existingProperty);
			messengerPropertyRepository.delete(existingProperty);
		}
		loadMessengerPropertyCache();
	}

	@Override
	public void enableAIChatbot(EnableAIChatbotRequest enableAIChatbotRequest) throws Exception {
		log.info("enableAIChatbot request : {}", enableAIChatbotRequest);
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO("businessNumber", String.valueOf(enableAIChatbotRequest.getAccountNumber()));
		if (businessDTO == null) {
			throw new MessengerException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		Integer inboxFeature = null;
		Integer webchatFeature = null;
		Integer chatbotFeature = null;
		BusinessOptionResponse businessOptions = businessService.getBusinessOptionsConfig(businessDTO.getBusinessId(), true);
		if (businessOptions!= null){
			inboxFeature = businessOptions.getEnableMessenger();
			webchatFeature = businessOptions.getEnableChatWidget();
			chatbotFeature = businessOptions.getChatbot();
		}

		if(inboxFeature==null || Integer.valueOf(0).equals(inboxFeature) || webchatFeature==null || Integer.valueOf(0).equals(webchatFeature)
				|| chatbotFeature==null || Integer.valueOf(0).equals(chatbotFeature)){
			throw new MessengerException(ErrorCode.INBOX_OR_WEBCHAT_NOT_ENABLED);
		}
		MessengerPropertyRequest messengerPropertyRequest = new MessengerPropertyRequest();
		messengerPropertyRequest.setName("accounts_with_gpt_reply_enabled");
		messengerPropertyRequest.setValue(String.valueOf(businessDTO.getBusinessId()));
		addCacheValue(messengerPropertyRequest);

		boolean isSMB = checkBusinessSMB(businessDTO);
		Integer userId = 1575523; //hardcoded userId for prod
		if (Objects.nonNull(enableAIChatbotRequest.getUserId())) {
			userId = enableAIChatbotRequest.getUserId();
		}

		queroService.queroRobinIngestion(businessDTO.getBusinessId(), userId, isSMB);
		queroService.queroDefaultFAQCreation(businessDTO.getBusinessId(), userId, isSMB);
		queroService.queroProfileLinkAndZeroStateMigration(businessDTO.getBusinessId(), userId, isSMB);
		log.info("enableAIChatbot successful");
	}

	private boolean checkBusinessSMB(BusinessDTO business) {
		return ("Business".equals(business.getType()) || "Product".equals(business.getType()));
	}

	@Override
	public void enableAIPerformance(EnableInboxPerformance enableInboxPerformance) throws Exception {
		log.info("enableInboxPerformance request : {}", enableInboxPerformance);
		if (Objects.nonNull(enableInboxPerformance)) {
			BusinessDTO businessDTO = businessService.getBusinessDTO(enableInboxPerformance.getAccountId());
			if (businessDTO == null) {
				throw new MessengerException(ErrorCode.BUSINESS_NOT_FOUND);
			}
			Integer inboxFeature = null;
			BusinessOptionResponse businessOptions = businessService.getBusinessOptionsConfig(businessDTO.getBusinessId(), true);
			if (businessOptions != null) {
				inboxFeature = businessOptions.getEnableMessenger();
			}

			if (inboxFeature == null || new Integer(0).equals(inboxFeature)) {
				throw new MessengerException(ErrorCode.INBOX_OR_WEBCHAT_NOT_ENABLED);
			}

			String inboxPerformanceImprovementEnabledAccount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("accounts_with_inbox_performance_improvement", "261968");
			List<String> inboxPerformanceImprovementEnabledAccountList = ControllerUtil.getTokensListFromString(inboxPerformanceImprovementEnabledAccount);

			if (Objects.nonNull(enableInboxPerformance.getEnable()) && enableInboxPerformance.getEnable() == 0) {
				inboxPerformanceImprovementEnabledAccountList.remove(String.valueOf(businessDTO.getAccountId()));
				MessengerPropertyRequest messengerPropertyRequest = new MessengerPropertyRequest();
				messengerPropertyRequest.setName("accounts_with_inbox_performance_improvement");
				messengerPropertyRequest.setValue(String.join(",", inboxPerformanceImprovementEnabledAccountList));
				MessengerProperty existingProperty = messengerPropertyRepository.findByName(messengerPropertyRequest.getName());
				messengerPropertyRepository.delete(existingProperty);
				loadMessengerPropertyCache();
				addCacheValue(messengerPropertyRequest);
			}else if(Objects.nonNull(enableInboxPerformance.getEnable()) && enableInboxPerformance.getEnable() == 1){
				MessengerPropertyRequest messengerPropertyRequest = new MessengerPropertyRequest();
				messengerPropertyRequest.setName("accounts_with_inbox_performance_improvement");
				messengerPropertyRequest.setValue(String.valueOf(businessDTO.getAccountId()));
				addCacheValue(messengerPropertyRequest);
			}
		}
		log.info("enableInboxPerformance successful");
	}
}