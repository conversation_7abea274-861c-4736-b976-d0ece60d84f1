package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.WhatsappMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessageResponse.Message;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.SendWAMessageResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WhatsappMessageStatusEnum;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.WhatsappException;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WhatsappSendEventHandler extends MessageEventHandlerAbstract {

	private final MessengerEvent EVENT = MessengerEvent.WHATSAPP_SEND;

	private final PulseSurveyService pulseSurveyService;
	
	private final WhatsappMessageService whatsappMessageService;
	
	private final MessengerMessageService messengerMessageService;
	
	private final MessengerMediaFileService messengerMediaFileService;
	
	private final IWhatsAppTemplateService whatsAppTemplateService;
	
	private final SendMessageService sendMessageService;
	

	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	public SendResponse handle(MessageDTO messageDTO) throws Exception {
		if(!messageDTO.isBOT()) messageDTO.setMsgTypeForResTimeCalc("S");
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        messageDTO.setBusinessDTO(businessDTO);
        MessengerContact messengerContact=getMessengerContact(messageDTO);
        messageDTO.setMessengerContact(messengerContact);
        SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);

		SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages);
		
		WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(messengerContact, businessDTO.getAccountId());
		sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
		sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
		
		sendResponse.setLastMsgSource(messageDTO.getSource());
		sendResponse.setMessages(messages);
		
		// handle PulseSurveyContext
		PulseSurveyContext context = null;
		try {
			context = pulseSurveyService.handlePulseSurveyContext(null, messageDTO.getCustomerDTO(), businessDTO);
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		ContactDocument contactDocument = new ContactDocument();
		if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
			contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
		} else {
			contactDocument.setOngoingPulseSurvey(false);
		}
		messengerContactService.updateContactOnES(messageDTO.getMessengerContact().getId(), contactDocument, messageDTO.getBusinessDTO().getAccountId());
		return sendResponse;
	}

	private SortedSet<Message> sendMessage(MessageDTO messageDTO) throws Exception {
        getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
//        commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO),"whatsapp"); //This is not required for whatsapp, as the templates are external
        sendMessageDTO.setSource(Source.WHATSAPP.getSourceId());
        sendMessageDTO.setFromBusinessId(getMessengerContact(sendMessageDTO).getBusinessId());
        MessengerEvent event = MessengerEvent.SMS_SEND;
        SortedSet<SendResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
        if (StringUtils.isNotBlank(sendMessageDTO.getMediaurl())
                || CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
            event = MessengerEvent.MMS_SEND;
        }
        if (event.equals(MessengerEvent.MMS_SEND)) {
            log.info("Processing WA attachment SEND for contact {} ",sendMessageDTO.getToCustomerId());
            // Process mms data.
            sendMessageDTO.setMessengerMediaFiles(new ArrayList<MessengerMediaFile>());
            String text=sendMessageDTO.getBody();
            for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
                MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
                sendMessageDTO.setMediaurl(mediaFile.getUrl());
//                sendMessageDTO.setMediaId(sendMessageDTO.getMediaIds().get(media));
                if (media < sendMessageDTO.getMediaUrls().size()) {
                    sendMessageDTO.setBody(null);
                }
                MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
                messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
                sendMessageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
                if(media!=0) {
                    messageDTO.setPartOfExistingMessage(true);
                }
                if(StringUtils.isNotEmpty(text) && media == sendMessageDTO.getMediaUrls().size() - 1){
                    sendMessageDTO.setBody(text);
                    text = null;
                }
                sendWAMessage(sendMessageDTO, event);
            }
        } else {
            log.info("Processing WA TEXT SEND for contact {} ",sendMessageDTO.getToCustomerId());
            sendWAMessage(sendMessageDTO, event);
        }

        super.handle(sendMessageDTO);
        MessageResponse.Message message = new SendResponse.Message(sendMessageDTO.getUserDTO(), sendMessageDTO.getConversationDTO(), sendMessageDTO.getMessengerMediaFiles(), messageDTO.getBusinessDTO().getTimeZoneId());
        message.setWaMsgHeader(sendMessageDTO.getWaMsgHeader());
        message.setWaMsgFooter(sendMessageDTO.getWaMsgFooter());
        message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
        robinService.updateChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), sendMessageDTO.getUserDTO(), Source.FACEBOOK.name());
        messages.add(message);
        return messages;
    }

	private void sendWAMessage(MessageDTO messageDTO, MessengerEvent event) throws Exception {
		//String pageId =platformDbRepository.getFacebookPageIdByBusinessId(getBusinessDTO(messageDTO).getBusinessId());
		String waPhoneNoId = whatsAppTemplateService.getWAPhoneNumberIdByBusinessId(messageDTO.getBusinessDTO().getBusinessId());
		String custWAId = messageDTO.getMessengerContact().getWhatsappConversationId();
		//1. send WA message using social service
		SendWAMessageResponse sendWAMessageResponse = whatsappMessageService.sendWACallToSocial(messageDTO, waPhoneNoId);
		if (Objects.isNull(sendWAMessageResponse) || CollectionUtils.isEmpty(sendWAMessageResponse.getMessages())) {
			throw new WhatsappException(new ErrorMessageBuilder(ErrorCode.WHATSAPP_API_ERROR, ComponentCodeEnum.WHATSAPP, HttpStatus.BAD_REQUEST));
		}
		//2. save messge in db
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		UserDTO userDTO=getUserDTO(messageDTO);
		WhatsappMessage whatsappMessage = whatsappMessageService.saveWhatsappMessage(messageDTO, waPhoneNoId, custWAId,
				sendWAMessageResponse.getMessages().get(0).getId(), WhatsappMessageStatusEnum.sent);
		MessengerMessageMetaData messageMetaData=new MessengerMessageMetaData();
		messageMetaData.setSentThrough(SentThrough.WEB);
		messageMetaData.setCommunicationDirection(CommunicationDirection.SEND);
		ConversationDTO conversationDTO = new ConversationDTO(whatsappMessage, messageMetaData);
		log.info("Messenger contact Id {} ,conversation Id: {}  ", messageDTO.getMessengerContact().getId(),conversationDTO.getId());
		messengerMessageService.saveMessengerMessage(conversationDTO,userDTO);		
		messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), whatsappMessage.getId());
		if(Objects.nonNull(messageDTO.getMessengerMediaFileDTO())) {
			MessengerMediaFile  messengerMediaFile=new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO());
			messengerMediaFile.setExtension(messageDTO.getMessengerMediaFileDTO().getFileExtension());
			messengerMediaFile.setMessageId(whatsappMessage.getId());
			messageDTO.getMessengerMediaFiles().add(messengerMediaFile);
		}
		sendMessageDTO.setConversationDTO(conversationDTO);
		sendMessageService.pushSendRequestToKafka(sendMessageDTO,event,userDTO,false);
	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		if (Objects.isNull(businessDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
			businessDTO = communicationHelperService.getBusinessDTO(businessId);
			dto.setBusinessDTO(businessDTO);
		}
		return businessDTO;
	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (Objects.isNull(customerDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			///customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
			customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
			dto.setCustomerDTO(customerDTO);
		}
		return customerDTO;
	}

	UserDTO getUserDTO(MessageDTO messageDTO) {
		UserDTO userDTO = messageDTO.getUserDTO();
		if (Objects.isNull(userDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			userDTO = communicationHelperService.getUserDTO(dto.getUserId());
			dto.setUserDTO(userDTO);
		}
		return userDTO;
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		return MessageTag.INBOX;
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		SendMessageDTO dto = (SendMessageDTO) messageDTO;
		MessageDocumentDTO messageDocumentDTO= new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
		if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
			messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
					.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
							messengerMediaFile.getExtension(),messengerMediaFile.getMessageId().toString()))
					.collect(Collectors.toList()));
		}
		messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
		messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
		messageDocumentDTO.setWaMsgHeader(dto.getWaMsgHeader());
		messageDocumentDTO.setWaMsgFooter(dto.getWaMsgFooter());
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		return messageDocumentDTO;
	}
	
	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.WHATSAPP.name());
		lastMessageMetadataPOJO.setLastMessageSource(Source.WHATSAPP.getSourceId());
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		SendMessageDTO  dto = (SendMessageDTO) messageDTO;
		messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
		messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
		messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
            messengerContact.setLastMessage("Sent an attachment");
        } else {
            messengerContact.setLastMessage(sendMessageDTO.getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), messengerContact.getWhatsappConversationId(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
			messageDTO.setMessengerContact(messengerContact);
		}
		if(messengerContact.getBlocked()) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.CONTACT_IS_BLOCKED, ComponentCodeEnum.CUSTOMER, HttpStatus.BAD_REQUEST));

		}
		return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		//No email notification for send
		return null;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if (Objects.isNull(messageId)) {
			WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
			messageId = dto.getConversationDTO().getId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}
}
