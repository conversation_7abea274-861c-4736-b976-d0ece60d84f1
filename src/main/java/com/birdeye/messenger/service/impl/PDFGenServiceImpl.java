/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.util.Collections;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.service.PDFGenService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PDFGenServiceImpl implements PDFGenService {

	@Value("${pdf.gen.service.url}")
	private String pdfGenUrl;

	private final RestTemplate restTemplate;

	@Override
	public Map<String, Object> getRichLinkDataFromUrl(String url) {
		ResponseEntity<Map<String,Object>> richLinkData=null;
		String requestUrl=pdfGenUrl+"/urlMetadata?url="+url;
		HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity request=new HttpEntity<>(headers);
		richLinkData = restTemplate.exchange(requestUrl, HttpMethod.GET, request,
				new ParameterizedTypeReference<Map<String, Object>>() {
			});
		if (richLinkData.getStatusCode().is2xxSuccessful()) {
			log.info("richlink data : {} fetched for url : {}", richLinkData.getBody(), url);
			return richLinkData.getBody() != null ? richLinkData.getBody() : null;
		} else {
			log.error("error fetching richlink data for url : {}", url);
			return null;
		}
	}

}
