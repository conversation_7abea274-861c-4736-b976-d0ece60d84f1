package com.birdeye.messenger.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dao.repository.TeamConfigForRoundRobinRepository;
import com.birdeye.messenger.service.TeamConfigForRoundRobinRepositoryService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class TeamConfigForRoundRobinRepositoryServiceImpl implements TeamConfigForRoundRobinRepositoryService{

    @Autowired
    private TeamConfigForRoundRobinRepository teamConfigForRoundRobinRepository;
    
    
    @Override
    @CachePut(cacheNames = Constants.ROUND_ROBIN_ACCOUNTS_CACHE, key="'ACCID-'.concat(#accountId)", unless="#result == null")
    public TeamConfigForRoundRobin saveTeamConfigForRoundRobin(Integer accountId, TeamConfigForRoundRobin teamConfigForRoundRobin) {
    	return teamConfigForRoundRobinRepository.save(teamConfigForRoundRobin);
    }

    @Override
    public void deleteTeamConfigForRoundRobin(Integer accountId) {
        teamConfigForRoundRobinRepository.deleteByAccountId(accountId);
    }

    @Override
    public TeamConfigForRoundRobin findTeamConfigForRoundRobin(Integer accountId, Integer teamId) {
        return teamConfigForRoundRobinRepository.findByAccountIdAndTeamId(accountId, teamId);
    }

    @Override
    @Cacheable(cacheNames = Constants.ROUND_ROBIN_ACCOUNTS_CACHE, key="'ACCID-'.concat(#accountId)", unless="#result == null")
    public TeamConfigForRoundRobin findTeamConfigForRoundRobinByAccountId(Integer accountId) {
        return teamConfigForRoundRobinRepository.findByAccountId(accountId);
    }
    
    @Override
    @CacheEvict(cacheNames = Constants.ROUND_ROBIN_ACCOUNTS_CACHE, key="'ACCID-'.concat(#accountId)")
    public void evictTeamConfigCache(Integer accountId) {
    	log.info("Clearing TeamConfigCache for accountId {} ", accountId);
    }
}
