package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomChannelMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomChannelDTO;
import com.birdeye.messenger.dto.CustomChannelMessageDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.CustomChannelMessageService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * Event handler for custom channel messages
 */
@Service
@Slf4j
public class CustomChannelReceiveEventHandler extends MessageEventHandlerAbstract {

	private final MessengerEvent EVENT = MessengerEvent.CUSTOM_CHANNEL_RECEIVE;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private RedisLockService redisLockService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private CustomChannelMessageService customChannelMessageService;

	@Autowired
	private MessengerMediaFileService messengerMediaFileService;

	@Autowired
	private NLPService nlpService;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		return ((CustomChannelMessageDTO) messageDTO).getBusinessDTO();
	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {

		CustomChannelMessageDTO customChannelMessageDTO = (CustomChannelMessageDTO) messageDTO;

		if (customChannelMessageDTO.getCustomerDTO() != null) {
			return customChannelMessageDTO.getCustomerDTO();
		}

		List<Integer> accountIds = contactService.getAllContactUpgradeEnabledAccounts();

		KontactoRequest kontactoRequest = create(customChannelMessageDTO, getBusinessDTO(customChannelMessageDTO));

		// Check for active customer in upgrade enabled accounts
		if (Objects.isNull(customChannelMessageDTO.getCustomerDTO()) && CollectionUtils.isNotEmpty(accountIds)
				&& messageDTO.getBusinessDTO() != null
				&& accountIds.contains(messageDTO.getBusinessDTO().getAccountId())) {
			CustomerDTO customerDTO = commonService.getActiveCustomerBySource(kontactoRequest.getPhone(),
					kontactoRequest.getEmailId(), kontactoRequest.getName(),
					customChannelMessageDTO.getBusinessDTO().getBusinessId(),
					customChannelMessageDTO.getBusinessDTO().getAccountId(), kontactoRequest.getSource());
			if (Objects.nonNull(customerDTO)) {
				customChannelMessageDTO.setCustomerDTO(customerDTO);
			}
		}

		// If still no customer, create new one
		if (Objects.isNull(customChannelMessageDTO.getCustomerDTO())
				|| Objects.isNull(customChannelMessageDTO.getCustomerDTO().getBusinessId())) {
			CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
					getBusinessDTO(customChannelMessageDTO).getRoutingId(), -1);

			if (Objects.isNull(customerDTO) || Objects.isNull(customerDTO.getId()))
				throw new NotFoundException("Customer data not returned by contact-service for Custom message receive");
			customChannelMessageDTO.setCustomerDTO(customerDTO);
		}
		return customChannelMessageDTO.getCustomerDTO();
	}

	private KontactoRequest create(CustomChannelMessageDTO customChannelMessageDTO, BusinessDTO businessDTO) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setName(customChannelMessageDTO.getName());
		kontactoRequest.setPhone(customChannelMessageDTO.getPhone());
		kontactoRequest.setEmailId(customChannelMessageDTO.getEmail());
		kontactoRequest.setBusinessId(businessDTO.getBusinessId());
		kontactoRequest.setSource(KontactoRequest.API);
		KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
		locationInfo.setCountryCode(businessDTO.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		if (getSendEmailNotification(messageDTO)) {
			return MessageTag.UNREAD;
		} else
			return MessageTag.INBOX;
	}

	@Override
	protected boolean getSendEmailNotification(MessageDTO messageDTO) {
		CustomChannelMessageDTO customChannelMessageDTO = (CustomChannelMessageDTO) messageDTO;
		if (customChannelMessageDTO.getMessengerContact().getSpam().equals(true)) {
			return false;
		}
		customChannelMessageDTO.setSendEmailNotification(true);
		return true;
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			CustomerDTO customerDTO = getCustomerDTO(messageDTO);
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), customerDTO.getId(), businessDTO.getAccountId());
			messageDTO.setMessengerContact(messengerContact);
			if (BooleanUtils.isTrue(messengerContact.getIsNew())) {
				// We want to update our contact doc the very first time with customChannel when
				// leadSource is custom.
				CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
				customerDTO.setCustomChannel(dto.getCustomChannel());
			}
		}
		return messengerContact;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if (Objects.isNull(messageId)) {
			CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
			messageId = dto.getCustomChannelDTO().getId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
		// No specific event to publish for custom channel messages
	}

	@Override
	protected boolean isFireBaseSyncEnabled(MessageDTO messageDTO) {
		if (messageDTO.getMessengerContact().getSpam().equals(true)) {
			return Boolean.FALSE;
		}
		return Boolean.TRUE;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);

		lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.CUSTOM.name());
		lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.CUSTOM.getSourceId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.CUSTOM.getSourceId());

		// Set the custom channel - already lowercase from DTO
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
		lastMessageMetadataPOJO.setLastMessageCustomChannel(dto.getCustomChannel());

		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

		Date lastMsgOn = dto.getTimestamp();
		messengerContact.setLastMsgOn(lastMsgOn);
		messengerContact.setUpdatedAt(lastMsgOn);
		messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
		ConversationDTO conversationDTO = dto.getConversationDTO();

		if (StringUtils.isEmpty(dto.getBody()) && CollectionUtils.isNotEmpty(dto.getMediaUrl())) {
			messengerContact.setLastMessage("Received an attachment");
		} else {
			messengerContact.setLastMessage(dto.getBody());
		}

		// Set encryption for the message
		// Using the same approach as EmailReceiveEventHandler - using
		// conversationDTO.getRecipient() and conversationDTO.getSender()
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact,
				dto.getCustomChannelDTO().getEncrypted(), conversationDTO.getRecipient(), conversationDTO.getSender());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	/**
	 * Validate the custom channel message
	 *
	 * @param customChannelMessageDTO The message DTO to validate
	 * @throws Exception If validation fails
	 */
	private void validate(CustomChannelMessageDTO customChannelMessageDTO) throws Exception {
		// Check for profanity
		if (StringUtils.isNotBlank(customChannelMessageDTO.getBody())
				&& nlpService.isTextProfane(customChannelMessageDTO.getBody())) {
			log.info("CustomChannelReceiveHandler: Input message not saved due to profanity filtering for business: {}",
					customChannelMessageDTO.getBusinessId());
			throw new InputValidationException("Message not saved as it qualified as profane");
		}
	}

	@Override
	public MessageResponse handle(MessageDTO messageDTO) throws Exception {
		CustomChannelMessageDTO customChannelMessageDTO = (CustomChannelMessageDTO) messageDTO;
		log.info("Custom channel receive request for channel: {}, businessId: {}",
				customChannelMessageDTO.getCustomChannel(), customChannelMessageDTO.getBusinessId());

		// Validate the message (check for profanity)
		validate(customChannelMessageDTO);

		messageDTO.setMsgTypeForResTimeCalc("R");

		messageDTO.setSource(Source.CUSTOM.getSourceId());

		BusinessDTO businessDTO = customChannelMessageDTO.getBusinessDTO();
		if (Objects.isNull(businessDTO)) {
			log.error("Business not found for ID: {}", customChannelMessageDTO.getBusinessId());
			throw new InputValidationException(ErrorCode.NO_BUSINESS_FOUND);
		}

		Integer isMessengerEnabled = businessService.isMessengerEnabled(businessDTO.getAccountId());
		if (isMessengerEnabled != 1) {
			log.error("Messenger is not enabled for account: {}", businessDTO.getAccountId());
			throw new InputValidationException(ErrorCode.MESSENGER_DISABLED);
		}

		// Get or create customer using the getCustomerDTO method
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);

		customChannelMessageDTO.setCustomerId(customerDTO.getId());
		customChannelMessageDTO.setBusinessId(businessDTO.getBusinessId());

		Optional<Lock> lockOpt = Optional.empty();

		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);

			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key: {}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOM_CHANNEL_RECEIVE_EVENT_DELAYED_QUEUE,
						customChannelMessageDTO);
				return null;
			}

			processCustomChannelMessage(messageDTO);

			// Publishing activity if channel is changed
			publishActivity(messageDTO);

			// Check for Pulse Survey context similar to other handlers
			try {
				// Handle pulse survey context
				PulseSurveyContext context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO,
						businessDTO);
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())) {
					customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					customerDTO.setOngoingPulseSurvey(false);
				}
				messageDTO.setCustomerDTO(customerDTO);
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}

			return super.handle(messageDTO);
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	/**
	 * Process a custom channel message
	 *
	 * @param messageDTO The message DTO
	 * @throws Exception If an error occurs
	 */
	private void processCustomChannelMessage(MessageDTO messageDTO) throws Exception {
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;

		MessengerContact messengerContact = getMessengerContact(dto);
		// Save the custom channel message to the database
		CustomChannelMessage customChannelMessage = customChannelMessageService.saveCustomChannelMessage(dto,
				String.valueOf(getBusinessDTO(messageDTO).getBusinessNumber()),
				String.valueOf(messengerContact.getId()),
				"RECEIVE");

		// Create a new conversation DTO
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setChannel(Channel.CUSTOM);
		conversationDTO.setCommunicationDirection(CommunicationDirection.RECEIVE);
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
		conversationDTO.setSender(String.valueOf(getBusinessDTO(messageDTO).getBusinessNumber())); // For encryption
		conversationDTO.setRecipient(String.valueOf(getMessengerContact(messageDTO).getId())); // For encryption
		conversationDTO.setCustomChannel(dto.getCustomChannel());
		// Set dates from the saved message
		conversationDTO.setCreateDate(customChannelMessage.getCreateDate());
		conversationDTO.setSentOn(customChannelMessage.getSentOn());
		// Encrypted body
		conversationDTO.setBody(customChannelMessage.getMessageBody());
		conversationDTO.setSubject(dto.getEmailSubject());
		conversationDTO.setEncrypted(customChannelMessage.getEncrypted());
		conversationDTO.setSource(Source.CUSTOM.getSourceId());

		dto.setConversationDTO(conversationDTO);
		messageDTO.setMessageId(customChannelMessage.getId());

		// Create a CustomChannelDTO from the CustomChannelMessage and ConversationDTO
		CustomChannelDTO customChannelDTO = new CustomChannelDTO(customChannelMessage, conversationDTO,
				messengerContact);

		dto.setCustomChannelDTO(customChannelDTO);

		// Save the messenger message using the CustomChannelDTO
		messengerMessageService.saveMessengerMessage(customChannelDTO, null);

		// Save media files if mediaUrls are present
		saveMediaFiles(messageDTO, customChannelMessage);
	}

	private void saveMediaFiles(MessageDTO messageDTO, CustomChannelMessage customChannelMessage) {
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
		CustomChannelDTO customChannelDTO = dto.getCustomChannelDTO();

		// Save media files if mediaUrls are present
		if (CollectionUtils.isNotEmpty(dto.getMediaUrl())) {
			log.info("Saving media files for custom channel message with URLs: {}", dto.getMediaUrl());

			List<MessengerMediaFile> savedMediaFiles = new ArrayList<>();
			for (String mediaUrl : dto.getMediaUrl()) {
				// Create a MessengerMediaFile using the mediaUrl constructor
				MessengerMediaFile mediaFile = new MessengerMediaFile(mediaUrl);
				// Set the message ID to the custom channel message ID
				mediaFile.setMessageId(customChannelMessage.getId());
				// Save each media file individually using saveMedia
				MessengerMediaFile savedMediaFile = messengerMediaFileService.saveMedia(mediaFile);
				savedMediaFiles.add(savedMediaFile);
			}

			// Store the saved media files in the DTO
			customChannelDTO.setSavedMediaFiles(savedMediaFiles);

			log.info("Media files saved with IDs: {}",
					savedMediaFiles.stream().map(MessengerMediaFile::getId).collect(Collectors.toList()));
		}
	}

	@Override
	protected MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
		notificationRequest.setMsgId(dto.getConversationDTO().getId());
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(10); // number of messages to be fetched from ES

		// The creation time is the last received time if last delivery time is null
		if (dto.getConversationDTO().getCreateDate() != null) {
			notificationRequest.setLastMsgTime(dto.getConversationDTO().getCreateDate().getTime());
		} else {
			notificationRequest.setLastMsgTime(new Date().getTime());
			log.info(
					"CustomChannelReceiveHandler: Both sentOn and createDate found null for businessId {} messageID {} customer {}",
					businessDTO.getBusinessId(), dto.getConversationDTO().getId(), dto.getCustomerId());
		}
		notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
		return notificationRequest;
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		CustomChannelMessageDTO dto = (CustomChannelMessageDTO) messageDTO;
		CustomChannelDTO customChannelDTO = dto.getCustomChannelDTO();

		ConversationDTO conversationDTO = dto.getConversationDTO();
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(conversationDTO,
				getMessengerContact(messageDTO).getId());

		// Set the message ID from the CustomChannelMessage
		messageDocumentDTO.setM_id(String.valueOf(dto.getMessageId()));

		// Add media files if present
		if (customChannelDTO != null && CollectionUtils.isNotEmpty(customChannelDTO.getSavedMediaFiles())) {
			List<MessageDocument.MediaFile> docMediaFiles = customChannelDTO.getSavedMediaFiles().stream()
					.map(mediaFile -> new MessageDocument.MediaFile(mediaFile.getExtension(), mediaFile.getUrl(),
							mediaFile.getContentSize(), mediaFile.getName(), mediaFile.getContentType()))
					.collect(Collectors.toList());
			messageDocumentDTO.setMediaFiles(docMediaFiles);
		}

		// Set spam flag based on customer blocked status
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		if (customerDTO != null && Boolean.TRUE.equals(customerDTO.getBlocked())) {
			messageDocumentDTO.setSpam(true);
		} else {
			messageDocumentDTO.setSpam(false);
		}

		// Set the message document DTO on the message DTO
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);

		return messageDocumentDTO;
	}

	/**
	 * Add activity if channel has changed
	 *
	 * @param messageDTO The message DTO
	 */
	private void addActivityIfChannelChanged(MessageDTO messageDTO) {
		CustomChannelMessageDTO customChannelMessageDTO = (CustomChannelMessageDTO) messageDTO;
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);

		String lastChannel = lastMessageMetadataPOJO.getLastMessageChannel();
		String lastCustomChannel = lastMessageMetadataPOJO.getLastMessageCustomChannel();

		log.info("[addActivityIfChannelChanged] last channel: {}, last custom channel: {}", lastChannel,
				lastCustomChannel);

		// Check if channel has changed or if custom channel has changed
		// Both channels are lowercase, so we can use equals instead of equalsIgnoreCase
		if (lastChannel == null || !MessageDocument.Channel.CUSTOM.name().equals(lastChannel)
				|| (lastCustomChannel != null
						&& !lastCustomChannel.equals(customChannelMessageDTO.getCustomChannel()))) {

			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			ActivityDto activityDto = ActivityDto.builder().mcId(messengerContact.getId())
					.created(new Date(customChannelMessageDTO.getTimestamp().getTime() - 1000))
					.actorId(messengerContact.getCustomerId())
					.activityType(ActivityType.CUSTOM_CHANNEL_CONVERSATION_STARTED).from(messengerContact.getId())
					.businessId(businessDTO.getBusinessId()).to(businessDTO.getBusinessId())
					.accountId(businessDTO.getAccountId()).source(Source.CUSTOM.getSourceId())
					.customChannel(customChannelMessageDTO.getCustomChannel()) // Already lowercase from DTO
					.build();

			messageDTO.setActivityMessage(activityDto);
		}
		log.info("[addActivityIfChannelChanged] Activity added: {}", messageDTO.getActivityMessage());
	}

	/**
	 * Publish activity if channel has changed
	 *
	 * @param messageDTO The message DTO
	 */
	private void publishActivity(MessageDTO messageDTO) {
		addActivityIfChannelChanged(messageDTO);
		if (messageDTO.getActivityMessage() != null) {
			conversationActivityService.persistActivityForChannel(messageDTO);
		}
	}
}
