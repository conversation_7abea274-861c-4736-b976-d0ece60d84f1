package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.external.service.AppointmentService;
import com.birdeye.messenger.service.AppointmentConfirmationService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class AppointmentConfirmationServiceImpl implements AppointmentConfirmationService {

    @Autowired
    private RedisHandler redisHandler;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    protected KafkaService kafkaService;

    @Override
    @Async
    public void checkAndConfirmAppointment(BusinessDTO business, MessengerContact messengerContact, String message) {
        try {
            if(Objects.nonNull(business) && Objects.nonNull(messengerContact)){
                Object appointmentConfirmationContextData =  redisHandler.getOpsForHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()),business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                if(Objects.nonNull(appointmentConfirmationContextData)) {
                    log.info("appointment context found for the business: {} , messengerContact: {}, message: {}",business.getBusinessId(),messengerContact.getId(),message);
                    AppointmentConfirmationContextCachedDataDto response = ControllerUtil.getObjectFromJsonText(appointmentConfirmationContextData.toString(), AppointmentConfirmationContextCachedDataDto.class);
                    if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getAltAppointmentId())) {
                        if (StringUtils.isNotBlank(message) && checkIfConfirmationMessage(message)) {
                            AppointmentConfirmationResponseDto confirmationResponseDto = appointmentService.confirmAppointment(response.getAltAppointmentId(), response.getPmsAppointmentId(), business.getBusinessId());
                            if (Objects.nonNull(confirmationResponseDto) && StringUtils.isNotBlank(confirmationResponseDto.getAppointmentRequestId())) {
                                AppointmentConfirmationConfirmEventCampaignService event = new AppointmentConfirmationConfirmEventCampaignService(response.getReviewRequestId(), "appointment_reminder", "mobile", 13, business.getBusinessId(), System.currentTimeMillis());
                                log.info("push event to appointment service for appointment confirmation: {}", event);
                                kafkaService.publishToKafkaAsync(KafkaTopicEnum.APPOINTMENT_CAMPAIGN_REPLY_CLICK_EVENT, event);
                            }
                        }
                        redisHandler.deleteValueFromHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()), business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                    }
                }
            }
        }catch (Exception e){
            log.info("check and confirm appointment failed for messenger contact : {}",messengerContact.getId());
        }
    }

    @Override
    @Async
    public void checkAndDeleteAppointmentConfirmationContext(BusinessDTO business, MessengerContact messengerContact) {
        try {
            if(Objects.nonNull(business) && Objects.nonNull(messengerContact)){
                Object appointmentConfirmationContextData = redisHandler.getOpsForHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()),business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                if(Objects.nonNull(appointmentConfirmationContextData)) {
                    AppointmentConfirmationContextCachedDataDto response = ControllerUtil.getObjectFromJsonText(appointmentConfirmationContextData.toString(), AppointmentConfirmationContextCachedDataDto.class);
                    if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getAltAppointmentId())) {
                        log.info("delete appointment context for mcId: {} and altAppointmentId: {}",messengerContact.getId(),response.getAltAppointmentId());
                        redisHandler.deleteValueFromHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()), business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                    }
                }
            }
        }catch (Exception e){
            log.info("check And Delete Appointment Confirmation Context failed for messenger contact : {}",messengerContact.getId());
        }
    }

    @Override
    @Async
    public void updateAppointmentConfirmationContext(String accountId, String businessId, String mcId, String altAppointmentId,Long reviewRequestId,Long eventTime,String pmsAppointmentId) {
        try {
            log.info("create appointment context for mcId: {} and altAppointmentId: {}",mcId,altAppointmentId);
            AppointmentConfirmationContextCachedDataDto request = new AppointmentConfirmationContextCachedDataDto(reviewRequestId,altAppointmentId,eventTime,pmsAppointmentId);
            redisHandler.putData(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(accountId),
                    businessId.concat("-").concat(mcId), ControllerUtil.getJsonTextFromObject(request));
        }catch(Exception e) {
            log.info("update Appointment Confirmation Context failed for messenger contact : {}", mcId);
        }
    }

    @Override
    public Boolean checkAppointmentConfirmationContext(BusinessDTO business, MessengerContact messengerContact, String message) {
        try {
            if(Objects.nonNull(business) && Objects.nonNull(messengerContact)){
                Object appointmentConfirmationContextData = redisHandler.getOpsForHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()),business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                if(Objects.nonNull(appointmentConfirmationContextData)) {
                    AppointmentConfirmationContextCachedDataDto response = ControllerUtil.getObjectFromJsonText(appointmentConfirmationContextData.toString(), AppointmentConfirmationContextCachedDataDto.class);
                    if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getAltAppointmentId())) {
                        if (StringUtils.isNotBlank(message) && checkIfConfirmationMessage(message)) {
                            return false;
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("check Appointment Confirmation Context failed for messenger contact : {}",messengerContact.getId());
        }
        return true;
    }

    private boolean checkIfConfirmationMessage(String message){
        String appointmentConfirmationReplies = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("appointment_confirmation_replies","C");
        List<String> appointmentConfirmationReplyList =ControllerUtil.getTokensListFromString(appointmentConfirmationReplies);
        String smallCaseMessage = message.trim().toLowerCase();
        return appointmentConfirmationReplyList.stream()
                .map(String::toLowerCase)
                .anyMatch(reply -> reply.equals(smallCaseMessage));
    }

    @Override
    public AppointmentConfirmationContextCachedDataDto checkAndGetAppointmentConfirmationContext(BusinessDTO business, MessengerContact messengerContact) {
        try {
            if(Objects.nonNull(business) && Objects.nonNull(messengerContact)){
                Object appointmentConfirmationContextData = redisHandler.getOpsForHash(Constants.APPOINTMENT_CONFIRMATION_CONTEXT.concat(business.getAccountId().toString()),business.getBusinessId().toString().concat("-").concat(messengerContact.getId().toString()));
                if(Objects.nonNull(appointmentConfirmationContextData)) {
                    AppointmentConfirmationContextCachedDataDto response = ControllerUtil.getObjectFromJsonText(appointmentConfirmationContextData.toString(), AppointmentConfirmationContextCachedDataDto.class);
                    if (Objects.nonNull(response) && StringUtils.isNotBlank(response.getAltAppointmentId())) {
                        return response;
                    }
                }
            }
        }catch (Exception e){
            log.info("check Appointment Confirmation Context failed for messenger contact : {}",messengerContact.getId());
        }
        return null;
    }
}
