package com.birdeye.messenger.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.dao.repository.BusinessReceptionistConfigurationRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessReceptionistConfigurationService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class BusinessReceptionistConfigurationServiceImpl implements BusinessReceptionistConfigurationService {

	@Autowired
	private BusinessReceptionistConfigurationRepository receptionistConfigurationRepository;
	
	@Autowired
	private BusinessService businessService;

	private static final Integer DEFAULT_CONFIG_ID = 1;

	@Override
	public BusinessReceptionistConfiguration getReceptionistConfiguration(Long enterpriseId) {
		BusinessReceptionistConfiguration config = receptionistConfigurationRepository.findByBusinessId(enterpriseId);
		return config;
	}

	@Override
	@Cacheable(cacheNames = Constants.DEFAULT_RECEPTIONIST_CONFIG_CACHE, key = "'default_rec_config_cache'", unless = "#result == null")
	public BusinessReceptionistConfiguration getDefaultBusinessChatWidgetConfig() {
		BusinessReceptionistConfiguration config = receptionistConfigurationRepository
				.getDefaultReceptionistConfig(DEFAULT_CONFIG_ID); // id=1 Default Configuration
		return config;
	}

	@Override
	@CacheEvict(cacheNames = Constants.DEFAULT_RECEPTIONIST_CONFIG_CACHE, key = "'default_rec_config_cache'")
	public void clearDefaultBusinessChatWidgetConfig() {
		log.info("Clearing DEFAULT_RECEPTIONIST_CONFIG_CACHE");
	}
	
	@Override
	public void saveReceptionistConfig(BusinessReceptionistConfiguration config) {
		receptionistConfigurationRepository.saveAndFlush(config);
	}

	@Override
	public BusinessReceptionistConfiguration getConfigOfAccountFromLocation(BusinessDTO businessDTO) {
		BusinessReceptionistConfiguration config = null;
		if (businessDTO.getEnterpriseId() != null) {
			// Enterprise
			BusinessDTO business = businessService.getBusinessDTO(businessDTO.getEnterpriseId());
			config = getReceptionistConfiguration(business.getBusinessNumber());
		} else {
			// SMB
			config = getReceptionistConfiguration(businessDTO.getBusinessNumber());
		}
		return config;
	}

	@Override
	@Cacheable(cacheNames = Constants.INBOX_MISSED_CALL_CACHE, key = "'inbox_missedcall_cache'", unless = "#result == null")
	public BusinessReceptionistConfiguration getMissedCallConfigInbox() {
		BusinessReceptionistConfiguration config = receptionistConfigurationRepository.getMissedCallConfigInbox();
		return config;
	}
	
	@Override
	@Cacheable(cacheNames = Constants.NON_INBOX_MISSED_CALL_CACHE, key = "'noninbox_missedcall_cache'", unless = "#result == null")
	public BusinessReceptionistConfiguration getMissedCallConfigNonInbox() {
		BusinessReceptionistConfiguration config = receptionistConfigurationRepository.getMissedCallConfigNonInbox();
		return config;
	}
	
	@Override
	@CacheEvict(value = Constants.INBOX_MISSED_CALL_CACHE, key = "'inbox_missedcall_cache'")
	public void clearMissedCallInboxConfig() {
		log.info("Clearing INBOX_MISSED_CALL_CACHE");
	}
	
	@Override
	@CacheEvict(value = Constants.NON_INBOX_MISSED_CALL_CACHE, key = "'noninbox_missedcall_cache'")
	public void clearMissedCallNonInboxConfig() {
		log.info("Clearing NON_INBOX_MISSED_CALL_CACHE");
	}
}
