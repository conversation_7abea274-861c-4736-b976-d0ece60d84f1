package com.birdeye.messenger.service.impl;

import java.util.Date;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.CustomChannelMessage;
import com.birdeye.messenger.dao.repository.CustomChannelMessageRepository;
import com.birdeye.messenger.dto.CustomChannelMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.service.CustomChannelMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.LogUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of CustomChannelMessageService
 */
@Service
@Slf4j
public class CustomChannelMessageServiceImpl implements CustomChannelMessageService {

    @Autowired
    private CustomChannelMessageRepository customChannelMessageRepository;

    @Override
    public CustomChannelMessage saveCustomChannelMessage(CustomChannelMessageDTO customChannelMessageDTO, String businessNumber, String messengerContactId, String communicationDirection) {
        log.info("Saving custom channel message for businessId: {}, customerId: {}, channel: {}, direction: {}",
                customChannelMessageDTO.getBusinessId(), customChannelMessageDTO.getCustomerId(), customChannelMessageDTO.getCustomChannel(), communicationDirection);
        
        CustomChannelMessage customChannelMessage = new CustomChannelMessage();
        
        customChannelMessage.setBusinessId(customChannelMessageDTO.getBusinessId());
        customChannelMessage.setMcId(Integer.valueOf(messengerContactId));
        
        // Set communication direction from parameter
        customChannelMessage.setCommunicationDirection(communicationDirection);
        customChannelMessage.setMessageBody(customChannelMessageDTO.getBody());
        
        // Encrypt message body same as email receive -> businessNumber,mcId
        encrypt(customChannelMessage, businessNumber, messengerContactId); 
        
        // Set the last media URL if media URLs are present
        if (CollectionUtils.isNotEmpty(customChannelMessageDTO.getMediaUrl())) {
            customChannelMessage.setMediaURL(customChannelMessageDTO.getMediaUrl().get(customChannelMessageDTO.getMediaUrl().size() - 1));
        }
        customChannelMessage.setCustomChannel(customChannelMessageDTO.getCustomChannel());
        customChannelMessage.setEmailSubject(customChannelMessageDTO.getEmailSubject());
        
        Date messageTimestamp = customChannelMessageDTO.getTimestamp() != null ? 
                customChannelMessageDTO.getTimestamp() : new Date();
        customChannelMessage.setCreateDate(messageTimestamp);
        customChannelMessage.setSentOn(messageTimestamp);
   
        long startTime = System.currentTimeMillis();
        CustomChannelMessage savedMessage = customChannelMessageRepository.saveAndFlush(customChannelMessage);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("saveCustomChannelMessage", startTime, endTime);
        
        log.info("Custom channel message saved with ID: {}", savedMessage.getId());
        return savedMessage;
    }


    @Override
    public CustomChannelMessage findById(Integer id) {
        return customChannelMessageRepository.findById(id).orElse(null);
    }

    @Override
    public CustomChannelMessage save(CustomChannelMessage message) {
        log.info("Saving custom channel message: {}", message.getId());
        return customChannelMessageRepository.save(message);
    }

    @Override
    public CustomChannelMessage saveCampaignCustomChannelMessage(SendMessageDTO sendMessageDTO, String businessNumber, String messengerContactId) {
        log.info("Saving campaign custom channel message for businessId: {}, customerId: {}, templateId: {}",
                sendMessageDTO.getBusinessDTO().getBusinessId(), sendMessageDTO.getCustomerId(), sendMessageDTO.getTemplateId());
        
        CustomChannelMessage customChannelMessage = new CustomChannelMessage();
        customChannelMessage.setBusinessId(sendMessageDTO.getBusinessDTO().getBusinessId());
        customChannelMessage.setMcId(Integer.valueOf(messengerContactId));
        
        // Set communication direction to SEND for outgoing messages
        customChannelMessage.setCommunicationDirection("SEND");
        
        // Set template name as message body
        customChannelMessage.setMessageBody(sendMessageDTO.getTemplateName());
        
        // Set the last media URL if media URLs are present
        if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
            customChannelMessage.setMediaURL(sendMessageDTO.getMediaUrls().get(sendMessageDTO.getMediaUrls().size() - 1).getUrl());
        }
        
        customChannelMessage.setCustomChannel(sendMessageDTO.getCustomChannel());
//        customChannelMessage.setEmailSubject(sendMessageDTO.getEmailSubject());
        
        Date now = new Date();
        customChannelMessage.setCreateDate(now);
        customChannelMessage.setSentOn(now);
        
        // Encrypt the message body
        encrypt(customChannelMessage, businessNumber, messengerContactId);
        
        long startTime = System.currentTimeMillis();
        CustomChannelMessage savedMessage = customChannelMessageRepository.saveAndFlush(customChannelMessage);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("saveCampaignCustomChannelMessage", startTime, endTime);
        
        log.info("Campaign custom channel message saved with ID: {}", savedMessage.getId());
        return savedMessage;
    }

    private void encrypt(CustomChannelMessage message, String businessNumber, String messengerContactId) {
        String originalBody = message.getMessageBody();
        if (StringUtils.isNotEmpty(originalBody)) {
            try {
                String encryptedBody = EncryptionUtil.encrypt(
                    originalBody,
                    StringUtils.join(businessNumber, messengerContactId),
                    StringUtils.join(messengerContactId, businessNumber)
                );
                message.setMessageBody(encryptedBody);
                message.setEncrypted(1);
            } catch (Exception e) {
                log.error("Encryption failed for custom channel message body: {} with business {}", 
                    originalBody, message.getBusinessId());
                // Store unencrypted message if encryption fails
                message.setEncrypted(0);
            }
        }
    }

}
