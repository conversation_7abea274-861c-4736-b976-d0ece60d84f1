package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.apple.chat.QuickReplyInteractiveData;
import com.birdeye.messenger.dto.apple.chat.QuickReplyInteractiveData.Item;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;
import com.birdeye.messenger.service.AppleInteractiveEventHandler;
import com.birdeye.messenger.util.ControllerUtil;
@Service
public class AppleQuickReplyEventHandler implements AppleInteractiveEventHandler {

	@Override
	public AppleInteractiveMessageType getEvent() {
		return AppleInteractiveMessageType.QUICK_REPLY;
	}

	@Override
	public QuickReplyInteractiveData handle(SendMessageDTO sendMessageDTO) throws Exception {
		QuickReplyInteractiveData quickReplyInteractiveData=new QuickReplyInteractiveData();
		quickReplyInteractiveData.getData().setRequestIdentifier(ControllerUtil.generateUniqueMessageId());
		quickReplyInteractiveData.getData().getQuickReply().setSummaryText("Was your question answered?");
		Item item1 = new Item("1", "Yes");
		Item item2 = new Item("2", "No");
		List<Item> items=new ArrayList<QuickReplyInteractiveData.Item>();
		items.add(item1);items.add(item2);
		quickReplyInteractiveData.getData().getQuickReply().setItems(items);
		return quickReplyInteractiveData;
	}

}
