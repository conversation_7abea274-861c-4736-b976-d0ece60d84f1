package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.RobinActiveHours;
import com.birdeye.messenger.dao.repository.RobinActiveHoursRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomChatBotHoursDto;
import com.birdeye.messenger.dto.RobinActiveHoursDto;
import com.birdeye.messenger.enums.ChatBotHoursType;
import com.birdeye.messenger.service.RobinActiveHoursService;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobinActiveHoursServiceImpl implements RobinActiveHoursService {

    private final RobinActiveHoursRepository robinActiveHoursRepository;

    @Override
    @Transactional
    public void saveAllRobinActiveHourConfig(List<RobinActiveHoursDto> robinActiveHoursDtoList) {

        List<Integer> businessIds = robinActiveHoursDtoList.stream()
                .map(RobinActiveHoursDto::getBusinessId)
                .collect(Collectors.toList());
        List<Integer> widgetIds = robinActiveHoursDtoList.stream()
                .map(RobinActiveHoursDto::getWidgetId)
                .collect(Collectors.toList());

        List<RobinActiveHours> existingEntries = robinActiveHoursRepository
                .findAllByWidgetIdInAndBusinessIdIn(widgetIds,businessIds);

        Map<String, RobinActiveHours> existingEntriesMap = existingEntries.stream()
                .collect(Collectors.toMap(
                        entry -> entry.getBusinessId() + "|" + entry.getWidgetId(),
                        entry -> entry
                ));

        List<RobinActiveHours> entriesToSave = new ArrayList<>();

        for (RobinActiveHoursDto dto : robinActiveHoursDtoList) {
            String key = dto.getBusinessId() + "|" + dto.getWidgetId();
            RobinActiveHours entry = existingEntriesMap.get(key);

            if (entry != null) {
                entry.setRobinInsideBusinessHours(dto.getRobinInsideBusinessHours());
                entry.setRobinOutsideBusinessHours(dto.getRobinOutsideBusinessHours());
            } else {
                entry = new RobinActiveHours();
                entry.setAccountId(dto.getAccountId());
                entry.setBusinessId(dto.getBusinessId());
                entry.setWidgetId(dto.getWidgetId());
                entry.setRobinInsideBusinessHours(dto.getRobinInsideBusinessHours());
                entry.setRobinOutsideBusinessHours(dto.getRobinOutsideBusinessHours());
            }
            if(Objects.nonNull(entry.getRobinInsideBusinessHours()) && entry.getRobinInsideBusinessHours()==1){
                entry.setConfigType(ChatBotHoursType.INSIDE_BUSINESS_HOUR.getLabel());
            }
            if(Objects.nonNull(entry.getRobinOutsideBusinessHours()) && entry.getRobinOutsideBusinessHours()==1){
                entry.setConfigType(ChatBotHoursType.OUTSIDE_BUSINESS_HOUR.getLabel());
            }
            entriesToSave.add(entry);
        }
        robinActiveHoursRepository.saveAll(entriesToSave);

    }

    @Override
    @Transactional
    public void deleteRobinActiveHourConfigByBusinessId(List<Integer> businessId) {
        robinActiveHoursRepository.deleteByBusinessIdIn(businessId);
    }

    @Override
    public List<RobinActiveHours> getRobinHoursBusinessIdForWidgetId(Integer widgetId) {
        return robinActiveHoursRepository.findAllByWidgetId(widgetId);
    }

    @Override
    public List<RobinActiveHours> getRobinHoursForWidgetIdAndBusinessID(Integer widgetId, Integer businessId) {
        List<RobinActiveHours> robinActiveHoursList =  robinActiveHoursRepository.findAllByWidgetIdAndBusinessId(widgetId,businessId);
        if(CollectionUtils.isNotEmpty(robinActiveHoursList)){
           return robinActiveHoursList;
        }
        return null;
    }

    @Override
    public List<RobinActiveHours> getRobinHoursForBusinessId(Integer businessId) {
        List<RobinActiveHours> robinActiveHoursList = robinActiveHoursRepository.findAllByBusinessId(businessId);
        if(CollectionUtils.isNotEmpty(robinActiveHoursList)){
            return robinActiveHoursList;
        }
        return null;
    }



    @Override
    @Transactional
    public void processChatBotHours(BusinessWebChatConfigurationRequest request, BusinessDTO businessDTO) {
        request.getChatbotHours().stream()
                .filter(chatbotHour -> isValidForAddition(chatbotHour))
                .forEach(chatbotHour -> {
                    try {
                        saveNewConfiguration(
                                businessDTO.getAccountId(),
                                request.getWidgetConfigId(),
                                chatbotHour,request
                        );
                    } catch (Exception e) {
                        log.info("Error saving chatbot configuration for chatbotHour {}", chatbotHour);
                    }
                });
    }


    private boolean isValidForDeletion(CustomChatBotHoursDto chatbotHour) {
        return chatbotHour != null
                && chatbotHour.getAddConfig() != null
                && chatbotHour.getAddConfig() == 0
                && CollectionUtils.isNotEmpty(chatbotHour.getBusinessLocations())
                && StringUtils.isNotEmpty(chatbotHour.getUId());
    }


    private boolean isValidForAddition(CustomChatBotHoursDto chatbotHour) {
        return chatbotHour != null
                && chatbotHour.getAddConfig() != null
                && chatbotHour.getAddConfig() != 0
                && CollectionUtils.isNotEmpty(chatbotHour.getBusinessLocations());
    }

    private void saveNewConfiguration(Integer accountId, Integer widgetId,
                                      CustomChatBotHoursDto chatbotHour,BusinessWebChatConfigurationRequest request) {
        String uid = chatbotHour.getUId();
        List<RobinActiveHours> configsToSave = new ArrayList<>();

        if (chatbotHour.getAddConfig() == 2 || request.isSmb()) {
            log.info("widget edited for uid {}", uid);
            robinActiveHoursRepository.deleteByUid(uid);
        }
        addOrUpdateConfigurations(chatbotHour, uid, accountId, widgetId, configsToSave);

        if (!configsToSave.isEmpty()) {
            robinActiveHoursRepository.saveAll(configsToSave);
        }
    }

    private void addOrUpdateConfigurations(CustomChatBotHoursDto chatbotHour, String uid, Integer accountId, Integer widgetId, List<RobinActiveHours> configsToSave) {

        if (CollectionUtils.isNotEmpty(chatbotHour.getHoursOfOperations())) {
            for (CustomChatBotHoursDto.ChatBotHoursOfOperations operation : chatbotHour.getHoursOfOperations()) {
                if (CollectionUtils.isNotEmpty(operation.getWorkingHours())) {
                    for (CustomChatBotHoursDto.ChatBotHoursOfOperations.ChatBotWorkingHour workingHour : operation.getWorkingHours()) {
                        for (Integer businessId : chatbotHour.getBusinessLocations()) {
                            RobinActiveHours hoursConfig = new RobinActiveHours();
                                hoursConfig = RobinActiveHours.builder()
                                        .accountId(accountId)
                                        .businessId(businessId)
                                        .widgetId(widgetId)
                                        .configType(chatbotHour.getConfigType())
                                        .uId(uid)
                                        .day(operation.getDay())
                                        .startHour(workingHour.getStartHour())
                                        .closeHour(workingHour.getEndHour())
                                        .robinInsideBusinessHours(0)
                                        .robinOutsideBusinessHours(0)
                                        .build();
                            configsToSave.add(hoursConfig);
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<CustomChatBotHoursDto> getChatBotHoursByWidgetId(List<RobinActiveHours> configurations) {
        if (CollectionUtils.isEmpty(configurations)) {
            return Collections.emptyList();
        }

        configurations = configurations.stream()
                .filter(config -> !StringUtils.isEmpty(config.getUId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(configurations)) {
            return Collections.emptyList();
        }

        Map<String, Map<String, List<RobinActiveHours>>> configsByUidAndType = configurations.stream()
                .collect(Collectors.groupingBy(RobinActiveHours::getUId,
                        Collectors.groupingBy(RobinActiveHours::getConfigType)));

        List<CustomChatBotHoursDto> result = new ArrayList<>();

        configsByUidAndType.forEach((uid, configTypeMap) -> {
            configTypeMap.forEach((configType, configs) -> {
                CustomChatBotHoursDto dto = new CustomChatBotHoursDto();
                dto.setUId(uid);
                dto.setConfigType(configType);

                List<Integer> businessLocations = configs.stream()
                        .map(RobinActiveHours::getBusinessId)
                        .distinct()
                        .collect(Collectors.toList());
                dto.setBusinessLocations(businessLocations);

                int firstBusinessId = businessLocations.get(0);
                List<RobinActiveHours> firstLocationConfigs = configs.stream()
                        .filter(config -> config.getBusinessId().equals(firstBusinessId))
                        .collect(Collectors.toList());

                Map<Integer, List<RobinActiveHours>> configsByDay = firstLocationConfigs.stream()
                        .collect(Collectors.groupingBy(RobinActiveHours::getDay));

                List<CustomChatBotHoursDto.ChatBotHoursOfOperations> hoursOfOperations = new ArrayList<>();

                configsByDay.forEach((day, dayConfigs) -> {
                    CustomChatBotHoursDto.ChatBotHoursOfOperations operation =
                            new CustomChatBotHoursDto.ChatBotHoursOfOperations();
                    operation.setDay(day);

                    List<CustomChatBotHoursDto.ChatBotHoursOfOperations.ChatBotWorkingHour> workingHours =
                            dayConfigs.stream()
                                    .map(config -> {
                                        CustomChatBotHoursDto.ChatBotHoursOfOperations.ChatBotWorkingHour workingHour =
                                                new CustomChatBotHoursDto.ChatBotHoursOfOperations.ChatBotWorkingHour();
                                        workingHour.setStartHour(config.getStartHour());
                                        workingHour.setEndHour(config.getCloseHour());
                                        return workingHour;
                                    })
                                    .collect(Collectors.toList());

                    operation.setWorkingHours(workingHours);
                    hoursOfOperations.add(operation);
                });

                hoursOfOperations.sort(Comparator.comparing(
                        CustomChatBotHoursDto.ChatBotHoursOfOperations::getDay));

                dto.setHoursOfOperations(hoursOfOperations);
                result.add(dto);
            });
        });

        return result;
    }

    @Override
    @Transactional
    public void removeChatBotHours(BusinessWebChatConfigurationRequest request, BusinessDTO businessDTO) {

        if (Objects.isNull(request) || CollectionUtils.isEmpty( request.getChatbotHours())) {
            log.info("custom chatbbot hours not present for widgetId: {}", request.getWidgetConfigId());
            return;
        }
        request.getChatbotHours().stream()
                .filter(chatbotHour -> isValidForDeletion(chatbotHour))
                .forEach(chatbotHour -> {
                    try {
                        robinActiveHoursRepository.deleteByUIdAndBusinessIdIn(
                                chatbotHour.getUId(),
                                chatbotHour.getBusinessLocations()
                        );
                    } catch (Exception e) {
                        log.error("Error deleting chatbot hours for uId: {}", chatbotHour.getUId());
                     }
        });
    }
}
