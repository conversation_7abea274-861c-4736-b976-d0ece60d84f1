package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.ImageData;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.Item;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.ReceivedMessage;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.ReplyMessage;
import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.Section;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;
import com.birdeye.messenger.enums.IntentType;
import com.birdeye.messenger.external.dto.Suggestion;
import com.birdeye.messenger.external.dto.Suggestion.Type;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.service.AppleInteractiveEventHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
@Service
public class AppleListPickerEventHandler implements AppleInteractiveEventHandler {

	@Override
	public AppleInteractiveMessageType getEvent() {
		return AppleInteractiveMessageType.LIST_PICKER;
	}

	@Override
	public ListPickerInteractiveData handle(SendMessageDTO sendMessageDTO) throws Exception {
		SuggestionHolder suggestionHolder = JSONUtils.fromJSON(sendMessageDTO.getBody(), SuggestionHolder.class);
		ListPickerInteractiveData interactiveData=new ListPickerInteractiveData();
		List<Item> items=new ArrayList<ListPickerInteractiveData.Item>();
		int order=0;
		int identifier=1;
        List<ImageData> imageData = interactiveData.getData().getImages();
        int imageIdentifier = 3;
		if(suggestionHolder!=null &&  CollectionUtils.isNotEmpty(suggestionHolder.getSuggestions())) {
			for (Suggestion  suggestion : suggestionHolder.getSuggestions()) {
                Item item = new Item(String.valueOf(identifier++), order++, suggestion.getValue());
                if (Type.INTENT.equals(suggestion.getType())) {
                    IntentType intentType = IntentType.getIntentTypeFromQueryText(suggestion.getValue().toLowerCase());
                    if (Objects.nonNull(intentType)) {
                        imageData.add(new ImageData(intentType.getImageBase64(),
                                String.valueOf(imageIdentifier)));
                        item.setImageIdentifier(String.valueOf(imageIdentifier++));

                    }

                }
                items.add(item);
			}
            interactiveData.setReceivedMessage(
                    new ReceivedMessage("icon", MessengerConstants.APPLE_ROBIN_SUGGESTION_HEADER,
                            MessengerConstants.APPLE_ROBIN_SUGGESTION_SUB_HEADER, "1"));
            interactiveData.setReplyMessage(new ReplyMessage("icon", "Selected item", "Selected item", "1"));
			interactiveData.getData().setRequestIdentifier(ControllerUtil.generateUniqueMessageId());
			interactiveData.getData().getListPicker().getSections().add(new Section(items, 0, "Select an option", false));
		}
		return interactiveData;
	}

}
