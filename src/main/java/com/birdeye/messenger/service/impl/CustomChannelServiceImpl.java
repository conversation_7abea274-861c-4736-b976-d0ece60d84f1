package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomChannel;
import com.birdeye.messenger.dao.repository.CustomChannelRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomChannelMessageDTO;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest.ContactInfo;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest.Content;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest.Metadata;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CustomChannelService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.validator.EmailValidatorService;

import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of the CustomChannelService interface
 */
@Service
@Slf4j
public class CustomChannelServiceImpl implements CustomChannelService {

    @Autowired
    private CustomChannelRepository customChannelRepository;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private MessengerEventHandlerService messengerEventHandlerService;

    // Property key for maximum number of custom channels allowed per account
    private static final String MAX_CUSTOM_CHANNELS_PROPERTY = "custom_channel_max_limit";
    
    //UI restricted keywords
	private static final Set<String> RESTRICTED_CHANNEL_NAMES = Set.of("null", "text", "applemessages",
			"securemessaging", "tiktok", "internalnote", "chatbot", "chatbotai");
    
    private static final String CHANNEL_NAME_REGEX ="^[a-zA-Z0-9._'-]+$";
    
    @Override
    public MessageResponse receiveMessage(CustomChannelReceiveRequest request) throws Exception {
    	

        validateRequest(request);

        // Get business information from location ID
        BusinessDTO business = businessService.getBusinessByBusinessNumber(request.getLocationId());
        if (business == null) {
            log.error("Business not found for location ID: {}", request.getLocationId());
            throw new NotFoundException(ErrorCode.NO_BUSINESS_FOUND);
        }
        
        log.info("Processing custom channel message for channel: {}", request.getChannel());
        // Check for channel limit
        Optional<CustomChannel> customChannelOpt = customChannelRepository.findByAccountId(business.getAccountId());
        if (customChannelOpt.isPresent() && StringUtils.isNotBlank(customChannelOpt.get().getChannels())) {
            List<String> channelList = ControllerUtil.getTokensListFromString(customChannelOpt.get().getChannels());
            Integer maxChannels = MessengerUtil.getIntegerPropertyFromCache(MAX_CUSTOM_CHANNELS_PROPERTY, 10);
            
            if (channelList.size() >= maxChannels && !channelList.contains(request.getChannel())) {
                log.error("Custom channel limit reached for account: {}.", business.getAccountId());
                throw new InputValidationException(ErrorCode.CUSTOM_CHANNEL_LIMIT_REACHED);
            }
        }


        // Get or create the custom channel using the already fetched customChannelOpt
        Integer channelId = getOrCreateCustomChannel(business.getAccountId(), request.getChannel(), customChannelOpt);
        log.info("Using custom channel ID: {} for account: {}", channelId, business.getAccountId());

        // Create CustomChannelMessageDTO from the request with all fields set
        CustomChannelMessageDTO messageDTO = CustomChannelMessageDTO.fromRequest(request, business);

        // Process the message through the event handler system
        MessageResponse response = messengerEventHandlerService.handleEvent(messageDTO);
        log.info("Custom channel message processed successfully");

        return response;
    }

    //TODO move to a validator class
	private void validateRequest(CustomChannelReceiveRequest request) {
		
		// Normalize channel name
        String channelName = StringUtils.trimToNull(request.getChannel());
        if (StringUtils.isBlank(channelName)) {
			log.error("Channel name is empty");
			throw new InputValidationException(ErrorCode.CHANNEL_NOT_FOUND);
		}
        
        channelName = channelName.toLowerCase();
        request.setChannel(channelName);
		
		// Validate locationId is null     
        if(request.getLocationId()==null) {
        	log.error("Location Id is null");
        	throw new InputValidationException(ErrorCode.CUSTOM_CHANNEL_LOCATION_ID_REQUIRED);
        }

		// Validate channel name format
        if (!isValidChannelName(channelName)) {
            log.error("Invalid custom channel name: {}", channelName);
            throw new InputValidationException(ErrorCode.INVALID_CUSTOM_CHANNEL_NAME);
        }
        
        // Validate contact info
        ContactInfo contact = request.getContact();
        if (contact == null) {
            log.error("Contact information is null");
            throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_OR_PHONE_REQUIRED);
        }

        // Validate customer name length
        if (StringUtils.isNotBlank(contact.getName()) && contact.getName().length() > 100) {
            log.error("Customer name exceeds 100 characters: {}", contact.getName());
            throw new InputValidationException(ErrorCode.NAME_TOO_LARGE);
        }

        // Validate that at least one of email or phone is provided
        if (StringUtils.isBlank(contact.getEmail()) && StringUtils.isBlank(contact.getPhone())) {
            log.error("Neither email nor phone provided for contact");
            throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_OR_PHONE_REQUIRED);
        }

        // Validate email if provided
        if (StringUtils.isNotBlank(contact.getEmail())) {
            if (contact.getEmail().length() > Constants.EMAIL_ID_MAX_LENGTH) {
                log.error("Customer email id exceeds {} characters: {}", Constants.EMAIL_ID_MAX_LENGTH, contact.getEmail());
                throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_ID_TOO_LONG);
            }
            if (contact.getEmail().length() < Constants.EMAIL_ID_MIN_LENGTH) {
                log.error("Customer email id is less than {} characters: {}", Constants.EMAIL_ID_MIN_LENGTH, contact.getEmail());
                throw new InputValidationException(ErrorCode.EMAIL_ID_TOO_SHORT);
            }

            // Validate email format
            int index = contact.getEmail().trim().indexOf("@");
            String mailId = null;
            // Keeping the value to 0 as @ at first index would also mean no valid value
            if (index > 0) {
                mailId = contact.getEmail().trim().substring(0, index);
            }

            if (mailId == null) {
                log.error("Invalid email format: {}", contact.getEmail());
                throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
            }

            EmailValidatorService emailValidator = new EmailValidatorService();
            if (!emailValidator.validate(contact.getEmail())) {
                log.error("Invalid email format: {}", contact.getEmail());
                throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
            }
        }

        // Validate content
        Content content = request.getContent();
        if (content == null) {
            log.error("Content is null");
            throw new InputValidationException(ErrorCode.MESSAGE_BODY_OR_MEDIA_REQUIRED);
        }

        // Validate that either body or attachment is present
        if (StringUtils.isBlank(content.getBody()) && CollectionUtils.isEmpty(content.getAttachmentURL())) {
            log.error("Neither message body nor attachment provided");
            throw new InputValidationException(ErrorCode.MESSAGE_BODY_OR_MEDIA_REQUIRED);
        }

        // Set default metadata if not provided
		if (request.getMetadata() == null || request.getMetadata().getTimestamp() == null) {
			Metadata metadata = new Metadata();
			metadata.setTimestamp(System.currentTimeMillis());
			request.setMetadata(metadata);
		}
	}

    @Override
    public boolean isValidChannelName(String channelName) {
        if (StringUtils.isBlank(channelName)) {
            return false;
        }

        // Check against Channel enum values from MessageDocument
        for (Channel channel : Channel.values()) {
            if (channelName.equalsIgnoreCase(channel.name())) {
                return false;
            }
        }

        if (RESTRICTED_CHANNEL_NAMES.contains(channelName)) {
        	return false;
        }
        
        if (channelName.contains(" ")) {
            return false;
        }
        
        if (!channelName.matches(CHANNEL_NAME_REGEX)) {
            return false;
        }
        
        return true;
    }

    @Override
    public boolean hasReachedChannelLimit(Integer accountId) {
        Optional<CustomChannel> customChannelOpt = customChannelRepository.findByAccountId(accountId);
        if (!customChannelOpt.isPresent()) {
            return false; // No channels yet
        }

        String channels = customChannelOpt.get().getChannels();
        if (StringUtils.isBlank(channels)) {
            return false; // No channels yet
        }

        List<String> channelList = ControllerUtil.getTokensListFromString(channels);
        Integer maxChannels = MessengerUtil.getIntegerPropertyFromCache(MAX_CUSTOM_CHANNELS_PROPERTY, 10);

        return channelList.size() >= maxChannels;
    }



    private Integer getOrCreateCustomChannel(Integer accountId, String channelName, Optional<CustomChannel> customChannelOpt) {
        if (customChannelOpt.isPresent()) {
            CustomChannel customChannel = customChannelOpt.get();
            String existingChannels = customChannel.getChannels();

            List<String> channelList;
            if (StringUtils.isBlank(existingChannels)) {
                channelList = new ArrayList<>();
            } else {
                channelList = ControllerUtil.getTokensListFromString(existingChannels);
            }

            // Add the new channel if it doesn't exist
            if (!channelList.contains(channelName)) {
                channelList.add(channelName);
                String updatedChannels = ControllerUtil.toCommaSeparatedString(channelList);
                customChannel.setChannels(updatedChannels);
                customChannel.setUpdatedAt(new Date());
                customChannelRepository.save(customChannel);
            }

            return customChannel.getId();
        } else {
            // Create new record
            CustomChannel newChannel = new CustomChannel(accountId, channelName);
            CustomChannel savedChannel = customChannelRepository.save(newChannel);
            return savedChannel.getId();
        }
    }
}
