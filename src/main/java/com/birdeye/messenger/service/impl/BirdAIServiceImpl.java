/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ConversationSummaryRequest;
import com.birdeye.messenger.dto.ConversationSummaryResponse;
import com.birdeye.messenger.dto.RobinPlainTextRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SurveyDetail;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.service.BirdAIService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationSummaryMessageBodyService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BirdAIServiceImpl implements BirdAIService {

    private final MessengerContactService messengerContactService;

    private final ElasticSearchExternalService elasticSearchExternalService;

    private final ConversationSummaryMessageBodyService conversationSummaryMessageBodyService;

    private final ChatbotService chatbotService;
    
    private final CommonService commonService;

    @Override
    public List<ConversationSummaryResponse> getMessagesForConversationSummary(
            ConversationSummaryRequest conversationSummaryRequest) {
        Integer mcId = conversationSummaryRequest.getMcId();
        Integer accountId = conversationSummaryRequest.getAccountId();
        Integer page = conversationSummaryRequest.getPage();
        Integer size = conversationSummaryRequest.getSize();
        String orderBy = conversationSummaryRequest.getOrderBy();
        SortOrder sortOrder = conversationSummaryRequest.getSortOrder() == SortOrderEnum.ASC ? SortOrder.ASC
                : SortOrder.DESC;
        boolean excludeReviews = conversationSummaryRequest.isExcludeReviews();
        boolean excludeSurveys = conversationSummaryRequest.isExcludeSurveys();
        boolean excludePayments = conversationSummaryRequest.isExcludePayments();
        boolean excludeAppointments = conversationSummaryRequest.isExcludeAppointments();
        boolean excludeInternalNotes = conversationSummaryRequest.isExcludeInternalNotes();
        boolean excludeRobinResponses = conversationSummaryRequest.isExcludeRobinResponses();

        MessengerContact messengerContact = validateMessengerContact(mcId);

        BoolQueryBuilder boolQueryBuilder = buildQueryBuilderForConversationSummaryRequest(mcId, accountId,
                excludeReviews, excludeSurveys, excludePayments, excludeAppointments, excludeInternalNotes,
                excludeRobinResponses);
        SortBuilder<?> sortBuilder = SortBuilders.fieldSort(orderBy).order(SortOrder.DESC);

        ESQueryBuilderRequest<MessageDocument> esQueryBuilderRequest = ESQueryBuilderRequest.buildESQueryBuilderRequest(
                page, size, MessageDocument.class, accountId, Constants.Elastic.MESSAGE_INDEX, boolQueryBuilder,
                sortBuilder, null, null);

        List<MessageDocument> messageDocuments = elasticSearchExternalService
                .getResultUsingQueryBuilder(esQueryBuilderRequest);

        List<ConversationSummaryResponse> conversationSummaryResponses = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(messageDocuments)) {
            messageDocuments = messageDocuments.stream().map(messageDocument -> {
                messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
                return messageDocument;
            }).collect(Collectors.toList());

            log.info("{} messageDocuments present", messageDocuments.size());
            List<RobinPlainTextRequest> robinPlainTextRequests = messageDocuments.stream()
                    .filter(messageDocument -> MessageType.RICH_CONTENT_CHAT == messageDocument.getMessageType())
                    .map(RobinPlainTextRequest::new)
                    .filter(robinPlainTextRequest -> !MessengerUtil.checkIfAnyValueIsNull(robinPlainTextRequest.getId(),
                            robinPlainTextRequest.getIntentType(), robinPlainTextRequest.getData()))
                    .collect(Collectors.toList());

            Map<String, String> robinPlainTextResponseMap = chatbotService.getRobinPlainTextBodyForRichContentChat(
                    robinPlainTextRequests, accountId, messengerContact.getBusinessId());

            conversationSummaryResponses = messageDocuments.stream()
                    .map(messageDocument -> buildConversationSummaryResponseFromMessageDocument(messageDocument,
                            robinPlainTextResponseMap))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (sortOrder == SortOrder.ASC) {
                Collections.sort(conversationSummaryResponses);
            }

        }
        return conversationSummaryResponses;
    }

    private MessengerContact validateMessengerContact(Integer mcId) {
        log.info("validateMessengerContact called with mcId : {}", mcId);
        MessengerContact messengerContact = messengerContactService.findById(mcId);

        if (Objects.isNull(messengerContact)) {
            throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        return messengerContact;
    }

    private BoolQueryBuilder buildQueryBuilderForConversationSummaryRequest(Integer mcId, Integer accountId,
            boolean excludeReviews, boolean excludeSurveys, boolean excludePayments, boolean excludeAppointments,
            boolean excludeInternalNotes, boolean excludeRobinResponses) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        List<MessageType> ignoreMessageTypes = new LinkedList<>(
                Arrays.asList(MessageType.ROBIN_SUGGESTION, MessageType.RICH_LINK));
        List<ActivityType> ignoreActivityTypes = new LinkedList<>(
                Constants.EXCLUDED_CONVERSATION_SUMMARY_ACTIVITY_TYPES);

        boolQueryBuilder.must(QueryBuilders.termQuery("c_id", mcId)).must(QueryBuilders.termQuery("e_id", accountId));

        if (excludeReviews) {
            ignoreMessageTypes.add(MessageType.REVIEW);
        }

        if (excludeSurveys) {
            ignoreMessageTypes.add(MessageType.SURVEY_RESPONSE);
        }

        if (excludeInternalNotes) {
            ignoreMessageTypes.add(MessageType.INTERNAL_NOTES);
        }
        if (excludeRobinResponses) {
            ignoreMessageTypes.add(MessageType.RICH_CONTENT_CHAT);
        }

        if (excludePayments) {
            ignoreActivityTypes.addAll(Constants.PAYMENT_ACTIVITY_TYPES);
        }

        if (excludeAppointments) {
            ignoreActivityTypes.addAll(Constants.APPOINTMENT_ACTIVITY_TYPES);
        }

        boolQueryBuilder.mustNot(QueryBuilders.termsQuery("messageType", ignoreMessageTypes))
                .mustNot(QueryBuilders.termsQuery("activityType", ignoreActivityTypes));

        return boolQueryBuilder;
    }

    private ConversationSummaryResponse buildConversationSummaryResponseFromMessageDocument(
            MessageDocument messageDocument, Map<String, String> robinResponsesPlainTextMap) {
        Long createdAtTimeStamp = messageDocument.getCr_time();
        String createdAtString = messageDocument.getCr_date();
        ConversationSummaryResponse conversationSummaryResponse = null;
        if (Objects.nonNull(createdAtTimeStamp) || StringUtils.isNotBlank(createdAtString)) {
            MessageType messageType = messageDocument.getMessageType();
            ActivityType activityType = messageDocument.getActivityType();
            ConversationSummaryMessageType conversationSummaryMessageType = commonService.getConversationSummaryMessageType(
                    messageType,
                    activityType);
            Long createdAt = null;
            try {
                createdAt = Objects.nonNull(createdAtTimeStamp) ? createdAtTimeStamp
                        : Constants.SERVER_TIME_FORMATTER.parse(createdAtString).getTime();
            } catch (ParseException e) {
                log.error("error while parsing date : {}", e.getMessage());
            }
            CommunicationDirection communicationDirection = messageDocument.getCommunicationDirection();
            String userName = messageDocument.getU_name();
            Integer userId = messageDocument.getU_id();
            String agentName = MessengerConstants.DEFAULT_USER_ID_AND_NAMES_MAP.containsKey(userId)
                    ? MessengerConstants.DEFAULT_USER_ID_AND_NAMES_MAP.get(userId)
                    : StringUtils.isNotBlank(userName) ? "%%" + userName + "%%: " : Constants.AGENT;
            String name = CommunicationDirection.RECEIVE == communicationDirection
                    || Constants.PAYMENT_APPOINTMENT_RECEIVE_DIRECTION_ACTIVITY_TYPES.contains(activityType)
                            ? Constants.CUSTOMER
                            : agentName;

            if (ConversationSummaryMessageType.APPOINTMENT == conversationSummaryMessageType
                    || ConversationSummaryMessageType.PAYMENT == conversationSummaryMessageType) {
                String formattedBody = conversationSummaryMessageBodyService.formatMessageBodyForActivityType(
                        activityType,
                        messageDocument, conversationSummaryMessageType);
                String body = StringUtils.isNotBlank(formattedBody) ? name + formattedBody : null;
                if (StringUtils.isNotBlank(body)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().body(body).createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).build();
                }
                return conversationSummaryResponse;
            }

            else if (ConversationSummaryMessageType.CHAT == conversationSummaryMessageType
                    && MessageType.RICH_CONTENT_CHAT == messageType
                    && MapUtils.isNotEmpty(robinResponsesPlainTextMap)) {
                String id = messageDocument.getM_id()
                        + MessengerUtil.getMessageTypeSuffix(messageDocument.getMessageType(),
                                messageDocument.getSource());
                String robinBody = robinResponsesPlainTextMap.get(id);
                String body = StringUtils.isNotBlank(robinBody) ? name + robinBody : null;
                if (StringUtils.isNotBlank(body)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().body(body).createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).build();
                }
                return conversationSummaryResponse;
            }

            else if (ConversationSummaryMessageType.CHAT == conversationSummaryMessageType) {
                String messageBody = messageDocument.getMsg_body();
                String body = StringUtils.isNotEmpty(messageBody) ? name + messageBody : null;
                if (StringUtils.isNotBlank(body)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().body(body).createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).build();
                }
                return conversationSummaryResponse;
            }

            else if (ConversationSummaryMessageType.INTERNAL_NOTE == conversationSummaryMessageType) {
                String messageBody = messageDocument.getMsg_body();
                String body = StringUtils.isNotEmpty(messageBody)
                        ? name + Constants.INTERNAL_NOTE_MESSAGE_BODY + messageBody
                        : null;
                if (StringUtils.isNotBlank(body)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().body(body).createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).build();
                }
                return conversationSummaryResponse;

            }

            else if (ConversationSummaryMessageType.SURVEY == conversationSummaryMessageType) {
                SurveyDetail surveyDetail = messageDocument.getSurvey_detail();
                if (Objects.nonNull(surveyDetail)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).surveyInfo(surveyDetail).build();
                }
                return conversationSummaryResponse;
            }

            else if (ConversationSummaryMessageType.REVIEW == conversationSummaryMessageType) {
                Integer reviewId = messageDocument.getReviewId();
                if (Objects.nonNull(reviewId)) {
                    conversationSummaryResponse = ConversationSummaryResponse.builder().createdAt(createdAt)
                            .messageType(conversationSummaryMessageType).reviewId(reviewId).build();
                    return conversationSummaryResponse;
                }
            }
        }

        return conversationSummaryResponse;
    }

}
