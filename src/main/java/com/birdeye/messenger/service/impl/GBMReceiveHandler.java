package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage.Context.UserInfo;
import com.birdeye.messenger.enums.GoogleMessageStatusEnum;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.SUPPORTED_UPLOAD_FILES;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.GoogleMessageService;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleSocialIntegrationService;
import com.birdeye.messenger.sro.GetEnterpriseIdSocialResponse;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class GBMReceiveHandler extends MessageEventHandlerAbstract {

	private final GoogleSocialIntegrationService googleSocialIntegrationService;

	private final BusinessService businessService;

	private final GoogleMessageService googleMessageService;

	private final MessengerMessageService messengerMessageService;

	private final PulseSurveyService pulseSurveyService;

	private final MigrationService migrationService;

	private final SpamDetectionService spamDetectionService;
	
	private final MessengerEvent EVENT = MessengerEvent.GOOGLE_RECEIVE;

	@Value("${ess.business.id}")
	private Integer ESSBusinessId;

	@Value("${ess.agent.id}")
	private String ESSAgentId;

	@Autowired
    private RedisLockService redisLockService;
	
	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	public MessageResponse handle(MessageDTO messageDTO) throws Exception {
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		log.info("GBM: receive handler called, request data {}", event);
		if (!handleMissingPlaceIdInRequest(messageDTO)) {
			log.error("Cant set businessDTO for an enterprise with agentId : {}", event.getAgent());
		}
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        Integer accountId = Objects.nonNull(businessDTO) ? businessDTO.getAccountId() : null;

        if (Objects.isNull(accountId) || Integer.valueOf(0).equals(businessService.isMessengerEnabled(accountId))
                || !googleMessageService.isGoogleMessagingEnabled(accountId)) {
            log.info("GBMReceiveHandler: Messenger or GoogleMessaging disabled for business {}",
                    accountId);
            return new MessageResponse();
        }
//		MessengerContact mc = getMessengerContact(messageDTO);
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.GBM_RECEIVE_EVENT_DELAYED_QUEUE,
						event);
				return null;
			}
			// 1. Set type to save last message state for response time calculation
			messageDTO.setMsgTypeForResTimeCalc("R");
			messageDTO.setSource(Source.GOOGLE.getSourceId());
			// 2.Drop the message if received from same requestId
			String requestId = event.getRequestId(); // use this for de-duplication
			dropMessageIfDuplicate(requestId);
			// 3.Name all user's as Annonymous Google user who has opted out of identity
			// sharing
			addNameIfUserOptOutOfIdentitySharing(event);
			// 4. Save back up message in mySQl
			MessageDocumentDTO messageDocumentDTO = saveInSecondaryStorage(messageDTO);
			messageDTO.setMessageDocumentDTO(messageDocumentDTO);

			// handle PulseSurveyContext
			PulseSurveyContext context = null;
			try {
				context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
					customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					customerDTO.setOngoingPulseSurvey(false);
				}
				messageDTO.setCustomerDTO(customerDTO);
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}
			super.handle(messageDTO);

//			triggerAutoResponse(messageDTO, event,contactDocument,Source.GOOGLE);
			return null;
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private void addNameIfUserOptOutOfIdentitySharing(GoogleUserMessage event) {
		if (event.getContext().getUserInfo() == null) {
			event.getContext().setUserInfo(new UserInfo());
		}
		if (StringUtils.isBlank(event.getContext().getUserInfo().getDisplayName())) {
			event.getContext().getUserInfo().setDisplayName(Constants.ANNONYMOUS_GOOGLE_USER);
		}
	}

//	private void triggerAutoResponse(MessageDTO messageDTO, GoogleUserMessage event,ContactDocument contactDocument, Source source) {
//		// ------------------ create partial payload for auto response and call send api
//		// in async -----
//		RobinAutoReplyConfig robinAutoReplyConfig = robinService.getRobinAutoReplyConfigForBusiness(contactDocument.getB_id(),contactDocument.getE_id(),source);
//		if(!MessengerConstants.ROBIN_REPLY_USER.equals(contactDocument.getLastMessageUserId()) &&
//				(Boolean.TRUE.equals(robinAutoReplyConfig.getAutoReplyInsideBusinessHours()) || Boolean.TRUE.equals(robinAutoReplyConfig.getAutoReplyOutsideBusinessHours()))) {
//			SendMessageDTO message = new SendMessageDTO();
//			message.setUserId(-1);
//			message.setFromBusinessId(getBusinessDTO(messageDTO).getBusinessId());
//			message.setToCustomerId(getMessengerContact(messageDTO).getId().toString());
//			message.setBusinessIdentifierId(message.getFromBusinessId().toString());
//			message.setBOT(true);
////		MessengerContact messengerContact = getMessengerContact(messageDTO);
//			autoResponderService.sendAutoResponseForGoogleMessaging(message, event.getSendTime());
//		}
//	}

	private MessageDocumentDTO saveInSecondaryStorage(MessageDTO messageDTO) {
		MessengerMessage msg = getMsg(messageDTO);
		GoogleMessage gMsg = getGMsg(messageDTO);
		Optional<MessengerMediaFile> media = getAndSetMedia(messageDTO);

		// Save all in single transaction
		Map.Entry<MessengerMessage, GoogleMessage> entry = messengerMessageService.saveGoogleMessage(msg, gMsg, media);
		media.ifPresent(
				(image) -> migrationService.publishImageUploadRequest(getBusinessDTO(messageDTO).getBusinessId(),
						entry.getValue().getMediaId(), image.getUrl(), MessageDocument.Channel.GOOGLE));
		
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(entry.getValue(), entry.getKey());

		MessengerContact messengerContact = getMessengerContact(messageDTO);
		if( messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)){
			messageDocumentDTO.setSpam(true);
		}else{
			messageDocumentDTO.setSpam(false);
		}

		return messageDocumentDTO;
	}

	private void dropMessageIfDuplicate(String requestId) {
		Optional<GoogleMessage> googleMessage = googleMessageService.findByRequestId(requestId);
		if (googleMessage.isPresent()) {
			throw new BadRequestException("Duplicate request. Message already present with id" + requestId);
		}
	}

	@Override
	public void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.GOOGLE.name());
		lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.GOOGLE.getSourceId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.GOOGLE.getSourceId());
		Date lastMsgOn = new Date();
		messengerContact.setLastMsgOn(lastMsgOn);
		messengerContact.setUpdatedAt(lastMsgOn);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		lastMessageMetadataPOJO.setLastFbReceivedAt(sdf.format(lastMsgOn));
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
	}

	@Override
	public void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact mc = getMessengerContact(messageDTO);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		if (hasGoogleUserUploadedImage(event.getMessage().getText())) {
			mc.setLastMessage("Received an image.");
		} else {
			mc.setLastMessage(event.getMessage().getText());
		}
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(mc, 1, mc.getGoogleConversationId(),
				businessDTO.getBusinessNumber());
		mc.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		return null;
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {

	}

	@Override
	public MessengerContact getMessengerContact(MessageDTO messageDTO) {
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			GoogleUserMessage event = (GoogleUserMessage) messageDTO;
			String conversationId = event.getConversationId();
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			Optional<MessengerContact> optMessengerContact = messengerContactService
					.getByGoogleConversationIdAndSubaccountId(conversationId, businessDTO.getBusinessId());
			if (optMessengerContact.isPresent()) {
				messageDTO.setCustomerDTO(getCustomerDTO(messageDTO));
				messageDTO.setMessengerContact(optMessengerContact.get());
			} else {
				CustomerDTO customerDTO = getCustomerDTO(messageDTO);
				MessengerContact mc = messengerContactService.getOrCreateMessengerContactForGoogleContact(
						conversationId, businessDTO.getBusinessId(), customerDTO.getId());
				messageDTO.setMessengerContact(mc);
			}
		}
		MessageTag messageTag = getMessageTag(messageDTO);
		spamDetectionService.spamDetectionAllChannels(messageDTO, messageDTO.getMessengerContact(), messageDTO.getCustomerDTO(), messageTag);
		return messageDTO.getMessengerContact();
	}

	@Override
	public CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		if (Objects.isNull(messageDTO.getCustomerDTO())) {
			GoogleUserMessage event = (GoogleUserMessage) messageDTO;
			GoogleUserMessage.Context.UserInfo userInfo = event.getContext().getUserInfo();
			KontactoRequest kontactoRequest = new KontactoRequest();
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			kontactoRequest.setBusinessId(businessDTO.getBusinessId());
			kontactoRequest.setName(userInfo.getDisplayName());
			kontactoRequest.setEmailId(event.getConversationId() + "@gbmdummy.com");
			kontactoRequest.setSource(KontactoRequest.GOOGLE);
			KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
			locationInfo.setCountryCode(businessDTO.getCountryCode());
			kontactoRequest.setLocation(locationInfo);
			CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, businessDTO.getAccountId(),
					-1);
			if (Objects.isNull(customerDTO)) {
				log.error("GoogleBusinessMessageReceiveHandler: failed to create/get customer for event {}", event);
				throw new MessengerException(
						"Failed to get or create customer for conversationId" + event.getConversationId());
			}
			messageDTO.setCustomerDTO(customerDTO);
		}
		return messageDTO.getCustomerDTO();
	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		if (Objects.isNull(messageDTO.getBusinessDTO())) {
			GoogleUserMessage event = (GoogleUserMessage) messageDTO;
			String placeId = event.getContext().getPlaceId();
			Integer businessId = googleSocialIntegrationService.getBusinessIdForPlaceId(placeId);
			if (Objects.isNull(businessId)) {
				throw new NotFoundException("No mapped location found for placeId " + placeId);
			}
			BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
			messageDTO.setBusinessDTO(businessDTO);
		}
		return messageDTO.getBusinessDTO();
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		if (getSendEmailNotification(messageDTO)) {
			return MessageTag.UNREAD;
		} else
			return MessageTag.INBOX;
	}

	@Override
	protected boolean getSendEmailNotification(MessageDTO messageDTO) {
		return true;
	}

	@Override
	public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
		notificationRequest.setMsgId(Integer.valueOf(messageDTO.getMessageDocumentDTO().getM_id()));
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(10); // number of messages to be fetched from ES
		// The creation time is the last received time if last delivery time is null.
		notificationRequest.setLastMsgTime(messageDTO.getMessageDocumentDTO().getCr_time());
		notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
		return notificationRequest;

	}

	private boolean hasGoogleUserUploadedImage(String text) {
		if (StringUtils.isNotBlank(text) && StringUtils.startsWith(text, "https://storage.googleapis.com")
				&& StringUtils.contains(text, "x-goog-expires")) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}

	private MessengerMessage getMsg(MessageDTO messageDTO) {
		MessengerMessage msg = new MessengerMessage();
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		msg.setAccountId(businessDTO.getAccountId());
		msg.setChannel(MessageDocument.Channel.GOOGLE.name());
		msg.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE.name());
		msg.setMessageType(MessageDocument.MessageType.CHAT.name());
		msg.setMessengerContactId(getMessengerContact(messageDTO).getId());
		msg.setCreatedDate(event.getSendTime());
		return msg;
	}

	private GoogleMessage getGMsg(MessageDTO messageDTO) {
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		GoogleMessage gMsg = new GoogleMessage();
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		gMsg.setBusinessId(businessDTO.getBusinessId());
		gMsg.setCreateDate(event.getSendTime());
		gMsg.setRequestId(event.getRequestId());
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		gMsg.setCustomerId(customerDTO.getId());
		gMsg.setEncrypted(0);
		GoogleUserMessage.Message message = event.getMessage();
		gMsg.setMessageBody(hasGoogleUserUploadedImage(message.getText()) ? "" : message.getText()); // set it empty
																										// since the
																										// text is a
																										// image url
																										// uploaded by
																										// google user
		gMsg.setMessageId(message.getMessageId());
		gMsg.setRecipientId(event.getContext().getPlaceId());
		gMsg.setSenderId(event.getConversationId());
		gMsg.setSentOn(event.getSendTime());
		gMsg.setStatus(GoogleMessageStatusEnum.RECEIVED.name());
		return gMsg;
	}

	private Optional<MessengerMediaFile> getAndSetMedia(MessageDTO messageDTO) {
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		if (!hasGoogleUserUploadedImage(event.getMessage().getText()))
			return Optional.empty();
		MessengerMediaFileDTO media = new MessengerMediaFileDTO();
		media.setUrl(event.getMessage().getText());
		media.setContentType("image/" + SUPPORTED_UPLOAD_FILES.JPEG.getExtension());
		media.setName(event.getRequestId() + "_image" + ".jpeg");
		media.setFileExtension("jpeg");
		messageDTO.setMessengerMediaFileDTO(media);
		return Optional.of(new MessengerMediaFile(media));
	}

	private Boolean handleMissingPlaceIdInRequest(MessageDTO messageDTO) {
		GoogleUserMessage event = (GoogleUserMessage) messageDTO;
		String placeId = event.getContext().getPlaceId();
		if (StringUtils.isEmpty(placeId)) {
			log.info("Empty placeId. Request : {}", event);
			String googleConvId = event.getConversationId();
			MessengerContact messengerContact = messengerContactService.findByGoogleConversationId(googleConvId);
			if (Objects.nonNull(messengerContact)) {
				messageDTO.setMessengerContact(messengerContact);
				Integer businessId = messengerContact.getBusinessId();
				BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
				messageDTO.setBusinessDTO(businessDTO);
				log.info("MessangerContact : {} and BusinessDTO : {} both set for messageDto : {}", messengerContact,
						businessDTO, messageDTO);
				return true;
			} else {
				String agentId = event.getAgent();
				List<String> ESSAgentIdList = Arrays.asList(ESSAgentId.split(","));
				if (CollectionUtils.isNotEmpty(ESSAgentIdList) && ESSAgentIdList.contains(agentId)) {
					BusinessDTO businessDTO = businessService.getBusinessDTO(ESSBusinessId);
					messageDTO.setBusinessDTO(businessDTO);
					log.info("Conversation set with businessDTO of ESS");
					return true;
				} else {
					GetEnterpriseIdSocialResponse agentResponse = googleSocialIntegrationService.getAccountIdByAgentId(agentId);
					BusinessDTO businessDTO = null;
					try {
						businessDTO = businessService.getBusinessLiteDTO("businessNumber", String.valueOf(agentResponse.getEnterpriseId()));
						if ("Enterprise-Location".equals(businessDTO.getType())) {
							log.info("GMB msg on Enterprise businessDTO : {}", businessDTO);
							//Map to a location from response
							businessDTO = businessService.getBusinessLiteDTO(agentResponse.getBusinessId());
							messageDTO.setActivityForGmbAutoselect(true);
						}
						messageDTO.setBusinessDTO(businessDTO);
						log.info("Conversation set with businessDTO : {} for agent : {}", businessDTO, agentId);
						return true;
					} catch (Exception e) {
						log.error("Some error occured while fetching businessDTO : {}", e);
						return false;
					}

				}
			}
		}
		return false;
	}
}
