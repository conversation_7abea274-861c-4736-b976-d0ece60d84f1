package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.repository.ConversationActivityRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.TeamDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.StatusEnum;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.LogUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationActivityServiceImpl implements ConversationActivityService {

	private static DateFormat dateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);

	private final ConversationActivityRepository repo;
	
	@Lazy
	private final MessengerContactService messengerContactService;
	private final UserService userService;
	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private FirebaseService fcmService;

	@Override
	public ConversationActivity create(ActivityType type, UserDTO userDTO) {
		Objects.requireNonNull(type);
		ConversationActivity activity = new ConversationActivity();
		activity.setActivityType(type.getLabel());
		activity.setCreatedDate(new Date());
		activity.setContent("User performed " + type);
		activity.setUpdatedDate(new Date());

		// TODO: we are not persisting user Ids?
		// activity.setTriggeredBy(userDTO.getName());
		repo.save(activity);

		return activity;

	}

	@Override
	public ConversationActivity create(ActivityDto activity) {
		Objects.requireNonNull(activity);
		ConversationActivity activityEntity = new ConversationActivity(activity);
		long startTime = System.currentTimeMillis();
		ConversationActivity conversationActivity = repo.save(activityEntity);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("createActivity", startTime, endTime);
		activity.setId(conversationActivity.getId());
		return activityEntity;
	}

	@Override
	public void persistActivityInStoreAndMirror(LiveChatSessionToken liveChatSessionToken, Integer source,
			ActivityType activityType, TeamDto team) {
		log.info("[persistActivityInStore][{}] - Received event for source {} and session details {}", activityType,
				source, liveChatSessionToken);
		ActivityDto activityDto = null;
		if ((activityType == ActivityType.WEBCHAT_START_WITH_TEAM
				|| activityType == ActivityType.LIVECHAT_START_WITH_TEAM) && team != null) {
			activityDto = ActivityDto.builder().mcId(liveChatSessionToken.getMcid()).created(new Date())
					.activityType(activityType).to(team.getTeamId()).toName(team.getName())
					.accountId(liveChatSessionToken.getAccountId()).source(source)
					.businessId(liveChatSessionToken.getBusinessId()).build();
		} else {
			activityDto = ActivityDto.builder().mcId(liveChatSessionToken.getMcid()).created(new Date())
					.actorId(liveChatSessionToken.getCustomerId()).activityType(activityType)
					.from(liveChatSessionToken.getMcid())
					.to(liveChatSessionToken.getBusinessId()).accountId(liveChatSessionToken.getAccountId())
					.source(source).businessId(liveChatSessionToken.getBusinessId()).build();
		}
		// adding activity to conversation_ activity and messenger_message
		persistActivityInDatabase(activityDto, null);
		// adding activity related data into message index
		MessageDocument messageDocument = persistActivityInES(activityDto);
		log.info("[persistActivityInStore][{}] - Syncing Firebase WEBDB for businessId {} and accountId {}",
				activityType, liveChatSessionToken.getBusinessId(), liveChatSessionToken.getAccountId());
		// updating messenger index on firebase
		// fcmService.mirrorOnWeb(liveChatSessionToken.getAccountId(),
		// liveChatSessionToken.getBusinessId());
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(liveChatSessionToken.getAccountId());
		firebaseDto.setBusinessId(liveChatSessionToken.getBusinessId());
		firebaseDto.setMcId(liveChatSessionToken.getMcid());
		fcmService.mirrorOnWeb(firebaseDto);

		MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
		messengerBaseFilter.setAccountId(liveChatSessionToken.getAccountId());
		messengerBaseFilter.setConversationId(liveChatSessionToken.getMcid());
		messengerBaseFilter.setCount(1);
		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
		if (!CollectionUtils.isEmpty(contactDocuments)) {
			fcmService.mirrorOnMobile(contactDocuments.get(0), messageDocument);
		}
	}

	@Override
	public void persistActivityInStoreForWebChat(LiveChatSessionToken liveChatSessionToken, Integer source,
			ActivityType activityType, Date createdDate, TeamDto team) {
		log.info("[persistActivityInStore][{}] - Received event for source {} and session details {}", activityType,
				source, liveChatSessionToken);
		ActivityDto activityDto = null;
		if (activityType == ActivityType.WEBCHAT_START_WITH_TEAM && team != null) {
			activityDto = ActivityDto.builder().mcId(liveChatSessionToken.getMcid()).created(createdDate)
					.actorId(liveChatSessionToken.getCustomerId()).activityType(activityType)
					.to(team.getTeamId()).toName(team.getName()).accountId(liveChatSessionToken.getAccountId())
					.source(source).build();
		} else {
			activityDto = ActivityDto.builder().mcId(liveChatSessionToken.getMcid()).created(createdDate)
					.actorId(liveChatSessionToken.getCustomerId()).activityType(activityType)
					.from(liveChatSessionToken.getMcid())
					.to(liveChatSessionToken.getBusinessId()).accountId(liveChatSessionToken.getAccountId())
					.source(source).build();
		}
		// adding activity to conversation_ activity and messenger_message
		log.info("[persistActivityInStore][{}] - Received event for source {} and session details {}", activityType,
				source, liveChatSessionToken);
		persistActivityInDatabase(activityDto, null);
		// adding activity related data into message index
		persistActivityInES(activityDto);
	}

	@Override
	public void persistActivityForChannel(ActivityDto activityDto) {
		log.info("[persistActivityForChannel] - Received event for activity details {}", activityDto);
		// adding activity to conversation_ activity and messenger_message
		persistActivityInDatabase(activityDto, null);
		// adding activity related data into message index
		persistActivityInES(activityDto);
		// updating messenger index on firebase
		// fcmService.mirrorOnWeb(activityDto.getAccountId(),
		// activityDto.getBusinessId());
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(activityDto.getAccountId());
		firebaseDto.setBusinessId(activityDto.getBusinessId());
		firebaseDto.setMcId(activityDto.getMcId());
		fcmService.mirrorOnWeb(firebaseDto);
	}

	/**
	 * This method is used to persist conversation activity and messenger message
	 * tables
	 * 
	 * @return
	 */
	@Override
	public ConversationActivity persistActivityInDatabase(ActivityDto activityDto, UserDTO userDTO) {
		log.info("[persistActivityInDatabase] - Received event for activityDto {}", activityDto);
		ConversationActivity activity = create(activityDto);
		messengerMessageService.saveMessengerActivity(activity, activityDto.getMcId(), userDTO);
		return activity;
	}

	/**
	 * This table is used to add activity related details on message document
	 * 
	 * @param activityDto
	 */
	@Override
	public MessageDocument persistActivityInES(ActivityDto activityDto) {
		log.info("[persistActivityInES][{}] - Received event for activityDto {}", activityDto.getActivityType(),
				activityDto);
		MessageDocument messageDocument = new MessageDocument(activityDto);
		messageDocument = messengerContactService.addNewMessageDocOnES(messageDocument,
				messageDocument.getM_id() + "_a");
		return messageDocument;
	}

	@Override
	public void persistActivityForChannel(MessageDTO messageDTO) {
		log.info("[persistActivityForChannel] - Received event for source {} and activity details {}",
				messageDTO.getSource(), messageDTO.getActivityMessage());
		// adding activity to conversation_ activity and messenger_message
		persistActivityInDatabase(messageDTO.getActivityMessage(), null);
		// adding activity related data into message index
		MessageDocument messageDocument = persistActivityInES(messageDTO.getActivityMessage());

		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		Integer mcId = messageDTO.getMessengerContact().getId();
		if (businessDTO != null && mcId != null && (!Integer.valueOf(17).equals(messageDTO.getSource())
				|| ActivityType.SECURE_CHAT_ENDED == messageDTO.getActivityMessage().getActivityType()
				|| ActivityType.SECURE_CHAT_INITIATED == messageDTO.getActivityMessage().getActivityType())) {
			// fcmService.mirrorOnWeb(businessDTO.getAccountId(),
			// businessDTO.getBusinessId());
			FirebaseDto firebaseDto = new FirebaseDto();
			firebaseDto.setAccountId(businessDTO.getAccountId());
			firebaseDto.setBusinessId(businessDTO.getBusinessId());
			if (ActivityType.SECURE_CHAT_ENDED == messageDTO.getActivityMessage().getActivityType()
					|| ActivityType.SECURE_CHAT_INITIATED == messageDTO.getActivityMessage().getActivityType()) {
				firebaseDto.setLastMsgTimeStamp(new Date().getTime());
				firebaseDto.setTimestamp(firebaseDto.getLastMsgTimeStamp());
			}
			firebaseDto.setMcId(mcId);
			fcmService.mirrorOnWeb(firebaseDto);
			MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
			messengerBaseFilter.setAccountId(businessDTO.getAccountId());
			messengerBaseFilter.setConversationId(mcId);
			messengerBaseFilter.setCount(1);
			List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
			if (!CollectionUtils.isEmpty(contactDocuments)) {
				fcmService.mirrorOnMobile(contactDocuments.get(0), messageDocument);
			}
		}
	}

	@Override
	public void updateReferralActivity(List<String> activityTypes, Integer referrer, Integer lead, StatusEnum status,
			Date updatedDate) {
		repo.updateReferralActivity(status, referrer, lead, activityTypes, updatedDate);
	}

	@Override
	public boolean checkIfReferralActivityExists(Integer referrer, Integer lead, List<String> activityTypes) {
		return repo.getReferralActivity(referrer, lead, activityTypes).stream().findAny().isPresent();
	}

	@Override
	public void deleteConversationActivityUsingIds(List<Integer> messageIds) {
		repo.deleteByIdIn(messageIds);
	}

	@Override
	@Transactional
	public void deleteConversationActivities(Map<String, List<Integer>> result, Integer customerId) {
	    try {
		if (MapUtils.isNotEmpty(result)) {
			Map<String, Integer> activitiesLabelMap = Arrays.asList(ActivityType.values()).stream()
					.collect(Collectors.toMap(ActivityType::getLabel, ActivityType::getId));

			List<Integer> activityIds = result.entrySet().stream()
					.filter(e -> (e.getKey() != null && activitiesLabelMap.get(e.getKey()) != null))
					.map(Map.Entry::getValue).flatMap(i -> i.stream()).collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(activityIds)) {
                deleteConversationActivityUsingIds(activityIds);
                log.info("Deleting conversation activities for ids: {} and customer id: {}", activityIds, customerId);
            }
        }
    } catch (Exception e2) {
        log.error("error : {} occurred in deleteConversationActivities", e2.getMessage());
    }
}

	@Override
	public void createBlockUnblockActivitySpamPath(ActivityType activityType, CustomerDTO customerDTO,
			ContactDocument contactDocument, BusinessDTO businessDTO, Date created) {
		UserDTO userDTO = userService.getUserDTO(customerDTO.getUserId());
		ActivityDto block = ActivityDto.builder().mcId(contactDocument.getM_c_id()).created(created).updated(created)
				.activityType(activityType)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(userDTO.getId())
				.actorName(userDTO.getName())
				.type(MessageDocument.Type.CREATE)
				.accountId(businessDTO.getRoutingId()).businessId(customerDTO.getBusinessId())
				.build();
		ConversationActivity conversationActivity = persistActivityInDatabase(block, userDTO);
		block.setId(conversationActivity.getId());
		persistActivityInES(block);
	}

	@Override
	public void createMissedCallActivity(ActivityType activityType, CustomerDTO customerDTO,
			ContactDocument contactDocument, BusinessDTO businessDTO, Date created) {
		UserDTO userDTO = userService.getUserDTO(customerDTO.getUserId());
		ActivityDto block = ActivityDto.builder().mcId(contactDocument.getM_c_id()).created(created).updated(created)
				.activityType(activityType)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(userDTO.getId())
				.actorName(userDTO.getName())
				.type(MessageDocument.Type.CREATE)
				.accountId(businessDTO.getRoutingId()).businessId(customerDTO.getBusinessId())
				.build();
		ConversationActivity conversationActivity = persistActivityInDatabase(block, userDTO);
		block.setId(conversationActivity.getId());
		persistActivityInES(block);
	}
	
	@Async
	@Override
    public void createBlockUnblockActivity(ActivityType activityType, CustomerDTO customerDTO, ContactDocument contactDocument, Integer accountId) {
        UserDTO userDTO = userService.getUserDTO(customerDTO.getUserId());
        Date created = new Date();
        ActivityDto block = ActivityDto.builder().mcId(contactDocument.getM_c_id()).created(created).updated(created)
                .activityType(activityType)
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .actorId(userDTO.getId())
                .actorName(userDTO.getName())
                .type(MessageDocument.Type.CREATE)
                .accountId(accountId)
                .businessId(customerDTO.getBusinessId())
                .build();
        ConversationActivity conversationActivity = persistActivityInDatabase(block, userDTO);
        block.setId(conversationActivity.getId());
        MessageDocument messageDocument = persistActivityInES(block);
        fcmService.mirrorOnMobile(contactDocument, messageDocument);
    }


}
