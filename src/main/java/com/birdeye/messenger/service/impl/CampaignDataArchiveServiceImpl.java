package com.birdeye.messenger.service.impl;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import jakarta.transaction.Transactional;

import com.birdeye.messenger.dao.entity.MessengerContactArchive;
import com.birdeye.messenger.dao.repository.*;
import com.birdeye.messenger.util.ControllerUtil;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.MigrateHiddenCampaignDto;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.BusinessException;
import com.birdeye.messenger.service.CampaignDataArchiveService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.util.LogUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CampaignDataArchiveServiceImpl implements CampaignDataArchiveService{
    
    @Autowired
    private ElasticSearchExternalService elasticSearchService;
    
    @Autowired
    private KafkaService kafkaService;

    private final ElasticSearchExternalService esService;

    private final ObjectMapper objectMapper;
    
    @Autowired
    MessengerContactRepository messengerContactRepository;
    
    @Autowired
    MessengerMessageRepository messengerMessageRepository;
    
    @Autowired
    SmsRepository smsRepository;
    
    @Autowired
    EmailRepository emailRepository;
    
    @Autowired
    ArchiveMessengerContactRepository archiveMessengerContactRepository;
    
    @Autowired
    ArchiveMessengerMessageRepository archiveMessengerMessageRepository;
    
    @Autowired
    ArchiveSmsRepository archiveSmsRepository;
    
    @Autowired
    ArchiveEmailRepository archiveEmailRepository;


    /**
     *
     * List of businessId -> loop for a single id -> get it's messenger contact from new index
     * -> loop on all messenger contact find corresponding message document 
     * -> create bulk request for db tables and es , perform reindex on db , delete parallely
     *
     */

    @Override
    public void migrateCampaignData(){
        String range = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getProperty("campaign_archival_range",
                        "now-3M");
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("range",range);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                .addTemplateAndDataModel(Constants.Elastic.GET_HIDDEN_CAMPAIGN_CONVERSATION_ACCOUNT,data).addSize(0)
                .build();
        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        if (searchResponse.status().getStatus() != 200) {
            log.info("[Campaign Archive Migration-Accounts] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
        Terms aggregation = searchResponse.getAggregations().get("b_count");
        if(Objects.nonNull(aggregation)){
            long batchThreshold = 1700000;
            long currentBatchCount = 0;
            List<Integer> accounts = new ArrayList<>();

            for (Terms.Bucket bucket : aggregation.getBuckets()) {
                Integer key = Integer.valueOf(bucket.getKeyAsString());
                long docCount = bucket.getDocCount();

                if (currentBatchCount + docCount <= batchThreshold) {
                    currentBatchCount += docCount;
                    accounts.add(key);
                } else {
                    log.info("[Campaign Archive Migration-Accounts] Account Batch: {} and Batch Size: {} for migration",accounts,accounts.size());
                    kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_ACCOUNT_CAMPAIGN_CONVERSATIONS, null,
                            new MigrateHiddenCampaignDto(accounts));

                    // Reset batch variables
                    currentBatchCount = docCount;
                    accounts = new ArrayList<>();
                    accounts.add(key);
                }
            }
            if (!accounts.isEmpty()) {
                log.info("[Campaign Archive Migration-Accounts] Account Batch: {} and Batch Size: {} for migration",accounts,accounts.size());
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_ACCOUNT_CAMPAIGN_CONVERSATIONS, null,
                        new MigrateHiddenCampaignDto(accounts));
            }
        }
    }

    @Override
    public void migrateHiddenCampaignDocsMessageDocumentsAndDbData(List<Integer> accounts){
        log.info("[Campaign Archive Migration-Messages] Account Batch: {} and Batch Size: {} for migration",accounts,accounts.size());
        if(Objects.nonNull(accounts)){
            for(Integer accountId : accounts){
                try {
                    String range = CacheManager.getInstance()
                            .getCache(SystemPropertiesCache.class).getProperty("campaign_archival_range",
                                    "now-3M");
                    Map<String, Object> templateData = new HashMap<>();
                    templateData.put("accountId", accountId);
                    templateData.put("range",range);
                    templateData.put("size",10000);
                    Map<String, Object> data = new HashMap<>();
                    data.put("data", templateData);
                    ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                            .addScroll("10m")
                            .addRoutingId(accountId)
                            .addTemplateAndDataModel(Constants.Elastic.GET_HIDDEN_CAMPAIGN_CONVERSATIONS,data).addSize(10000)
                            .build();

                    SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
                    if(Objects.isNull(searchResponse)){
                        pushMigrationFailForAccount(accountId);
                        continue;
                    }
                    if (searchResponse.status().getStatus() != 200) {
                        log.info("[Campaign Archive Migration-Messages] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
                        throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                    }
                    String scrollId = searchResponse.getScrollId();
                    while (true) {
                        if (Objects.nonNull(searchResponse) && (searchResponse.getHits().getHits().length != 0)) {
                            MigrateHiddenCampaignDto docIdToMigrate = getMCIDSListFromResponse(searchResponse,accountId);
                            if (Objects.nonNull(docIdToMigrate)) {
                                log.info("[Campaign Archive Migration-Messages] contacts data to migrate: {}",docIdToMigrate.getMcIds().size());
                                kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_MESSAGE_DOC_BY_MCID, accountId,
                                        (Serializable) docIdToMigrate);
                            }
                        }
                        searchResponse = elasticSearchService.readMoreFromSearch(scrollId,"10m");
                        if (searchResponse.status().getStatus() != 200) {
                            log.info("[Campaign Archive Migration-Messages] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
                            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                        }
                        if (searchResponse.getHits().getHits().length == 0) {
                            esService.clearScrollContext(scrollId);
                            break;
                        }
                        if(Objects.isNull(searchResponse)){
                            pushMigrationFailForAccount(accountId);
                        }
                    }
                } catch (Exception e) {
                    pushMigrationFailForAccount(accountId);
                }
            }
        }
    }
    
    private void pushMigrationFailForAccount(Integer accountId){
        log.info("[Campaign Archive Migration-Messages] migration failed for account: {}",accountId);
        kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_ACCOUNT_CAMPAIGN_FAILURES, accountId,
                new MigrateHiddenCampaignDto(Collections.singletonList(accountId)));
    }

    private MigrateHiddenCampaignDto getMCIDSListFromResponse(SearchResponse searchResponse,Integer accountId) {
        List<Integer> conversationIds = new ArrayList<>();
        List<Integer> customerId = new ArrayList<>();
        if(Objects.nonNull(searchResponse)) {
            SearchHit[] searchHit = searchResponse.getHits().getHits();

            if (searchHit.length > 0) {
                Arrays.stream(searchHit).forEach(hit -> {
                    ContactDocument contactDocument = objectMapper.convertValue(hit.getSourceAsMap(), ContactDocument.class);
                    conversationIds.add(contactDocument.getM_c_id());
                    customerId.add(contactDocument.getC_id());
                });
            }
        }
        if(Objects.nonNull(conversationIds)&&Objects.nonNull(customerId)){
            return new MigrateHiddenCampaignDto(conversationIds,customerId,accountId,null);
        }else{
            log.info("conversationId or customerId not found for the batch:{}",searchResponse.getHits().getHits().length);
        }
        return null;
    }

    @Override
    @Transactional
    public void archiveMessageData(MigrateHiddenCampaignDto migrationRequest){
        try{
            log.info("[Campaign Archive Migration-DB/ES contacts/messages] contacts/messages data to migrate: {},{} and account: {}",migrationRequest.getMcIds().size(),migrationRequest.getCustomerId().size(),migrationRequest.getAccountId());
            List<List<Integer>> conversationIdBatch=ListUtils.partition(migrationRequest.getMcIds(),100);
            List<List<Integer>> customerIdsBatch=ListUtils.partition(migrationRequest.getCustomerId(),100);
            for(int i=0;i<conversationIdBatch.size();i++){
                List<Integer> conversationBatch=conversationIdBatch.get(i);
                List<Integer> customerIdsBatchForConversation=customerIdsBatch.get(i);
                migrateDbDataForCampaignHidden(conversationBatch,customerIdsBatchForConversation,migrationRequest.getAccountId(),migrationRequest.isSecondaryToTertiary());
            }
        }catch(Exception e){
            log.info("[Campaign Archive Migration-DB/ES contacts/messages] contacts/messages batch failed account:{}",migrationRequest.getAccountId());
            kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_CAMPAIGN_MESSAGES_FAILED_BATCH, migrationRequest.getAccountId(),
                    migrationRequest);
            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    private void migrateDbDataForCampaignHidden(List<Integer> conversationBatch,List<Integer> customerIdsBatchForConversation,Integer accountId,boolean secondaryToTertiary){
        try{
            log.info("Update/Delete Db/Es data for the accountId:{}",accountId);
            if(BooleanUtils.isFalse(secondaryToTertiary)){
                MigrateHiddenCampaignDto response=updatedConversionBatch(conversationBatch,customerIdsBatchForConversation,accountId);
                conversationBatch=response.getMcIds();
                customerIdsBatchForConversation=response.getCustomerId();
                // Process DB on messengerContactId
                List<MessengerContact>m_c_Ids = messengerContactRepository.findByIdsIn(conversationBatch);
                log.info("Batch received from es to migrate:{} and corresponding Db entries found:{}",conversationBatch.size(),m_c_Ids.size());
                messengerContactRepository.moveContactsToArchive(conversationBatch);
                messengerContactRepository.deleteByIdIn(conversationBatch);
                messengerMessageRepository.moveContactsToArchive(conversationBatch);
                messengerMessageRepository.deleteByIdIn(conversationBatch);

                // Process DB on customerId
                smsRepository.moveContactsToArchive(customerIdsBatchForConversation);
                smsRepository.deleteByIdIn(customerIdsBatchForConversation);
                emailRepository.moveContactsToArchive(customerIdsBatchForConversation);
                emailRepository.deleteByIdIn(customerIdsBatchForConversation);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_MESSAGE_DOCUMENT_REINDEX_DELETE,accountId,
                        new MigrateHiddenCampaignDto(conversationBatch,null,accountId,null,secondaryToTertiary,null));
            }else if(BooleanUtils.isTrue(secondaryToTertiary)){
                log.info("[Archive Campaign Data Job] Deletion From Secondary mcIdsBatch: {}, customerIdsBatch: {}",conversationBatch.size(),customerIdsBatchForConversation.size());
                archiveMessengerContactRepository.deleteByIdIn(conversationBatch);
                archiveMessengerMessageRepository.deleteByIdIn(conversationBatch);
                archiveSmsRepository.deleteByIdIn(customerIdsBatchForConversation);
                archiveEmailRepository.deleteByIdIn(customerIdsBatchForConversation);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_MESSAGE_DOCUMENT_DELETE,accountId,
                        new MigrateHiddenCampaignDto(conversationBatch,null,accountId,null,secondaryToTertiary,null));
            }
        }catch(Exception e){
            log.info("[Campaign Archive Migration-DB/ES contacts/messages] contacts/messages batch failed: {},{} and account:{}",conversationBatch,customerIdsBatchForConversation,accountId);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void archiveCampaignDataJob(MigrateHiddenCampaignDto request){
        try{
            LocalDateTime curTime = LocalDateTime.now();
            log.info("[Archive Campaign Data Job] Started:{}",(curTime.toString()));
            long startTime = System.currentTimeMillis();
            String range = getRange(request);
            boolean isReIndexed= reIndexContactDocument(request,range);
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("range",range);
            Map<String, Object> data = new HashMap<>();
            data.put("data", templateData);
            if(Boolean.TRUE.equals(isReIndexed)){
                ESRequest esRequest=new ESRequest.Builder(new ESRequest()).addIndex(getIndex(request))
                        .addTemplateAndDataModel(Constants.Elastic.GET_HIDDEN_CAMPAIGN_CONVERSATION_ACCOUNT,data).addSize(0)
                        .build();
                SearchResponse searchResponse=elasticSearchService.getSearchResult(esRequest);
                if(searchResponse.status().getStatus()!=200){
                    log.info("[Archive Campaign Data Job] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
                    throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                }
                Terms aggregation=searchResponse.getAggregations().get("b_count");
                if(Objects.nonNull(aggregation)){
                    for(Terms.Bucket bucket: aggregation.getBuckets()){
                        Integer accountId=Integer.valueOf(bucket.getKeyAsString());
                        try{
                            templateData.put("accountId",accountId);
                            templateData.put("size",10000);
                            data=new HashMap<>();
                            data.put("data",templateData);
                            esRequest=new ESRequest.Builder(new ESRequest()).addIndex(getIndex(request))
                                    .addScroll("10m")
                                    .addRoutingId(accountId)
                                    .addTemplateAndDataModel(Constants.Elastic.GET_HIDDEN_CAMPAIGN_CONVERSATIONS,data).addSize(10000)
                                    .build();
                            searchResponse=elasticSearchService.getSearchResult(esRequest);
                            if(Objects.isNull(searchResponse)){
                                continue;
                            }
                            if(searchResponse.status().getStatus()!=200){
                                log.info("[Archive Campaign Data Job] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
                                throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                            }
                            String scrollId=searchResponse.getScrollId();
                            while(true){
                                if(Objects.nonNull(searchResponse)&&(searchResponse.getHits().getHits().length!=0)){
                                    MigrateHiddenCampaignDto docIdToMigrate=getMCIDSListFromResponse(searchResponse,accountId);
                                    if(Objects.nonNull(docIdToMigrate)){
                                        log.info("[Archive Campaign Data Job] contacts data to migrate: {}",docIdToMigrate.getMcIds().size());
                                        docIdToMigrate.setSecondaryToTertiary(request.isSecondaryToTertiary());
                                        kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_MESSAGE_DOC_BY_MCID,accountId,
                                                docIdToMigrate);
                                    }
                                }
                                searchResponse=elasticSearchService.readMoreFromSearch(scrollId,"10m");
                                if(searchResponse.status().getStatus()!=200){
                                    log.info("[Archive Campaign Data Job] Failed to retrieve data from Elasticsearch with status code: {}",searchResponse.status().getStatus());
                                    throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                                }
                                if(Objects.isNull(searchResponse)||searchResponse.getHits().getHits().length==0){
                                    esService.clearScrollContext(scrollId);
                                    break;
                                }
                            }
                        }catch(Exception e){
                            log.info("[Archive Campaign Data Job] archival failed for account: {}",accountId);
                            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
                        }
                    }
                }
                log.info("[Archive Campaign Data Job] push event  Deleting Data from inbox_conversation index");
                if(request.isSecondaryToTertiary()){
                    LocalDateTime fourMonthsAgo=curTime.minusMonths(4);
                    long epochTimeMillis=fourMonthsAgo.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    request.setReindexTime(epochTimeMillis);
                }else{
                    LocalDateTime threeMonthsAgo=curTime.minusMonths(3);
                    long epochTimeMillis=threeMonthsAgo.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                    request.setReindexTime(epochTimeMillis);
                }
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.ARCHIVE_DELETE_CONVERSATIONS_EVENT,null,
                        request);
                long endTime=System.currentTimeMillis();
                LogUtil.logExecutionTime("[Archive Campaign Data Job] Ended",startTime,endTime);
            }
        }catch(Exception e){
            log.info("[Archive Campaign Data Job] archival failed");
        }
    }

    @Override
    public void archiveCampaignDataJobDeleteConversation(MigrateHiddenCampaignDto request){
        log.info("[Archive Campaign Data Job Delete] Deleting Data from inbox_conversation index");
        String range = request.getReindexTime().toString();
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("range",range);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        data.put("size", "10000");
        ESRequest deleteRequest = new ESRequest.Builder(new ESRequest()).addIndex(getIndex(request))
                .addTemplateAndDataModel(Constants.Elastic.DELETE_CAMPAIGN_HIDDEN_DATA,data).addSize(1000)
                .build();
        elasticSearchService.deleteByQueryWithRefresh(deleteRequest,false);
    }
    
    private String getRange(MigrateHiddenCampaignDto request){
        if(request.isSecondaryToTertiary()){
            return CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class).getProperty("campaign_archival_range_tertiary",
                            "now-4M");
        }else{
            return CacheManager.getInstance()
                    .getCache(SystemPropertiesCache.class).getProperty("campaign_archival_range",
                            "now-3M");
        }
    }
    
    private boolean reIndexContactDocument(MigrateHiddenCampaignDto request,String range){
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("hide", "true"))
                .must(QueryBuilders.termQuery("c_tag", "5"))
                .must(QueryBuilders.rangeQuery("lastUpdateDate").lt(range));
        if(!request.isSecondaryToTertiary()){
            return elasticSearchService.reindexByQuery(boolQuery,Constants.Elastic.CONTACT_INDEX,Constants.Elastic.ARCHIVE_CONTACT_INDEX);
        }else{
//            return elasticSearchService.reindexByQuery(boolQuery,Constants.Elastic.ARCHIVE_CONTACT_INDEX,Constants.Elastic.ARCHIVE_CONTACT_INDEX_TERTIARY);
            return true;
        }
    }
    
    private String getIndex(MigrateHiddenCampaignDto request){
        if(request.isSecondaryToTertiary()){
            return Constants.Elastic.ARCHIVE_CONTACT_INDEX;
        }else{
            return Constants.Elastic.CONTACT_INDEX;
        }
    }
    
    private MigrateHiddenCampaignDto updatedConversionBatch(List<Integer> conversationBatch,List<Integer> customerIdsBatchForConversation,Integer accountId){
        String range = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getProperty("campaign_archival_range",
                        "now-3M");
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("range",range);
        templateData.put("size",10000);
        templateData.put("mcIds",ControllerUtil.toCommaSeparatedString(conversationBatch));
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_HIDDEN_CAMPAIGN_CONVERSATION_BY_MCIDS,data).addSize(10000)
                .build();

        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        MigrateHiddenCampaignDto migrateHiddenCampaignDto = getMCIDSListFromResponse(searchResponse,accountId);
        return migrateHiddenCampaignDto;
    }
    
}
