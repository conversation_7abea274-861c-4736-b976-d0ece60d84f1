/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.secure.messaging.SecureMessageReceiveResponse;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.SecureMessagingException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingCommonService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SecureMessageReceiveEventHandler extends MessageEventHandlerAbstract {
    private final MessengerEvent SECURE_MESSAGE_RECEIVE = MessengerEvent.SECURE_MESSAGE_RECEIVE;

    private final BusinessService businessService;

    private final MessengerMessageService messengerMessageService;

    private final MessengerMediaFileService messengerMediaFileService;

    private final PulseSurveyService pulseSurveyService;

    private final SecureMessagingMessageService secureMessagingMessageService;

    private final SecureMessagingCommonService secureMessagingCommonService;

    @Override
    public MessengerEvent getEvent() {
        return SECURE_MESSAGE_RECEIVE;
    }

    @Override
    public SecureMessageReceiveResponse handle(MessageDTO messageDTO) throws Exception {
        log.info("handle called with : {}", messageDTO);
        getMessengerContact(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        Integer accountId = Objects.nonNull(businessDTO) ? businessDTO.getAccountId() : null;
        if (Objects.isNull(accountId) || Integer.valueOf(0).equals(businessService.isMessengerEnabled(accountId))) {
            log.info("SecureMessaging: Messenger disabled for business {}", accountId);
            throw new SecureMessagingException(ErrorCode.SECURE_MESSAGING_NOT_ENABLED);
        }
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        sendMessageDTO.setBusinessIdentifierId(businessDTO.getBusinessId().toString());
        messageDTO.setMsgTypeForResTimeCalc("R");
        // 4. Save back up message in mySQl
        secureMessagingMessageService.publishSecureMessagingActivity(sendMessageDTO, ActivityType.SECURE_CHAT_STARTED);
        MessageDocumentDTO messageDocumentDTO = saveInSecondaryStorage(messageDTO);
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);

        // handle PulseSurveyContext
		PulseSurveyContext context = null;
		try {
			context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
			if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
				customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
			} else {
				customerDTO.setOngoingPulseSurvey(false);
			}
			messageDTO.setCustomerDTO(customerDTO);
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		super.handle(messageDTO);

		return new SecureMessageReceiveResponse(Integer.valueOf(messageDocumentDTO.getM_id()),
                messageDocumentDTO.getCr_time());
    }

    private MessageDocumentDTO saveInSecondaryStorage(MessageDTO messageDTO) {
        log.info("saveInSecondaryStorage called ");
        MessengerMessage msg = getMsg(messageDTO);
        SecureMessage secureMessage = getSecureMsg(messageDTO);
        // Save all in single transaction
        Map.Entry<MessengerMessage, SecureMessage> entry = messengerMessageService.saveSecureMessage(msg,
                secureMessage);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        List<MessengerMediaFile> mediaFiles = sendMessageDTO.getMediaUrls().stream()
                .map(media -> new MessengerMediaFile(new MessengerMediaFileDTO(media), entry.getValue().getId()))
                .collect(Collectors.toList());
        messengerMediaFileService.saveAll(mediaFiles);
        return new MessageDocumentDTO(entry.getValue(), entry.getKey(), mediaFiles);
    }

    @Override
    public void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.SECURE_MESSAGE.name());
        lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.SECURE_MESSAGE.getSourceId());
        lastMessageMetadataPOJO.setLastMessageSource(Source.SECURE_MESSAGE.getSourceId());
        Date lastMsgOn = new Date();
        messengerContact.setLastMsgOn(lastMsgOn);
        messengerContact.setUpdatedAt(lastMsgOn);
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        log.info("updateLastMessageMetaData successfull");
    }

    @Override
    public void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact mc = getMessengerContact(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
            mc.setLastMessage("Received an attachment.");
        } else {
            mc.setLastMessage(sendMessageDTO.getBody());
        }
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(mc, 1, customerDTO.getPhone(),
                businessDTO.getBusinessNumber());
        mc.setEncrypted(isEncrypted ? 1 : 0);
        log.info("alterAndUpdateLastMessage successfull");
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        return null;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {

    }

    @Override
    public MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact mc = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            Integer id = Integer.valueOf(sendMessageDTO.getToCustomerId());
            Integer customerId = getCustomerDTO(sendMessageDTO).getId();
            MessengerContact messengerContact = messengerContactService.findMessengerContactByMcIdAndCustomerId(id,
                    customerId);
            secureMessagingCommonService.validateMessengerContact(messengerContact);
            secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(messengerContact, null, null);
            if (Objects.nonNull(messengerContact)) {
                messageDTO.setMessengerContact(messengerContact);
                mc = messengerContact;
            }
        }
        log.info("getMessengerContact id : {}", mc.getId());
        return mc;
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
            SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            Integer customerId = Integer.valueOf(sendMessageDTO.getCustomerId());
            customerDTO = contactService.findById(customerId);
            secureMessagingCommonService.checkIfMessengerContactOrCustomerBlocked(null, customerDTO, null);
            if (Objects.nonNull(customerDTO)) {
                messageDTO.setCustomerDTO(customerDTO);
            }
        }

        log.info("getCustomerDTO id : {}", customerDTO.getId());
        return customerDTO;
    }

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            Integer businessId = getMessengerContact(messageDTO).getBusinessId();
            businessDTO = businessService.getBusinessDTO(businessId);

            messageDTO.setBusinessDTO(businessDTO);
        }
        log.info("getBusinessDTO id : {}", businessDTO.getBusinessId());
        return businessDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if (getSendEmailNotification(messageDTO)) {
            return MessageTag.UNREAD;
        } else
            return MessageTag.INBOX;
    }

    @Override
    protected boolean getSendEmailNotification(MessageDTO messageDTO) {
        return true;
    }

    @Override
    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZone());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setMsgId(Integer.valueOf(messageDTO.getMessageDocumentDTO().getM_id()));
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(0); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        notificationRequest.setLastMsgTime(messageDTO.getMessageDocumentDTO().getCr_time());
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }

    private MessengerMessage getMsg(MessageDTO messageDTO) {
        MessengerMessage msg = new MessengerMessage();
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        msg.setAccountId(businessDTO.getAccountId());
        msg.setChannel(MessageDocument.Channel.SECURE_MESSAGE.name());
        msg.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE.name());
        msg.setMessageType(MessageDocument.MessageType.CHAT.name());
        msg.setMessengerContactId(getMessengerContact(messageDTO).getId());
        msg.setCreatedDate(new Date());
        return msg;
    }

    private SecureMessage getSecureMsg(MessageDTO messageDTO) {
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        SecureMessage secureMessage = new SecureMessage();
        String body = sendMessageDTO.getBody();
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        Date date = new Date();
        secureMessage.setCustomerId(customerDTO.getId());
        secureMessage.setMessageBody(body);
        secureMessage.setStatus("received");
        secureMessage.setSentOn(date);
        secureMessage.setEncrypted(0);
        secureMessage.setBusinessId(businessDTO.getBusinessId());
        secureMessage.setUpdatedOn(date);
        secureMessage.setMcId(getMessengerContact(messageDTO).getId());
        return secureMessage;
    }

}
