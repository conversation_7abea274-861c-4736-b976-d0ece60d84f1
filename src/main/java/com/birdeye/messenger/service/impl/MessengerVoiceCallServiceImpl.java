package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import com.birdeye.messenger.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dao.entity.VoiceCall;
import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.dto.BusinessReceptionistResponse;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ChatbotService;
import com.birdeye.messenger.external.service.ChatbotService.QueryChannel;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerVoiceCallService;
import com.birdeye.messenger.service.PostEventHandler;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.ReceptionistService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.RejectedMissedCallAuditService;
import com.birdeye.messenger.service.RobinService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.VoiceCallService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.BusinessHoursUtility;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.RequestDtoValidator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service class for Twilio voice call related operations
 * 
 */
@Service("messengerVoiceCallService")
@Slf4j
@RequiredArgsConstructor
public class MessengerVoiceCallServiceImpl implements MessengerVoiceCallService {

	@Autowired
	private BusinessService businessService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private SmsService smsService;

	@Autowired
	private VoiceCallService voiceCallService;

	@Autowired
	private PostEventHandler postEventHandler;

	@Autowired
	private ReceptionistService receptionistService;

	@Autowired
	@Lazy
	private MessengerVoiceCallService self;

	@Autowired
	private ChatbotService chatbotService;

	@Autowired
	private RejectedMissedCallAuditService missedCallAuditService;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private MessengerContactRepository  messengerContactRepository;

	@Autowired
	private RobinService robinService;

	@Autowired
    private RedisLockService redisLockService;

	@Autowired
    protected KafkaService kafkaService;

	@Autowired
    private NLPService nlpService;
	
	@Autowired
    private CommonService commonService;

	@Override
	public void processCall(CallReceiveDTO request, BusinessDTO businessDTO,CustomerDTO customerDTO) throws MessengerException {

		log.info("Processing missed call receive event");

		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.VOICECALL_RECEIVE_EVENT_DELAYED_QUEUE,
						request);
				return;
			}
			if (customerDTO != null) {
				// Audit call..
				VoiceCallDto voiceCall = voicecallDto(request);
				voiceCall.setBusinessId(businessDTO.getBusinessId());
				voiceCall.setCustomerId(customerDTO.getId());
				VoiceCallDto dto = voiceCallService.save(voiceCall);
				//In case of missed call from spam number no autoreply will be triggered
				boolean isAutoReplyApplicable = processCallWithoutAutoReply(dto,customerDTO,businessDTO);
				if(isAutoReplyApplicable) {
					processCallAutoReply(dto, true);
				}
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	// Extra settings for message doc
	private SmsDTO voiceCallSmsDTO(Sms sms, Integer source) {
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setChannel(Channel.VOICE_CALL);
		conversationDTO.setCommunicationDirection(CommunicationDirection.SEND);
		SmsDTO dto = new SmsDTO(sms, conversationDTO);
		dto.setMessageBodyUnencrypted(sms.getMessageBodyUnencrypted());
		if (source==Source.LIVE_CHAT_SEND.getSourceId()) {
			dto.setSource(source);
		}
		dto.setRecordingDuration(sms.getRecordingDuration());
		dto.setCreateDate(sms.getCreateDate());
		return dto;
	}


	private SmsDTO autoReplySMSToCustomer(VoiceCallDto dto, String body, Integer source) {
		SmsDTO smsDto = new SmsDTO();
		smsDto.setBusinessId(dto.getBusinessId());
		smsDto.setMessageBodyUnencrypted(body);
		smsDto.setCustomerId(dto.getCustomerId());
		// From Customer
		smsDto.setToNumber(dto.getFromNumber());
		// To Business
		smsDto.setFromNumber(dto.getToNumber());
		smsDto.setSource(source == Source.LIVE_CHAT_SEND.getSourceId() ? Source.VOICE_CALL_AUTOREPLY.getSourceId() : source);
		smsDto.setRecordingDuration(dto.getDuration());
		smsDto.setSpam(dto.getSpam());
		return smsDto;
	}

	private KontactoRequest create(CallReceiveDTO callReceiveRequest, String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setPhone(callReceiveRequest.getFromNumber());
		kontactoRequest.setSource(KontactoRequest.VOICECALL);
		// kontactoRequest.setSmsEnabled(1);
		// Inheriting country code from business.
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	/**
	 * New flow with recording support. a) If Receptionist is OFF, go to old flow.
	 * VoiceCallServiceImpl#processCall()
	 */
	@Override
	public void handleCallBack(CallReceiveDTO request) throws MessengerException {
		RequestDtoValidator.validateVoiceCallRequest(request);
		// check if duplicate message is present with same twiilio call Id
		long startTime = System.currentTimeMillis();
		List<VoiceCall> existingVoicecall = voiceCallService.findByCallSid(request.getTwilioCallId());
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("findByCallSid", startTime, endTime);
		if(CollectionUtils.isNotEmpty(existingVoicecall)) {
			log.info("MessengerVoiceCallServiceImpl: discarded as duplicate call with same callSId {}", request.getTwilioCallId());
			throw new BadRequestException("Voicecall discarded as other call found with same callSId");
		}

		// check for profanity : BIRD-5436
		if(StringUtils.isNotBlank(request.getTranscription()) && nlpService.isTextProfane(request.getTranscription())) {
			log.warn("VoiceCall: Input message not saved due to profanity filtering {}", request);
			return;
		}
		String toBusinessNumber = request.getToNumber();
		InfoByPhoneNumberRequest businessRequest = new InfoByPhoneNumberRequest(InfoByPhoneNumberRequest.Type.voice, toBusinessNumber);
		BusinessDTO business = businessService.getBusinessInfoByPhoneNumber(businessRequest);
		if (business == null) {
			throw new BadRequestException("No business found with number { " + toBusinessNumber + " }");
		}
		if (isVoiceCallsRejectForAccount(business.getEnterpriseId())) {
			log.warn("Account VoiceCalls Reject. Account Id  {}, Call from {}", business.getEnterpriseId(), request.getFromNumber());
			return;
		}
		// Receptionist Enabled = BusinessOptions feature AND Receptionist Setting(Removed).
		boolean receptionistEnabled = receptionistService.isReceptionistEnabledCheck(business);
		//1. Receptionist is disabled : continue on old flow

		// GET or CREATE customer
		CustomerDTO customer = null;
		List<Integer> accountIds=contactService.getAllContactUpgradeEnabledAccounts();
        if(CollectionUtils.isNotEmpty(accountIds)&& business!=null && accountIds.contains(business.getAccountId())){
        	customer=commonService.getActiveCustomerAndUpdateConversationState(
    				request.getFromNumber(), null, business.getBusinessId(), business.getAccountId());
        }		
		if (Objects.isNull(customer)) {
			KontactoRequest kontactoRequest = create(request, business.getCountryCode());
			kontactoRequest.setBusinessId(business.getBusinessId());
			customer = contactService.getorCreateNewCustomer(kontactoRequest, business.getRoutingId(), -1);
		}
		if (!receptionistEnabled) {
			processCall(request, business,customer);
			return;
		}

		//2. MissedCall Event
		if(request.getRecordingStatus()==null || "Absent".equalsIgnoreCase(request.getRecordingStatus())) {
			processCall(request, business,customer);
			return;
		}
		// GET or CREATE customer
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customer.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.VOICECALL_RECEIVE_EVENT_DELAYED_QUEUE,
						request);
				return;
			}
			if (customer != null) {
				// Save voice call messages.
				VoiceCallDto voiceCall = voicecallDto(request);
				voiceCall.setBusinessId(business.getBusinessId());
				voiceCall.setCustomerId(customer.getId());
				VoiceCallDto dto = voiceCallService.save(voiceCall);

				// ****** UPDATE MESSENGER CONTACT with Contact & SMS information ******///

				MessengerData data = postEventHandler.handleVoiceMail(business, customer, dto,request);
				log.info("Voice call receive event processed with customer#{} and messenger contact#{}",
						customer.getId(), data.getContactDocument().getM_c_id());

				// handle PulseSurveyContext
				PulseSurveyContext context = null;
				try {
					context = pulseSurveyService.handlePulseSurveyContext(null, customer, business);
				} catch (Exception ex) {
					log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
				}
				ContactDocument contactDocument = data.getContactDocument();
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
					contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					contactDocument.setOngoingPulseSurvey(false);
				}
				messengerContactService.updateContactOnES(data.getContactDocument().getM_c_id(), contactDocument, business.getAccountId());

				// Push event for auto-reply in async path.
				if (Boolean.FALSE.equals(customer.getBlocked())) {
					dto.setSpam(contactDocument.getSpam()!=null ? contactDocument.getSpam(): false);	//BIRDEYE-151516 TODO: remove this if it is not necessary
					self.processCallAutoReply(dto, false);
				}
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private Boolean isVoiceCallsRejectForAccount(Integer accountId) {
		String voiceCallsRejectAccounts = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("account.voiceCallsReject", "717879");
		if (voiceCallsRejectAccounts != null)
			return voiceCallsRejectAccounts.contains(String.valueOf(accountId));

		return false;
	}

	private VoiceCallDto voicecallDto(CallReceiveDTO request) {
		VoiceCallDto dto = new VoiceCallDto();
		dto.setCallSid(request.getTwilioCallId());
		// Customer phone
		dto.setFromNumber(request.getFromNumber());
		// Business phone
		dto.setToNumber(request.getToNumber());
		dto.setSource(Source.VOICE_CALL.getSourceId()); // Since message doc is common.
		// Recording fields.
		dto.setRecordingSid(request.getRecordingSid());
		dto.setRecordingUrl(request.getRecordingUrl());
		if (StringUtils.isEmpty(request.getTranscription())){
			dto.setTranscription(MessengerConstants.NO_TRANSCRIPTION_MESSAGE);
		} else {
			dto.setTranscription(request.getTranscription());
		}
		// TODO: Need to see if twilio dates are availabl here
		dto.setCreateDate(new Date());
		if  (request.getRecordingDuration()  != null)
			dto.setDuration(request.getRecordingDuration());
		dto.setSpam(request.getSpam());
		return dto;
	}

	//TODO Event
	@Async
	public void processCallAutoReply(VoiceCallDto dto, boolean missedCall) {
		log.info("Processing voice call auto reply for {} ", dto);

		String toBusinessNumber = dto.getToNumber();
		Integer customerId = dto.getCustomerId();

		CustomerDTO customerDTO = contactService.findById(customerId);
		InfoByPhoneNumberRequest businessRequest = new InfoByPhoneNumberRequest(InfoByPhoneNumberRequest.Type.voice, toBusinessNumber);
		BusinessDTO business = businessService.getBusinessInfoByPhoneNumber(businessRequest);

		//fetch robin configuration for business
		RobinAutoReplyConfig robinAutoReplyConfig = robinService.getRobinAutoReplyConfigForBusiness(business.getBusinessId(), business.getAccountId(), Source.VOICE_CALL);

		// Prepare SMS Message for business.
		Integer sourceId = Source.VOICE_CALL_AUTOREPLY.getSourceId();
		String autoReplyText = null;
		if(missedCall) {
			//Auto reply text for inbox as well as non-inbox businesses
			autoReplyText = getMissedCallAutoReplyText(business, customerDTO.getFirstName());
			if (autoReplyText ==  null) {
				//Audit Rejected Call
				missedCallAuditService.saveRejectedMissedCalls(dto);
				return;
			}
			sourceId = Source.MISSED_CALL_AUTOREPLY.getSourceId();

		} else if(robinAutoReplyConfig == null || robinAutoReplyConfig.isEnableRobin()) {
			if (StringUtils.isNotEmpty(dto.getTranscription())){
				// Check the session status if in AUTO mode.
				BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(business.getBusinessId(),false);
				Boolean isReceivedDuringBusinessHour = null;
				if (businessTimingDTO != null){
					isReceivedDuringBusinessHour = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
				}
				log.info("Calling Robin for Intent Match with the voicemail transcription for businessId: {}", business.getBusinessId());
				Optional<ChatbotQueryResponse> chatbotQueryResponseOptional = chatbotService
						.getQueryResponse(business.getBusinessId(), business.getRoutingId(), dto.getTranscription(), QueryChannel.VOICE_CALL,
								customerDTO.getId(), null, isReceivedDuringBusinessHour, new LivechatAnonymousConversationDataDto(), null);
				boolean chatbotToReply = chatbotQueryResponseOptional.isPresent();
				if (chatbotToReply) {
					boolean partial = false;
					if (chatbotQueryResponseOptional.get().getData().get("partial") != null)
						partial = (boolean) chatbotQueryResponseOptional.get().getData().get("partial");
					if (partial) {
						//all tokens not replaced
						autoReplyText = "We could not find the requested information for your number "+ customerDTO.getPhone();
					}  else {
						autoReplyText = chatbotQueryResponseOptional.get().getData().get("response").toString();
					}
					sourceId = Source.LIVE_CHAT_SEND.getSourceId();
				}
			}
		}
		//If no response provided by Robin or DEFAULT_FALLBACK, send configured auto-reply message
		//or robin is off for the location/enterprise
		if (StringUtils.isEmpty(autoReplyText)) {
			autoReplyText = receptionistService.getReceptionistAutoReplyMessage(business, dto, customerDTO.getFirstName());
			sourceId = Source.VOICE_CALL_AUTOREPLY.getSourceId();
		} else {
			autoReplyText = receptionistService.getFormattedGreeting(business, autoReplyText);
			String cName = customerDTO.getFirstName();
			String customerName = StringUtils.isEmpty(cName) ? "there" : receptionistService.extractFirstNameOnly(cName);
			autoReplyText = autoReplyText.replace("[Contact first name]", customerName);
		}
		// fromNumber is set as voiceNumber whereas it should be smsNumber for texting
		BusinessReceptionistResponse phoneNumbers  = businessService.getDataForReceptionist(business.getBusinessId());
		if (CollectionUtils.isNotEmpty(phoneNumbers.getData())) {
			String fromNumber = phoneNumbers.getData().get(0).getSmsPhoneNumber();
			dto.setToNumber(smsService.getFormattedBusinessNumber(business.getBusinessId(), fromNumber));
		} else {
			log.info("Autoreply SMS number not found for business :: {}", business.getBusinessId());
		}
		SmsDTO autoReplyToVoiceCall = autoReplySMSToCustomer(dto, autoReplyText, sourceId);

		log.info("Preparing autoreply sms for businessId :: {} :::: {} ",
				dto.getBusinessId(), autoReplyToVoiceCall);

		Sms sms = smsService.saveAndSend(autoReplyToVoiceCall,business);

		// ****** UPDATE MESSENGER CONTACT with Contact & SMS information ******//
		MessengerData data = postEventHandler.handleCallAutoReply(business, customerDTO, voiceCallSmsDTO(sms, sourceId),dto.getSpam());

		log.info("Auto reply for Voice call with customer#{} and messenger contact#{} is processed.", customerDTO.getId(),
				data.getContactDocument().getM_c_id());

	}

	private String getMissedCallAutoReplyText(BusinessDTO business, String cName) {
		String autoReplyText = null;
		BusinessOptionResponse businessOptionResponse = businessService.getBusinessOptionsConfig(business.getAccountId(), true);
		Integer receptionistEnabled = 0;
		Integer inboxEnabled = 0;
		if (businessOptionResponse!=null && (businessOptionResponse.getEnableMessenger()!=null && businessOptionResponse.getEnableMessenger()==1))
			inboxEnabled = 1;
		if (businessOptionResponse!=null && (businessOptionResponse.getEnableReceptionist()!=null && businessOptionResponse.getEnableReceptionist()==1))
			receptionistEnabled = 1;

		BusinessReceptionistConfiguration config = receptionistService.getBusinessReceptionistConfiguration(business);
		if (config != null && config.getMissedCallReject() == 1) {
			log.error("[Receptionist] getMissedCallAutoReplyText : Missed call handling OFF for business Id {}", business.getBusinessId());
			return null;
		}

		if (receptionistEnabled == 0 && inboxEnabled == 0) {
			// handle non-inbox business missedcall auto-reply text
			autoReplyText = receptionistService.getReceptionistMissedCallAutoReplyNonInbox(business);
		} else if (receptionistEnabled == 0 && inboxEnabled == 1) {
			// handle inbox business missedcall auto-reply text
			autoReplyText = receptionistService.getReceptionistMissedCallAutoReplyInbox(business);
		} else if (receptionistEnabled == 1 && inboxEnabled == 1) {
			// handle configuration missedcall auto-reply text
			autoReplyText = receptionistService.getReceptionistConfiguredMissedCallAutoReply(business, cName);
		} else {
			log.error("[Receptionist] getMissedCallAutoReplyText : conflicting status of Receptionist and Inbox for business Id {}", business.getBusinessId());
		}
		return autoReplyText;
	}

	@Override
	public Integer countVoiceCalls(BizAppVoiceCallRequest request) {
		if(request.getStartDate() == null){
			return voiceCallService.countVoiceCallForBusinessIds(request);
		}else{
			return voiceCallService.countVoiceCallForBusinessIdsAndCreateDate(request);
		}
	}

	public boolean processCallWithoutAutoReply(VoiceCallDto dto,CustomerDTO customerDTO,BusinessDTO businessDTO){
		log.info("Process missed Call without Autoreply {}",dto);
		MessengerContact messengerContact = postEventHandler.handleMissedCallWithoutAutoReply(dto,businessDTO,customerDTO);
		messengerContactRepository.saveAndFlush(messengerContact);
		//Terminate PulseSurveyContext if it exist
		PulseSurveyContext context = null;
		try {
			if (customerDTO != null && customerDTO.getId()!=null) {
				context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
				if (context != null) {
					ContactDocument contactDocument = new ContactDocument();
					if (PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
						contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
					} else {
						contactDocument.setOngoingPulseSurvey(false);
					}
					messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, businessDTO.getRoutingId());
				}
			}
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		if(Boolean.FALSE.equals(messengerContact.getSpam())){
			log.info("Process Autoreply for the missedcall {}",dto);
			return true;
		}
        return false;
	}

}
