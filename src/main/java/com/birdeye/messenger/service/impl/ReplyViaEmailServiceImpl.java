package com.birdeye.messenger.service.impl;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.EmailMessageDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.ReplyViaEmailService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ReplyViaEmailServiceImpl implements ReplyViaEmailService {

	@Value("${email.reply.domain}")
	private String emailReplyDomain;

	@Autowired
	public  BusinessService businessService;

	@Autowired
	@Lazy
	private MessengerEventHandlerService messengerEventHandlerService;

	@Autowired
	private CommonService commonService;
	
	@Autowired
	private MessageService messageService;
	
	private final RedisHandler redisHandler;

	@Override
	public boolean checkAndReplyViaEmail(EmailMessageDTO emailMessageDTO) {
		String pattern = "^reply-(\\d+)-(\\d+)".concat(emailReplyDomain).concat("$");
		Pattern regex = Pattern.compile(pattern);
		Matcher matcher = regex.matcher(emailMessageDTO.getEmailDTO().getTo().get(0));
		if (matcher.matches()) {
			log.info("checkAndReplyViaEmail : true");
			String mcid = matcher.group(1);
			String locationNumber = matcher.group(2);

			BusinessDTO businessDTOReply = businessService.getBusinessByBusinessNumber(Long.parseLong(locationNumber));
			if(Objects.isNull(businessDTOReply))
				throw new NotFoundException("[getBusinessDTO] No business found for given business Number " + emailMessageDTO.getBusinessNumber());

			SendMessageDTO sendMessageDTO = new SendMessageDTO();
			sendMessageDTO.setFromBusinessId(businessDTOReply.getBusinessId());
			sendMessageDTO.setToCustomerId(mcid);
			sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTOReply.getBusinessId()));
			sendMessageDTO.setBody(removePreviousEmailChainFromBody(emailMessageDTO.getEmailDTO().getBody()));
			UserDTO userDto= null;
			if (redisHandler.isReplyViaEmailAllowed(emailMessageDTO.getEmailDTO().getFrom())) {
				if (emailMessageDTO.getEmailDTO().getFrom() != null) {
					userDto=commonService.getUserIdByEmail(emailMessageDTO.getEmailDTO().getFrom(), businessDTOReply.getBusinessId());
					sendMessageDTO.setUserId(userDto.getId());
				}
				if (sendMessageDTO.getUserId() == null) {
					throw new NotAuthorizedException(ErrorCode.USER_NOT_FOUND, ErrorCode.USER_NOT_FOUND.getErrorMessage());
				}
				try {
					log.info("debug log : Reply via email : {}", sendMessageDTO);
					MessageResponse sendResponse = messengerEventHandlerService.handleEvent(sendMessageDTO);
					if (Objects.nonNull(sendResponse) && Objects.nonNull(sendResponse.getMessages()) && Objects.nonNull(sendResponse.getMessages().first())
							&& CommunicationDirection.SEND.equals(sendResponse.getMessages().first().getDirection())) {
						//Add replyViaEmail : true to Message document
						String docId = sendResponse.getMessages().first().getId();
						messageService.updateReplyViaEmailStatus(docId, true);
					}
				} catch (Exception e) {
					log.info("Reply via Email failed : {}", e.getMessage());
					return false;
				}
			} else {
				log.error("Reply via Email limit breached : {}", emailMessageDTO.getEmailDTO().getFrom());
			}
			return true;
		}
		return false;
	}
	
	private String removePreviousEmailChainFromBody(String emailBody) {
		String[] chainDelimiters = {
				"________________________________",
				"From: .*",
				"-----Original Message-----",
				"On .* wrote:"
		};

		for (String delimiter : chainDelimiters) {
			if (emailBody.contains(delimiter)) {
				emailBody = emailBody.split(delimiter)[0].trim();
			}
		}

		String[] signatureIndicators = {
				"-- ",
				"Best regards,",
				"Thanks,",
				"Sincerely,",
				"Sent from my iPhone",
				"Sent from Outlook"
		};

		for (String indicator : signatureIndicators) {
			if (emailBody.contains(indicator)) {
				emailBody = emailBody.split(indicator)[0].trim();
				break;
			}
		}
		return emailBody;
	}
}
