/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessageResponse.Message;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SecureMessageSendEventHandler extends MessageEventHandlerAbstract {

    private final MessengerEvent SECURE_MESSAGE_SEND = MessengerEvent.SECURE_MESSAGE_SEND;

    private final CommonService commonService;

    private final MessengerMediaFileService messengerMediaFileService;

    private final BusinessService businessService;

    private final MessengerMessageService messengerMessageService;

    private final SecureMessagingMessageService secureMessagingMessageService;

    @Override
    public MessengerEvent getEvent() {
        return SECURE_MESSAGE_SEND;
    }

    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        // Responsible for time calculation
        if (!messageDTO.isBOT())
            messageDTO.setMsgTypeForResTimeCalc("S");
        getBusinessDTO(messageDTO);
        getCustomerDTO(messageDTO);
        getMessengerContact(messageDTO);
        getUserDTO(messageDTO);

        log.info("Send secure Message request From : {} to : {} ,messageDTO : {}",
                messageDTO.getBusinessDTO().getBusinessId(),
                messageDTO.getMessengerContact().getCustomerId(), messageDTO);

        SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
        SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages);
        sendResponse.setLastMsgSource(messageDTO.getSource());
        sendResponse.setMessages(messages);
        log.info("handle successfull");
        return sendResponse;
    }

    private SortedSet<Message> sendMessage(MessageDTO messageDTO) throws Exception {
        log.info("sendMessage called");
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        sendMessageDTO.setCustomerId(messengerContact.getCustomerId());
        sendMessageDTO.setSource(Source.SECURE_MESSAGE.getSourceId());
        sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
        commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO), "secure_message");
        return handleMultiMediaAndTextMessages(sendMessageDTO);
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        log.info("getMessageDocumentDTO called");
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(),
                getMessengerContact(messageDTO).getId());
        List<MessengerMediaFile> mediaFiles = dto.getMediaUrls();
        if (CollectionUtils.isNotEmpty(mediaFiles)) {
            messageDocumentDTO.setMediaFiles(mediaFiles.stream().map(MediaFile::new).collect(
                    Collectors.toList()));
        }
        messageDocumentDTO.setFrom(dto.getConversationDTO().getSender());
        messageDocumentDTO.setTo(dto.getConversationDTO().getRecipient());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }

    SortedSet<Message> handleMultiMediaAndTextMessages(MessageDTO messageDTO) throws Exception {
        log.info("handleMultiMediaAndTextMessages called");
        SortedSet<Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        UserDTO userDTO = getUserDTO(messageDTO);
        secureMessagingMessageService.publishSecureMessagingActivity(sendMessageDTO, ActivityType.SECURE_CHAT_STARTED);
        ConversationDTO conversationDTO = saveSecureMessage(messageDTO);
        List<MessengerMediaFile> mediaFiles = sendMessageDTO.getMediaUrls().stream()
                .map(media -> new MessengerMediaFile(new MessengerMediaFileDTO(media), conversationDTO.getId()))
                .collect(Collectors.toList());
        messengerMediaFileService.saveAll(mediaFiles);
        sendMessageDTO.setConversationDTO(conversationDTO);
        super.handle(sendMessageDTO);
        Message message = new Message(userDTO, conversationDTO, sendMessageDTO.getMediaUrls(),
                messageDTO.getBusinessDTO().getTimeZoneId());
        message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
        allMessages.add(message);
        log.info("handleMultiMediaAndTextMessages successfully returned : {}", allMessages);
        return allMessages;

    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
            Integer customerId = getMessengerContact(messageDTO).getCustomerId();
            customerDTO = contactService.findByIdNoCaching(customerId);
            messageDTO.setCustomerDTO(customerDTO);
        }
        if (customerDTO.getBlocked()) {
            throw new MessengerException(ErrorCode.CONTACT_IS_BLOCKED, ErrorCode.CONTACT_IS_BLOCKED.getErrorMessage());
        }
        log.info("getCustomerDTO id : {}", customerDTO.getId());
        return customerDTO;
    }

    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messengerContact)) {
            SendMessageDTO smsDTO = (SendMessageDTO) messageDTO;
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
                    businessDTO.getBusinessId(), smsDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        log.info("getMessengerContact id : {}", messengerContact.getId());
        return messengerContact;
    }

    /**
     * No Email Notification on Send Events
     */
    @Override
    MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        return null;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if (Objects.isNull(messageId)) {
            SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        publishEventIfRepliedOnUnassignedConversation(messageDTO);
    }

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = businessService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        log.info("getBusinessDTO id : {}", businessDTO.getBusinessId());
        return businessDTO;
    }

    private ConversationDTO saveSecureMessage(MessageDTO messageDTO) {
        log.info("saveSecureMessage called");
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();

        String senderId = businessDTO.getBusinessId().toString();
        String receiverId = messengerContact.getCustomerId().toString();
        UserDTO userDTO = messageDTO.getUserDTO();

        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        Date date = new Date();
        String message = sendMessageDTO.getBody();
        SecureMessage secureMessage = new SecureMessage();
        secureMessage.setCustomerId(Integer.parseInt(receiverId));
        secureMessage.setStatus("sent");
        secureMessage.setSentOn(date);
        secureMessage.setBusinessId(Integer.parseInt(senderId));
        secureMessage.setUpdatedOn(date);
        secureMessage.setMessageBody(message);
        MessengerMessage messengerMessage = new MessengerMessage();
        messengerMessage.setAccountId(businessDTO.getAccountId());
        messengerMessage.setChannel(MessageDocument.Channel.SECURE_MESSAGE.name());
        messengerMessage.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND.name());
        messengerMessage.setCreatedDate(date);
        messengerMessage.setSentThrough(MessageDocument.SentThrough.WEB.name());

        if (userDTO != null) {
            secureMessage.setUserId(userDTO.getId());
            messengerMessage.setCreatedBy(userDTO.getId());
        }
        messengerMessage.setMessageType(MessageDocument.MessageType.CHAT.name());
        Integer mcId = messengerContact.getId();
        messengerMessage.setMessengerContactId(mcId);
        secureMessage.setMcId(mcId);
        messengerMessageService.saveSecureMessage(messengerMessage, secureMessage);
        return new ConversationDTO(secureMessage);
    }

    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        log.info("getUserDTO id : {}", userDTO.getId());
        return userDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if (messageDTO.isBOT())
            return MessageTag.UNREAD;
        return MessageTag.INBOX;
    }

    // UPDATE MESSENGER CONTACT
    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("SEND");
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.SECURE_MESSAGE.name());
        lastMessageMetadataPOJO.setLastMessageSource(Source.SECURE_MESSAGE.getSourceId());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        if (BooleanUtils.isNotFalse(dto.getUpdateLastResponseAt())) {
            messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        }
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
        log.info("updateLastMessageMetaData successfull");
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        messengerContact.setLastMessage(sendMessageDTO.getBody());

        if (StringUtils.isEmpty(sendMessageDTO.getConversationDTO().getBody())
                && CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
            messengerContact.setLastMessage("Sent an attachment");
        }
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, 1, customerDTO.getPhone(),
                businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
        log.info("alterAndUpdateLastMessage successfull");
    }

}
