package com.birdeye.messenger.service.impl;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.xcontent.ToXContent;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessMoveAudit;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.MessengerNote;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledLocationRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetConfigRepository;
import com.birdeye.messenger.dao.repository.ConversationActivityRepository;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dao.repository.LiveChatWidgetConfigRepository;
import com.birdeye.messenger.dao.repository.LocationWidgetDetailsRepository;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dao.repository.MessengerMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerNoteRepository;
import com.birdeye.messenger.dao.repository.ResponseTimeOutBoxRepository;
import com.birdeye.messenger.dao.repository.SmsRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.ActivityMigrateDTO;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLite;
import com.birdeye.messenger.dto.BusinessMigrationBatchDto;
import com.birdeye.messenger.dto.ConvStateForResTimeCalc;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.GetUserIdsRequest;
import com.birdeye.messenger.dto.ImageUploadRequestToS3;
import com.birdeye.messenger.dto.ImageUploadToS3Response;
import com.birdeye.messenger.dto.ImportCsvMessage;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.LastRecievedMessageAggregation;
import com.birdeye.messenger.dto.LastRecievedMessageAggregation.Bucket;
import com.birdeye.messenger.dto.LastRecievedMessageAggregation.InnerHits;
import com.birdeye.messenger.dto.LocationWidgetDTO;
import com.birdeye.messenger.dto.MergeDuplicateContactsEvent;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageMigrationDto;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.MigrateBusinessIdInOldMessageDocumentRequest;
import com.birdeye.messenger.dto.MigrateLastReceivedMessageSourceRequest;
import com.birdeye.messenger.dto.PodiumImportMsgStatus;
import com.birdeye.messenger.dto.Record;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.UpdateFBAndGBMIdEvents;
import com.birdeye.messenger.dto.UpdateFBAndGBMIdRequest;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.elastic.MessageDocumentTemp;
import com.birdeye.messenger.dto.report.ResponseTimeMigrationDto;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BusinessMoveAuditEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.FacebookMessageStatusEnum;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.StatusEnum;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.BusinessMoveAuditService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.sro.BusinessMoveEvent;
import com.birdeye.messenger.sro.Conversation;
import com.birdeye.messenger.sro.CreateDeafaultCreationRequest;
import com.birdeye.messenger.sro.CreateDefaultConfigRequest;
import com.birdeye.messenger.sro.CustomerDetails;
import com.birdeye.messenger.sro.LocationDetails;
import com.birdeye.messenger.sro.Message;
import com.birdeye.messenger.sro.MessagesDeleteRequest;
import com.birdeye.messenger.sro.StatusRequest;
import com.birdeye.messenger.sro.User;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class MigrationServiceImpl implements MigrationService {

	public static final String VIA_FACEBOOK_MESSENGER = "via Facebook Messenger";
	private final MessengerContactService messengerContactService;
	private final CommunicationHelperService communicationHelperService;
	private final ThreadPoolTaskExecutor poolExecutor;
	private final SmsService smsService;
	private final ContactService contactService;
	private final KafkaService kafkaService;
	private final LocationWidgetDetailsRepository locationWidgetDetailsRepository;
	private final WebchatService webchatService;
	private final BusinessChatWidgetConfigService businessChatWidgetConfigService;
	private final BusinessChatWidgetConfigRepository businessChatWidgetConfigRepository;
	private final BusinessService businessService;
	private final BusinessMoveAuditService businessMoveAuditService;
	private final MessengerMessageRepository messengerMessageRepository;
	private final MessengerNoteRepository messengerNoteRepository;
	private final SmsRepository smsRepository;
	private final MessengerMediaFileService messengerMediaFileService;
	private final FacebookMessageRepository facebookMessageRepository;
	private final ConversationActivityRepository conversationActivityRepository;
	private final CommonService commonService;
	private final ConversationActivityService conversationActivityService;
	private final MessengerContactRepository messengerContactRepository;
	private final ResponseTimeOutBoxRepository responseTimeOutBoxRepository;
	private final LiveChatWidgetConfigRepository liveChatWidgetConfigRepository;

	private final SocialService socialService;

	@Autowired
	@Lazy
	private MessageService messageService;
	@Autowired
	@Lazy
	private ConversationService conversationService;
	@Autowired
	private BusinessChatEnabledLocationRepository businessChatEnabledLocationRepository;

	private final ElasticSearchExternalService elasticSearchService;

	private final ObjectMapper objectMapper;

	private final MessengerMessageService messengerMessageService;

	@Override
	public Integer migrate(MessengerFilter messengerFilter) {
		Date startDate = MessengerUtil.parseDate(messengerFilter.getStartDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS,
				TimeZone.getTimeZone("UTC"));

		Map<Integer, List<MessengerContact>> businessIdToContactList = messengerContactService
				.findAllByUpdatedAtAfter(startDate)
				.stream()
				.collect(Collectors.groupingBy(MessengerContact::getBusinessId, HashMap::new,
						Collectors.toCollection(ArrayList::new)));
		AtomicInteger contactsUpdated = new AtomicInteger(0);
		businessIdToContactList.forEach((businessId, contactList) -> {
			BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(businessId);
			// String businessSmsNumber =
			// platformDbRepository.getBusinessSmsNumberById(businessId);
			String businessSmsNumber = businessService.getBusinessSmsNumberById(businessId);
			if (Objects.nonNull(businessDTO)) {
				log.info("------------------------ Contact Documents Migrated to ES ----------------- ");
				contactList.forEach(messengerContact -> {
					UserDTO userDTO = communicationHelperService.getUserDTO(messengerContact.getAssignee());
					MessageTag tag = MessageTag.getMessageTagById(messengerContact.getTag());
					CustomerDTO customerDTO = communicationHelperService
							.getCustomerDTO(messengerContact.getCustomerId());
					ContactDocument contactDocument = messengerContactService.updateContactOnES(messengerContact,
							customerDTO, businessDTO, tag, null);
					String jsonString = new ESRequest.Upsert<>(contactDocument, messengerContact.getId().toString())
							.toJson();
					log.info(MessengerUtil.jSonPrettyPrinter(jsonString) + "\n");
					contactsUpdated.incrementAndGet();
					migrateSMSData(businessDTO, messengerContact, userDTO, tag, businessSmsNumber);
					// poolExecutor.execute(() -> migrateSMSData(businessDTO, messengerContact,
					// userDTO, tag));
					// poolExecutor.execute(() -> migrateFaceBookData(businessDTO, messengerContact,
					// userDTO, tag));
				});
			} else {
				log.info("Data not migrated for business id {}", businessId);
			}
		});
		log.info("Migrated data for {} contacts", contactsUpdated.get());
		return contactsUpdated.get();
	}

	private void migrateSMSData(BusinessDTO businessDTO, MessengerContact messengerContact, UserDTO userDTO,
			MessageTag tag, String businessSmsNumber) {
		if (Objects.nonNull(messengerContact.getCustomerId())) {
			List<Sms> smsList = smsService.findByCustomerId(messengerContact.getCustomerId());
			List<SmsDTO> smsDTOList = smsList.stream().map(sms -> new SmsDTO(sms, null)).collect(Collectors.toList());
			for (SmsDTO smsDTO : smsDTOList) {
				MessengerEvent event = smsDTO.getFromNumber().equalsIgnoreCase(businessSmsNumber)
						? MessengerEvent.SMS_SEND
						: MessengerEvent.SMS_RECEIVE;
				MessengerMediaFileDTO messengerMediaFileDTO = getMessengerMediaFile(smsDTO.getMediaURL());
				MessageDocument messageDocument = null;
				if (event.equals(MessengerEvent.SMS_SEND)) {
					messageDocument = messengerContactService.andNewMessageOnEs(
							new MessageDocumentDTO(smsDTO, messengerContact.getId()), messengerMediaFileDTO, userDTO,
							businessDTO, event);
				} else {
					messageDocument = messengerContactService.andNewMessageOnEs(
							new MessageDocumentDTO(smsDTO, messengerContact.getId()), messengerMediaFileDTO, null,
							businessDTO, event);
				}
				String jsonString = new ESRequest.Upsert<>(messageDocument, messengerContact.getId().toString())
						.toJson();
				log.info(MessengerUtil.jSonPrettyPrinter(jsonString) + "\n");
			}
			// log.info("Migrated sms data for business {} to elastic search {} ",
			// smsDTOList, businessDTO.getBusinessId());
		}
	}

	private MessengerMediaFileDTO getMessengerMediaFile(String mediaURL) {
		if (Objects.nonNull(mediaURL)) {
			String extension = FilenameUtils.getExtension(mediaURL);
			String fileName = FilenameUtils.getName(mediaURL);
			MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO();
			messengerMediaFileDTO.setUrl(mediaURL);
			if (extension.equalsIgnoreCase("jpg") || extension.equalsIgnoreCase("JPEG")) {
				messengerMediaFileDTO.setContentType("image/jpg");
				messengerMediaFileDTO.setName(fileName + ".jpg");
			}
			if (extension.equalsIgnoreCase("png")) {
				messengerMediaFileDTO.setContentType("image/png");
				messengerMediaFileDTO.setName(fileName + ".png");
			}
			return messengerMediaFileDTO;
		}
		return null;
	}

	@Override
	public void migrateByMessengerContactId(Integer contactId, Boolean migrateSMS) {
		MessengerContact messengerContact = messengerContactService.findById(contactId);
		BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(messengerContact.getBusinessId());
		// String businessSmsNumber =
		// platformDbRepository.getBusinessSmsNumberById(businessDTO.getBusinessId());
		String businessSmsNumber = businessService.getBusinessSmsNumberById(businessDTO.getBusinessId());
		UserDTO userDTO = communicationHelperService.getUserDTO(messengerContact.getAssignee());
		MessageTag tag = MessageTag.getMessageTagById(messengerContact.getTag());
		CustomerDTO customerDTO = communicationHelperService.getCustomerDTO(messengerContact.getCustomerId());
		ContactDocument contactDocument = messengerContactService.updateContactOnES(messengerContact, customerDTO,
				businessDTO, tag, null);
		String jsonString = new ESRequest.Upsert<>(contactDocument, messengerContact.getId().toString()).toJson();
		log.info(MessengerUtil.jSonPrettyPrinter(jsonString) + "\n");
		if (migrateSMS) {
			migrateSMSData(businessDTO, messengerContact, userDTO, tag, businessSmsNumber);
		}
	}

	@Override
	public void migrateImageUrl(Integer messageId) {
		Sms sms = smsService.findById(messageId);
		if (StringUtils.isNotEmpty(sms.getMediaURL())) {
			MessageDocument messageDocument = new MessageDocument();
			messageDocument.setA_url(sms.getMediaURL());
			ESRequest.Upsert<MessageDocument> upsert = new ESRequest.Upsert<>(messageDocument, messageId.toString());
			BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(sms.getBusinessId());
			ESRequest esRequest = new ESRequest.Builder(new ESRequest())
					.addIndex(Constants.Elastic.MESSAGE_INDEX)
					.addRoutingId(businessDTO.getRoutingId())
					.addPayloadForUpsert(upsert).build();
			boolean updated = elasticSearchService.updateDocument(esRequest, false);
			log.info("Media URL update status for messageId {} is  {} ", messageId, updated);
		} else {
			log.info("Media URL found empty for message id {} ", messageId);
		}
	}

	@Override
	public void updateAndMigrateContactDataToES(Integer messengerContactId) {
		MessengerContact messengerContact = messengerContactService.findById(messengerContactId);
		if (messengerContact != null && messengerContact.getCustomerId() == null) {
			KontactoRequest kontactoRequest = contactService.createContactoRequest(messengerContactId);
			if (kontactoRequest == null) {
				log.info("No Facebook messages found for Messenger Contact id {} ", messengerContactId);
				return;
			}
			log.info("Requesting contact service to get/create customer for messenger contactId {}",
					messengerContactId);
			CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
					kontactoRequest.getRoutingId(),
					-8);
			if (customerDTO.getId() != null) {
				log.info("Customer created Id {} for messenger contact Id {}",
						customerDTO.getId(), messengerContactId);
				ContactDocument contactDocument = new ContactDocument();
				contactDocument.setC_id(customerDTO.getId());
				messengerContact.setCustomerId(customerDTO.getId());
				ESRequest.Upsert<ContactDocument> upsert = new ESRequest.Upsert<>(contactDocument,
						messengerContactId.toString());
				messengerContactService.saveOrUpdateMessengerContact(messengerContact);
				ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
						.addRoutingId(kontactoRequest.getRoutingId())
						.addPayloadForUpsert(upsert).build();
				boolean updated = elasticSearchService.updateDocument(esRequest, false);
				log.info("Customer ID update status for Messenger Contact Id {} is  {} ", messengerContactId, updated);
			}
		} else {
			log.info("MessengerContact is either null or customer is already associated with this contact id {} ",
					messengerContactId);
		}
	}

	@Override
	public void migrateImageUrl() {
		int[] smsIdS = getSMSIdS();
		Arrays.stream(smsIdS).forEach(smsId -> {
			try {
				migrateImageUrl(smsId);
			} catch (Exception e) {
				log.error("[migrateImageUrl] exception occurred while migrating for smsId " + smsId, e);
			}
		});
	}

	private int[] getSMSIdS() {
		int[] list = { ********, ********, ********, ********, ********, ********, ********, ********, ********,
				********, ********, ********, ********, ********, ********, ********, ********, ********, ********,
				********, ********, ********, ********, ********, ********, ********, 86718722, 86722620, 86722844,
				86724124, 86725432, 86725442, 86725446, 86725546, 86726613, 86727590, 86727717, 86727781, 86727799,
				86728513, 86730309, 86730325, 86732559, 86733156, 86734773, 86737087, 86738022, 86738028, 86738095,
				86738257, 86738462, 86740049, 86741085, 86741706, 86743376, 86745342, 86748739, 86749470, 86750740,
				86750891, 86754476, 86755869, 86756341, 86757366, 86757376, 86760791, 86760794, 86761209, 86762305,
				86763089, 86764990, 86764991, 86774851, 86774915, 86777104, 86777106, 86777111, 86782032, 86782324,
				86782427, 86782549, 86787965, 86789122, 86790465, 86791859, 86792301, 86801512, 86803101, 86807848,
				86808527, 86808703, 86808782, 86808896, 86808904, 86808907, 86808913, 86809023, 86809692, 86818679,
				86821993, 86822291, 86825377, 86843392, 86843398, 86843405, 86843440, 86850943, 86855213, 86856642,
				86857643, 86858280, 86859537, 86860179, 86861000, 86862817, 86863909, 86864231, 86865408, 86865432,
				86865509, 86865510, 86865531, 86865651, 86865655, 86865667, 86865668, 86866880, 86866887, 86867075,
				86868008, 86869888, 86872704, 86875234, 86876597, 86878425, 86878960, 86880776, 86881577, 86882793,
				86882797, 86882801, 86882825, 86883248, 86890079, 86893311, 86894056, 86894522, 86898764, 86901933,
				86905205, 86905307, 86906321, 86906620, 86910326, 86910327, 86910359, 86910456, 86910738, 86912463,
				86912865, 86913901, 86914022, 86914194, 86914214, 86914306, 86914460, 86914473, 86915180, 86916436,
				86916744, 86916748, 86916800, 86916836, 86916986, 86917387, 86921669, 86922387, 86922544, 86922613,
				86922619, 86923570, 86924441, 86924450, 86931745, 86932099, 86932100, 86932858, 86932872, 86932880,
				86937382, 86942628, 86942658, 86942698, 86946905, 86947479, 86949456, 86949480, 86950251, 86950273,
				86964925, 86965311, 86965740, 86965743, 86965746, 86965750, 86965796, 86966545, 86966685, 86966876,
				86967515, 86974236, 86976149, 86979807, 86979877, 86980299, 86981045, 86981826, 86989104, 86989250,
				86989323, 86991658, 86992119, 86992646, 86992872, 86994555, 86994732, 86994772, 86994792, 86996005,
				87004503, 87004718, 87006420, 87008182, 87008220, 87008301, 87012002, 87014951, 87022957, 87030048,
				87036566, 87041527, 87043873, 87043876, 87043877, 87043891, 87043892, 87044007, 87045091, 87045555,
				87045723, 87045806, 87049654, 87053663, 87054831, 87056200, 87064551, 87066627, 87067472, 87067526,
				87067647, 87067732, 87072617, 87073135, 87074553, 87075348, 87075356, 87075540, 87076474, 87076862,
				87077038, 87077130, 87077162, 87077181, 87077184, 87077187, 87077281, 87082362, 87082391, 87085065,
				87089562, 87089616, 87089658, 87089847, 87090191, 87090212, 87090244, 87090447, 87090453, 87090460,
				87090478, 87090483, 87090487, 87090504, 87090837, 87091574, 87091596, 87095151, 87096069, 87102291,
				87102552, 87106045, 87106482, 87106574, 87107249, 87109100, 87109123, 87109146, 87109494, 87109516,
				87110240, 87110323, 87111528, 87114965, 87116642, 87116648, 87122073, 87122861, 87124197, 87125067,
				87125589, 87125812, 87127339, 87127854, 87128962, 87132963, 87133954, 87135475, 87136862, 87136876,
				87138760, 87148783, 87150267, 87150278, 87150318, 87155767, 87157511, 87160823, 87163942, 87164917,
				87166493, 87175408, 87179904, 87180903, 87180906, 87181021, 87181266, 87181293, 87181296, 87181342,
				87181350, 87181441, 87186360, 87203971, 87208079, 87208377, 87209102, 87209895, 87209900, 86525460,
				86526780, 86527806, 86535861, 86544170, 86544912, 86544958, 86545112, 86545140, 86545314, 86545368,
				86545407, 86547971, 86552741, 86553421, 86564573, 86564870, 86565942, 86567107, 86567307, 86567321,
				86572399, 86572423, 86572620, 86573730, 86573996, 86575741, 86576522, 86579729, 86579751, 86591905,
				86591906, 86591921, 86599881, 86602636, 86602977, 86604722, 86607195, 86608044, 86608046, 86609540,
				86609541, 86609542, 86609544, 86609615, 86609642, 86613827, 86615973, 86616692, 86617247, 86617296,
				86617512, 86619879, 86620023, 86620186, 86620944, 86622681, 86624877, 86626376, 86627511, 86631281,
				86633932, 86636171, 86636768, 86636884, 86636984, 86647565, 86659327, 86660018, 86663840, ********,
				********, ********, ********, ********, ********, ********, ********, ********, ********, ********,
				********, ********, ********, ********, ********, ********, ********, ********, ********, ********,
				********, ********, ********, ********, ********, 86718722, 86722620, 86722844, 86724124, 86725432,
				86725442, 86725446, 86725546, 86726613, 86727590, 86727717, 86727781, 86727799, 86728513, 86730309,
				86730325, 86732559, 86733156, 86734773, 86737087, 86738022, 86738028, 86738095, 86738257, 86738462,
				86740049, 86741085, 86741706, 86743376, 86745342, 86748739, 86749470, 86750740, 86750891, 86754476,
				86755869, 86756341, 86757366, 86757376, 86760791, 86760794, 86761209, 86762305, 86763089, 86764990,
				86764991, 86774851, 86774915, 86777104, 86777106, 86777111, 86782032, 86782324, 86782427, 86782549,
				86787965, 86789122, 86790465, 86791859, 86792301, 86801512, 86803101, 86807848, 86808527, 86808703,
				86808782, 86808896, 86808904, 86808907, 86808913, 86809023, 86809692, 86818679, 86821993, 86822291,
				86825377, 86843392, 86843398, 86843405, 86843440, 86850943, 86855213, 86856642, 86857643, 86858280,
				86859537, 86860179, 86861000, 86862817, 86863909, 86864231, 86865408, 86865432, 86865509, 86865510,
				86865531, 86865651, 86865655, 86865667, 86865668, 86866880, 86866887, 86867075, 86868008, 86869888,
				86872704, 86875234, 86876597, 86878425, 86878960, 86880776, 86881577, 86882793, 86882797, 86882801,
				86882825, 86883248, 86890079, 86893311, 86894056, 86894522, 86898764, 86901933, 86905205, 86905307,
				86906321, 86906620, 86910326, 86910327, 86910359, 86910456, 86910738, 86912463, 86912865, 86913901,
				86914022, 86914194, 86914214, 86914306, 86914460, 86914473, 86915180, 86916436, 86916744, 86916748,
				86916800, 86916836, 86916986, 86917387, 86921669, 86922387, 86922544, 86922613, 86922619, 86923570,
				86924441, 86924450, 86931745, 86932099, 86932100, 86932858, 86932872, 86932880, 86937382, 86942628,
				86942658, 86942698, 86946905, 86947479, 86949456, 86949480, 86950251, 86950273, 86964925, 86965311,
				86965740, 86965743, 86965746, 86965750, 86965796, 86966545, 86966685, 86966876, 86967515, 86974236,
				86976149, 86979807, 86979877, 86980299, 86981045, 86981826, 86989104, 86989250, 86989323, 86991658,
				86992119, 86992646, 86992872, 86994555, 86994732, 86994772, 86994792, 86996005, 87004503, 87004718,
				87006420, 87008182, 87008220, 87008301, 87012002, 87014951, 87022957, 87030048, 87036566, 87041527,
				87043873, 87043876, 87043877, 87043891, 87043892, 87044007, 87045091, 87045555, 87045723, 87045806,
				87049654, 87053663, 87054831, 87056200, 87064551, 87066627, 87067472, 87067526, 87067647, 87067732,
				87072617, 87073135, 87074553, 87075348, 87075356, 87075540, 87076474, 87076862, 87077038, 87077130,
				87077162, 87077181, 87077184, 87077187, 87077281, 87082362, 87082391, 87085065, 87089562, 87089616,
				87089658, 87089847, 87090191, 87090212, 87090244, 87090447, 87090453, 87090460, 87090478, 87090483,
				87090487, 87090504, 87090837, 87091574, 87091596, 87095151, 87096069, 87102291, 87102552, 87106045,
				87106482, 87106574, 87107249, 87109100, 87109123, 87109146, 87109494, 87109516, 87110240, 87110323,
				87111528, 87114965, 87116642, 87116648, 87122073, 87122861, 87124197, 87125067, 87125589, 87125812,
				87127339, 87127854, 87128962, 87132963, 87133954, 87135475, 87136862, 87136876, 87138760, 87148783,
				87150267, 87150278, 87150318, 87155767, 87157511, 87160823, 87163942, 87164917, 87166493, 87175408,
				87179904, 87180903, 87180906, 87181021, 87181266, 87181293, 87181296, 87181342, 87181350, 87181441,
				87186360, 87203971, 87208079, 87208377, 87209102, 87209895, 87209900 };
		return list;

	}

	@Override
	public void migrateContactDataToES(MessengerFilter messengerFilter) {
		List<Integer> mcIds = messengerFilter.getMcIds();
		for (int i = 0; i < mcIds.size(); i++) {
			log.info("Creating thread for messenger contact Id {} migration", mcIds.get(i));
			int index = i;
			poolExecutor.execute(() -> migrateContactData(mcIds.get(index)));
		}
	}

	private void migrateContactData(Integer mcId) {
		updateAndMigrateContactDataToES(mcId);
		log.info("Migrated messenger contact Id {}", mcId);
	}

	@Override
	public void migrateContactDataToES() {
		List<Integer> mcIds = new ArrayList<>();// platformDbRepository.getFBMessengerContactIdForMigration();
		int listSize = mcIds.size();
		log.info("FB messenger contact migration list size: {}", listSize);
		int startIndex = 0;
		int endIndex = 0;
		int loop = listSize / 1000;
		for (int i = 1; i <= loop; i++) {
			MessengerFilter filter = new MessengerFilter();
			endIndex = i * 1000;
			filter.setMcIds(mcIds.subList(startIndex, endIndex));
			log.info("Submitting MC migration in batch of size: {}", endIndex - startIndex);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.FB_MESSENGER_CONTACT_MIGRATION, null, filter);
			startIndex = endIndex + 1;
		}
		if (listSize > endIndex) {
			MessengerFilter filter = new MessengerFilter();
			endIndex = listSize;
			filter.setMcIds(mcIds.subList(startIndex, endIndex));
			log.info("Submitting MC migration in batch of size: {}", endIndex - startIndex);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.FB_MESSENGER_CONTACT_MIGRATION, null, filter);
		}

	}

	@Override
	public void migrateLocationWidgets() {
		List<LocationWidgetDTO> locationWidgetDTOS = locationWidgetDetailsRepository.getLocationWidgetDetails();
		for (LocationWidgetDTO locationWidgetDTO : locationWidgetDTOS) {
			poolExecutor.execute(() -> createDefaultlocationWidget(locationWidgetDTO));
		}
	}

	private void createDefaultlocationWidget(LocationWidgetDTO locationWidgetDTO) {
		BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(locationWidgetDTO.getBusinessId());
		if (!webchatService.checkIfAlreadyCreated(locationWidgetDTO.getBusinessId())) {
			List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigService
					.getWebChatConfigByBusinessNumber(locationWidgetDTO.getEnterpriseId());
			if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
				log.info("populating enterprise widget configs for the location:{},enterprise:{} ",
						locationWidgetDTO.getBusinessId(), locationWidgetDTO.getEnterpriseId());
				BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigs.get(0);
				businessChatWidgetConfig.setWidgetName("Default location widget");
				businessChatWidgetConfig.setId(null);
				businessChatWidgetConfig.setUpdatedAt(new Date());
				businessChatWidgetConfig.setCreatedAt(new Date());
				businessChatWidgetConfig.setEnterpriseId(businessDTO.getEnterpriseId());
				businessChatWidgetConfig.setBusinessId(locationWidgetDTO.getBusinessId());
				businessChatWidgetConfigRepository.saveAndFlush(businessChatWidgetConfig);
			} else {
				BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
						.getDefaultBusinessChatWidgetConfig();
				log.info("populating default widget configs for the location:{},enterprise:{} ",
						locationWidgetDTO.getBusinessId(), locationWidgetDTO.getEnterpriseId());
				businessChatWidgetConfig.setWidgetName("Default location widget");
				businessChatWidgetConfig.setId(null);
				businessChatWidgetConfig.setUpdatedAt(new Date());
				businessChatWidgetConfig.setCreatedAt(new Date());
				businessChatWidgetConfig.setHeaderHeadline(webchatService.replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getHeaderHeadline(), businessDTO));
				businessChatWidgetConfig
						.setWebChatOnlineClosingMessageBody(webchatService.replaceTextWithBusinessNamePrePopulated(
								businessChatWidgetConfig.getWebChatOnlineClosingMessageBody(), businessDTO));
				businessChatWidgetConfig.setAutoReplyTxt(webchatService.replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getAutoReplyTxt(), businessDTO));
				businessChatWidgetConfig
						.setReplyTextPostBusinessHr(webchatService.replaceTextWithBusinessNamePrePopulated(
								businessChatWidgetConfig.getReplyTextPostBusinessHr(), businessDTO));
				businessChatWidgetConfig.setEnterpriseId(businessDTO.getEnterpriseId());
				businessChatWidgetConfig.setBusinessId(locationWidgetDTO.getBusinessId());
				businessChatWidgetConfigRepository.saveAndFlush(businessChatWidgetConfig);
			}
		}
	}

	@Override
	public void moveInboxData(BusinessMoveEvent businessMoveEvent) {
		BusinessMoveAudit businessMoveAudit = checkForDuplicateEvent(businessMoveEvent);
		boolean isDuplicate = checkForDuplicateEvent(businessMoveEvent, businessMoveAudit);
		if (!isDuplicate) {
			switch (businessMoveEvent.getEventType()) {
				case Constants.UPGRADE:
					if (businessMoveEvent.getTargetEnterpriseId() != null
							&& businessMoveEvent.getTargetEnterpriseNumber() != null) {
						log.info("Business Upgrade Event Started for payload :{}", businessMoveEvent);
						businessMoveAudit = createBusinessMoveAudit(businessMoveEvent, businessMoveAudit);
						migrateUpgradeEventData(businessMoveEvent, businessMoveAudit);
					}
					break;
				case Constants.DOWNGRADE:
					if (businessMoveEvent.getSourceEnterpriseId() != null
							&& businessMoveEvent.getSourceEnterpriseNumber() != null) {
						log.info("Business Downgrade Event Started for payload :{}", businessMoveEvent);
						businessMoveAudit = createBusinessMoveAudit(businessMoveEvent, businessMoveAudit);
						migrateDowngradeEventData(businessMoveEvent, businessMoveAudit);
					}
					break;
				case Constants.MOVE:
					if (businessMoveEvent.getSourceBusinessId() != null
							&& businessMoveEvent.getSourceBusinessNumber() != null) {
						log.info("Business Move Event Started for payload :{}", businessMoveEvent);
						businessMoveAudit = createBusinessMoveAudit(businessMoveEvent, businessMoveAudit);
						migrateMoveEventData(businessMoveEvent, businessMoveAudit);
					}
					break;
			}
		}
	}

	@Override
	public void businessUpgradeDowngradeMoveEvent(BusinessMigrationBatchDto event) {
		switch (event.getBusinessMoveEvent().getEventType()) {
			case Constants.UPGRADE:
				businessUpgradeEventData(event);
				break;
			case Constants.DOWNGRADE:
				businessDownGradeEventData(event);
				break;
			case Constants.MOVE:
				businessMoveEventData(event);
				break;
		}
	}

	private void migrateUpgradeEventData(BusinessMoveEvent businessMoveEvent, BusinessMoveAudit businessMoveAudit) {
		Integer sourceAccountId = businessMoveEvent.getSourceBusinessId();
		Integer targetAccountId = businessMoveEvent.getTargetEnterpriseId();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactByBusinessId(businessMoveEvent.getSourceBusinessId());
		BusinessMigrationBatchDto businessMigrationBatchDto = new BusinessMigrationBatchDto();
		businessMigrationBatchDto.setBusinessMoveEvent(businessMoveEvent);
		businessMigrationBatchDto.setBusinessMoveAudit(businessMoveAudit);
		Integer batchSize = Integer.valueOf(CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class).getProperty("business_move_batch_size",
						"10"));
		if (CollectionUtils.isNotEmpty(messengerContacts)) {
			log.info("sourceAccountId: {}, targetAccountId: {}, Messenger contacts size: {}", sourceAccountId,
					targetAccountId, messengerContacts.size());
			List<List<MessengerContact>> messengerContactsOuterList = ListUtils.partition(messengerContacts, batchSize);
			for (List<MessengerContact> messengerContactSubList : messengerContactsOuterList) {
				businessMigrationBatchDto.setMessengerContactId(
						messengerContactSubList.stream().map(MessengerContact::getId).collect(Collectors.toList()));
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.BUSINESS_MIGRATION_BATCH, sourceAccountId,
						businessMigrationBatchDto);
			}
		}

	}

	private void businessUpgradeEventData(BusinessMigrationBatchDto businessMigrationBatchDto) {
		BusinessMoveAuditEnum status = BusinessMoveAuditEnum.SUCCESS;
		Integer sourceAccountId = businessMigrationBatchDto.getBusinessMoveEvent().getSourceBusinessId();
		Integer targetAccountId = businessMigrationBatchDto.getBusinessMoveEvent().getTargetEnterpriseId();
		String description = null;
		List<Integer> customerIds = new ArrayList<>();
		List<Integer> messengerContactIdList = new ArrayList<>();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactsByMcIds(businessMigrationBatchDto.getMessengerContactId());

		List<ContactDocument> contactFromES = getContactDocumentfromEs(
				businessMigrationBatchDto.getMessengerContactId(), sourceAccountId);
		Map<Integer, ContactDocument> contactDocumentMap = contactFromES.stream()
				.collect(Collectors.toMap(ContactDocument::getM_c_id, mc -> mc));

		List<MessageDocument> messageDocuments = getMessageDocuments(businessMigrationBatchDto.getMessengerContactId(),
				sourceAccountId);

		// Loop on CD
		if (CollectionUtils.isNotEmpty(contactFromES)) {
			List<ContactDocument> contactDocumentBulkRequest = new ArrayList<>();
			for (MessengerContact mc : messengerContacts) {
				customerIds.add(mc.getCustomerId());
				messengerContactIdList.add(mc.getId());
				ContactDocument cd = contactDocumentMap.get(mc.getId());
				if (cd != null) {
					try {
						ContactDocument contactDocument = updateContactDocument(cd, targetAccountId,
								businessMigrationBatchDto.getBusinessMoveEvent().getTargetBusinessNumber(), mc,
								businessMigrationBatchDto.getBusinessMoveAudit().getEventType());
						if (contactDocument != null) {
							contactDocumentBulkRequest.add(contactDocument);
						}
					} catch (Exception e) {
						log.error("Exception while updating contact doc mc_id :{}, exception :{}", mc.getId(), e);
						status = BusinessMoveAuditEnum.FAILED;
						description = "Error while upserting contact doc!!";
					}
				}
			}
			if (CollectionUtils.isNotEmpty(contactDocumentBulkRequest)) {
				Integer routingId = targetAccountId;
				BulkUpsertPayload<ContactDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
						contactDocumentBulkRequest,
						routingId, routingId, Constants.Elastic.CONTACT_INDEX);
				try {
					elasticSearchService.bulkUpsert(bulkUpsertPayload);
				} catch (Exception e) {
					log.error("updateContactDocumentOnEs : Exception while updating : {}", e);
					status = BusinessMoveAuditEnum.FAILED;
					description = "Error while upserting contact doc!!";
				}
			}
		}

		// Loop on MD
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			List<MessageDocument> messageDocumentBulkRequest = new ArrayList<>();
			for (MessageDocument messageDocument : messageDocuments) {
				messageDocument.setE_id(targetAccountId);
				messageDocumentBulkRequest.add(messageDocument);
			}
			if (CollectionUtils.isNotEmpty(messageDocumentBulkRequest)) {
				log.info("MessageDocument BulkRequest size: {}", messageDocumentBulkRequest.size());
				// Partitioning All MeesageDocuments in List of 100 documents for Bulk
				// processing
				List<List<MessageDocument>> messageDocumentBulkRequestOuterList = ListUtils
						.partition(messageDocumentBulkRequest, 100);
				for (List<MessageDocument> messageDocumentBulkRequestInnerList : messageDocumentBulkRequestOuterList) {
					Integer routingId = targetAccountId;
					BulkUpsertPayload<MessageDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
							messageDocumentBulkRequestInnerList,
							routingId, routingId, Constants.Elastic.MESSAGE_INDEX);
					try {
						elasticSearchService.bulkUpsert(bulkUpsertPayload);
					} catch (Exception e) {
						log.error("updateMessageDocumentOnEs : Exception while updating : {}", e);
						status = BusinessMoveAuditEnum.FAILED;
					}
				}
			}
		}

		try {
			if (CollectionUtils.isNotEmpty(messengerContactIdList)) {
				log.info("updating messenger messages");
				messengerMessageService.updateMessengerMessagesInBusinessUpgradeOrDowngrade(targetAccountId,
						sourceAccountId, messengerContactIdList);
			}

		} catch (Exception e) {
			log.error("error occurred while updating messenger messages : {}", e.getMessage());
			status = BusinessMoveAuditEnum.FAILED;
		}
		updateBusinessMoveAudit(businessMigrationBatchDto.getBusinessMoveAudit(), status, description,
				businessMigrationBatchDto.getBusinessMoveEvent());
		if (status.equals(BusinessMoveAuditEnum.SUCCESS)) {
			MessagesDeleteRequest messagesDeleteRequest = new MessagesDeleteRequest(
					businessMigrationBatchDto.getBusinessMoveEvent().getEventId(),
					businessMigrationBatchDto.getBusinessMoveAudit().getId(), sourceAccountId,
					businessMigrationBatchDto.getBusinessMoveEvent().getSourceBusinessId(), messengerContactIdList);
			publishDeleteEventToKafka(messagesDeleteRequest);
		}
	}

	private List<ContactDocument> getContactDocumentfromEs(List<Integer> mcId, Integer accountId) {
		log.info("Get Contact doc from ES mcId:{} , accountId:{}", mcId, accountId);
		// MessangerBaseFilter contactDocFilter = new MessangerBaseFilter();
		// contactDocFilter.setConversationId(mcId);
		// contactDocFilter.setAccountId(accountId);
		// contactDocFilter.setCount(1);
		// return messengerContactService.getContactFromES(contactDocFilter);

		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("size", mcId.size());
		dataModel.put("conversationIds", ControllerUtil.toCommaSeparatedString(mcId));
		// GET Contact Documents
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(accountId).addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATIONS_BY_ID, dataModel)
				.build();
		return elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
	}

	private List<MessageDocument> getMessageDocuments(List<Integer> mcId, Integer accountId) {
		log.info("Get message doc from ES mcId:{} , accountId:{}", mcId, accountId);
		// MessangerBaseFilter messageDocFilter = new MessangerBaseFilter();
		// messageDocFilter.setConversationId(mcId);
		// messageDocFilter.setAccountId(accountId);
		// messageDocFilter.setCount(10000);
		// messageDocFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
		// return messengerContactService.getMessagesFromES(messageDocFilter);
		//
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("mcids", ControllerUtil.toCommaSeparatedString(mcId));

		Map<String, Object> data = new HashMap<>();
		data.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId).addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_BY_MCIDS, data).build();
		ElasticData messageData = elasticSearchService.getDataFromElastic(esRequest,
				MessageDocument.class);

		if (CollectionUtils.isNotEmpty(messageData.getResults())) {
			return messageData.getResults();
		}
		return Collections.emptyList();
	}

	private ContactDocument updateContactDocument(ContactDocument cd, Integer targetBusinessId,
			Long targetBusinessNumber, MessengerContact mc, String eventType) {
		if (!targetBusinessId.equals(cd.getE_id())) {
			cd.setE_id(targetBusinessId);

			String lastMessage = cd.getL_msg();

			if (StringUtils.isNotEmpty(lastMessage) && cd.getIs_encrypted() != null && cd.getIs_encrypted() == 1
					&& !eventType.equals(Constants.MOVE)) {
				try {
					lastMessage = EncryptionUtil.decrypt(mc.getLastMessage(),
							StringUtils.join(cd.getB_num(), cd.getC_phone()),
							StringUtils.join(cd.getC_phone(), cd.getB_num()), false);
					if (StringUtils.isEmpty(lastMessage)) {
						lastMessage = EncryptionUtil.decrypt(mc.getLastMessage(),
								StringUtils.join(cd.getB_num(), mc.getFacebookId()),
								StringUtils.join(mc.getFacebookId(), cd.getB_num()), false);
						if (StringUtils.isEmpty(lastMessage)) {
							// this is to check for email cases
							lastMessage = EncryptionUtil.decryptMessage(1, mc.getLastMessage(),
									cd.getB_num().toString(), mc.getId().toString());
							lastMessage = EncryptionUtil.encrypt(lastMessage,
									StringUtils.join(targetBusinessNumber, mc.getId()),
									StringUtils.join(mc.getId(), targetBusinessNumber));
						} else
							lastMessage = EncryptionUtil.encrypt(lastMessage,
									StringUtils.join(targetBusinessNumber.toString(), mc.getFacebookId()),
									StringUtils.join(mc.getFacebookId(), targetBusinessNumber.toString()));
					} else
						lastMessage = EncryptionUtil.encrypt(lastMessage,
								StringUtils.join(targetBusinessNumber.toString(), cd.getC_phone()),
								StringUtils.join(cd.getC_phone(), targetBusinessNumber.toString()));
				} catch (Exception e) {
					log.warn("Exception while encrypting or decrypting the message for mcid : {} exception:{}",
							cd.getM_c_id(), e);
				}
			}
			if (!eventType.equals(Constants.MOVE)) {
				cd.setB_num(targetBusinessNumber);
				cd.setL_msg(lastMessage);
				mc.setLastMessage(lastMessage);
				messengerContactService.saveOrUpdateMessengerContact(mc);
			}
			// messengerContactService.updateContactOnES(cd.getM_c_id(),cd,
			// targetBusinessId);
			return cd;
		}
		return null;
	}

	private void publishEventStatusToKafka(BusinessMoveEvent businessMoveEvent, BusinessMoveAuditEnum status) {
		StatusRequest statusRequest = new StatusRequest(businessMoveEvent.getEventId(), status.getValue());
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.BUSINESS_CONVERSION_AUDIT, null,
				statusRequest);
	}

	private void publishDeleteEventToKafka(MessagesDeleteRequest messagesDeleteRequest) {
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.MESSAGES_DELETE, null,
				messagesDeleteRequest);
	}

	private void migrateDowngradeEventData(BusinessMoveEvent businessMoveEvent, BusinessMoveAudit businessMoveAudit) {
		Integer sourceAccountId = businessMoveEvent.getSourceEnterpriseId();
		Integer targetAccountId = businessMoveEvent.getTargetBusinessId();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactByBusinessId(businessMoveEvent.getSourceBusinessId());
		BusinessMigrationBatchDto businessMigrationBatchDto = new BusinessMigrationBatchDto();
		businessMigrationBatchDto.setBusinessMoveEvent(businessMoveEvent);
		businessMigrationBatchDto.setBusinessMoveAudit(businessMoveAudit);
		Integer batchSize = Integer.valueOf(CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class).getProperty("business_move_batch_size",
						"10"));
		if (CollectionUtils.isNotEmpty(messengerContacts)) {
			log.info("sourceAccountId: {}, targetAccountId: {}, Messenger contacts size:{}", sourceAccountId,
					targetAccountId, messengerContacts.size());
			List<List<MessengerContact>> messengerContactsOuterList = ListUtils.partition(messengerContacts, batchSize);
			for (List<MessengerContact> messengerContactSubList : messengerContactsOuterList) {
				businessMigrationBatchDto.setMessengerContactId(
						messengerContactSubList.stream().map(MessengerContact::getId).collect(Collectors.toList()));
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.BUSINESS_MIGRATION_BATCH, sourceAccountId,
						businessMigrationBatchDto);
			}
		}
	}

	private void businessDownGradeEventData(BusinessMigrationBatchDto event) {
		BusinessMoveAuditEnum status = BusinessMoveAuditEnum.SUCCESS;
		Integer sourceAccountId = event.getBusinessMoveEvent().getSourceEnterpriseId();
		Integer targetAccountId = event.getBusinessMoveEvent().getTargetBusinessId();
		String description = null;
		List<Integer> customerIds = new ArrayList<>();
		List<Integer> messengerContactIdList = new ArrayList<>();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactsByMcIds(event.getMessengerContactId());
		List<ContactDocument> contactDocumentBulkRequest = new ArrayList<>();

		List<ContactDocument> contactFromES = getContactDocumentfromEs(event.getMessengerContactId(), sourceAccountId);
		Map<Integer, ContactDocument> contactDocumentMap = contactFromES.stream()
				.collect(Collectors.toMap(ContactDocument::getM_c_id, mc -> mc));
		log.info("Contact Documents Fetched From Es for mcids : {} are : {} and mapsize", event.getMessengerContactId(),
				contactFromES.size(), contactDocumentMap.size());

		List<MessageDocument> messageDocuments = getMessageDocuments(event.getMessengerContactId(), sourceAccountId);
		log.info("Message Documents Fetched From Es for mcids : {} are : {}", event.getMessengerContactId(),
				messageDocuments.size());
		// Loop on CD
		if (CollectionUtils.isNotEmpty(contactFromES)) {
			for (MessengerContact mc : messengerContacts) {
				customerIds.add(mc.getCustomerId());
				messengerContactIdList.add(mc.getId());
				ContactDocument cd = contactDocumentMap.get(mc.getId());
				if (cd != null) {
					try {
						ContactDocument contactDocument = updateContactDocument(cd, targetAccountId,
								event.getBusinessMoveEvent().getTargetBusinessNumber(), mc,
								event.getBusinessMoveAudit().getEventType());
						if (contactDocument != null) {
							contactDocumentBulkRequest.add(contactDocument);
						}
					} catch (Exception e) {
						log.error("Exception while updating contact doc mc_id :{}, exception :{}", mc.getId(), e);
						status = BusinessMoveAuditEnum.FAILED;
						description = "Error while upserting contact doc!!";
					}
				}
			}
			if (CollectionUtils.isNotEmpty(contactDocumentBulkRequest)) {
				Integer routingId = targetAccountId;
				BulkUpsertPayload<ContactDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
						contactDocumentBulkRequest,
						routingId, routingId, Constants.Elastic.CONTACT_INDEX);
				try {
					elasticSearchService.bulkUpsert(bulkUpsertPayload);
				} catch (Exception e) {
					log.error("updateContactDocumentOnEs : Exception while updating : {}", e);
					status = BusinessMoveAuditEnum.FAILED;
					description = "Error while upserting contact doc!!";
				}
			}
		}

		// Loop on MD
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			List<MessageDocument> messageDocumentBulkRequest = new ArrayList<>();
			for (MessageDocument messageDocument : messageDocuments) {
				messageDocument.setE_id(targetAccountId);
				messageDocumentBulkRequest.add(messageDocument);
			}
			if (CollectionUtils.isNotEmpty(messageDocumentBulkRequest)) {
				log.info("MessageDocument BulkRequest size: {}", messageDocumentBulkRequest.size());
				// Partitioning All MeesageDocuments in List of 100 documents for Bulk
				// processing
				List<List<MessageDocument>> messageDocumentBulkRequestOuterList = ListUtils
						.partition(messageDocumentBulkRequest, 100);
				for (List<MessageDocument> messageDocumentBulkRequestInnerList : messageDocumentBulkRequestOuterList) {
					Integer routingId = targetAccountId;
					BulkUpsertPayload<MessageDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
							messageDocumentBulkRequestInnerList,
							routingId, routingId, Constants.Elastic.MESSAGE_INDEX);
					try {
						elasticSearchService.bulkUpsert(bulkUpsertPayload);
					} catch (Exception e) {
						log.error("updateMessageDocumentOnEs : Exception while updating : {}", e);
						status = BusinessMoveAuditEnum.FAILED;
						description = "Error while upserting message doc!!";
					}
				}
			}
		}

		try {
			if (CollectionUtils.isNotEmpty(messengerContactIdList)) {
				log.info("updating messenger messages");
				messengerMessageService.updateMessengerMessagesInBusinessUpgradeOrDowngrade(targetAccountId,
						sourceAccountId, messengerContactIdList);
			}

		} catch (Exception e) {
			log.error("error occurred while updating messenger messages : {}", e.getMessage());
			status = BusinessMoveAuditEnum.FAILED;
		}

		updateBusinessMoveAudit(event.getBusinessMoveAudit(), status, description, event.getBusinessMoveEvent());
		if (status.equals(BusinessMoveAuditEnum.SUCCESS)) {
			MessagesDeleteRequest messagesDeleteRequest = new MessagesDeleteRequest(
					event.getBusinessMoveEvent().getEventId(), event.getBusinessMoveAudit().getId(), sourceAccountId,
					event.getBusinessMoveEvent().getSourceBusinessId(), messengerContactIdList);
			publishDeleteEventToKafka(messagesDeleteRequest);
		}
	}

	private BusinessMoveAudit createBusinessMoveAudit(BusinessMoveEvent businessMoveEvent,
			BusinessMoveAudit businessMoveAudit) {
		if (businessMoveAudit == null) {
			businessMoveAudit = new BusinessMoveAudit(businessMoveEvent.getSourceBusinessId(),
					businessMoveEvent.getSourceEnterpriseId(),
					businessMoveEvent.getTargetBusinessId(), businessMoveEvent.getTargetEnterpriseId(),
					businessMoveEvent.getEventType(), BusinessMoveAuditEnum.INPROGRESS.getValue());
		} else {
			businessMoveAudit.setStatus(BusinessMoveAuditEnum.INPROGRESS.getValue());
			businessMoveAudit.setDescription(null);
			businessMoveAudit.setUpdated(new Date());
		}
		return businessMoveAuditService.saveBusinessMoveAudit(businessMoveAudit);
	}

	private BusinessMoveAudit updateBusinessMoveAudit(BusinessMoveAudit businessMoveAudit, BusinessMoveAuditEnum status,
			String description, BusinessMoveEvent businessMoveEvent) {
		BusinessMoveAudit businessMoveAuditNew = businessMoveAuditService
				.findBusinessMoveAuditById(businessMoveAudit.getId());
		if (businessMoveAuditNew != null && businessMoveAuditNew.getStatus() != "failed") {
			if (status.getValue().equals(BusinessMoveAuditEnum.FAILED.getValue())) {
				businessMoveAudit.setStatus(status.getValue());
				businessMoveAudit.setUpdated(new Date());
				businessMoveAudit.setDescription(description);
				// callback to busines service.
				publishEventStatusToKafka(businessMoveEvent, status);
				return businessMoveAuditService.saveBusinessMoveAudit(businessMoveAudit);
			} else if ((businessMoveAuditNew.getStatus().equals(BusinessMoveAuditEnum.INPROGRESS.getValue()))
					&& status.getValue().equals(BusinessMoveAuditEnum.SUCCESS.getValue())) {
				businessMoveAudit.setStatus(status.getValue());
				businessMoveAudit.setUpdated(new Date());
				businessMoveAudit.setDescription(description);
				// callback to busines service.
				publishEventStatusToKafka(businessMoveEvent, status);
				return businessMoveAuditService.saveBusinessMoveAudit(businessMoveAudit);
			}
		}
		return null;
	}

	private BusinessMoveAudit checkForDuplicateEvent(BusinessMoveEvent businessMoveEvent) {
		BusinessMoveAudit businessMoveAudit = businessMoveAuditService
				.findBusinessMoveAudit(businessMoveEvent.getSourceBusinessId(), businessMoveEvent.getEventType());
		return businessMoveAudit;
	}

	@Override
	@Transactional
	public void migratePodiumData(Conversation conversation) {
		Integer accountId = conversation.getAccountId();
		MessengerContact messengerContact = null;
		Integer businessId = conversation.getBusinessId();
		try {
			if (businessId == null) {
				List<BusinessLite> businessLiteObjects = businessService.getBusinessLiteObjects(accountId);
				businessId = getBusinessIdFromBuisnessLiteObjects(conversation.getLocationDetails(),
						businessLiteObjects);
				if (businessId == null) {
					log.error("No business found for location: {} and accountId: {}", conversation.getLocationDetails(),
							conversation.getAccountId());
					throw new BadRequestException(
							new ErrorMessageBuilder(ErrorCode.BUSINESS_NOT_FOUND, HttpStatus.BAD_REQUEST));
				}
				conversation.setBusinessId(businessId);
			}
			BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
			KontactoRequest kontactoRequest = null;
			List<MessageDocument> msgDocs = new ArrayList<MessageDocument>();
			Integer userId = -1;
			// create customer for FB and chat
			if (StringUtils.isNotEmpty(conversation.getCustomerDetails().getFbUserId())) {
				// Todo -check for FB integration and get the FB page Id
				kontactoRequest = createKontactoFBRequest(conversation.getCustomerDetails(), businessDTO);
				userId = -8;
			} else {
				kontactoRequest = create(conversation.getCustomerDetails(), businessDTO);
			}
			CustomerDTO customer = contactService.getorCreateNewCustomer(kontactoRequest, accountId, userId);
			Map<String, Integer> userIdMap = getUserIdFromCore(conversation);
			log.info("[migratePodiumData] : UserId Map fetched from core services for account: {} are {}",
					conversation.getAccountId(), userIdMap);
			if (userIdMap != null && customer != null) {
				// Create Messenger Contact shell to MCID
				messengerContact = getOrCreateMessengerContact(conversation, customer, businessDTO, userIdMap);
				if (messengerContact == null) {
					throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
				}
				if (BooleanUtils.isTrue(messengerContact.getIsNew())) {
					messengerContactRepository.saveAndFlush(messengerContact);
				}
				conversation.setMessengerContactId(messengerContact.getId());
				conversation.setCustomerPhoneNumber(customer.getPhone());
				conversation.setCustomerId(customer.getId());
				if (CollectionUtils.isNotEmpty(conversation.getMessages())) {
					saveMessages(conversation, businessDTO, userIdMap, msgDocs);
					pushMessagesToES(conversation, msgDocs);
				}
				// updating last message and upserting progress status and activity
				if (messengerContact != null) {
					if (BooleanUtils.isTrue(messengerContact.getIsNew())) {
						// updating messenger contact with lastmessage on receiving first message
						updateLastMessageInContact(conversation, userIdMap, businessDTO, customer);
					}
					executePostPersistEvents(conversation, messengerContact, businessDTO);
				}
				pushEventStatusBeAssist(conversation.getEventId(), conversation.getMessages().get(0).getUuid(),
						"success", null);
			} else {
				log.error("[migratePodiumData] : either UserId Map or customer is NULL for accountId: {}",
						conversation.getAccountId());
				pushEventStatusBeAssist(conversation.getEventId(), conversation.getMessages().get(0).getUuid(),
						"failed", "UserId Map or Customer is NULL");
			}
		} catch (Exception ex) {
			log.error("[migratePodiumData] : Other exception occurred : {}", ex);
			pushEventStatusBeAssist(conversation.getEventId(), conversation.getMessages().get(0).getUuid(), "failed",
					"Other failure");
		}
	}

	private void executePostPersistEvents(Conversation conversation, MessengerContact messengerContact,
			BusinessDTO businessDTO) {
		Optional<MessengerContact> contactOptional = messengerContactRepository.findById(messengerContact.getId());
		if (contactOptional.isPresent()) {
			MessengerContact contact = contactOptional.get();
			StatusEnum status = StatusEnum.getStatusEnumByName(conversation.getStatus());
			if (status == null) {
				log.error("[executePostPersistEvents] - Invalid Status received in request for contactId {} : {}",
						contact.getId(), conversation.getStatus());
				return;
			}
			// determining source of conversation
			Source source = Source.SMS;
			if (conversation.getCustomerDetails() != null
					&& StringUtils.isNotBlank(conversation.getCustomerDetails().getFbUserId()))
				source = Source.FACEBOOK;
			log.info("[executePostPersistEvents] - Import status present for contactId {} : {}", contact.getId(),
					status);
			boolean isUpdateRequired = false;
			ContactDocument contactDocument = new ContactDocument();
			if (contact.getImportStatus() == null) {
				isUpdateRequired = true;
				contact.setImportStatus(status.getId());
				contactDocument.setImport_status(status.getId());
				logConversationActivity(contact.getId(), conversation.getCustomerId(), businessDTO, source,
						StatusEnum.COMPLETED.equals(status) ? ActivityType.IMPORT_MESSAGES_COMPLETED
								: ActivityType.IMPORT_MESSAGES_INPROGRESS);
			} else if (StatusEnum.COMPLETED.name().equals(conversation.getStatus())) {
				isUpdateRequired = true;
				contact.setImportStatus(status.getId());
				contactDocument.setImport_status(status.getId());
				updateConversationActivity(contact.getId(), conversation.getCustomerId(), businessDTO, source);
			}
			if (isUpdateRequired) {
				log.info("[executePostPersistEvents] - updating import status for contactId {} : {}", contact.getId(),
						contactDocument);
				messengerContactRepository.save(contact);
				messengerContactService.updateContactDocumentOnES(contactDocument,
						String.valueOf(messengerContact.getId()), businessDTO.getRoutingId());
			}
		}
	}

	private void pushMessagesToES(Conversation conversation, List<MessageDocument> msgDocs) {
		if (CollectionUtils.isNotEmpty(msgDocs)) {
			BulkUpsertPayload<MessageDocument> bulkUpsertPayload = new BulkUpsertPayload<>(msgDocs,
					conversation.getAccountId(), conversation.getAccountId(), Constants.Elastic.MESSAGE_INDEX);
			log.info("Bulk upsert messages to ES for business: {}", conversation.getBusinessId());
			elasticSearchService.bulkUpsert(bulkUpsertPayload);
		}
	}

	private Integer getBusinessIdFromBuisnessLiteObjects(LocationDetails locationDetails,
			List<BusinessLite> businessLiteObjects) {
		Integer businessId = null;
		String locationName = locationDetails.getName();
		if (CollectionUtils.isNotEmpty(businessLiteObjects)) {
			for (BusinessLite businessLite : businessLiteObjects) {
				if ((StringUtils.isNotBlank(locationName) && (businessLite.getBusinessName().contains(locationName)
						|| locationName.contains(businessLite.getBusinessName())))
						&& StringUtils.isNotBlank(businessLite.getPhone())
						&& businessLite.getPhone().equals(locationDetails.getPhone())) {
					businessId = businessLite.getBusinessId();
					break;
				}
			}
		}
		return businessId;
	}

	private MessageTag getTag(Integer open) {
		MessageTag tag = MessageTag.INBOX;
		if (open != null && open == 0) {
			tag = MessageTag.ARCHIVED;
		}
		return tag;
	}

	private void saveMessages(Conversation conversation, BusinessDTO businessDTO, Map<String, Integer> userIdMap,
			List<MessageDocument> msgDocs) {
		List<Message> webChatMsgs = new ArrayList<Message>();
		List<Message> facebookMsgs = new ArrayList<Message>();
		List<Message> activityMsgs = new ArrayList<Message>();
		List<Message> internalNoteMsgs = new ArrayList<Message>();
		String businessPhoneNumber = smsService.getFormattedBusinessNumber(conversation.getBusinessId());
		// String businessFbPage =
		// platformDbRepository.getFacebookPageIdByBusinessId(conversation.getBusinessId());
		String businessFbPage = socialService.getFacebookPageIdByBusinessId(conversation.getBusinessId());
		if (StringUtils.isEmpty(businessFbPage)) {
			log.info("Busines FB page mapping not found,creating dummy FB page for business: {}",
					conversation.getBusinessId());
			businessFbPage = Constants.DUMMY_FB_PAGE + businessDTO.getBusinessId();
		}
		conversation.setBusinessPhoneNumber(businessPhoneNumber);
		conversation.setBusinessFbPage(businessFbPage);
		groupMessagesByTypes(conversation, webChatMsgs, facebookMsgs, activityMsgs, internalNoteMsgs);
		if (CollectionUtils.isNotEmpty(internalNoteMsgs)) {
			log.info("Saving internal notes for business: {}", conversation.getBusinessId());
			List<MessengerNote> internalNotesEntities = createNoteEntities(internalNoteMsgs, userIdMap, conversation);
			messengerNoteRepository.saveAll(internalNotesEntities);
			List<MessengerMessage> messengerMessageEntities = createNotesMessengerMessageEntities(internalNotesEntities,
					conversation, msgDocs, businessDTO);
			messengerMessageRepository.saveAll(messengerMessageEntities);
		}
		if (CollectionUtils.isNotEmpty(webChatMsgs)) {
			log.info("Saving sms msg for business: {}", conversation.getBusinessId());
			List<Sms> smsEntities = createSmsEntities(webChatMsgs, userIdMap, conversation);
			smsRepository.saveAll(smsEntities);
			List<MessengerMessage> messengerMessageEntities = createMessengerMessageEntitiesFromSms(smsEntities,
					conversation, msgDocs, businessDTO, userIdMap);
			messengerMessageRepository.saveAll(messengerMessageEntities);
		}
		if (CollectionUtils.isNotEmpty(activityMsgs)) {
			log.info("Saving activity for business: {}", conversation.getBusinessId());
			List<ConversationActivity> activityEntities = createActivityEntities(activityMsgs, conversation);
			conversationActivityRepository.saveAll(activityEntities);
			List<MessengerMessage> messengerMessageEntities = createActivitiesMessengerMessageEntities(
					activityEntities, conversation, msgDocs, businessDTO, userIdMap);
			messengerMessageRepository.saveAll(messengerMessageEntities);
		}
		if (CollectionUtils.isNotEmpty(facebookMsgs)) {
			log.info("Saving FB message for business: {}", conversation.getBusinessId());
			List<FacebookMessage> facebookEntities = createFacebookEntities(facebookMsgs, userIdMap, conversation);
			facebookMessageRepository.saveAll(facebookEntities);
			List<MessengerMessage> messengerMessageEntities = createFacebookMsgMessengerMessageEntities(
					facebookEntities, conversation, msgDocs, businessDTO, userIdMap);
			messengerMessageRepository.saveAll(messengerMessageEntities);
		}
	}

	private void logConversationActivity(Integer contactId, Integer customerId, BusinessDTO businessDTO, Source source,
			ActivityType activityType) {
		log.info("[logConversationActivity] : adding imported messages activity for business: {}",
				businessDTO.getBusinessId());
		ActivityDto activityDto = ActivityDto.builder().mcId(contactId).created(new Date())
				.actorId(customerId).activityType(activityType)
				.businessId(businessDTO.getBusinessId())
				.accountId(businessDTO.getAccountId()).source(source.getSourceId()).build();
		conversationActivityService.persistActivityForChannel(activityDto);
	}

	private void updateConversationActivity(Integer contactId, Integer customerId, BusinessDTO businessDTO,
			Source source) {
		log.info(
				"[updateConversationActivity] : updating imported messages activity for businessId {} and contactId {}",
				businessDTO.getBusinessId(),
				contactId);
		List<MessageDocument> messageDocumentList = fetchMessageDocumentList(contactId, businessDTO.getAccountId(),
				Arrays.asList(ActivityType.IMPORT_MESSAGES_INPROGRESS.name(),
						ActivityType.IMPORT_MESSAGES_COMPLETED.name()));
		log.info(
				"[updateConversationActivity] : activity messages fetched from ES activity for businessId {} and contactId: {} with size {}",
				businessDTO.getBusinessId(), contactId, messageDocumentList.size());
		if (CollectionUtils.isNotEmpty(messageDocumentList)) {
			MessageDocument messageDocument = messageDocumentList.get(0);
			log.info("[updateConversationActivity] : messageId fetched from ES for import activity {}",
					messageDocument.getId());
			Integer activityId = Integer.parseInt(messageDocument.getM_id());

			ActivityDto activityDto = ActivityDto.builder().id(activityId).mcId(contactId).created(new Date())
					.actorId(customerId).activityType(ActivityType.IMPORT_MESSAGES_COMPLETED)
					.businessId(businessDTO.getBusinessId())
					.accountId(businessDTO.getAccountId()).source(source.getSourceId()).build();
			conversationActivityService.persistActivityForChannel(activityDto);
			log.info("[updateConversationActivity] : successfully updated activity for contactId {} and msgId {}",
					contactId, messageDocument.getId());
		} else {
			log.info("[updateConversationActivity] : No In Progress activity found for contactId {}", contactId);
			logConversationActivity(contactId, customerId, businessDTO, source, ActivityType.IMPORT_MESSAGES_COMPLETED);
		}
	}

	/**
	 * this msg is used to fetch msg list from ES
	 * 
	 * @param contactId
	 * @param routingId
	 * @param activityTypeList
	 * @return
	 */
	private List<MessageDocument> fetchMessageDocumentList(Integer contactId, Integer routingId,
			List<String> activityTypeList) {
		log.info("[fetchMessageDocumentList] - fetching msgs for contactId {}, routingId {} and activityType {}",
				contactId,
				routingId, activityTypeList);
		MessengerFilter esQueryData = new MessengerFilter();
		esQueryData.setConversationId(contactId);
		esQueryData.setStartIndex(0);
		esQueryData.setCount(10);
		esQueryData.setAccountId(routingId);
		esQueryData.setQueryFile(Constants.Elastic.GET_MESSAGES_V4);
		Map<String, Object> params = new HashMap<>();

		if (CollectionUtils.isNotEmpty(activityTypeList)) {
			params.put("messageType", MessageDocument.MessageType.ACTIVITY.name());
			params.put("activityType", ControllerUtil.getJsonTextFromObject(activityTypeList));
		}
		params.put("contactId", contactId);
		esQueryData.setParams(params);
		ElasticData messageData = messengerContactService.getMessageData(esQueryData);
		return messageData.getResults();
	}

	private List<MessengerMessage> createActivitiesMessengerMessageEntities(
			List<ConversationActivity> activityEntities, Conversation conversation, List<MessageDocument> msgDocs,
			BusinessDTO businessDTO, Map<String, Integer> userIdMap) {

		List<MessengerMessage> messengerMessages = new ArrayList<MessengerMessage>();
		if (CollectionUtils.isNotEmpty(activityEntities)) {
			activityEntities.forEach(activity -> {
				UserDTO fromUserDTO = null;
				UserDTO toUserDTO = null;
				Integer from = null;
				String fromName = null;
				Integer to = null;
				String toName = null;
				MessengerMessage messengerMessage = new MessengerMessage();
				messengerMessage.setAccountId(conversation.getBusinessId());
				messengerMessage.setMessageId(activity.getId());
				messengerMessage.setCreatedDate(activity.getCreatedDate());
				messengerMessage.setCreatedBy(activity.getTriggerFrom());
				messengerMessage.setSentThrough(activity.getMessageMigrationDto().getSentThrough());
				messengerMessage.setMessageType(MessageDocument.MessageType.ACTIVITY.name());
				messengerMessage.setMessengerContactId(conversation.getMessengerContactId());
				messengerMessage.setExtRefUid(activity.getMessageMigrationDto().getExtRefUid());
				if (activity.getTriggerFrom() != null) {
					fromUserDTO = communicationHelperService.getUserDTO(activity.getTriggerFrom());
					from = activity.getTriggerFrom();
					fromName = fromUserDTO.getName();
				}
				if (activity.getTriggerFor() != null) {
					toUserDTO = communicationHelperService.getUserDTO(activity.getTriggerFor());
					to = activity.getTriggerFor();
					toName = toUserDTO.getName();
				}
				ActivityDto activityDto = ActivityDto.builder().mcId(conversation.getMessengerContactId())
						.created(activity.getCreatedDate()).actorId(from).actorName(fromName)
						.activityType(getActivityType(activity.getActivityType())).from(from).fromName(fromName).to(to)
						.toName(toName).accountId(conversation.getAccountId()).id(activity.getId()).build();

				MessageDocument assignmentActivity = new MessageDocument(activityDto);
				assignmentActivity.setExt_ref_uid(messengerMessage.getExtRefUid());
				msgDocs.add(assignmentActivity);
				log.info(
						"[createActivitiesMessengerMessageEntities] - persisting activity in store for business {} : {}",
						conversation.getBusinessId(), messengerMessage);
				messengerMessages.add(messengerMessage);
			});
		}
		return messengerMessages;
	}

	private ActivityType getActivityType(String activityType) {
		for (ActivityType actType : ActivityType.values()) {
			if (actType.getLabel().equals(activityType)) {
				return actType;
			}
		}
		return null;
	}

	private List<ConversationActivity> createActivityEntities(List<Message> activityMsgs, Conversation conversation) {
		Map<String, String> extIdActivity = new HashMap<String, String>();
		Set<String> userNames = new HashSet<String>();
		activityMsgs = activityMsgs.stream().filter(msg -> msg.getActivityType() != null).collect(Collectors.toList());
		activityMsgs.forEach(msg -> {
			extIdActivity.put(msg.getUuid(), msg.getMessageBody());
		});

		Map<String, ActivityMigrateDTO> activitiesMap = commonService.getActivityObject(extIdActivity);
		activitiesMap.forEach((k, v) -> {
			if (StringUtils.isNotBlank(v.getCreatedBy())) {
				userNames.add(v.getCreatedBy().trim());
			}
			if (StringUtils.isNotBlank(v.getTriggeredFor())) {
				userNames.add(v.getTriggeredFor().trim());
			}
		});
		GetUserIdsRequest request = new GetUserIdsRequest();
		request.setUserNames(userNames);
		request.setAccountId(conversation.getAccountId());
		Map<String, Integer> userIdMap = businessService.getUserIds(request);

		List<ConversationActivity> activityList = new ArrayList<ConversationActivity>();
		activityMsgs.forEach(dto -> {
			MessengerMessage existingMessengerMessage = findExistingMessengerMessage(conversation.getAccountId(),
					dto.getUuid());
			if (null == existingMessengerMessage) {
				ConversationActivity conversationActivity = convertDtoToActivityEntity(dto, userIdMap, conversation,
						activitiesMap);
				if (conversationActivity != null) {
					activityList.add(conversationActivity);
				}
			}
		});
		log.info("[createActivityEntities] - created activityList from received msg list : {}", activityList);
		return activityList;

	}

	private ConversationActivity convertDtoToActivityEntity(Message message, Map<String, Integer> userIdMap,
			Conversation conversation, Map<String, ActivityMigrateDTO> activitiesMap) {
		ConversationActivity conversationActivity = null;
		ActivityMigrateDTO activityMigrateDTO = null;
		Integer triggredFrom = null;
		Integer triggredFor = null;
		activityMigrateDTO = activitiesMap.get(message.getUuid());

		if (ActivityType.ADD_NOTE.getLabel().equals(message.getActivityType())) {
			conversationActivity = setTriggeredFrom(userIdMap, conversationActivity, activityMigrateDTO, triggredFrom);
		} else if (ActivityType.ASSIGNED.getLabel().equals(message.getActivityType())) {
			conversationActivity = setTriggeredFrom(userIdMap, conversationActivity, activityMigrateDTO, triggredFrom);
			if (activityMigrateDTO != null) {
				String assignee = activityMigrateDTO.getTriggeredFor();
				if (StringUtils.isNotBlank(assignee)) {
					// Handle messages : Melinda Stokes assigned this conversation to themself
					if (Constants.THEMSELF_FIELD.equals(assignee)
							&& StringUtils.isNotBlank(activityMigrateDTO.getCreatedBy())) {
						triggredFor = userIdMap.get(activityMigrateDTO.getCreatedBy());
					} else {
						triggredFor = userIdMap.get(assignee);
					}
				}
				triggredFor = triggredFor != null ? triggredFor : Constants.INBOX_USER_ID;
				conversationActivity.setTriggerFor(triggredFor);
			}

		} else if (ActivityType.CLOSED.getLabel().equals(message.getActivityType())) {
			conversationActivity = setTriggeredFrom(userIdMap, conversationActivity, activityMigrateDTO, triggredFrom);

		} else if (ActivityType.UNASSIGNED.getLabel().equals(message.getActivityType())) {
			conversationActivity = setTriggeredFrom(userIdMap, conversationActivity, activityMigrateDTO, triggredFrom);

		} else if (ActivityType.REOPENED.getLabel().equals(message.getActivityType())) {
			conversationActivity = setTriggeredFrom(userIdMap, conversationActivity, activityMigrateDTO, triggredFrom);
		}
		if (conversationActivity != null) {
			MessageMigrationDto messageMigrationDto = new MessageMigrationDto();
			conversationActivity.setMessageMigrationDto(messageMigrationDto);
			conversationActivity.setActivityType(message.getActivityType());
			conversationActivity.setCreatedDate(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
			conversationActivity.setUpdatedDate(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
			conversationActivity.getMessageMigrationDto().setSentThrough(message.getSentThrough());
			conversationActivity.getMessageMigrationDto().setExtRefUid(message.getUuid());
		}
		return conversationActivity;
	}

	private ConversationActivity setTriggeredFrom(Map<String, Integer> userIdMap,
			ConversationActivity conversationActivity, ActivityMigrateDTO activityMigrateDTO, Integer triggredFrom) {
		if (activityMigrateDTO != null) {
			conversationActivity = new ConversationActivity();
			String createdBy = activityMigrateDTO.getCreatedBy();
			if (StringUtils.isNotBlank(createdBy)) {
				triggredFrom = userIdMap.get(createdBy);
			}
			triggredFrom = triggredFrom != null ? triggredFrom : Constants.INBOX_USER_ID;
			conversationActivity.setTriggerFrom(triggredFrom);
		}
		return conversationActivity;
	}

	private List<MessengerMessage> createFacebookMsgMessengerMessageEntities(List<FacebookMessage> facebookEntities,
			Conversation conversation, List<MessageDocument> msgDocs, BusinessDTO businessDTO,
			Map<String, Integer> userIdMap) {

		List<MessengerMessage> messengerMessages = new ArrayList<MessengerMessage>();
		if (CollectionUtils.isNotEmpty(facebookEntities)) {
			facebookEntities.forEach(facebookMsg -> {
				UserDTO userDTO = null;
				MessengerEvent messengerEvent = null;
				MessengerMessage messengerMessage = new MessengerMessage();
				messengerMessage.setAccountId(conversation.getBusinessId());
				messengerMessage.setMessageId(facebookMsg.getId());
				messengerMessage.setCreatedDate(facebookMsg.getCreateDate());
				messengerMessage.setCreatedBy(facebookMsg.getMessageMigrationDto().getCreatedBy());
				messengerMessage.setSentThrough(facebookMsg.getMessageMigrationDto().getSentThrough());
				messengerMessage.setMessageType(MessageDocument.MessageType.CHAT.name());
				messengerMessage.setMessengerContactId(conversation.getMessengerContactId());
				messengerMessage.setExtRefUid(facebookMsg.getMessageMigrationDto().getExtRefUid());
				MessengerMessageMetaData messageMetaData = new MessengerMessageMetaData();
				messageMetaData.setCommunicationDirection(CommunicationDirection.RECEIVE);
				if (FacebookMessageStatusEnum.sent.name().equals(facebookMsg.getStatus())) {
					messageMetaData.setCommunicationDirection(CommunicationDirection.SEND);
				}
				messageMetaData.setSentThrough(getSentThrough(messengerMessage.getSentThrough()));
				ConversationDTO conversationDTO = new ConversationDTO(facebookMsg, messageMetaData);
				conversationDTO.setExtRefUid(messengerMessage.getExtRefUid());
				MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(conversationDTO,
						conversation.getMessengerContactId());
				if (facebookMsg.getMessageMigrationDto().getCreatedBy() != null) {
					userDTO = communicationHelperService
							.getUserDTO(facebookMsg.getMessageMigrationDto().getCreatedBy());
				}
				if (FacebookMessageStatusEnum.sent.name().equals(facebookMsg.getStatus())) {
					messengerEvent = MessengerEvent.SMS_SEND;
					if (facebookMsg.getMessageMigrationDto().getMessengerMediaFileDTO() != null) {
						messengerEvent = MessengerEvent.MMS_SEND;
					}
				} else {
					messengerEvent = MessengerEvent.SMS_RECEIVE;
					if (facebookMsg.getMessageMigrationDto().getMessengerMediaFileDTO() != null) {
						messengerEvent = MessengerEvent.MMS_RECEIVE;
					}
				}
				msgDocs.add(getMessageUpsertDocument(businessDTO, userDTO, messageDocumentDTO,
						facebookMsg.getMessageMigrationDto().getMessengerMediaFileDTO(), messengerEvent));
				log.info(
						"[createFacebookMsgMessengerMessageEntities] - persisting fb msg in store for business {} : {}",
						conversation.getBusinessId(), messengerMessage);
				messengerMessages.add(messengerMessage);
			});
		}
		return messengerMessages;

	}

	private List<FacebookMessage> createFacebookEntities(List<Message> facebookMsgs, Map<String, Integer> userIdMap,
			Conversation conversation) {
		List<FacebookMessage> facebookMsgList = new ArrayList<FacebookMessage>();
		facebookMsgs.forEach(msg -> {
			MessengerMessage existingMessengerMessage = findExistingMessengerMessage(conversation.getAccountId(),
					msg.getUuid());
			if (null == existingMessengerMessage) {
				facebookMsgList.add(convertDtoToFacebookEntity(msg, userIdMap, conversation));
			}
		});
		log.info("[createFacebookEntities] - created facebook msg list from received msg list : {}", facebookMsgList);
		return facebookMsgList;
	}

	private FacebookMessage convertDtoToFacebookEntity(Message message, Map<String, Integer> userIdMap,
			Conversation conversation) {
		String recipientId = null;
		String senderId = null;
		String status = null;
		Integer encrypted = 0;
		String messageBody = null;
		String messageId = conversation.getBusinessId() + message.getUuid();
		FacebookMessage fbMessage = new FacebookMessage();
		MessageMigrationDto messageMigrationDto = new MessageMigrationDto();
		fbMessage.setMessageMigrationDto(messageMigrationDto);
		fbMessage.setCreateDate(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
		fbMessage.setCustomerId(conversation.getMessengerContactId());
		fbMessage.getMessageMigrationDto().setSentThrough(message.getSentThrough());
		fbMessage.getMessageMigrationDto().setExtRefUid(message.getUuid());
		if (CommunicationDirection.SEND.name().equals(message.getCommunicationDirection())) {
			recipientId = conversation.getCustomerDetails().getFbUserId();
			senderId = conversation.getBusinessFbPage();
			status = FacebookMessageStatusEnum.sent.name();
			Integer userId = getCreatedBy(message, userIdMap);
			fbMessage.getMessageMigrationDto().setCreatedBy(userId);

		} else {
			recipientId = conversation.getBusinessFbPage();
			senderId = conversation.getCustomerDetails().getFbUserId();
			status = FacebookMessageStatusEnum.received.name();
		}
		fbMessage.setRecipientFacebookId(recipientId);
		fbMessage.setSenderFacebookId(senderId);
		messageBody = message.getMessageBody();
		fbMessage.setMessageId(messageId);
		fbMessage.setStatus(status);
		fbMessage.setSentOn(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
		String encryptionEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("encryption.enabled", "true");
		try {
			if (encryptionEnabled != null && encryptionEnabled.equalsIgnoreCase("true")
					&& StringUtils.isNotBlank(messageBody)) {
				messageBody = EncryptionUtil.encrypt(messageBody, StringUtils.join(senderId, recipientId),
						StringUtils.join(recipientId, senderId));
				encrypted = 1;
			}
		} catch (Exception e) {
			log.error("Encryption for recieved Facebook message failed: {}", e);
		}
		if (StringUtils.isNotBlank(messageBody)) {
			fbMessage.setMessageBody(messageBody);
		}
		fbMessage.setEncrypted(encrypted);
		fbMessage.setBusinessId(conversation.getBusinessId());
		if (message.getMediaFile() != null) {
			MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(message);
			fbMessage.setMediaURL(messengerMediaFileDTO.getUrl());
			try {
				facebookMessageRepository.saveAndFlush(fbMessage);
				fbMessage.getMessageMigrationDto().setMessengerMediaFileDTO(messengerMediaFileDTO);
				MessengerMediaFile messengerMediaFile = messengerMediaFileService.saveMediaFile(messengerMediaFileDTO,
						fbMessage.getId());
				publishImageUploadRequest(conversation.getBusinessId(), messengerMediaFile.getId(),
						messengerMediaFile.getUrl(), Channel.FACEBOOK);
			} catch (Exception e) {
				log.error("Duplicate facebook message message id: {}: ", messageId);
			}
		}
		return fbMessage;
	}

	private List<MessengerMessage> createMessengerMessageEntitiesFromSms(List<Sms> smsEntities,
			Conversation conversation, List<MessageDocument> msgDocs, BusinessDTO businessDTO,
			Map<String, Integer> userIdMap) {

		List<MessengerMessage> messengerMessages = new ArrayList<MessengerMessage>();
		if (CollectionUtils.isNotEmpty(smsEntities)) {
			smsEntities.forEach(sms -> {
				UserDTO userDTO = null;
				MessengerEvent messengerEvent = null;
				MessengerMessage messengerMessage = new MessengerMessage();
				messengerMessage.setAccountId(conversation.getBusinessId());
				messengerMessage.setMessageId(sms.getSmsId());
				messengerMessage.setCreatedDate(sms.getCreateDate());
				messengerMessage.setCreatedBy(sms.getMessageMigrationDto().getCreatedBy());
				messengerMessage.setSentThrough(sms.getMessageMigrationDto().getSentThrough());
				messengerMessage.setMessageType(MessageDocument.MessageType.CHAT.name());
				messengerMessage.setMessengerContactId(conversation.getMessengerContactId());
				messengerMessage.setExtRefUid(sms.getMessageMigrationDto().getExtRefUid());
				ConversationDTO conversationDTO = new ConversationDTO(sms);
				conversationDTO.setSentThrough(getSentThrough(messengerMessage.getSentThrough()));
				conversationDTO.setExtRefUid(messengerMessage.getExtRefUid());
				if (sms.getMessageMigrationDto().getCreatedBy() != null) {
					userDTO = communicationHelperService.getUserDTO(sms.getMessageMigrationDto().getCreatedBy());
				}
				if (CommunicationDirection.SEND.equals(sms.getMessageMigrationDto().getCommunicationDirection())) {
					messengerEvent = MessengerEvent.SMS_SEND;
					if (sms.getMessageMigrationDto().getMessengerMediaFileDTO() != null) {
						messengerEvent = MessengerEvent.MMS_SEND;
					}
				} else {
					messengerEvent = MessengerEvent.SMS_RECEIVE;
					conversationDTO.setCommunicationDirection(CommunicationDirection.RECEIVE);
					if (sms.getMessageMigrationDto().getMessengerMediaFileDTO() != null) {
						messengerEvent = MessengerEvent.MMS_RECEIVE;
					}
				}
				MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(conversationDTO,
						conversation.getMessengerContactId());
				msgDocs.add(getMessageUpsertDocument(businessDTO, userDTO, messageDocumentDTO,
						sms.getMessageMigrationDto().getMessengerMediaFileDTO(), messengerEvent));
				log.info("[createMessengerMessageEntitiesFromSms] - persisting sms in store for business {} : {}",
						conversation.getBusinessId(), messengerMessage);
				messengerMessages.add(messengerMessage);
			});
		}
		return messengerMessages;

	}

	private SentThrough getSentThrough(String sentThrough) {
		for (SentThrough st : SentThrough.values()) {
			if (st.name().equals(sentThrough))
				return st;
		}
		return SentThrough.WEB;
	}

	private List<Sms> createSmsEntities(List<Message> webChatMsgs, Map<String, Integer> userIdMap,
			Conversation conversation) {
		List<Sms> smsList = new ArrayList<Sms>();
		webChatMsgs.forEach(dto -> {
			MessengerMessage existingMessengerMessage = findExistingMessengerMessage(conversation.getAccountId(),
					dto.getUuid());
			if (null == existingMessengerMessage) {
				smsList.add(convertDtoToSmsEntity(dto, userIdMap, conversation));
			}
		});
		log.info("[createSmsEntities] - created sms list from received msg list : {}", smsList);
		return smsList;
	}

	private Sms convertDtoToSmsEntity(Message dto, Map<String, Integer> userIdMap, Conversation conversation) {
		Sms sms = new Sms();
		MessageMigrationDto messageMigrationDto = new MessageMigrationDto();
		sms.setMessageMigrationDto(messageMigrationDto);
		String fromNumber;
		String toNumber;
		if (MessageDocument.CommunicationDirection.SEND.name().equals(dto.getCommunicationDirection())) {
			fromNumber = smsService.getFormattedBusinessNumber(conversation.getBusinessId(),
					conversation.getBusinessPhoneNumber());
			toNumber = smsService.getFormattedBusinessNumber(conversation.getBusinessId(),
					conversation.getCustomerDetails().getPhone());
			Integer userId = getCreatedBy(dto, userIdMap);
			sms.getMessageMigrationDto().setCreatedBy(userId);
			sms.getMessageMigrationDto().setCommunicationDirection(CommunicationDirection.SEND);
		} else {
			fromNumber = smsService.getFormattedBusinessNumber(conversation.getBusinessId(),
					conversation.getCustomerDetails().getPhone());
			toNumber = smsService.getFormattedBusinessNumber(conversation.getBusinessId(),
					conversation.getBusinessPhoneNumber());
			sms.getMessageMigrationDto().setCommunicationDirection(CommunicationDirection.RECEIVE);
		}
		sms.setFromNumber(fromNumber);
		sms.setToNumber(toNumber);
		sms.setBusinessId(conversation.getBusinessId());
		sms.setCustomerId(conversation.getCustomerId());
		sms.setMessageBodyUnencrypted(dto.getMessageBody());
		sms.setCreateDate(parseDate(dto.getCreatedDate(), conversation.getTimeZoneId()));
		sms.setSentOn(parseDate(dto.getCreatedDate(), conversation.getTimeZoneId()));
		sms.setSource(1);
		smsService.encrypt(sms);
		sms.getMessageMigrationDto().setSentThrough(dto.getSentThrough());
		sms.getMessageMigrationDto().setExtRefUid(dto.getUuid());
		if (dto.getMediaFile() != null) {
			MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(dto);
			sms.setMediaURL(messengerMediaFileDTO.getUrl());
			messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(messengerMediaFileDTO.getUrl()));
			sms.getMessageMigrationDto().setMessengerMediaFileDTO(messengerMediaFileDTO);
			smsRepository.saveAndFlush(sms);
			MessengerMediaFile messengerMediaFile = messengerMediaFileService.saveMediaFile(messengerMediaFileDTO,
					sms.getSmsId());
			// publishing image uploading event
			publishImageUploadRequest(conversation.getBusinessId(), messengerMediaFile.getId(),
					messengerMediaFile.getUrl(), Channel.SMS);
		}
		return sms;
	}

	private void groupMessagesByTypes(Conversation conversation, List<Message> webChatMsgs, List<Message> facebookMsgs,
			List<Message> activityMsgs, List<Message> internalNoteMsgs) {
		log.info("grouping message by messageType for account {}", conversation.getAccountId());
		conversation.getMessages().forEach(msg -> {
			String msgType = msg.getMessageType();
			if (MessageDocument.MessageType.ACTIVITY.name().equals(msgType)) {
				activityMsgs.add(msg);
			} else if (MessageDocument.MessageType.CHAT.name().equals(msgType)) {
				if (MessageDocument.Channel.FACEBOOK.name().equals(msg.getChannel())) {
					facebookMsgs.add(msg);
				} else {
					webChatMsgs.add(msg);
				}
			} else if (MessageDocument.MessageType.INTERNAL_NOTES.name().equals(msgType)) {
				internalNoteMsgs.add(msg);
			}
		});
	}

	private List<MessengerNote> createNoteEntities(List<Message> internalNoteMsgs, Map<String, Integer> userIdMap,
			Conversation conversation) {
		Integer accountId = conversation.getAccountId();
		List<MessengerNote> internalNotesList = new ArrayList<MessengerNote>();
		internalNoteMsgs.forEach(dto -> {
			MessengerMessage existingMessengerMessage = findExistingMessengerMessage(accountId, dto.getUuid());
			if (null == existingMessengerMessage) {
				internalNotesList.add(convertDtoToNoteEntity(dto, userIdMap, conversation.getTimeZoneId()));
			}
		});
		log.info("[createNoteEntities] - created msg notes from received msg list : {}", internalNotesList);
		return internalNotesList;
	}

	private void updateLastMessageInContact(Conversation conversation, Map<String, Integer> userIdMap,
			BusinessDTO businessDTO, CustomerDTO customerDTO) {
		MessengerContact messengerContact = null;
		UserDTO userDTO = null;
		Message lastItem = getLastMessage(conversation);
		if (lastItem == null) {
			log.error("[migratePodiumData] : No LastMessage found for contactId: {} and businessId: {}",
					conversation.getMessengerContactId(),
					conversation.getBusinessId());
			return;
		}

		Optional<MessengerContact> messengerContactOpt = messengerContactRepository
				.findById(conversation.getMessengerContactId());
		if (messengerContactOpt.isPresent()) {
			messengerContact = messengerContactOpt.get();
			Integer userId = getCreatedBy(lastItem, userIdMap);
			userDTO = communicationHelperService.getUserDTO(userId);
			if (MessageType.INTERNAL_NOTES.name().equals(lastItem.getMessageType())) {
				messengerContact = updateNotesLastMessage(messengerContact, conversation, userDTO,
						lastItem);
			} else if (MessageType.CHAT.name().equals(lastItem.getMessageType())
					&& !"FAIL".equals(lastItem.getCommunicationDirection())) {
				if (Channel.FACEBOOK.name().equals(lastItem.getChannel())) {
					if (CommunicationDirection.SEND.name().equals(lastItem.getCommunicationDirection())) {
						messengerContact = updateFacebookLastMessage(messengerContact, conversation, userDTO,
								lastItem, CommunicationDirection.SEND, businessDTO);
					} else {
						messengerContact = updateFacebookLastMessage(messengerContact, conversation, userDTO,
								lastItem, CommunicationDirection.RECEIVE, businessDTO);
					}
				} else {
					if (CommunicationDirection.SEND.name().equals(lastItem.getCommunicationDirection())) {
						messengerContact = updateSmsLastMessage(messengerContact, conversation, userDTO,
								lastItem, CommunicationDirection.SEND, businessDTO);
					} else {
						messengerContact = updateSmsLastMessage(messengerContact, conversation, userDTO,
								lastItem, CommunicationDirection.RECEIVE, businessDTO);
					}
				}
			} else
				messengerContact = updateActivityorFailedLastMessage(messengerContact, lastItem, conversation);
			messengerContactRepository.save(messengerContact);
			messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO,
					getTag(conversation.getOpen()), userDTO);
			log.info("[updateLastMessageInContact] - successfully updated messenger contact for contactId : {}",
					messengerContact.getId());
		}
	}

	private Message getLastMessage(Conversation conversation) {
		Message lastMessage = conversation.getLastItem();
		if (lastMessage != null && (MessageType.ACTIVITY.name().equals(lastMessage.getMessageType())
				|| "FAIL".equals(lastMessage.getCommunicationDirection()))) {
			List<Message> messageList = conversation.getMessages();
			Collections.reverse(messageList);
			Optional<Message> lastMessageOpt = messageList.stream().filter(msg -> isChatOrNote(msg)).findFirst();
			if (lastMessageOpt.isPresent())
				lastMessage = lastMessageOpt.get();
		}
		return lastMessage;
	}

	private boolean isChatOrNote(Message msg) {
		return MessageType.CHAT.name().equals(msg.getMessageType())
				|| MessageType.INTERNAL_NOTES.name().equals(msg.getMessageType());
	}

	private MessengerContact updateSmsLastMessage(MessengerContact messengerContact, Conversation conversation,
			UserDTO userDTO, Message lastItem, CommunicationDirection communicationDirection, BusinessDTO businessDTO) {

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType(communicationDirection.name());
		if (CommunicationDirection.SEND.equals(communicationDirection)) {
			lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
			lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		}
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastResponseAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setLastMsgOn(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setUpdatedAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));

		if (StringUtils.isEmpty(lastItem.getMessageBody()) && lastItem.getMediaFile() != null) {
			messengerContact.setLastMessage("Sent an attachment");
			if (CommunicationDirection.RECEIVE.equals(communicationDirection)) {
				messengerContact.setLastMessage("Received an attachment");
			}
		} else {
			messengerContact.setLastMessage(lastItem.getMessageBody());
		}
		String encryptionEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("encryption.enabled", "true");
		Integer encypted = "true".equalsIgnoreCase(encryptionEnabled) ? 1 : 0;
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, encypted,
				conversation.getCustomerPhoneNumber(),
				businessDTO.getBusinessNumber());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
		return messengerContact;
	}

	private MessengerContact updateFacebookLastMessage(MessengerContact messengerContact, Conversation conversation,
			UserDTO userDTO, Message lastItem, CommunicationDirection communicationDirection, BusinessDTO businessDTO) {
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType(communicationDirection.name());
		if (CommunicationDirection.SEND.equals(communicationDirection)) {
			lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
			lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		}
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastResponseAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setLastMsgOn(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setUpdatedAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		if (StringUtils.isEmpty(lastItem.getMessageBody()) && lastItem.getMediaFile() != null) {
			messengerContact.setLastMessage("Sent an attachment");
			if (CommunicationDirection.RECEIVE.equals(communicationDirection)) {
				messengerContact.setLastMessage("Received an attachment");
			}
		} else {
			messengerContact.setLastMessage(lastItem.getMessageBody());
		}
		String encryptionEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("encryption.enabled", "true");
		Integer encypted = "true".equalsIgnoreCase(encryptionEnabled) ? 1 : 0;
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, encypted,
				messengerContact.getFacebookId(),
				businessDTO.getBusinessNumber());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
		return messengerContact;
	}

	private MessengerContact updateActivityorFailedLastMessage(MessengerContact messengerContact, Message lastItem,
			Conversation conversation) {
		messengerContact.setLastResponseAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setUpdatedAt(parseDate(lastItem.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setEncrypted(0);
		return messengerContact;
	}

	private MessengerContact updateNotesLastMessage(MessengerContact messengerContact, Conversation conversation,
			UserDTO userDTO, Message message) {
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetaData.setLastMessageType(MessageType.INTERNAL_NOTES.name());
		lastMessageMetaData.setLastMessageUserId(userDTO.getId());
		lastMessageMetaData.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
		messengerContact.setLastMsgOn(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setUpdatedAt(parseDate(message.getCreatedDate(), conversation.getTimeZoneId()));
		messengerContact.setLastMessage(message.getMessageBody());
		messengerContact.setEncrypted(0);
		return messengerContact;
	}

	private Map<String, Integer> getUserIdFromCore(Conversation conversation) {
		Map<String, Integer> response = new HashMap<String, Integer>();
		GetUserIdsRequest request = null;
		if (StringUtils.isNotBlank(conversation.getAssigneeEmailId())) {
			request = new GetUserIdsRequest();
			request.getEmailIds().add(conversation.getAssigneeEmailId());
		}
		if (CollectionUtils.isNotEmpty(conversation.getMessages())) {
			if (request == null) {
				request = new GetUserIdsRequest();
			}
			addUserNamesInRequest(conversation.getMessages(), request);
		}
		if (conversation.getLastItem() != null) {
			if (request == null) {
				request = new GetUserIdsRequest();
			}
			String userName = conversation.getLastItem().getTriggeredFrom();
			if (StringUtils.isNotBlank(userName)) {
				request.getUserNames().add(userName);
			}
		}
		if (request != null) {
			request.setAccountId(conversation.getAccountId());
			response = businessService.getUserIds(request);
		}
		return response;
	}

	private void addUserNamesInRequest(List<Message> messages, GetUserIdsRequest request) {
		messages.forEach(msg -> {
			String userName = msg.getTriggeredFrom();
			if (StringUtils.isNotBlank(userName)) {
				request.getUserNames().add(userName);
			}
		});
	}

	private KontactoRequest create(CustomerDetails cd, BusinessDTO businessDTO) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setBusinessId(businessDTO.getBusinessId());
		kontactoRequest.setName(getContactName(cd));
		kontactoRequest.setEmailId(cd.getEmailId());
		kontactoRequest.setPhone(cd.getPhone());
		kontactoRequest.setSource(KontactoRequest.DASHBOARD);
		// For Web-chat sms enable by default
		kontactoRequest.setSmsEnabled(1);
		KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
		locationInfo.setCountryCode(businessDTO.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	private String getContactName(CustomerDetails cd) {
		String contactName = null;
		if (StringUtils.isNotBlank(cd.getName()) && cd.getName().length() > 50) {
			String truncatedName = cd.getName().replaceAll("(?<=.{50})\\b.*", "");
			if (StringUtils.isNotBlank(truncatedName) && truncatedName.length() > 50) {
				contactName = truncatedName.substring(0, truncatedName.lastIndexOf(" "));
			} else {
				contactName = truncatedName;
			}
		} else {
			contactName = cd.getName();
		}
		return contactName;
	}

	private KontactoRequest createKontactoFBRequest(CustomerDetails cd, BusinessDTO businessDTO) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setBusinessId(businessDTO.getBusinessId());
		kontactoRequest.setName(getContactName(cd));
		kontactoRequest.setEmailId(cd.getFbUserId() + "@fb.com");
		kontactoRequest.setSource(KontactoRequest.FACEBOOK);
		KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
		locationInfo.setCountryCode(businessDTO.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	private MessengerContact getOrCreateMessengerContact(Conversation conversation, CustomerDTO customerDTO,
			BusinessDTO businessDTO, Map<String, Integer> userIdMap) {
		Integer tag = 1;
		if (conversation.getOpen() != null && conversation.getOpen() == 0) {
			tag = 3;
		}
		MessengerContact messengerContact = messengerContactService.findOrCreate(businessDTO, tag, customerDTO);
		Integer userId = -1;
		if (messengerContact != null && BooleanUtils.isTrue(messengerContact.getIsNew())) {
			if (conversation.getCustomerDetails() != null
					&& StringUtils.isNotEmpty(conversation.getCustomerDetails().getFbUserId())) {
				messengerContact.setFacebookId(conversation.getCustomerDetails().getFbUserId());
				userId = -8;
			}
			messengerContact.setExtRefUid(conversation.getUuid());
			if (StringUtils.isNotBlank(conversation.getAssigneeEmailId())) {
				if (userIdMap.get(conversation.getAssigneeEmailId()) != null) {
					messengerContact.setCurrentAssignee(userIdMap.get(conversation.getAssigneeEmailId()));
					messengerContact.setCurrentAssigner(userId);
					messengerContact.setCurrentAssigneeEmailId(conversation.getAssigneeEmailId());
					if (StringUtils.isNotBlank(conversation.getAssigneeName())) {
						messengerContact.setCurrentAssigneeName(conversation.getAssigneeName());
					}
				}

			}
			messengerContact.setIsRead(false);
			messengerContact.setUpdatedAt(new Date());
		}

		return messengerContact;
	}

	private MessengerNote convertDtoToNoteEntity(Message message, Map<String, Integer> userIdMap, String timezoneId) {
		Integer userId = getCreatedBy(message, userIdMap);
		MessengerNote messengerNote = new MessengerNote();
		MessageMigrationDto messageMigrationDto = new MessageMigrationDto();
		messengerNote.setContent(message.getMessageBody());
		messengerNote.setCreatedBy(userId);
		messengerNote.setNoteStatus(MessageDocument.NoteState.CREATED.name());
		messengerNote.setNoteType(MessageDocument.NoteType.INFO.name());
		messengerNote.setCreatedDate(parseDate(message.getCreatedDate(), timezoneId));
		messageMigrationDto.setSentThrough(message.getSentThrough());
		messageMigrationDto.setExtRefUid(message.getUuid());
		messengerNote.setMessageMigrationDto(messageMigrationDto);
		return messengerNote;
	}

	private Integer getCreatedBy(Message message, Map<String, Integer> userIdMap) {
		Integer userId = Constants.INBOX_USER_ID;
		if (StringUtils.isNotBlank(message.getTriggeredFrom())) {
			if (VIA_FACEBOOK_MESSENGER.equals(message.getTriggeredFrom()))
				return MessengerConstants.FACEBOOK_PAGE_USER;
			if (MapUtils.isNotEmpty(userIdMap)) {
				userId = userIdMap.get(message.getTriggeredFrom()) != null ? userIdMap.get(message.getTriggeredFrom())
						: userId;
			}
		}
		return userId;
	}

	private List<MessengerMessage> createNotesMessengerMessageEntities(List<MessengerNote> internalNotesEntities,
			Conversation conversation, List<MessageDocument> msgDocs, BusinessDTO businessDTO) {
		List<MessengerMessage> messengerMessages = new ArrayList<MessengerMessage>();
		if (CollectionUtils.isNotEmpty(internalNotesEntities)) {
			internalNotesEntities.forEach(note -> {
				UserDTO userDTO = null;
				MessengerMessage messengerMessage = new MessengerMessage();
				messengerMessage.setAccountId(conversation.getBusinessId());
				messengerMessage.setMessageId(note.getId());
				messengerMessage.setCreatedDate(note.getCreatedDate());
				messengerMessage.setCreatedBy(note.getCreatedBy());
				messengerMessage.setMessageType(MessageDocument.MessageType.INTERNAL_NOTES.name());
				messengerMessage.setMessengerContactId(conversation.getMessengerContactId());
				messengerMessage.setSentThrough(note.getMessageMigrationDto().getSentThrough());
				messengerMessage.setExtRefUid(note.getMessageMigrationDto().getExtRefUid());
				ConversationDTO conversationDTO = new ConversationDTO(messengerMessage, note);
				MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(conversationDTO,
						conversation.getMessengerContactId());
				if (note.getCreatedBy() != 0) {
					userDTO = communicationHelperService.getUserDTO(note.getCreatedBy());
				}
				msgDocs.add(getMessageUpsertDocument(businessDTO, userDTO, messageDocumentDTO, null,
						MessengerEvent.INTERNAL_NOTES));
				log.info("[createNotesMessengerMessageEntities] - persisting notes in store for business {} : {}",
						conversation.getBusinessId(), messengerMessage);
				messengerMessages.add(messengerMessage);
			});
		}
		return messengerMessages;
	}

	private MessageDocument getMessageUpsertDocument(BusinessDTO businessDTO, UserDTO userDTO,
			MessageDocumentDTO messageDocumentDTO, MessengerMediaFileDTO messengerMediaFileDTO,
			MessengerEvent messengerEvent) {
		return messengerContactService.getMessageDocument(messageDocumentDTO, messengerMediaFileDTO, userDTO,
				businessDTO, messengerEvent);
	}

	private static Date parseDate(String str, String timezoneId) {
		try {
			LocalDateTime ldt = LocalDateTime.parse(str, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS'Z'"));
			return Date.from(ldt.atZone(ZoneId.of(timezoneId)).toInstant());
		} catch (Exception e) {
			log.error("Error in parsing date: {}, returning current Date", str);
		}
		return new Date();
	}

	@Override
	public void migrateMedia(ImageUploadToS3Response imageUploadToS3Response) {
		log.info("[migrateMedia] : starting persisting url for request: {}", imageUploadToS3Response);
		if (StringUtils.isBlank(imageUploadToS3Response.getSubBucket())
				|| StringUtils.isBlank(imageUploadToS3Response.getSourceKey())
				|| StringUtils.isBlank(imageUploadToS3Response.getCdnUrl())
				|| !imageUploadToS3Response.getSourceKey().contains("_"))
			throw new InputValidationException(ErrorCode.MISSING_PARAMETERS);
		BusinessDTO businessDTO = businessService
				.getBusinessLiteDTO(Integer.parseInt(imageUploadToS3Response.getSubBucket()));
		if (businessDTO == null) {
			log.error("[migrateMedia] : business not found for business id {}", imageUploadToS3Response.getSubBucket());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		String cdnUrl = commonService.getBaseCDNImageURLForBusiness(businessDTO.getBusinessId());
		String imageUrl = new StringBuilder(cdnUrl).append("/").append(imageUploadToS3Response.getCdnUrl()).toString();

		String[] ids = imageUploadToS3Response.getSourceKey().split("_");
		Channel msgChannel = Channel.valueOf(ids[1]);

		if (!(Channel.FACEBOOK.equals(msgChannel) || Channel.SMS.equals(msgChannel) || Channel.APPLE.equals(msgChannel)
				|| Channel.GOOGLE.equals(msgChannel)))
			throw new InputValidationException(ErrorCode.INVALID_REQUEST);

		Optional<MessengerMediaFile> messengerMediaFile = messengerMediaFileService.findById(Integer.parseInt(ids[0]));
		if (messengerMediaFile.isPresent()) {
			MessengerMediaFile messengerMedia = messengerMediaFile.get();
			messengerMedia.setUrl(imageUrl);
			messengerMedia.setName(imageUploadToS3Response.getCdnUrl());
			messengerMediaFileService.saveMedia(messengerMedia);
			log.info("[migrateMedia] : successfully persisted media url for mediaId {} and sourceKey {}",
					messengerMedia.getId(), imageUploadToS3Response.getSourceKey());

			String msgId = Channel.FACEBOOK.equals(msgChannel) ? messengerMedia.getMessageId() + "_m"
					: Channel.GOOGLE.equals(msgChannel) ? messengerMedia.getMessageId() + "_g"
							: Channel.APPLE.equals(msgChannel) ? messengerMedia.getMessageId() + "_ac"
									: String.valueOf(messengerMedia.getMessageId());
			// upsert operation on ES
			updateMediaLinksOnES(businessDTO.getAccountId(), msgId, imageUploadToS3Response.getCdnUrl(), imageUrl,
					msgChannel);
		} else
			log.error("[migrateMedia] : no entry present within messenger media with msg Id {}",
					imageUploadToS3Response.getSourceKey());
	}

	public void updateMediaLinksOnES(Integer accountId, String msgId, String imageName, String imageUrl,
			Channel channel) {
		log.info("[updateMediaLinksOnES] : starting updating media link into ES for _id {} and channel {}", msgId,
				channel);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("size", 1);
		dataModel.put("from", 0);
		dataModel.put("_id", msgId);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.MESSAGE_BY_ID_V2, dataModel).build();

		List<MessageDocument> messageDocument = elasticSearchService.searchByQuery(esRequest, MessageDocument.class);
		log.info("[updateMediaLinksOnES] : fetched msg content: {}", messageDocument);

		if (CollectionUtils.isNotEmpty(messageDocument)) {
			MessageDocument msg = messageDocument.get(0);
			if (MessageDocument.MessageType.CHAT.equals(msg.getMessageType()) && channel.equals(msg.getChannel())) {
				List<MessageDocument.MediaFile> mediaFiles = msg.getMediaFiles();
				if (CollectionUtils.isNotEmpty(mediaFiles) && mediaFiles.size() == 1) {
					log.info("[updateMediaLinksOnES] : setting updated url and name to ES document for _id {}", msgId);
					mediaFiles.get(0).setA_url(imageUrl);
					mediaFiles.get(0).setA_name(imageName);
				}
				if (StringUtils.isNotBlank(msg.getA_url()))
					msg.setA_url(imageUrl);
				if (StringUtils.isNotBlank(msg.getA_name()))
					msg.setA_name(imageName);
				messengerContactService.addNewMessageDocOnES(msg, String.valueOf(msgId));
				log.info("[updateMediaLinksOnES] : updated media link into ES successfully for _id {}", msgId);
			}
		} else {
			log.error("[updateMediaLinksOnES] : no msg found on ES for _id {}", msgId);
		}
	}

	/**
	 *
	 * @param businessId
	 * @param messageId
	 * @param imageUrl
	 */
	@Override
	public void publishImageUploadRequest(Integer businessId, Integer messageId, String imageUrl, Channel channel) {
		try {
			ImageUploadRequestToS3 request = new ImageUploadRequestToS3(String.valueOf(businessId), imageUrl,
					KafkaTopicEnum.IMPORTED_ATTACHMENT_URL_UPDATE_EVENT.getTopicValue(),
					messageId + "_" + channel.name(),
					"attachments/", "messenger");
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.CORE_UPLOAD_IMAGE, null, request);
		} catch (Exception e) {
			log.error(
					"[publishImageUploadRequest] : exception occurred while publishing message to Kafka for businessId {} and messageId {}",
					businessId, messageId);
		}
	}

	/**
	 * 
	 * @param businessId
	 * @param extRefUid
	 * @return
	 */
	private MessengerMessage findExistingMessengerMessage(Integer businessId, String extRefUid) {
		return messengerMessageRepository.findByAccountIdAndExtRefUid(businessId, extRefUid);
	}

	@Override
	public String publishEventForLastReceivedMessageSource(MigrateLastReceivedMessageSourceRequest request) {
		List<ContactDocument> contactDocuments = new ArrayList<ContactDocument>();
		List<Integer> mcids = new ArrayList<Integer>();
		SearchResponse searchResponse = null;
		String scrollId = request.getScrollId();
		if (StringUtils.isBlank(scrollId)) {
			searchResponse = getSearchResult(request);
		} else {
			searchResponse = elasticSearchService.readMoreFromSearch(scrollId, request.getSessionTime().trim());
			if (searchResponse != null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getSearchResult(request);
			}
		}
		if (Objects.nonNull(searchResponse)) {
			contactDocuments = getContactsListFromResponse(searchResponse);
		}
		if (CollectionUtils.isEmpty(contactDocuments)) {
			log.info("No record's found for LastReceivedMessageSource migration");
			return null;
		}
		scrollId = searchResponse.getScrollId();
		Integer businessId = contactDocuments.get(0).getB_id();
		for (ContactDocument contactDocument : contactDocuments) {
			if (mcids.size() == request.getBatchSize() || !businessId.equals(contactDocument.getB_id())) {
				pushEventInKafka(mcids, businessId);
				mcids.clear();
			}
			mcids.add(contactDocument.getM_c_id());
			businessId = contactDocument.getB_id();
		}
		if (CollectionUtils.isNotEmpty(mcids) && mcids.size() <= request.getBatchSize()) {
			log.info("Pushing last batch conversation update event  for businessId: {} and conversationIds: {}",
					businessId, mcids);
			pushEventInKafka(mcids, businessId);
		}
		return scrollId;
	}

	private SearchResponse getSearchResult(MigrateBusinessIdInOldMessageDocumentRequest request) {
		SearchResponse searchResult;
		Map<String, Integer> messageFilter = new HashMap<>();
		messageFilter.put("count", request.getEsFetchSize());
		if (request.getAccountId() == null) {
			messageFilter.put("contactId", request.getContactId());
		}
		messageFilter.put("accountId", request.getAccountId());
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addScroll(request.getSessionTime().trim())
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_FOR_BID_MIGRATION, dataModel).build();
		if (request.getAccountId() != null) {
			esRequest.setRoutingId(request.getAccountId());
		}
		searchResult = elasticSearchService.getSearchResult(esRequest);
		return searchResult;
	}

	private SearchResponse getSearchResult(MigrateLastReceivedMessageSourceRequest request) {
		SearchResponse searchResponse;
		Map<String, Integer> messageFilter = new HashMap<>();
		messageFilter.put("count", request.getEsFetchSize());
		if (request.getAccountId() == null) {
			messageFilter.put("contactId", request.getContactId());
		}
		messageFilter.put("accountId", request.getAccountId());
		messageFilter.put("businessId", request.getBusinessId());
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addScroll(request.getSessionTime().trim())
				.addTemplateAndDataModel(Constants.Elastic.GET_CONTACT_FOR_MIGRATION_V1, dataModel).build();
		searchResponse = elasticSearchService.getSearchResult(esRequest);
		return searchResponse;
	}

	private void pushEventInKafka(List<Integer> mcids, Integer businessId) {
		MessengerFilter filter = new MessengerFilter();
		filter.setMcIds(mcids);
		log.info("Pushing conversation update event for businessId: {} and conversationIds: {}", businessId, mcids);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.UPDATE_LAST_RECIEVED_MESSAGE_SOURCE, null, filter);
	}

	private void pushMergeDuplicateContactsEventInKafka(List<Integer> customerIds, Integer businessId) {
		MergeDuplicateContactsEvent event = new MergeDuplicateContactsEvent();
		event.setCustomerIds(customerIds);
		event.setBusinessId(businessId);
		log.info("Pushing merge duplicate contacts event for businessId: {} and conversationIds: {}", businessId,
				customerIds);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.MERGE_DUPLCATE_CONTACTS, null, event);
	}

	private void pushBidMigrationEventInKafka(List<String> messageDocumentIds, Integer accountId) {
		MessengerFilter filter = new MessengerFilter();
		filter.setCount(messageDocumentIds.size());
		filter.setMessageDocumentIds(messageDocumentIds);
		filter.setAccountId(accountId);
		filter.setEnterpriseId(accountId);
		log.info("Pushing message update event for accountId: {} and messageIds: {}", accountId, messageDocumentIds);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.UPDATE_BUSINESSID_MESSAGE_DOC, null, filter);
	}

	@Override
	@Transactional
	public void migrateLastRecievedMessageSource(List<Integer> mcids) {
		List<MessengerContact> contacts = messengerContactRepository.findContactsByIds(mcids);
		Map<Integer, MessengerContact> messengerContactMap = new HashMap<Integer, MessengerContact>();
		contacts.forEach(mc -> {
			messengerContactMap.put(mc.getId(), mc);
		});
		Map<Integer, ContactDocument> contactDocumentMap = new HashMap<Integer, ContactDocument>();
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("count", 1000);
		messageFilter.put("mcIds", mcids.toString());
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("data", messageFilter);
		// GET Contact Documents
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_CONTACT_FOR_MIGRATION, dataModel).build();
		List<ContactDocument> contactDocuments = elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
		contactDocuments.forEach(doc -> {
			contactDocumentMap.put(doc.getM_c_id(), doc);
		});
		// GET Contact's Last Recieved Source Map
		Map<Integer, Integer> contactIdSourceMap = getContactLastRecievedSourceMap(dataModel);
		if (MapUtils.isNotEmpty(contactIdSourceMap)) {
			updateLastRecievedMessageSourceInContact(messengerContactMap, contactDocumentMap, contactIdSourceMap);
		}
	}

	private void updateLastRecievedMessageSourceInContact(Map<Integer, MessengerContact> messengerContactMap,
			Map<Integer, ContactDocument> contactDocumentMap, Map<Integer, Integer> contactIdSourceMap) {
		List<ContactDocument> contactDocumentList = new ArrayList<ContactDocument>();
		List<MessengerContact> messengerContactList = new ArrayList<MessengerContact>();
		List<Integer> mcids = new ArrayList<Integer>();
		if (MapUtils.isNotEmpty(contactIdSourceMap)) {
			contactIdSourceMap.forEach((contactId, source) -> {
				MessengerContact contact = messengerContactMap.get(contactId);
				ContactDocument contactDocument = contactDocumentMap.get(contactId);
				if (contact != null && contactDocument != null) {
					// Updating LastReceivedMessageSource in Messenger Contact
					LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(contact);
					lastMessageMetaData.setLastReceivedMessageSource(source);
					contact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
					messengerContactList.add(contact);
					mcids.add(contactId);
					// Updating LastReceivedMessageSource in ContactDocument
					lastMessageMetaData = new LastMessageMetaData();
					lastMessageMetaData.setLastReceivedMessageSource(source);
					contactDocument.setLastMessageMetaData(lastMessageMetaData);
					contactDocumentList.add(contactDocument);
				}
			});
			if (CollectionUtils.isNotEmpty(messengerContactList) && CollectionUtils.isNotEmpty(contactDocumentList)) {
				log.info("Updating last recieved message source for mcids: ", mcids);
				messengerContactRepository.saveAll(messengerContactList);
				Integer routingId = contactDocumentList.get(0).getE_id();
				;
				BulkUpsertPayload<ContactDocument> bulkUpsertPayload = new BulkUpsertPayload<>(contactDocumentList,
						routingId, routingId, Constants.Elastic.CONTACT_INDEX);
				try {
					elasticSearchService.bulkUpsert(bulkUpsertPayload);
				} catch (Exception e) {
					log.error("Exception while updating the message docs  exception :{}", e);
				}
			}
		}
	}

	private Map<Integer, Integer> getContactLastRecievedSourceMap(Map<String, Object> dataModel) {
		ESRequest esRequest;
		Map<Integer, Integer> contactIdSourceMap = new HashMap<Integer, Integer>();
		esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_LAST_RECIEVED_MESSAGE_SOURCE, dataModel).build();
		SearchResponse searchResponse = elasticSearchService.getSearchResult(esRequest);
		if (Objects.nonNull(searchResponse)) {
			// To convert SearchResponse to json
			XContentBuilder builder = null;
			LastRecievedMessageAggregation response = null;
			try {
				builder = XContentFactory.jsonBuilder();
				searchResponse.toXContent(builder, ToXContent.EMPTY_PARAMS);
				JSONObject json = new JSONObject(builder.toString());
				response = ControllerUtil.getObjectFromJsonText(json.toString(),
						LastRecievedMessageAggregation.class);
			} catch (IOException e) {
				log.error("IOException in getDataFromElastic for request {}", esRequest);
			} catch (JSONException e) {
				log.error("JSONException in getDataFromElastic for request {}", esRequest);
			}
			// LastRecievedMessageAggregation response =
			// ControllerUtil.getObjectFromJsonText(searchResponse.getJsonString(),
			// LastRecievedMessageAggregation.class);
			if (response != null && response.getAggregations() != null
					&& response.getAggregations().getOuter_conv_aggr() != null) {
				if (CollectionUtils.isNotEmpty(response.getAggregations().getOuter_conv_aggr().getBuckets())) {
					List<Bucket> buckets = response.getAggregations().getOuter_conv_aggr().getBuckets();
					buckets.forEach(bucket -> {
						if (bucket.getInner_conv_aggr() != null && bucket.getInner_conv_aggr().getHits() != null
								&& CollectionUtils.isNotEmpty(bucket.getInner_conv_aggr().getHits().getHits())) {
							InnerHits innetHit = bucket.getInner_conv_aggr().getHits().getHits().get(0);
							if (innetHit.get_source() != null && innetHit.get_source().getC_id() != null
									&& innetHit.get_source().getSource() != null) {
								contactIdSourceMap.put(innetHit.get_source().getC_id(),
										innetHit.get_source().getSource());
							}
						}
					});
				}
			}
		}
		return contactIdSourceMap;
	}

	public void validateRequest(Conversation conversation) {
		if (conversation.getBusinessId() == null && conversation.getLocationDetails() == null) {
			log.error("BusinessId and LocationDetails bot cannot be null for account {} ", conversation.getAccountId());
			throw new BadRequestException(
					new ErrorMessageBuilder(ErrorCode.INVALID_MIGRATION_REQUEST, HttpStatus.BAD_REQUEST));
		}
	}

	@Override
	public String publishEventToMigrateBusinessIdInOldMessageDocs(
			@NotNull @Valid MigrateBusinessIdInOldMessageDocumentRequest request) {
		List<MessageDocument> messageDocuments = new ArrayList<MessageDocument>();
		List<String> messageDocumentIds = new ArrayList<String>();
		SearchResponse searchResponse = null;
		String scrollId = request.getScrollId();
		if (StringUtils.isBlank(scrollId)) {
			searchResponse = getSearchResult(request);
		} else {
			searchResponse = elasticSearchService.readMoreFromSearch(scrollId, request.getSessionTime().trim());
			if (searchResponse != null && searchResponse.status() == RestStatus.NOT_FOUND) {
				// Regenerating scroll Id if scroll session is expired
				searchResponse = getSearchResult(request);
			}
		}
		if (Objects.nonNull(searchResponse)) {
			messageDocuments = getMessagesListFromResponse(searchResponse);
		}
		if (CollectionUtils.isEmpty(messageDocuments)) {
			log.info("No record's found for b_id migration");
			return null;
		}
		scrollId = searchResponse.getScrollId();
		Integer accountId = messageDocuments.get(0).getE_id();
		for (MessageDocument messageDocument : messageDocuments) {
			if (messageDocument.getE_id() != null) {
				if (messageDocumentIds.size() == request.getBatchSize()
						|| !accountId.equals(messageDocument.getE_id())) {
					pushBidMigrationEventInKafka(messageDocumentIds, accountId);
					messageDocumentIds.clear();
				}
				messageDocumentIds.add(messageDocument.getM_id());
				accountId = messageDocument.getE_id();
			}
		}
		if (CollectionUtils.isNotEmpty(messageDocumentIds) && messageDocumentIds.size() <= request.getBatchSize()) {
			log.info("Pushing last batch of message update event for accountId: {} and messageDocumentIds: {}",
					accountId, messageDocumentIds);
			pushBidMigrationEventInKafka(messageDocumentIds, accountId);
		}
		return scrollId;

	}

	private List<ContactDocument> getContactsListFromResponse(SearchResponse searchResponse) {
		List<ContactDocument> contactList = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> contactList
						.add(objectMapper.convertValue(hit.getSourceAsMap(), ContactDocument.class)));
			}
		}
		return contactList;
	}

	private List<MessageDocument> getMessagesListFromResponse(SearchResponse searchResponse) {
		List<MessageDocument> msgList = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> msgList
						.add(objectMapper.convertValue(hit.getSourceAsMap(), MessageDocument.class)));
			}
		}
		return msgList;
	}

	@Override
	public void migrateBusinessIdInOldMessageDocs(MessengerFilter messengerFilter) {
		Set<Integer> mcids = null;
		Map<String, String> mcidBidMap = new HashMap<String, String>();
		if (CollectionUtils.isNotEmpty(messengerFilter.getMessageDocumentIds())) {
			messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_BY_ID);
			String documentIds = "\"" + String.join("\", \"", messengerFilter.getMessageDocumentIds()) + "\"";
			messengerFilter.setDocumentIds(documentIds);
			messengerFilter.setStartIndex(0);
			messengerFilter.setCount(messengerFilter.getMessageDocumentIds().size());
			List<MessageDocumentTemp> messsages = messengerContactService
					.getMessagesFromESForBidMigration(messengerFilter);
			List<MessageDocumentTemp> messsageDocs = new ArrayList<MessageDocumentTemp>();
			if (CollectionUtils.isNotEmpty(messsages)) {
				mcids = getUniqueMcids(messsages);
				if (CollectionUtils.isNotEmpty(mcids)) {
					mcidBidMap = getContactBusinessIdMap(mcids);
					for (MessageDocumentTemp messageDocument : messsages) {
						if (messageDocument.getB_id() == null && mcidBidMap.get(messageDocument.getC_id()) != null) {
							log.info("updating b_id for message documentId {} ", messageDocument.get_id());
							messageDocument.setB_id(Integer.valueOf(mcidBidMap.get(messageDocument.getC_id())));
							messageDocument.set_id(null);
							messsageDocs.add(messageDocument);
						}
					}
					if (CollectionUtils.isNotEmpty(messsageDocs)) {
						bulkUpdateMessageDocs(messengerFilter, messsageDocs);
					}
				}
			}
		}
	}

	private void bulkUpdateMessageDocs(MessengerFilter messengerFilter, List<MessageDocumentTemp> messsages) {
		Integer routingId = messengerFilter.getEnterpriseId();
		BulkUpsertPayload<MessageDocumentTemp> bulkUpsertPayload = new BulkUpsertPayload<>(messsages,
				routingId, routingId, Constants.Elastic.MESSAGE_INDEX);
		try {
			elasticSearchService.bulkUpsert(bulkUpsertPayload);
		} catch (Exception e) {
			log.error("Exception while updating the message docs  exception :{}", e);
		}
	}

	private Set<Integer> getUniqueMcids(List<MessageDocumentTemp> messsages) {
		Set<Integer> mcids = new HashSet<Integer>();
		for (MessageDocumentTemp messageDocument : messsages) {
			if (StringUtils.isNotBlank(messageDocument.getC_id())) {
				mcids.add(Integer.valueOf(messageDocument.getC_id()));
			}
		}
		return mcids;
	}

	private Map<String, String> getContactBusinessIdMap(Set<Integer> mcids) {
		Map<String, String> mcidBidMap = new HashMap<String, String>();
		List<Object[]> conversationDetails = messengerContactRepository.findBusinessIdsByMcIds(mcids);
		if (CollectionUtils.isNotEmpty(conversationDetails)) {
			for (Object[] objects : conversationDetails) {
				mcidBidMap.put(objects[0].toString(), (String) objects[1].toString());
			}
		}
		return mcidBidMap;
	}

	@Override
	public void migrateWidgetConfiguation(Integer widgetId) {
		List<Integer> webChatWidgetIds = new ArrayList<Integer>();
		if (widgetId != null) {
			webChatWidgetIds.add(widgetId);
		} else {
			webChatWidgetIds = businessChatWidgetConfigRepository.getAllWidgetIds();
		}
		if (CollectionUtils.isNotEmpty(webChatWidgetIds)) {
			final int chunkSize = 500;
			final AtomicInteger counter = new AtomicInteger();
			final Collection<List<Integer>> result = webChatWidgetIds.stream()
					.collect(Collectors.groupingBy(it -> counter.getAndIncrement() / chunkSize))
					.values();
			result.forEach(list -> poolExecutor.execute(() -> migrateWidgetConfiguation(list)));
		}
	}

	private void migrateWidgetConfiguation(List<Integer> webChatWidgetIds) {
		if (CollectionUtils.isNotEmpty(webChatWidgetIds)) {
			List<LiveChatWidgetConfig> chatWidgetConfigs = new ArrayList<LiveChatWidgetConfig>();
			webChatWidgetIds.forEach(widgetId -> {
				LiveChatWidgetConfig chatWidgetConfig = new LiveChatWidgetConfig();
				chatWidgetConfig.setWidgetId(widgetId);
				chatWidgetConfigs.add(chatWidgetConfig);
			});
			liveChatWidgetConfigRepository.saveAll(chatWidgetConfigs);
			log.info("Webchat widget ids size: {}", webChatWidgetIds.size());
		}
	}

	private void migrateMoveEventData(BusinessMoveEvent businessMoveEvent, BusinessMoveAudit businessMoveAudit) {
		Integer sourceAccountId = businessMoveEvent.getSourceEnterpriseId() != null
				? businessMoveEvent.getSourceEnterpriseId()
				: businessMoveEvent.getSourceBusinessId();
		Integer targetAccountId = businessMoveEvent.getTargetEnterpriseId();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactByBusinessId(businessMoveEvent.getSourceBusinessId());
		BusinessMigrationBatchDto businessMigrationBatchDto = new BusinessMigrationBatchDto();
		businessMigrationBatchDto.setBusinessMoveEvent(businessMoveEvent);
		businessMigrationBatchDto.setBusinessMoveAudit(businessMoveAudit);
		Integer batchSize = Integer.valueOf(CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class).getProperty("business_move_batch_size",
						"10"));
		if (CollectionUtils.isNotEmpty(messengerContacts)) {
			log.info("sourceAccountId: {}, targetAccountId: {}, Messenger contacts size:{}", sourceAccountId,
					targetAccountId, messengerContacts.size());
			List<List<MessengerContact>> messengerContactsOuterList = ListUtils.partition(messengerContacts, batchSize);
			for (List<MessengerContact> messengerContactSubList : messengerContactsOuterList) {
				businessMigrationBatchDto.setMessengerContactId(
						messengerContactSubList.stream().map(MessengerContact::getId).collect(Collectors.toList()));
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.BUSINESS_MIGRATION_BATCH, sourceAccountId,
						businessMigrationBatchDto);
			}
		}
	}

	private void businessMoveEventData(BusinessMigrationBatchDto event) {
		BusinessMoveAuditEnum status = BusinessMoveAuditEnum.SUCCESS;
		Integer sourceAccountId = event.getBusinessMoveEvent().getSourceEnterpriseId() != null
				? event.getBusinessMoveEvent().getSourceEnterpriseId()
				: event.getBusinessMoveEvent().getSourceBusinessId();
		Integer targetAccountId = event.getBusinessMoveEvent().getTargetEnterpriseId();
		String description = null;
		List<Integer> customerIds = new ArrayList<>();
		List<Integer> messengerContactIdList = new ArrayList<>();
		List<MessengerContact> messengerContacts = messengerContactService
				.findMessengerContactsByMcIds(event.getMessengerContactId());
		List<ContactDocument> contactFromES = getContactDocumentfromEs(event.getMessengerContactId(), sourceAccountId);
		Map<Integer, ContactDocument> contactDocumentMap = contactFromES.stream()
				.collect(Collectors.toMap(ContactDocument::getM_c_id, mc -> mc));

		List<MessageDocument> messageDocuments = getMessageDocuments(event.getMessengerContactId(), sourceAccountId);

		// Loop on CD
		if (CollectionUtils.isNotEmpty(contactFromES)) {
			List<ContactDocument> contactDocumentBulkRequest = new ArrayList<>();
			for (MessengerContact mc : messengerContacts) {
				customerIds.add(mc.getCustomerId());
				messengerContactIdList.add(mc.getId());
				ContactDocument cd = contactDocumentMap.get(mc.getId());
				if (cd != null) {
					try {
						ContactDocument contactDocument = updateContactDocument(cd, targetAccountId,
								event.getBusinessMoveEvent().getTargetBusinessNumber(), mc,
								event.getBusinessMoveAudit().getEventType());
						if (contactDocument != null) {
							contactDocumentBulkRequest.add(contactDocument);
						}
					} catch (Exception e) {
						log.error("Exception while updating contact doc mc_id :{}, exception :{}", mc.getId(), e);
						status = BusinessMoveAuditEnum.FAILED;
						description = "Error while upserting contact doc!!";
					}
				}
			}
			if (CollectionUtils.isNotEmpty(contactDocumentBulkRequest)) {
				Integer routingId = targetAccountId;
				BulkUpsertPayload<ContactDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
						contactDocumentBulkRequest,
						routingId, routingId, Constants.Elastic.CONTACT_INDEX);
				try {
					elasticSearchService.bulkUpsert(bulkUpsertPayload);
				} catch (Exception e) {
					log.error("updateContactDocumentOnEs : Exception while updating : {}", e);
					status = BusinessMoveAuditEnum.FAILED;
					description = "Error while upserting contact doc!!";
				}
			}
		}

		// Loop on MD
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			List<MessageDocument> messageDocumentBulkRequest = new ArrayList<>();
			for (MessageDocument messageDocument : messageDocuments) {
				messageDocument.setE_id(targetAccountId);
				messageDocumentBulkRequest.add(messageDocument);
			}
			if (CollectionUtils.isNotEmpty(messageDocumentBulkRequest)) {
				log.info("MessageDocument BulkRequest size: {}", messageDocumentBulkRequest.size());
				// Partitioning All MeesageDocuments in List of 100 documents for Bulk
				// processing
				List<List<MessageDocument>> messageDocumentBulkRequestOuterList = ListUtils
						.partition(messageDocumentBulkRequest, 100);
				for (List<MessageDocument> messageDocumentBulkRequestInnerList : messageDocumentBulkRequestOuterList) {
					Integer routingId = targetAccountId;
					BulkUpsertPayload<MessageDocument> bulkUpsertPayload = new BulkUpsertPayload<>(
							messageDocumentBulkRequestInnerList,
							routingId, routingId, Constants.Elastic.MESSAGE_INDEX);
					try {
						elasticSearchService.bulkUpsert(bulkUpsertPayload);
					} catch (Exception e) {
						log.error("updateMessageDocumentOnEs : Exception while updating : {}", e);
						status = BusinessMoveAuditEnum.FAILED;
						description = "Error while upserting messenger doc!!";
					}
				}
			}
		}
		try {
			if (CollectionUtils.isNotEmpty(messengerContactIdList)) {
				log.info("updating messenger messages");
				messengerMessageService.updateMessengerMessagesInBusinessUpgradeOrDowngrade(targetAccountId,
						sourceAccountId, messengerContactIdList);
			}

		} catch (Exception e) {
			log.error("error occurred while updating messenger messages : {}", e.getMessage());
			status = BusinessMoveAuditEnum.FAILED;
		}

		updateBusinessMoveAudit(event.getBusinessMoveAudit(), status, description, event.getBusinessMoveEvent());
		if (status.equals(BusinessMoveAuditEnum.SUCCESS)) {
			MessagesDeleteRequest messagesDeleteRequest = new MessagesDeleteRequest(
					event.getBusinessMoveEvent().getEventId(), event.getBusinessMoveAudit().getId(), sourceAccountId,
					event.getBusinessMoveEvent().getSourceBusinessId(), messengerContactIdList);
			publishDeleteEventToKafka(messagesDeleteRequest);
		}
	}

	@Override
	@Transactional
	public void migrateLeadSources(Integer mcId) {
		MessengerContact mc = messengerContactService.findById(mcId);
		if (Objects.nonNull(mc) && Objects.isNull(mc.getContactState())) {
			CustomerDTO customerDTO = contactService.findByIdNoCaching(mc.getCustomerId());
			if (Objects.nonNull(customerDTO) && !LeadSource.REFERRAL.equals(customerDTO.getLeadSource())) {
				BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());
				if (Objects.nonNull(businessDTO)) {
					ContactDocument doc = new ContactDocument();
					doc.setLead(customerDTO.isLead());
					doc.setContactState(customerDTO.getContactState());
					doc.setLeadOrigin(customerDTO.getLeadSource());
					ESRequest.Upsert<ContactDocument> updateDoc = new ESRequest.Upsert<>(doc, mc.getId().toString());
					// updateDoc.setDocAsUpsert(false); // only update no create if doc is not
					// present
					ESRequest esRequest = new ESRequest.Builder(new ESRequest())
							.addIndex(Constants.Elastic.CONTACT_INDEX)
							.addRoutingId(businessDTO.getRoutingId())
							.addPayloadForUpsert(updateDoc).build();
					boolean successs = elasticSearchService.updateDocument(esRequest, false);
					if (successs) {
						mc.setLead(doc.getLead());
						mc.setLeadSource(doc.getLeadOrigin());
						mc.setContactState(doc.getContactState());
						mc.setUpdatedAt(new Date());
						messengerContactService.saveOrUpdateMessengerContact(mc);
					}
				}
			}
		}
	}

	@Override
	public void migrateLeadSourcesFromDate(Date start) {
		List<Integer> mcIds = messengerContactService.getByDateWhereContactStateIsNull(start);
		mcIds.forEach(mcId -> {
			User u = new User();
			u.setId(mcId);
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.MIGRATE_LEAD_DATA, u);
		});
	}

	/**
	 * This migration is only for messages from human sources. (auto replies, robin
	 * replies and voice call is excluded)
	 * Some important stuffs to keep in mind :
	 * CLOSED activity has source 0 : hence cannot filter out source 0 in query.
	 * Needs to be done here in code
	 * Robin has userId as -10, Voice call has a messageType CHAT, msg_type
	 * SMS_RECEIVE, u_id -2 : Query has filtered out -10 and -2 both
	 * Exception here as many msg_type SMS_RECEIVE have u_id as 0 or -2 (voice call)
	 * which usually should not be there hence cannot filter out 0 in query
	 * Few SMS_SEND type don't have a u_id and few don't have a source : Filter out
	 * both of these
	 * Campaign message have source 0 and u_id 0 : All SMS_SEND messages with source
	 * 0 filtered in code
	 * Missed Auto-replies have a source 5 and u_id 0 : already filtered out in
	 * query
	 * 
	 * @param contact
	 */
	@Override
	public void migrateResponseTime(MessengerContact contact) {
		// get all the messages for contact except notes, activities except closed
		// activity and auto-replies
		MessengerContact mc = messengerContactService.findById(contact.getId());
		ConvStateForResTimeCalc initialState = ConvStateForResTimeCalc.getInitialState();
		mc.setCurrentStateForResTimeCalc(initialState); // set initial state assuming no messages present, change it
														// later if messages found.
		BusinessDTO businessDTO = null;
		try {
			businessDTO = businessService.getBusinessDTO(mc.getBusinessId(), 1);
		} catch (Exception e) {
			log.error("migrateResponseTime: Invalid businessId {} for mcId {}", mc.getBusinessId(), mc.getId());
			log.error("migrateResponseTime: ", e);
		}
		if (Objects.isNull(businessDTO)) {
			log.info("migrateResponseTime: No business found for mcId {} businessId {}", mc.getId(),
					mc.getBusinessId());
			return;
		}

		Map<String, Object> templateData = new HashMap<>();
		templateData.put("businessId", businessDTO.getBusinessId());
		templateData.put("mcId", mc.getId());
		Map<String, Object> data = new HashMap<>();
		data.put("data", templateData);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addSize(10000)
				.addRoutingId(businessDTO.getAccountId())
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_FOR_RES_TIME_MIGRATION, data)
				.addIndex(Constants.Elastic.MESSAGE_INDEX).build();

		List<MessageDocument> msgDocs = elasticSearchService.searchByQuery(esRequest, MessageDocument.class);

		if (CollectionUtils.isNotEmpty(msgDocs)) {
			msgDocs = filterDocuments(msgDocs);
			if (CollectionUtils.isNotEmpty(msgDocs)) {
				List<List<MessageDocument>> instances = new ArrayList<>();
				int start = 0;
				for (int i = 0; i < msgDocs.size(); i++) {
					MessageDocument doc = msgDocs.get(i);
					if (Objects.nonNull(doc.getMessageType()) && MessageType.ACTIVITY.equals(doc.getMessageType())
							&& ActivityType.CLOSED.equals(doc.getActivityType())) {
						List<MessageDocument> instance = msgDocs.subList(start, i);
						instances.add(instance);
						start = i + 1;
					}
				}
				if (start < msgDocs.size()) {
					List<MessageDocument> instance = msgDocs.subList(start, msgDocs.size());
					instances.add(instance);
				}
				boolean firstResponse = false;
				for (List<MessageDocument> instance : instances) {
					firstResponse = true;
					for (int i = 1; i < instance.size(); i++) {
						MessageDocument d1 = instance.get(i - 1);
						MessageDocument d2 = instance.get(i);
						String d1MsgType = d1.getMsg_type();
						String d2MsgType = d2.getMsg_type();
						if (("SMS_RECEIVE".equals(d1MsgType) || "MMS_RECEIVE".equals(d1MsgType)
								|| "FACEBOOK_RECEIVE".equals(d1MsgType) ||
								"LIVECHAT_RECEIVE".equals(d1MsgType) || "LIVECHAT_MEDIA_RECEIVE".equals(d1MsgType)
								|| "EMAIL_RECEIVE".equals(d1MsgType))
								&& ("SMS_SEND".equals(d2MsgType) || "MMS_SEND".equals(d2MsgType)
										|| "FACEBOOK_SEND".equals(d2MsgType) ||
										"LIVECHAT_SEND".equals(d2MsgType) || "LIVECHAT_MEDIA_SEND".equals(d2MsgType)
										|| "EMAIL_SEND".equals(d2MsgType))) {
							ResponseTimeOutBox responseTimeOutBox = new ResponseTimeOutBox();
							responseTimeOutBox.setBusinessId(businessDTO.getBusinessId());
							responseTimeOutBox.setFirstResponse(firstResponse);
							responseTimeOutBox.setMcId(mc.getId());
							responseTimeOutBox.setMsgId((String) d2.getId());
							responseTimeOutBox.setReceivedMsgId((String) d1.getId());

							DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
							Date d1CrDate;
							Date d2CrDate;
							try {
								d1CrDate = df.parse(d1.getCr_date());
								d2CrDate = df.parse(d2.getCr_date());
							} catch (ParseException e) {
								log.error("migrateResponseTime: failed to parse cr_date. Skipped Document Ids {} {}",
										d1.getId(), d2.getId());
								continue;
							}
							responseTimeOutBox.setReceiveTimeEpoch(
									Objects.isNull(d1.getCr_time()) ? d1CrDate.getTime() : d1.getCr_time());
							responseTimeOutBox.setSentTimeEpoch(
									Objects.isNull(d2.getCr_time()) ? d2CrDate.getTime() : d2.getCr_time());
							responseTimeOutBox.setSource(d2.getSource());
							responseTimeOutBox.setStatus("PENDING");
							responseTimeOutBoxRepository.save(responseTimeOutBox);
							kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESPONSE_TIME_CALC, responseTimeOutBox);
							firstResponse = false;
						}
					}
				}
				MessageDocument lastDoc = msgDocs.get(msgDocs.size() - 1);
				if (!(MessageType.ACTIVITY.equals(lastDoc.getMessageType())
						&& ActivityType.CLOSED.equals(lastDoc.getActivityType()))) {
					initialState.setRtmLastMsgId(lastDoc.getM_id());
					if ("SMS_RECEIVE".equals(lastDoc.getMsg_type()) || "MMS_RECEIVE".equals(lastDoc.getMsg_type())
							|| "FACEBOOK_RECEIVE".equals(lastDoc.getMsg_type()) ||
							"LIVECHAT_RECEIVE".equals(lastDoc.getMsg_type())
							|| "LIVECHAT_MEDIA_RECEIVE".equals(lastDoc.getMsg_type())
							|| "EMAIL_RECEIVE".equals(lastDoc.getMsg_type())) {
						initialState.setRtmLastMsgType("R");
					} else {
						initialState.setRtmLastMsgType("S");
					}
					initialState.setRtmLastMsgEpoch(lastDoc.getCr_time());
					initialState.setTagAsFirstResponse(firstResponse);
					mc.setCurrentStateForResTimeCalc(initialState);
				}
			}
		}

		mc.setRtmPauseTagging(false);
		messengerContactService.saveOrUpdateMessengerContact(mc);

	}

	private static List<MessageDocument> filterDocuments(List<MessageDocument> msgDocs) {
		msgDocs = msgDocs.stream()
				.filter(msg -> !(Source.CAMPAIGN.getSourceId().equals(msg.getSource())
						&& "SMS_SEND".equals(msg.getMsg_type()))) // code side filtering as CLOSED activity also has
																	// source 0
				.filter(msg -> !(Objects.isNull(msg.getSource()) && "SMS_SEND".equals(msg.getMsg_type()))) // sent
																											// messages
																											// must have
																											// a source
				.filter(msg -> !("SMS_SEND".equals(msg.getMsg_type())
						&& (Objects.isNull(msg.getU_id()) || Integer.valueOf(0).equals(msg.getU_id())))) // sent messages
																										// must have a
																										// userId
				.collect(Collectors.toList());
		return msgDocs;
	}

	@Override
	public void initiateResponseTimeMigration(ResponseTimeMigrationDto responseTimeMigrationDto) {
		if (!"lastMsgOn".equals(responseTimeMigrationDto.getType())
				&& !"createdOn".equals(responseTimeMigrationDto.getType()))
			throw new BadRequestException("type can only be lastMsgOn or createdOn");
		if (Objects.isNull(responseTimeMigrationDto.getStartTime()))
			throw new BadRequestException("start time must be present");
		Date startDate = new Date(responseTimeMigrationDto.getStartTime());
		Date endDate = Objects.isNull(responseTimeMigrationDto.getEndTime()) ? new Date()
				: new Date(responseTimeMigrationDto.getEndTime());

		List<Integer> mcIds = null;
		List<Integer> businessIds = responseTimeMigrationDto.getBusinessIds();

		if ("lastMsgOn".equals(responseTimeMigrationDto.getType())) {
			if (CollectionUtils.isNotEmpty(businessIds)) {
				mcIds = messengerContactRepository.findMcForResponseTimeMigrationBasedOnLastActivityDate(businessIds,
						startDate, endDate);
			} else {
				mcIds = messengerContactRepository.findMcForResponseTimeMigrationBasedOnLastActivityDate(startDate,
						endDate);
			}
		} else {
			if (CollectionUtils.isNotEmpty(businessIds)) {
				mcIds = messengerContactRepository.findContactsForMigrationCreatedBetweenForBusiness(businessIds,
						startDate, endDate);
			} else {
				mcIds = messengerContactRepository.findContactsForMigrationCreatedBetween(startDate, endDate);
			}
		}

		if (CollectionUtils.isNotEmpty(mcIds)) {
			log.info("initiateResponseTimeMigration: found {} contacts for migration {}", mcIds.size());
			mcIds.forEach(id -> {
				MessengerContact mc = new MessengerContact();
				mc.setId(id);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESPONSE_TIME_MIGRATION, mc);
			});
		} else {
			throw new BadRequestException("initiateResponseTimeMigration: No mcIds found for given input");
		}
	}

	public static void main(String[] args) {
		MessageDocument d1 = new MessageDocument();
		List<MessageDocument> docs = new ArrayList<>();

		d1.setMsg_type("SMS_RECEIVE");
		d1.setSource(1);
		d1.setMessageType(MessageType.CHAT);

		docs.add(d1);

		MessageDocument d2 = new MessageDocument();
		d2.setMsg_type("SMS_RECEIVE");
		d2.setU_id(0);

		docs.add(d2);

		// invalid u_id
		MessageDocument d3 = new MessageDocument();
		d3.setMsg_type("SMS_SEND");
		d3.setU_id(0);
		d3.setSource(10);

		docs.add(d3);

		// invalid u_id
		MessageDocument d31 = new MessageDocument();
		d31.setMsg_type("SMS_SEND");
		d31.setU_id(null);
		d31.setSource(10);

		docs.add(d31);

		// valid send message
		MessageDocument d4 = new MessageDocument();
		d4.setMsg_type("SMS_SEND");
		d4.setU_id(2);
		d4.setSource(1);

		docs.add(d4);

		// invalid source
		MessageDocument d5 = new MessageDocument();
		d5.setMsg_type("SMS_SEND");
		d5.setU_id(2);
		d5.setSource(null);

		docs.add(d5);

		// closed activity case
		MessageDocument d6 = new MessageDocument();
		d6.setMessageType(MessageType.ACTIVITY);
		d6.setSource(0);

		docs.add(d6);

		// campaign case
		MessageDocument d7 = new MessageDocument();
		d7.setMsg_type("SMS_SEND");
		d7.setSource(0);

		docs.add(d7);

		// invalid source and u_id
		MessageDocument d8 = new MessageDocument();
		d8.setMsg_type("SMS_SEND");
		d8.setSource(null);
		d8.setU_id(null);

		docs.add(d8);

		docs = filterDocuments(docs);
		System.out.println(docs);

		String name = "Piyush   Ravi";
		System.out.println(name.split("\\s+")[0]);

	}

	@Override
	public void publishEventToMergeDuplicateContacts(Integer businessId, Integer customerId, Integer pageSize) {
		log.info("request received to publish event to merge duplicate contacts for businessId: {} ,customerId: {}");
		List<Integer> customerIds = new ArrayList<Integer>();
		List<Object[]> objects = new ArrayList<Object[]>();
		Integer prevBusinessId = null;
		if (businessId != null) {
			objects = messengerContactRepository.findDuplicateContactsByLocation(businessId);
		} else if (customerId != null) {
			objects = messengerContactRepository.findDuplicateContactsByCustomer(customerId);
		} else {
			int pageNo = 0;
			Pageable pageable = PageRequest.of(pageNo, pageSize);
			Page<Object[]> pagedResult = messengerContactRepository.findDuplicateContacts(pageable);
			while (pagedResult.hasContent()) {
				objects.addAll(pagedResult.getContent());
				pageNo++;
				pageable = PageRequest.of(pageNo, pageSize);
				pagedResult = messengerContactRepository.findDuplicateContacts(pageable);
			}
		}
		if (CollectionUtils.isNotEmpty(objects)) {
			prevBusinessId = (Integer) objects.get(0)[1];
			for (Object[] object : objects) {
				Integer bizId = (Integer) object[1];
				if (!prevBusinessId.equals(bizId)) {
					pushMergeDuplicateContactsEventInKafka(customerIds, prevBusinessId);
					customerIds.clear();
				}
				customerIds.add((Integer) object[0]);
				prevBusinessId = (Integer) object[1];
			}
			if (CollectionUtils.isNotEmpty(customerIds)) {
				pushMergeDuplicateContactsEventInKafka(customerIds, prevBusinessId);
			}
		}
	}

	// 1.Get primarycontact
	// 2.Update primaryContact mcid in all secondary message DB
	// 3.Add reviewerId in primarycontact DB
	// 4.Delete activites from secondary contacts messages DB
	// 5.Delete secondary contacts DB
	// 6.Add reviews and survey in primary contact ES
	// 7.Delete activites from secondary contacts messages ES
	// 8.Delete secondary contacts ES
	// 9.Update primaryContact mcid in all secondary message ES
	// 10.update Last message in contact
	@Override
	@Transactional
	public void mergeDuplicateContacts(MergeDuplicateContactsEvent event) {
		List<Integer> customerIds = event.getCustomerIds();

		log.info("Request received to merge duplicate contact's: {}", event);
		if (CollectionUtils.isNotEmpty(event.getCustomerIds())) {
			removeCustomerIdsMappedToMoreThanOneLocation(event);
			if (CollectionUtils.isEmpty(event.getCustomerIds())) {
				log.info("All customerIds in request mapped to more than one location hence reurning:", customerIds);
				return;
			}
			Map<Integer, List<MessengerContact>> customerIdMessengerContactsMap = getCustomerIdMessengerContactsMap(
					event);
			removeCustomerWithMoreThanOneNonCampaignConv(customerIdMessengerContactsMap);
			Map<Integer, List<ContactDocument>> customerIdContactDocumentMap = getCustomerIdContactDocumentMap(event);
			if (MapUtils.isNotEmpty(customerIdMessengerContactsMap)
					&& MapUtils.isNotEmpty(customerIdContactDocumentMap)) {
				BusinessDTO businessDTO = businessService.getBusinessLiteDTO(event.getBusinessId());
				customerIdMessengerContactsMap.forEach((customerId, messengerContacts) -> {
					try {
						List<ContactDocument> contactDocuments = customerIdContactDocumentMap.get(customerId);
						List<Integer> mcids = contactDocuments.stream().map(cd -> cd.getM_c_id())
								.collect(Collectors.toList());
						MessengerContact primaryMessengerContact = getPrimaryContact(messengerContacts, mcids);
						if (primaryMessengerContact == null) {
							log.info("Contact exist in db but not in ES for customer: {}", customerId);
							return;
						}
						List<Integer> secondaryContactIds = getSencondaryContactIds(primaryMessengerContact.getId(),
								messengerContacts);
						deleteConversationActivities(secondaryContactIds);
						updatePrimaryContactMcidInDB(primaryMessengerContact.getId(), secondaryContactIds);
						messengerContactRepository.deleteAllByIds(secondaryContactIds);
						if (CollectionUtils.isNotEmpty(contactDocuments) && contactDocuments.size() > 1) {
							addReviewsAndSurveyResponsesToPrimaryContact(contactDocuments, customerId,
									primaryMessengerContact);
							updateLastMessageInPrimaryContact(contactDocuments, primaryMessengerContact,
									messengerContacts);
							deleteSecondaryConversationsAndActivities(secondaryContactIds, businessDTO.getRoutingId());
							updateMcidInES(primaryMessengerContact.getId(), secondaryContactIds);
						}
					} catch (Exception e) {
						log.error("Error in mergeing records for customer {}", customerId);
					}
				});
			}
		}
	}

	private void removeCustomerWithMoreThanOneNonCampaignConv(
			Map<Integer, List<MessengerContact>> customerIdMessengerContactsMap) {
		if (MapUtils.isNotEmpty(customerIdMessengerContactsMap)) {
			customerIdMessengerContactsMap.forEach((customerId, messengerContacts) -> {
				boolean flag = isCustomerWithMoreThanOneNonCampaignConv(messengerContacts);
				if (flag) {
					customerIdMessengerContactsMap.remove(customerId);
				}
			});
		}
	}

	private boolean isCustomerWithMoreThanOneNonCampaignConv(List<MessengerContact> messengerContacts) {
		boolean flag = false;
		int count = 0;
		if (CollectionUtils.isNotEmpty(messengerContacts)) {
			for (MessengerContact messengerContact : messengerContacts) {
				if (messengerContact.getTag() != null
						&& !messengerContact.getTag().equals(MessageTag.CAMPAIGN.getCode())) {
					count++;
				}
			}
			if (count > 1)
				flag = true;
		}
		return flag;
	}

	Map<Integer, List<ContactDocument>> customerIdContactDocumentMap = new HashMap<Integer, List<ContactDocument>>();

	private Map<Integer, List<ContactDocument>> getCustomerIdContactDocumentMap(MergeDuplicateContactsEvent event) {
		List<ContactDocument> contactDocs = getContactDocsFromES(event.getCustomerIds());
		log.info("Merge contatcs | Contact's data fetched from ES");
		if (CollectionUtils.isNotEmpty(contactDocs)) {
			contactDocs.forEach(cd -> {
				if (!customerIdContactDocumentMap.containsKey(cd.getC_id())) {
					customerIdContactDocumentMap.put(cd.getC_id(), new ArrayList<ContactDocument>());
				}
				customerIdContactDocumentMap.get(cd.getC_id()).add(cd);
			});
		}
		return customerIdContactDocumentMap;
	}

	private Map<Integer, List<MessengerContact>> getCustomerIdMessengerContactsMap(MergeDuplicateContactsEvent event) {
		List<MessengerContact> contacts = messengerContactRepository.findContactsByCustomerIds(event.getCustomerIds());
		Map<Integer, List<MessengerContact>> customerIdMessengerContactsMap = new ConcurrentHashMap<Integer, List<MessengerContact>>();
		if (CollectionUtils.isNotEmpty(contacts)) {
			contacts.forEach(mc -> {
				if (!customerIdMessengerContactsMap.containsKey(mc.getCustomerId())) {
					customerIdMessengerContactsMap.put(mc.getCustomerId(), new ArrayList<MessengerContact>());
				}
				customerIdMessengerContactsMap.get(mc.getCustomerId()).add(mc);
			});
		}
		return customerIdMessengerContactsMap;
	}

	private void removeCustomerIdsMappedToMoreThanOneLocation(MergeDuplicateContactsEvent event) {
		List<Integer> cids = messengerContactRepository.findDuplicateContactsByCustomerIds(event.getCustomerIds(),
				event.getBusinessId());
		if (CollectionUtils.isNotEmpty(cids)) {
			event.getCustomerIds().removeAll(cids);
		}
	}

	private void updateLastMessageInPrimaryContact(List<ContactDocument> contactDocuments,
			MessengerContact primaryMessengerContact, List<MessengerContact> messengerContacts) {
		String recentMessage = "";
		Integer encrypted = 0;
		MessageDocument messageDocument = getLatestMessagesFromContacts(messengerContacts);
		if (messageDocument != null && messageDocument.getC_id() != null
				&& !messageDocument.getC_id().equals(primaryMessengerContact.getId().toString())) {
			MessengerContact secondaryMessengerContact = messengerContacts.stream()
					.filter(contact -> messageDocument.getC_id().equals(contact.getId().toString())).findFirst()
					.orElse(null);
			ContactDocument primaryConv = contactDocuments.stream()
					.filter(conv -> (conv.getM_c_id().equals(primaryMessengerContact.getId()))).findFirst()
					.orElse(null);
			ContactDocument secondaryConv = contactDocuments.stream()
					.filter(conv -> (messageDocument.getC_id().equals(conv.getM_c_id().toString()))).findFirst()
					.orElse(null);
			if (primaryConv != null && secondaryConv != null && secondaryMessengerContact != null
					&& primaryMessengerContact.getTag() != null
					&& primaryMessengerContact.getTag() != MessageTag.CAMPAIGN.getCode()) {
				if (messageDocument.getIs_encrypt() != null && messageDocument.getIs_encrypt() == 1) {
					try {
						recentMessage = EncryptionUtil.decrypt(messageDocument.getMsg_body(),
								StringUtils.join(secondaryConv.getB_num(), secondaryConv.getM_c_id()),
								StringUtils.join(secondaryConv.getM_c_id(), secondaryConv.getB_num()), false);
						if (StringUtils.isNotBlank(recentMessage)) {
							recentMessage = EncryptionUtil.encrypt(recentMessage,
									StringUtils.join(primaryConv.getB_num(), primaryConv.getM_c_id()),
									StringUtils.join(primaryConv.getM_c_id(), primaryConv.getB_num()));
						}
						if (StringUtils.isBlank(recentMessage) && StringUtils.isNotBlank(messageDocument.getFrom())
								&& StringUtils.isNotBlank(messageDocument.getTo())) {
							recentMessage = EncryptionUtil.decrypt(messageDocument.getMsg_body(),
									StringUtils.join(messageDocument.getFrom(), messageDocument.getTo()),
									StringUtils.join(messageDocument.getTo(), messageDocument.getFrom()), false);
							if (StringUtils.isNotBlank(recentMessage)) {
								recentMessage = EncryptionUtil.encrypt(recentMessage,
										StringUtils.join(primaryConv.getB_num(), primaryConv.getC_phone()),
										StringUtils.join(primaryConv.getC_phone(), primaryConv.getB_num()));
							}
						}
						if (StringUtils.isBlank(recentMessage)) {
							recentMessage = messageDocument.getMsg_body();
						}
						encrypted = 1;
					} catch (Exception e) {
					}

				} else {
					recentMessage = messageDocument.getMsg_body();
				}
			}
			primaryMessengerContact.setLastMessage(recentMessage);
			primaryMessengerContact.setEncrypted(encrypted);
			messengerContactRepository.save(primaryMessengerContact);
			primaryConv.setL_msg(recentMessage);
			primaryConv.setIs_encrypted(encrypted);
			ESRequest.Upsert<ContactDocument> upsert = new ESRequest.Upsert<>(primaryConv,
					primaryConv.getM_c_id().toString());
			ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
					.addRoutingId(primaryConv.getE_id())
					.addPayloadForUpsert(upsert).build();
			boolean updated = elasticSearchService.updateDocument(esRequest, false);
			log.info("Last messages is updated for Messenger Contact Id {} is  {} ", primaryMessengerContact.getId(),
					updated);
		}
	}

	private MessageDocument getLatestMessagesFromContacts(List<MessengerContact> messengerContacts) {
		MessageDocument messageDocument = null;
		Map<String, Object> messageFilter = new HashMap<>();
		List<Integer> contactIds = messengerContacts.stream().map(contact -> contact.getId())
				.collect(Collectors.toList());
		messageFilter.put("mcids", ControllerUtil.toCommaSeparatedString(contactIds));
		Map<String, Object> data = new HashMap<>();
		data.put("messageFilter", messageFilter);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGES_BY_MCIDS, data).build();
		ElasticData messageData = elasticSearchService.getDataFromElastic(esRequest,
				MessageDocument.class);
		if (!messageData.isSucceeded()) {
			log.error("mergeDuplicateContacts: Failed to fetch data from ES");
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		if (CollectionUtils.isNotEmpty(messageData.getResults())) {
			messageDocument = (MessageDocument) messageData.getResults().get(0);
		}
		return messageDocument;
	}

	private List<ContactDocument> getContactDocsFromES(List<Integer> cids) {
		int size = 1000;
		int i = 0;
		List<ContactDocument> result = new ArrayList<ContactDocument>();
		for (; i < cids.size();) {
			log.info("Fetching data from ES for cids {}", cids);
			if (i + size > cids.size()) {
				break;
			}
			List<ContactDocument> contactDocs = getContactDocumentFromES(cids.subList(i, i + size));
			if (CollectionUtils.isNotEmpty(contactDocs)) {
				result.addAll(contactDocs);
			}
			i = i + size;
		}
		if (i < cids.size()) {
			List<ContactDocument> contactDocs1 = getContactDocumentFromES(cids.subList(i, cids.size()));
			log.info("Fetching remaining data from ES for cids {}", cids);
			if (CollectionUtils.isNotEmpty(contactDocs1)) {
				result.addAll(contactDocs1);
			}
		}
		return result;
	}

	private List<ContactDocument> getContactDocumentFromES(List<Integer> cids) {
		Map<String, Object> data = new HashMap<>();
		data.put("cids", ControllerUtil.toCommaSeparatedString(cids));
		data.put("count", cids.size() * 8);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_CONTACT_BY_CUSTOMER_IDS, data).build();
		ElasticData conversationData = elasticSearchService.getDataFromElastic(esRequest,
				ContactDocument.class);
		if (!conversationData.isSucceeded()) {
			log.error("mergeDuplicateContacts: Failed to fetch data from ES");
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return (List<ContactDocument>) conversationData.getResults();
	}

	private void deleteSecondaryConversationsAndActivities(List<Integer> secondaryContactIds, Integer accountId) {
		if (CollectionUtils.isNotEmpty(secondaryContactIds)) {
			messageService.deleteActivitiesByConversationIds(secondaryContactIds, accountId);
			conversationService.deleteConversations(secondaryContactIds, accountId);
		}
	}

	private void addReviewsAndSurveyResponsesToPrimaryContact(List<ContactDocument> contactDocuments,
			Integer customerId,
			MessengerContact primaryMessengerContact) {
		if (CollectionUtils.isNotEmpty(contactDocuments)) {
			Optional<ContactDocument> primaryConvOpt = contactDocuments.stream()
					.filter(cd -> cd.getM_c_id().equals(primaryMessengerContact.getId())).findFirst();
			if (primaryConvOpt.isPresent()) {
				ContactDocument primaryConv = primaryConvOpt.get();
				contactDocuments.stream().filter(cd -> !cd.getM_c_id().equals(primaryMessengerContact.getId()))
						.forEach(doc -> {
							if (CollectionUtils.isNotEmpty(doc.getReviews())) {
								if (CollectionUtils.isEmpty(primaryConv.getReviews())) {
									primaryConv.setReviews(new ArrayList<ContactDocument.Review>());
								}
								primaryConv.getReviews().addAll(doc.getReviews());
							}
							if (CollectionUtils.isNotEmpty(doc.getSurveyResponses())) {
								if (CollectionUtils.isEmpty(primaryConv.getSurveyResponses())) {
									primaryConv.setSurveyResponses(new ArrayList<ContactDocument.SurveyResponse>());
								}
								primaryConv.getSurveyResponses().addAll(doc.getSurveyResponses());
							}
						});
				// update last review on
				if (CollectionUtils.isNotEmpty(primaryConv.getReviews())) {
					Optional<Review> reviewOpt = primaryConv.getReviews().stream()
							.sorted(Comparator.comparingLong(Review::getRdate).reversed()).findFirst();
					if (reviewOpt.isPresent()) {
						primaryConv.setL_review_on(reviewOpt.get().getRdate());
					}
				}
				// update last survey on
				if (CollectionUtils.isNotEmpty(primaryConv.getSurveyResponses())) {
					Optional<ContactDocument.SurveyResponse> surveyResponseOptional = primaryConv.getSurveyResponses()
							.stream()
							.sorted(Comparator.comparingLong(ContactDocument.SurveyResponse::getRespDate).reversed())
							.findFirst();
					if (surveyResponseOptional.isPresent()) {
						primaryConv.setL_survey_rsp_on(surveyResponseOptional.get().getRespDate());
					}
				}
				ESRequest.Upsert<ContactDocument> upsert = new ESRequest.Upsert<>(primaryConvOpt.get(),
						primaryMessengerContact.getId().toString());
				ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
						.addRoutingId(primaryConvOpt.get().getE_id())
						.addPayloadForUpsert(upsert).build();
				boolean updated = elasticSearchService.updateDocument(esRequest, false);
				log.info("Reviews/Surveys is added for Messenger Contact Id {} is  {} ",
						primaryMessengerContact.getId(), updated);
			}
		}
	}

	private void updatePrimaryContactMcidInDB(Integer primaryMessengerContactId, List<Integer> secondaryContactIds) {
		messengerMessageRepository.updateMessengerContactId(primaryMessengerContactId, secondaryContactIds);
		facebookMessageRepository.updateMessengerContactId(primaryMessengerContactId, secondaryContactIds);
	}

	private List<Integer> getSencondaryContactIds(Integer primaryMessengerContactId,
			List<MessengerContact> messengerContacts) {
		return messengerContacts.stream().filter(mc -> (!mc.getId().equals(primaryMessengerContactId)))
				.map(mc -> mc.getId()).collect(Collectors.toList());
	}

	private MessengerContact getPrimaryContact(List<MessengerContact> messengerContacts, List<Integer> mcids) {
		MessengerContact contact = messengerContacts.stream()
				.filter(mc -> (mc.getTag().equals(MessageTag.INBOX.getCode())
						|| mc.getTag().equals(MessageTag.UNREAD.getCode())
						|| mc.getTag().equals(MessageTag.ARCHIVED.getCode())) && mcids.contains(mc.getId()))
				.findFirst().orElse(null);
		if (contact == null) {
			contact = messengerContacts.stream().filter(mc -> mcids.contains(mc.getId())).findFirst().orElse(null);
		}
		return contact;
	}

	private void updateMcidInES(Integer primaryContactId, List<Integer> secondaryContactIds) {
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("mcids", ControllerUtil.toCommaSeparatedString(secondaryContactIds));
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> params = new HashMap<>();
		data.put("messageFilter", messageFilter);
		ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_BY_MCIDS)
				.freeMarkerDataModel(data);
		Map<String, Object> scriptData = new HashMap<>();
		params.put("mcid", primaryContactId);
		scriptData.put("inline", "ctx._source.c_id=params['mcid']");
		scriptData.put("params", params);
		builder.scriptParam(scriptData);

		boolean updateByQueryResponse = elasticSearchService.updateByQuery(builder.build());
		if (!updateByQueryResponse) {
			log.error("error in updating mcid {} to messages document for cids:{}", primaryContactId,
					secondaryContactIds);
			return;

		}
		log.info("Mcid {} updation success for cids:{}", primaryContactId, secondaryContactIds);
	}

	public void deleteConversationActivities(List<Integer> secondaryContactIds) {
		if (CollectionUtils.isNotEmpty(secondaryContactIds)) {
			List<String> activityLabels = Arrays.asList(ActivityType.values()).stream()
					.map(activity -> activity.getLabel()).collect(Collectors.toList());
			activityLabels.add(MessageDocument.MessageType.ACTIVITY.name());
			List<MessengerMessage> messengerMessages = messengerMessageRepository
					.findByMcIdsAndMessageTypes(secondaryContactIds, activityLabels);
			List<Integer> activityIds = new ArrayList<Integer>();
			List<Integer> mmIds = new ArrayList<Integer>();
			if (CollectionUtils.isNotEmpty(messengerMessages)) {
				messengerMessages.forEach(mm -> {
					activityIds.add(mm.getMessageId());
					mmIds.add(mm.getId());
				});
			}
			if (CollectionUtils.isNotEmpty(activityIds)) {
				conversationActivityService.deleteConversationActivityUsingIds(activityIds);
			}
			if (CollectionUtils.isNotEmpty(mmIds)) {
				messengerMessageRepository.deleteByIds(mmIds);
			}
		}
	}

	@Override
	public void publishEventToMigrateFbAndGBMId(Integer businessId, Integer mcId, Integer pageSize) {
		log.info("request received to publish event to migrate FbAndGBMId for businessId: {} ,mcId: {}");
		List<UpdateFBAndGBMIdRequest> events = new ArrayList<UpdateFBAndGBMIdRequest>();
		List<Object[]> objects = new ArrayList<Object[]>();
		Integer prevBusinessId = null;
		if (businessId != null) {
			objects = messengerContactRepository.findContactsByLocation(businessId);
		} else if (mcId != null) {
			objects = messengerContactRepository.findContactById(mcId);
		} else {
			int pageNo = 0;
			Pageable pageable = PageRequest.of(pageNo, pageSize);
			Page<Object[]> pagedResult = messengerContactRepository.findContacts(pageable);
			while (pagedResult.hasContent()) {
				objects.addAll(pagedResult.getContent());
				pageNo++;
				pageable = PageRequest.of(pageNo, pageSize);
				pagedResult = messengerContactRepository.findContacts(pageable);
			}
		} // mc.id,mc.business_id,mc.facebook_id,mc.google_conversation_id
		if (CollectionUtils.isNotEmpty(objects)) {
			prevBusinessId = (Integer) objects.get(0)[1];
			for (Object[] object : objects) {
				Integer bizId = (Integer) object[1];
				if (!prevBusinessId.equals(bizId) || events.size() == pageSize) {
					log.info("pushing event in kafka to migrate FbAndGBMId : {}", events);
					pushFBAndGBMIdEventInKafka(events, prevBusinessId);
					events.clear();
				}
				events.add(new UpdateFBAndGBMIdRequest((Integer) object[0], (Integer) object[1], (String) object[2],
						(String) object[3]));
				prevBusinessId = (Integer) object[1];
			}
			if (CollectionUtils.isNotEmpty(events)) {
				log.info("pushing last event in kafka to migrate FbAndGBMId : {}", events);
				pushFBAndGBMIdEventInKafka(events, prevBusinessId);
			}
		}

	}

	private void pushFBAndGBMIdEventInKafka(List<UpdateFBAndGBMIdRequest> events, Integer prevBusinessId) {
		UpdateFBAndGBMIdEvents event = new UpdateFBAndGBMIdEvents();
		event.setBusinessId(prevBusinessId);
		event.setRequests(events);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.UPDATE_FB_GBM_ID, null, event);
	}

	@Override
	public void updateFbAndGBMId(UpdateFBAndGBMIdEvents event) {
		log.info("Request received to update FbAndGBMId in ES: {}", event);
		List<Integer> mcIds = new ArrayList<Integer>();
		if (CollectionUtils.isEmpty(event.getRequests())) {
			log.error("Invalid request | update FbAndGBMId in ES: {}", event);
			return;
		}
		event.getRequests().forEach(doc -> {
			mcIds.add(doc.getMcId());
		});
		Map<Integer, ContactDocument> contactDocumentMap = new HashMap<Integer, ContactDocument>();
		List<ContactDocument> contactDocumentList = new ArrayList<ContactDocument>();
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("conversationIds", ControllerUtil.toCommaSeparatedString(mcIds));
		// GET Contact Documents
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATIONS_BY_ID, dataModel).build();
		List<ContactDocument> contactDocuments = elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
		contactDocuments.forEach(doc -> {
			contactDocumentMap.put(doc.getM_c_id(), doc);
		});
		event.getRequests().forEach(doc -> {
			if (contactDocumentMap.get(doc.getMcId()) != null) {
				ContactDocument contactDocument = contactDocumentMap.get(doc.getMcId());
				if (StringUtils.isNotBlank(doc.getFacebookId())) {
					contactDocument.setFacebook_conversation_id(doc.getFacebookId());
				}
				if (StringUtils.isNotBlank(doc.getGoogleConversationId())) {
					contactDocument.setGoogle_conversation_id(doc.getGoogleConversationId());
				}
				contactDocumentList.add(contactDocument);
			}
		});
		if (CollectionUtils.isNotEmpty(contactDocumentList)) {
			Integer routingId = contactDocumentList.get(0).getE_id();
			;
			BulkUpsertPayload<ContactDocument> bulkUpsertPayload = new BulkUpsertPayload<>(contactDocumentList,
					routingId, routingId, Constants.Elastic.CONTACT_INDEX);
			try {
				elasticSearchService.bulkUpsert(bulkUpsertPayload);
			} catch (Exception e) {
				log.error("Exception while updating the contact documents for event: {} exception :{}", event, e);
			}
		}
	}

	public void migrateWidgets() {
		List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigService.getAllWidgets();
		for (BusinessChatWidgetConfig bc : businessChatWidgetConfigs) {
			if (bc.getId() != 1) {
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.MIGRATE_MULTI_LOC_WIDGETS, bc);
			}
		}
	}

	@Override
	@org.springframework.transaction.annotation.Transactional
	public void migrateWidgetToMultiLocWidget(BusinessChatWidgetConfig bc) {
		if (bc.getExternalId() == null || bc.getExternalId() == 0) {
			bc.setExternalId(bc.getBusinessId());
			if (bc.getEnterpriseId() != null) {
				BusinessDTO businessDTO = businessService.getBusinessLiteDTO(bc.getEnterpriseId());
				if (businessDTO != null) {
					BusinessChatEnabledLocation businessChatEnabledLocation = new BusinessChatEnabledLocation();
					businessChatEnabledLocation.setBusinessNumber(bc.getBusinessId());
					businessChatEnabledLocation.setBusinessChatWidgetId(bc.getId());
					businessChatEnabledLocationRepository.save(businessChatEnabledLocation);
					bc.setBusinessId(businessDTO.getBusinessNumber());
				}
			}
			businessChatWidgetConfigRepository.saveAndFlush(bc);
		}
	}

	// @Override
	// public void migrateWidgetToLocation( ){
	// List<BusinessChatWidgetConfig> businessChatWidgetConfigs =
	// businessChatWidgetConfigRepository.getWebChatConfigByEnt();
	// for(BusinessChatWidgetConfig bc : businessChatWidgetConfigs){
	// if (bc.getExternalId() != null ){
	// addLocs(bc);
	// }
	// }
	// }
	//
	// private void addLocs(BusinessChatWidgetConfig bc){
	// BusinessChatEnabledLocation businessChatEnabledLocation = new
	// BusinessChatEnabledLocation();
	// businessChatEnabledLocation.setBusinessNumber(bc.getExternalId());
	// businessChatEnabledLocation.setBusinessChatWidgetId(bc.getId());
	// businessChatEnabledLocationRepository.save(businessChatEnabledLocation);
	// }

	@Override
	public void createDefaultWidget(List<CreateDeafaultCreationRequest> defaultWidgetCreationRequest) {
		if (CollectionUtils.isNotEmpty(defaultWidgetCreationRequest)) {
			for (CreateDeafaultCreationRequest createDeafaultWidgetForAccount : defaultWidgetCreationRequest) {
				BusinessDTO businessDTO = businessService
						.getBusinessLiteDTO(createDeafaultWidgetForAccount.getAccountId());
				CreateDefaultConfigRequest request = createDefaultWidgetRequest(businessDTO,
						createDeafaultWidgetForAccount.getUserId());
				boolean status = webchatService.createDefaultConfig(request);
				log.info("Default widget creation status {} for accountId: {}", status,
						createDeafaultWidgetForAccount.getAccountId());
			}
		}
	}

	private CreateDefaultConfigRequest createDefaultWidgetRequest(BusinessDTO businessDTO, Integer userId) {
		CreateDefaultConfigRequest defaultConfigRequest = new CreateDefaultConfigRequest();
		defaultConfigRequest.setBusinessId(businessDTO.getBusinessId());
		defaultConfigRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		if ("Business".equals(businessDTO.getType()) && businessDTO.getEnterpriseId() == null) {
			defaultConfigRequest.setIsSMB(true);
			defaultConfigRequest.setIsEnterprise(false);
		}
		if ("Enterprise-Location".equals(businessDTO.getType()) || "Enterprise-product".equals(businessDTO.getType())) {
			defaultConfigRequest.setIsEnterprise(true);
			defaultConfigRequest.setIsSMB(false);
		}
		defaultConfigRequest.setUserId(userId);
		return defaultConfigRequest;
	}

	private boolean checkForDuplicateEvent(BusinessMoveEvent businessMoveEvent, BusinessMoveAudit businessMoveAudit) {
		if (businessMoveAudit != null) {
			if (businessMoveEvent.getForceExecute().equals(true)) {
				return false;
			} else {
				return true;
			}
		}
		return false;
	}

	@Override
	public void migrateCsvDataToES(ImportCsvMessage request) throws Exception {
		log.info("Request to migrate csv data to ES {} : {}", request.getAccountId(), request);
		if (Objects.isNull(request.getAccountId())) {
			log.info("AccountId cannot be null {}", request);
			throw new MessengerException("AccountId cannot be null");
		}
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO("businessNumber", request.getAccountId());
		boolean isSmb = checkBusinessSMB(businessDTO);
		List<BusinessLite> businessLiteObjects = new ArrayList<>();
		if (!isSmb) {
			log.info("Enterprise : {}", request.getAccountId());
			businessLiteObjects = businessService.getBusinessLiteObjects(businessDTO.getBusinessId());
		}
		List<Record> records = request.getRecords();
		for (Record record : records) {
			if ("phone".equals(record.getChannel()) &&
					("outbound".equalsIgnoreCase(record.getInbound_or_outbound())
							|| "inbound".equalsIgnoreCase(record.getInbound_or_outbound()))) {
				if (Objects.isNull(record.getChannel_identifier())) {
					log.info("Phone Number cannot be null {}", record);
					pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
							"Phone Number null");
					continue;
				}
				if (Objects.isNull(record.getBody())) {
					log.info("Message Body cannot be null {}", record);
					pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
							"Message Body null");
					continue;
				}
				if (Objects.isNull(record.getDate())) {
					log.info("Message Date cannot be null {}", record);
					pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
							"Message Date null");
					continue;
				}
				if (Objects.isNull(record.getMessage_uuid())) {
					log.info("Message UUID cannot be null {}", record);
					pushEventStatusBeAssist(request.getEventId(),String.valueOf(record.getMessage_uuid()),"failed","Message UUID null");
					continue;
				}

				log.info("Processing Record for AccId-{} : {}", request.getAccountId(), record);
				try {
					// Create data in Inbox via migrate/podium/data
					Integer businessId = null;
					if (!isSmb) {
						String locIdentifier = record.getLocation_name();
						if (CollectionUtils.isNotEmpty(businessLiteObjects)) {
							for (BusinessLite businessLite : businessLiteObjects) {
								if (StringUtils.isNotBlank(locIdentifier)
										&& (Objects.nonNull(businessLite.getBusinessAlias()))
										&& (businessLite.getBusinessAlias().contains(locIdentifier)
												|| locIdentifier.contains(businessLite.getBusinessAlias()))) {
									businessId = businessLite.getBusinessId();
									break;
								}
							}
						}
						if (businessId == null) {
							log.info("No businessId found. Row ignored {}", record);
							pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()),
									"failed", "No businessId found");
							continue;
							// throw new MessengerException("No businessId found. Row ignored.");
						}
					} else {
						log.info("SMB {}", record);
						businessId = businessDTO.getBusinessId();
					}
					Conversation podCon = new Conversation();
					podCon.setEventId(request.getEventId());
					podCon.setAccountId(businessDTO.getAccountId());
					podCon.setBusinessId(businessId);
					podCon.setUuid(String.valueOf(record.getId()));
					CustomerDetails cd = new CustomerDetails();
					cd.setName(record.getContact_name());
					cd.setPhone(record.getChannel_identifier());
					podCon.setCustomerDetails(cd);

					List<Message> msgList = new ArrayList<>();
					Message msg = new Message();
					msg.setUuid(String.valueOf(record.getMessage_uuid()));
					msg.setMessageBody(record.getBody());
					String formattedDate = formatDateForImportCsv(record, request);
					if (Objects.isNull(formattedDate)) {
						continue;
					}
					msg.setCreatedDate(formattedDate);
					msg.setMessageType("CHAT");
					if ("outbound".equalsIgnoreCase(record.getInbound_or_outbound())) {
						msg.setCommunicationDirection("SEND");
					} else if ("inbound".equalsIgnoreCase(record.getInbound_or_outbound())) {
						msg.setCommunicationDirection("RECEIVE");
					}
					msg.setSentThrough("WEB");
					msgList.add(msg);
					podCon.setMessages(msgList);
					podCon.setStatus("IN_PROGRESS");
					podCon.setLastItem(msg);
					podCon.setTimeZoneId(request.getTimezone());
					kafkaService.publishToKafkaAsync(KafkaTopicEnum.MIGRATE_PODIUM_DATA, record.getChannel_identifier(),
							podCon);
				} catch (Exception ex) {
					log.error("Failure in migrateCsvDataToES. Request: {} : {}", record, ex);
					pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
							"Other failure");
				}
			} else {
				log.info("Other channel in migrateCsvDataToES. Row ignored: {}", record);
				pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
						"Channel or Event type not supported");
			}
		}
		if (request.isIslastChunk()) {
			log.info("Last chunk processed for account : {}", request.getAccountId());
		}
	}

	private boolean checkBusinessSMB(BusinessDTO business) {
		return "Business".equals(business.getType());
	}

	public String formatDateForImportCsv(Record record, ImportCsvMessage request) {
		String dateString = record.getDate();
		String format = request.getDateformat();
		String d = null;
		try {
			DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(format);
			LocalDateTime dateTime = LocalDateTime.parse(dateString.replaceAll("'", "").replaceAll("Z", ""),
					inputFormatter);
			DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS'Z'");
			d = dateTime.format(outputFormatter);
		} catch (Exception ex) {
			log.error("Date parsing failed : {}", dateString);
			pushEventStatusBeAssist(request.getEventId(), String.valueOf(record.getMessage_uuid()), "failed",
					"Incorrect Date format");
		}
		return d;
	}

	private void pushEventStatusBeAssist(String eventId, String message_uuid, String status, String error) {
		PodiumImportMsgStatus statusObject = new PodiumImportMsgStatus(eventId, message_uuid, status, error);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.CSV_UPLOAD_MSG_STATUS, eventId, statusObject);
	}
}
