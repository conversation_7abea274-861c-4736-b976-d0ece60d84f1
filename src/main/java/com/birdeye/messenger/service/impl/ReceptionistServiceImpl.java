package com.birdeye.messenger.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.birdeye.messenger.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.dto.BusinessReceptionistResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessReceptionistConfigurationService;
import com.birdeye.messenger.service.ReceptionistService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.sro.BusinessMissedCallSetting;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.sro.ReceptionistConfigurationMessage;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.PhoneNoValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class ReceptionistServiceImpl implements ReceptionistService {

	@Autowired
	private BusinessReceptionistConfigurationService receptionistConfigurationService;

	@Autowired
	private BusinessService businessService;
	
	@Autowired
	private SmsService smsService;

	@Override
	public Map<?, ?> getReceptionistConfiguration(long enterpriseId) throws Exception{
		BusinessDTO business = businessService.getBusinessLiteDTO("businessNumber", String.valueOf(enterpriseId));
		BusinessReceptionistConfiguration config = receptionistConfigurationService.getReceptionistConfiguration(enterpriseId);
		if (Objects.isNull(config)) {
			// get default config
			log.debug("Receptionist config not present for business {}. Getting Default Config.", enterpriseId);
			config = receptionistConfigurationService.getDefaultBusinessChatWidgetConfig(); // id=1 Default Configuration
		}
		Map<String, Object> resp = getReceptionistConfigMap(config, business);
		
		return resp;
	}

	private Map<String, Object> getReceptionistConfigMap(BusinessReceptionistConfiguration config, BusinessDTO business) {
		Map<String, Object> resp = new HashMap<>();
		resp.put("forwardingSetupComplete", config.getForwardingSetupStatus() == 1 ? true : false);
		resp.put("voicemailGreetingText", config.getVoicemailGreetingText());
		resp.put("voicemailSourceUrl", config.getVoicemailSourceUrl());
		resp.put("autoReplyText", "Whitelabel".equalsIgnoreCase(business.getAccountType()) ? config.getAutoreplyText().replace(SENT_VIA_BIRDEYE,"") : config.getAutoreplyText());
		if (checkBusinessSMB(business)) {
			// Get Landline Number and Receptionist Number from business service
			BusinessReceptionistResponse phoneNumbers  = businessService.getDataForReceptionist(business.getBusinessId());
			resp.put("landlineNumber", CollectionUtils.isNotEmpty(phoneNumbers.getData()) ? phoneNumbers.getData().get(0).getLandlineNumber() : "");
			resp.put("receptionistNumber", CollectionUtils.isNotEmpty(phoneNumbers.getData()) ? phoneNumbers.getData().get(0).getReceptionistNumber() : "");
		}
		resp.put("missedCallAutoreplyText", "Whitelabel".equalsIgnoreCase(business.getAccountType()) ? config.getMissedCallAutoreplyText().replace(SENT_VIA_BIRDEYE,"") : config.getMissedCallAutoreplyText());
		return resp;
	}

	private boolean checkBusinessSMB(BusinessDTO business) {
		return ("Business".equals(business.getType()) || "Product".equals(business.getType()));
	}
	
	@Override
	public void saveOrUpdateReceptionistConfiguration(Long enterpriseId, ReceptionistConfigurationMessage request) {
		BusinessReceptionistConfiguration getConfig = receptionistConfigurationService.getReceptionistConfiguration(enterpriseId);
		if (Objects.isNull(getConfig)) {
			// save config
			BusinessReceptionistConfiguration saveConfig = DtoToEntityConverter.from(request);
			saveConfig.setBusinessId(enterpriseId);
			receptionistConfigurationService.saveReceptionistConfig(saveConfig);
		} else {
			// update config
			updateExistingConfig(getConfig, request);
			receptionistConfigurationService.saveReceptionistConfig(getConfig);
		}
	}

	private void updateExistingConfig(BusinessReceptionistConfiguration getConfig,
			ReceptionistConfigurationMessage request) {
		getConfig.setForwardingSetupStatus(request.isForwardingSetupComplete() == true ? 1 : 0);
		getConfig.setVoicemailGreetingText(request.getVoicemailGreetingText());
		getConfig.setVoicemailSourceUrl(request.getVoicemailSourceUrl());
		getConfig.setAutoreplyText(request.getAutoReplyText());
		getConfig.setMissedCallAutoreplyText(request.getMissedCallAutoreplyText());
	}

	@Override
	public Map<String, Object> getReceptionistGreeting(String businessPhoneNumber) throws Exception{
		if (StringUtils.isBlank(businessPhoneNumber)) {
			log.error("Invalid greeting request. Phone number {} can't be empty.", businessPhoneNumber);
			return null;
		}
		log.info("Get receptionist greeting for business phone number {}", businessPhoneNumber);
//		BusinessDTO businessDTO = businessService.getBusinessLiteDTO("smsPhoneNumber", businessPhoneNumber);
		InfoByPhoneNumberRequest businessRequest = new InfoByPhoneNumberRequest(InfoByPhoneNumberRequest.Type.voice, businessPhoneNumber);
		BusinessDTO businessDTO = businessService.getBusinessInfoByPhoneNumber(businessRequest);
		if (Objects.isNull(businessDTO)) {
			log.info("getReceptionistGreeting: businessDTO returned null from core-service for businessPhoneNumber {}",
					businessPhoneNumber);
			return null;
		}
		boolean receptionistEnabled = isReceptionistEnabledCheck(businessDTO);
		Map<String, Object> greetingMap = new HashMap<>();
		if (!receptionistEnabled) {
			log.info("getReceptionistGreeting: Receptionist Feature not enabled for businessId {}",
					businessDTO.getBusinessId());
			greetingMap.put("greeting", null);
			greetingMap.put("receptionist", 0);
			return greetingMap;
		}
		
		BusinessReceptionistConfiguration config = getBusinessReceptionistConfiguration(businessDTO);
		String greeting = config.getVoicemailGreetingText();
		// Replace business tokens
		String formattedGreeting = getFormattedGreeting(businessDTO, greeting);
		
		greetingMap.put("greeting", formattedGreeting);
		greetingMap.put("receptionist", 1);
		return greetingMap;
	}

	/**
	 * Method to check if receptionist is enabled
	 * 1. Check Receptionist feature flag &
	 * 2. Check Receptionist config flag : Removed
	 */
	@Override
	public boolean isReceptionistEnabledCheck(BusinessDTO businessDTO) {
		BusinessOptionResponse businessOptions = businessService.getBusinessOptionsConfig(businessDTO.getAccountId(), true);
		if (businessOptions!=null && (businessOptions.getEnableReceptionist()!=null && businessOptions.getEnableReceptionist()==1))
			return true;
		
		return false;
	}
	
	/**
	 * Method to replace tokens from greeting text
	 */
	@Override
	public String getFormattedGreeting(BusinessDTO businessDTO, String greeting) {
		String businessPhone = "";
		String countryCode=Objects.nonNull(businessDTO.getCountryCode())?businessDTO.getCountryCode():(Objects.nonNull(businessDTO.getLocation())?businessDTO.getLocation().getCountryCode():null);
		if (StringUtils.isNotBlank(businessDTO.getPhone())) {
			businessPhone = PhoneNoValidator.formatOriginal(businessDTO.getPhone(), countryCode);
		}
		String textingNumber  = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
		//BIRDEYE-87171
		BusinessTimingDTO businessTimingDTO = null;
		if (greeting.contains(Constants.BUSINESS_HOURS_OLD) || greeting.contains(Constants.BUSINESS_HOURS_NEW)){
			businessTimingDTO = businessService.getBusinessTimings(businessDTO.getBusinessId(),true);
		}
		PublicDataBusinessDTO publicBusinessDto = null;
		if (greeting.contains(Constants.BUSINESS_SERVICES) || greeting.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
        		|| greeting.contains(Constants.BUSINESS_LANGUAGES) || greeting.contains(Constants.BUSINESS_SERVICES_CAP)
        				|| greeting.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || greeting.contains(Constants.BUSINESS_LANGUAGES_CAP)){
			publicBusinessDto = businessService.getPublicBusinessData(businessDTO.getBusinessId());
		}
		BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(businessDTO.getBusinessId());
		BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessDTO.getBusinessId());
		String formattedGreeting = ControllerUtil.replaceTokensOld(businessDTO, null, greeting, businessPhone, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);
		if("Whitelabel".equalsIgnoreCase(businessDTO.getAccountType())) {
			formattedGreeting = formattedGreeting.replace(SENT_VIA_BIRDEYE,"");
		}
		return formattedGreeting;
	}
	
	@Override
	public String getReceptionistMissedCallAutoReplyInbox(BusinessDTO businessDTO) {
		BusinessReceptionistConfiguration config = receptionistConfigurationService.getMissedCallConfigInbox();
		return getFormattedGreeting(businessDTO, config.getMissedCallAutoreplyText());
	}
	
	@Override
	public String getReceptionistMissedCallAutoReplyNonInbox(BusinessDTO businessDTO) {
		BusinessReceptionistConfiguration config = receptionistConfigurationService.getMissedCallConfigNonInbox();
		return getFormattedGreeting(businessDTO, config.getMissedCallAutoreplyText());
	}
	
	@Override
	public void clearReceptionistCache() {
		receptionistConfigurationService.clearDefaultBusinessChatWidgetConfig();
		receptionistConfigurationService.clearMissedCallInboxConfig();
		receptionistConfigurationService.clearMissedCallNonInboxConfig();
	}
	
	@Override
	public String getReceptionistAutoReplyMessage(BusinessDTO businessDTO, VoiceCallDto dto, String cName) {
		BusinessReceptionistConfiguration config = getBusinessReceptionistConfiguration(businessDTO);
		//Replaces [Business Name], [Business Phone], [Business Address Inline]
		String  autoreply = config.getAutoreplyText();
		if (dto.getDuration() != null && dto.getDuration().intValue() <  5) {
			autoreply = config.getMissedCallAutoreplyText();
		}
		String formattedAutoReply = getFormattedGreeting(businessDTO, autoreply);

		String customerName = StringUtils.isEmpty(cName) ? "there" : extractFirstNameOnly(cName);
		formattedAutoReply = formattedAutoReply.replace("[Contact first name]", customerName);

		return formattedAutoReply;
	}
	
	@Override
	public String extractFirstNameOnly(String fullName) {
		if (fullName == null) {
			return "";
		}
		StringBuilder firstName = new StringBuilder();
		if (StringUtils.contains(fullName, " ")) {
			firstName.append(StringUtils.substringBefore(fullName, " "));
		} else {
			firstName.append(fullName);
		}
		return ControllerUtil.escapeHtmlEncoding(firstName.toString());
	}
	
	@Override
	public String getReceptionistConfiguredMissedCallAutoReply(BusinessDTO businessDTO, String cName) {
		BusinessReceptionistConfiguration config = getBusinessReceptionistConfiguration(businessDTO);
		//Replaces [Business Name], [Business Phone], [Business Address Inline]
		String formattedAutoReply = getFormattedGreeting(businessDTO, config.getMissedCallAutoreplyText());

		String customerName = StringUtils.isEmpty(cName) ? "there" : extractFirstNameOnly(cName);
		formattedAutoReply = formattedAutoReply.replace("[Contact first name]", customerName);

		return formattedAutoReply;
	}
	
	@Override
	public BusinessReceptionistConfiguration getBusinessReceptionistConfiguration(BusinessDTO businessDTO) {
		BusinessReceptionistConfiguration config = receptionistConfigurationService.getConfigOfAccountFromLocation(businessDTO);
		if (ObjectUtils.isEmpty(config)) {
			log.info("Business Receptionist configuration not found. Getting default configuration for business {}",
					businessDTO.getBusinessId());
			config = receptionistConfigurationService.getDefaultBusinessChatWidgetConfig(); // id=1 Default Configuration
		}
		return config;
	}

	@Override
	public void updateMissedCallSetting(BusinessMissedCallSetting request) {
		BusinessReceptionistConfiguration getConfig = receptionistConfigurationService.getReceptionistConfiguration(request.getEnterpriseId());
		if (Objects.isNull(getConfig)) {
			// save config
			BusinessReceptionistConfiguration saveConfig = new BusinessReceptionistConfiguration();
			saveConfig.setBusinessId(request.getEnterpriseId());
			saveConfig.setMissedCallReject(request.getMissedCallStatus());
			receptionistConfigurationService.saveReceptionistConfig(saveConfig);
		} else {
			// update config
			updateExistingBusinessConfig(getConfig, request);
			receptionistConfigurationService.saveReceptionistConfig(getConfig);
		}
		
	}
	
	private void updateExistingBusinessConfig(BusinessReceptionistConfiguration getConfig,
			BusinessMissedCallSetting request) {
		getConfig.setMissedCallReject(request.getMissedCallStatus());
	}

	@Override
	public ReceptionistTextToSpeechTokenReplaceResponse replaceTokensForTextToSpeechMessage(ReceptionistTextToSpeechTokenReplaceRequest request){
		if(Objects.nonNull(request) && (Objects.isNull(request.getBusinessId()) || StringUtils.isEmpty(request.getBody()))){
			log.error("Invalid request replaceTokensForTextToSpeechMessage. BusinessId or Text Body can't be empty: {}", request);
			return null;
		}
		BusinessDTO businessDTO = businessService.getBusinessDTO(request.getBusinessId());
		if (Objects.isNull(businessDTO)) {
			log.info("replaceTokensForTextToSpeechMessage: businessDTO returned null from core-service for businessId {}",
					request.getBusinessId());
			return null;
		}

		String formattedBody = getFormattedGreeting(businessDTO, request.getBody());

		ReceptionistTextToSpeechTokenReplaceResponse response = new ReceptionistTextToSpeechTokenReplaceResponse(request.getBusinessId(),formattedBody);
        return response;
	}
}
