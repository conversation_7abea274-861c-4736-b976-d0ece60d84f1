package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ChatTranscriptEvent;
import com.birdeye.messenger.dto.ConversationClosedEvent;
import com.birdeye.messenger.dto.ConversationWebhookEventDTO;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.EmailMessageDocument;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.TidyUpCloseEvent;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WebhookEventEnum;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.EventPublisherService;
import com.birdeye.messenger.service.FreemarkerTemplateService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.WebhookService;
import com.birdeye.messenger.util.ControllerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class EventPublisherServiceImpl implements EventPublisherService {

    private final KafkaService kafkaService;
    
    private final WebhookService webhookService;
    
    private final CommonService commonService;
    
    private final MessengerContactService messengerContactService;
    
    private final BusinessService businessService;
    
    private final EmailService emailService;
    private final FreemarkerTemplateService freemarkerTemplateService; 
    private final CommunicationHelperService communicationHelperService; 
    
    @Async
    @Override
    public void produceEvent(ActivityType activityType, Integer accountId, List<Integer> conversationIds,
    		ContactDocument contactDocument) {
    	if (activityType.equals(ActivityType.CLOSED) && contactDocument!=null) {
    		ConversationClosedEvent conversationClosedEvent = new ConversationClosedEvent(accountId, null,null);
    		if(isBusinessSubsribedToClosedEvent(conversationClosedEvent)) {
    			conversationIds.forEach(conversationId -> {
    				ConversationClosedEvent event = new ConversationClosedEvent(accountId, conversationId,conversationClosedEvent.getChatTranscriptContentType());
    				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CONVERSATION_CLOSED_EVENT, null, event);
    				log.info("pushed to kafka CONVERSATION_CLOSED_EVENT :: {}", event);
    			});
    		}
    		conversationIds.forEach(conversationId -> {
    			Integer assignmentId;
    			if ("U".equals(contactDocument.getAssignmentType())){
    				assignmentId = contactDocument.getCr_asgn_id();
    			} else {
    				assignmentId = contactDocument.getTeam_id();
    			}
    			ChatTranscriptEvent event = new ChatTranscriptEvent(accountId, conversationId, 
    					contactDocument.getAssignmentType(), assignmentId, contactDocument.getC_id());
    			kafkaService.publishToKafkaAsync(KafkaTopicEnum.CHAT_TRANSCRIPT_EVENT, conversationId, event);
    			log.info("pushed to kafka CHAT_TRANSCRIPT_EVENT :: {}", event);
    		});
    	} else if(activityType.equals(ActivityType.CLOSED)) {
    		//Push to kafka for Campaign Automation
    		if (contactDocument == null) {
    			TidyUpCloseEvent closeEvent = new TidyUpCloseEvent(accountId, conversationIds);
    			kafkaService.publishToKafkaAsync(KafkaTopicEnum.TIDY_UP_CLOSED, accountId, closeEvent);
    			return;
    		}
    	}
    }

    private boolean isBusinessSubsribedToClosedEvent(ConversationClosedEvent conversationClosedEvent) {
    	 return webhookService.isBusinessSubsribedToClosedEvent(conversationClosedEvent);
	}

    @Override
	public void publishConversationClosedEvent(ConversationClosedEvent event) {
		Integer mcId=event.getConversationId();
		Integer accountId=event.getAccountId();
        Long lastReceivedMessageTime=null;
	    String chatTranscript="";
		MessengerContact messengerContact = messengerContactService.findById(mcId);
        if (Objects.isNull(messengerContact)) {
            log.info("publishConversationClosedEvent: no records found for mcId: {}", mcId);
            return;
        }
        BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
        if (Objects.isNull(businessDTO) || !businessDTO.getAccountId().equals(accountId)) {
            log.info("publishConversationClosedEvent: contact with Id {} does not belong to account with Id {}", mcId, accountId);
            return;
        }
        // ----------------------- validation code ends here ------------------------------------------------------

        ContactDocument contactDocument = messengerContactService.getContact(businessDTO.getAccountId(), mcId);
        if(contactDocument==null) {
			log.info("conversation does not exist in ES for conversationId: {}",mcId);
			throw new NotFoundException(ErrorCode.INVALID_CONVERSATION_ID);
			
		}
        List<String> messageTypesToBeExcluded = getMessageTypesToBeExcluded();
        List<MessageDocument> messages = getConversationMessages(businessDTO.getAccountId(), mcId,messageTypesToBeExcluded,"desc");
        if (CollectionUtils.isEmpty(messages) || Objects.isNull(contactDocument)) {
            log.info("publishConversationClosedEvent: no messages or contact found for contact with Id {} and account with Id {}", mcId, accountId);
            return;
        }
       List<MessageDocument> closedActivities= messages.stream().filter(msg->ActivityType.CLOSED.equals(msg.getActivityType())).collect(Collectors.toList());
       if (CollectionUtils.isEmpty(closedActivities)) {
           log.info("publishConversationClosedEvent: no closed activities found for contact with Id {} and account with Id {}", mcId, accountId);
           return;
       }
       Optional<MessageDocument> receivedMessageOpt=messages.stream().filter(msg->(CommunicationDirection.RECEIVE.equals(msg.getCommunicationDirection()))).findFirst();
      
       if(receivedMessageOpt.isPresent()) {
    	   lastReceivedMessageTime=receivedMessageOpt.get().getCr_time();
       }
       List<MessageDocument> chatTranscriptMessages = messages.stream()
				.filter(msg -> !MessageType.ACTIVITY.equals(msg.getMessageType()))
				.filter((closedActivities.size() > 1) ? msg -> msg.getCr_time() >= closedActivities.get(1).getCr_time()
						&& msg.getCr_time() <= closedActivities.get(0).getCr_time() : msg -> true)
				.sorted(Comparator.comparingLong(MessageDocument::getCr_time)).collect(Collectors.toList());
       List<EmailMessageDocument> emailMessageDocuments = emailService.prepareFormattedEmailMessages(chatTranscriptMessages, contactDocument, businessDTO.getTimeZoneId() != null ? businessDTO.getTimeZoneId() : "PST");
       ConversationWebhookEventDTO conversationWebhookDTO = webhookService
				.mapMessengerContactToConversationWebhookDTO(messengerContact);
       addAssigneeEmailId(conversationWebhookDTO);
       conversationWebhookDTO.setLastReceivedMessageTime(lastReceivedMessageTime);
       conversationWebhookDTO.setClosedTime(closedActivities.get(0).getCr_time());
       conversationWebhookDTO.setLead(BooleanUtils.isTrue(contactDocument.getLead()));
       conversationWebhookDTO.setLeadSource(contactDocument.getLeadOrigin()!=null?contactDocument.getLeadOrigin().name():null);
       setChatTranscript(event, mcId, accountId, chatTranscript, emailMessageDocuments, conversationWebhookDTO);
       Integer lastMsgSource = messengerContactService.getLastMessageSource(contactDocument);
       lastMsgSource=lastMsgSource != null ? lastMsgSource : 1;
       conversationWebhookDTO.setLastMessageSource(Source.getValue(lastMsgSource).name());
       webhookService.publishMessengerWebhookEvent(conversationWebhookDTO, accountId,
				WebhookEventEnum.CONVERSATION_CLOSED.getName(), messengerContact.getBusinessId());
	}

	private void addAssigneeEmailId(ConversationWebhookEventDTO conversationWebhookDTO) {
		if (conversationWebhookDTO.getAssignee()!=null && conversationWebhookDTO.getAssignee().getId()!=null && Constants.Assignment.USER_ASSIGNED.equals(conversationWebhookDTO.getAssignee().getType())) {
			   UserDTO userDTO = communicationHelperService.getUserDTO(conversationWebhookDTO.getAssignee().getId());
			   conversationWebhookDTO.getAssignee().setEmailId(userDTO.getEmailId());
		   }
	}

	private void setChatTranscript(ConversationClosedEvent event, Integer mcId, Integer accountId,
			String chatTranscript, List<EmailMessageDocument> emailMessageDocuments,
			ConversationWebhookEventDTO conversationWebhookDTO) {
		if("html".equalsIgnoreCase(event.getChatTranscriptContentType())){
			   Map<String, Object> dataModel = new HashMap<>();
			   dataModel.put("emailMessageDocuments", emailMessageDocuments);
			   chatTranscript= freemarkerTemplateService.processTemplate(Constants.Elastic.GET_MESSAGES_IN_HTML_FOR_WEBHOOK, dataModel);
			} else {
				try {
					final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
					chatTranscript = OBJECT_MAPPER.writeValueAsString(emailMessageDocuments);
				} catch (JsonProcessingException e) {
					log.info(
							"publishConversationClosedEvent: Error in converting messages to json for contact with Id {} and account with Id {}",
							mcId, accountId);
				}
			}
		   conversationWebhookDTO.setChatTranscript(chatTranscript);
	}
	private List<MessageDocument> getConversationMessages(Integer accountId, Integer mcId,
			List<String> messageTypesToBeExcluded,String sortOrder) {
		    List<String> excludeThese = messageTypesToBeExcluded.stream().map(type -> "\"" + type + "\"").collect(Collectors.toList());
	        String excluded = ControllerUtil.toCommaSeparatedString(excludeThese);
	        MessengerFilter filter = new MessengerFilter();
	        filter.setCount(10000);
	        filter.setAccountId(accountId);
	        Map<String, Object> params = new HashMap<>();
	        params.put("contactId", mcId);
	        params.put("sortOrder", sortOrder);
	        params.put("messageType", excluded);
	        params.put("excludedSources", ControllerUtil.toCommaSeparatedString(commonService.getExcludedMessageSourcesList()));
	        filter.setQueryFile(Constants.Elastic.GET_MESSAGE_FOR_WEBHOOK_EVENT);
	        filter.setParams(params);
	        ElasticData<MessageDocument> messageData = messengerContactService.getMessageData(filter);
	        if(messageData.isSucceeded() && messageData.getTotal() > 0) {
	            return messageData.getResults();
	        }
	        return Collections.emptyList();
	}

	protected List<String> getMessageTypesToBeExcluded() {
		List<String> messageTypes=new ArrayList<String>();
		messageTypes.add(MessageDocument.MessageType.EVENTS.toString());
		messageTypes.add(MessageDocument.MessageType.RICH_CONTENT_CHAT.toString());
        return messageTypes;
    }
}
