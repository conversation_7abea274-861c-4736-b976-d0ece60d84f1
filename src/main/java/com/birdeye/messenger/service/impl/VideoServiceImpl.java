package com.birdeye.messenger.service.impl;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.VideoRoomStatus;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.InitiateVideoConversationDTO;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.VideoConversationDTO;
import com.birdeye.messenger.dto.VideoConversationEvent;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.VideoContextDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.VideoConversationState;
import com.birdeye.messenger.exception.BadDataException;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.VideoService;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
public class VideoServiceImpl implements VideoService {
	
	@Autowired
	private NexusService nexusService;
	
	@Autowired
	private MessengerContactService messengerContactService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private ContactService contactService;
	
	@Value(value = "${video.host}")
	private String videoHost;
	
	@Autowired
	private MessengerEventHandlerService messengerEventHandlerService;
	
	@Autowired
	private ConversationActivityService conversationActivityService;
	
	@Autowired
	private BusinessService bizService;
	
	@Autowired
	@Lazy
	private VideoService videoService;
	
	@Autowired
	private FirebaseService firebaseService;

	@Autowired
    private RedisLockService redisLockService;
    
	@Autowired
    private  KafkaService kafkaService;
	
	@Override
	public InitiateVideoConversationDTO initiateVideo(Integer conversationId, Integer accountId, Integer loggedInUserId,Integer source) throws Exception {
		log.info("[Video] Recieved Request for initiating video conversation for accountId:{} and conversationId:{}",accountId, conversationId);

		MessengerContact conversation = validateAndFetchMessengerContact(conversationId);

		CustomerDTO customer = contactService.findById(conversation.getCustomerId());
		Optional<Lock> lockOpt = Optional.empty();
		String lockKey = "";
		try {
			lockKey = Constants.CUSTOMER_ID_PREFIX+customer.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (lockOpt.isPresent()){
				VideoConversationDTO videoRoomDetails = nexusService.initiateVideo(conversation.getBusinessId(), conversationId);

				UserDTO loggedInUserDetails = userService.getUserDTO(loggedInUserId);
				
				InitiateVideoConversationDTO response = new InitiateVideoConversationDTO();
				response.setConversationId(conversationId);
				response.setAccountId(accountId);
				response.setRoomId(videoRoomDetails.getRoomId());
				response.setBizUserMeetingLink(prepareBizUserMeetingLink(videoRoomDetails, loggedInUserDetails));
				response.setCustomerMeetingLink(prepareCustomerMeetingLink(videoRoomDetails,customer));
				response.setRoomId(videoRoomDetails.getRoomId());
				response.setSubaccountId(conversation.getBusinessId());

				conversation.setVideoRoomId(videoRoomDetails.getRoomId());
				conversation.setVideoRoomSid(videoRoomDetails.getUniqueId());
				conversation.setVideoRoomCreationDate(videoRoomDetails.getDateCreated());
				conversation.setVideoOnlineCustomerId(0);
				conversation.setVideoOnlineUserId(0);
				conversation.setVideoConversationStartDate(null);
				conversation.setVideoRoomStatus(VideoRoomStatus.A);
				conversation.setVideoInitiatedByUserId(loggedInUserId);

				conversation.setUpdatedAt(new Date());
				conversation.setIsRead(false);
				conversation.setViewedBy(null);
				messengerContactService.saveOrUpdateMessengerContact(conversation);

				ContactDocument contactDocument = new ContactDocument();
				contactDocument.setRecentVideoConversationContext(new VideoContextDocument(videoRoomDetails.getRoomId(), videoRoomDetails.getDateCreated().getTime(), null, videoRoomDetails.getDateCreated().getTime(), VideoConversationState.NBO.toString(),response.getBizUserMeetingLink()));
				DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
				contactDocument.setUpdatedAt(df.format(conversation.getUpdatedAt()));
				contactDocument.setViewedBy(StringUtils.isNotBlank(conversation.getViewedBy()) ? MessengerUtil.convertCommaSeparatedStringToList(conversation.getViewedBy()) : new ArrayList<>());
				contactDocument.setC_read(conversation.getIsRead() != null ? conversation.getIsRead() : false);
				messengerContactService.updateContactOnES(conversationId, contactDocument, accountId);

				videoService.sendMessageToCustomer(conversation, customer, response, loggedInUserDetails,source);
				return response;
			} else {
				//Reject such events
				log.info("Video, Unable to acquire lock. key:{}",lockKey);
				throw new RedisLockException(ErrorCode.ERROR_CREATING_VIDEO_ROOM);
			}
		}finally {
			if (lockOpt.isPresent()){
				redisLockService.unlock(lockOpt.get());
			}
		}
	}
	
	@Override
	public void handleVideoConversationEvent(VideoConversationEvent event) {
		MessengerContact conversation = messengerContactService.findById(event.getContactId());
		boolean valid = checkValidity(event,conversation);
		if(!valid) {
			return;
		}
		CustomerDTO customer = contactService.findById(conversation.getCustomerId());
		Optional<Lock> lockOpt = Optional.empty();
		String lockKey = "";
		try {
			lockKey = Constants.CUSTOMER_ID_PREFIX+customer.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (lockOpt.isPresent()){
				BusinessDTO businessDTO = bizService.getBusinessDTO(conversation.getBusinessId());
				Integer accountId = businessDTO.getAccountId();
				
				MessangerBaseFilter mbf  = new MessangerBaseFilter(accountId, conversation.getId(),1);
				List<ContactDocument> contactFromES = messengerContactService.getContactFromES(mbf);
				ContactDocument loadedConversationDoc = contactFromES.get(0);
				
				if (loadedConversationDoc.getRecentVideoConversationContext().getRoomId().equals(event.getRoomId())
						&& loadedConversationDoc.getRecentVideoConversationContext().getState().equals(VideoConversationState.CLO.name())) {
					log.error("Received an old video state event. CustomerId: {}", event.getContactId());
					return;
				}
				ContactDocument conversationDoc = new ContactDocument();
				VideoContextDocument videoContextDoc = new VideoContextDocument();

				conversation.setVideoRecentStateTransitionDate(event.getEventTimestamp());
				videoContextDoc.setLastStateTransitionEpoch(event.getEventTimestamp().getTime());
				conversationDoc.setRecentVideoConversationContext(videoContextDoc);

				switch (event.getEvent()) {
				case "ROOM_CREATED":
					return;
				case "CUSTOMER_CONNECTED":
					if(conversation.getVideoOnlineUserId()!=null && conversation.getVideoOnlineUserId() > 0) {
						if(conversation.getVideoConversationStartDate() == null) {
							conversation.setVideoConversationStartDate(event.getEventTimestamp());
							videoContextDoc.setConversationStartEpoch(event.getEventTimestamp().getTime());
						}	
						videoContextDoc.setState(VideoConversationState.ACT.toString());
					} else {
						videoContextDoc.setState(VideoConversationState.OCO.toString());
					}
					conversation.setVideoOnlineCustomerId(Integer.parseInt(event.getParticipantId()));

					break;
				case "USER_CONNECTED":
					if(conversation.getVideoOnlineCustomerId()!=null && conversation.getVideoOnlineCustomerId() > 0) {
						if(conversation.getVideoConversationStartDate() == null) {
							conversation.setVideoConversationStartDate(event.getEventTimestamp());
							videoContextDoc.setConversationStartEpoch(event.getEventTimestamp().getTime());
							publishConversationStartActivity(conversation,accountId);
						}	
						videoContextDoc.setState(VideoConversationState.ACT.toString());
					} else {
						videoContextDoc.setState(VideoConversationState.OBO.toString());
					}
					conversation.setVideoOnlineUserId(Integer.parseInt(event.getParticipantId()));
					break;
				case "USER_DISCONNECTED":
					if(conversation.getVideoOnlineCustomerId()==null || conversation.getVideoOnlineCustomerId() <= 0) {
						videoContextDoc.setState(VideoConversationState.NBO.toString());
						try {
							nexusService.terminateRoom(event.getRoomId());
						} catch (Exception e) {
							// DO-NOTHING : ERROR already logged in implementation
						}
					} else {
						videoContextDoc.setState(VideoConversationState.OCO.toString());
					}
					conversation.setVideoOnlineUserId(0);
					break;
				case "CUSTOMER_DISCONNECTED":
					if(conversation.getVideoOnlineUserId()==null || conversation.getVideoOnlineUserId() <= 0) {
						videoContextDoc.setState(VideoConversationState.NBO.toString());
						try {
							nexusService.terminateRoom(event.getRoomId());
						} catch (Exception e) {
							// DO-NOTHING : ERROR already logged in implementation
						}
					} else {
						videoContextDoc.setState(VideoConversationState.OBO.toString());
					}
					conversation.setVideoOnlineCustomerId(0);
					break;
				case "ROOM_ENDED":
					videoContextDoc.setState(VideoConversationState.CLO.toString());
					conversation.setVideoRoomStatus(VideoRoomStatus.I);
					publishConversationStopActivity(conversation, accountId, event);
					break;
				default:
					break;
				}
				conversation.setUpdatedAt(new Date());
				messengerContactService.saveOrUpdateMessengerContact(conversation);

				DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
				conversationDoc.setUpdatedAt(df.format(conversation.getUpdatedAt()));

				messengerContactService.upsertContactDocumentOnES(conversationDoc, conversation.getId().toString(), accountId);

				loadedConversationDoc.setRecentVideoConversationContext(videoContextDoc); // Doc may not be updated yet

				//firebaseService.mirrorOnWeb(accountId, conversation.getBusinessId());
				FirebaseDto firebaseDto = new FirebaseDto();
				firebaseDto.setAccountId(accountId);
				firebaseDto.setBusinessId(conversation.getBusinessId());
				firebaseDto.setMcId(conversation.getId());
				firebaseService.mirrorOnWeb(firebaseDto);
				firebaseService.mirrorOnMobile(loadedConversationDoc);
			} else {
				//add to delayed queue
				log.info("Video, Unable to acquire lock. Key:{}",lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.VIDEO_CONVERSATION_DELAYED_QUEUE, event);
			}
		}finally {
			if (lockOpt.isPresent()){
				redisLockService.unlock(lockOpt.get());
			}
		}
	}
	
	private void publishConversationStartActivity(MessengerContact conversation, Integer accountId) {
		Date roomCreationDate = conversation.getVideoRoomCreationDate();
		roomCreationDate.setTime(roomCreationDate.getTime()-1000);
		ActivityDto activity = ActivityDto.builder().accountId(accountId).activityType(ActivityType.VIDEO_CONV_START).mcId(conversation.getId()).created(roomCreationDate).build();
		ConversationActivity ConversationActivity = conversationActivityService.create(activity);
		activity.setId(ConversationActivity.getId());
		conversationActivityService.persistActivityInDatabase(activity, null);
		conversationActivityService.persistActivityInES(activity);
	}
	
	private void publishConversationStopActivity(MessengerContact conversation, Integer accountId,VideoConversationEvent conversationEvent) {
		UserDTO userDetails = null;
		CustomerDTO customerDetails = null;
		try {
			userDetails = userService.getUserDTO(conversation.getVideoInitiatedByUserId());
		} catch (Exception e) {
			log.error("[Video] Some error occured while fetching user details for userId:{}", conversation.getVideoInitiatedByUserId(),e);
			userDetails = new UserDTO();
		}
		
		try {
			customerDetails = contactService.findById(conversation.getCustomerId());
		} catch (Exception e) {
			log.error("[Video] Some error occured while fetching customer details for customerId:{}", conversation.getCustomerId(),e);
			customerDetails = new CustomerDTO();
		}
		
		Long durationInMS = conversationEvent.getRoomDurationInSeconds()!=null ? conversationEvent.getRoomDurationInSeconds()*1000 :null;
		String captializedUserName = StringUtils.capitalize(userDetails.getName());
		ActivityDto activity = ActivityDto.builder().accountId(accountId).activityType(ActivityType.VIDEO_CONV_END).mcId(conversation.getId()).businessId(conversation.getBusinessId()).created(conversation.getVideoRecentStateTransitionDate()).durationInMS(durationInMS).from(userDetails.getId()).fromName(captializedUserName).to(customerDetails.getId()).toName(customerDetails.getDerivedNameForVideoConversation()).build();
		ConversationActivity ConversationActivity = conversationActivityService.create(activity);
		activity.setId(ConversationActivity.getId());
		conversationActivityService.persistActivityInDatabase(activity, null);
		conversationActivityService.persistActivityInES(activity);
	}

	private boolean checkValidity(VideoConversationEvent event, MessengerContact conversation) {
		if(conversation.getVideoRecentStateTransitionDate()!=null && conversation.getVideoRecentStateTransitionDate().getTime() > event.getEventTimestamp().getTime()) {
			log.warn("[Video] Received an old event:{}", event);
			return false;
		}
		return true;
	}

	@Async
	@Override
	public void sendMessageToCustomer(MessengerContact conversaton, CustomerDTO customer, InitiateVideoConversationDTO videoInitResponse, UserDTO loggedInUser,Integer source) throws Exception {		
		SendMessageDTO sendMessageDTO = new SendMessageDTO();
		sendMessageDTO.setBody("You can join the video chat here: "+videoInitResponse.getCustomerMeetingLink());
		sendMessageDTO.setFromBusinessId(conversaton.getBusinessId());
		sendMessageDTO.setToCustomerId(conversaton.getId().toString());
		sendMessageDTO.setBusinessIdentifierId(conversaton.getBusinessId().toString());
		sendMessageDTO.setUserDTO(loggedInUser);
		sendMessageDTO.setUserId(loggedInUser.getId());
		sendMessageDTO.setSource(source);
		setMultiMessages(sendMessageDTO);
		messengerEventHandlerService.handleEvent(sendMessageDTO);
	}

	private String prepareCustomerMeetingLink(VideoConversationDTO videoRoomDetails, CustomerDTO customer) {
		String encodedName = null;
		try {
			encodedName = URLEncoder.encode(customer.getDerivedNameForVideoConversation(),"UTF-8");
			encodedName = StringUtils.replace(encodedName, "+", "%20");
		 } catch (UnsupportedEncodingException e) {
			 log.error("[Video] Some error occured while encoding customer name:{}",customer.getName(), e);
			 throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		 }
		String url = new StringBuilder(videoHost).append("/join/").append(videoRoomDetails.getRoomId()).append("/").append(encodedName).append("/").append(customer.getId()).append("/0").toString();
		return url;
	}

	private String prepareBizUserMeetingLink(VideoConversationDTO videoRoomDetails, UserDTO loggedInUserDTO) {
		 String encodedName = null;
		 try {
			 encodedName = StringUtils.isNotBlank(loggedInUserDTO.getName())?URLEncoder.encode(loggedInUserDTO.getName(), "UTF-8"):null;
			 encodedName = StringUtils.replace(encodedName, "+", "%20");
		 } catch (UnsupportedEncodingException e) {
			 log.error("[Video] Some error occured while encoding biz user name:{}",loggedInUserDTO.getName(), e);
			 throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		 }
		 String url = new StringBuilder(videoHost).append("/join/").append(videoRoomDetails.getRoomId()).append("/").append(encodedName).append("/").append(loggedInUserDTO.getId()).append("/1").toString();
		return url;
	}

	private MessengerContact validateAndFetchMessengerContact(Integer conversationId) {
		MessengerContact conversation = messengerContactService.findById(conversationId);

		if (conversation == null) {
			throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		}

		if(conversation.getVideoRoomStatus()!=null &&  conversation.getVideoRoomStatus()==VideoRoomStatus.A) {
			throw new BadRequestException(ErrorCode.VIDEO_CONVERSATION_IN_PROGRESS);
		}
		
		return conversation;
	}
	
	private void setMultiMessages(SendMessageDTO sendMessageDTO) {
		if (Integer.valueOf(16).equals(sendMessageDTO.getSource())) {
			List<String> multiMessages = new ArrayList<>(2);
			String messages[] = sendMessageDTO.getBody().split(":", 2);
			log.info("messagesdata : {}", messages);
			multiMessages.add(messages[0] + ":");
			multiMessages.add(messages[1].trim());
			sendMessageDTO.setMultiMessages(multiMessages);
			log.info("multimessages : {}", multiMessages);
			sendMessageDTO.setUserId(sendMessageDTO.getUserDTO().getId());
			sendMessageDTO.setBody(null);
		}
	}


}
