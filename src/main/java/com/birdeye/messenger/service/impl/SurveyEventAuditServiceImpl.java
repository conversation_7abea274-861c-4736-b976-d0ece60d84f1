package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.ReviewEventAudit;
import com.birdeye.messenger.dao.entity.SurveyEventAudit;
import com.birdeye.messenger.dao.repository.ReviewEventAuditRepository;
import com.birdeye.messenger.dao.repository.SurveyEventAuditRepository;
import com.birdeye.messenger.service.ReviewEventAuditService;
import com.birdeye.messenger.service.SurveyEventAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class SurveyEventAuditServiceImpl implements SurveyEventAuditService {

    @Autowired
    SurveyEventAuditRepository surveyEventAuditRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveSurveyEventAudit(SurveyEventAudit surveyEventAudit) {
        surveyEventAuditRepository.saveAndFlush(surveyEventAudit);
    }
}
