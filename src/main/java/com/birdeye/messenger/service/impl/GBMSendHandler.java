package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.RobinSessionDocument;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.GoogleMessagingException;
import com.birdeye.messenger.exception.InstagramException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.googleBusinessMessaging.GoogleSocialIntegrationService;
import com.birdeye.messenger.service.googleBusinessMessaging.impl.GoogleUtils;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.messenger.dto.MessageResponse.Message;

@Service
@RequiredArgsConstructor
@Slf4j
public class GBMSendHandler extends MessageEventHandlerAbstract {

    private final MessengerMessageService messengerMessageService;
    private final GoogleSocialIntegrationService googleSocialIntegrationService;
    private final SendMessageService sendMessageService;
    private final BusinessService businessService;
    private final GoogleUtils googleUtils;
    private final MessengerMediaFileService messengerMediaFileService;
    private final CommonService commonService;

    @Override
    public MessengerEvent getEvent() {
        return MessengerEvent.GOOGLE_SEND;
    }

    /*
     * GET MESSENGER CONTACT
     * Generate messageId
     * Add Entry in google_message
     * Add Entry in messenger_message
     * Update messenger_contact
     * Update ES
     * Update Response time (Report support)
     * Notification + Sync + event + other activities
     */
    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        // Responsible for time calculation
        if(!messageDTO.isBOT()) messageDTO.setMsgTypeForResTimeCalc("S");
        getBusinessDTO(messageDTO);
        getCustomerDTO(messageDTO);
        getMessengerContact(messageDTO);
        getUserDTO(messageDTO);

        log.info("Send Google Message request From : {} to : {}", messageDTO.getBusinessDTO().getBusinessId(), messageDTO.getMessengerContact().getGoogleConversationId());

        SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
        SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages, googleUtils.isGoogleSendAvailable(messageDTO.getMessengerContact(), messageDTO.getBusinessDTO().getRoutingId()));
        sendResponse.setLastMsgSource(messageDTO.getSource());
        sendResponse.setMessages(messages);
        return sendResponse;
    }

    private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
        MessengerContact messengerContact = getMessengerContact(messageDTO);

        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        sendMessageDTO.setCustomerId(messengerContact.getCustomerId());
        sendMessageDTO.setSource(Source.GOOGLE.getSourceId());
        sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
        commonService.setMessageBody(sendMessageDTO,getCustomerDTO(messageDTO), "google");
        return handleMultiMediaAndTextMessages(sendMessageDTO);
    }

    private void sendMessageToGoogle(MessageDTO messageDTO, MessengerEvent event) throws Exception {
        String messageId = generateUniqueMessageId();
        prepareAndCallSocialGoogleSend(messageDTO, messageId, getMessengerContact(messageDTO));
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        UserDTO userDTO = getUserDTO(messageDTO);
        ConversationDTO conversationDTO = saveGoogleMessage(messageDTO, messageId);
        messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), conversationDTO.getId());
		if(Objects.nonNull(messageDTO.getMessengerMediaFileDTO())) {
			MessengerMediaFile  messengerMediaFile=new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO());
			messengerMediaFile.setMessageId(conversationDTO.getId());
			messengerMediaFile.setExtension(messageDTO.getMessengerMediaFileDTO().getFileExtension());
			messageDTO.getMessengerMediaFiles().add(messengerMediaFile);
		}
		sendMessageDTO.setConversationDTO(conversationDTO);
		sendMessageService.audit(sendMessageDTO, event, userDTO);
    }

    private String generateUniqueMessageId() {
        return UUID.randomUUID().toString();
    }


    void prepareAndCallSocialGoogleSend(MessageDTO messageDTO, String messageId, MessengerContact messengerContact) {
        SendGoogleMessageSocialDTO sendGoogleMessageSocialDTO =
                SendGoogleMessageSocialDTO.builder().messageDTO(messageDTO).messageId(messageId).conversationId(messengerContact.getGoogleConversationId()).build();
        googleSocialIntegrationService.sendGoogleCallToSocial(sendGoogleMessageSocialDTO, messageDTO.isBOT());
    }


    // UPDATE MESSENGER CONTACT
    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("SEND");
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.GOOGLE.name());
        lastMessageMetadataPOJO.setLastMessageSource(Source.GOOGLE.getSourceId());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        if(!messageDTO.isBOT()) messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        messengerContact.setLastMessage(sendMessageDTO.getBody());

        if (StringUtils.isEmpty(sendMessageDTO.getConversationDTO().getBody())
                && StringUtils.isNotEmpty(sendMessageDTO.getConversationDTO().getMediaUrl())) {
            messengerContact.setLastMessage("Sent an attachment");
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), messengerContact.getGoogleConversationId(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }


    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messengerContact)) {
            SendMessageDTO smsDTO = (SendMessageDTO) messageDTO;
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
                    businessDTO.getBusinessId(), smsDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        return messengerContact;
    }

    /**
     * No Email Notification on Send Events
     */
    @Override
    MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        return null;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if (Objects.isNull(messageId)) {
            SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        publishEventIfRepliedOnUnassignedConversation(messageDTO);
    }

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = businessService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }

    private ConversationDTO saveGoogleMessage(MessageDTO messageDTO, String uniqueMessageId) {

        // Create Data for Google Message

        MessengerContact messengerContact = messageDTO.getMessengerContact();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();

        String senderId = businessDTO.getBusinessId().toString();
        String receiverId = messengerContact.getGoogleConversationId();

        UserDTO userDTO = messageDTO.getUserDTO();

        String message = null;

        if (messageDTO instanceof SendMessageDTO) {
            message = ((SendMessageDTO) messageDTO).getBody();
        }

        GoogleMessage googleMessage = new GoogleMessage();
        googleMessage.setCreateDate(new Date());
        googleMessage.setCustomerId(messengerContact.getCustomerId());
        googleMessage.setRecipientId(receiverId);
        googleMessage.setSenderId(senderId);
        googleMessage.setMessageBody(message);
        googleMessage.setMessageId(uniqueMessageId);
        googleMessage.setStatus(GoogleMessageStatusEnum.SENT.toString());
        googleMessage.setSentOn(new Date());
        googleMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());

        if (messageDTO.getMessengerMediaFileDTO() != null && StringUtils.isNotBlank(messageDTO.getMessengerMediaFileDTO().getUrl())) {
            googleMessage.setMediaURL(messageDTO.getMessengerMediaFileDTO().getUrl());
        }

        // Create Data for Messenger Message
        MessengerMessage messengerMessage = new MessengerMessage();
        messengerMessage.setAccountId(businessDTO.getAccountId());
        messengerMessage.setChannel(MessageDocument.Channel.GOOGLE.name());
        messengerMessage.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND.name());
        messengerMessage.setCreatedDate(new Date());
        messengerMessage.setSentThrough(MessageDocument.SentThrough.WEB.name());

        if (userDTO != null) {
            messengerMessage.setCreatedBy(userDTO.getId());
        }
        messengerMessage.setMessageType(MessageDocument.MessageType.CHAT.name());
        messengerMessage.setMessengerContactId(messengerContact.getId());

        messengerMessageService.saveGoogleMessage(messengerMessage, googleMessage, Optional.empty());

        return new ConversationDTO(googleMessage);
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
            Integer customerId = messageDTO.getMessengerContact().getCustomerId();
            customerDTO = contactService.findByIdNoCaching(customerId);
            messageDTO.setCustomerDTO(customerDTO);
        }
        if(customerDTO.getBlocked()) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.CONTACT_IS_BLOCKED, ComponentCodeEnum.CUSTOMER, HttpStatus.BAD_REQUEST));
        }
        return customerDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if(messageDTO.isBOT()) return MessageTag.UNREAD;
        return MessageTag.INBOX;
    }

    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
        if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
    		messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
    				.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
    						messengerMediaFile.getExtension(),messengerMediaFile.getMessageId().toString()))
    				.collect(Collectors.toList()));
    	}
        messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
        messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }

    SortedSet<Message> handleMultiMediaAndTextMessages(MessageDTO messageDTO) throws Exception {
        SortedSet<Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        String text = sendMessageDTO.getBody();
        // save media files
        List<MessengerMediaFile> mediaUrls = sendMessageDTO.getMediaUrls();
        int mediaCount = mediaUrls.size();
        if(CollectionUtils.isNotEmpty(mediaUrls)) {
        	sendMessageDTO.setMessengerMediaFiles(new ArrayList<MessengerMediaFile>());
        }
        try {
			for (int currMediaIdx = 0; currMediaIdx < mediaCount; currMediaIdx++) {
			    MessengerMediaFile mediaFile = mediaUrls.get(currMediaIdx);
			    sendMessageDTO.setMediaurl(mediaFile.getUrl());
			    sendMessageDTO.setBody(null);
			    MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(mediaFile);
			    messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
			    sendMessageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
			    if (currMediaIdx != 0) {
			        messageDTO.setPartOfExistingMessage(true);
			    }
			    sendMessageToGoogle(sendMessageDTO, MessengerEvent.GOOGLE_SEND);
			}
			// save text if present
			if (StringUtils.isNotEmpty(text)) {
			    sendMessageDTO.setMessengerMediaFileDTO(null);
			    sendMessageDTO.setBody(text);
			   sendMessageToGoogle(sendMessageDTO, MessengerEvent.GOOGLE_SEND);
			}
		} catch (Exception e) {
			log.error("Error occurred while sending Google message: {}",e);
		}
        if(sendMessageDTO.getConversationDTO() == null || sendMessageDTO.getConversationDTO().getId() == null) {
			throw new GoogleMessagingException("Failed to send message to google via social-service integration");
		}
        super.handle(sendMessageDTO);
        Message message = new Message(sendMessageDTO.getUserDTO(), sendMessageDTO.getConversationDTO(),sendMessageDTO.getMessengerMediaFiles(), messageDTO.getBusinessDTO().getTimeZoneId());
        message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
        robinService.updateChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), sendMessageDTO.getUserDTO(),Source.GOOGLE.name());
        allMessages.add(message);
        return allMessages;
    }
}
