package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.util.MessengerUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.AssignmentType;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.UserMessengerNotificationSetting;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.AssignmentService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.NotificationService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.WebhookService;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.UnassignConversationEvent;
import com.birdeye.messenger.util.ControllerUtil;

/**
 * <AUTHOR>
 *
 */
@Service
public class AssignmentServiceImpl implements AssignmentService {

	private static final Logger log = LoggerFactory.getLogger(AssignmentServiceImpl.class);

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private ThreadPoolTaskExecutor poolExecutor;

	@Autowired
	@Lazy
	private AssignmentService assignmentService;

	@Autowired
	private UserService userService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Autowired
	private CommunicationHelperService communicationHelperService;

	@Autowired
	private FirebaseService fcmService;

	@Autowired
	@Lazy
	private NotificationService notificationService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private WebhookService webhookService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private RedisLockService redisLockService;

	@Override
	public ConversationAssignmentResponseDTO assignConversation(ConversationAssignmentRequestDTO request,
			Integer assigner, Integer accountId) {
		log.info("assignConversation request : {}, assigner : {}, accountId : {}", request, assigner, accountId);
		MessengerContact messengerContact = Objects.nonNull(request.getMessengerContact())
				? request.getMessengerContact()
						: messengerContactService.findById(request.getMcId());

		if (Objects.isNull(messengerContact)) {
			log.error("No messenger contact found for mcId in request:{}", request);
			throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		}
		BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
		if(Objects.nonNull(businessDTO) && !businessDTO.getAccountId().equals(accountId)){
			log.info("Messenger contact: {} is not associated with business: {}", request.getMcId(), accountId);
			throw new BadRequestException(ErrorCode.MESSENGER_CONTACT_NOT_ASSOCIATED_BUSINESS);
		}
		String lockKey = "";
		Optional<Lock> lockOpt = Optional.empty();
		if (messengerContact.getCustomerId() != null) {
			lockKey = Constants.CUSTOMER_ID_PREFIX + messengerContact.getCustomerId();
		} else {
			lockKey = Constants.REVIEWER_ID_PREFIX + messengerContact.getReviewerId();
		}
		try {
			lockOpt = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				// Reject such events
				log.info("[Assignment] Unable to acquire lock key:{}", lockKey);
				throw new MessengerException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
			return commonAssignment(request, assigner, accountId, messengerContact);
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private ConversationAssignmentResponseDTO commonAssignment(ConversationAssignmentRequestDTO request,
			Integer assigner, Integer accountId, MessengerContact messengerContact) {
		messengerContact= messengerContactService.findById(messengerContact.getId());
		if (messengerContact.getCurrentAssignee() != null && messengerContact.getCurrentAssignee() > 0
				&& request.getTo()!=null && request.getTo().getId()!=null && messengerContact.getCurrentAssignee().equals(request.getTo().getId())) {
			log.info("Conversation already assigned to {}", messengerContact.getCurrentAssignee());
		}
		else {
			MessengerContact conversation = updateAssignment(request, assigner, accountId, messengerContact);
			log.info("[Assignment] triggering secondary activities asynchronously for conversation:{}", request.getMcId());
			assignmentService.triggerSecondaryActionsAsync(conversation, request, assigner, accountId);
		}
		return new ConversationAssignmentResponseDTO(request.getMcId(), assigner, request.getFrom(), request.getTo());
	}

	private Optional<MessageDocument> trackActivity(MessengerContact conversation,
			ConversationAssignmentRequestDTO request, Integer assigner,
			Integer accountId) {
		log.info("[AssignmentUpdate] tracking activity for conversation:{}", request.getMcId());
		UserDTO assignerDetails = userService.getUserDTO(assigner);
		ActivityType activityType = fetchActivityType(request, assigner, accountId);

		if (activityType == null) {
			log.error("Bad assignment request for request:{}", request);
			throw new BadRequestException(ErrorCode.INCORRECT_ASSIGNMENT_ACTIVITY);
		}

		Integer assignerId = assigner;
		Integer fromId = request.getFrom() != null ? request.getFrom().getId() : null;
		String fromName = request.getFrom() != null ? request.getFrom().getName() : null;
		Integer toId = request.getTo() != null ? request.getTo().getId() : null;
		String toName = request.getTo() != null ? request.getTo().getName() : null;
		String assignerName = assignerDetails != null ? assignerDetails.getName() : null;

		ActivityDto activityDto = ActivityDto.builder().mcId(conversation.getId()).created(new Date())
				.actorId(assignerId).actorName(assignerName).activityType(activityType).from(fromId).fromName(fromName)
				.to(toId).toName(toName).accountId(accountId).businessId(conversation.getBusinessId())
				.activityMeantFor(ActivityMeantFor.BUSINESS_USER).build();

		ConversationActivity conversationActivity = null;

		try {
			conversationActivity = conversationActivityService.create(activityDto);
		} catch (Exception e) {
			log.error("[AssignmentUpdate] Assignment activity tracking failed for the request: {}", request, e);
			return Optional.empty();
		}
		MessageDocument assignmentActivity = new MessageDocument(activityDto);

		try {
			messengerContactService.addNewMessageDocOnES(assignmentActivity,
					conversationActivity.getId() + "_a");
		} catch (Exception e) {
			log.error(
					"[AssignmentUpdate] Some error occured while publishing activity in es for request -> Ignoring:{}",
					request, e);
			return Optional.empty();
		}
		return Optional.ofNullable(assignmentActivity);
	}

	private ActivityType fetchActivityType(ConversationAssignmentRequestDTO request, Integer assigner,
			Integer accountId) {
		String assignmentType = request.getTo() != null && StringUtils.isNotEmpty(request.getTo().getType())
				? request.getTo().getType()
				: "U";

		if (assignmentType.equalsIgnoreCase("T")) {
			return ActivityType.TEAM_ASSIGN;
		}
		if (request.isAssigned()) {
			return (assigner != null && assigner != 0) ? ActivityType.ASSIGNED : ActivityType.AUTO_ASSIGN;
		} else if (request.isReassignedAssigned()) {
			return ActivityType.REASSIGNED;
		} else if (request.isUnassigned()) {
			return (assigner != null && assigner != 0) ? ActivityType.UNASSIGNED : ActivityType.AUTO_UNASSIGN;
		}
		return null;
	}

	private void checkAndEmailAssignee(ConversationAssignmentRequestDTO request, BusinessDTO businessDTO,
			Integer assigner, MessengerGlobalFilter notificationRequest) {
		boolean selfAssignment = assigner != null && request.getTo() != null && request.getTo().getId().equals(assigner)
				&& Constants.Assignment.USER_ASSIGNED.equalsIgnoreCase(request.getTo().getType());
		boolean autoAssignment = assigner == null;
		boolean userAssignment = false;
		boolean teamAssignment = false;
		if (request.getTo() != null) {
			userAssignment = request.getTo().getType().equalsIgnoreCase(Constants.Assignment.USER_ASSIGNED);
			teamAssignment = request.getTo().getType().equalsIgnoreCase(Constants.Assignment.TEAM_ASSIGNED);
		}

		boolean notify = !autoAssignment && !selfAssignment  && !request.isDoNotNotify();
		if (notify) {
			if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(notificationRequest.getEmailIds())){
				notificationService.sendEmailNotificationV2(notificationRequest, null);
				return;
			}
			Map<String, Set<String>> notificationTypeToUserEmails = notificationRequest.getNotificationTypeToUserEmailMap();
			if (notificationTypeToUserEmails != null && !notificationTypeToUserEmails.isEmpty()) {
				notificationTypeToUserEmails.forEach((notificationType, emails) -> {
					// Set the current type and emails in the notificationMetaData if needed
					if(Constants.ASSINGNED_TO_OTHERS.equals(notificationType)) {
						notificationRequest.setAssignToOthers(true);
					}else{
						notificationRequest.setAssignToOthers(false);
					}
					notificationRequest.setEmailIds(emails);
					notificationService.sendEmailNotificationV2(notificationRequest, null);
				});
			}
		}
	}

	private boolean notifyOnAssignmentConfigured(Integer mcId, Integer assignedTo, Integer applicableAccountId) {
		UserMessengerNotificationSetting userNotificationSettings = businessService
				.getUserNotificationSettings(applicableAccountId, assignedTo);
		log.info("[AssignmentUpdate] User Notification settings received for mcId:{} & assignee:{} are {}", mcId,
				assignedTo, userNotificationSettings);
		if (userNotificationSettings.getAssignedToMeAlert() != null
				&& userNotificationSettings.getAssignedToMeAlert().equals(1)) {
			return true;
		}
		return false;
	}

	private MessengerContact updateAssignment(ConversationAssignmentRequestDTO request, Integer assigner,
			Integer accountId, MessengerContact conversation) {
		conversation = assignmentService.updateAssignmentInPrimaryStore(request, assigner, accountId, conversation);
		boolean updated = updateAssignmentInReadStore(request, assigner, accountId, conversation);
		// TODO - In case of failure revert the DB Transaction and return error
		if (!updated) {
			// TODO - Revert the DB changes and throw error
		}
		return conversation;
	}

	/*
	 * 
	 * 1) Validate -> Assignment permissions to the assignee (Improvement) 2) Fetch
	 * the details of Current Assignee (id & name from DB) 3) Get the details of the
	 * new assignee (based on id -> Improvement) - Currently name of the current
	 * assignee is to be taken from BE 4) Save the new assignee in primary store 5)
	 * Update read store with the data
	 */

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public MessengerContact updateAssignmentInPrimaryStore(ConversationAssignmentRequestDTO request, Integer assigner,
			Integer accountId, MessengerContact conversation) {
		log.info("[Assignment] updating assignment for conversation {} in primary store", request.getMcId());

		Integer toAssigneeId = request.getTo() != null ? request.getTo().getId() : null;
		String toAssigneeName = request.getTo() != null ? request.getTo().getName() : null;
		String toAssigneeType = request.getTo() != null ? request.getTo().getType() : "U";
		String toAssigneeEmailId = request.getTo() != null ? request.getTo().getEmailId() : null;

		MessengerContact messContact = conversation;

		messContact.setCurrentAssigner(assigner);
		if (toAssigneeType.equalsIgnoreCase("T")) {
			// If conversation is assigned to a team , then the same is not present in a
			// user's assignment bucket
			messContact.setAssignmentType(AssignmentType.T);

			messContact.setCurrentAssignee(null);
			messContact.setCurrentAssigneeName(null);

			messContact.setTeamId(toAssigneeId);
			messContact.setTeamName(toAssigneeName);

		} else {
			// Conversation assigned to a user should preserve the team assignment happened
			// earlier
			messContact.setAssignmentType(AssignmentType.U);
			messContact.setCurrentAssignee(toAssigneeId);
			messContact.setCurrentAssigneeName(toAssigneeName);

		}

		messContact.setUpdatedAt(new Date());
		if (request.isSaveInDBAndES()) {
			messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(messContact);
		}
		return messContact;
	}

	private boolean updateAssignmentInReadStore(ConversationAssignmentRequestDTO request, Integer assigner,
			Integer accountId, MessengerContact messengerContact) {
		// TODO - Fix the hack of making current assigneeId as -100000 in case of
		// unassignment in es
		log.info("[Assignment] updating assignment for conversation {} in read store", request.getMcId());
		Integer toAssigeeId = request.getTo() != null ? request.getTo().getId() : Constants.Elastic.UNASSIGNED_ID;
		String toAssigneeName = request.getTo() != null ? request.getTo().getName() : Constants.Elastic.UNASSIGNED_NAME;
		String toAssigneeType = request.getTo() != null ? request.getTo().getType() : "U";

		ContactDocument conversationDoc = new ContactDocument();
		conversationDoc.setType(ContactDocument.Type.UPDATE.getType());

		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		conversationDoc.setUpdatedAt(df.format(messengerContact.getUpdatedAt()));

		if (toAssigneeType.equalsIgnoreCase("T")) {
			conversationDoc.setAssignmentType(toAssigneeType);

			conversationDoc.setCr_asgn_id(Constants.Elastic.UNASSIGNED_ID);
			conversationDoc.setCr_asgn_name(Constants.Elastic.UNASSIGNED_NAME);

			conversationDoc.setTeam_id(toAssigeeId);
			conversationDoc.setTeam_name(toAssigneeName);

		} else {
			conversationDoc.setAssignmentType(toAssigneeType);
			conversationDoc.setCr_asgn_id(toAssigeeId);
			conversationDoc.setCr_asgn_name(toAssigneeName);
		}

		if (Objects.nonNull(messengerContact.getSpam())) {
			conversationDoc.setSpam(messengerContact.getSpam());
		}

		try {
			if (request.isSaveInDBAndES()) {
				messengerContactService.updateContactOnES(request.getMcId(), conversationDoc, accountId);
			}
		} catch (Exception e) {
			log.error("Unable to update assignee:{} in messenger contact document for mcId:{} ", request.getTo(),
					request.getMcId());
			return false;
		}
		return true;
	}

	public MessengerGlobalFilter getEmailNotificationMetaData(ConversationAssignmentRequestDTO request,
			BusinessDTO businessDTO, Integer assigner) {
		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		UserDTO userDTO = null;
		if (assigner != null) {
			userDTO = communicationHelperService.getUserDTO(assigner);
		}
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.ASSIGNED_TO);
		notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(5); // number of messages to be fetched from ES
		notificationRequest.setConversationId(request.getMcId());
		Set<String> emailIds = new HashSet<>();
		Map<String, Set<String>> notificationTypeToUserEmails = new HashMap<>();

		boolean unAssign = request.getTo() == null;
		notificationRequest.setUnassign(unAssign);

		if (request.getTo() != null
				&& request.getTo().getType().equals(Constants.Assignment.TEAM_ASSIGNED)) {
			Integer teamId = request.getTo().getId();
			UserIdEmailIdDTO emails = businessService.getEmailConfigOfUsersForTeamsAssignment(teamId,
					businessDTO.getBusinessId());
			emailIds.addAll(emails.getUserIdEmailIdMap().values());
			// TO PREPARE TEMPLATE FOR TEAM OTHER WISE USER
			notificationRequest.setMessageForTeam(true);
			// DO NOT NOTIFY WHO ASSIGN THE CONVERSATION TO A TEAM
			if (userDTO != null) {
				emailIds.remove(userDTO.getEmailId());
			}
		} else {
			Map<String, Map<String, Map<String, Map<String, String>>>> webChatIntervalToUserNotifications = userService
					.getInboxUserEmailAlertSettings(notificationRequest.getBizId(), NotificationRequestType.ASSIGNED_TO_CONVERSATION.toString(), null);
			if (MapUtils.isNotEmpty(webChatIntervalToUserNotifications)) {
				webChatIntervalToUserNotifications.forEach((webChatInterval, messengerUserNotificationMap) -> {
					Map<String, Set<String>> userEmailIds = getSendNotificationEmailIds(
							messengerUserNotificationMap,
							notificationRequest,
							request,
							businessDTO,
							assigner
					);
					if (MapUtils.isNotEmpty(userEmailIds)) {
						userEmailIds.forEach((notificationType, emails) -> {
							notificationTypeToUserEmails
									.computeIfAbsent(notificationType, k -> new HashSet<>())
									.addAll(emails);
						});
					}
				});
			}
		}
		notificationRequest.setEmailIds(emailIds);
		notificationRequest.setNotificationTypeToUserEmailMap(notificationTypeToUserEmails);
		notificationRequest.setUserDTO(userDTO);
		return notificationRequest;
	}

	/*
	 * 1. Publish activity
	 * 2. SYNC
	 * 3. Trigger push notification
	 * 4. Trigger Email
	 * 
	 */
	@Override
	@Async
	public void triggerSecondaryActionsAsync(MessengerContact conversation, ConversationAssignmentRequestDTO request,
			Integer assigner,
			Integer accountId) {

		Optional<MessageDocument> activityDocOpt = Optional.empty();

		if (!request.isDoNotPublishActivity()) { /*****
													 * isDoNotPublishActivity -> Notifications will be restricted as
													 * well
													 ***/
			activityDocOpt = trackActivity(conversation, request, assigner, accountId);
		}

		BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(conversation.getBusinessId());

		List<MessageDocument> last2MessagesFromES = null;

			// TODO Remove getEmailNotificationMetaData Unnecessary call
		MessengerGlobalFilter notificationRequest = getEmailNotificationMetaData(request, businessDTO, assigner);
		notificationRequest.setCount(5);
		notificationRequest.setQueryFile(Constants.Elastic.GET_MESSAGES_V1);
		notificationRequest.setExcludedSources(
				ControllerUtil.toCommaSeparatedString(commonService.getExcludedMessageSourcesList()));
		last2MessagesFromES = messengerContactService.getMessagesFromES(notificationRequest);


		MessageDocument messageDocument = null;
		if (!CollectionUtils.isEmpty(last2MessagesFromES)) {
			messageDocument = last2MessagesFromES.get(0);
		}

		if (activityDocOpt.isPresent() && !request.isBulkAction()) {
			syncAndSendPushNotification(activityDocOpt.get(), accountId, conversation.getId(), assigner,
					messageDocument);
		} else {
			log.warn(
					"[AssignmentUpdate] Unable to push assignment changes in read store for request:{}, hence mirroring & Notification may not work",
					request);
		}
		checkAndEmailAssignee(request, businessDTO, assigner,notificationRequest);
		ConversationWebhookEventDTO conversationWebhookDTO = webhookService
				.mapMessengerContactToConversationWebhookDTO(conversation);
		webhookService.publishMessengerWebhookEvent(conversationWebhookDTO, accountId,
				WebhookEventEnum.CONVERSATION_UPDATED.getName(), conversation.getBusinessId());
	}

	private void syncAndSendPushNotification(MessageDocument messageDocument, Integer accountId, Integer conversationId,
			Integer assigner, MessageDocument lastMessage) {
		MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
		messengerBaseFilter.setAccountId(accountId);
		messengerBaseFilter.setConversationId(conversationId);
		messengerBaseFilter.setCount(1);
		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
		if (!CollectionUtils.isEmpty(contactDocuments)) {
			fcmService.pushToFireBase(contactDocuments.get(0), messageDocument, assigner, lastMessage);
		}
	}

	@Override
	public void getConversationsAssignedToTeam(DeleteTeamEvent deleteTeamEvent) {
		if (deleteTeamEvent != null && deleteTeamEvent.getTeamId() != null && deleteTeamEvent.getAccountId() != null) {
			Integer teamId = deleteTeamEvent.getTeamId();
			Integer accountId = deleteTeamEvent.getAccountId();
			List<Integer> teamIds = new ArrayList<>();
			teamIds.add(teamId);
			ConversationRequest conversationRequest = new ConversationRequest();
			conversationRequest.setCount(null);
			conversationRequest.getFilters().setTeamAssignedTo(teamIds);
			ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(conversationRequest);
			freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_BY_TEAM_ID);
			ElasticData conversationData = messengerContactService.getConversationData(freeMarkerData,
					deleteTeamEvent.getAccountId());
			ConversationResponse response = new ConversationResponse();
			if (!conversationData.isSucceeded()) {
				log.error("getConversationV2: Failed to fetch data from ES");
				// throw new MessengerException("Failed to fetch conversation data from ES");
			}
			response.setCount(conversationData.getTotal());
			List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();
			PublishUnassignConversationEvents(contactDocuments, teamId, accountId);
		}

	}

	@Override
	public void unassignConversation(UnassignConversationEvent unassignConversationEvent) {
		if (unassignConversationEvent != null && unassignConversationEvent.getMessengerContactId() != null) {
			MessangerBaseFilter messengerBaseFilter = new MessangerBaseFilter();
			messengerBaseFilter.setAccountId(unassignConversationEvent.getAccountId());
			messengerBaseFilter.setConversationId(unassignConversationEvent.getMessengerContactId());
			messengerBaseFilter.setCount(1);
			List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(messengerBaseFilter);
			if (!CollectionUtils.isEmpty(contactDocuments) && unassignConversationEvent.getUserId() != null
					&& contactDocuments.get(0).getCr_asgn_id() != null
					&& contactDocuments.get(0).getCr_asgn_id().equals(unassignConversationEvent.getUserId())) {
				log.info("Unassigning conversation Id: {} assigned to User Id {}",
						unassignConversationEvent.getMessengerContactId(), unassignConversationEvent.getUserId());
				unassignConversation(unassignConversationEvent.getMessengerContactId(),
						unassignConversationEvent.getAccountId());
			} else if (!CollectionUtils.isEmpty(contactDocuments) && unassignConversationEvent.getTeamId() != null
					&& contactDocuments.get(0).getTeam_id().equals(unassignConversationEvent.getTeamId())
					&& contactDocuments.get(0).assignedToTeamOnly()) {
				log.info("Unassigning conversation Id: {} assigned to team Id {}",
						unassignConversationEvent.getMessengerContactId(), unassignConversationEvent.getTeamId());
				unassignConversation(unassignConversationEvent.getMessengerContactId(),
						unassignConversationEvent.getAccountId());
			}

		}

	}

	private void unassignConversation(Integer mcId, Integer accountId) {
		ConversationAssignmentRequestDTO requestDTO = populateAssignmentRequest(mcId);
		assignmentService.assignConversation(requestDTO, null, accountId);
	}

	private ConversationAssignmentRequestDTO populateAssignmentRequest(Integer mcId) {
		ConversationAssignmentRequestDTO requestDTO = new ConversationAssignmentRequestDTO();
		requestDTO.setDoNotNotify(true);
		requestDTO.setMcId(mcId);
		requestDTO.setTo(null);
		return requestDTO;
	}

	private void PublishUnassignConversationEvents(List<ContactDocument> contactDocuments, Integer teamId,
			Integer accountId) {
		List<UnassignConversationEvent> unassignConversationEvents = contactDocuments
				.stream().filter(
						contactDocument -> contactDocument.getAssignmentType().equals("T"))
				.collect(
						Collectors.mapping(contactDocument -> new UnassignConversationEvent(contactDocument.getM_c_id(),
								teamId, null, accountId), Collectors.toList()));
		for (UnassignConversationEvent unassignConversationEvent : unassignConversationEvents) {
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.UNASSIGN_CONVERSATION, null, unassignConversationEvent);
		}
	}

	@Override
	@Transactional
	public void messengerAccessRevoked(UserEvent userEvent) {
		unassignConversation(userEvent);
		if (UserEventEnum.USER_REMOVED_ACCOUNT.equals(userEvent.getUserEventEnum())
				|| UserEventEnum.USER_ROLE_CHNAGED.equals(userEvent.getUserEventEnum())) {
			removeUserTeamsMapping(userEvent);
		}
	}

	private void removeUserTeamsMapping(UserEvent userEvent) {
		businessService.removeUserTeamsMapping(userEvent);
	}

	private void unassignConversation(UserEvent userEvent) {
		ConversationRequest conversationRequest = new ConversationRequest();
		Integer accountId = userEvent.getAccountId();
		conversationRequest.setCount(Constants.Elastic.FETCH_SIZE);
		conversationRequest.getFilters().setAssignedTo(Collections.singletonList(userEvent.getUserId()));
		conversationRequest.getFilters().setBusinessIds(userEvent.getBusinessIds());
		ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(conversationRequest);
		freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V3);
		ElasticData conversationData = messengerContactService.getConversationData(freeMarkerData,
				userEvent.getAccountId());
		List<ContactDocument> contactDocuments = (List<ContactDocument>) conversationData.getResults();
		contactDocuments.forEach(doc -> {
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.UNASSIGN_CONVERSATION, null,
					new UnassignConversationEvent(doc.getM_c_id(), null, userEvent.getUserId(), accountId));

		});
	}

	private Map<String, Set<String>> getSendNotificationEmailIds(Map<String,Map<String,Map<String,String>>> messengerUserNotificationMap,
												   MessengerGlobalFilter notificationRequest, ConversationAssignmentRequestDTO request,
													BusinessDTO businessDTO, Integer assigner) {
		notificationRequest.setStartIndex(0);
		Map<String, Set<String>> userEmails = new HashMap<>();
		notificationRequest.setCount(1);
		ContactDocument contact = messengerContactService.getContact(notificationRequest);
		Integer cr_assigned_to = contact.getCr_asgn_id();

		boolean unAssign = request.getTo() == null;

		Map<String,Map<String,String>> usersNotificationsSettingsMap=messengerUserNotificationMap.get("conversationTypeConfigMap");
		if(MapUtils.isNotEmpty(usersNotificationsSettingsMap)) {
			usersNotificationsSettingsMap.forEach((noticationType, usersMap) -> {
				if (contact.assignedToUser() && Constants.ASSINGNED_TO_ME.equals(noticationType)) {
					usersMap.forEach((userId, userEmail) -> {
						if (request.getTo() != null && userId.equals(request.getTo().getId().toString())) {
							userEmails.computeIfAbsent(noticationType, k -> new HashSet<>()).add(userEmail);
						}
					});
				} else if (contact.assignedToUser() && Constants.ASSINGNED_TO_OTHERS.equals(noticationType)) {
					usersMap.forEach((userId, userEmail) -> {
						if (!userId.equals(cr_assigned_to.toString())) {
							userEmails.computeIfAbsent(noticationType, k -> new HashSet<>()).add(userEmail);
							notificationRequest.setAssignToOthers(true);
						}
					});
				} else if (contact.unassigned() && Constants.UNASSIGNED.equals(noticationType) && unAssign) {
					usersMap.forEach((userId, userEmail) -> {
						userEmails.computeIfAbsent(noticationType, k -> new HashSet<>()).add(userEmail);
					});
				}

			});
		}
		return userEmails;
	}
}