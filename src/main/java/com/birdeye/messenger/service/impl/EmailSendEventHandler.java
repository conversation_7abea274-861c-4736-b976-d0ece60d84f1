/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TimeZone;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.payment.CurrencyConstant;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.EmailMessageConstants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.EmailCampaignRequest;
import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.EmailSenderDto;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.NexusEmailDTO.EmailFileAttachementData;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.PaymentAutomationUser;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.CampaignService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.service.payment.impl.PaymentNotificationName;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmailSendEventHandler extends MessageEventHandlerAbstract{

	@Autowired
	protected SendMessageService sendMessageService;

	private final MessengerEvent EVENT = MessengerEvent.EMAIL_SEND;

	@Autowired
	protected MessengerMessageService messengerMessageService;

	@Autowired
	protected FacebookEventService facebookEventService;

	@Autowired
	protected CommonService commonService;

	@Autowired
	protected ContactService contactService;

	@Autowired
	protected EmailService emailService;

	@Value("${email.reply.domain}")
	private String emailReplyDomain;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private CampaignService campaignService;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	private final String RESELLER = "Whitelabel";
	
	private final WhatsappMessageService whatsappMessageService;

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		if (Objects.isNull(businessDTO) || Objects.isNull(businessDTO.getTimeZoneId())){
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
			businessDTO = communicationHelperService.getBusinessDTO(businessId);
			dto.setBusinessDTO(businessDTO);
		}
		return businessDTO;
	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (Objects.isNull(customerDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			//customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
			customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
			dto.setCustomerDTO(customerDTO);
		}
		return customerDTO;
	}

	@Override
	UserDTO getUserDTO(MessageDTO messageDTO) {
		UserDTO userDTO = messageDTO.getUserDTO();
		if (Objects.isNull(userDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			userDTO = communicationHelperService.getUserDTO(dto.getUserId());
			dto.setUserDTO(userDTO);
		}
		return userDTO;
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		if (messageDTO.getSource().equals(Source.WEB_CHAT.getSourceId())) {
			messageDTO.setMessageTag(MessageTag.UNREAD);
		} else {
			messageDTO.setMessageTag(MessageTag.INBOX);
		}
		return messageDTO.getMessageTag();
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		SendMessageDTO dto = (SendMessageDTO) messageDTO;
		MessageDocumentDTO messageDocumentDTO =  new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
		if(CollectionUtils.isNotEmpty(dto.getMessengerMediaFileList())) {
			messageDocumentDTO.setMediaFiles(dto.getMessengerMediaFileList().stream().map(attachement -> new MessageDocument.MediaFile(attachement.getFileExtension(), attachement.getUrl(), attachement.getContentSize(), attachement.getName(), attachement.getContentType())).collect(
					Collectors.toList()));
		}
		if (dto.getTemplateId() != null) {
			messageDocumentDTO.setTemplateId(dto.getTemplateId());
		}
		if(BooleanUtils.isTrue(messageDTO.isSecureFaq())) {
			messageDocumentDTO.setSecureFaq(messageDTO.isSecureFaq());
		}
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		return messageDocumentDTO;
	}

	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		messengerContact.setLastResponseAt(((SendMessageDTO) messageDTO).getConversationDTO().getSentOn());
		messengerContact.setLastMsgOn(new Date());
		messengerContact.setUpdatedAt(new Date());
		UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.EMAIL.name());
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.EMAIL.getSourceId());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		ConversationDTO conversationDTO = sendMessageDTO.getConversationDTO();
		if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
			messengerContact.setLastMessage("Sent an attachment");
		} else {
			messengerContact.setLastMessage(sendMessageDTO.getBody());
		}
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(),
				conversationDTO.getRecipient(), conversationDTO.getSender());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}


	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService
					.getOrCreateContactForExistingCustomer(businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(),
							businessDTO.getAccountId());
			messageDTO.setMessengerContact(messengerContact);
		}
		messengerContact.setTemplateId(sendMessageDTO.getTemplateId());
		return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if (Objects.isNull(messageId)) {
			SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
			messageId = sendMessageDTO.getConversationDTO().getId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	@Override
	public SendResponse handle(MessageDTO messageDTO) throws Exception {
		messageDTO.setMsgTypeForResTimeCalc("S");
		MessengerContact contact = getMessengerContact(messageDTO);
		SendMessageDTO dto = (SendMessageDTO) messageDTO;
		dto.setCustomerId(contact.getCustomerId());
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		if(customerDTO.getBlocked()) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.CONTACT_IS_BLOCKED, ComponentCodeEnum.CUSTOMER)
					.message("Contact - {} is blocked {}", customerDTO.getId()));
		}
		SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		int routeId = businessDTO.getEnterpriseId() != null
				? businessDTO.getEnterpriseId()
				: businessDTO.getBusinessId();
		SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages,
				facebookEventService.isFBSendAvailable(getMessengerContact(messageDTO), routeId), true);
		WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(getMessengerContact(messageDTO), routeId);
		sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
		sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
		sendResponse.setMessages(messages);
		sendResponse.setLastMsgSource(messageDTO.getSource());
		// handle PulseSurveyContext
		PulseSurveyContext context = null;
		try {
			context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		ContactDocument contactDocument = new ContactDocument();
		if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
			contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
		} else {
			contactDocument.setOngoingPulseSurvey(false);
		}
		messengerContactService.updateContactOnES(messageDTO.getMessengerContact().getId(), contactDocument, messageDTO.getBusinessDTO().getAccountId());
		return sendResponse;
	}

	private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		getBusinessDTO(messageDTO);
		sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
		if (!Source.MOBILE.getSourceId().equals(messageDTO.getSource())) {
			sendMessageDTO.setSource(Source.EMAIL.getSourceId());
		}
		sendMessageDTO.setFromBusinessId(messageDTO.getBusinessDTO().getBusinessId());
		MessengerEvent event = MessengerEvent.EMAIL_SEND;
		messageDTO.setSentThrough(SentThrough.WEB);

		updateCustomerDTO(sendMessageDTO);

		SortedSet<MessageResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
		List<MessengerMediaFileDTO> messengerMediaFiles = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			String text = sendMessageDTO.getBody();
			for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
				MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
				sendMessageDTO.setMediaurl(mediaFile.getUrl());
				if (media < sendMessageDTO.getMediaUrls().size() - 1) {
					sendMessageDTO.setBody(text);
				}
				MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(mediaFile);
				messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
				messengerMediaFileDTO.setData(mediaFile.getData());
				messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
				messengerMediaFiles.add(messengerMediaFileDTO);
				if (media != 0) {
					messageDTO.setPartOfExistingMessage(true);
				}
			}
			messageDTO.setMessengerMediaFileList(messengerMediaFiles);
		}
		messages.add(sendEmail(messageDTO, event, messengerMediaFiles));
		return messages;
	}

	private void updateCustomerDTO(SendMessageDTO sendMessageDTO) {
		//	CustomerDTO customerDTO = contactService.findById(sendMessageDTO.getCustomerId());
		//CustomerDTO customerDTO = communicationHelperService.getCustomerDTO(sendMessageDTO.getCustomerId());
		CustomerDTO customerDTO = contactService.findByIdNoCaching(sendMessageDTO.getCustomerId());
		if(customerDTO != null) {
			sendMessageDTO.setCustomerDTO(customerDTO);
		}
	}

	private SendResponse.Message sendEmail(MessageDTO messageDTO, MessengerEvent event,
										   List<MessengerMediaFileDTO> messengerMediaFiles) throws Exception {

		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;

		String emailBody = sendMessageDTO.getBody();
		if(Objects.nonNull(messageDTO.getPaymentRequest())) {
			emailBody = sendMessageDTO.getPaymentRequestEmailBody();
		}

		UserDTO userDTO = getUserDTO(messageDTO);
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		/**
		 * Throw exception when email id is not available
		 */

		vaidateEmail(customerDTO.getEmailId());
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		Email email = null;
		ConversationDTO conversationDTO = null;
		String replayToEmailId = getReplyTo(businessDTO);
		sendMessageDTO.setReplyToEmailId(replayToEmailId);
		sendMessageDTO.setToEmailId(customerDTO.getEmailId());
		sendMessageDTO.setFromEmailId(businessDTO.getBirdEyeEmailId());
		sendMessageDTO.setEncrypted(1);

		if (sendMessageDTO.getTemplateId() != null) {
			email = emailService.saveCampaignEmail(sendMessageDTO, String.valueOf(businessDTO.getBusinessNumber()),
					String.valueOf(getMessengerContact(messageDTO).getId()));
			conversationDTO = new ConversationDTO(email);
			conversationDTO.setSentThrough(messageDTO.getSentThrough());
			conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
			conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
			conversationDTO.setRecipient(String.valueOf(getMessengerContact(messageDTO).getId()));
			conversationDTO.setTemplateId(sendMessageDTO.getTemplateId());
			sendMessageDTO.setConversationDTO(conversationDTO);
			publishActivity(messageDTO);
			messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);
			EmailCampaignRequest campaignRequest = createCampaignRequest(sendMessageDTO, email,
					getFromNameFromCoreService(businessDTO.getBusinessId(), businessDTO.getBusinessName(),
							userDTO),
					fetchFromEmailId(businessDTO.getBusinessName(), businessDTO.getBusinessId()), userDTO);
			campaignService.createEmailCampaignRequest(campaignRequest);
		} else {
			sendMessageDTO.setEmailSubject(getEmailSubject(sendMessageDTO, getBusinessDTO(messageDTO).getBusinessName()));
			email = emailService.saveEmail(sendMessageDTO, String.valueOf(businessDTO.getBusinessNumber()), String.valueOf(getMessengerContact(messageDTO).getId()));
			EmailDTO emailDTO = populateEmailDto(messageDTO, messengerMediaFiles, userDTO, email, emailBody, customerDTO, businessDTO);
			conversationDTO = new ConversationDTO(email, emailDTO);
			conversationDTO.setSentThrough(messageDTO.getSentThrough());
			conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
			conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
			conversationDTO.setRecipient(String.valueOf(getMessengerContact(messageDTO).getId()));
			sendMessageDTO.setConversationDTO(conversationDTO);
			// adding activity log for send email
			publishActivity(messageDTO);
			messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);
			String key="email-via-inbox-custom-template-"+businessDTO.getBusinessId();
			Integer isCustomTemplate = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty(key, 0);
			emailDTO.setCustomTemplate(BooleanUtils.toBoolean(isCustomTemplate));
			sendMessageService.sendEmail(emailDTO);
			saveMediaFiles(messengerMediaFiles, conversationDTO.getId());
		}
		sendMessageService.pushSendRequestToKafka(sendMessageDTO, event, getUserDTO(messageDTO), false);
		super.handle(sendMessageDTO);
		MessageResponse.Message message = new SendResponse.Message(userDTO, conversationDTO, getMessengerMediaFiles(messengerMediaFiles), messageDTO.getBusinessDTO().getTimeZoneId());
		message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
		return message;
	}

	private void publishActivity(MessageDTO messageDTO) {
		addActivityIfChannelChanged(messageDTO, getMessengerContact(messageDTO));
		if (messageDTO.getActivityMessage() != null) {
			conversationActivityService.persistActivityForChannel(messageDTO);
			// Taking pause to add delay between activity and message
			try {
				Thread.sleep(1000);
			} catch (Exception e) {
				log.error("Getting exception while taking pause isLiveChatSessionActive method ");
			}
		}
	}

	@Override
	public void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}

	@Override
	protected boolean isFireBaseSyncEnabled(MessageDTO messageDTO) {
		//handle for robin autoreply in livechat
		boolean isFireBaseSyncEnabled = true;
		if (messageDTO.getUserDTO()!=null && messageDTO.getUserDTO().getId() == MessengerConstants.ROBIN_REPLY_USER) {
			isFireBaseSyncEnabled = false;
		}
		return isFireBaseSyncEnabled;
	}

	public EmailDTO populateEmailDto(MessageDTO messageDTO, List<MessengerMediaFileDTO> messengerMediaFiles, UserDTO userDTO, Email email, String emailBody,
									 CustomerDTO customerDTO, BusinessDTO businessDTO) {

		EmailDTO emailDTO = new EmailDTO();
		emailDTO.setBusinessId(String.valueOf(businessDTO.getBusinessId()));
		emailDTO.setSubject(getEmailSubject((SendMessageDTO) messageDTO, businessDTO.getBusinessName()));
		emailDTO.clearAndAddTo(customerDTO.getEmailId());
		emailDTO.setFrom(fetchFromEmailId(businessDTO.getBusinessName(), businessDTO.getBusinessId()));
		emailDTO.setReplyTo(getReplyTo(businessDTO));
		emailDTO.setRequestType(Constants.EMAIL_VIA_INBOX);
		emailDTO.setRequestSubType(Constants.EMAIL_VIA_INBOX);
		emailDTO.setEmailCategory(Constants.MARKETING);
		if(Objects.nonNull(messageDTO.getPaymentRequest())) {
			emailDTO.setRequestType(Constants.PAYMENT_EMAIL_TEMPLATE);
			emailDTO.setRequestSubType(Constants.PAYMENT_EMAIL_TEMPLATE);
			emailDTO.setEmailCategory(Constants.SERVICE);
		}
		emailDTO.setFromName(getFromNameFromCoreService(businessDTO.getBusinessId(), businessDTO.getBusinessName(), userDTO));
		emailDTO.setBusinessNumber(businessDTO.getBusinessNumber());
		emailDTO.setS3AttachementUrls(getAttachmentS3Urls(messengerMediaFiles));
		emailDTO.setFileAttachementData(getFileAttachment(messengerMediaFiles));
		emailDTO.setExternalUid(String.valueOf(email.getId()));
		emailDTO.setDataObject(buildSendEmailTemplate(emailBody, businessDTO, messageDTO, emailDTO));
		emailDTO.setTempBody(emailBody);
		emailDTO.setRecipientType(Constants.CUSTOM_RECIPIENT_TYPE);
		emailDTO.setIsCustomerEmail(1);
		emailDTO.setCustomerId(customerDTO.getId());
		return emailDTO;
	}

	private void saveMediaFiles(List<MessengerMediaFileDTO> messengerMediaFiles, Integer conversationId) {
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			for (MessengerMediaFileDTO mediaFiles : messengerMediaFiles) {
				sendMessageService.saveMediaFile(mediaFiles, conversationId);
			}
		}
	}

	private List<String> getAttachmentS3Urls(List<MessengerMediaFileDTO> messengerMediaFiles) {
		List<String> attachmentS3Urls = new ArrayList<String>();
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			for (MessengerMediaFileDTO mediaFile : messengerMediaFiles) {
				attachmentS3Urls.add(mediaFile.getUrl());
			}
		}
		return attachmentS3Urls;
	}

	private String defaultMessage(String businessName) {
		return EmailMessageConstants.DEFAULT_SUBJECT.concat(businessName);
	}

	private String getEmailSubject(SendMessageDTO sendMessageDTO, String businessName) {
		String emailSubject;
		if(Objects.nonNull(sendMessageDTO.getPaymentRequest())) {
			boolean isCurrencyAvailable = Objects.nonNull(sendMessageDTO.getCreatePaymentLinkResponse()) && Objects.nonNull(sendMessageDTO.getCreatePaymentLinkResponse().getCurrency());
			String currency =  isCurrencyAvailable ? sendMessageDTO.getCreatePaymentLinkResponse().getCurrency().getCurrencySymbol() : CurrencyConstant.USD.getCurrencySymbol();
			if(Objects.nonNull(sendMessageDTO.getPaymentRequest().getSubscriptionRequest())) {
				emailSubject = businessName + " sent you a recurring payment request for " + MessengerUtil.getFormattedAmount(sendMessageDTO.getPaymentRequest().getItems().get(0).getAmount(), Optional.of(currency));
			}else {
				emailSubject = businessName + " sent you a payment request for " + MessengerUtil.getFormattedAmount(sendMessageDTO.getPaymentRequest().getItems().get(0).getAmount(), Optional.of(currency));
			}
		} else if (StringUtils.isNotBlank(sendMessageDTO.getEmailSubject())) {
			emailSubject = sendMessageDTO.getEmailSubject();
		} else {
			emailSubject = defaultMessage(businessName);
		}
		return emailSubject;
	}

	private String getReplyTo(BusinessDTO business) {

		BusinessOptionResponse businessOptionResponse = null;
		if(business.getBusinessId() != null) {
			businessOptionResponse=businessService.getBusinessOptionsConfig(business.getBusinessId(), true);
		}
		if("Whitelabel".equals(business.getAccountType()) && (businessOptionResponse == null || StringUtils.isBlank(businessOptionResponse.getInboxEmailReplyDomain()))) {
			return null;
		}
		String replyDomain = businessOptionResponse == null || StringUtils.isBlank(businessOptionResponse.getInboxEmailReplyDomain()) ? emailReplyDomain : businessOptionResponse.getInboxEmailReplyDomain();

		String bname = MessengerUtil.formatBusinessNameWOCaseChange(business.getBusinessName());
		return bname.concat("-").concat(String.valueOf(business.getBusinessNumber())).concat(replyDomain);
	}
	private List<MessengerMediaFile> getMessengerMediaFiles(List<MessengerMediaFileDTO> messengerMediaFiles) {
		List<MessengerMediaFile> mediaFileList = new ArrayList<MessengerMediaFile>();
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			for (MessengerMediaFileDTO mediaFileDTO : messengerMediaFiles) {
				MessengerMediaFile mediaFile = new MessengerMediaFile(mediaFileDTO);
				mediaFileList.add(mediaFile);
			}
		}
		return mediaFileList;
	}


	private List<EmailFileAttachementData> getFileAttachment(List<MessengerMediaFileDTO> messengerMediaFiles) {
		List<EmailFileAttachementData> fileattachments = new ArrayList<EmailFileAttachementData>();
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			for (MessengerMediaFileDTO mediaFile : messengerMediaFiles) {
				EmailFileAttachementData file = new EmailFileAttachementData();
				file.setFileName(mediaFile.getName());
				file.setType(mediaFile.getContentType());
				file.setUrl(mediaFile.getUrl());
				fileattachments.add(file);
			}
		}
		return fileattachments;
	}

	private Map<String, String> buildSendEmailTemplate(String emailBody, BusinessDTO businessDTO, MessageDTO messageDTO, EmailDTO emailDTO) {
		Map<String, String> dataModel = new HashMap<>();
		String formattedBody = StringUtils.replace(emailBody, "\n", "<br />");
		if(Objects.nonNull(messageDTO.getPaymentRequest())) {
			DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
			Date dateInBusinessTimeZone = ControllerUtil.convertToBusinessTimeZone(new Date(messageDTO.getCreatePaymentLinkResponse().getLinkCreatedAt()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			boolean isCurrencyAvailable = Objects.nonNull(messageDTO.getCreatePaymentLinkResponse()) && Objects.nonNull(messageDTO.getCreatePaymentLinkResponse().getCurrency());
			String currency =  isCurrencyAvailable ? messageDTO.getCreatePaymentLinkResponse().getCurrency().getCurrencySymbol() : CurrencyConstant.USD.getCurrencySymbol();
			String formattedDate = df.format(dateInBusinessTimeZone);
			if(Objects.nonNull(messageDTO.getPaymentRequest().getSubscriptionRequest())){
				dataModel.put("title", "Your recurring payment request has been initiated");
				dataModel.put("isSubscription", "true");
			}else {
				dataModel.put("title", "Payment Request");
			}
			dataModel.put("body", formattedBody);
			dataModel.put("notificationName", PaymentNotificationName.PAYMENT_REQUEST.name());
			dataModel.put("date", formattedDate);
			String invoiceNumber = messageDTO.getPaymentRequest().getInvoiceNumber();
			dataModel.put("invoiceNumber", StringUtils.isNotBlank(invoiceNumber) ? invoiceNumber : "N/A");
			dataModel.put("itemDesc", messageDTO.getPaymentRequest().getItems().get(0).getItemDesc());
			dataModel.put("itemPrice", MessengerUtil.getFormattedAmount(messageDTO.getPaymentRequest().getItems().get(0).getAmount(), Optional.of(currency)));
			dataModel.put("paymentUrl", messageDTO.getCreatePaymentLinkResponse().getPaymentLink());
			dataModel.put("amount", MessengerUtil.getFormattedAmount(messageDTO.getPaymentRequest().getItems().get(0).getAmount(), Optional.of(currency)));
		} else {
			dataModel.put("messageBody", formattedBody);
			dataModel.put("businessName", businessDTO.getBusinessName());
		}
		dataModel.put("replyTo", emailDTO.getReplyTo());
		return dataModel;
	}

	void addActivityIfChannelChanged(MessageDTO messageDTO, MessengerContact messengerContact) {
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		String lastChannel = lastMessageMetadataPOJO.getLastMessageChannel();
		log.info("[addActivityIfChannelChanged] channel present {}", lastChannel);
		if(lastChannel == null || !MessageDocument.Channel.EMAIL.name().equals(lastChannel)) {
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			ActivityDto activityDto = ActivityDto.builder().mcId(messengerContact.getId()).created(new Date(new Date().getTime() - 1000))
					.actorId(messengerContact.getCustomerId()).activityType(ActivityType.EMAIL_START).from(messengerContact.getId()).businessId(businessDTO.getBusinessId())
					.to(messengerContact.getBusinessId()).accountId(businessDTO.getAccountId()).source(Source.EMAIL.getSourceId()).build();
			messageDTO.setActivityMessage(activityDto);
		}
		log.info("[addActivityIfChannelChanged] Activity added {}", messageDTO.getActivityMessage());
	}

	private void vaidateEmail(String emailId) {
		boolean isInvalidEmail = !MessengerUtil.isValidEmail(emailId);
		if (isInvalidEmail) {
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.INVALID_CUSTOMER_EMAIL_ID, ComponentCodeEnum.EMAIL)
					.message("Email Id - {} is not valid", emailId));
		}
	}

	private EmailCampaignRequest createCampaignRequest(SendMessageDTO sendMessageDTO, Email email,
													   String fromName, String fromEmailId, UserDTO userDto) {
		EmailCampaignRequest campaignRequest = new EmailCampaignRequest(sendMessageDTO.getTemplateId(),
				email.getBusinessId(), email.getId(), email.getCustomerId(), sendMessageDTO.getSurveyId(), fromName,
				sendMessageDTO.getReplyToEmailId(), sendMessageDTO.getTemplateType(), fromEmailId, userDto.getEmailId());
		return campaignRequest;
	}

	private String getFromNameFromCoreService(Integer businessId, String businessName, UserDTO userDTO) {
		String userDTOName = userDTO.getName();
		String fromNameWithBusiness = businessName.concat(EmailMessageConstants.FROM_NAME_SUFFIX);
		String fromName = null;
		if (PaymentAutomationUser.getPaymentAutomationUserByUserId(userDTO.getId()) != null){
			fromName= fromNameWithBusiness;
		}else {
			fromName = userDTOName.concat(EmailMessageConstants.SPACE).concat(fromNameWithBusiness);
		}
		EmailSenderDto emailSenderInfo = getEmailSendDto(businessId);
		if (emailSenderInfo != null) {
			if (RESELLER.contains(emailSenderInfo.getAccountType())) {
				if (PaymentAutomationUser.getPaymentAutomationUserByUserId(userDTO.getId()) != null) {
					fromName= businessName.concat(" via ").concat(emailSenderInfo.getSenderName());
				}else {
					fromName = userDTOName.concat(EmailMessageConstants.SPACE).concat(businessName).concat(" via ")
							.concat(emailSenderInfo.getSenderName());
				}
			}
		}
		return fromName;
	}

	private String fetchFromEmailId(String businessName, Integer businessId) {
		EmailSenderDto emailSenderInfo = getEmailSendDto(businessId);
		String bname = MessengerUtil.formatBusinessNameWOCaseChange(businessName);
		String fromEmail = bname.concat(EmailMessageConstants.BIRDEYE_EMAIL_SUFFIX);
		if (emailSenderInfo != null) {
			if (RESELLER.contains(emailSenderInfo.getAccountType())) {
				fromEmail = emailSenderInfo.getSenderEmail();
			}
		}
		return fromEmail;
	}

	private EmailSenderDto getEmailSendDto(Integer businessId) {
		EmailSenderDto emailSenderInfo = businessService.getSenderEmailInfo(businessId);
		return emailSenderInfo;
	}
}
