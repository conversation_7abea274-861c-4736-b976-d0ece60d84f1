package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.DailyReportRequest;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.GenericFirebaseMessage;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class MessengerServiceImpl implements MessengerService{
	
	
	private final RedisHandler redisHandler;
	
	private final NexusService nexusService;
	
	private final BusinessService businessService;
	
	private final ElasticSearchExternalService elasticSearchService;
	
	private final MessageService messageService;
	
	@Override
	public <T> Void updateFbIntegratingStatusCache(GenericFirebaseMessage<?> facebookStatusRequest) {
		String[] topicData = facebookStatusRequest.getTopicName().split("/");

		String enterpriseId = topicData[1];
		String businessId = topicData[2];

		//Update Status in Cache
		Map<String, Object> data = (Map<String, Object>) facebookStatusRequest.getData();
		String status = (String) data.get("socialIntegrationStatus");

		Map<String, String> map = new HashMap<>();
		map.put("status", status);
		
		try {
			redisHandler.updateFbStatusCache(businessId, map);
		} catch (JsonProcessingException e) {

		}
		
		//Update TS in firebase DB
		String topicTS = "messenger/"+enterpriseId;
		/*Map<String, Object> dataTS = new HashMap<>();
		dataTS.put("_t", new Date().getTime());*/
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(Integer.parseInt(enterpriseId));
		firebaseDto.setBusinessId(Integer.valueOf(enterpriseId));
		nexusService.updateFirebaseDb(new GenericFirebaseMessage<>(firebaseDto, topicTS));

		return null;
	}

	@Override
	public Map messengerDailyReport(DailyReportRequest request) {
		Integer messengerEnabled = businessService.isMessengerEnabled(request.getEnterpriseId());
		if (Integer.valueOf(0).equals(messengerEnabled)) {
			log.info("messengerDailyReport: Messenger Not Enabled for business {}", request.getEnterpriseId());
			return null;
		}
		//TODO Try to get the count in single ES call (Use Aggregation)
		//Get openConversation count from ES
		Long openConversationCount = messengerOpenConversationCount(request.getBusinessIds(), request.getUserId(), request.getEnterpriseId());

		//Get assignedConversation count from ES
		Long assignedConversationCount = messengerAssignedConversationCount(request.getBusinessIds(), request.getUserId(), request.getEnterpriseId());

		Map<String, Long> dailyCount = new HashMap<>();
		dailyCount.put("openConversationCount", openConversationCount);
		dailyCount.put("assignedConversationCount", assignedConversationCount);

		return dailyCount;
	}
	
	private Long messengerOpenConversationCount(List<Integer> businessIds, Integer userId, Integer enterpriseId) {
		Map<String, String> messageFilter = new HashMap<>();
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
		messageFilter.put("userId", String.valueOf(userId));
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.GET_OPEN_CONVERSATIONS_COUNT, dataModel).build();
		return elasticSearchService.getDocumentCount(esRequest);
	}
	
	private Long messengerAssignedConversationCount(List<Integer> businessIds, Integer userId, Integer enterpriseId) {
		Map<String, String> messageFilter = new HashMap<>();
		messageFilter.put("userId", String.valueOf(userId));
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));

		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.GET_ASSIGNED_CONVERSATIONS_COUNT, dataModel).build();
		return elasticSearchService.getDocumentCount(esRequest);
	}
	
	@Override
	public boolean hasAgentRespondedOnOutsideBH(Integer accountId, Integer mcId, Boolean isReceivedDuringBusinessHr) {
		boolean hasAgentRespondedOnOutsideBH=false;
		if (BooleanUtils.isFalse(isReceivedDuringBusinessHr)) {
			Integer autoResponseTimeInMin = MessengerUtil.getIntegerPropertyFromCache("send_auto_response_time_in_min",
					10);
			String agentLastResponseTime = messageService.getAgentLastResponseTime(mcId, accountId);
			if (StringUtils.isNotBlank(agentLastResponseTime)) {
				Date msgDate = DateUtils.getDateInUTC(agentLastResponseTime, MessageDocumentDTO.DATE_FORMAT);
				hasAgentRespondedOnOutsideBH = MessengerUtil.hasAgentRespondedOnOutsideBH(msgDate.getTime(),
						new Date().getTime(), isReceivedDuringBusinessHr, autoResponseTimeInMin);
			}
		}
		return hasAgentRespondedOnOutsideBH;
	}
	@Override
	public boolean hasAgentRespondedWithinConfiguredTimeWindow(Integer accountId, Integer mcId) {
		boolean hasAgentRespondedWithinConfiguredTimeWindow=false;
			Integer autoResponseTimeInMin = MessengerUtil.getIntegerPropertyFromCache("agent_response_time_window_in_min",
					10);
			String agentLastResponseTime = messageService.getAgentLastResponseTime(mcId, accountId);
			if (StringUtils.isNotBlank(agentLastResponseTime)) {
				Date msgDate = DateUtils.getDateInUTC(agentLastResponseTime, MessageDocumentDTO.DATE_FORMAT);
				Long timeDiff = DateUtils.calculateTimeDifference(msgDate.getTime(), new Date().getTime(), TimeUnit.MINUTES);
				hasAgentRespondedWithinConfiguredTimeWindow=timeDiff<autoResponseTimeInMin;
			}
		return hasAgentRespondedWithinConfiguredTimeWindow;
	}
//	private final FacebookMessageRepository facebookMessageRepository;
//	private final MessengerContactRepository messengerContactRepository;
//	private final CommonService commonService;
//	private final KafkaService kafkaService;
//	private final BusinessService businessService;
//	private final CommunicationService communicationService;
//	private final MessengerContactService messengerContactService;
	
//	@Override
//	public ScheduleAlertMessage receiveMessage(FacebookMessageRequest message) throws Exception {
//		log.info("Message From facebook : Input request: {}", message);
//		if (message != null) {
//			FacebookMessage fbMsg = null;
//			
//			String businessFacebookId;
//			String customerFacebookId;
//			Messaging msg = message.getEntry().get(0).getMessaging().get(0);
//
//			if (msg.getMessage() !=null && msg.getMessage().getIs_echo()) {
//				businessFacebookId = msg.getSender().getId();
//				customerFacebookId=msg.getRecipient().getId();
//			} else {
//				businessFacebookId = msg.getRecipient().getId();
//				customerFacebookId = msg.getSender().getId();
//			}
//			// find business by facebookpage id
//			
//			
//			
//			// TODO get from social
//			
//			
//			
//			
////			Integer businessId = businessFacebookPageRepository.findBusinessByFacebookId(businessFacebookId);
//			Integer businessId=null;
//			if (businessId == null) {
//				log.error("business Not found for facebook page id : "+businessFacebookId);
//				throw new NotFoundException(ErrorCode.INVALID_FACEBOOK_ID);
//			}
//			UserDetailsMessage userInfo=null;
//			if (msg.getDelivery() != null || msg.getRead() != null) {
//				if (msg.getDelivery() != null) {
//					fbMsg = facebookMessageRepository.getMessageByMessageId(msg.getDelivery().getMids().get(0));
//					if(fbMsg!=null) {
//						fbMsg.setStatus("delivered");
//						facebookMessageRepository.saveAndFlush(fbMsg);
//						log.info("Status updated with messageId :" + msg.getDelivery().getMids().get(0));
//					} else {
//						log.info("Message not found for messageId :" + msg.getDelivery().getMids().get(0));
//					}
//				} else {
////					facebookMessageRepository.updateAllMessageWithStatusDelivered(customerMessage.getEmailId(),
////							businessId);
//					log.info("Status updated for customer in business with businessId  :" + businessId);
//				}
//				return null;
//			} else {
//
//				MessengerContact contact = messengerContactRepository.findByFacebookId(customerFacebookId,businessId);
//				if(msg.getMessage().getIs_echo() && contact==null) {
//					log.error("contact not found for send message with echo flag: "+msg.getMessage().getIs_echo());
//					throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
//				}
//				
//				if (contact==null) {
//					// create contact if not present in case of receive message
//					
//					// get facebook user details
//					GetUserDetailsMessage userMessage = new GetUserDetailsMessage(msg.getRecipient().getId(),
//							msg.getSender().getId());
//					ResponseEntity<String> userDetailResponse=null;
//					try {
//					userDetailResponse = commonService.getFacebookUserInfo(userMessage);
//					} catch (Exception e) {
//						log.error("Social call failed"+e.toString());
//					}
//					if(userDetailResponse!=null) {
//						if (!userDetailResponse.getStatusCode().is2xxSuccessful())
//							throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
//									"Error response from platform api userDetailResponse");
//						String userDetailStr = ControllerUtil.getJsonTextFromObject(userDetailResponse.getBody());
//						log.info("Response of GetUserDetailsMessage API :" + userDetailStr);
//						userInfo = ControllerUtil.getObjectFromJsonText(userDetailStr, UserDetailsMessage.class);
//					}
//					
//				} 
//				
//					Integer encrypted = 0;
//					MessengerMediaFile file = null;
//					String body = msg.getMessage().getText();
//					SmsReceiveMessage.Media mediaUrl = null;
//					String encryptionEnabled = businessService.getParametersByName("encryption.enabled");
//					if (CollectionUtils.isNotEmpty(msg.getMessage().getAttachments())) {
//						mediaUrl = new SmsReceiveMessage.Media();
//						int i = 0;
//						if (msg.getMessage().getAttachments().size() > 0) {
//							ScheduleAlertMessage alertMessage=null;
//							for (i = 0; i < msg.getMessage().getAttachments().size(); i++) {
//								mediaUrl = new SmsReceiveMessage.Media();
//								updateMedia(msg.getMessage().getAttachments().get(i), mediaUrl);
//								if (mediaUrl != null) {
//									file = new MessengerMediaFile(mediaUrl);
//								}
//								FacebookMessage fbMessage = saveFacebookMessage(null, msg.getRecipient().getId(),
//										msg.getSender().getId(), "", msg.getMessage().getMid(),
//										msg.getMessage().getIs_echo() ? "sent" : "received", new Date(), "", encrypted,
//										businessId, mediaUrl == null ? null : mediaUrl.getMediaUrl());
//								log.info("Facebook message: {}", fbMessage);
//								alertMessage = new ScheduleAlertMessage(fbMessage,
//										file);
//								if (!msg.getMessage().getIs_echo()) {
//									Integer messengerEnabled = businessService.isMessengerEnabled(businessId);
//									
//									if (messengerEnabled != null && messengerEnabled == 1) {
////										ResponseEntity<Object> res = commonService.scheduleAlert(alertMessage);
//										ResponseEntity<Object> res=null;
//										if (!res.getStatusCode().is2xxSuccessful())
//											throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
//													"Error response from platform api scheduleAlert " + businessId);
//										else {
//											fbMessage.setCustomerId(ControllerUtil.jsonToMap(ControllerUtil.getJsonTextFromObject(res.getBody()), Integer.class).get("contactId"));
//											facebookMessageRepository.saveAndFlush(fbMessage);
//										}
//									}
//								}else {
//									// push to kafka 
//									if(StringUtils.isNotBlank(message.getObject())){
//										alertMessage.setCustomerId(contact!=null?contact.getId():fbMessage.getCustomerId());
//										if(alertMessage.getCustomerId()==null) {
//											throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
//										}
//										pushFacebookSendRequestToKafka(alertMessage,userInfo);
//										fbMessage.setCustomerId(alertMessage.getCustomerId());
//										facebookMessageRepository.saveAndFlush(fbMessage);
//									}
//								}
//								alertMessage.setCustomerId(contact!=null?contact.getId():fbMessage.getCustomerId());
//							}
//							if(StringUtils.isBlank(body))
//								return alertMessage;
//						}
//
//					} 
//					if(StringUtils.isNotBlank(body)){
//						try {
//							if (encryptionEnabled != null
//									&& encryptionEnabled.equalsIgnoreCase("true")) {
//								body = EncryptionUtils.encrypt(body,StringUtils.join(msg.getRecipient().getId(), msg.getSender().getId()),StringUtils.join(msg.getSender().getId(), msg.getRecipient().getId()));
//								encrypted = 1;
//							}
//						} catch (Exception e) {
//							log.info("Encryption for recieved Facebook message failed: {}", e);
//						}
//						FacebookMessage fbMessage = saveFacebookMessage(null, msg.getRecipient().getId(),
//								msg.getSender().getId(), body,CollectionUtils.isEmpty(msg.getMessage().getAttachments())? msg.getMessage().getMid():msg.getMessage().getMid()+"_text",
//								msg.getMessage().getIs_echo() ? "sent" : "received", new Date(), "", encrypted,
//								businessId, mediaUrl == null ? null : mediaUrl.getMediaUrl());
//						log.info("Facebook message: {}", fbMessage);
//
//						ScheduleAlertMessage alertMessage = new ScheduleAlertMessage(fbMessage,
//								file);
//						if (!msg.getMessage().getIs_echo()) {
//							Integer messengerEnabled = businessService.isMessengerEnabled(businessId);
//							if (messengerEnabled != null && messengerEnabled == 1) {
////								ResponseEntity<Object> res = commonService.scheduleAlert(alertMessage);
////								if (!res.getStatusCode().is2xxSuccessful())
////									throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
////											"Error response from platform api scheduleAlert " + businessId);
////								else {
////									fbMessage.setCustomerId(ControllerUtil.jsonToMap(ControllerUtil.getJsonTextFromObject(res.getBody()), Integer.class).get("contactId"));
////									facebookMessageRepository.saveAndFlush(fbMessage);
////								}
//							}
//						} else {
//							// push to kafka 
//							file=null;
//							if(StringUtils.isNotBlank(message.getObject())){
//								ScheduleAlertMessage alert = new ScheduleAlertMessage(fbMessage,
//										file);
//								alert.setCustomerId(contact!=null?contact.getId():fbMessage.getCustomerId());
//								if(alert.getCustomerId()==null) {
//									throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
//								}
//								// push message to kafka
//								pushFacebookSendRequestToKafka(alert,userInfo);
//								fbMessage.setCustomerId(alert.getCustomerId());
//								facebookMessageRepository.saveAndFlush(fbMessage);
//							}
//						}
//						alertMessage.setCustomerId(contact!=null?contact.getId():fbMessage.getCustomerId());
//						return alertMessage;
//
//					}
//			}
//		}
//		return null;
//	}
//	
//	private void updateMedia(Attachments attachments,SmsReceiveMessage.Media media) {
//		media.setMediaUrl(attachments.getPayload().getUrl());
//		media.setMime(attachments.getType());
//	}
//	
//	private FacebookMessage saveFacebookMessage(Integer custId,String recipientId,String senderId,String message,String messageId,String status,Date sentOn,String failureReason,Integer encrypted,Integer businessId,String mediaUrl) {
//		FacebookMessage fbMessage=new FacebookMessage();
//		fbMessage.setCreateDate(new Date());
//		fbMessage.setCustomerId(custId);
//		fbMessage.setRecipientFacebookId(recipientId);
//		fbMessage.setSenderFacebookId(senderId);
//		if(StringUtils.isNoneBlank(message)) {
//			fbMessage.setMessageBody(message);
//		}
//		if(messageId.substring(0, 2).equalsIgnoreCase("m_"))
//			fbMessage.setMessageId(messageId.subSequence(2, messageId.length()).toString());
//		else
//			fbMessage.setMessageId(messageId);
//		fbMessage.setStatus(status);
//		fbMessage.setSentOn(sentOn);
//		if(StringUtils.isNoneBlank(failureReason)) {
//			fbMessage.setFailureReason(failureReason);
//		}
//		fbMessage.setEncrypted(encrypted);
//		fbMessage.setBusinessId(businessId);
//		if(StringUtils.isNoneBlank(mediaUrl)) {
//			fbMessage.setMediaURL(mediaUrl);
//		}
//		try {
//			facebookMessageRepository.saveAndFlush(fbMessage);
//		} catch (Exception e) {
//			log.error("exception in saving facebook message : "+e.toString());
//			throw new DuplicateEntryException(ErrorCode.DUPLICATE_MESSAGE);
//		}
//		return fbMessage;
//	}
//	
//	
//    private void pushFacebookSendRequestToKafka(ScheduleAlertMessage message,UserDetailsMessage userInfo) throws Exception{
//    	// getting dummy user if messge sent directly from facebook page
//    	
//    	
//    	//TODO get userId from core service
//    	
//    	
////    	User user=userController.getUserByEmailId("<EMAIL>");
//    	UserDTO userDTO=null;
//    	String type = "SMS_SEND";
//        if (StringUtils.isNotBlank(message.getMediaURL())) {
//            type = "MMS_SEND";
//        }
//    	FacebookMessage fbMessage=new FacebookMessage(message);
//    	SendMessageDTO sendMessageDTO= new SendMessageDTO();
//    	if(message.getEncrypted()!=null && message.getEncrypted()==1)
//    		sendMessageDTO.setBody(EncryptionUtils.decrypt(message.getMessageBody(), StringUtils.join(message.getRecipientFacebookId(), message.getSenderFacebookId()), StringUtils.join(message.getSenderFacebookId(), message.getRecipientFacebookId())));
//    	else
//    		sendMessageDTO.setBody(message.getMessageBody());
//    	sendMessageDTO.setToCustomerId(message.getCustomerId().toString());
//    	sendMessageDTO.setMediaurl(message.getMediaURL());
//    	sendMessageDTO.setFromBusinessId(message.getBusinessId().toString());
//    	if(userDTO!=null)
//    		sendMessageDTO.setToBusinessUserId(userDTO.getId().toString());
//    	pushFacebookSendRequestToKafka(fbMessage, sendMessageDTO, type, userDTO, message.getFile(), userInfo);
//    }
//    
//    private void pushFacebookSendRequestToKafka(FacebookMessage fbMessage,SendMessageDTO sendMessageDTO,String type, UserDTO userDTO, MessengerMediaFile mediaFile,UserDetailsMessage userInfo) throws Exception{
//        if (fbMessage != null && userDTO != null) {
//        	// audit messenger here
//        	
//        	
//        	
////            MessengerAudit audit = new MessengerAudit(sms.getId(), user.getId(), type);
////            em.persist(audit);
//        }
//        ConversationWrapperMessage conversationKafkaWrapper = new ConversationWrapperMessage(sendMessageDTO, fbMessage);
//        SendConversationRequest request = commonService.getSendSMSRequestForMessenger(conversationKafkaWrapper);
//        kafkaService.publishToKafkaAsync(KafkaTopicEnum.COMM_SMS_SEND,fbMessage.getBusinessId(),
//        		request);
//        updateMessengerContact(fbMessage, MessageTag.INBOX, type, userDTO, mediaFile, sendMessageDTO.getBody(),null,userInfo);
//    }
//    
//	public void updateMessengerContact(FacebookMessage fbMessage, MessageTag tag, String type, UserDTO userDTO,MessengerMediaFile mediaFile, String messageBody, ScheduleAlertMessage msg,UserDetailsMessage userInfo) {
//		// always send customermessage as null in case of messenger contact already exists
//		
//		//1. get messengerContact
//		MessengerContact contact = messengerContactService.getOrCreateContact(fbMessage.getBusinessId(), fbMessage.getCustomerId());
//		//2 . get business info from core service
//		BusinessDTO businessDTO= businessService.getBusinessDTO(fbMessage.getBusinessId());
//		//3. Update last message details on messengerContact DB
//		messengerContactService.updateLastMessageDetails(contact, new MessengerContactDTO(fbMessage, userInfo, businessDTO, userDTO.getId(), MessengerEvent.SMS_RECEIVE, tag));
//		//4. Update contact document on elasticSearch (messenger/contact)
//        ContactDocument contactDocument = communicationService.updateContactOnES(contact, new CustomerDTO(userInfo), businessDTO, tag, 1);
//        //5. Add received message details on elasticSearch (messenger/message)
//        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(fbMessage,contactDocument.getM_c_id());
//        MessageDocument messageDocument = communicationService.andNewMessageOnEs(messageDocumentDTO,new MessengerMediaFileDTO(mediaFile), userDTO, businessDTO, MessengerEvent.SMS_RECEIVE);
//		
//        
//        //TODO push using platform code only
//			
//			
//			
//			// Firebase notification for mobile. Remove this once flow directed to nifi.
////			log.info("updateMessengerContact: pushFirebaseNotification for sms: {}", fbMessage.getId());
////			pushFirebaseNotification(fbMessage, userId, mediaFile, type);
//	}
    

}
