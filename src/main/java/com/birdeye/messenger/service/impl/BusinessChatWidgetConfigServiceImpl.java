package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.birdeye.messenger.util.LogUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatEnabledTeam;
import com.birdeye.messenger.dao.entity.BusinessChatLocationHierarchy;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetUserProfile;
import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.entity.WebchatCustomLocationName;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledLocationRepository;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledTeamRepository;
import com.birdeye.messenger.dao.repository.BusinessChatLocationHierarchyRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetConfigRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetUserProfileRepository;
import com.birdeye.messenger.dao.repository.WebchatCustomLocationNameRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessWebchatWidgetDetails;
import com.birdeye.messenger.dto.Location;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.LiveChatWidgetConfigService;
import com.birdeye.messenger.sro.BusinessLocationInfo;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;
import com.birdeye.messenger.sro.BusinessWebChatWidgetUserProfile;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BusinessChatWidgetConfigServiceImpl implements BusinessChatWidgetConfigService {

    @Autowired
    private BusinessChatWidgetConfigRepository businessChatWidgetConfigRepository;

    @Autowired
    private BusinessChatWidgetUserProfileRepository businessChatWidgetUserProfileRepository;

    @Autowired
    private BusinessChatEnabledLocationRepository businessChatEnabledLocationRepository;

    @Autowired
    private BusinessChatEnabledTeamRepository businessChatEnabledTeamRepository;

    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private LiveChatWidgetConfigService liveChatWidgetConfigService;
    
    @Autowired
    private BusinessChatLocationHierarchyRepository businessChatLocationHierarchyRepository;
    
    @Autowired
    private WebchatCustomLocationNameRepository webchatCustomLocationNameRepository;

    @Override
    @Transactional
    public Optional<BusinessChatWidgetConfig> saveOrUpdateBusinessChatWidgetConfig(BusinessWebChatConfigurationRequest bwcr) {
        BusinessChatWidgetConfig bcwc = null;
        boolean edit = false;
        if (bwcr.getWidgetConfigId() == null || bwcr.getWidgetConfigId() == Constants.DEFAULT_CONFIG_ID){
            bcwc = populateBusinessChatWidgetConfig(bwcr,new BusinessChatWidgetConfig(), true);
        }else {
            Optional<BusinessChatWidgetConfig> bcwcOptional = businessChatWidgetConfigRepository.findById(bwcr.getWidgetConfigId());
            if (!bcwcOptional.isPresent()){
                log.info("Business chat widget config is not present for id :{}",bwcr.getWidgetConfigId());
                return Optional.empty();
            }
            bcwc = populateBusinessChatWidgetConfig(bwcr,bcwcOptional.get(),false);
            edit = true;
        }
        try {
            bcwc = businessChatWidgetConfigRepository.saveAndFlush(bcwc);
            if (bcwc != null){
                saveBusinessChatWidgetUserProfile(bwcr.getUserProfile(),edit,bcwc);
                saveBusinessChatEnabledLocation(bwcr.getBusinessLocations(),edit,bcwc);
                saveBusinessChatEnabledTeam(bwcr.getTeams(),edit,bcwc);
                saveBusinessChatLocationHierarchy(bwcr.getLocationHierarchy(),edit,bcwc);
            }
        }catch (Exception e){
            log.error("Exception while saving business chat widget config, Exception:{}",e);
            return Optional.empty();
        }
        populateLiveChatWidgetConfig(bwcr,bcwc.getId());

        return Optional.of(bcwc);
    }

    private void saveBusinessChatLocationHierarchy(LocationHierarchy locationHierarchy, boolean edit, BusinessChatWidgetConfig bcwc) {
        if (edit){
            log.info("Deleting location hierarchy for config Id: {}",bcwc.getId());
            businessChatLocationHierarchyRepository.deleteBusinessChatLocationHierarchyByWidgetConfigId(bcwc.getId());
        }
        if (locationHierarchy != null && locationHierarchy.getLevelId() != null){
            log.info("saving location hierarchy for config Id: {}",bcwc.getId());
            List<BusinessChatLocationHierarchy> businessChatLocationHierarchies
                    =  locationHierarchy.getSubLevels().stream().collect(Collectors.mapping(s -> new BusinessChatLocationHierarchy(locationHierarchy.getLevelId(),
                   s ,bcwc.getId(),bcwc.getUpdatedBy()
            ),Collectors.toList()));
            businessChatLocationHierarchyRepository.saveAll(businessChatLocationHierarchies);
        }
    }

    private void populateLiveChatWidgetConfig(BusinessWebChatConfigurationRequest bwcr,Integer busWebChatConfId) {

        LiveChatWidgetConfig liveChatWidgetConfigFromDB = liveChatWidgetConfigService.getLiveChatMessageConfig(bwcr.getWidgetConfigId());

        if (liveChatWidgetConfigFromDB == null){
            liveChatWidgetConfigFromDB = new LiveChatWidgetConfig();
        }

        liveChatWidgetConfigFromDB.setOfflineClosingMessageBody(bwcr.getLiveChatOfflineClosingMessageBody());
        liveChatWidgetConfigFromDB.setOfflineClosingMessageHeader(bwcr.getLiveChatOfflineClosingMessageHeader());
        liveChatWidgetConfigFromDB.setOfflineWelcomeMessage(bwcr.getLiveChatOfflineWelcomeMessage());

        liveChatWidgetConfigFromDB.setOnlineClosingMessageBody(bwcr.getLiveChatOnlineClosingMessageBody());
        liveChatWidgetConfigFromDB.setOnlineClosingMessageHeader(bwcr.getLiveChatOnlineClosingMessageHeader());
        liveChatWidgetConfigFromDB.setOnlineWelcomeMessage(bwcr.getLiveChatOnlineWelcomeMessage());

        liveChatWidgetConfigFromDB.setWidgetId(busWebChatConfId);
        
        liveChatWidgetConfigFromDB.setOnlineTextMessage(bwcr.getLiveChatOnlineTextMessage());
        liveChatWidgetConfigFromDB.setOfflineTextMessage(bwcr.getLiveChatOfflineTextMessage());

        liveChatWidgetConfigService.saveLiveChatConfig(liveChatWidgetConfigFromDB);
    }

    private BusinessChatWidgetConfig populateBusinessChatWidgetConfig(BusinessWebChatConfigurationRequest bwcr, BusinessChatWidgetConfig bcwc, boolean saveFlow){
        bcwc.setBusinessId(bwcr.getBusinessId());
        bcwc.setBannerColor(bwcr.getBannerColor());
        bcwc.setBannerTextColor(bwcr.getBannerTextColor());
        bcwc.setChatIcon(bwcr.getChatIcon());
        bcwc.setChatIconValue(bwcr.getChatIconValue());
        bcwc.setBusinessDomain(bwcr.getBusinessDomain());
        bcwc.setChatTheme(bwcr.getChatTheme());
        bcwc.setHeaderDescription(bwcr.getHeaderDescription());
        bcwc.setHeaderHeadline(bwcr.getHeaderHeadline());
        bcwc.setWebChatOnlineClosingMessageHeader(bwcr.getWebChatOnlineClosingMessageHeader());
        bcwc.setWebChatOnlineClosingMessageBody(bwcr.getWebChatOnlineClosingMessageBody());
        bcwc.setUpdatedBy(bwcr.getUserKey());
        bcwc.setPopupInterval(bwcr.getPopupInterval());
        bcwc.setMicrosite(bwcr.getMicrosite());
        bcwc.setBtnColor(bwcr.getBtnColor());
        bcwc.setBtnTxtColor(bwcr.getBtnTxtColor());
        bcwc.setEnableReplyInBusinessHr(bwcr.getEnableReplyInBusinessHr());
        bcwc.setEnableReplyPostBusinessHr(bwcr.getEnableReplyPostBusinessHr());
        bcwc.setAutoReplyTxt(bwcr.getAutoReplyTxt());
        bcwc.setReplyTextPostBusinessHr(bwcr.getReplyTextPostBusinessHr());
        bcwc.setChatBubble(bwcr.getChatBubble());
        bcwc.setEnableChatBubble(bwcr.getEnableChatBubble());
        bcwc.setEnableChatBubbleSound(bwcr.getEnableChatBubbleSound());
        bcwc.setChatIconColor(bwcr.getChatIconColor());
        bcwc.setChatIconForeColor(bwcr.getChatIconForeColor());
        //new fields added
        bcwc.setWidgetName(bwcr.getWidgetName());
        if (saveFlow){
            bcwc.setEnterpriseId(bwcr.getEnterpriseId());
            bcwc.setStatusUpdatedOn(new Date());
        }
        bcwc.setWidgetCloneId(bwcr.getWidgetCloneId());
        bcwc.setEnabled(bwcr.getEnabled());
        bcwc.setUpdatedAt(new Date());
        // Livechat and Chatbot configurations added
        bcwc.setLivechatEnabled(bwcr.getIsLiveChatEnabled() != null ? (bwcr.getIsLiveChatEnabled() ? 1 : 0) : 0);
        bcwc.setChatbotEnabled(bwcr.getIsChatbotEnabled() != null ? (bwcr.getIsChatbotEnabled() ? 1 : 0) : 0);

        bcwc.setGoogleAnalyticsVersion(bwcr.getGoogleAnalyticsVersion());
        bcwc.setGoogleTrackingId(bwcr.getGoogleTrackingId());
        bcwc.setEnableGoogleAnalytics(bwcr.getEnableGoogleAnalytics() != null ? (bwcr.getEnableGoogleAnalytics() ? 1 : 0) : 0);
        bcwc.setOfflineClosingMessageBody(bwcr.getWebChatOfflineClosingMessageBody());
        bcwc.setOfflineClosingMessageHeader(bwcr.getWebChatOfflineClosingMessageHeader());
        bcwc.setAllLocationDisabled(bwcr.getAllLocationDisabled());
        if( !StringUtils.isBlank(bwcr.getDisclaimer())){
            String disclaimer = bwcr.getDisclaimer().trim();
            bcwc.setDisclaimer(disclaimer);
        }else{
            String defaultDisclaimer = String.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("default_global_webchat_disclamier","By sending this message, you expressly consent to receive communications from us. You may opt out at any time."));
            bcwc.setDisclaimer(defaultDisclaimer);
        }
        Boolean autoDetectLocationBoolean = bwcr.isAutoDetectLocationEnabled();
        Integer autoDetectLocationInteger = autoDetectLocationBoolean.equals(true) ? 1 : 0;
        bcwc.setAutoDetectLocation(autoDetectLocationInteger);
        bcwc.setDisclaimerSelectionStatus(bwcr.getDisclaimerSelectionStatus());
        bcwc.setEnablePrechatForm(bwcr.getEnablePrechatForm());
        bcwc.setPrechatFormInsideBusinessHours(bwcr.getPrechatFormInsideBusinessHours());
		bcwc.setPrechatFormOutsideBusinessHours(bwcr.getPrechatFormOutsideBusinessHours());
		bcwc.setMobileView(bwcr.getMobileView() != null ? bwcr.getMobileView() : Constants.WEBCHAT_DEFAULT_MOBILE_VIEW);
		return bcwc;
    }

    private void saveBusinessChatWidgetUserProfile(List<BusinessWebChatWidgetUserProfile> businessWebChatWidgetUserProfiles,boolean edit,BusinessChatWidgetConfig bcwc){

        if (edit){
            log.info("Deleting BusinessWebChatWidgetUserProfile for config Id: {}",bcwc.getId());
            businessChatWidgetUserProfileRepository.deleteBusinessChatWidgetUserProfileByWidgetConfigID(bcwc.getId());
        }
        log.info("saving BusinessWebChatWidgetUserProfile for config Id: {}",bcwc.getId());
        if (CollectionUtils.isNotEmpty(businessWebChatWidgetUserProfiles)){
            List<BusinessChatWidgetUserProfile> businessChatWidgetUserProfiles
                    =  businessWebChatWidgetUserProfiles.stream().collect(Collectors.mapping(bwcup -> new BusinessChatWidgetUserProfile(
                            bwcup.getUserProfileName(),bwcup.getUserProfileImage(),bcwc.getId(),bcwc,bcwc.getUpdatedBy()
            ),Collectors.toList()));
            businessChatWidgetUserProfileRepository.saveAll(businessChatWidgetUserProfiles);
        }
    }

    private void saveBusinessChatEnabledLocation(
            List<BusinessLocationInfo> businessLocationInfos, boolean edit, BusinessChatWidgetConfig bcwc){

        if (edit){
            log.info("Deleting businessLocationInfos for config Id: {}",bcwc.getId());
            businessChatEnabledLocationRepository.deleteBusinessChatEnabledLocationByWidgetConfigId(bcwc.getId());
        }

        if (CollectionUtils.isNotEmpty(businessLocationInfos)){
            log.info("saving businessLocationInfos for config Id: {}",bcwc.getId());
            List<BusinessChatEnabledLocation> businessChatEnabledLocations
                    =  businessLocationInfos.stream().collect(Collectors.mapping(businessLocationInfo -> new BusinessChatEnabledLocation(
                   businessLocationInfo.getBusinessId(),bcwc.getId(),bcwc.getUpdatedBy()
            ),Collectors.toList()));

            businessChatEnabledLocationRepository.saveAll(businessChatEnabledLocations);
        }
    }


    @Override
    public String getWidgetName(String widgetName, Long businessId, Integer enterpriseId, Integer id) {
        Integer count = 0;
        //duplicate name check for widget in case of edit and save
        if (id == null){
             //save
             count = businessChatWidgetConfigRepository.getNameCount(widgetName,businessId,enterpriseId);
        }else {
            //edit
            count = businessChatWidgetConfigRepository.getNameCount(widgetName,businessId,enterpriseId,id);
        }

        if (count == 0)
            return widgetName;
        return widgetName + "(" + count + ")";
    }

    @Override
    @Cacheable(cacheNames = Constants.DEFAULT_CONFIG_CACHE, key="'default_config_cache'", unless="#result == null")
    public BusinessChatWidgetConfig getDefaultBusinessChatWidgetConfig(){
        BusinessChatWidgetConfig businessChatWidgetConfig= businessChatWidgetConfigRepository.getDefaultConfig(Constants.DEFAULT_CONFIG_ID);
        return businessChatWidgetConfig;
    }
    
    @Override
    @Cacheable(cacheNames = Constants.WIDGET_CONFIG_CACHE, key="'widget_config_cache'.concat(#widgetId)", unless="#result == null")
    public BusinessChatWidgetConfig getBusinessChatWidgetConfig(Integer widgetId){
        Optional<BusinessChatWidgetConfig> businessChatWidgetConfig= businessChatWidgetConfigRepository.findById(widgetId);
        return businessChatWidgetConfig.get();
    }

    @Override
    @Cacheable(cacheNames = Constants.DEFAULT_WIDGET_CACHE, key="'default_widget_cache'", unless="#result == null")
    public BusinessWebchatWidgetDetails getDefaultChatWidgetDetails(){
        BusinessWebchatWidgetDetails businessChatWidgetDetails= businessChatWidgetConfigRepository.findDefaultWidgetDetails();
        return businessChatWidgetDetails;
    }

    public List<BusinessChatWidgetConfig> findwidgetConfigsByEnterpriseId( Integer enterpriseId){
        List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigRepository.findwidgetConfigsByEnterpriseId(enterpriseId);
        return  businessChatWidgetConfigs;
    }

    @Override
    public BusinessChatWidgetConfig getBusinessChatWidgetConfig(BusinessDTO business) {
        BusinessChatWidgetConfig businessChatWidgetConfig = null;
        List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigRepository.findByBusinessId(business.getBusinessNumber());
        if (CollectionUtils.isEmpty(businessChatWidgetConfigs) &&
                business.getEnterpriseNumber() != null) {
            businessChatWidgetConfigs = businessChatWidgetConfigRepository.findByBusinessId(business.getEnterpriseNumber());
        } else if (CollectionUtils.isEmpty(businessChatWidgetConfigs) &&
                business.getEnterpriseId() != null) {
            BusinessDTO entDto = businessService.getBusinessLiteDTO(business.getEnterpriseId());
            if(Objects.nonNull(entDto)) {
                businessChatWidgetConfigs = businessChatWidgetConfigRepository.findByBusinessId(entDto.getBusinessNumber());
            }
        }
        if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
            businessChatWidgetConfig = businessChatWidgetConfigs.get(0);
        }
        return businessChatWidgetConfig;
    }

    @Override
    @Transactional
    public boolean deleteById(Integer id) {
        try {
            businessChatWidgetUserProfileRepository.deleteBusinessChatWidgetUserProfileByWidgetConfigID(id);
            businessChatEnabledLocationRepository.deleteBusinessChatEnabledLocationByWidgetConfigId(id);
            businessChatEnabledTeamRepository.deleteBusinessChatEnabledTeamByWidgetConfigId(id);
            businessChatLocationHierarchyRepository.deleteBusinessChatLocationHierarchyByWidgetConfigId(id);
            businessChatWidgetConfigRepository.deleteById(id);
            liveChatWidgetConfigService.deleteByWidgetId(id);
            return true;
        }catch (Exception e){
            log.error("Exception while deleting business chat widget config, Exception:{}",e);
            return false;
        }
    }


    @Override
    @Transactional
    public boolean enableorDisableBusinessChatWidgetConfig(Integer id,Integer enabled) {
        try {
            businessChatWidgetConfigRepository.enableorDisableBusinessChatWidgetConfig(id,enabled);
            return true;
        }catch (Exception e){
            log.error("Exception while disbaling or enabling business chat widget config, Exception:{}",e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean enableorDisableBusinessChatWidgetConfigs(List<Integer> ids,Integer enabled) {
        try {
            businessChatWidgetConfigRepository.enableorDisableBusinessChatWidgetConfigs(ids,enabled);
            return true;
        }catch (Exception e){
            log.error("Exception while disbaling or enabling business chat widget config, Exception:{}",e);
            return false;
        }
    }

    @Override
    public BusinessChatWidgetConfig getWebChatConfigByWidgetId(Integer widgetId) {
        Optional<BusinessChatWidgetConfig> businessChatWidgetConfig = businessChatWidgetConfigRepository.findById(widgetId);

        if (businessChatWidgetConfig.isPresent())
            return businessChatWidgetConfig.get();
        log.info("No widget found for widgetId: {}",widgetId);
        return null;
    }

//	@Override
//	public List<BusinessLocationInfo> getBusinessLocationsData(Integer chatConfigId, BusinessDTO businessDTO) {
//
//        List<Long> chatEnabledLocationIds = new ArrayList<>();
//		List<BusinessDTO> childBusinesses = getAllBusinessesInHierarchy(businessDTO);
//
//		if (chatConfigId != null){
//		    chatEnabledLocationIds = getChatEnabledLocationIds(chatConfigId);
//        }
//
//		List<BusinessLocationInfo> businessLocationInfos = new ArrayList<>();
//		if (CollectionUtils.isNotEmpty(childBusinesses)) {
//			businessLocationInfos = new ArrayList<>();
//			for (BusinessDTO business : childBusinesses) {
//				String location = business.getBusinessAlias() != null ? business.getBusinessAlias() : business.getBusinessName();
//				if (StringUtils.isNotBlank(location)) {
//					BusinessLocationInfo blInfo = new BusinessLocationInfo();
//					blInfo.setLocation(location);
//					blInfo.setBusinessId(business.getBusinessNumber());
//					blInfo.setBusinessSMSPhoneNumber(business.getPhoneNumber());
//					if (chatConfigId != null){
//                        populateChatEnabledFlag(chatEnabledLocationIds, blInfo);
//                    }
//					businessLocationInfos.add(blInfo);
//				}
//			}
//		}
//		return businessLocationInfos;
//	}

    private List<Long> getChatEnabledLocationIds(Integer chatConfigId) {
        List<BusinessChatEnabledLocation> businessLocations  = businessChatEnabledLocationRepository.findByBusinessChatWidgetId(chatConfigId);
        List<Long> chatEnabledBusinessIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(businessLocations)) {
            for (BusinessChatEnabledLocation chatEnabledLocation : businessLocations) {
                chatEnabledBusinessIds.add(chatEnabledLocation.getBusinessNumber());
            }
        }
        return chatEnabledBusinessIds;
    }

    @Override
    public Map<Integer,List<Long>> getChatEnabledLocationIds(List<Integer> chatConfigIds) {
        List<BusinessChatEnabledLocation> businessLocations  = businessChatEnabledLocationRepository.findByWidgetIds(chatConfigIds);
        Map<Integer,List<Long>> map =  new HashMap<>();
        if (CollectionUtils.isNotEmpty(businessLocations)) {
           map = businessLocations.stream()
                    .collect(Collectors.groupingBy(BusinessChatEnabledLocation::getBusinessChatWidgetId,
                            Collectors.mapping(BusinessChatEnabledLocation::getBusinessNumber, Collectors.toList())));
        }
        return map;
    }

//    @Override
//	public List<BusinessDTO> getAllBusinessesInHierarchy(BusinessDTO businessDTO) {
//		List<Integer> businessIds = businessService.getBusinessSubHierarchyList(businessDTO.getBusinessNumber());
//		log.info("child Businesses size for business number {} is {}", businessDTO.getBusinessNumber(), businessIds != null ? businessIds.size() : 0);
//		// To remove roll-up account
//		List<Integer> corpBizIds = businessService.getBusinessCorporateIds(businessDTO.getBusinessId());
//		log.info("corp Businesses size for business id {} is {}", businessDTO.getBusinessId(), corpBizIds != null ? corpBizIds.size() : 0);
//
//		businessIds.removeAll(corpBizIds);
//		log.info("child Businesses size without corp business for business id {} is {}", businessDTO.getBusinessId(), businessIds != null ? businessIds.size() : 0 );
//
//		if (CollectionUtils.isNotEmpty(businessIds)) {
//			return businessService.getBusinessesByBusinessNumbersOrIds(null, businessIds).values().stream().collect(Collectors.toList());
//		}
//		return null;
//
//
//	}


    @Override
    public List<BusinessDTO> getAllBusinessesInHierarchy(BusinessDTO businessDTO) {
       Map<Integer,BusinessDTO> businessDTOS = businessService.getBusinessSMSEnabledLocations(businessDTO.getBusinessId());
        if (MapUtils.isNotEmpty(businessDTOS)){
            log.info("child Businesses size for business number {} is {}", businessDTO.getBusinessNumber(), businessDTOS != null ? businessDTOS.size() : 0);
            List<BusinessDTO> childBusinesses =  businessDTOS.values().stream().collect(Collectors.toList());
            return childBusinesses;
        }
        return null;
    }



    private void populateChatEnabledFlag(List<Long> chatEnabledLocationIds, BusinessLocationInfo blInfo) {
        if (CollectionUtils.isEmpty(chatEnabledLocationIds)) {
            blInfo.setChatEnabled(true);
        } else if (chatEnabledLocationIds.contains(blInfo.getBusinessId())) {
            blInfo.setChatEnabled(true);
        }
    }

    @Override
    public List<BusinessChatWidgetUserProfile> getUserProfileByChatWidgetId(Integer widgetId) {
        return businessChatWidgetUserProfileRepository.findByBusinessChatWidgetId(widgetId);
    }

    @Override
    public List<BusinessChatWidgetConfig> getWebChatConfigByBusinessNumber(Long businessNumber) {
        return businessChatWidgetConfigRepository.getWebChatConfigByBusinessNumber(businessNumber);
    }

    @Override
    public List<BusinessChatEnabledLocation> findByBusinessNumber(Long businessNumber) {
        return businessChatEnabledLocationRepository.findByBusinessNumber(businessNumber);
    }

    @Override
    public void deleteBusinessChatenabledLocationsById(Integer id) {
        businessChatEnabledLocationRepository.deleteById(id);
    }

    @Override
    public BusinessChatWidgetConfig saveDefaultWidget(BusinessChatWidgetConfig businessChatWidgetConfig) {
        return businessChatWidgetConfigRepository.save(businessChatWidgetConfig);
    }

    @Override
    @Cacheable(cacheNames = Constants.WEBCHAT_WEBSITE_CACHE, key="'External-'.concat(#externalId)", unless="#result == null")
    public BusinessChatWidgetConfig getWebchatConfigBusinessWebsite(Long externalId) {
        List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigRepository.getWebChatConfigByExternalIdAndEnabled(externalId);
        if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)){
            return businessChatWidgetConfigs.get(0);
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = Constants.WEBCHAT_MICROSITE_CACHE, key="'BNO-'.concat(#business.getBusinessNumber())", unless="#result == null")
    public BusinessChatWidgetConfig getWebchatConfigMicrosite(BusinessDTO business) {
        BusinessChatWidgetConfig businessChatWidgetConfig = null;
        Map<Integer, BusinessChatWidgetConfig> chatWidgetMap=new HashMap<Integer, BusinessChatWidgetConfig>();
        List<BusinessChatWidgetConfig> businessChatWidgetConfigs = new ArrayList<BusinessChatWidgetConfig>();
        if (business.getEnterpriseId()!= null){
            List<BusinessChatEnabledLocation> businessChatEnabledLocationsByNumber = findByBusinessNumber(business.getBusinessNumber());
            if(CollectionUtils.isNotEmpty(businessChatEnabledLocationsByNumber)) {
                businessChatEnabledLocationsByNumber.forEach(chatEnabledLocation->{
                    chatWidgetMap.put(chatEnabledLocation.getBusinessChatWidgetId(), chatEnabledLocation.getBusinessChatWidget());
                });
                businessChatWidgetConfigs.addAll(chatWidgetMap.values());
            }else {
                BusinessDTO businessDTOEnterprise = businessService.getBusinessLiteDTO(business.getEnterpriseId());
                businessChatWidgetConfigs=businessChatWidgetConfigRepository.getWebChatConfigByBusinessNumberAndEnabled(businessDTOEnterprise.getBusinessNumber());
            }
        }else {
            businessChatWidgetConfigs=businessChatWidgetConfigRepository.getWebChatConfigByBusinessNumberAndEnabled(business.getBusinessNumber());
        }
        if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
            businessChatWidgetConfig=businessChatWidgetConfigs.get(0);
            //If email_required is set to true for any widget in config list then return that widget config, else return first config from list
            if(businessChatWidgetConfigs.size()>1) {
                Optional<BusinessChatWidgetConfig> businessChatWidgetConfigOpt=businessChatWidgetConfigs.stream().filter(config->config.getEmailMandatory()==1).findFirst();
                if(businessChatWidgetConfigOpt.isPresent()) {
                    businessChatWidgetConfig=businessChatWidgetConfigOpt.get();
                    log.info("Get microsite config for email mandatory, businessId: {} and returned widgetId is {}",business.getBusinessId(),businessChatWidgetConfig.getId());
                }
            }
            return businessChatWidgetConfig;
        }
        return null;
    }

    public List<Integer> getTeamIdsByWidgetConfigId(Integer widgetConfigId) {
        return businessChatEnabledTeamRepository.findTeamIdByWidgetConfigId(widgetConfigId);
    }

    private void saveBusinessChatEnabledTeam(
            List<Integer> teams, boolean edit, BusinessChatWidgetConfig bcwc){

        if (edit){
            log.info("Deleting teams for config Id: {}",bcwc.getId());
            businessChatEnabledTeamRepository.deleteBusinessChatEnabledTeamByWidgetConfigId(bcwc.getId());
        }

        if (CollectionUtils.isNotEmpty(teams)){
            log.info("saving teams for config Id: {}",bcwc.getId());
            List<BusinessChatEnabledTeam> businessChatEnabledTeams
                    =  teams.stream().collect(Collectors.mapping(teamId -> new BusinessChatEnabledTeam(
                    teamId,bcwc.getId(),bcwc.getUpdatedBy()
            ),Collectors.toList()));

            businessChatEnabledTeamRepository.saveAll(businessChatEnabledTeams);
        }
    }

    @Override
    @Transactional
    public List<Integer> deleteTeamByTeamId(Integer teamId) {
        List<Integer> widgetConfigIds = businessChatEnabledTeamRepository.findWidgetConfigIdByTeamId(teamId);
        businessChatEnabledTeamRepository.deleteBusinessChatEnabledTeamByTeamId(teamId);
        return widgetConfigIds;
    }

    @Cacheable(cacheNames = Constants.LOCATION_CACHE, key="'webchat-'.concat(#chatConfigId).concat('-').concat(#businessDTO.getBusinessId())", unless="#result == null")
    public List<BusinessLocationInfo> getBusinessLocationsData(Integer chatConfigId, BusinessDTO businessDTO) {
        log.info("getBusinessLocationsData widgetId: {} and businessId :{}", chatConfigId, businessDTO.getBusinessId());
        List<Long> chatEnabledLocationIds = new ArrayList<>();
        List<BusinessDTO> childBusinesses = getAllBusinessesInHierarchy(businessDTO);

        if (chatConfigId != null){
            chatEnabledLocationIds = getChatEnabledLocationIds(chatConfigId);
        }
        Map<Integer,WebchatCustomLocationName> webchatCustomLocations=getWebChAtCustomLocationName(businessDTO.getRoutingId());
        List<BusinessLocationInfo> businessLocationInfos = getBusinessLocationInfos(childBusinesses,chatEnabledLocationIds,webchatCustomLocations);
        return businessLocationInfos;
    }

    private Map<Integer, WebchatCustomLocationName> getWebChAtCustomLocationName(Integer accountId) {
        List<WebchatCustomLocationName> customLocationNames=webchatCustomLocationNameRepository.getByAccountId(accountId);
        Map<Integer, WebchatCustomLocationName> customLocationNameMap = customLocationNames.stream().collect(
                Collectors.toMap(WebchatCustomLocationName::getBusinessId, v->v));
        return customLocationNameMap;
    }

    private List<BusinessLocationInfo> getBusinessLocationInfos( List<BusinessDTO> childBusinesses,  List<Long> chatEnabledLocationIds, Map<Integer, WebchatCustomLocationName> webchatCustomLocations){

        Map<Integer, BusinessLocationInfo> businessLocationInfomap = new HashMap<>();
        List<BusinessLocationInfo> businessLocationInfos = new ArrayList<>();
        Map<String, List<BusinessLocationInfo>> addressToBusinessMap = new HashMap<>();
        boolean customLocationNameExists=MapUtils.isNotEmpty(webchatCustomLocations);
        if (CollectionUtils.isNotEmpty(childBusinesses)) {
            for (BusinessDTO business : childBusinesses) {
                String location = prepareLocation(business.getLocation(),
                        business.getBusinessAlias() != null ? business.getBusinessAlias() : business.getBusinessName());
                if (StringUtils.isNotBlank(location)) {
                    BusinessLocationInfo blInfo = new BusinessLocationInfo();
                    blInfo.setBusinessId(business.getBusinessNumber());
                    blInfo.setShortBusinessId(business.getBusinessId());
                    blInfo.setPhone(business.getPhone());
                    blInfo.setEmailId(business.getEmailId());
                    if (business.getLocation() != null) {
                        blInfo.setAddress1(business.getLocation().getAddress1());
                        blInfo.setAddress2(business.getLocation().getAddress2());
                        blInfo.setZipcode(business.getLocation().getZip());
                        blInfo.setCountryCode(business.getLocation().getCountryCode());
                        blInfo.setBusinessAlias(business.getBusinessAlias());
                        blInfo.setBusinessName(business.getBusinessName());
                        blInfo.setName(StringUtils.isNotBlank(business.getBusinessName()) ? business.getBusinessName()
                                : business.getBusinessAlias());
                        blInfo.setCity(business.getLocation().getCity());
                        blInfo.setState(business.getLocation().getState());
                        blInfo.setLocationName(business.getBusinessName());
                        String latitude = business.getLocation().getLatitude();
                        String longitude = business.getLocation().getLongitude();
                        if (StringUtils.isNotBlank(latitude) && StringUtils.isNotBlank(longitude)) {
                            blInfo.setLatitude(Double.valueOf(latitude));
                            blInfo.setLongitude(Double.valueOf(longitude));
                        }
                    }
                    blInfo.setLocation(location);
                    blInfo.setBusinessSMSPhoneNumber(business.getPhoneNumber());
                    // BIRDEYE-26285 - handling duplicate location name with same city and state name (appending alias)
                    getUniqueLocationName(blInfo, addressToBusinessMap);
                    populateChatEnabledFlag( chatEnabledLocationIds, blInfo);
                    populateCustomLocationName(customLocationNameExists,webchatCustomLocations, blInfo);
                    businessLocationInfomap.put(business.getBusinessId(), blInfo);
                    businessLocationInfos.add(blInfo);
                }else {
                    log.info( "Location data not available for business id {0}",
                            business.getBusinessNumber());
                }
            }
        }
        if(CollectionUtils.isNotEmpty(businessLocationInfos))
        {
            Collections.sort(businessLocationInfos,BusinessLocationInfo.businessLocationComparator);
        }
        return businessLocationInfos;

    }


    
    private void populateCustomLocationName(boolean customLocationNameExists, Map<Integer, WebchatCustomLocationName> webchatCustomLocations,
                                            BusinessLocationInfo blInfo) {
        if(customLocationNameExists && webchatCustomLocations.containsKey(blInfo.getShortBusinessId())) {
            WebchatCustomLocationName customLocationName=webchatCustomLocations.get(blInfo.getShortBusinessId());
            blInfo.setPrimaryName(customLocationName.getPrimaryName());
            blInfo.setSecondaryName(customLocationName.getSecondaryName());
        }
    }

    @Override
    public List<Integer> getEnabledWidgetIds() {
        List<Integer> widgetConfigIds = businessChatWidgetConfigRepository.getEnabledWebChatConfigId();
        return widgetConfigIds;
    }

    @Override
    public void updateBusinessChatWidgetConfig(BusinessChatWidgetConfig businessChatWidgetConfig) {
        long startTime = System.currentTimeMillis();
        businessChatWidgetConfigRepository.saveAndFlush(businessChatWidgetConfig);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("updateBusinessChatWidgetConfig", startTime, endTime);

    }

    @Override
    public List<Integer> findWidgetsByStatusUpdatedOn() {
        return businessChatWidgetConfigRepository.findWidgetIdsByStatusUpdatedOn();
    }


    private String prepareLocation(Location location, String alias) {
        if (location == null) {
            return null;
        }
        StringBuilder loc = new StringBuilder();
        if (StringUtils.isNotEmpty(location.getCity())) {
            loc.append(location.getCity());
        }
        if (StringUtils.isNotEmpty(location.getState())) {
            loc.append(", ").append(location.getState());
        }
        if (StringUtils.isNotEmpty(loc)) {
            return loc.toString();
        } else {
            return appendToString(location.getAddress2(), appendToString(location.getAddress1(), appendToString(alias , null, null), " - "), ", ");
        }
    }

    private String appendToString(String toAppend, String originalString, String separator) {
        if (StringUtils.isNotBlank(toAppend)) {
            if (StringUtils.isNotBlank(originalString)) {
                originalString = StringUtils.join(originalString, separator);
            }
            originalString = StringUtils.join(originalString, toAppend);
        }
        return originalString;
    }

    private void getUniqueLocationName(BusinessLocationInfo blInfo, Map<String, List<BusinessLocationInfo>> addressToBusinessMap) {
        if (addressToBusinessMap.containsKey(blInfo.getLocation())) {
            if (addressToBusinessMap.get(blInfo.getLocation()).size() == 1) {
                addressToBusinessMap.get(blInfo.getLocation()).get(0).setLocation(modifyLocationName(addressToBusinessMap.get(blInfo.getLocation()).get(0)));
            }
            addressToBusinessMap.get(blInfo.getLocation()).add(blInfo);
            blInfo.setLocation(modifyLocationName(blInfo));
        } else {
            List<BusinessLocationInfo> blInfoList = new ArrayList<>();
            blInfoList.add(blInfo);
            addressToBusinessMap.put(blInfo.getLocation(), blInfoList);
        }
    }

    private String modifyLocationName(BusinessLocationInfo blInfo) {
        String location = null;
        location = appendToString(blInfo.getAddress2(), appendToString(blInfo.getAddress1(), appendToString(blInfo.getLocation(), location, null), " - "), ", ");
        return location;
    }

    @Override
    public List<BusinessChatLocationHierarchy> getLocationHierarchy(Integer widgetId) {
        return businessChatLocationHierarchyRepository.findBybusinessChatWidgetId(widgetId);
    }

    @Override
    public List<BusinessChatWidgetConfig> getAllWidgets() {
        return businessChatWidgetConfigRepository.findAll();
    }

    //used for List API and config page
    @Cacheable(cacheNames = Constants.LOCATION_CACHE, key="'webchatInternal-'.concat(#chatConfigId)", unless="#result == null")
    public List<BusinessLocationInfo> getBusinessLocationsDataInternal(Integer chatConfigId,List<Long> chatEnabledLocationIds, List<BusinessDTO> childBusinesses) {

        List<BusinessLocationInfo> businessLocationInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(chatEnabledLocationIds)){
            return businessLocationInfos;
        }else {
           return getBusinessLocationInfos(childBusinesses,chatEnabledLocationIds,null);
        }
    }

    @Override
    public boolean disableBusinessChatWidgetConfigsAndLocations(List<Integer> widgetIds) {

        if (CollectionUtils.isNotEmpty(widgetIds)){
            try {
                businessChatWidgetConfigRepository.disableBusinessChatWidgetConfigsAndLocations(widgetIds);
                return true;
            }catch (Exception e){
                log.info("Exception while disabling multi loc widgets:{}",e);
                return false;
            }
        }
        return false;
    }

    @Override
    public List<BusinessChatWidgetConfig> getWebChatConfigByWidgetIds(List<Integer> widgetIds) {
        return businessChatWidgetConfigRepository.getWebChatConfigByWidgetIds(widgetIds);
    }

    @Override
    public Map<Integer, BusinessChatWidgetConfig> getWebChatConfigMap(List<Integer> widgetIds) {
        log.info("getWebChatConfigMap called for ids : {}", widgetIds);
        return getWebChatConfigByWidgetIds(widgetIds).stream()
                .collect(Collectors.toMap(BusinessChatWidgetConfig::getId, v -> v));
    }

    @Override
    public void saveWebChatConfig(List<BusinessChatWidgetConfig> businessChatWidgetConfigs) {
        businessChatWidgetConfigRepository.saveAll(businessChatWidgetConfigs);
    }

    @Override
    public List<Integer> getTeamIdsByWidgetConfigIdOrdered(Integer widgetConfigId) {
        return businessChatEnabledTeamRepository.findTeamIdByWidgetConfigIdOrdered(widgetConfigId);
    }

}

