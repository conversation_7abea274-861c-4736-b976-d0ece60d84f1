package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.birdeye.messenger.util.JSONUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.SingletonMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest.OpType;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.ReviewEventAudit;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.ReviewAttributionEvent;
import com.birdeye.messenger.dto.UnlinkReviewResponse;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BusinessAccountType;
import com.birdeye.messenger.enums.CustomerSentimentEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.ReviewEventTypeEnum;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.ReviewService;
import com.birdeye.messenger.service.AddNoteService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.ReviewEventAuditService;
import com.birdeye.messenger.service.ReviewEventHandlerService;
import com.birdeye.messenger.sro.DeleteReviewEvent;
import com.birdeye.messenger.sro.ReviewAttributionRequest;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.util.DateUtils;
import com.google.common.base.Preconditions;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ReviewEventHandlerServiceImpl implements ReviewEventHandlerService {

    @Autowired
    MessengerContactService messengerContactService;

    @Autowired
    ContactService contactService;

    @Autowired
    BusinessService businessService;

    @Autowired
    private MessengerMessageService messengerMessageService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private ConversationActivityService conversationActivityService;

    @Autowired
    private FirebaseService firebaseService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ReviewEventAuditService reviewEventAuditService;

    @Autowired
    private FirebaseService fcmService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private ElasticSearchExternalService elasticSearchService;

    @Autowired
    private AddNoteService noteService;

    @Override
    @Transactional
    public void handleUpsertEvent(ReviewEvent reviewEvent) throws Exception {
        Integer businessId = reviewEvent.getReviewDetail().getBusinessId();
        Integer accountId = reviewEvent.getReviewDetail().getAccountId();
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
        if (Objects.isNull(businessDTO)){
			log.error("ReviewEvent | No business found for businessId:{} and accountId: {} ",businessId,reviewEvent.getReviewDetail().getAccountId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
        boolean isMessengerEnabled = businessService.getMessengerEnabled(accountId);
        String accountType = getAccountType(businessDTO);
        String type = getBusinessType(businessDTO);

        Optional<Lock> lockOpt = Optional.empty();
        String lockKey = reviewEvent.getReviewDetail().getCustomerId() == null
                ? Constants.REVIEWER_ID_PREFIX + reviewEvent.getReviewDetail().getReviewerId().toString()
                : Constants.CUSTOMER_ID_PREFIX + reviewEvent.getReviewDetail().getCustomerId().toString();
        try {
            if (reviewEvent.getEventType().equals(ReviewEventTypeEnum.ACTION_UPDATE)) {
                if (!checkIfReviewExists(reviewEvent.getReviewDetail().getReviewId(), accountId)) {
                    log.info("Action update event encountered before ADD review event.Hence returning!!");
                    return;
                }
            }
            lockOpt = redisLockService.tryLock(lockKey);
            if (lockOpt.isPresent()) {
                if (((accountType != null && !accountType.equals(BusinessAccountType.DIRECT.getType()))
                        || (type != null && "Product".equals(type))) && !isMessengerEnabled) {
                    log.info("Messenger is disabled for business id and account Type is not direct:{}", businessId);
                    return;
                }
                MessengerContact mc = createAndUpdateReviewsInInbox(reviewEvent, businessId, isMessengerEnabled,businessDTO);
                if (isMessengerEnabled) {
                    FirebaseDto firebaseDto = new FirebaseDto();
                    firebaseDto.setAccountId(accountId);
                    firebaseDto.setBusinessId(businessDTO.getBusinessId());
                    if (ReviewEventTypeEnum.ADD.equals(reviewEvent.getEventType())) {
                        firebaseDto.setCRead(false);
                        firebaseDto.setViewedBy(new ArrayList<>());
                        firebaseDto.setCId(reviewEvent.getReviewDetail().getCustomerId());
                    }
                    if (mc != null) {
                    	firebaseDto.setMcId(mc.getId());
                    }
                    firebaseService.mirrorOnWeb(firebaseDto);
                }

            } else {
                log.info("Lock is already acquired for the key:{}", lockKey);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.REVIEW_EVENT_ADD_DELAYED_QUEUE, reviewEvent);
            }
        } catch (Exception e) {
            auditFailedEvents(reviewEvent, e.getMessage());
            log.error("Exception while creating or updating review in inbox:{}",
                    reviewEvent.getReviewDetail().getReviewId(), e);
            throw e;
        } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }
        }
    }

    private MessengerContact createAndUpdateReviewsInInbox(ReviewEvent reviewEvent, Integer businessId, boolean isMessengerEnabled, BusinessDTO businessDTO)
            throws Exception {

        Integer customerId = reviewEvent.getReviewDetail().getCustomerId();
        MessengerMessage messengerMessage=null;
        MessengerContact mc = null;
        if (Objects.nonNull(customerId)) {
        	if (ReviewEventTypeEnum.MIGRATED.equals(reviewEvent.getEventType())) {
        		messengerMessage= messengerMessageService.findByReviewId(reviewEvent.getReviewDetail().getReviewId());
        		if (Objects.nonNull(messengerMessage)) {
        			mc = attributeReview(reviewEvent, customerId, messengerMessage);
        		}
        		return mc;
        	}
        	List<MessengerContact> messengerContacts = messengerContactService.findByCustomerIdAndBusinessId(customerId,
        			businessId);
        	if (CollectionUtils.isNotEmpty(messengerContacts)) {
        		mc = messengerContacts.get(0);
        	}
        	if (Objects.isNull(mc)) {
        		if (Objects.nonNull(reviewEvent.getReviewDetail().getReviewerId())) {
        			mc = messengerContactService.findByReviewerIdAndBusinessId(reviewEvent.getReviewDetail().getReviewerId(), businessId);
        		}
        	}
        	if (Objects.nonNull(mc)) {
        		mc = updateExistingConversation(reviewEvent, mc, isMessengerEnabled,businessDTO);
        	} else {
        		mc = createNewConversation(reviewEvent, isMessengerEnabled);
        	}
        } else if (Objects.nonNull(reviewEvent.getReviewDetail().getReviewerId())) {
            mc = messengerContactService.findByReviewerIdAndBusinessId(reviewEvent.getReviewDetail().getReviewerId(), businessId);
            if (Objects.nonNull(mc)) {
                mc = updateExistingConversation(reviewEvent, mc, isMessengerEnabled,businessDTO);
            } else {
                mc = createNewConversation(reviewEvent, isMessengerEnabled);
            }
        } else {
            log.info("Customer and reviewer is not present.Hence unable to create conversation!!");
        }
        return mc;
    }

    private MessengerContact attributeReview(ReviewEvent reviewEvent, Integer customerId, MessengerMessage messengerMessage) {
        MessengerContact messengerContact = messengerContactService
                .findById(messengerMessage.getMessengerContactId());
        if (messengerContact.getCustomerId() != null) {
            log.info("Review id {} is already linked to customer id {}",
                    reviewEvent.getReviewDetail().getReviewId(), messengerContact.getCustomerId());
            return null;
        }
        ReviewAttributionRequest reviewAttributionRequest = createReviewAttributionRequest(
                messengerContact.getId(), reviewEvent);
        linkReview(null, reviewEvent.getReviewDetail().getAccountId(), reviewAttributionRequest, false);
        log.info("Review id {} linked to customer id {}", reviewEvent.getReviewDetail().getReviewId(),
                customerId);
        return messengerContact;
    }

    private ReviewAttributionRequest createReviewAttributionRequest(Integer mcId,
            ReviewEvent reviewEvent) {
        ReviewAttributionRequest request = new ReviewAttributionRequest();
        request.setAccountId(reviewEvent.getReviewDetail().getAccountId());
        request.setBusinessId(reviewEvent.getReviewDetail().getBusinessId());
        request.setCustomerId(reviewEvent.getReviewDetail().getCustomerId());
        request.setMcId(mcId);
        return request;
    }

    private MessengerContact updateExistingConversation(ReviewEvent reviewEvent, MessengerContact messengerContact,
            boolean isMessengerEnabled,BusinessDTO businessDTO) {
        Optional<ContactDocument> contactDocumentOptional = messengerContactService
                .getContactDocument(reviewEvent.getReviewDetail().getAccountId(), messengerContact.getId());
        //Bad data handling | Creating conversation at runtime if contact is present in DB but not in ES
		if (!contactDocumentOptional.isPresent()) {
			contactDocumentOptional=createContactDocument(reviewEvent, messengerContact, businessDTO);
		}
        if (contactDocumentOptional.isPresent()) {
            ContactDocument contactDocument = contactDocumentOptional.get();
            ContactDocument conversationDoc=new ContactDocument();
            boolean criticalUpdate = checkForCriticalUpdatesInReviewEvent(contactDocument,
                    reviewEvent.getReviewDetail());
            updateLatestReviewDataAndSentiment(reviewEvent, contactDocument, messengerContact,conversationDoc);
            if (!reviewEvent.getEventType().equals(ReviewEventTypeEnum.SYNC)
                    && criticalUpdate) {
                markConversationAsOpenAndUnread(reviewEvent, conversationDoc, messengerContact);
                // as if review is added or updated conversation if hidden should be marked as
                // unhidden.
                conversationDoc.setHide(false);
            }
            updateReviewDetails(reviewEvent, contactDocument,conversationDoc);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            conversationDoc.setUpdatedAt(simpleDateFormat.format(new Date()));
            messengerContact.setUpdatedAt(new Date());
            if (reviewEvent.getReviewDetail().getReviewerId() != null) {
                messengerContact.setReviewerId(reviewEvent.getReviewDetail().getReviewerId());
            }
            messengerContactService.saveOrUpdateMessengerContact(messengerContact);
            // create or update messengerMessage
            createMessengerMessage(reviewEvent, messengerContact.getId());
            boolean updated = messengerContactService.updateContactOnES(messengerContact.getId(), conversationDoc,
                    reviewEvent.getReviewDetail().getAccountId());
            if (BooleanUtils.isTrue(updated)) {
                MessageDocument messageDocument = messengerContactService.addReviewToMessages(reviewEvent,
                        reviewEvent.getReviewDetail().getAccountId(), reviewEvent.getReviewDetail().getBusinessId(),
                        messengerContact.getId());
                if (Objects.nonNull(messageDocument)) {
                    publishBrowserNotification(reviewEvent, contactDocument, messageDocument,
                            businessService.getBusinessLiteDTO(reviewEvent.getReviewDetail().getBusinessId()),
                            isMessengerEnabled);
                } else {
                    throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_MESSENGER_MESSAGE);
                }
            } else {
                log.error("Error while updating contact Document in ES for reviewId:{} , AccountId:{}",
                        reviewEvent.getReviewDetail().getReviewId(), reviewEvent.getReviewDetail().getAccountId());
                throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_CONTACT_DOCUMENT);
            }
        } else {
            log.error("No contact Document exist in ES for Mcid:{} , businessId:{}", messengerContact.getId(),
                    messengerContact.getBusinessId());
            // throw new MessengerException(ErrorCode.NO_CONTACT_DOCUMENT_EXIST_IN_ES);
        }
        return messengerContact;
    }

	private Optional<ContactDocument> createContactDocument(ReviewEvent reviewEvent, MessengerContact messengerContact,
			BusinessDTO businessDTO) {
		CustomerDTO customerDTO=null;
		if (reviewEvent.getReviewDetail().getCustomerId() != null) {
		    	customerDTO = contactService.findById(reviewEvent.getReviewDetail().getCustomerId());
		}
		ContactDocument contactDocument = messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.getMessageTagById(messengerContact.getTag()), null);
		log.info("No contact Document exist in ES for Mcid:{} , businessId:{} , creating it at runtime",
				messengerContact.getId(), messengerContact.getBusinessId());
		if(contactDocument!=null) {
			return Optional.of(contactDocument);
		}
		return Optional.empty();
	}
	private boolean checkForCriticalUpdatesInReviewEvent(ContactDocument contactDocument,
            ReviewEvent.Review reviewEvent) {
        // BIRDEYE-97006 - Review conversation will be reopened only if ->
        // there is any change in Review comment, Review rating or Reviewer name
        boolean crticalUpdateFound = false;
        Optional<Review> reviewOpt = Optional.empty();
        List<Review> reviews = contactDocument.getReviews();
        if (CollectionUtils.isNotEmpty(reviews)) {
            reviewOpt = reviews.stream().filter(r -> reviewEvent.getReviewId().equals(r.getId())).findFirst();
        }
        if (reviewOpt.isPresent()) {
            Review existingReview = reviewOpt.get();
            if (reviewEvent.getComment() != null
                    && !reviewEvent.getComment().equalsIgnoreCase(existingReview.getCmnt())) {
                crticalUpdateFound = true;
            }
            if (reviewEvent.getRating() != null && !reviewEvent.getRating().equals(existingReview.getRtng())) {
                crticalUpdateFound = true;
            }
        } else {
            // new review
            crticalUpdateFound = true;
        }
        return crticalUpdateFound;
    }

    private void updateLatestReviewDataAndSentiment(ReviewEvent review, ContactDocument contactDocument,
            MessengerContact mc,ContactDocument conversationDoc) {
        if ((contactDocument.getL_review_on() == null)
                || (contactDocument.getL_review_on() <= review.getReviewDetail().getReviewDate())) {
        	conversationDoc.setL_review_on(review.getReviewDetail().getReviewDate());
            updateSentimentDetails(review, conversationDoc);
            mc.setLastReviewDate(new Date(review.getReviewDetail().getReviewDate()));
            if (review.getReviewDetail().getCustomerId() == null) {
            	conversationDoc.setL_rvr_name(review.getReviewDetail().getReviewerName());
            }
        }
    }

    private MessengerContact createNewConversation(ReviewEvent reviewEvent, boolean isMessengerEnabled) {
        MessengerContact mc = messengerContactService.createMessengerContactAndMessengerMessage(reviewEvent);
        if (Objects.isNull(mc)) {
            throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_MESSENGER_CONTACT_AND_MESSAGE);
        }
        createMessengerMessage(reviewEvent, mc.getId());
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(reviewEvent.getReviewDetail().getBusinessId());
        if (Objects.nonNull(businessDTO)) {
            Optional<ContactDocument> contactDocumentOptional = createContactDocument(reviewEvent, businessDTO, mc);
            if (contactDocumentOptional.isPresent()) {
                MessageDocument messageDocument = messengerContactService.addReviewToMessages(reviewEvent,
                        reviewEvent.getReviewDetail().getAccountId(), reviewEvent.getReviewDetail().getBusinessId(),
                        mc.getId());
                if (Objects.nonNull(messageDocument)) {
                    publishBrowserNotification(reviewEvent, contactDocumentOptional.get(), messageDocument, businessDTO,
                            isMessengerEnabled);
                } else {
                    throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_MESSENGER_MESSAGE);
                }
            } else {
                log.error("Error while updating contact Document in ES for reviewId:{} , AccountId:{}",
                        reviewEvent.getReviewDetail().getReviewId(), reviewEvent.getReviewDetail().getAccountId());
                throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_CONTACT_DOCUMENT);
            }
        }
        return mc;
    }

    private ContactDocument.Review DTOtoDocumentConverter(ReviewEvent reviewEvent, ContactDocument.Review review) {
        ReviewEvent.Review fromReview = reviewEvent.getReviewDetail();
        if (Objects.isNull(review)) {
            review = new ContactDocument.Review();
        }
        review.setAsstd_by(fromReview.getAssistedBy());
        review.setCmnt(fromReview.getComment());
        review.setFeatured(fromReview.getFeatured());
        review.setId(fromReview.getReviewId());
        review.setRcmded(fromReview.getRecommended());
        review.setRdate(fromReview.getReviewDate());
        review.setRespnded(fromReview.getIsResponded());
        review.setRtng(Objects.isNull(fromReview.getRating()) ? 0 : fromReview.getRating());
        review.setRvr_name(fromReview.getReviewerName());
        review.setShrd(fromReview.getIsShared());
        review.setS_id(fromReview.getSourceId());
        review.setStatus(fromReview.getStatus());
        review.setTag_ids(fromReview.getTagIds());
        review.setTcktd(fromReview.getIsTicketed());
        review.setUpdtd(fromReview.getIsUpdated());
        review.setRvr_id(fromReview.getReviewerId());
        return review;
    }

    private void createMessengerMessage(ReviewEvent reviewEvent, Integer mcId) {
        MessengerMessage messengerMessage = messengerMessageService.findByReviewId(reviewEvent.getReviewDetail().getReviewId());
        if (Objects.nonNull(messengerMessage)) {
            // update with current timestamp
            messengerMessage.setCreatedDate(new Date());
            messengerMessage.setAccountId(reviewEvent.getReviewDetail().getAccountId());
            messengerMessage.setMessengerContactId(mcId);
            messengerMessageService.updateMessengerMessage(messengerMessage);
        } else {
            messengerMessageService.saveMessengerMessageReviewDetails(reviewEvent, mcId);
        }
    }

    private void markConversationAsOpenAndUnread(ReviewEvent reviewEvent, ContactDocument contactDocument,
            MessengerContact messengerContact) {
        contactDocument.setViewedBy(new ArrayList<>());
        contactDocument.setC_read(false);
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);
        if (Objects.nonNull(reviewEvent.getReviewDetail().getReviewDate())
                && DateUtils.calculateTimeDifference(System.currentTimeMillis(),
                        reviewEvent.getReviewDetail().getReviewDate(), TimeUnit.DAYS) < 30l) {
            contactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
            messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        }
    }

    @Transactional
    public void handleDeleteEvent(DeleteReviewEvent deleteReviewEvent) throws Exception {
        String lockKey = "";
        Optional<Lock> lockOpt = Optional.empty();
        try {
            Integer reviewId = deleteReviewEvent.getReviewDetail().getReviewId();
            Integer accountId = deleteReviewEvent.getReviewDetail().getAccountId();
            Integer businessId = deleteReviewEvent.getReviewDetail().getBusinessId();
            ContactDocument conversation = getConversationByReviewId(reviewId,businessId, accountId);
            if (Objects.isNull(conversation)) {
                return;
            }
            if (conversation.getC_id() != null) {
                lockKey = Constants.CUSTOMER_ID_PREFIX + conversation.getC_id();
                lockOpt = redisLockService.tryLock(lockKey);
            } else if (CollectionUtils.isNotEmpty(conversation.getReviews())) {
                lockKey = Constants.REVIEWER_ID_PREFIX + conversation.getReviews().get(0).getRvr_id();
                lockOpt = redisLockService.tryLock(lockKey);
            }
            if (lockOpt.isPresent()) {
                Integer conversationId = conversation.getM_c_id();
                Map<String, List<Integer>> result = null;
                Optional<Review> reviewOpt = getReview(conversation, reviewId);
                boolean isSingleReviewConversation = messageService.isSingleReviewConversation(conversationId,
                        accountId,
                        reviewId);
                if (isSingleReviewConversation) {
                    log.info("deleting single review conversation Id:{}", conversationId);
                    boolean conversationDeleted =
                            conversationService.deleteConversation(conversationId, accountId,RefreshPolicy.IMMEDIATE);
                    if (conversationDeleted) {
                        boolean deleted = messengerContactService.deleteMessengerContact(conversationId);
                        if (deleted) {
                            List<MessengerMessage> messengerMessages = messengerMessageService
                                    .deleteByMCId(conversationId);
                            if (CollectionUtils.isNotEmpty(messengerMessages)) {
                                result = messengerMessages.stream()
                                        .collect(Collectors.groupingBy(MessengerMessage::getMessageType,
                                                Collectors.mapping(MessengerMessage::getMessageId,
                                                        Collectors.toList())));
                                deleteConversationActivities(result, conversationId);
                            }
                            messageService.deleteMessagesByMcIdWithRefresh(conversationId, accountId, false);
                        }
                    }
                } else {
                    messengerMessageService.deleteByMessengerContactIdAndReviewId(conversationId, reviewId);
                    messageService.deleteReviewByIdAndMcId(reviewId,conversationId,accountId);
                    updateLastReviewOnAndReviewerName(accountId, conversation, reviewOpt.get());
                    conversationService.removeReviewFromOldConversation(conversation, accountId, reviewId);
                    messengerContactService.updateContactOnES(conversationId, conversation, accountId);
                }
                // firebaseService.mirrorOnWeb(accountId, businessId);
                FirebaseDto firebaseDto = new FirebaseDto();
                firebaseDto.setAccountId(accountId);
                firebaseDto.setBusinessId(businessId);
                firebaseDto.setMcId(conversationId);
                firebaseService.mirrorOnWeb(firebaseDto);
            } else {
                log.info("Lock is already acquired for the key:{}", lockKey);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.REVIEW_EVENT_DELETE_DELAYED_QUEUE, deleteReviewEvent);
            }
        } catch (Exception e) {
            auditFailedEvents(deleteReviewEvent, e.getMessage());
            log.error("Exception while deleting review Id:{}",
                    deleteReviewEvent.getReviewDetail().getReviewId(), e);
            throw e;
        } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }

        }
    }

    private void auditFailedEvents(DeleteReviewEvent deleteReviewEvent, String message) {
        DeleteReviewEvent.Review review = deleteReviewEvent.getReviewDetail();
        ReviewEventAudit reviewEventAudit = new ReviewEventAudit(review.getReviewId(), review.getReviewerId(),
                review.getBusinessId(),
                review.getAccountId(), review.getCustomerId(), "Failed", deleteReviewEvent.getEventType().toString(),
                message, new Date());
        reviewEventAuditService.saveReviewEventAudit(reviewEventAudit);
    }

    private ContactDocument getConversationByReviewId(Integer reviewId, Integer businessId, Integer accountId) {
        MessengerFilter filter = new MessengerFilter();
        Map<String, Object> params = new HashMap<>();
        params.put("businessId", businessId);
        filter.setAccountId(accountId);
        filter.setReviewId(reviewId);
        filter.setQueryFile(Constants.Elastic.GET_CONVERSATION_BY_REVIEW_ID);
        filter.setParams(params);
        List<ContactDocument> contactFromES = messengerContactService.getConversationFromES(filter);
        if (CollectionUtils.isEmpty(contactFromES)) {
            log.info("No conversation exisits for reviewId: {} and accountId: {} ", reviewId, accountId);
            return null;
        }
        ContactDocument conversation = contactFromES.get(0);
        return conversation;
    }

    private void updateLastReviewOnAndReviewerName(Integer accountId, ContactDocument conversation,
            Review review) {
        if (review.getRdate() != null && review.getRdate().equals(conversation.getL_review_on())) {
            log.info("updating last review_on for conversation Id:{}", conversation.getM_c_id());
            Long lReviewOn = conversationService.getLastReviewOrSurveyOn(conversation, accountId,
                    review.getId(), Constants.REVIEW);
            conversation.setL_review_on(lReviewOn);
            MessengerContact messengerContact = messengerContactService.findById(conversation.getM_c_id());
            messengerContact.setLastReviewDate(lReviewOn != null ? new Date(lReviewOn) : null);
            messengerContactService.saveOrUpdateMessengerContact(messengerContact);
        }
    }

    private void updateLastReviewOnAndReviewerName(Integer accountId, ContactDocument conversation,
            Review review, MessengerContact messengerContact) {
        if (review.getRdate() != null && review.getRdate().equals(conversation.getL_review_on())) {
            log.info("updating last review_on for conversation Id:{}", conversation.getM_c_id());
            Long lReviewOn = conversationService.getLastReviewOrSurveyOn(conversation, accountId,
                    review.getId(), Constants.REVIEW);
            conversation.setL_review_on(lReviewOn);
            if (lReviewOn == null) {
                conversation.setL_rvr_name(null);
            }
            messengerContact.setLastReviewDate(lReviewOn != null ? new Date(lReviewOn) : null);
            messengerContactService.saveOrUpdateMessengerContact(messengerContact);
        }
    }

    private Optional<Review> getReview(ContactDocument conversation, Integer reviewId) {
        Optional<Review> reviewOpt = Optional.empty();
        List<Review> reviews = conversation.getReviews();
        if (CollectionUtils.isNotEmpty(reviews)) {
            reviewOpt = reviews.stream().filter(r -> reviewId.equals(r.getId())).findFirst();
        }
        if (!reviewOpt.isPresent()) {
            throw new MessengerException(ErrorCode.INVALID_REVIEW_ID);
        }
        return reviewOpt;
    }

    private void auditFailedEvents(ReviewEvent reviewEvent, String message) {
        ReviewEvent.Review review = reviewEvent.getReviewDetail();
        ReviewEventAudit reviewEventAudit = new ReviewEventAudit(review.getReviewId(), review.getReviewerId(),
                review.getBusinessId(),
                review.getAccountId(), review.getCustomerId(), "Failed", reviewEvent.getEventType().toString(), message,
                new Date());

        reviewEventAuditService.saveReviewEventAudit(reviewEventAudit);
    }

    private void publishBrowserNotification(ReviewEvent reviewEvent, ContactDocument cd, MessageDocument md,
            BusinessDTO businessDTO, boolean isMessengerEnabled) {
        // publish notification in case of add review only and if reviewdate lies within
        // last 7 days
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -7);
        Long dateToCompare = calendar.getTimeInMillis();

        if (reviewEvent.getEventType().equals(ReviewEventTypeEnum.ADD)
                && reviewEvent.getReviewDetail().getReviewDate() > dateToCompare && isMessengerEnabled) {
            firebaseService.pushFCMNotificationForWEB(cd, md, businessDTO, null, null, reviewEvent.getReviewDetail());
        }
    }

    private Optional<ContactDocument> createContactDocument(ReviewEvent reviewEvent, BusinessDTO businessDTO,
            MessengerContact mc) {
        ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
        contactBuilder.addBusinessInfo(businessDTO).addMessengerContactInfo(mc)
                .addReviewDetails(reviewEvent.getReviewDetail());
        if (reviewEvent.getReviewDetail().getCustomerId() != null) {
            CustomerDTO customerDTO = contactService.findById(reviewEvent.getReviewDetail().getCustomerId());
            if (Objects.nonNull(customerDTO)) {
                contactBuilder.addCustomerInfo(customerDTO);
            }
        } else {
            Integer score = evaluateSentimentScore(reviewEvent);
            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setSentiment(CustomerSentimentEnum.fromScore(score).value());
            customerDTO.setSentimentScore(score);
            contactBuilder.addSentimentDetails(customerDTO);
        }
        ContactDocument cd = contactBuilder.build();
        cd.setL_rvr_name(Objects.isNull(reviewEvent.getReviewDetail().getCustomerId())
                ? reviewEvent.getReviewDetail().getReviewerName()
                : null);
        cd.setL_review_on(reviewEvent.getReviewDetail().getReviewDate());
        cd.setC_tag(MessengerTagEnum.UNREAD.getId());
        if (Objects.nonNull(reviewEvent.getReviewDetail().getReviewDate())
                && DateUtils.calculateTimeDifference(System.currentTimeMillis(),
                        reviewEvent.getReviewDetail().getReviewDate(), TimeUnit.DAYS) >= 30l) {
            cd.setC_tag(MessengerTagEnum.DONE.getId());
            mc.setTag(MessengerTagEnum.DONE.getId());
        }
        cd.setViewedBy(new ArrayList<>());
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        cd.setUpdatedAt(df.format(new Date()));
        cd.setHide(false);
        boolean updated = messengerContactService.upsertContactDocumentOnESWithRefresh(cd, mc.getId().toString(),
                reviewEvent.getReviewDetail().getAccountId(),true);
        if (updated) {
            return Optional.of(cd);
        }
        return Optional.empty();
    }

    private Optional<ContactDocument> createContactDocument(Review review, BusinessDTO businessDTO,
            MessengerContact mc,boolean refresh) {
        ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
        contactBuilder.addBusinessInfo(businessDTO).addMessengerContactInfo(mc);
        ContactDocument cd = contactBuilder.build();
        cd.setReviews(Arrays.asList(review));
        cd.setL_rvr_name(review.getRvr_name());
        cd.setL_review_on(review.getRdate());
        cd.setC_tag(mc.getTag());
        List<Integer> viewedBy = mc.getViewedBy() != null
                ? Stream.of(mc.getViewedBy().split(",", -1)).map(Integer::valueOf)
                        .collect(Collectors.toList())
                : null;
        cd.setViewedBy(viewedBy);
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        cd.setUpdatedAt(df.format(new Date()));
        cd.setHide(false);
        Integer score = evaluateSentimentScore(review);
        cd.setSentimentScore(score);
        cd.setSentiment(CustomerSentimentEnum.fromScore(score).value());
        boolean updated = messengerContactService.upsertContactDocumentOnESWithRefresh(cd, mc.getId().toString(),
                businessDTO.getRoutingId(),refresh);
        if (updated) {
            return Optional.of(cd);
        }
        return Optional.empty();
    }

    private void updateReviewDetails(ReviewEvent reviewEvent, ContactDocument contactDocument,ContactDocument conversationDoc) {
        Optional<ContactDocument.Review> existingReview = Optional.empty();
        List<ContactDocument.Review> reviews = contactDocument.getReviews();
		if (CollectionUtils.isNotEmpty(reviews)) {
			existingReview = reviews.stream().filter(r -> r.getId().equals(reviewEvent.getReviewDetail().getReviewId()))
					.findFirst();
		}
		if (existingReview.isPresent()) {
			DTOtoDocumentConverter(reviewEvent, existingReview.get());
			conversationDoc.setReviews(contactDocument.getReviews());
		} else {
            ContactDocument.Review review = DTOtoDocumentConverter(reviewEvent, null);
            List<ContactDocument.Review> reviewsList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(contactDocument.getReviews())) {
                reviewsList = contactDocument.getReviews();
            }
            reviewsList.add(review);
            conversationDoc.setReviews(reviewsList);
        }
    }

    private void deleteConversationActivities(Map<String, List<Integer>> result, Integer mcId) {
        if (MapUtils.isNotEmpty(result)) {
            Map<String, Integer> activitiesLabelMap = Arrays.asList(ActivityType.values()).stream()
                    .collect(Collectors.toMap(ActivityType::getLabel, ActivityType::getId));

            List<Integer> activityIds = result.entrySet().stream()
                    .filter(e -> (e.getKey() != null && activitiesLabelMap.get(e.getKey()) != null))
                    .map(Map.Entry::getValue).flatMap(i -> i.stream()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(activityIds)) {
                conversationActivityService.deleteConversationActivityUsingIds(activityIds);
                log.info("Deleting conversation activities for ids: {} and contact id: {}", activityIds, mcId);
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public UnlinkReviewResponse unlinkReview(Integer userId, Integer accountId, Integer reviewId,
            Integer conversationId, boolean isCustomerDelete, boolean publishEvent) {
        UnlinkReviewResponse unlinkReviewResponse = new UnlinkReviewResponse();

        String lockKey = "";
        ContactDocument conversation = messengerContactService.getContact(accountId, conversationId);
        Optional<Lock> lockOpt = Optional.empty();
        if (conversation.getC_id() != null) {
            lockKey = Constants.CUSTOMER_ID_PREFIX + conversation.getC_id();
            lockOpt = redisLockService.tryLock(lockKey);
        }
        try {
            if (lockOpt.isPresent()) {
                BusinessDTO businessDTO = businessService.getBusinessLiteDTO(conversation.getB_id());
                MessengerContact messengerContact = messengerContactService.findById(conversationId);
                validateRequest(conversationId, messengerContact, businessDTO, conversation.getB_id());
                MessengerMessage messengerMessage = messengerMessageService
                        .findByMessengerContactIdAndReviewId(conversationId, reviewId);
                Optional<Review> reviewOpt = getReview(conversation, reviewId);
                // Creating new MessengerContact
                MessengerContact newMessengerContact = messengerContactService.createMessengerContact(messengerContact,
                        reviewOpt.get());
                if (messengerMessage != null) {
                    messengerMessage.setMessengerContactId(newMessengerContact.getId());
                    // Updating new mcId in Messenger Message
                    messengerMessageService.updateMessengerMessage(messengerMessage);
                }
                boolean isSingleReviewConversation = messageService.isSingleReviewConversation(conversation.getM_c_id(),
                        accountId, reviewId);
                if (isSingleReviewConversation && !isCustomerDelete) {
                    log.info("deleting single message conversation Id:{}", conversationId);
                    conversationService.deleteConversation(conversationId, accountId,RefreshPolicy.IMMEDIATE);
                    messengerContactService.deleteMessengerContact(conversationId);
                    deleteConversationActivites(accountId, conversationId);
                    messengerMessageService.deleteAllExceptReviewByMCId(conversationId);
                    unlinkReviewResponse.setContactDeleted(true);
                } else {
                    updateLastReviewOnAndReviewerName(accountId, conversation, reviewOpt.get(), messengerContact);
                    conversationService.removeReviewFromOldConversation(conversation, accountId, reviewId);

                    messengerContactService.upsertContactDocumentOnESWithRefresh(conversation,
                            String.valueOf(conversationId), accountId, false);
                }
                createNewConversation(accountId, reviewId, businessDTO, reviewOpt, newMessengerContact,!isCustomerDelete);
                messageService.updateNewConversationIdInMessageDoc(reviewId, accountId, newMessengerContact.getId(),
                        conversation.getM_c_id());
                if (publishEvent) {
                    publishReviewUnlinkEvent(reviewId, conversation.getC_id(), userId, Constants.UNLINK,
                            new Date().getTime());
                }
                // 13. Push Change Event to FireBase
                // fcmService.mirrorOnWeb(accountId, businessDTO.getBusinessId());
                FirebaseDto firebaseDto = new FirebaseDto();
                firebaseDto.setAccountId(accountId);
                firebaseDto.setBusinessId(businessDTO.getBusinessId());
                firebaseDto.setMcId(newMessengerContact.getId());
                fcmService.mirrorOnWeb(firebaseDto);
                unlinkReviewResponse.setMcId(newMessengerContact.getId());
                return unlinkReviewResponse;
            } else {
                throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
            }
        } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }
        }
    }

    private void createNewConversation(Integer accountId, Integer reviewId, BusinessDTO businessDTO,
            Optional<Review> reviewOpt, MessengerContact newMessengerContact,boolean refresh) {
        Optional<ContactDocument> contactDocumentOptional = createContactDocument(reviewOpt.get(), businessDTO,
                newMessengerContact,refresh);
        if (!contactDocumentOptional.isPresent()) {
            log.error("Error while creating contact Document in ES for reviewId:{} , AccountId:{}", reviewId,
                    accountId);
            throw new MessengerException(ErrorCode.ERROR_WHILE_CREATING_CONTACT_DOCUMENT);
        }
    }

    private void deleteConversationActivites(Integer accountId, Integer conversationId) {
        Map<String, List<Integer>> result = null;
        List<MessengerMessage> messengerMessages = messengerMessageService.findByMessengerContactId(conversationId);
        if (CollectionUtils.isNotEmpty(messengerMessages)) {
            result = messengerMessages.stream().collect(Collectors.groupingBy(MessengerMessage::getMessageType,
                    Collectors.mapping(MessengerMessage::getMessageId, Collectors.toList())));
            deleteConversationActivities(result, conversationId);
            messageService.deleteActivitiesByConversationId(conversationId, accountId);
        }
    }

    private void publishReviewUnlinkEvent(Integer reviewId, Integer customerId, Integer userId, String eventType,
            Long at) {
        List<Long> reviewIds = new ArrayList<>();
        reviewIds.add(reviewId.longValue());
        ReviewAttributionEvent event = new ReviewAttributionEvent(reviewIds, customerId, userId, eventType, at);
        try {
            reviewService.attributeReviews(event);
        } catch (Exception e) {
            log.error("Exception occurred in sync call to review-service", e);
            kafkaService.publishToKafkaAsync(KafkaTopicEnum.REVIEW_LINK_UNLINK_EVENT, event);
        }
    }

    private void validateRequest(Integer conversationId, MessengerContact messengerContact, BusinessDTO businessDTO,
            Integer businessId) {
        if (messengerContact == null) {
            log.info("messenger contact Id {} does not exist for conversationId: {}", conversationId);
            throw new BadRequestException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        if (businessDTO == null) {
            log.info("Business Id {} does not exist for conversationId: {}", businessId, conversationId);
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
    }

    /**
     * API usage : Link a review only conversation to a actual customer
     * conversation.
     * Why ?
     * Answer: Review only conversation doesn't have a customerId( so no email or
     * phone number available)
     * hence no messages can be sent out to for those conversations.
     * To enable communication(text,email) for those conversation they
     * need to be linked to customerId(phone, email). This api does the same.
     *
     * From where this API is called ?
     * Answer: a. From inbox (only for review_date > 15th Jan 2021 data is migrated
     * to inbox).
     * API runs on mcId since we already have a review conversation created by ADD
     * review event
     * b. From ADD review event (latest reviews ingested from review-service) - when
     * reviews are auto-attributed
     * c. From reviews tab (can be called for any review new or historical)
     * Cases and gotcha to understand:
     * 1. Review-only (no sms, facebook, etc messages .. only contains review)
     * conversation doesn't have a customerId
     * 2. Two reviews (different reviewIds) with same reviewerId can be linked to
     * two different customer conversations.
     * Hence reviewId is important key here, reviewerId is not that important.
     * Queries should be run on bases of reviewId instead of reviewerId
     * 3. Historic reviews (before 15th Jan 2021) do not have conversations in
     * inbox. Link review request for those
     * reviews will only come from reviews tab so we'll have to fetch review from
     * review-service on the fly.
     * 4. Review only conversation may contain more than 1 review but both reviews
     * should have same reviewerId
     */
    @Override
    @Transactional
    public Integer linkReview(Integer userId, Integer accountId, ReviewAttributionRequest reviewAttributionRequest,
            boolean publishLinkEventToReviewService) {
        Optional<Lock> lockAcquiredCustomerOpt = Optional.empty();
        Optional<Lock> lockAcquiredReviewerOpt = Optional.empty();
        String lockKeyReviewer = "";
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(reviewAttributionRequest.getBusinessId());
        if (Objects.isNull(businessDTO)) {
            log.info("No business found for id:{}", reviewAttributionRequest.getBusinessId());
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        CustomerDTO customerDTO = null;
        if (Objects.isNull(reviewAttributionRequest.getCustomerId())) {
            // create new customer
            customerDTO = createCustomer(reviewAttributionRequest, businessDTO, userId);
        } else {
            // get existing customer
            customerDTO = contactService.findById(reviewAttributionRequest.getCustomerId());
        }
        if (Objects.nonNull(customerDTO)) {
            String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
            lockAcquiredCustomerOpt = redisLockService.tryLock(lockKeyCustomer, 2, TimeUnit.SECONDS);
            try {
                if (lockAcquiredCustomerOpt.isPresent()) {
                    SingletonMap map = findMessengerContact(reviewAttributionRequest, businessDTO);
                    String condition = (String) map.getKey();
                    MessengerContact fromMessengerContact = (MessengerContact) map.getValue();
                    // fromMessengerContact - not null means either we have found it by mcId or by
                    // reviewId
                    if (Objects.nonNull(fromMessengerContact) || "CREATE".equals(condition)) {
                        // if fromMessengerContact is null it means the request has come from reviews
                        // tab and it must have reviewerId
                        lockKeyReviewer = Constants.REVIEWER_ID_PREFIX
                                + (Objects.nonNull(fromMessengerContact) ? fromMessengerContact.getReviewerId()
                                        : reviewAttributionRequest.getReviewerId());
                        lockAcquiredReviewerOpt = redisLockService.tryLock(lockKeyReviewer, 2, TimeUnit.SECONDS);
                        if (!lockAcquiredReviewerOpt.isPresent()) {
                            throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
                        }

                        if (Objects.isNull(fromMessengerContact)) {
                            Integer reviewId = reviewAttributionRequest.getReviewIds().get(0); // not null check already
                                                                                               // performed in
                                                                                               // findMessengerContact()
                                                                                               // method
                            ReviewEvent reviewEvent = reviewService.findById(reviewId);
                            fromMessengerContact = createNewConversation(reviewEvent, false); // false since we don't
                                                                                              // want to trigger a
                                                                                              // notification
                        }
                        List<MessengerContact> toMessengerContacts = messengerContactService
                                .findByCustomerIdAndBusinessId(customerDTO.getId(), businessDTO.getBusinessId());
                        if (CollectionUtils.isNotEmpty(toMessengerContacts)) {
                            if (fromMessengerContact.getId().equals(toMessengerContacts.get(0).getId())) {
                                log.info("linkReview: review already attributed for request {}",
                                        reviewAttributionRequest);
                                return fromMessengerContact.getId();
                            }
                            mergeConversations(fromMessengerContact, toMessengerContacts.get(0), accountId, userId,
                                    customerDTO, publishLinkEventToReviewService);
                            return toMessengerContacts.get(0).getId();
                        } else {
                            // update the existing conversation with the customer details.
                            return updateConversationWithCustomerDetails(customerDTO, fromMessengerContact, accountId,
                                    userId, publishLinkEventToReviewService);
                        }
                    } else {
                        log.info("Messenger Contact is not available for mcId : {} reviewId {}",
                                reviewAttributionRequest.getMcId(), reviewAttributionRequest.getReviewIds());
                        throw new BadRequestException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
                    }
                } else {
                    throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
                }
            } finally {
                if (lockAcquiredCustomerOpt.isPresent()) {
                    redisLockService.unlock(lockAcquiredCustomerOpt.get());
                }
                if (lockAcquiredReviewerOpt.isPresent()) {
                    redisLockService.unlock(lockAcquiredReviewerOpt.get());
                }
            }
        } else {
            log.info("unable to get or create customer !!");
            throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
        }
    }

    private SingletonMap findMessengerContact(ReviewAttributionRequest reviewAttributionRequest,
            BusinessDTO businessDTO) {
        // when called from inbox tab mcId will be present, when called from reviews tab
        // reviewId & reviewerId will be present
        if (null != reviewAttributionRequest.getMcId()) {
            return new SingletonMap("DO_NOT_CREATE",
                    messengerContactService.findById(reviewAttributionRequest.getMcId()));
        }
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(reviewAttributionRequest.getReviewIds()),
                "ReviewId must be present when called from reviews tab");
        Integer reviewId = reviewAttributionRequest.getReviewIds().get(0);
        MessengerMessage messengerMessage = messengerMessageService
                .findByReviewId(reviewId);
        if (Objects.nonNull(messengerMessage)) {
            int mcId = messengerMessage.getMessengerContactId();
            return new SingletonMap("DO_NOT_CREATE", messengerContactService.findById(mcId));
        }
        return new SingletonMap("CREATE", null);
    }

    private CustomerDTO createCustomer(ReviewAttributionRequest reviewAttributionRequest, BusinessDTO businessDTO,
            Integer userId) {
        KontactoRequest kontactoRequest = new KontactoRequest();
        kontactoRequest.setBusinessId(businessDTO.getBusinessId());
        kontactoRequest.setEmailId(reviewAttributionRequest.getCustomerEmail());
        kontactoRequest.setName(reviewAttributionRequest.getCustomerName());
        kontactoRequest.setPhone(reviewAttributionRequest.getCustomerPhone());
        kontactoRequest.setSource(KontactoRequest.DASHBOARD);
        kontactoRequest.setRoutingId(reviewAttributionRequest.getAccountId());
        KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
        locationInfo.setCountryCode(reviewAttributionRequest.getCountryCode());
        kontactoRequest.setLocation(locationInfo);
        return contactService.getorCreateNewCustomer(kontactoRequest, reviewAttributionRequest.getAccountId(), userId);

    }

    private Integer updateConversationWithCustomerDetails(CustomerDTO customerDTO, MessengerContact messengerContact,
            Integer accountId, Integer userId, boolean publishLinkEventToReviewService) {
        updateMessengerContact(messengerContact, customerDTO);
        Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(accountId,
                messengerContact.getId());
        if (contactDocumentOptional.isPresent()) {
            ContactDocument cd = contactDocumentOptional.get();
            if (updateContactDocument(cd, customerDTO, accountId)) {
                if (publishLinkEventToReviewService) {
                    publishReviewLinkEvent(contactDocumentOptional, customerDTO.getId(), userId, new Date().getTime());
                }
                return messengerContact.getId();
            }
        }
        return null;
    }

    private boolean updateContactDocument(ContactDocument cd, CustomerDTO customerDTO, Integer accountId) {
        ContactDocument.Builder builder = new ContactDocument.Builder(cd);
        builder.addCustomerInfo(customerDTO);
        cd = builder.build();
        cd.setViewedBy(new ArrayList<>());
        cd.setC_tag(MessengerTagEnum.UNREAD.getId());
        // this is required as conversation is now attributed.
        cd.setL_rvr_name("");
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        cd.setUpdatedAt(df.format(new Date()));
        return messengerContactService.upsertContactDocumentOnESWithRefresh(cd, cd.getM_c_id().toString(), accountId,false);
    }

    private void updateMessengerContact(MessengerContact mc, CustomerDTO customerDTO) {
        mc.setViewedBy(null);
        mc.setTag(MessengerTagEnum.UNREAD.getId());
        mc.setCustomerId(customerDTO.getId());
        mc.setIsRead(false);
        mc.setLeadSource(customerDTO.getLeadSource());
        mc.setContactState(customerDTO.getContactState());
        mc.setUpdatedAt(new Date());
        mc.setReviewerId(null);
        mc.setLead(customerDTO.isLead());
        messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(mc);
    }

    private void updateMessengerContact(MessengerContact messengerContact) {
        messengerContact.setUpdatedAt(new Date());
        messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(messengerContact);
    }

    private void updateMessengerMessages(MessengerContact from, MessengerContact to) {
        messengerMessageService.updateMessengerMessage(from.getId(), to.getId());
    }

    private Optional<ContactDocument> mergeContactDocument(Optional<ContactDocument> fromContactDocumentOptional,
            Optional<ContactDocument> toContactDocumentOptional, MessengerContact toMessengerContact) {
        if (fromContactDocumentOptional.isPresent() && toContactDocumentOptional.isPresent()) {
            ContactDocument fromContactDocument = fromContactDocumentOptional.get();
            ContactDocument toContactDocument = toContactDocumentOptional.get();
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            toContactDocument.setUpdatedAt(df.format(new Date()));
            if (CollectionUtils.isEmpty(toContactDocument.getReviews())) {
                toContactDocument.setReviews(fromContactDocument.getReviews());
                toContactDocument.setL_review_on(fromContactDocument.getL_review_on());
            } else {
                List<ContactDocument.Review> reviews = toContactDocument.getReviews();
                reviews.addAll(fromContactDocument.getReviews());
                toContactDocument.setReviews(reviews);
                if (fromContactDocument.getL_review_on() > toContactDocument.getL_review_on()) {
                    toContactDocument.setL_review_on(fromContactDocument.getL_review_on());
                }
            }
            if (toContactDocument.getC_tag().equals(MessengerTagEnum.CAMPAIGN.getId())) {
                toContactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
                toMessengerContact.setTag(MessengerTagEnum.UNREAD.getId());
                toContactDocument.setHide(false);
            }
            // this is required as unattributed review could have internal notes.
            // so there might be possibility that internal note in unattributed conversation
            // is latest.
            if ((Objects.nonNull(fromContactDocument.getL_msg_on()) && Objects.nonNull(toContactDocument.getL_msg_on())
                    && fromContactDocument.get_l_msg_on_epoch() > toContactDocument.get_l_msg_on_epoch())
                    ||
                    (Objects.nonNull(fromContactDocument.getL_msg_on())
                            && Objects.isNull(toContactDocument.getL_msg_on()))) {
                updateLastMessage(fromContactDocument, toContactDocument, toMessengerContact);
            }
            return Optional.of(toContactDocument);
        }
        return Optional.empty();
    }

    private void updateMessageDocument(Integer fromMcId, Integer toMcId, Integer accountId) {
        List<MessageDocument> messageDocuments = getMessageDocuments(fromMcId, accountId);
        if (CollectionUtils.isNotEmpty(messageDocuments)) {
            messageDocuments.stream().forEach(messageDocument -> messageDocument.setC_id(toMcId.toString()));
            BulkUpsertPayload<MessageDocument> bulkUpsertActivities = new BulkUpsertPayload<>(messageDocuments,
                    accountId, accountId, Constants.Elastic.MESSAGE_INDEX);
            try {
                elasticSearchService.performBulkRequestWithRefresh(bulkUpsertActivities,OpType.INDEX,RefreshPolicy.NONE);
            } catch (Exception e) {
                log.error("Exception while updating bulk messages", e);
                throw new MessengerException(ErrorCode.ERROR_WHILE_BULK_UPDATING_MESSAGES);
            }
        }

    }

    private void deleteUnattributedConversation(MessengerContact messengerContact, Integer accountId,
            Integer businessId) {
        Map<String, List<Integer>> result = null;
        Integer mcId = messengerContact.getId();
        boolean conversationDeleted = conversationService.deleteConversation(messengerContact.getId(), accountId,
                RefreshPolicy.IMMEDIATE);
        if (conversationDeleted) {
            // 2. Delete From MessengerContact from DB
            boolean deleted = messengerContactService.deleteMessengerContact(mcId);
            // 3. Remove data from MessageIndex
            messageService.deleteMessagesByMcIdWithRefresh(mcId, accountId, false);
            if (deleted) {
                messageService.deleteMessagesDataFromDBOnCustomerDelete(mcId, messengerContact.getCustomerId());
            }
            FirebaseDto firebaseDto = new FirebaseDto();
            firebaseDto.setAccountId(accountId);
            firebaseDto.setBusinessId(businessId);
            firebaseDto.setMcId(mcId);
            firebaseService.mirrorOnWeb(firebaseDto);
        }
    }

    private void updateLastMessage(ContactDocument fromContactDocument, ContactDocument toContactDocument,
            MessengerContact toMessengerContact) {
        toContactDocument.setL_msg_on(fromContactDocument.getL_msg_on());
        toContactDocument.setL_msg(fromContactDocument.getL_msg());
        toContactDocument.setLastMessageMetaData(fromContactDocument.getLastMessageMetaData());
        toContactDocument.setLastMessageType(fromContactDocument.getLastMessageType());
        toContactDocument.setLastMsgSource(fromContactDocument.getLastMsgSource());
        toContactDocument.setLastMessageUserId(fromContactDocument.getLastMessageUserId());
        toContactDocument.setLastMessageUserName(fromContactDocument.getLastMessageUserName());
        toMessengerContact.setLastMessage(fromContactDocument.getL_msg());
        toMessengerContact.setLastMsgOn(new Date(fromContactDocument.get_l_msg_on_epoch()));
        toMessengerContact.setLastMessageMetaData(JSONUtils.toJSON(fromContactDocument.getLastMessageMetaData()));
    }

    private List<MessageDocument> getMessageDocuments(Integer mcId, Integer accountId) {
        log.info("Get message doc from ES mcId:{} , accountId:{}", mcId, accountId);
        MessangerBaseFilter messageDocFilter = new MessangerBaseFilter();
        messageDocFilter.setConversationId(mcId);
        messageDocFilter.setAccountId(accountId);
        messageDocFilter.setCount(10000);
        messageDocFilter.setQueryFile(Constants.Elastic.GET_MESSAGES);
        return messengerContactService.getMessagesFromES(messageDocFilter);
    }

    private void publishReviewLinkEvent(Optional<ContactDocument> contactDocumentOptional, Integer customerId,
            Integer userId, Long at) {
        if (contactDocumentOptional.isPresent()
                && CollectionUtils.isNotEmpty(contactDocumentOptional.get().getReviews())) {
            if (CollectionUtils.isNotEmpty(contactDocumentOptional.get().getReviews())) {
                List<Long> reviewIds = contactDocumentOptional.get().getReviews().stream()
                        .map(r -> r.getId().longValue()).collect(Collectors.toList());
                ReviewAttributionEvent event = new ReviewAttributionEvent(reviewIds, customerId, userId, Constants.LINK,
                        at);
                try {
                    log.info("publishReviewLinkEvent: calling review-service {}", event);
                    reviewService.attributeReviews(event);
                } catch (Exception e) {
                    log.error("publishReviewLinkEvent: error in review-service api call", e);
                    kafkaService.publishToKafkaAsync(KafkaTopicEnum.REVIEW_LINK_UNLINK_EVENT, event);
                }
            } else {
                log.info("publishReviewLinkEvent: contactDocument doesn't have reviews {}",
                        contactDocumentOptional.get());
            }
        } else {
            log.info("publishReviewLinkEvent: contactDocument is null or do not have reviews {}",
                    contactDocumentOptional.isPresent() ? contactDocumentOptional.get() : null);
        }
    }

    private void mergeConversations(MessengerContact fromMessengerContact, MessengerContact toMessengerContact,
            Integer accountId, Integer userId, CustomerDTO customerDTO, boolean publishLinkEventToReviewService) {
        updateMessengerMessages(fromMessengerContact, toMessengerContact);
        // Merge with the existing conversation available with customer provided
        // We will be transferring notes and reviews to the attributed conversation.
        Optional<ContactDocument> fromContactDocument = messengerContactService.getContactDocument(accountId,
                fromMessengerContact.getId());
        Optional<ContactDocument> toContactDocument = messengerContactService.getContactDocument(accountId,
                toMessengerContact.getId());
        Optional<ContactDocument> contactDocumentOptional = mergeContactDocument(fromContactDocument, toContactDocument,
                toMessengerContact);
        log.info("**** fromContactDocument - {}, toContactDocument-{}", fromContactDocument, toContactDocument);
        updateMessengerContact(toMessengerContact);
        if (contactDocumentOptional.isPresent()) {
            ContactDocument contactDocument = contactDocumentOptional.get();
            messengerContactService.upsertContactDocumentOnESWithRefresh(contactDocument,
                    contactDocument.getM_c_id().toString(), contactDocument.getE_id(), false);
        }
        // update Message document with the new McId
        updateMessageDocument(fromMessengerContact.getId(), toMessengerContact.getId(), accountId);
        // After successful merging of the unattributed conversation to the attributed
        // one.
        // Delete the unattributed conversation.
        deleteUnattributedConversation(fromMessengerContact, accountId, fromMessengerContact.getBusinessId());
        if (publishLinkEventToReviewService) {
            publishReviewLinkEvent(fromContactDocument, customerDTO.getId(), userId, new Date().getTime());
        }

    }

    private boolean checkIfReviewExists(Integer reviewId, Integer accountId) {
        MessengerMessage messengerMessage = messengerMessageService.findByReviewId(reviewId);
        if (Objects.nonNull(messengerMessage)) {
            return true;
        }
        return false;
    }

    private Integer evaluateSentimentScore(ReviewEvent reviewEvent) {
        Float reviewRating = reviewEvent.getReviewDetail().getRating();
        Boolean recommended = reviewEvent.getReviewDetail().getRecommended();

        Integer score = null;
        // reviewRating > 0 check as discussed with Nitin, experience score should be
        // unknown in case reviewRating == 0
        if (reviewRating != null && reviewRating > 0) {
            score = Math.round(reviewRating * 2);
        } else if (recommended != null) {
            // facebook reviews don't have rating
            // recommended flag determines the sentiment
            score = recommended ? 10 : 1;
        }
        return score;
    }

    private Integer evaluateSentimentScore(Review review) {
        Float reviewRating = review.getRtng();
        Boolean recommended = review.getRcmded();

        Integer score = null;
        if (reviewRating != null && reviewRating > 0) {
            score = Math.round(reviewRating * 2);
        } else if (recommended != null) {
            // facebook reviews don't have rating
            // recommended flag determines the sentiment
            score = recommended ? 10 : 1;
        }
        return score;
    }

    private void updateSentimentDetails(ReviewEvent reviewEvent,ContactDocument conversationDoc) {
        if (reviewEvent.getReviewDetail().getCustomerId() == null) {
            Integer score = evaluateSentimentScore(reviewEvent);
            conversationDoc.setSentimentScore(score);
            conversationDoc.setSentiment(CustomerSentimentEnum.fromScore(score).value());
        }
    }

    private String getAccountType(BusinessDTO businessDTO) {
        if (Objects.nonNull(businessDTO)) {
            return businessDTO.getAccountType();
        }
        return null;
    }

    private String getBusinessType(BusinessDTO businessDTO) {
        if (Objects.nonNull(businessDTO)) {
            return businessDTO.getType();
        }
        return null;
    }

    @Override
    @Transactional
    public UnlinkReviewResponse unlinkReview(Integer userId, Integer accountId, Integer reviewId, Integer customerId) {
        MessengerContact mc = messengerContactService.findByCustomerId(customerId);
        if (mc != null) {
            return unlinkReview(userId, accountId, reviewId, mc.getId(), false, false);
        }
        log.info("unlinkReview: messengerContact not found for customerId {}", customerId);
        return null;
    }

}
