package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.repository.MessengerMediaFileRepository;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.util.MediaUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MessengerMediaFileServiceImpl implements MessengerMediaFileService {

	@Autowired
	private MessengerMediaFileRepository repository;

	@Override
	public MessengerMediaFileDTO saveMedia(SmsDTO sms) {
		if (StringUtils.isEmpty(sms.getMediaURL())) {
			log.warn("No media to save for sms {}", sms.getSmsId());
			return null;
		}
		String fileName = extractFileName(sms);
		MessengerMediaFile messengerMediaFile = new MessengerMediaFile();
		messengerMediaFile.setMessageId(sms.getSmsId());
		messengerMediaFile.setUrl(sms.getMediaURL());
		messengerMediaFile.setName(fileName);
		Map<String, Object> contentHeaders = MediaUtils.getContentHeaders(sms.getMediaURL());
		messengerMediaFile.setContentSize(String.valueOf(contentHeaders.get(MediaUtils.CONTENT_LEN)));
		messengerMediaFile.setContentType(String.valueOf(contentHeaders.get(MediaUtils.CONTENT_TYPE)));
		repository.save(messengerMediaFile);
		return new MessengerMediaFileDTO(messengerMediaFile);
	}

	private String extractFileName(SmsDTO sms) {
		String fileName = sms.getMediaURL().substring(sms.getMediaURL().lastIndexOf('_') + 1);
		return fileName;
	}

	@Override
	public MessengerMediaFile saveMedia(MessengerMediaFile messengerMediaFile) {
		return repository.save(messengerMediaFile);
	}
	@Override
	public void saveMediaFiles(List<MessengerMediaFileDTO> mediaFileDTOs,Integer id) {
		if(CollectionUtils.isNotEmpty(mediaFileDTOs)) {
			List<MessengerMediaFile> mediaFiles=new ArrayList<MessengerMediaFile>();
			mediaFileDTOs.forEach(mediaFileDTO->{
				MessengerMediaFile mediaFile = new MessengerMediaFile(mediaFileDTO);
				mediaFile.setMessageId(id);
				mediaFiles.add(mediaFile);
			});
			repository.saveAll(mediaFiles);
		}
	}
	@Override
	public MessengerMediaFile saveMediaFile(MessengerMediaFileDTO mediaFileDTO,Integer id) {
		if(mediaFileDTO!=null) {
			MessengerMediaFile mediaFile = new MessengerMediaFile(mediaFileDTO);
			mediaFile.setMessageId(id);
			return repository.saveAndFlush(mediaFile);
		}
		return null;
	}

	@Override
	public List<MessengerMediaFile> findByMsgId(Integer msgId) {
		return repository.findByMessageId(msgId);
	}

	@Override
	public Optional<MessengerMediaFile> findById(Integer id) {
		return repository.findById(id);
	}

	@Override
	public List<MessengerMediaFile> findByIds(List<Integer> mediaIds) {
		return repository.findByIds(mediaIds);
	}

	@Override
	public void saveAll(Collection<MessengerMediaFile> values) {
		repository.saveAll(values);
	}
}
