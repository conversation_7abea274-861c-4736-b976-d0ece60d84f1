package com.birdeye.messenger.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.TeamAssigneeDto;
import com.birdeye.messenger.dto.UserDTO;

public interface CommunicationHelperService {

    BusinessDTO getBusinessDTO(Integer businessId);
    CustomerDTO getCustomerDTO(Integer customerId);
    UserDTO getUserDTO(Integer userId);
    Map<Integer, List<Integer>> getValidUserDTOs(Integer userId, Integer accountId, Set<Integer> businessIds);
	Map<Integer, TeamAssigneeDto> getValidUserDTOs(Integer userId, Integer accountId, Integer businessId);

}
