package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.beanutils.BeanUtilsBean;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.beanutils.converters.IntegerConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatEnabledTeam;
import com.birdeye.messenger.dao.entity.BusinessChatLocationHierarchy;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfigAudit;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetUserProfile;
import com.birdeye.messenger.dao.entity.CustomFieldsWidget;
import com.birdeye.messenger.dao.entity.LiveChatMessage;
import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.entity.LocationWidgetDetails;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;
import com.birdeye.messenger.dao.entity.RobinActiveHours;
import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dao.entity.WebchatCustomLocationName;
import com.birdeye.messenger.dao.entity.WebchatInstallationAudit;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledLocationRepository;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledTeamRepository;
import com.birdeye.messenger.dao.repository.BusinessChatLocationHierarchyRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetConfigAuditRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetConfigRepository;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetUserProfileRepository;
import com.birdeye.messenger.dao.repository.CustomFieldWidgetRepository;
import com.birdeye.messenger.dao.repository.LiveChatMessageConfigRepository;
import com.birdeye.messenger.dao.repository.LocationWidgetDetailsRepository;
import com.birdeye.messenger.dao.repository.WebchatCustomLocationNameRepository;
import com.birdeye.messenger.dao.repository.WebchatInstallationAuditRepository;
import com.birdeye.messenger.dto.BizAppChatWidgetConfigRequest;
import com.birdeye.messenger.dto.BusinessAPIKeyMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLocationCustomFieldsTokensDto;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.BusinessWebChatWidgetConfiguration;
import com.birdeye.messenger.dto.BusinessWebchatEventDetails;
import com.birdeye.messenger.dto.BusinessWebchatWidgetDetails;
import com.birdeye.messenger.dto.BusinessWebchatWidgetList;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomFieldDto;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.dto.LocationLevelRequest;
import com.birdeye.messenger.dto.MessengerData;
import com.birdeye.messenger.dto.PublicDataBusinessDTO;
import com.birdeye.messenger.dto.RobinActiveHoursDto;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.TeamDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UserLoginMessage;
import com.birdeye.messenger.dto.WebChatConfigForExternalIdResponse;
import com.birdeye.messenger.dto.WebChatCustomFieldDataRequest;
import com.birdeye.messenger.dto.WebchatMessageDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BusinessAccountTypeEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageStatusEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WebchatInstallationAuditEnum;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.AssignmentService;
import com.birdeye.messenger.service.BusinessChatWidgetConfigService;
import com.birdeye.messenger.service.BusinessWebchatEventService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.CustomFieldService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.LiveChatMessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.NotificationService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.ResponseTimeCalculationService;
import com.birdeye.messenger.service.RobinActiveHoursService;
import com.birdeye.messenger.service.RoundRobinAssignmentService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.SmsSource;
import com.birdeye.messenger.service.TeamConfigForRoundRobinRepositoryService;
import com.birdeye.messenger.service.WebchatService;
import com.birdeye.messenger.sro.BusinessDeactivationRequest;
import com.birdeye.messenger.sro.BusinessLocationInfo;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.sro.BusinessWebChatConfigurationRequest;
import com.birdeye.messenger.sro.BusinessWebChatWidgetUserProfile;
import com.birdeye.messenger.sro.CreateDefaultConfigRequest;
import com.birdeye.messenger.sro.CustomFieldWidgetDto;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.GetLocationsResponse;
import com.birdeye.messenger.sro.MultiLocationWidgetRequest;
import com.birdeye.messenger.sro.OperationHours;
import com.birdeye.messenger.sro.RefreshInstallationStatusResponse;
import com.birdeye.messenger.sro.SaveWebchatResponse;
import com.birdeye.messenger.sro.Team;
import com.birdeye.messenger.sro.TeamDTO;
import com.birdeye.messenger.sro.UpdateEmailMandatoryRequest;
import com.birdeye.messenger.sro.WebChatConfigurationGenericRequest;
import com.birdeye.messenger.sro.WebChatWidgetDefaultConfiguration;
import com.birdeye.messenger.sro.WebchatConfigFilter;
import com.birdeye.messenger.sro.WebchatCustomLocationNameRequest;
import com.birdeye.messenger.sro.WebchatCustomLocationNameRequest.WidgetCustomLocationName;
import com.birdeye.messenger.sro.WebchatInstallationCrawlerRequest;
import com.birdeye.messenger.sro.WebchatWidgetTeamsOrderingRequest;
import com.birdeye.messenger.sro.WebchatWidgetTeamsOrderingRequest.WidgetTeamsOrdering;
import com.birdeye.messenger.sro.WidgetCustomFieldRequest;
import com.birdeye.messenger.util.BusinessHourComparator;
import com.birdeye.messenger.util.BusinessHoursUtility;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.RequestDtoValidator;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class WebchatServiceImpl implements WebchatService {

	@Autowired
	private ContactService contactService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private NLPService nlpService;

	@Autowired
	private SmsService smsService;

	@Autowired
	@Lazy
	private NotificationService notificationService;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	BusinessChatWidgetConfigService businessChatWidgetConfigService;

	@Autowired
	BusinessChatWidgetConfigAuditRepository businessChatWidgetConfigAuditRepository;

	@Autowired
	private LocationWidgetDetailsRepository locationWidgetDetailsRepository;

	@Autowired
	private BusinessChatWidgetConfigRepository businessChatWidgetConfigRepository;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private RedisHandler redisHandler;

	@Autowired
	@Lazy
	private SMSSendEventHandler smsSendEventHandler;

	@Autowired
	private ThreadPoolTaskExecutor poolExecutor;

	@Autowired
	private FirebaseService fcmService;

	@Autowired
	private AssignmentService assignmentService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Autowired
	private ResponseTimeCalculationService responseTimeCalculationService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private LiveChatMessageConfigRepository liveChatMessageConfigRepository;

	@Autowired
	private BusinessWebchatEventService businessWebchatEventService;

	@Autowired
	private WebchatInstallationAuditRepository webchatInstallationAuditRepository;

	@Autowired
	private BusinessChatEnabledLocationRepository businessChatEnabledLocationRepository;

	@Autowired
	private BusinessChatLocationHierarchyRepository businessChatLocationHierarchyRepository;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	@Lazy
	private WebchatService self;

	@Autowired
	private BusinessChatEnabledTeamRepository businessChatEnabledTeamRepository;

	@Autowired
	private LiveChatMessageService liveChatMessageService;

	@Autowired
	@Lazy
	private MessengerService messengerService;

	@Autowired
	private BusinessChatWidgetUserProfileRepository businessChatWidgetUserProfileRepository;

	@Autowired
	private WebchatCustomLocationNameRepository webchatCustomLocationNameRepository;

	@Autowired
	private CustomFieldWidgetRepository customFieldWidgetRepository;

	@Autowired
	private CustomFieldService customFieldService;

	@Autowired
	private RoundRobinAssignmentService roundRobinAssignmentService;
	
	@Autowired
    private RedisLockService redisLockService;
	
	@Autowired
	private TeamConfigForRoundRobinRepositoryService teamConfigForRoundRobinRepositoryService;
	
	@Autowired
	private CommonService commonService;

	@Autowired
	private RobinActiveHoursService robinActiveHoursService;

	private KontactoRequest create(WebchatMessageDTO webchatRequest) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String custName = webchatRequest.getName();
		kontactoRequest.setName(custName);
		kontactoRequest.setEmailId(webchatRequest.getEmail());
		kontactoRequest.setPhone(webchatRequest.getMobileNumber());
		kontactoRequest.setSource(KontactoRequest.WEBCHAT);
		// For Web-chat sms enable by default
		if (StringUtils.isNotBlank(webchatRequest.getMobileNumber())) {
			kontactoRequest.setSmsEnabled(1);
		}
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(webchatRequest.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		Map<String, String> customField = new HashMap<>();
		if (CollectionUtils.isNotEmpty(webchatRequest.getCustomFieldsData())) {
			for (WebChatCustomFieldDataRequest webChatCustomFieldDataRequest : webchatRequest.getCustomFieldsData()) {
				customField.put(webChatCustomFieldDataRequest.getName(), webChatCustomFieldDataRequest.getValue());
			}
			kontactoRequest.setAdditionalParams(customField);
		}
		Map<String, String> urlParams = new HashMap<>();
		urlParams.put("originPageUrl", webchatRequest.getPageUrlTrackedByGoogleAnalytics());
		urlParams.put("messageBody", webchatRequest.getMessage());
		kontactoRequest.setUrlParams(urlParams);
		return kontactoRequest;
	}

	/**
	 * 1. Validate
	 * 2. Get Location/account details
	 * 3. Account should not be free
	 * 4. Check message for profanity
	 * 5. Assign the conversation
	 * 6. Get or create customer via Kontacto
	 * 7. Save SMS
	 * 8. Update Messenger Contact
	 * 9. Email Notification
	 * 10. SYNC/PushNotification
	 * 11. Trigger auto-reply
	 *
	 */
	public Boolean sendWebchatSMS(WebchatMessageDTO request) {

		log.debug("WebchatSendSMSRequest for businessId :: {} and apiKey :: {} clientIp :: {} ",
				request.getBusinessNumber(), request.getApiKey(), request.getClientIp());

		// PART 1 : SAVE CUSTOMER TO BUSINESS MESSAGE
		// FIXME - These data validations should happen at the controller layer. Use
		// Validators for them
		RequestDtoValidator.validateWebchatSendSMSRequest(request);

		// fetch business for given location in request
		BusinessDTO businessDto = getBusinessDto(request.getBusinessNumber());
		try {
			commonService.validateWidgetApiKey(businessDto.getBusinessNumber(), request.getApiKey());
		} catch (Exception e) {
			log.info("Error validating widget api key : {}",e.getMessage());
			return false;
		}
		
		boolean isFreeAccount = BusinessDTO.isFreeAccount(businessDto);
		boolean sendAutoreply =false;
		Boolean isReceivedDuringBusinessHours=null;
		if (isFreeAccount) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.FREE_ACCOUNT_NOT_SUPPORTED,
					ComponentCodeEnum.WEBCHAT, HttpStatus.BAD_REQUEST));

		}

		// Integration with NLP Service
		if (nlpService.isTextProfane(request.getMessage())) {
			log.warn("Message detected as profane for businessId :: {} and apiKey :: {} and message :: {} ",
					request.getBusinessNumber(), request.getApiKey(), request.getMessage());
			return null;
		}

		TeamDto teamDto = null;
		if (request.getTeamId() != null) {
			teamDto = validateTeamId(request.getTeamId());
		}
		CustomerDTO customer = null;
		List<Integer> accountIds = contactService.getAllContactUpgradeEnabledAccounts();
		if (CollectionUtils.isNotEmpty(accountIds) && businessDto != null
				&& accountIds.contains(businessDto.getAccountId())) {
			customer = commonService.getActiveCustomerBySource(request.getMobileNumber(), request.getEmail(),
					request.getName(), businessDto.getBusinessId(), businessDto.getAccountId(), Source.WEB_CHAT.name());
		}
		// Auto user for webchat is -1, can be part of kontacto itself.
		if (Objects.isNull(customer)) {
			KontactoRequest kontactoRequest = create(request);
			kontactoRequest.setBusinessId(businessDto.getBusinessId());
			customer = contactService.getorCreateNewCustomer(kontactoRequest, businessDto.getRoutingId(), -1);
		}
		if (customer != null) {
			Optional<Lock> lockOpt = Optional.empty();
			try {
				String lockKey = Constants.CUSTOMER_ID_PREFIX+customer.getId();
				lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
				if (!lockOpt.isPresent()) {
					//Reject such events
					log.info("[Webchat send] Unable to acquire lock key:{}",lockKey);
					throw new MessengerException(ErrorCode.WEBCHAT_SEND_LOCK);
				}
				// Prepare SMS Message for business.
				LiveChatMessageObject customerToBusinessMessage = getLiveChatMessageFromWebchatSendRequest(request, customer, businessDto);
				log.debug( "Customer to Business SMS Message for businessId :: {} and apiKey :: {} is :::: {} ",
						new Object[] { request.getBusinessNumber(), request.getApiKey(), customerToBusinessMessage });
				//	Sms savedSMS  = smsService.saveSmsFromCustomer(customerToBusinessMessage);
				LiveChatMessage liveChatMessage = liveChatMessageService.saveLiveChatMessageFromCustomer(customerToBusinessMessage,MessageStatusEnum.RECEIVED);

				// Encryption data.
				ConversationDTO conversationDTO=new ConversationDTO();
				addWebchatMessageMetaData(conversationDTO);

				LiveChatMessageObject liveChatMessageObject=new LiveChatMessageObject(liveChatMessage, conversationDTO);
				//SmsDTO sms = new SmsDTO(savedSMS,conversationDTO);
				liveChatMessageObject.setMessageBodyUnencrypted(request.getMessage());
				liveChatMessageObject.setPageUrlTrackedByGoogleAnalytics(request.getPageUrlTrackedByGoogleAnalytics());
				if(Objects.nonNull(request.getClientIp())) {
					liveChatMessageObject.setClientIp(request.getClientIp());
				}
				if(StringUtils.isNotBlank(request.getDevice())){
					liveChatMessageObject.setDevice(request.getDevice());
				}

				// ****** UPDATE MESSENGER CONTACT with Contact & SMS information  ******//
				MessengerData data = messengerContactService.processMessengerContactForWebChat(businessDto, customer, liveChatMessageObject,request);
				MessengerContact messengerContact = data.getMessengerContact();
				if(Objects.isNull(messengerContact.getRtmPauseTagging()) || Boolean.FALSE.equals(messengerContact.getRtmPauseTagging())) {
					ResponseTimeOutBox responseTimeOutBox = responseTimeCalculationService.processResTimeMessage(messengerContact, liveChatMessage.getId() + MessengerUtil.getMessageTypeSuffix(MessageType.CHAT, liveChatMessage.getSource()), liveChatMessage.getCreateDate().getTime(), liveChatMessage.getSource(), "R");
					MessengerData messengerData = messengerContactService.saveOrUpdateMessengerContact(messengerContact, responseTimeOutBox);
					if(Objects.nonNull(responseTimeOutBox)) kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESPONSE_TIME_CALC, messengerData.getResponseTimeOutBox());
				}
				else {
					messengerContactService.saveOrUpdateMessengerContact(messengerContact);
				}
				messengerMessageService.saveMessengerMessage(liveChatMessageObject, null);
				MessageDocument messageDocument = data.getMessageDocument();
				ContactDocument contactDocument = data.getContactDocument();

				TeamConfigForRoundRobin config = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobinByAccountId(businessDto.getAccountId());

				if (teamDto != null && (Objects.isNull(config) || !(config.getTeamId().equals(teamDto.getTeamId())))){
					//assigning conversation to the team .
					log.info("Assigning to team id:{}",teamDto.getTeamId());
					assignConversationToTeam(teamDto,Integer.valueOf(data.getMessageDocument().getC_id()),businessDto);
					ContactDocument contactDocument1 = messengerContactService.getContact(contactDocument.getE_id(),contactDocument.getM_c_id());
					if(contactDocument1 != null){
						log.info("Updated contact doc with team assignment team id:{},{}",contactDocument1.getTeam_id(),contactDocument1.getM_c_id());
						contactDocument = contactDocument1;
					}
				}

				// logging webchat activity before any webchat message received
				logWebchatActivity(businessDto, customer, request.getSource(), liveChatMessageObject.getMessengerContactId(), liveChatMessageObject.getCreateDate(), teamDto,config);

				if(!customer.getBlocked()) {
					// ****** Mail notification wherever applicable.****** //
					notificationService.processUnreadMessageNotification(businessDto, messageDocument);
					// ******  Push event to firebase ****** //
					fcmService.pushToFireBase(contactDocument, messageDocument, null, null);
				}
				BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessDto.getBusinessId(),false);
				if (businessTimingDTO != null){
					isReceivedDuringBusinessHours = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
				}
				boolean hasAgentRespondedOnOutsideBH=messengerService.hasAgentRespondedOnOutsideBH(businessDto.getRoutingId(), messengerContact.getId(), isReceivedDuringBusinessHours);
				// Auto reply to webchat message using webchat configuration.
				//			platformService.triggerAutoReplyForWebchat(businessDto.getBusinessId(), customer.getId(), request.getWidgetConfigId());
				if(!BooleanUtils.isTrue(request.getEmailMandatory()) && !hasAgentRespondedOnOutsideBH && Boolean.FALSE.equals(contactDocument.getBlocked())) {
					sendAutoreply=true;
				}
				// handle PulseSurveyContext
				PulseSurveyContext context = null;
				try {
					context = pulseSurveyService.handlePulseSurveyContext(null, customer, businessDto);
				} catch (Exception ex) {
					log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
				}
				ContactDocument exContactDocument = new ContactDocument();
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
					exContactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					exContactDocument.setOngoingPulseSurvey(false);
				}
				if(customer.getBlocked()) {
					exContactDocument.setSpam(true);
					exContactDocument.setBlocked(true);
				}
				messengerContactService.updateContactOnES(messengerContact.getId(), exContactDocument, businessDto.getAccountId());
			} finally {
				if (lockOpt.isPresent()) {
					redisLockService.unlock(lockOpt.get());
				}
			}
			if(sendAutoreply) {
				self.sendAutoReplyToCustomerForWebchatSMS(request.getWidgetConfigId(),businessDto,customer,false);
			}
			return BooleanUtils.isTrue(isReceivedDuringBusinessHours);
		}
		return false;
	}

	private void logWebchatActivity(BusinessDTO business, CustomerDTO customer, Integer source, Integer mcId,
			Date createdDate, TeamDto team,TeamConfigForRoundRobin config) {
		log.debug("[logWebchatActivity] - Received log webchat activity event for source {} and mcId {}", source, mcId);
		LiveChatSessionToken liveChatSessionToken = new LiveChatSessionToken();
		liveChatSessionToken.setBusinessId(business.getBusinessId());
		liveChatSessionToken.setCustomerId(customer.getId());
		liveChatSessionToken.setMcid(mcId);
		liveChatSessionToken.setAccountId(business.getAccountId());
		liveChatSessionToken.setChannel(source != null ? Source.getValue(source).name() : Source.WEB_CHAT.name());
		ActivityType activityType = team != null && team.getTeamId() != null && StringUtils.isNotBlank(team.getName()) &&
				(config == null || !team.getTeamId().equals(config.getTeamId()))
				? ActivityType.WEBCHAT_START_WITH_TEAM
				: ActivityType.WEBCHAT_START;
		conversationActivityService.persistActivityInStoreForWebChat(liveChatSessionToken, source, activityType,
				createdDate != null ? new Date(createdDate.getTime() - 1000) : new Date(), team);
	}

	private void addWebchatMessageMetaData(ConversationDTO conversationDTO) {
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setChannel(Channel.WEB_CHAT);
		conversationDTO.setCommunicationDirection(CommunicationDirection.RECEIVE);
	}

	private SmsDTO getSmsMessageFromWebchatSendSMSRequest(WebchatMessageDTO request, CustomerDTO customer,
			BusinessDTO businessDto) {
		log.info("getSmsMessageFromWebchatSendSMSRequest:BIRDEYE-63455 debug {} {} {} ", request, customer,
				businessDto);
		SmsDTO smsDto = new SmsDTO();
		smsDto.setBusinessId(businessDto.getBusinessId());
		smsDto.setCustomerId(customer.getId());
		smsDto.setToNumber(customer.getPhoneE164());
		smsDto.setMessageBodyUnencrypted(request.getMessage());
		smsDto.setSource(getWebchatSource(request.getSource()));
		return smsDto;
	}

	private LiveChatMessageObject getLiveChatMessageFromWebchatSendRequest(WebchatMessageDTO request,
			CustomerDTO customer,
			BusinessDTO businessDto) {
		log.info("getLiveChatMessageFromWebchatSendRequest  debug {} {} {} ", request, customer, businessDto);
		LiveChatMessageObject liveChatMessageDto = new LiveChatMessageObject();
		liveChatMessageDto.setBusinessId(businessDto.getBusinessId());
		liveChatMessageDto.setCustomerId(customer.getId());
		liveChatMessageDto.setEmail(request.getEmail());
		liveChatMessageDto.setEmailMandatory(request.getEmailMandatory());
		liveChatMessageDto.setMessageBodyUnencrypted(request.getMessage());
		liveChatMessageDto.setSource(getWebchatSource(request.getSource()));
		return liveChatMessageDto;
	}

	/**
	 * Webchat message source should be set as “2” in SMS table when message is
	 * received from business site webchat and should be set as “6” when message
	 * is received from business profile webchat.
	 *
	 * Default value is Business Site Webchat.(2)
	 */
	private static int getWebchatSource(Integer source) {
		if (source != null) {
			return source;
		}
		return SmsSource.BIZSITE_WEBCHAT.getValue();
	}

	@Override
	public List<GetLocationsResponse> getLocationsByEnterpriseId(Integer enterpriseId) {
		log.info("Get widgets configs by enterprise Id:{}", enterpriseId);

		List<GetLocationsResponse> getLocationsResponses = new ArrayList<>();
		if (enterpriseId != null) {
			Map<Integer, BusinessDTO> businessDTOSMap = businessService.getBusinessSMSEnabledLocations(enterpriseId);
			// List<BusinessChatWidgetConfig> businessChatWidgetConfigs =
			// businessChatWidgetConfigService.findwidgetConfigsByEnterpriseId(enterpriseId);
			// Map<Long,BusinessChatWidgetConfig> businessChatWidgetConfigMap = new
			// HashMap<>();
			// if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)){
			// businessChatWidgetConfigMap =
			// businessChatWidgetConfigs.stream().collect(Collectors.toMap(BusinessChatWidgetConfig::
			// getBusinessId,Function.identity()));
			// }
			List<BusinessDTO> businessDTOS = businessDTOSMap.values().stream().collect(Collectors.toList());
			for (BusinessDTO businessDTO : businessDTOS) {
				GetLocationsResponse getLocationsResponse = populateGetLocationResponse(businessDTO);
				getLocationsResponses.add(getLocationsResponse);
			}
		}
		return getLocationsResponses;
	}

	private GetLocationsResponse populateGetLocationResponse(BusinessDTO businessDTO) {
		GetLocationsResponse getLocationsResponse = new GetLocationsResponse();
		getLocationsResponse.setBusinessId(businessDTO.getBusinessId());
		getLocationsResponse.setBusinessNumber(businessDTO.getBusinessNumber());
		getLocationsResponse.setBusinessName(businessDTO.getBusinessName());
		getLocationsResponse.setBusinessAlias(businessDTO.getBusinessAlias());
		// if (businessChatWidgetConfig != null){
		// getLocationsResponse.setWidgetConfigId(businessChatWidgetConfig.getId());
		// getLocationsResponse.setWidgetEnabled(businessChatWidgetConfig.getEnabled());
		// }
		return getLocationsResponse;
	}

	@Override
	public WebChatWidgetDefaultConfiguration getDefaultConfig(Integer locationId) {
		WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration = new WebChatWidgetDefaultConfiguration();
		if (locationId == null) {
			log.info("Location Id is empty!!");
			return new WebChatWidgetDefaultConfiguration();
		}
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(locationId);
		if (businessDTO != null) {

			BusinessChatWidgetConfig businessChatWidgetConfig = getDefaultBusinessChatWidgetConfig();

			webChatWidgetDefaultConfiguration = populateWebChatWidgetDefaultConfiguration(businessDTO,
					businessChatWidgetConfig, true);

			if (businessDTO.getEnterpriseId() == null) {
				List<BusinessLocationInfo> businessLocationInfos = businessChatWidgetConfigService
						.getBusinessLocationsData(null, businessDTO);
				if (CollectionUtils.isNotEmpty(businessLocationInfos)) {
					log.info("businessLocationInfos for business_id : {}, size : {}", businessDTO.getBusinessId(),
							businessLocationInfos.size());
				}
				webChatWidgetDefaultConfiguration.setBusinessLocations(businessLocationInfos);
				webChatWidgetDefaultConfiguration.setBusinessSMSPhoneNumber(businessLocationInfos.stream()
						.filter(bl -> StringUtils.isNotEmpty(bl.getBusinessSMSPhoneNumber()))
						.collect(Collectors.toList()).get(0).getBusinessSMSPhoneNumber());
				webChatWidgetDefaultConfiguration.setTeams(populateTeams(businessDTO.getBusinessId(), null, null));
			} else {
				webChatWidgetDefaultConfiguration
						.setTeams(populateTeams(businessDTO.getEnterpriseId(), null, businessDTO.getBusinessId()));
				webChatWidgetDefaultConfiguration.setBusinessSMSPhoneNumber(fetchSmsPhoneNumber(businessDTO));
			}
			populateLiveChatDefaultMessageConfig(webChatWidgetDefaultConfiguration, Constants.DEFAULT_CONFIG_ID,
					businessDTO);
		}
		return webChatWidgetDefaultConfiguration;
	}

	private void populateLiveChatDefaultMessageConfig(
			WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration, Integer widgetId,
			BusinessDTO businessDTO) {
		LiveChatWidgetConfig liveChatMessageDefaultConfig = liveChatMessageConfigRepository.findByWidgetId(widgetId);
		webChatWidgetDefaultConfiguration
				.setLiveChatOfflineClosingMessageBody(liveChatMessageDefaultConfig.getOfflineClosingMessageBody());
		webChatWidgetDefaultConfiguration
				.setLiveChatOfflineClosingMessageHeader(liveChatMessageDefaultConfig.getOfflineClosingMessageHeader());
		webChatWidgetDefaultConfiguration
				.setLiveChatOfflineWelcomeMessage(liveChatMessageDefaultConfig.getOfflineWelcomeMessage());
		webChatWidgetDefaultConfiguration
				.setLiveChatOnlineWelcomeMessage(liveChatMessageDefaultConfig.getOnlineWelcomeMessage());
		webChatWidgetDefaultConfiguration
				.setLiveChatOnlineClosingMessageHeader(liveChatMessageDefaultConfig.getOnlineClosingMessageHeader());
		webChatWidgetDefaultConfiguration
				.setLiveChatOnlineClosingMessageBody(liveChatMessageDefaultConfig.getOnlineClosingMessageBody());

		// https://birdeye.atlassian.net/browse/BIRDEYE-76016
		webChatWidgetDefaultConfiguration.setLiveChatOnlineTextMessage(replaceTextWithBusinessNamePrePopulated(
				liveChatMessageDefaultConfig.getOnlineTextMessage(), businessDTO));
		webChatWidgetDefaultConfiguration.setLiveChatOfflineTextMessage(replaceTextWithBusinessNamePrePopulated(
				liveChatMessageDefaultConfig.getOfflineTextMessage(), businessDTO));
	}

	private BusinessChatWidgetConfig getDefaultBusinessChatWidgetConfig() {
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
				.getDefaultBusinessChatWidgetConfig();
		return businessChatWidgetConfig;
	}

	private WebChatWidgetDefaultConfiguration populateWebChatWidgetDefaultConfiguration(BusinessDTO businessDTO,
			BusinessChatWidgetConfig businessChatWidgetConfig, Boolean defaultConfig) {
		WebChatWidgetDefaultConfiguration webChatConfiguration = new WebChatWidgetDefaultConfiguration();
		webChatConfiguration.setBusinessId(businessDTO.getBusinessNumber());
		webChatConfiguration.setBannerColor(businessChatWidgetConfig.getBannerColor());
		webChatConfiguration.setBannerTextColor(businessChatWidgetConfig.getBannerTextColor());
		webChatConfiguration.setChatIcon(businessChatWidgetConfig.getChatIcon());
		webChatConfiguration.setChatIconValue(businessChatWidgetConfig.getChatIconValue());
		webChatConfiguration.setChatTheme(businessChatWidgetConfig.getChatTheme());
		webChatConfiguration.setHeaderDescription(businessChatWidgetConfig.getHeaderDescription());
		webChatConfiguration.setHeaderHeadline(
				replaceTextWithBusinessNamePrePopulated(businessChatWidgetConfig.getHeaderHeadline(), businessDTO));
		webChatConfiguration
				.setWebChatOnlineClosingMessageHeader(businessChatWidgetConfig.getWebChatOnlineClosingMessageHeader());
		webChatConfiguration.setWebChatOnlineClosingMessageBody(replaceTextWithBusinessNamePrePopulated(
				businessChatWidgetConfig.getWebChatOnlineClosingMessageBody(), businessDTO));
		webChatConfiguration.setPopupInterval(businessChatWidgetConfig.getPopupInterval());
		webChatConfiguration.setMicrosite(businessChatWidgetConfig.getMicrosite());
		webChatConfiguration.setBtnColor(businessChatWidgetConfig.getBtnColor());
		webChatConfiguration.setBtnTxtColor(businessChatWidgetConfig.getBtnTxtColor());
		webChatConfiguration.setEnableReplyInBusinessHr(businessChatWidgetConfig.getEnableReplyInBusinessHr());
		webChatConfiguration.setEnableReplyPostBusinessHr(businessChatWidgetConfig.getEnableReplyPostBusinessHr());
		webChatConfiguration.setAutoReplyTxt(
				replaceTextWithBusinessNamePrePopulated(businessChatWidgetConfig.getAutoReplyTxt(), businessDTO));
		webChatConfiguration.setReplyTextPostBusinessHr(replaceTextWithBusinessNamePrePopulated(
				businessChatWidgetConfig.getReplyTextPostBusinessHr(), businessDTO));
		webChatConfiguration.setChatBubble(businessChatWidgetConfig.getChatBubble());
		webChatConfiguration.setEnableChatBubble(businessChatWidgetConfig.getEnableChatBubble());
		webChatConfiguration.setEnableChatBubbleSound(businessChatWidgetConfig.getEnableChatBubbleSound());
		webChatConfiguration.setChatIconColor(businessChatWidgetConfig.getChatIconColor());
		webChatConfiguration.setChatIconForeColor(businessChatWidgetConfig.getChatIconForeColor());
		webChatConfiguration.setBusinessName(businessDTO.getBusinessAlias() == null ? businessDTO.getBusinessName()
				: businessDTO.getBusinessAlias());
		webChatConfiguration.setEnabled(businessChatWidgetConfig.getEnabled());
		webChatConfiguration.setUserProfile(prepareUserProfileData(businessChatWidgetConfig.getId()));
		webChatConfiguration
				.setWebChatOfflineClosingMessageBody(businessChatWidgetConfig.getOfflineClosingMessageBody());
		webChatConfiguration
				.setWebChatOfflineClosingMessageHeader(businessChatWidgetConfig.getOfflineClosingMessageHeader());

		// Populate the LiveChat and Chatbot configurations
		webChatConfiguration.setIsChatbotEnabled(
				businessChatWidgetConfig.getChatbotEnabled() != null ? businessChatWidgetConfig.getChatbotEnabled() == 1
						: false);
		webChatConfiguration.setIsLiveChatEnabled(businessChatWidgetConfig.getLivechatEnabled() != null
				? businessChatWidgetConfig.getLivechatEnabled() == 1
				: false);
		webChatConfiguration.setEmailMandatory(
				businessChatWidgetConfig.getEmailMandatory() != null ? businessChatWidgetConfig.getEmailMandatory() == 1
						: false);
		webChatConfiguration
				.setAutoDetectLocationEnabled(Integer.valueOf(1).equals(businessChatWidgetConfig.getAutoDetectLocation()));
		if (defaultConfig) {
			webChatConfiguration.setUserProfile(populateDefaultUserProfile());
			webChatConfiguration.setStatusUpdateOn(new Date());
			BusinessOptionResponse businessOptions = businessService
					.getBusinessOptionsConfig(businessDTO.getBusinessId(), true);
			if (businessOptions.getChatbot() != null && businessOptions.getChatbot() == 1) {
				webChatConfiguration.setIsChatbotEnabled(true);
			}
		} else {
			webChatConfiguration.setWidgetConfigId(businessChatWidgetConfig.getId());
			webChatConfiguration.setStatusUpdateOn(businessChatWidgetConfig.getStatusUpdatedOn());
		}

		// https://birdeye.atlassian.net/browse/BIRDEYE-56283
		webChatConfiguration.setEnableGoogleAnalytics(businessChatWidgetConfig.getEnableGoogleAnalytics() != null
				? businessChatWidgetConfig.getEnableGoogleAnalytics() == 1
				: false);
		webChatConfiguration.setGoogleAnalyticsVersion(businessChatWidgetConfig.getGoogleAnalyticsVersion());
		webChatConfiguration.setGoogleTrackingId(businessChatWidgetConfig.getGoogleTrackingId());
		webChatConfiguration.setInstalled(businessChatWidgetConfig.getInstalled());

		if (StringUtils.isNotEmpty(businessChatWidgetConfig.getWebsites())) {
			List<String> websites = Stream.of(businessChatWidgetConfig.getWebsites().split(",", -1))
					.collect(Collectors.toList());
			webChatConfiguration.setWebsites(websites);
		}
		webChatConfiguration.setExternalId(businessChatWidgetConfig.getExternalId());
		webChatConfiguration.setAllLocationDisabled(businessChatWidgetConfig.getAllLocationDisabled());
		log.info("Custom field is enabled for widgetId {} and businessNumber {}", businessChatWidgetConfig.getId(),
				businessChatWidgetConfig.getBusinessId());
		List<CustomFieldsWidget> customFieldWidgetList = customFieldService
				.getWebchatCustomFields(businessChatWidgetConfig.getId(), businessDTO.getBusinessNumber());
		if (CollectionUtils.isNotEmpty(customFieldWidgetList)) {
			List<CustomFieldWidgetDto> customFieldWidgetDtoList = new ArrayList<>();
			for (CustomFieldsWidget customFieldsWidget : customFieldWidgetList) {
				CustomFieldWidgetDto customFieldWidgetDto = new CustomFieldWidgetDto();
				customFieldWidgetDto.setId(customFieldsWidget.getFieldId());
				customFieldWidgetDto.setName(customFieldsWidget.getFieldName());
				customFieldWidgetDto.setType(customFieldsWidget.getFieldType());
				customFieldWidgetDto.setMandatory(customFieldsWidget.getMandatory());
				customFieldWidgetDto.setSubType(customFieldsWidget.getFieldSubType());
				customFieldWidgetDto.setSubTypeValues(ControllerUtil.decodeStringToList(customFieldsWidget.getSubTypeValues()));
				customFieldWidgetDtoList.add(customFieldWidgetDto);
			}
			webChatConfiguration.setCustomFields(customFieldWidgetDtoList);
		} else {
			log.info("There is no custom fields for business id - {}, widget id - {}", businessDTO.getBusinessNumber(),
					businessChatWidgetConfig.getId());
		}

		if (!StringUtils.isBlank(businessChatWidgetConfig.getDisclaimer())) {
			webChatConfiguration.setDisclaimer(businessChatWidgetConfig.getDisclaimer());
		} else {
			String defaultDisclaimer = String.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
					.getProperty("default_global_webchat_disclamier",
							"By sending this message, you expressly consent to receive communications from us. You may opt out at any time."));
			webChatConfiguration.setDisclaimer(defaultDisclaimer);
		}
		webChatConfiguration.setDisclaimerSelectionStatus(businessChatWidgetConfig.getDisclaimerSelectionStatus());
		webChatConfiguration.setEnablePrechatForm(businessChatWidgetConfig.getEnablePrechatForm());
		webChatConfiguration.setPrechatFormInsideBusinessHours(businessChatWidgetConfig.getPrechatFormInsideBusinessHours());
		webChatConfiguration.setPrechatFormOutsideBusinessHours(businessChatWidgetConfig.getPrechatFormOutsideBusinessHours());
		
		webChatConfiguration.setTimerDisplay(businessChatWidgetConfig.getTimerDisplay());
		webChatConfiguration.setTimerMinutes(businessChatWidgetConfig.getTimerMinutes());
		webChatConfiguration.setTimerSeconds(businessChatWidgetConfig.getTimerSeconds());
		webChatConfiguration.setMobileView(businessChatWidgetConfig.getMobileView());
		return webChatConfiguration;

	}

	private List<BusinessWebChatWidgetUserProfile> prepareUserProfileData(Integer widgetId) {
		// get default configuration
		List<BusinessChatWidgetUserProfile> userProfiles = businessChatWidgetConfigService
				.getUserProfileByChatWidgetId(widgetId);
		if (CollectionUtils.isEmpty(userProfiles)) {
			return populateDefaultUserProfile();
		}
		List<BusinessWebChatWidgetUserProfile> buserProfiles = new ArrayList<>();
		for (BusinessChatWidgetUserProfile userprofile : userProfiles) {
			BusinessWebChatWidgetUserProfile buser = new BusinessWebChatWidgetUserProfile();
			buser.setUserProfileName(userprofile.getUserProfileName());
			buser.setUserProfileImage(userprofile.getUserProfileImage());
			buserProfiles.add(buser);
		}
		return buserProfiles;
	}

	@Override
	public String replaceTextWithBusinessNamePrePopulated(String replacetextValue, BusinessDTO business) {
		if (business == null) {
			return replacetextValue;
		}
		String businessName = StringUtils.isNotBlank(business.getBusinessName()) ? business.getBusinessName()
				: business.getBusinessAlias();
		// Default valye for templatedAutoReplyTextValue :: Thank you for
		// contacting [BIZ_NAME]! Our team member will reach out shortly.
		return replaceTextWithBusinessNamePrePopulated(replacetextValue, businessName);
	}

	private String replaceTextWithBusinessNamePrePopulated(String replacetextValue, String businessName) {
		if (StringUtils.isBlank(replacetextValue)
				|| !StringUtils.contains(replacetextValue, "[BIZ_NAME]") || StringUtils.isEmpty(businessName)) {
			return replacetextValue;
		}
		if (StringUtils.isBlank(businessName)) {
			businessName = StringUtils.EMPTY;
		}
		if (replacetextValue.contains("[BIZ_NAME]")) {
			replacetextValue = replacetextValue.replace("[BIZ_NAME]", businessName);
		}
		return replacetextValue;
	}

	@Override
	public SaveWebchatResponse saveWidgetConfig(
			BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest) {
		SaveWebchatResponse saveWebchatResponse = new SaveWebchatResponse();
		if (businessWebChatConfigurationRequest.getWidgetConfigId() == null) {
			log.info("Webchat config Id is null!!");
			saveWebchatResponse.setMessage("Webchat can not be null!!");
			return saveWebchatResponse;
		}
		validateWebChatGAInput(businessWebChatConfigurationRequest);
		if (!StringUtils.isEmpty(businessWebChatConfigurationRequest.getWidgetName())) {
			String widgetName = getWidgetName(businessWebChatConfigurationRequest);
			businessWebChatConfigurationRequest.setWidgetName(widgetName);
		}
		Optional<BusinessChatWidgetConfig> businessChatWidgetConfigOptional = businessChatWidgetConfigService
				.saveOrUpdateBusinessChatWidgetConfig(businessWebChatConfigurationRequest);
		if (businessChatWidgetConfigOptional.isPresent()) {
			poolExecutor.execute(() -> evictWebchatCache(businessChatWidgetConfigOptional.get().getBusinessId(),
					businessChatWidgetConfigOptional.get().getId()));
			evictWebchatConfigTeamCache(businessChatWidgetConfigOptional.get().getId());
			saveWebchatResponse.setWebChatConfigId(businessChatWidgetConfigOptional.get().getId());
			saveWebchatResponse.setWidgetName(businessChatWidgetConfigOptional.get().getWidgetName());
			removeChatBotCustomHours(businessWebChatConfigurationRequest);
			saveRobinActiveHours(businessWebChatConfigurationRequest);
			saveChatBotCustomHours(businessWebChatConfigurationRequest);
			return saveWebchatResponse;
		}
		return null;
	}

	private void validateWebChatGAInput(BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest) {
		if (Objects.nonNull(businessWebChatConfigurationRequest.getEnableGoogleAnalytics()) &&
				businessWebChatConfigurationRequest.getEnableGoogleAnalytics().equals(Boolean.TRUE) &&
				(StringUtils.isBlank(businessWebChatConfigurationRequest.getGoogleTrackingId()) ||
				StringUtils.isBlank(businessWebChatConfigurationRequest.getGoogleAnalyticsVersion()))) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.GA_INVALID_TRACKING_ID, HttpStatus.BAD_REQUEST));
		}
	}

	private String getWidgetName(BusinessWebChatConfigurationRequest bwcr) {
		Integer enterpriseId = bwcr.getEnterpriseId() == null ? bwcr.getShortBusinessId() : bwcr.getEnterpriseId();
		String widgetName1 = "";
		widgetName1 = businessChatWidgetConfigService.getWidgetName(bwcr.getWidgetName(), bwcr.getBusinessId(),
				enterpriseId, bwcr.getWidgetConfigId());
		return widgetName1;
	}

	public BusinessWebchatWidgetList getWebchatWidgetList(Integer page, Integer size, Integer sortBy, Integer sortOrder,
			String searchText, UserLoginMessage userLoginMessage) {
		log.info("getWebchatWidgetList for businessId :: {} ", userLoginMessage.getBusinessId());
		// List<Integer> businessIds=new ArrayList<Integer>();
		// businessIds.addAll(userLoginMessage.getBusinessIds());
		// businessIds.add(userLoginMessage.getBusinessId());
		// call core service to get business details.
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(userLoginMessage.getBusinessId());
		Map<Long, BusinessDTO> businessDTOList = new HashMap<>();
		businessDTOList.put(businessDTO.getBusinessNumber(), businessDTO);
		// Map<Long, BusinessDTO> businessDTOList =
		// businessDTOMap.values().stream().collect(Collectors.toMap(BusinessDTO::getBusinessNumber,
		// Function.identity()));
		BusinessWebchatWidgetList businessWebchatWidgetList = new BusinessWebchatWidgetList();
		Page<BusinessWebchatWidgetDetails> pageOfBusinessWebchatWidgetList;
		Pageable pageable = null;

		String sortByColumn = null;
		if (sortBy != null && sortBy == 1) {
			sortByColumn = "widgetName";
		} else if (sortBy != null && sortBy == 3) {
			// used JpaSort.unsafe becoz need to sort the data based on alias in JPQL
			if (sortOrder != null && sortOrder == 1) {
				pageable = PageRequest.of(page, size);
			} else {
				pageable = PageRequest.of(page, size);
			}
		} else {
			// sort by 2
			sortByColumn = "updatedAt";
		}
		if (pageable == null) {
			if (sortOrder != null && sortOrder == 1) {
				pageable = PageRequest.of(page, size, Sort.by(sortByColumn).ascending());
			} else {
				pageable = PageRequest.of(page, size, Sort.by(sortByColumn).descending());
			}
		}

		// getting total count of widgets for a enterprise
		// TODO - need to cache this method
		Long totalCount = Long.valueOf(getTotalCount(businessDTOList.keySet()));
		businessWebchatWidgetList.setTotalCount(totalCount);
		businessWebchatWidgetList.setDisableCreateWidget(isCreateWidgetDisabled(businessDTO, totalCount));
		if (sortBy != null && sortBy == 3) {
			if (StringUtils.isEmpty(searchText)) {
				pageOfBusinessWebchatWidgetList = businessChatWidgetConfigRepository
						.findWidgetsByBusinessIdIncludeDefault(businessDTO.getBusinessNumber(), null);
			} else {
				pageOfBusinessWebchatWidgetList = businessChatWidgetConfigRepository
						.findWidgetsByBusinessIdIn(businessDTO.getBusinessNumber(), searchText, null);
				businessWebchatWidgetList.setSearchCount(pageOfBusinessWebchatWidgetList.getTotalElements());
			}
		} else {
			if (StringUtils.isEmpty(searchText)) {
				pageOfBusinessWebchatWidgetList = businessChatWidgetConfigRepository
						.findWidgetsByBusinessIdIncludeDefault(businessDTO.getBusinessNumber(), pageable);
			} else {
				pageOfBusinessWebchatWidgetList = businessChatWidgetConfigRepository
						.findWidgetsByBusinessIdIn(businessDTO.getBusinessNumber(), searchText, pageable);
				businessWebchatWidgetList.setSearchCount(pageOfBusinessWebchatWidgetList.getTotalElements());
			}
		}
		List<BusinessWebchatWidgetDetails> retrievedList = pageOfBusinessWebchatWidgetList.isEmpty()
				? businessWebchatWidgetList.getBusinessWebchatWidgetDetails()
				: pageOfBusinessWebchatWidgetList.getContent();
		if (CollectionUtils.isNotEmpty(retrievedList) && sortBy != null && sortBy == 3) {
			// used JpaSort.unsafe becoz need to sort the data based on alias in JPQL
			Comparator<BusinessWebchatWidgetDetails> comparator = Comparator.comparing(BusinessWebchatWidgetDetails::getStatus);
			if (sortOrder != null && sortOrder != 1) {
				//DESC
				comparator = comparator.reversed();
			}
			retrievedList = retrievedList.stream().sorted(comparator).collect(Collectors.toList());
			businessWebchatWidgetList.setTotalPages((int) Math.ceil((double) retrievedList.size()/ size));
			int start = (int) pageable.getOffset();
			int end = Math.min((start + pageable.getPageSize()), retrievedList.size());
			retrievedList = retrievedList.subList(start, end);
		}

		if (CollectionUtils.isNotEmpty(retrievedList)) {
			List<BusinessWebchatWidgetDetails> modifiedList = prepareWidgetList(retrievedList, businessDTOList);
			businessWebchatWidgetList.setBusinessWebchatWidgetDetails(modifiedList);
			// businessWebchatWidgetList.setTotalCount(Long.valueOf(modifiedList.size()));

			businessWebchatWidgetList.setPage(page);
			businessWebchatWidgetList.setSize(size);
			businessWebchatWidgetList.setTotalPages(pageOfBusinessWebchatWidgetList.getTotalPages());
		} else {
			List<BusinessWebchatWidgetDetails> retrievedList1 = new ArrayList<>();// UI needs empty list
			// retrievedList1.add(new BusinessWebchatWidgetDetails());
			businessWebchatWidgetList.setBusinessWebchatWidgetDetails(retrievedList1);
			// businessWebchatWidgetList.setTotalCount(0L);

			businessWebchatWidgetList.setPage(page);
			businessWebchatWidgetList.setSize(size);
			businessWebchatWidgetList.setTotalPages(0);
		}
		return businessWebchatWidgetList;
	}

	/**
	 * to populate BusinessName and enterpriseLevel field in widget list
	 *
	 * @param content
	 * @param businessDTOList
	 * @param businessNumber
	 * @return
	 */
	private List<BusinessWebchatWidgetDetails> prepareWidgetList(List<BusinessWebchatWidgetDetails> content,
			Map<Long, BusinessDTO> businessDTOList) {
		if (CollectionUtils.isNotEmpty(content)) {
			BusinessDTO business = businessDTOList.get(content.get(0).getBusinessNumber());
			List<BusinessDTO> childBusinesses = businessChatWidgetConfigService.getAllBusinessesInHierarchy(business);
			List<Integer> widgetIds = content.stream().map(bcwd -> bcwd.getId()).collect(Collectors.toList());
			Map<Integer, List<Long>> map = businessChatWidgetConfigService.getChatEnabledLocationIds(widgetIds);
			for (BusinessWebchatWidgetDetails listItem : content) {
				if (listItem.getBusinessNumber() != null && businessDTOList.get(listItem.getBusinessNumber()) != null) {
					listItem.setBusinessName(business.getBusinessName());
					listItem.setAlias(business.getBusinessAlias());
					listItem.setBusinessId(business.getBusinessId());
					List<BusinessLocationInfo> businessLocationInfos = businessChatWidgetConfigService
							.getBusinessLocationsDataInternal(listItem.getId(), map.get(listItem.getId()),
									childBusinesses);
					List<BusinessLocationInfo> chatEnabledLocs = businessLocationInfos.stream()
							.filter(l -> l.isChatEnabled()).collect(Collectors.toList());
					listItem.setBusinessLocations(chatEnabledLocs);
				}
			}
		}
		return content;
	}

	@Override
	public boolean webChatConfigOperation(WebChatConfigurationGenericRequest request) {
		boolean status = false;
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
				.getWebChatConfigByWidgetId(request.getWidgetConfigId());
		if (businessChatWidgetConfig != null) {
			BusinessDTO businessDTO = businessService
					.getBusinessByBusinessNumber(businessChatWidgetConfig.getBusinessId());
			switch (request.getRequestType()) {
				case Constants.DELETE_OPERATION:
					status = businessChatWidgetConfigService.deleteById(request.getWidgetConfigId());
					break;
				case Constants.ENABLE_OPERATION:
					status = businessChatWidgetConfigService
							.enableorDisableBusinessChatWidgetConfig(request.getWidgetConfigId(), 1);
					break;
				case Constants.DISABLE_OPERATION:
					status = businessChatWidgetConfigService
							.enableorDisableBusinessChatWidgetConfig(request.getWidgetConfigId(), 0);
			}
			// Audit update
			if (status) {
				poolExecutor.execute(() -> evictWebchatCache(businessDTO, request.getWidgetConfigId()));
				// evictPlatformCache(businessDTO);
				evictWebchatConfigTeamCache(request.getWidgetConfigId());
				BusinessChatWidgetConfigAudit businessChatWidgetConfigAudit = new BusinessChatWidgetConfigAudit(
						request.getWidgetConfigId(), request.getUserKey(), request.getRequestType());
				businessChatWidgetConfigAuditRepository.save(businessChatWidgetConfigAudit);
			}
		}
		return status;
	}

	public WebChatWidgetDefaultConfiguration getBusinessWebChatConfig(WebchatConfigFilter configFilter) {
		log.info("getBusinessWebChatConfig for businessId :: {} ", configFilter.getBusinessDTO().getBusinessId());
		WebChatWidgetDefaultConfiguration chatWidgetConfiguration = getWebChatConfigurationForBusiness(
				configFilter.getBusinessDTO());
		boolean isSmb = checkBusinessSMB(configFilter.getBusinessDTO());
		if (configFilter.getBusinessDTO().getEnterpriseId() == null) {
			chatWidgetConfiguration.setTeams(populateTeams(configFilter.getBusinessDTO().getBusinessId(),
					chatWidgetConfiguration.getWidgetConfigId(), null));
		} else {
			chatWidgetConfiguration.setTeams(populateTeams(configFilter.getBusinessDTO().getEnterpriseId(),
					chatWidgetConfiguration.getWidgetConfigId(), configFilter.getBusinessDTO().getBusinessId()));
		}
		chatWidgetConfiguration.setSmb(isSmb);
		chatWidgetConfiguration.setAutoDetectLocationEnabled(null);
		List<RobinActiveHours> robinActiveHours = robinActiveHoursService.getRobinHoursForBusinessId(configFilter.getBusinessDTO().getAccountId());
		if(CollectionUtils.isNotEmpty(robinActiveHours)) {
			List<Integer> robinInsideBusinessHours = new ArrayList<>();
			List<Integer> robinOutsideBusinessHours = new ArrayList<>();
			robinActiveHours.forEach(robinActiveHours1 -> {
				if (robinActiveHours1.getRobinInsideBusinessHours() == 1) {
					robinInsideBusinessHours.add(robinActiveHours1.getBusinessId());
				}
				if (robinActiveHours1.getRobinOutsideBusinessHours() == 1) {
					robinOutsideBusinessHours.add(robinActiveHours1.getBusinessId());
				}
			});
			chatWidgetConfiguration.setRobinInsideBusinessHours(robinInsideBusinessHours);
			chatWidgetConfiguration.setRobinOutsideBusinessHours(robinOutsideBusinessHours);
			chatWidgetConfiguration.setChatbotHours(robinActiveHoursService.getChatBotHoursByWidgetId(robinActiveHours));
		}
		return chatWidgetConfiguration;
	}

	private WebChatWidgetDefaultConfiguration getWebChatConfigurationForBusiness(BusinessDTO business) {
		WebChatWidgetDefaultConfiguration chatWidgetConfiguration = null;

		log.info("Fetching config from DB for business number {}", business.getBusinessNumber());
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
				.getBusinessChatWidgetConfig(business);
		if(businessChatWidgetConfig==null) {
			Integer accountId=business.getRoutingId();
			BusinessDTO accountDto = businessService.getBusinessLiteDTO(accountId);
			log.info("Default widget does not exist for accountId: {}: creating it at runtime", accountId);
			CreateDefaultConfigRequest createDefaultConfigRequest=createDefaultConfigRequest(accountDto);
			businessChatWidgetConfig=createDefaultWebChatConfig(createDefaultConfigRequest);
		}
		if (businessChatWidgetConfig != null) {
			chatWidgetConfiguration = populateWebChatWidgetDefaultConfiguration(business, businessChatWidgetConfig,
					false);
			populateLiveChatDefaultMessageConfig(chatWidgetConfiguration, businessChatWidgetConfig.getId(), business);
			chatWidgetConfiguration.setWidgetName(businessChatWidgetConfig.getWidgetName());
			chatWidgetConfiguration.setBusinessSMSPhoneNumber(fetchSmsPhoneNumber(business)); // fetching sms phone number
		}
		return chatWidgetConfiguration;
	}

	private CreateDefaultConfigRequest createDefaultConfigRequest(BusinessDTO business) {
		CreateDefaultConfigRequest createDefaultConfigRequest=new CreateDefaultConfigRequest();
		boolean isSMB="Business".equals(business.getType()) && business.getEnterpriseId()==null;
		boolean isEnterprise=("Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType()));
		createDefaultConfigRequest.setBusinessId(business.getBusinessId());
		createDefaultConfigRequest.setBusinessNumber(business.getBusinessNumber());
		createDefaultConfigRequest.setIsEnterprise(isEnterprise);
		createDefaultConfigRequest.setIsSMB(isSMB);
		return createDefaultConfigRequest;
	}

	private String getWebChatConfigRedisKey(Long businessId) {
		String fieldName = "WebChatConfig:";
		fieldName += businessId;
		return fieldName;
	}

	private String getWebChatConfigWebsiteRedisKey(Long businessId) {
		String fieldName = "WebChatConfigWebsite:";
		fieldName += businessId;
		return fieldName;
	}

	private String getWebChatConfigLocationRedisKey(Long businessId) {

		String fieldName = "Web-chat:";
		fieldName += businessId;
		return fieldName;
	}

	private boolean checkBusinessSMB(BusinessDTO business) {
		return "Business".equals(business.getType());
	}

	@Override
	public WebChatWidgetDefaultConfiguration getWebChatConfigForWidgetId(Long businessId, Integer widgetId,
			BusinessDTO businessDTO) {
		log.info("getWebChatConfigForWidgetId for widgetId :: {} ", widgetId);
		WebChatWidgetDefaultConfiguration chatWidgetConfiguration = null;
		BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
				.getWebChatConfigByWidgetId(widgetId);
		LiveChatWidgetConfig liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(widgetId);

		if (businessChatWidgetConfig != null) {
			if (businessDTO == null) {
				businessDTO = businessService.getBusinessByBusinessNumber(
						widgetId == 1 ? businessId : businessChatWidgetConfig.getBusinessId());
			}

			chatWidgetConfiguration = populateWebChatWidgetDefaultConfiguration(businessDTO, businessChatWidgetConfig,
					false);
			// if (businessChatWidgetConfig.getEnterpriseId() == null) {
			// Widget at enterpriseLevel, Need to provide businessLocations
			List<BusinessLocationInfo> businessLocations = businessChatWidgetConfigService
					.getBusinessLocationsData(chatWidgetConfiguration.getWidgetConfigId(), businessDTO);
			if (CollectionUtils.isNotEmpty(businessLocations)) {
				log.info("businessLocationInfos for id : {}, size : {}", businessChatWidgetConfig.getId(),
						businessLocations.size());
				List<BusinessLocationInfo> filteredBusinessLocationInfos = businessLocations.stream()
						.filter(bl -> bl.isChatEnabled()).collect(Collectors.toList());
				chatWidgetConfiguration.setBusinessLocations(filteredBusinessLocationInfos);
				chatWidgetConfiguration.setBusinessSMSPhoneNumber(
						businessLocations.stream().filter(bl -> StringUtils.isNotEmpty(bl.getBusinessSMSPhoneNumber()))
								.collect(Collectors.toList()).get(0).getBusinessSMSPhoneNumber());
			}
			// BIRDEYE-60049
			chatWidgetConfiguration
					.setTeams(populateTeams(businessDTO.getBusinessId(), businessChatWidgetConfig.getId(), null));
			LocationHierarchy locationHierarchy = populateLocationHierarchy(businessChatWidgetConfig.getId());
			if (locationHierarchy != null) {
				chatWidgetConfiguration.setLocationHierarchy(locationHierarchy);
			}
			// }else {
			// chatWidgetConfiguration.setTeams(populateTeams(businessDTO.getEnterpriseId(),businessChatWidgetConfig.getId(),businessDTO.getBusinessId()));
			// chatWidgetConfiguration.setBusinessSMSPhoneNumber(fetchSmsPhoneNumber(businessDTO));
			// }
		}

		if (liveChatWidgetConfig != null) {
			populateLiveChatDefaultMessageConfig(chatWidgetConfiguration, widgetId, businessDTO);
		}
		List<RobinActiveHours> robinActiveHours = robinActiveHoursService.getRobinHoursBusinessIdForWidgetId(chatWidgetConfiguration.getWidgetConfigId());
		if(CollectionUtils.isNotEmpty(robinActiveHours)) {
			List<Integer> robinInsideBusinessHours = new ArrayList<>();
			List<Integer> robinOutsideBusinessHours = new ArrayList<>();
			robinActiveHours.forEach(robinActiveHours1 -> {
				if (robinActiveHours1.getRobinInsideBusinessHours() == 1) {
					robinInsideBusinessHours.add(robinActiveHours1.getBusinessId());
				}
				if (robinActiveHours1.getRobinOutsideBusinessHours() == 1) {
					robinOutsideBusinessHours.add(robinActiveHours1.getBusinessId());
				}
			});
			chatWidgetConfiguration.setRobinInsideBusinessHours(robinInsideBusinessHours);
			chatWidgetConfiguration.setRobinOutsideBusinessHours(robinOutsideBusinessHours);
			chatWidgetConfiguration.setChatbotHours(robinActiveHoursService.getChatBotHoursByWidgetId(robinActiveHours));
		}
		return chatWidgetConfiguration;
	}

	private LocationHierarchy populateLocationHierarchy(Integer id) {
		List<BusinessChatLocationHierarchy> businessChatLocationHierarchies = businessChatWidgetConfigService
				.getLocationHierarchy(id);
		if (CollectionUtils.isNotEmpty(businessChatLocationHierarchies)) {
			LocationHierarchy locationHierarchy = new LocationHierarchy();
			List<String> sublevels = new ArrayList<>();
			for (BusinessChatLocationHierarchy bclh : businessChatLocationHierarchies) {
				locationHierarchy.setLevelId(bclh.getLevelId());
				sublevels.add(bclh.getSubLevel());
			}
			locationHierarchy.setSubLevels(sublevels);
			return locationHierarchy;
		}
		return null;
	}

	private List<BusinessWebChatWidgetUserProfile> populateDefaultUserProfile() {
		BusinessWebChatWidgetUserProfile businessWebChatWidgetUserProfile = new BusinessWebChatWidgetUserProfile();
		businessWebChatWidgetUserProfile.setUserProfileName("");
		businessWebChatWidgetUserProfile.setUserProfileImage("");
		List<BusinessWebChatWidgetUserProfile> businessChatWidgetUserProfiles = new ArrayList<>();
		businessChatWidgetUserProfiles.add(businessWebChatWidgetUserProfile);
		return businessChatWidgetUserProfiles;
	}

	@Override
	@Transactional
	public void webChatConfigAction(BusinessDeactivationRequest request) {

		List<Integer> widgetIds = new ArrayList<>();
		if (request.getClosed() == 1 || request.getActivationStatus().equalsIgnoreCase(BusinessDTO.CANCELLED)
				|| request.getActivationStatus().equalsIgnoreCase(BusinessDTO.SUSPENDED)) {
			if (request.getIsSMB() || request.getIsEnterprise()) {
				List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigService
						.getWebChatConfigByBusinessNumber(request.getBusinessNumber());
				if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
					widgetIds = businessChatWidgetConfigs.stream()
							.map(businessChatWidgetConfig -> businessChatWidgetConfig.getId())
							.collect(Collectors.toList());
					businessChatWidgetConfigService.enableorDisableBusinessChatWidgetConfigs(widgetIds, 0);
				}
			} else {
				widgetIds = deleteLocationsIfPresent(request.getBusinessNumber());
			}
		}
		for (Integer id : widgetIds) {
			poolExecutor.execute(() -> evictWebchatCache(request.getBusinessNumber(), id));
			poolExecutor.execute(() -> evictWebchatConfigTeamCache(id));
		}
	}

	private List<Integer> deleteLocationsIfPresent(Long businessNumber) {

		List<BusinessChatEnabledLocation> businessChatEnabledLocationsByNumber = businessChatWidgetConfigService
				.findByBusinessNumber(businessNumber);
		List<Integer> widgetIds = businessChatEnabledLocationsByNumber.stream()
				.map(bcel -> bcel.getBusinessChatWidgetId()).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(widgetIds)) {
			Map<Integer, List<Long>> map = businessChatWidgetConfigService.getChatEnabledLocationIds(widgetIds);
			if (CollectionUtils.isNotEmpty(businessChatEnabledLocationsByNumber)) {
				businessChatEnabledLocationRepository.deleteAll(businessChatEnabledLocationsByNumber);
			}
			List<Integer> widgetsToBeDisable = new ArrayList<>();
			map.entrySet().stream().forEach(e -> {
				if (e.getValue().size() == 1) {
					widgetsToBeDisable.add(e.getKey());
				}
			});
			if (CollectionUtils.isNotEmpty(widgetsToBeDisable)) {
				businessChatWidgetConfigService.disableBusinessChatWidgetConfigsAndLocations(widgetsToBeDisable);
			}

			return widgetIds;
		}
		return new ArrayList<>();
	}

	private int getTotalCount(Set<Long> businessIds) {
		List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigRepository
				.getWebChatConfigByBusinessNumbers(businessIds);
		if (!CollectionUtils.isEmpty(businessChatWidgetConfigs)) {
			return businessChatWidgetConfigs.size();
		}
		return 0;
	}

	// TODO - This needs to be Async. will push a event and then evict
	private void evictPlatformCache(Long businessNumber, Integer enterpriseId) {
		if (enterpriseId == null) {
			BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(businessNumber);
			List<BusinessDTO> businessDTOs = businessChatWidgetConfigService.getAllBusinessesInHierarchy(businessDTO);
			if (CollectionUtils.isNotEmpty(businessDTOs)) {
				for (BusinessDTO businessDTO1 : businessDTOs) {
					evictWebchatConfigCache(businessDTO1.getBusinessNumber());
				}
			}
		} else {
			evictWebchatConfigCache(businessNumber);

		}
		evictWebchatConfigWebsiteCache(businessNumber);// this needs to be evict for business website
		evictWebchatConfigLocationCache(businessNumber);// this need to evict location cache at platform

	}

	private void evictPlatformCache(BusinessDTO businessDTO) {
		if (businessDTO != null && businessDTO.getEnterpriseId() == null) {
			List<BusinessDTO> businessDTOs = businessChatWidgetConfigService.getAllBusinessesInHierarchy(businessDTO);
			if (CollectionUtils.isNotEmpty(businessDTOs)) {
				for (BusinessDTO businessDTO1 : businessDTOs) {
					evictWebchatConfigCache(businessDTO1.getBusinessNumber());
				}
			}
		} else {
			evictWebchatConfigCache(businessDTO.getBusinessNumber());
		}
		evictWebchatConfigWebsiteCache(businessDTO.getBusinessNumber());
		evictWebchatConfigLocationCache(businessDTO.getBusinessNumber());
	}

	private void evictWebchatConfigCache(Long businessNumber) {
		String redisKey = getWebChatConfigRedisKey(businessNumber);
		redisHandler.evictPlatformCache(redisKey);
	}

	private void evictWebchatConfigWebsiteCache(Long businessNumber) {
		String redisKey = getWebChatConfigWebsiteRedisKey(businessNumber);
		redisHandler.evictPlatformCache(redisKey);
	}

	private void evictWebchatConfigLocationCache(Long businessNumber) {
		String redisKey = getWebChatConfigLocationRedisKey(businessNumber);
		redisHandler.evictPlatformCache(redisKey);
	}

	@Override
	@Transactional
	public boolean createDefaultConfig(CreateDefaultConfigRequest request) {
		return createDefaultWebChatConfig(request)!=null?true:false;
	}

	public BusinessChatWidgetConfig createDefaultWebChatConfig(CreateDefaultConfigRequest request) {
		if (request.getIsSMB() || request.getIsEnterprise()) {
			log.info("Request received to create default widget for accountId: {}",request.getBusinessId());
			if (checkIfAlreadyCreated(request.getBusinessNumber())) {
				return null;
			}
			BusinessDTO businessDTO = businessService.getBusinessDTO(request.getBusinessId());
			if (businessDTO != null) {

				BusinessChatWidgetConfig businessChatWidgetConfig = businessChatWidgetConfigService
						.getDefaultBusinessChatWidgetConfig();
				BusinessChatWidgetConfig businessChatWidgetConfigNew = null;
				try {
					businessChatWidgetConfigNew = (BusinessChatWidgetConfig) businessChatWidgetConfig.clone();
				} catch (Exception e) {
					log.error("Exception while cloning business chat widget config:{}", e);
					throw new MessengerException(ErrorCode.ERROR_WHILE_CLONING_CHAT_CONFIG);
				}

				businessChatWidgetConfigNew.setId(null);
				businessChatWidgetConfigNew.setBusinessId(request.getBusinessNumber());
				businessChatWidgetConfigNew.setWidgetName(Constants.DEFAULT_WIDGET_NAME);
				businessChatWidgetConfigNew.setAutoReplyTxt(replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getAutoReplyTxt(), businessDTO));
				businessChatWidgetConfigNew.setReplyTextPostBusinessHr(replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getReplyTextPostBusinessHr(), businessDTO));
				businessChatWidgetConfigNew.setWebChatOnlineClosingMessageHeader(
						businessChatWidgetConfig.getWebChatOnlineClosingMessageHeader());
				businessChatWidgetConfigNew.setWebChatOnlineClosingMessageBody(replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getWebChatOnlineClosingMessageBody(), businessDTO));
				businessChatWidgetConfigNew.setHeaderHeadline(replaceTextWithBusinessNamePrePopulated(
						businessChatWidgetConfig.getHeaderHeadline(), businessDTO));
				businessChatWidgetConfigNew
						.setOfflineClosingMessageBody(businessChatWidgetConfig.getOfflineClosingMessageBody());
				businessChatWidgetConfigNew
						.setOfflineClosingMessageHeader(businessChatWidgetConfig.getOfflineClosingMessageHeader());
				String defaultDisclaimer = String.valueOf(CacheManager.getInstance()
						.getCache(SystemPropertiesCache.class).getProperty("default_global_webchat_disclamier",
								"By sending this message, you expressly consent to receive communications from us. You may opt out at any time."));
				businessChatWidgetConfigNew.setDisclaimer(defaultDisclaimer);
				if (request.getIsSMB()) {
					businessChatWidgetConfigNew.setEnterpriseId(request.getEnterpriseId());
				}
				businessChatWidgetConfigNew.setUpdatedBy(String.valueOf(request.getUserId()));
				businessChatWidgetConfigNew.setUpdatedAt(new Date());
				businessChatWidgetConfigNew.setCreatedAt(new Date());
				businessChatWidgetConfigNew.setStatusUpdatedOn(new Date());

				BusinessOptionResponse businessOptions = businessService
						.getBusinessOptionsConfig(businessDTO.getBusinessId(), true);
				if (businessOptions.getChatbot() != null && businessOptions.getChatbot() == 1) {
					businessChatWidgetConfigNew.setChatbotEnabled(1);
				}
				businessChatWidgetConfigNew.setDisclaimerSelectionStatus(businessChatWidgetConfig.getDisclaimerSelectionStatus());
				try {
					BusinessChatWidgetConfig businessChatWidgetConfig1 = businessChatWidgetConfigRepository
							.saveAndFlush(businessChatWidgetConfigNew);
					createDefaultLiveChatMessageConfig(businessChatWidgetConfig1, null);
					businessChatWidgetConfig1.setExternalId(businessChatWidgetConfig1.getId().longValue());
					businessChatWidgetConfigRepository.saveAndFlush(businessChatWidgetConfig1);
					log.info("Default webchat for business Id:{} is created successfully", request.getBusinessId());
					return businessChatWidgetConfig1;
				} catch (Exception e) {
					log.error("Default webchat for business Id:{} fialed:{}", request.getBusinessId(), e);
					return null;
				}
			}
		}
		return null;
	}

	private void createDefaultLiveChatMessageConfig(BusinessChatWidgetConfig businessChatWidgetConfig1,
			Integer cloneId) {
		LiveChatWidgetConfig liveChatWidgetConfig = null;
		if (cloneId != null) {
			liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(cloneId);
		} else {
			liveChatWidgetConfig = liveChatMessageConfigRepository.findByWidgetId(Constants.DEFAULT_CONFIG_ID);
		}
		LiveChatWidgetConfig businessDefaultLiveChatWidgetConfig = new LiveChatWidgetConfig();
		businessDefaultLiveChatWidgetConfig.setWidgetId(businessChatWidgetConfig1.getId());
		businessDefaultLiveChatWidgetConfig.setOfflineWelcomeMessage(liveChatWidgetConfig.getOfflineWelcomeMessage());
		businessDefaultLiveChatWidgetConfig
				.setOfflineClosingMessageBody(liveChatWidgetConfig.getOfflineClosingMessageBody());
		businessDefaultLiveChatWidgetConfig
				.setOfflineClosingMessageHeader(liveChatWidgetConfig.getOfflineClosingMessageHeader());
		businessDefaultLiveChatWidgetConfig
				.setOnlineClosingMessageBody(liveChatWidgetConfig.getOnlineClosingMessageBody());
		businessDefaultLiveChatWidgetConfig
				.setOnlineClosingMessageHeader(liveChatWidgetConfig.getOnlineClosingMessageHeader());
		businessDefaultLiveChatWidgetConfig.setOnlineWelcomeMessage(liveChatWidgetConfig.getOnlineWelcomeMessage());

		liveChatMessageConfigRepository.saveAndFlush(businessDefaultLiveChatWidgetConfig);

	}

	@Override
	public boolean checkIfAlreadyCreated(Long businessNumber) {
		List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigService
				.getWebChatConfigByBusinessNumber(businessNumber);
		if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
			return true;
		}
		return false;
	}

	@Override
	public void addLocationLevelWidgetRequests(LocationLevelRequest request) {
		if (request != null) {
			LocationWidgetDetails locationWidgetDetails = new LocationWidgetDetails();
			locationWidgetDetails.setBusinessId(request.getBusinessId());
			locationWidgetDetails.setAccountType(request.getAccountType());
			locationWidgetDetails.setName(request.getName());
			locationWidgetDetails.setType(request.getType());
			if (request.getEnterprise() != null) {
				locationWidgetDetails.setEnterpriseId(request.getEnterprise().getBusinessId());
			}
			locationWidgetDetails.setCreateAt(new Date());
			locationWidgetDetailsRepository.save(locationWidgetDetails);
		}
	}

	private String fetchSmsPhoneNumber(BusinessDTO businessDTO) {
		List<BusinessDTO> businessDTOS = businessChatWidgetConfigService.getAllBusinessesInHierarchy(businessDTO);
		if (CollectionUtils.isNotEmpty(businessDTOS)) {
			return businessDTOS.get(0).getPhoneNumber();
		}
		return "";
	}

	private boolean isCreateWidgetDisabled(BusinessDTO businessDTO, Long totalCount) {
		// Restrict creating widget with some large number
		if (totalCount >= Constants.WIDGET_CREATION_LIMIT) {
			return true;
		}
		return false;
	}

	@Override
	public WebChatWidgetDefaultConfiguration getWebsiteWebchatConfig(Long externalId, boolean isMicrosite) {
		log.info("Get webchat config for business : {}, ismicrosite:{}", externalId, isMicrosite);
		BusinessDTO businessDTO = new BusinessDTO();
		BusinessChatWidgetConfig businessChatWidgetConfig = null;
		if (isMicrosite) {
			// checking for enterprise if not available on location.
			businessDTO = businessService.getBusinessByBusinessNumber(externalId);
			if (businessDTO == null) {
				log.error("Invalid value passed for business: {}, ismicrosite: {}", externalId, isMicrosite);
				return null;
			}

			businessChatWidgetConfig = businessChatWidgetConfigService.getWebchatConfigMicrosite(businessDTO);
			// Enable Widget For All Mictosites - If widget config is not available for
			// microsite - return default
			if (businessChatWidgetConfig == null) {
				businessChatWidgetConfig = getDefaultBusinessChatWidgetConfig();
			}
		} else {
			// Not checking for enterprise if not available on location.
			businessChatWidgetConfig = businessChatWidgetConfigService.getWebchatConfigBusinessWebsite(externalId);
			if (businessChatWidgetConfig == null) {
				return null;
			}
			businessDTO = businessService.getBusinessByBusinessNumber(businessChatWidgetConfig.getBusinessId());
		}
		if (businessChatWidgetConfig != null) {
			WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration = populateWebChatWidgetDefaultConfiguration(
					businessDTO, businessChatWidgetConfig, false);
			LiveChatWidgetConfig liveChatWidgetConfig = liveChatMessageConfigRepository
					.findByWidgetId(businessChatWidgetConfig.getId());
			if (liveChatWidgetConfig != null) {
				populateLiveChatDefaultMessageConfig(webChatWidgetDefaultConfiguration,
						businessChatWidgetConfig.getId(), businessDTO);
			}
			// Get business locations
			List<BusinessLocationInfo> businessLocationInfos = businessChatWidgetConfigService
					.getBusinessLocationsData(businessChatWidgetConfig.getId(), businessDTO);
			if (CollectionUtils.isNotEmpty(businessLocationInfos)) {
				log.info("businessLocationInfos for id : {}, size : {}", businessChatWidgetConfig.getId(),
						businessLocationInfos.size());
			}
			// filter Chat enabled locations
			List<BusinessLocationInfo> filteredBusinessLocationInfos = businessLocationInfos.stream()
					.filter(businessLocationInfo -> businessLocationInfo.isChatEnabled()).collect(Collectors.toList());
			if (CollectionUtils.isNotEmpty(filteredBusinessLocationInfos)) {
				webChatWidgetDefaultConfiguration.setBusinessLocations(filteredBusinessLocationInfos);
			} else {
				webChatWidgetDefaultConfiguration.setBusinessLocations(businessLocationInfos);
			}
			webChatWidgetDefaultConfiguration.setSmb(checkBusinessSMB(businessDTO));
			webChatWidgetDefaultConfiguration
					.setSupportedCountryCodes(businessService.getSupportedCountryCodes(businessDTO.getBusinessId()));

			Integer isMessengerEnabled = businessService.isMessengerEnabled(businessDTO.getAccountId());
			if (isMessengerEnabled != null
					&& isMessengerEnabled == 1) {
				List<TeamDto> teams = populateTeams(webChatWidgetDefaultConfiguration.getWidgetConfigId());
				if (CollectionUtils.isNotEmpty(teams)) {
					List<Team> teams1 = teams.stream().map(teamDto -> new Team(teamDto.getTeamId(), teamDto.getName()))
							.collect(Collectors.toList());
					webChatWidgetDefaultConfiguration.setTeams(teams1);
				}
			}
			return webChatWidgetDefaultConfiguration;
		}
		return null;
	}

	private WebChatWidgetDefaultConfiguration encodeWebChatConfiguration(
			WebChatWidgetDefaultConfiguration configuration) {
		if (configuration == null) {
			return null;
		}
		configuration.setHeaderDescription(encodeString(configuration.getHeaderDescription()));
		configuration.setThankyouMsgDescription(encodeString(configuration.getThankyouMsgDescription()));
		configuration.setHeaderHeadline(encodeString(configuration.getHeaderHeadline()));
		configuration.setThankyouMsgHeadline(encodeString(configuration.getThankyouMsgHeadline()));
		configuration.setAutoReplyTxt(encodeString(configuration.getAutoReplyTxt()));
		configuration.setChatBubble(encodeString(configuration.getChatBubble()));
		if (CollectionUtils.isNotEmpty(configuration.getUserProfile())) {
			for (BusinessWebChatWidgetUserProfile profile : configuration.getUserProfile()) {
				profile.setUserProfileName(encodeString(profile.getUserProfileName()));
			}
		}
		// handling location data also
		if (CollectionUtils.isNotEmpty(configuration.getBusinessLocations())) {
			for (BusinessLocationInfo locationInfo : configuration.getBusinessLocations()) {
				locationInfo.setLocation(encodeString(locationInfo.getLocation()));
			}
		}
		return configuration;
	}

	private String encodeString(String str) {
		if (StringUtils.isBlank(str)) {
			return str;
		}
		return str.replaceAll("\\'", "%27");
	}

	private void evictWebchatCache(Long businessId, Integer widgetId) {
		BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(businessId);
		evictWebchatCache(businessDTO, widgetId);
	}

	private void evictWebchatCache(BusinessDTO businessDTO, Integer widgetId) {
		List<String> keys = new ArrayList<>();
		keys.add(Constants.WEBCHAT_WEBSITE_CACHE + "::External-" + businessDTO.getBusinessNumber());
		keys.add(Constants.WEBCHAT_WEBSITE_CACHE + "::External-" + widgetId);
		keys.add(Constants.WEBCHAT_MICROSITE_CACHE + "::BNO-" + businessDTO.getBusinessNumber());
		keys.add(Constants.LOCATION_CACHE + "::webchat-" + widgetId + "-" + businessDTO.getBusinessId());
		keys.add(Constants.LOCATION_CACHE + "::webchatInternal-" + widgetId);
		keys.add(Constants.BUSINESS_CACHE + "::SMS_ENABLED_BID-" + businessDTO.getBusinessId());
		keys.add(Constants.WEBCHAT_CUSTOM_FIELDS + "::Widget_id-" + widgetId);
		if (businessDTO.getEnterpriseId() == null) {
			List<BusinessDTO> businessDTOs = businessChatWidgetConfigService.getAllBusinessesInHierarchy(businessDTO);
			if (CollectionUtils.isNotEmpty(businessDTOs)) {
				List<String> locationKeys = businessDTOs.stream().map(
						businessDTO1 -> Constants.WEBCHAT_MICROSITE_CACHE + "::BNO-" + businessDTO.getBusinessNumber())
						.collect(Collectors.toList());
				keys.addAll(locationKeys);
			}
		}
		redisHandler.evictWebChatCache(keys);
	}

	@Async
	public void sendAutoReplyToCustomerForWebchatSMS(Integer widgetConfigId, BusinessDTO businessDTO,
			CustomerDTO customerDTO, boolean sendLivechatTextMessage) {
		log.info("sendAutoReplyToCustomerForWebchatSMS for business : {}, widgetConfigId:{}",
				businessDTO.getBusinessId(), widgetConfigId);
		WebChatWidgetDefaultConfiguration webChatWidgetDefaultConfiguration = null;
		if (widgetConfigId != null) {
			webChatWidgetDefaultConfiguration = getWebChatConfigForWidgetId(businessDTO.getBusinessNumber(),
					widgetConfigId, businessDTO);
		} else {
			webChatWidgetDefaultConfiguration = getWebChatConfigurationForBusiness(businessDTO);
		}

		SendMessageDTO sendMessageDTO = getSMSDataForAutoResponse(webChatWidgetDefaultConfiguration, businessDTO,
				customerDTO, sendLivechatTextMessage);
		try {
			if (sendMessageDTO != null)
				smsSendEventHandler.handle(sendMessageDTO);
		} catch (Exception e) {
			log.error("Exception while sending message:{}", e);
		}
	}

	private SendMessageDTO getSMSDataForAutoResponse(WebChatWidgetDefaultConfiguration webChatWidgetConfiguration,
			BusinessDTO businessDTO,
			CustomerDTO customer, boolean sendLivechatTextMessage) {
		if (webChatWidgetConfiguration != null &&
				(webChatWidgetConfiguration.getEnableReplyPostBusinessHr() != 0
						|| webChatWidgetConfiguration.getEnableReplyInBusinessHr() != 0)) {
			SendMessageDTO sendMessageDTO = new SendMessageDTO();
			MessengerContact messengerContact = messengerContactService.findByCustomerId(customer.getId());
			String businessName = StringUtils.isNotBlank(businessDTO.getBusinessAlias())
					? businessDTO.getBusinessAlias()
					: businessDTO.getBusinessName();
			sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
			sendMessageDTO.setToCustomerId(String.valueOf(messengerContact.getId()));
			sendMessageDTO.setCustomerId(customer.getId());
			sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
			sendMessageDTO.setMessengerContact(messengerContact);
			// default source is sms
			if (sendMessageDTO.getSource() == null) {
				sendMessageDTO.setSource(1);
			}
			sendMessageDTO.setSource(sendMessageDTO.getSource());

			BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessDTO.getBusinessId(), true);
			String autoReplyText = null;
			if (sendLivechatTextMessage && BooleanUtils.isTrue(webChatWidgetConfiguration.getIsLiveChatEnabled())) {
				autoReplyText = getLiveChatAutoReplyText(webChatWidgetConfiguration, businessDTO, businessTimingDTO);
			} else {
				autoReplyText = getWebChatAutoReplyText(webChatWidgetConfiguration, businessDTO, businessTimingDTO);
			}

			if (StringUtils.isEmpty(autoReplyText)) {
				return null;
			}
			String phoneNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId(),
					businessDTO.getPhone());
			String textingNumber = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
			String msgBody = replaceTextWithBusinessNamePrePopulated(autoReplyText, businessName);
			PublicDataBusinessDTO publicBusinessDto = null;
			if (msgBody.contains(Constants.BUSINESS_SERVICES) || msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
	        		|| msgBody.contains(Constants.BUSINESS_LANGUAGES) || msgBody.contains(Constants.BUSINESS_SERVICES_CAP)
    				|| msgBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || msgBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
				publicBusinessDto = businessService.getPublicBusinessData(businessDTO.getBusinessId());
			}
			BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(businessDTO.getBusinessId());
			BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessDTO.getBusinessId());
			String formattedBody = ControllerUtil.replaceTokens(businessDTO, customer.getFirstName(),
					msgBody, phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto,businessProfileData,customFields);

			sendMessageDTO.setBody(formattedBody);

			UserDTO userDTO = new UserDTO();
			userDTO.setId(MessengerConstants.AUTO_REPLY_USER);
			sendMessageDTO.setUserDTO(userDTO);
			sendMessageDTO.setBOT(true);
			sendMessageDTO.setUpdateLastMessage(false);
			return sendMessageDTO;
		}
		return null;
	}

	private String getWebChatAutoReplyText(WebChatWidgetDefaultConfiguration webChatWidgetConfiguration,
			BusinessDTO business, BusinessTimingDTO businessTimingDTO) {

		Boolean isReceivedDuringBusinessHours = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
				new Date());
		if (isReceivedDuringBusinessHours) {
			if (webChatWidgetConfiguration.getEnableReplyInBusinessHr() != 0) {
				return webChatWidgetConfiguration.getAutoReplyTxt();
			}
		} else {
			if (webChatWidgetConfiguration.getEnableReplyPostBusinessHr() != 0) {
				return webChatWidgetConfiguration.getReplyTextPostBusinessHr();
			}
		}
		// if (businessTimingDTO.getWholeWeekOperating() == 1) {
		// if (webChatWidgetConfiguration.getEnableReplyInBusinessHr() != 0) {
		// return webChatWidgetConfiguration.getAutoReplyTxt();
		// }
		// return null;
		// }
		// if (businessTimingDTO.getWholeWeekOperating() == 2) {
		// if (webChatWidgetConfiguration.getEnableReplyPostBusinessHr() != 0) {
		// return webChatWidgetConfiguration.getReplyTextPostBusinessHr();
		// }
		// return null;
		// }

		if (webChatWidgetConfiguration.getEnableReplyInBusinessHr() != 0
				&& webChatWidgetConfiguration.getEnableReplyPostBusinessHr() == 0) {
			if (isReceivedDuringBusinessHr(businessTimingDTO, business)) {
				return webChatWidgetConfiguration.getAutoReplyTxt();
			}
			return null;
		}

		// below logic is for custom business hours ( other than 27 x 7 and
		// by_appointments_only )
		// boolean receivedDuringBusinessHr = isReceivedDuringBusinessHr(business);
		// LOGGER.log(Level.INFO, "getWebChatAutoReplyText : receivedDuringBusinessHr
		// {0} ", new Object[]{receivedDuringBusinessHr});

		if (webChatWidgetConfiguration.getEnableReplyInBusinessHr() != 0
				&& webChatWidgetConfiguration.getEnableReplyPostBusinessHr() != 0) {
			if (isReceivedDuringBusinessHr(businessTimingDTO, business)) {
				return webChatWidgetConfiguration.getAutoReplyTxt();
			}
			return webChatWidgetConfiguration.getReplyTextPostBusinessHr();
		}
		if (webChatWidgetConfiguration.getEnableReplyInBusinessHr() == 0
				&& webChatWidgetConfiguration.getEnableReplyPostBusinessHr() != 0) {
			if (!isReceivedDuringBusinessHr(businessTimingDTO, business)) {
				return webChatWidgetConfiguration.getReplyTextPostBusinessHr();
			}
		}
		return null;
	}

	private String getLiveChatAutoReplyText(WebChatWidgetDefaultConfiguration webChatWidgetConfiguration,
			BusinessDTO businessDTO, BusinessTimingDTO businessTimingDTO) {
		Boolean isReceivedDuringBusinessHours = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,
				new Date());
		if (isReceivedDuringBusinessHours) {
			return webChatWidgetConfiguration.getLiveChatOnlineTextMessage();
		} else {
			return webChatWidgetConfiguration.getLiveChatOfflineTextMessage();
		}
	}

	private List<Team> populateTeams(Integer accountId, Integer widgetConfigId, Integer locationId) {

		List<Team> teams = new ArrayList<>();
		List<TeamDTO> teamListDto = businessService.getTeams(accountId, locationId);
		List<Integer> businessChatEnabledTeamIds = null;
		if (widgetConfigId != null) {
			businessChatEnabledTeamIds = businessChatWidgetConfigService.getTeamIdsByWidgetConfigId(widgetConfigId);
		}
		if (CollectionUtils.isNotEmpty(teamListDto)) {
			for (TeamDTO teamDTO : teamListDto) {
				Team team = new Team(teamDTO.getTeamId(), teamDTO.getTeamName(), teamDTO.getUserCount(),
						businessChatEnabledTeamIds != null
								? businessChatEnabledTeamIds.contains(teamDTO.getTeamId()) ? true : false
								: false);
				teams.add(team);
			}
		}
		return teams;
	}

	@Override
	@Cacheable(cacheNames = Constants.TEAM_CACHE, key = "'TeamId-'.concat(#teamId)", unless = "#result == null")
	public TeamDto validateTeamId(Integer teamId) {
		List<Integer> teamIds = new ArrayList<>();
		teamIds.add(teamId);
		List<TeamDto> teams = businessService.getTeamsByIds(teamIds);
		if (CollectionUtils.isEmpty(teams)) {
			log.warn("No team found for the Id:{}", teamId);
			return null;
		}
		TeamDto teamDto = teams.get(0);
		return teamDto;
	}

	@Override
	public void assignConversationToTeam(TeamDto teamDto, Integer mcId, BusinessDTO businessDTO) {
        assignConversationToTeamWithDBAndESCall(teamDto, mcId, businessDTO, true, null);
	}

	private ConversationAssignmentRequestDTO populateAssignmentRequest(TeamDto teamDto, Integer mcId,boolean callDBAndES,MessengerContact messengerContact) {
		ConversationAssignmentRequestDTO requestDTO = new ConversationAssignmentRequestDTO();
		requestDTO.setDoNotNotify(true);
		requestDTO.setDoNotPublishActivity(true);
		requestDTO.setSaveInDBAndES(callDBAndES);
		requestDTO.setMcId(mcId);
		IdentityDTO identityDTO = new IdentityDTO();
		identityDTO.setId(teamDto.getTeamId());
		identityDTO.setName(teamDto.getName());
		identityDTO.setType("T");
		requestDTO.setTo(identityDTO);
		requestDTO.setMessengerContact(messengerContact);
		return requestDTO;
	}

	private void evictWebchatConfigTeamCache(Integer widgetConfigId) {
		String key = getWebChatConfigTeamRedisKey(widgetConfigId);
		redisHandler.evictPlatformCache(key);
	}

	private String getWebChatConfigTeamRedisKey(Integer webchatConfigId) {
		String fieldName = "Teams:";
		fieldName += webchatConfigId;
		return fieldName;
	}

	@Override
	public void deleteTeamFromWebchat(DeleteTeamEvent deleteTeamEvent) {

		if (deleteTeamEvent != null && deleteTeamEvent.getTeamId() != null) {
			List<Integer> widgetConfigIds = businessChatWidgetConfigService
					.deleteTeamByTeamId(deleteTeamEvent.getTeamId());
			if (CollectionUtils.isNotEmpty(widgetConfigIds)) {
				log.info("Deleting Team for id:{}", deleteTeamEvent.getTeamId());
				List<String> keys = new ArrayList<>();
				for (Integer widgetConfigId : widgetConfigIds) {
					keys.add(getWebChatConfigTeamRedisKey(widgetConfigId));
				}
				redisHandler.evictPlatformCaches(keys);
			}
		}
	}



	private boolean isReceivedDuringBusinessHr(BusinessTimingDTO businessTimingDTO, BusinessDTO businessDTO) {

		Calendar cal = null;
		// if (StringUtils.isNotEmpty(businessTimingDTO.getTimeZone()) &&
		// businessTimingDTO.getTimeZone().equalsIgnoreCase("British Summer Time")) {
		// log.info("isReceivedDuringBusinessHr: Timezone is BST");
		// cal = Calendar.getInstance();
		// cal.add(Calendar.HOUR_OF_DAY, 1);
		// log.info( "isReceivedDuringBusinessHr :{}", cal.getTime());
		// }
		// else {
		// //BIRDEYE-58105 issue regarding timezone, previous similar issue -
		// BIRDEYE-17004
		// String timezone = businessTimingDTO.getTimeZone();
		// if (("Central Standard Time").equalsIgnoreCase(timezone) || ("Central
		// Daylight Time").equalsIgnoreCase(timezone)) {
		// // Central Standard Time is not unique, many Timezone ids use this.
		// // https://docs.oracle.com/middleware/12212/wcs/tag-ref/MISC/TimeZones.html
		// timezone = "CST";
		// }
		TimeZone businessTimeZone = TimeZone.getTimeZone(businessTimingDTO.getTimeZoneId());
		log.info("isReceivedDuringBusinessHr : businessID {} name {}  timezone {} ", businessDTO.getBusinessId(),
				businessDTO.getBusinessName(), businessTimeZone);
		cal = ControllerUtil.convertToBusinessTimeZoneWithDaylight(new Date(), businessTimeZone);
		// }

		log.info(
				"isReceivedDuringBusinessHr : time at which messaged received (after conversion to business timezone) {}",
				cal.getTime());
		// at BirdEye we take Monday as day 0 and sunday as day 6 but in calender api
		// monday is day 2 and sunday as day 0 hence below operation
		int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK) - 2 < 0 ? 6 : cal.get(Calendar.DAY_OF_WEEK) - 2;
		int hourOfDay = cal.get(Calendar.HOUR_OF_DAY);
		int minuteOfHour = cal.get(Calendar.MINUTE);
		log.info("isReceivedDuringBusinessHr : current dayOfWeek {}, hourOfDay {}, minuteOfHour {} ", dayOfWeek,
				hourOfDay, minuteOfHour);

		List<OperationHours> currentDayBusinessHr = new ArrayList<>();
		for (OperationHours businessHour : businessTimingDTO.getCallCentreHours()) {
			if (businessHour.getDay().equals(dayOfWeek)) {
				currentDayBusinessHr.add(businessHour);
			}
		}
		Collections.sort(currentDayBusinessHr, new BusinessHourComparator());
		String webChatMessageReceiveTime = hourOfDay + ":" + minuteOfHour; // HH:mm
		log.info("isReceivedDuringBusinessHr : webChatMessageReceiveTime {} ", webChatMessageReceiveTime);
		log.info("isReceivedDuringBusinessHr : currentDayBusinessHours {} ", currentDayBusinessHr);
		for (OperationHours businessHour : currentDayBusinessHr) {
			if (businessHour.getIsOpen() != 0
					&& ControllerUtil.compareTime(webChatMessageReceiveTime, businessHour.getStartHour()) >= 0
					&& ControllerUtil.compareTime(webChatMessageReceiveTime, businessHour.getEndHour()) <= 0) {
				log.info("isReceivedDuringBusinessHr: businessHr {0}", businessHour);
				return true;
			}
			// handle the case when shop closes on next day
			else if (businessHour.getIsOpen() != 0
					&& ControllerUtil.compareTime(businessHour.getStartHour(), businessHour.getEndHour()) >= 0 &&
					(ControllerUtil.compareTime(webChatMessageReceiveTime, businessHour.getStartHour()) >= 0
							|| ControllerUtil.compareTime(webChatMessageReceiveTime, businessHour.getEndHour()) <= 0)) {
				log.info("isReceivedDuringBusinessHr: businessHr {}", businessHour);
				return true;
			}
		}
		return false;
	}

	@Cacheable(cacheNames = Constants.WEBCHAT_TEAM_CACHE, key = "'WidgetId-'.concat(#widgetConfigId)", unless = "#result == null")
	private List<TeamDto> populateTeams(Integer widgetConfigId) {

		List<Integer> teamIds = businessChatWidgetConfigService.getTeamIdsByWidgetConfigIdOrdered(widgetConfigId);
		if (CollectionUtils.isNotEmpty(teamIds)) {
			List<TeamDto> result = new ArrayList<TeamDto>();
			List<TeamDto> teamDTOS = businessService.getTeamsByIds(teamIds);
			if (CollectionUtils.isNotEmpty(teamDTOS)) {
				Map<Integer, TeamDto> teamsMap = teamDTOS.stream().collect(
						Collectors.toMap(TeamDto::getTeamId, team -> team));
				teamIds.forEach(id -> {
					TeamDto teamDto = teamsMap.get(id);
					if (teamDto != null) {
						result.add(teamDto);
					}
				});
			}
			return result;
		}
		return null;
	}

	@Override
	public void updateInstallationStatus(Pageable pageable) {
		log.info("updateInstallationStatus called with pagerequest : {}", pageable); // get distinct widgetId and
																						// websites from event table
		List<BusinessWebchatEventDetails> businessWebchatEventDetails = businessWebchatEventService
				.findDistinctEventsInLast24Hours(pageable);
		// updating status as installed
		updateBusinessChatWidgetConfigAsInstalled(businessWebchatEventDetails);
	}

	private void updateBusinessChatWidgetConfigAsInstalled(
			List<BusinessWebchatEventDetails> businessWebchatEventDetails) {
		log.info("updateBusinessChatWidgetConfigAsInstalled called with businessWebchatEventDetails : {} ",
				businessWebchatEventDetails);
		if (CollectionUtils.isNotEmpty(businessWebchatEventDetails)) {
			List<Integer> businessWebChatIds = businessWebchatEventDetails.stream()
					.map(BusinessWebchatEventDetails::getWidgetId).collect(Collectors.toList());
			Map<Integer, BusinessChatWidgetConfig> businessWebchatConfigMap = businessChatWidgetConfigService
					.getWebChatConfigMap(businessWebChatIds);
			List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessWebchatEventDetails.stream()
					.map((event) -> {
						BusinessChatWidgetConfig businessChatWidgetConfig = businessWebchatConfigMap
								.get(event.getWidgetId());
						if (Objects.nonNull(businessChatWidgetConfig)) {
							businessChatWidgetConfig.setWebsites(event.getWebsites());
							businessChatWidgetConfig.setInstalled(1);
							businessChatWidgetConfig.setStatusUpdatedOn(new Date());

						}
						return businessChatWidgetConfig;
					}).filter(config -> Objects.nonNull(config)).collect(Collectors.toList());
			businessChatWidgetConfigService.saveWebChatConfig(businessChatWidgetConfigs);
		}
	}

	@Override
	public void requestCrawlerToCheckInstallationStatus(Integer widgetId, List<String> websites, String type,
			KafkaTopicEnum kafkaTopicEnum) {
		WebchatInstallationAudit webchatInstallationAudit = new WebchatInstallationAudit(widgetId,
				WebchatInstallationAuditEnum.INPROGRESS.getValue(), type, new Date(), new Date());
		long startTime = System.currentTimeMillis();
		WebchatInstallationAudit webchatInstallationAudit1 = webchatInstallationAuditRepository
				.save(webchatInstallationAudit);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("WebchatInstallationAudit", startTime, endTime);
		WebchatInstallationCrawlerRequest webchatInstallationCrawlerRequest = new WebchatInstallationCrawlerRequest(
				webchatInstallationAudit1.getId(), widgetId, websites, type);
		kafkaService.publishToKafkaAsync(kafkaTopicEnum, webchatInstallationCrawlerRequest);
	}

	@Override
	public RefreshInstallationStatusResponse refreshWidgetStatus(Integer widgetId) {
		// this needs to be cached
		Optional<BusinessChatWidgetConfig> widgetConfig = businessChatWidgetConfigRepository.findById(widgetId);
		if (!widgetConfig.isPresent()) {
			log.info("Invalid WidgetId: {} ", widgetId);
			return null;
		}
		BusinessWebchatEventDetails businessWebchatEventDetails = businessWebchatEventService
				.findDistinctEventsInLast24HoursByWidgetId(widgetId);
		if (StringUtils.isNotEmpty(businessWebchatEventDetails.getWebsites())) {
			String websitesStr = businessWebchatEventDetails.getWebsites();
			List<String> websites = Stream.of(websitesStr.split(",", -1))
					.collect(Collectors.toList());
			widgetConfig.get().setWebsites(websitesStr);
			widgetConfig.get().setInstalled(1);
			widgetConfig.get().setStatusUpdatedOn(new Date());
			businessChatWidgetConfigRepository.save(widgetConfig.get());
			return new RefreshInstallationStatusResponse(true, websites, false, new Date());
		} else {
			// Call Crawler Service
			List<String> websitesByWidgetId = businessWebchatEventService.findWebsitesByWidgetId(widgetId);
			if (CollectionUtils.isNotEmpty(websitesByWidgetId)) {
				String topicName = StringUtils.join(Constants.WEBCHAT_INSTALLATION_STATUS
						+ "/", String.valueOf(widgetId));
				nexusService.deleteFromFireBaseDB(topicName);
				requestCrawlerToCheckInstallationStatus(widgetId, websitesByWidgetId, Constants.CrawlerEvent.API,
						KafkaTopicEnum.WEBCHAT_INSTALLATION_REQUEST_API);
				log.info("Requested crawler to check the status for the widget id:{}", widgetId);
				return new RefreshInstallationStatusResponse(null, null, true, null);
			} else {
				widgetConfig.get().setInstalled(0);
				widgetConfig.get().setWebsites(null);
				widgetConfig.get().setStatusUpdatedOn(new Date());
				businessChatWidgetConfigRepository.save(widgetConfig.get());
				return new RefreshInstallationStatusResponse(false, null, false, new Date());
			}
		}
	}

	@Override
	@Transactional
	public BusinessWebchatWidgetDetails saveWidgetConfig(MultiLocationWidgetRequest multiLocationWidgetRequest) {
		commonService.checkUserAccountAccess(multiLocationWidgetRequest.getUserId(),multiLocationWidgetRequest.getAccountId());
		BusinessDTO businessDTO = businessService.getBusinessDTO(multiLocationWidgetRequest.getAccountId());
		if (businessDTO != null) {
			BusinessChatWidgetConfig businessChatWidgetConfig = null;
			if (multiLocationWidgetRequest.getCloneId() != null) {
				businessChatWidgetConfig = businessChatWidgetConfigService
						.getWebChatConfigByWidgetId(multiLocationWidgetRequest.getCloneId());
				if (businessChatWidgetConfig != null) {
					businessChatWidgetConfig.setInstalled(null);
					businessChatWidgetConfig.setWebsites(null);

				} else {
					log.error("Empty  business chat widget config for id :{}", multiLocationWidgetRequest.getCloneId());
					throw new MessengerException(ErrorCode.ERROR_WHILE_CLONING_CHAT_CONFIG);
				}
			} else {
				businessChatWidgetConfig = businessChatWidgetConfigService.getDefaultBusinessChatWidgetConfig();
			}

			BusinessChatWidgetConfig businessChatWidgetConfigNew = null;
			try {
				businessChatWidgetConfigNew = (BusinessChatWidgetConfig) businessChatWidgetConfig.clone();
			} catch (Exception e) {
				log.error("Exception while cloning business chat widget config:{}", e);
				throw new MessengerException(ErrorCode.ERROR_WHILE_CLONING_CHAT_CONFIG);
			}
			businessChatWidgetConfigNew.setId(null);
			businessChatWidgetConfigNew.setBusinessId(businessDTO.getBusinessNumber());
			businessChatWidgetConfigNew.setWidgetName(businessChatWidgetConfigService.getWidgetName(
					multiLocationWidgetRequest.getWidgetName(), businessDTO.getBusinessNumber(), null, null));
			businessChatWidgetConfigNew.setAutoReplyTxt(
					replaceTextWithBusinessNamePrePopulated(businessChatWidgetConfig.getAutoReplyTxt(), businessDTO));
			businessChatWidgetConfigNew.setReplyTextPostBusinessHr(replaceTextWithBusinessNamePrePopulated(
					businessChatWidgetConfig.getReplyTextPostBusinessHr(), businessDTO));
			businessChatWidgetConfigNew.setWebChatOnlineClosingMessageHeader(
					businessChatWidgetConfig.getWebChatOnlineClosingMessageHeader());
			businessChatWidgetConfigNew.setWebChatOnlineClosingMessageBody(replaceTextWithBusinessNamePrePopulated(
					businessChatWidgetConfig.getWebChatOnlineClosingMessageBody(), businessDTO));
			businessChatWidgetConfigNew.setHeaderHeadline(
					replaceTextWithBusinessNamePrePopulated(businessChatWidgetConfig.getHeaderHeadline(), businessDTO));
			businessChatWidgetConfigNew
					.setOfflineClosingMessageBody(businessChatWidgetConfig.getOfflineClosingMessageBody());
			businessChatWidgetConfigNew
					.setOfflineClosingMessageHeader(businessChatWidgetConfig.getOfflineClosingMessageHeader());
			businessChatWidgetConfigNew.setUpdatedBy(String.valueOf(multiLocationWidgetRequest.getUserId()));
			businessChatWidgetConfigNew.setUpdatedAt(new Date());
			businessChatWidgetConfigNew.setCreatedAt(new Date());
			businessChatWidgetConfigNew.setStatusUpdatedOn(new Date());
			BusinessOptionResponse businessOptions = businessService
					.getBusinessOptionsConfig(businessDTO.getBusinessId(), true);
			String defaultDisclaimer = String.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
					.getProperty("default_global_webchat_disclamier",
							"By sending this message, you expressly consent to receive communications from us. You may opt out at any time."));
			businessChatWidgetConfigNew.setDisclaimer(defaultDisclaimer);
			if (businessOptions.getChatbot() != null && businessOptions.getChatbot() == 1) {
				businessChatWidgetConfigNew.setChatbotEnabled(1);
			}
			businessChatWidgetConfigNew.setWidgetCloneId(multiLocationWidgetRequest.getCloneId());
			businessChatWidgetConfigNew.setAllLocationDisabled(0);
			businessChatWidgetConfigNew.setMobileView(businessChatWidgetConfig.getMobileView());
			try {
				BusinessChatWidgetConfig businessChatWidgetConfig1 = businessChatWidgetConfigRepository
						.saveAndFlush(businessChatWidgetConfigNew);

				createDefaultLiveChatMessageConfig(businessChatWidgetConfig1, multiLocationWidgetRequest.getCloneId());
				if (multiLocationWidgetRequest.getCloneId() != null) {
					List<Integer> teamIds = businessChatWidgetConfigService
							.getTeamIdsByWidgetConfigId(multiLocationWidgetRequest.getCloneId());
					List<BusinessChatEnabledTeam> businessChatEnabledTeams = teamIds.stream()
							.collect(Collectors.mapping(
									id -> new BusinessChatEnabledTeam(id, businessChatWidgetConfig1.getId(),
											String.valueOf(multiLocationWidgetRequest.getUserId())),
									Collectors.toList()));
					businessChatEnabledTeamRepository.saveAll(businessChatEnabledTeams);
					List<BusinessChatWidgetUserProfile> businessChatWidgetUserProfiles = businessChatWidgetConfigService
							.getUserProfileByChatWidgetId(multiLocationWidgetRequest.getCloneId());
					List<BusinessChatWidgetUserProfile> businessChatWidgetUserProfiles1 = businessChatWidgetUserProfiles
							.stream().collect(Collectors.mapping(bwcup -> new BusinessChatWidgetUserProfile(
									bwcup.getUserProfileName(), bwcup.getUserProfileImage(),
									businessChatWidgetConfig1.getId(), businessChatWidgetConfig1,
									String.valueOf(multiLocationWidgetRequest.getUserId())), Collectors.toList()));
					businessChatWidgetUserProfileRepository.saveAll(businessChatWidgetUserProfiles1);
				}
				if (CollectionUtils.isNotEmpty(multiLocationWidgetRequest.getLocationIds())) {
					List<BusinessChatEnabledLocation> businessChatEnabledLocations = multiLocationWidgetRequest
							.getLocationIds().stream()
							.collect(Collectors.mapping(locId -> new BusinessChatEnabledLocation(
									locId, businessChatWidgetConfig1.getId(),
									String.valueOf(multiLocationWidgetRequest.getUserId())), Collectors.toList()));
					businessChatEnabledLocationRepository.saveAll(businessChatEnabledLocations);
				}
				LocationHierarchy locationHierarchy = multiLocationWidgetRequest.getLocationHierarchy();
				if (locationHierarchy != null) {
					List<BusinessChatLocationHierarchy> businessChatLocationHierarchies = locationHierarchy
							.getSubLevels().stream()
							.collect(Collectors.mapping(sublevel -> new BusinessChatLocationHierarchy(
									locationHierarchy.getLevelId(), sublevel, businessChatWidgetConfig1.getId(),
									String.valueOf(multiLocationWidgetRequest.getUserId())), Collectors.toList()));
					businessChatLocationHierarchyRepository.saveAll(businessChatLocationHierarchies);
				}
				// Need to create a trigger to update Id in some other column.As of now doing it
				// with the application code.Extra DB hit :(
				businessChatWidgetConfig1.setExternalId(Long.valueOf(businessChatWidgetConfig1.getId()));
				businessChatWidgetConfigRepository.saveAndFlush(businessChatWidgetConfig1);
				log.info("Multi location webchat for business Id:{} is created successfully",
						multiLocationWidgetRequest.getAccountId());
				return new BusinessWebchatWidgetDetails(businessChatWidgetConfig1.getId(),
						businessChatWidgetConfig1.getWidgetName(), businessChatWidgetConfig1.getEnabled(),
						businessDTO.getBusinessNumber(), businessDTO.getBusinessName(), businessDTO.getBusinessId(),
						businessDTO.getBusinessAlias(), businessChatWidgetConfig1.getInstalled(), "Not Installed",
						Long.valueOf(businessChatWidgetConfig1.getId()));
			} catch (Exception e) {
				log.error("Multi location webchat for business Id:{} fialed:{}",
						multiLocationWidgetRequest.getAccountId(), e);
				return null;
			}
		}
		return null;
	}

	@Override
	public void updateWidgetEmailMandatory(UpdateEmailMandatoryRequest emailMandatoryRequest, boolean emailMandatory) {
		if (emailMandatoryRequest != null && CollectionUtils.isNotEmpty(emailMandatoryRequest.getWidgetIds())) {
			List<BusinessChatWidgetConfig> businessChatWidgetConfigs = businessChatWidgetConfigService
					.getWebChatConfigByWidgetIds(emailMandatoryRequest.getWidgetIds());
			if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)) {
				businessChatWidgetConfigs.forEach(businessChatWidgetConfig -> {
					businessChatWidgetConfig.setEmailMandatory(emailMandatory ? 1 : 0);
				});
				businessChatWidgetConfigRepository.saveAll(businessChatWidgetConfigs);
				businessChatWidgetConfigs.forEach(businessChatWidgetConfig -> {
					evictWebchatCache(businessChatWidgetConfig.getBusinessId(), businessChatWidgetConfig.getId());
				});
			}
		}
	}

	@Override
	public void createWebchatCustomLocationName(WebchatCustomLocationNameRequest request) {
		Integer accountId = request.getAccountId();
		Set<Integer> bizIds = request.getWidgetCustomLocationNames().keySet();
		validateRequest(request);
		List<WebchatCustomLocationName> customLocationNames = new ArrayList<WebchatCustomLocationName>();
		List<WebchatCustomLocationName> webchatCustomLocationNames = new ArrayList<WebchatCustomLocationName>();
		if (CollectionUtils.isNotEmpty(bizIds)) {
			webchatCustomLocationNames = webchatCustomLocationNameRepository.getByBusinessIds(bizIds);
		}
		Map<Integer, WebchatCustomLocationName> webchatCustomLocationNameMap = webchatCustomLocationNames.stream()
				.collect(Collectors.toMap(WebchatCustomLocationName::getBusinessId, v -> v));
		request.getWidgetCustomLocationNames().forEach((key, value) -> {
			WebchatCustomLocationName customLocationName = new WebchatCustomLocationName(accountId, key,
					value.getPrimaryName(), value.getSecondaryName());
			if (webchatCustomLocationNameMap.containsKey(key)) {
				customLocationName.setId(webchatCustomLocationNameMap.get(key).getId());
			}
			customLocationNames.add(customLocationName);
		});
		webchatCustomLocationNameRepository.saveAll(customLocationNames);
		List<BusinessChatWidgetConfig> accountWidgets = businessChatWidgetConfigService.getAllWidgets();
		if (CollectionUtils.isNotEmpty(accountWidgets)) {
			List<Integer> widgetIds = accountWidgets.stream().map(w -> w.getId()).collect(Collectors.toList());
			evictWidgetLocationCache(widgetIds);
		}
	}

	private void evictWidgetLocationCache(List<Integer> widgetIds) {
		List<String> keys = new ArrayList<>();
		widgetIds.forEach(widgetId -> {
			keys.add(Constants.LOCATION_CACHE + "::webchat-" + widgetId);
			keys.add(Constants.LOCATION_CACHE + "::webchatInternal-" + widgetId);
		});
		redisHandler.evictWebChatCache(keys);
	}

	private void validateRequest(WebchatCustomLocationNameRequest request) {
		Map<Integer, WidgetCustomLocationName> map = request.getWidgetCustomLocationNames();
		Integer accountId = request.getAccountId();
		if (MapUtils.isEmpty(map)) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CUSTOM_LOCATION_NAME_REQUEST,
           		 ComponentCodeEnum.WEBCHAT, HttpStatus.BAD_REQUEST)
           		 .message("Invalid custom location name request : accountId {}, primaryName is required", accountId));
		}
		map.values().forEach(locationName -> {
			if (StringUtils.isBlank(locationName.getPrimaryName())
					&& StringUtils.isNotBlank(locationName.getSecondaryName())) {
				throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.WEBCHAT_CUSTOM_LOCATION_NAME_PRIMARY_NAME_MANDATORY,
						ComponentCodeEnum.WEBCHAT, HttpStatus.BAD_REQUEST)
						.message("Invalid custom location name request : accountId {}, primaryName is required", accountId));
			}
		});
		Map<Integer, BizLite> bizMap = businessService.getBizLite(new ArrayList<Integer>(map.keySet()),
				request.getAccountId());
		bizMap.values().forEach(bizLite -> {
			if (!accountId.equals(bizLite.getEnterpriseId())) {
				throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.WEBCHAT_CUSTOM_LOCATION_NAME_INVALID_BUSINESSID,
	            		ComponentCodeEnum.WEBCHAT, HttpStatus.BAD_REQUEST)
	            		.message("Invalid custom location name request : accountId {},inavalid businessId", accountId));

			}
		});
	}

	@Override
	@Transactional
	public void addWebchatCustomFields(WidgetCustomFieldRequest request) {
		List<CustomFieldDto> customFieldDtos = contactService.getCustomFields(request.getAccountId());
		Optional<BusinessChatWidgetConfig> businessChatWidgetConfig = businessChatWidgetConfigRepository
				.findById(request.getWidgetId());

		if (businessChatWidgetConfig.isPresent()) {
			List<CustomFieldsWidget> customFieldsWidgets = RequestDtoValidator.validateAndBuildCustomFieldList(request,
					customFieldDtos, businessChatWidgetConfig.get());
			// delete existing custom fields
			customFieldService.deleteWebchatCustomFields(request.getWidgetId(),
					businessChatWidgetConfig.get().getBusinessId());
			// evicting cache
			evictWebchatCache(businessChatWidgetConfig.get().getBusinessId(), request.getWidgetId());
			customFieldWidgetRepository.saveAll(customFieldsWidgets);
			businessChatWidgetConfig.get().setCustomFieldEnabled(true);
			businessChatWidgetConfigRepository.save(businessChatWidgetConfig.get());
			log.info("Successfully added custom fields for account id - {}", request.getAccountId());
		}
		log.info("There is no custom field from kontacto or no widget data for account id - {} and widget id - {}",
				request.getAccountId(), request.getWidgetId());
	}

	@Override
	public Boolean isCustomFieldExist(Integer fieldId) {
		log.info("finding custom field by field id - {}", fieldId);
		List<CustomFieldsWidget> customFieldsWidgets = customFieldWidgetRepository.findByfieldId(fieldId);
		if (CollectionUtils.isNotEmpty(customFieldsWidgets)) {
			log.info("custom field exist in messenger for field id -{}", fieldId);
			return true;
		}
		log.info("custom field does not exist in messenger for field id -{}", fieldId);
		return false;
	}

	@Override
	public List<BusinessChatWidgetConfig> getBusinessChatWidgetConfigForBizApp(BizAppChatWidgetConfigRequest request) {
		List<BusinessChatWidgetConfig> businessChatWidgetConfigs = new ArrayList<BusinessChatWidgetConfig>();
		if (request.getLivechatEnabled() != null) {
			businessChatWidgetConfigs = businessChatWidgetConfigRepository
					.getWebChatConfigByBusinessIdForLiveChatEnabled(request.getBusinessIds(), request.getEnabled(),
							request.getLivechatEnabled());
		} else if (request.getChatBotEnabled() != null) {
			businessChatWidgetConfigs = businessChatWidgetConfigRepository
					.getWebChatConfigByBusinessIdForChatBotEnabled(request.getBusinessIds(), request.getEnabled(),
							request.getChatBotEnabled());
		} else if (request.getInstalled() != null) {
			businessChatWidgetConfigs = businessChatWidgetConfigRepository.getWebChatConfigByBusinessIdForInstalled(
					request.getBusinessIds(), request.getEnabled(), request.getInstalled());
		}
		return businessChatWidgetConfigs;
	}

	@Override
	public WebChatConfigForExternalIdResponse getBusinessChatWidgetConfigByExternalId(Long externalId) {
		List<WebChatConfigForExternalIdResponse> response = businessChatWidgetConfigRepository
				.getWebChatConfigByExternalId(externalId);
		if (CollectionUtils.isNotEmpty(response)) {
			return response.get(0);
		}
		return null;
	}

	@Override
	public void getAndUpdateWidgetInstallationStatus() {
		int limit = Constants.GET_BUSINESS_CHAT_EVENT_LIMIT;
		Integer count = businessWebchatEventService.findDistinctEventsCountInLast24Hours();
		int pages = (count % limit == 0) ? (count / limit) : (count / limit) + 1;
		log.info("getAndUpdateWidgetInstallationStatus found count : {} and pages : {}", count, pages);
		for (int i = 0; i < pages; i++) {
			PageRequest pageRequest = PageRequest.of(i, limit);
			updateInstallationStatus(pageRequest);
		}

		// get the widgets whose status is not updated in last 24 hours
		List<Integer> widgetIds = businessChatWidgetConfigService.findWidgetsByStatusUpdatedOn();
		log.info("widgetids found : {}", widgetIds);
		if (CollectionUtils.isNotEmpty(widgetIds)) {
			for (Integer widgetId : widgetIds) {
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.UPDATE_INSTALLATION_STATUS, widgetId);
			}
		}
	}

	@Override
	public void webChatWidgetTeamsOrdering(WebchatWidgetTeamsOrderingRequest request) {
		if (CollectionUtils.isNotEmpty(request.getWidgetTeamsOrdering())) {
			List<WidgetTeamsOrdering> widgetTeamsOrdering = request.getWidgetTeamsOrdering();
			validateTeamsOrderingRequest(widgetTeamsOrdering);
			Map<Integer, Integer> teamsOrderMap = widgetTeamsOrdering.stream().collect(HashMap::new,
					(m, v) -> m.put(v.getTeamId(), v.getOrder()), HashMap::putAll);
			Set<Integer> teamIds = teamsOrderMap.keySet();
			List<BusinessChatEnabledTeam> businessChatEnabledTeams = businessChatEnabledTeamRepository
					.findByWidgetConfigIdByTeamIds(teamIds, request.getWidgetId());
			businessChatEnabledTeams.forEach(entry -> {
				entry.setSortOrder(teamsOrderMap.get(entry.getTeamId()));
			});
			businessChatEnabledTeamRepository.saveAll(businessChatEnabledTeams);
		}
	}

	private void validateTeamsOrderingRequest(List<WidgetTeamsOrdering> widgetTeamsOrdering) {
		widgetTeamsOrdering.forEach(item -> {
			if (item.getOrder() != null && item.getOrder() <= 0) {
				throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.ERROR_TEAM_ORDER, HttpStatus.BAD_REQUEST));
			}
		});
	}
	private BusinessDTO getBusinessDto(Long  businessNumber) {
		BusinessDTO businessDto = null;
		try {
			businessDto = businessService.getBusinessLiteDTOWithLocation(Constants.BUSINESS_NUMBER,businessNumber.toString());
		}catch (Exception e){
			log.error("Exception while getting business from core business Number:{} ",businessNumber,e);
		}
		if (businessDto == null) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_SEND_SMS_BUSINESS_NO,
					ComponentCodeEnum.WEBCHAT, HttpStatus.BAD_REQUEST));
		}
		String countryCode=(Objects.nonNull(businessDto.getLocation()) && StringUtils.isNotBlank(businessDto.getLocation().getCountryCode()))?businessDto.getLocation().getCountryCode():null;
		businessDto.setPhoneNumber(businessDto.getPhone());
		businessDto.setCountryCode(countryCode);
		return businessDto;
	}

    @Override
    public void assignConversationToTeamWithDBAndESCall(TeamDto teamDto, Integer mcId, BusinessDTO businessDTO,
            boolean callDBAndES, MessengerContact messengerContact) {
        ConversationAssignmentRequestDTO requestDTO = populateAssignmentRequest(teamDto, mcId, callDBAndES,messengerContact);

        assignmentService.assignConversation(requestDTO, null, businessDTO.getRoutingId());        
    }

	@Override
	public BusinessWebChatWidgetConfiguration getWebsiteWebchatConfigNew(String businessId, String apiKey,boolean isMicroSite,String activationStatus, String version) throws Exception{
		log.info("Web chat widget configuration for businessId :: {} and apiKey :: {} ,isMicrosite::{}, activationStatus :: {}, version :: {}", businessId,apiKey,isMicroSite,activationStatus,version);
		BusinessAPIKeyMessage businessAPIKeyMessage = null;
		Long externalId = null;
		if (StringUtils.isNotBlank(version) && version.equals("v2")) {
			if (!ControllerUtil.isBusinessIdValid(businessId)) {
				throw new NotFoundException(ErrorCode.INVALID_BUSINESS_ID);
			}

			Long businessNumber = null;
			if (businessId.indexOf("_") == -1) {
				businessNumber = Long.parseLong(businessId);
			} else if ("1".equals(businessId.substring(businessId.indexOf("_") + 1))) {
				businessNumber = Long.parseLong(businessId.substring(0, businessId.indexOf("_")));
			}
			externalId = businessNumber;
			if (!isMicroSite){
				WebChatConfigForExternalIdResponse businessChatWidgetConfig =  getConfigByExternalId(businessNumber,activationStatus);
				if(businessChatWidgetConfig == null){
					log.info("Widget not found for externalId:{}",businessNumber);
					return null;
				}
				if (businessChatWidgetConfig.getExternalId().equals(Long.valueOf(businessChatWidgetConfig.getId()))){
					log.info("condition matched!!");
					businessNumber = businessChatWidgetConfig.getBusinessId();
				}
			}

			try{
				commonService.validateWidgetApiKey(businessNumber,apiKey);
			}catch(Exception e){
				log.error("Getting exception in validateWidgetApiKey {}", e);
				return null;
			}
			BusinessDTO business = businessService.getBusinessByBusinessNumber(businessNumber);
			businessAPIKeyMessage = commonService.prepareBusinessAPIKeyMessage(business);
		}
		else {
			externalId = Long.valueOf(businessId);
			if (!isMicroSite){
				WebChatConfigForExternalIdResponse businessChatWidgetConfig =  getConfigByExternalId(Long.parseLong(businessId),activationStatus);
				if(businessChatWidgetConfig == null){
					log.info("Widget not found for externalId:{}",businessId);
					return null;
				}
				if (businessChatWidgetConfig.getExternalId().equals(Long.valueOf(businessChatWidgetConfig.getId()))){
					businessId = businessChatWidgetConfig.getBusinessId().toString();
				}
			}
			businessAPIKeyMessage = commonService.validateApiKey(businessId.toString(), apiKey);
		}

		BusinessWebChatWidgetConfiguration businessWebChatWidgetConfiguration = null;
		BizLite business1 = validateBusinessById(businessAPIKeyMessage.getId());
		//condition to check location is active and not closed
		WebChatWidgetDefaultConfiguration superConfig = null;
		if (isBusinesssActive(business1)){
				superConfig = getWebsiteWebchatConfig(externalId,isMicroSite);
			if (superConfig != null){
				log.info("superConfig :{}",superConfig);
				businessWebChatWidgetConfiguration = new BusinessWebChatWidgetConfiguration();
				copyProperties(businessId, businessWebChatWidgetConfiguration, superConfig);
				log.info("businessWebChatWidgetConfiguration :{}",businessWebChatWidgetConfiguration);
				businessWebChatWidgetConfiguration.setAccountType(BusinessAccountTypeEnum.getValue(businessAPIKeyMessage.getAccountType()));
				log.info("businessWebChatWidgetConfiguration1 :{}",businessWebChatWidgetConfiguration);
				// Since we don't need to send domain to externals
				businessWebChatWidgetConfiguration.setBusinessDomain(null);
				log.info("businessWebChatWidgetConfiguration2 :{}",businessWebChatWidgetConfiguration);


			}
		}
		// set livechat timeout from parameters table
		if(businessWebChatWidgetConfiguration!=null && !isFreeProfile(activationStatus)){
			businessWebChatWidgetConfiguration.setLivechatTimeout(Integer.valueOf(CacheManager.getInstance()
					.getCache(SystemPropertiesCache.class).getProperty("livechat_timeout",
							"60")));
			businessWebChatWidgetConfiguration = checkProductFeatureFlags(businessWebChatWidgetConfiguration, business1, isMicroSite);
		}
		checkAndUpdateWidgetLocale(businessWebChatWidgetConfiguration);
		return businessWebChatWidgetConfiguration;
	}

	public WebChatConfigForExternalIdResponse getConfigByExternalId(Long externalId,String activationStatus) throws Exception{
		WebChatConfigForExternalIdResponse response = null;
		if( activationStatus == "free") {
//			BusinessChatWidgetConfig businessChatWidgetConfig = null;
//			List<BusinessChatWidgetConfig> businessChatWidgetConfigs = em
//					.createNamedQuery("BusinessChatWidgetConfig.findByExternalId", BusinessChatWidgetConfig.class)
//					.setParameter("externalId",externalId).getResultList();
//			if (CollectionUtils.isNotEmpty(businessChatWidgetConfigs)){
//				businessChatWidgetConfig = businessChatWidgetConfigs.get(0);
//				response.setId(businessChatWidgetConfig.getId());
//				response.setBusinessId(businessChatWidgetConfig.getBusinessId());
//				response.setExternalId(businessChatWidgetConfig.getExternalId());
//			}
//			if(response != null) {
//				log.info("Get Widget Config By External Id For Free Path:: {} ", response);
//			}
		}else {
			response = getBusinessChatWidgetConfigByExternalId(externalId);
		}
		if(response != null) {
			log.info("Get Widget Config By External Id: {}, BusinessId: {}, Id: {}", response.getExternalId(),response.getBusinessId(),response.getId());
			return response;
		}
		return null;
	}

	private boolean isBusinesssActive(BizLite business){
		return  !business.getActivationStatus().equalsIgnoreCase(BusinessDTO.SUSPENDED) && !business.getActivationStatus().equalsIgnoreCase(BusinessDTO.CANCELLED) && business.getClosed() == 0;
	}

	private BizLite validateBusinessById(Integer businessId)throws Exception{
		BizLite business=null;
		Map<Integer, BizLite> businessDataForMessenger = businessService.getBizLite(Collections.singletonList(businessId), null);
		if (Objects.nonNull(businessDataForMessenger)) {
			business= businessDataForMessenger.get(businessId);
		}
		if (Objects.isNull(business)) {
			throw new MessengerException(ErrorCode.INVALID_BUSINESS_ID);
		}
		return business;
	}

	private void copyProperties(String businessId, BusinessWebChatWidgetConfiguration dest,
								WebChatWidgetDefaultConfiguration origin) {
		IntegerConverter converter = new IntegerConverter(null);
		DateConverter converter1 = new DateConverter(null);
		BeanUtilsBean beanUtilsBean = new BeanUtilsBean();
		beanUtilsBean.getConvertUtils().register(converter1,Date.class);
		beanUtilsBean.getConvertUtils().register(converter,Integer.class);
		try {
			beanUtilsBean.copyProperties(dest, origin);
		} catch (Exception exe) {
			log.info("Error {0} while copying the property for businessId {}",businessId);
		}
	}

	private boolean isFreeProfile(String activationStatus) {
		return StringUtils.isNotEmpty(activationStatus) && activationStatus.equalsIgnoreCase(BusinessDTO.FREE);
	}

	private BusinessWebChatWidgetConfiguration checkProductFeatureFlags(BusinessWebChatWidgetConfiguration bwcwc,
																		BizLite business, boolean isMicroSite) {
		Integer livechatFeature = null;
		Integer chatbotFeature = null;
		Integer webchatFeature = null;
		BusinessOptionResponse businessOptions = businessService.getBusinessOptionsConfig(business.getAccountId(), true);
		if (businessOptions!= null){
			livechatFeature = businessOptions.getLiveChat();
			chatbotFeature = businessOptions.getChatbot();
			webchatFeature = businessOptions.getEnableChatWidget();
		}

		if(!isMicroSite && (webchatFeature==null || webchatFeature==0)){
			return null;
		}

		if(livechatFeature==null || livechatFeature==0){
			bwcwc.setIsLiveChatEnabled(false);
		}

		if(chatbotFeature==null || chatbotFeature==0){
			bwcwc.setIsChatbotEnabled(false);
		}
		return bwcwc;
	}

	private void checkAndUpdateWidgetLocale(BusinessWebChatWidgetConfiguration businessWebChatWidgetConfiguration){
		try{
			String customLanguageEnabledWidgets=CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("custom_language_enabled_widgets","261968-fr");
			List<String> customLanguageEnabledWidgetsList=ControllerUtil.getTokensListFromString(customLanguageEnabledWidgets);
			String supportedLanguages = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("custom_languages_for_widget","fr,sp");
			List<String> supportedLanguagesList=ControllerUtil.getTokensListFromString(supportedLanguages);
			if (CollectionUtils.isNotEmpty(customLanguageEnabledWidgetsList) &&
					Objects.nonNull(businessWebChatWidgetConfiguration) &&
					Objects.nonNull(businessWebChatWidgetConfiguration.getWidgetConfigId()) &&
					CollectionUtils.isNotEmpty(supportedLanguagesList)) {

				String widgetId = businessWebChatWidgetConfiguration.getWidgetConfigId().toString();

				for (String language : supportedLanguagesList) {
					String widgetWithLanguage = widgetId + "-" + language;
					if (customLanguageEnabledWidgetsList.contains(widgetWithLanguage)) {
						businessWebChatWidgetConfiguration.setLocale(language);
						break;
					}
				}
			}
		}catch(Exception e){
			log.info("exception in checking for custom language widget: {}",e);
		}
	}

	private void saveRobinActiveHours(BusinessWebChatConfigurationRequest businessWebChatConfigurationRequest) {
		BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(businessWebChatConfigurationRequest.getBusinessId());

		List<Integer> removeRobinHoursBusinessId = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(businessWebChatConfigurationRequest.getRemoveRobinInsideBusinessHours())) {
			removeRobinHoursBusinessId.addAll(businessWebChatConfigurationRequest.getRemoveRobinInsideBusinessHours());
		}
		if (CollectionUtils.isNotEmpty(businessWebChatConfigurationRequest.getRemoveRobinOutsideBusinessHours())) {
			removeRobinHoursBusinessId.addAll(businessWebChatConfigurationRequest.getRemoveRobinOutsideBusinessHours());
		}
		if (CollectionUtils.isNotEmpty(removeRobinHoursBusinessId)) {
			robinActiveHoursService.deleteRobinActiveHourConfigByBusinessId(removeRobinHoursBusinessId);
			removeRobinHoursBusinessId.clear();
		}

		List<RobinActiveHoursDto> robinActiveHoursDtoList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(businessWebChatConfigurationRequest.getAddRobinInsideBusinessHours())) {
			for (Integer businessId : businessWebChatConfigurationRequest.getAddRobinInsideBusinessHours()) {
				RobinActiveHoursDto robinActiveHoursDto = new RobinActiveHoursDto(businessDTO.getAccountId(), businessId, businessWebChatConfigurationRequest.getWidgetConfigId(), 1, 0);
				robinActiveHoursDtoList.add(robinActiveHoursDto);
			}
		}
		if (CollectionUtils.isNotEmpty(businessWebChatConfigurationRequest.getAddRobinOutsideBusinessHours())) {
			for (Integer businessId : businessWebChatConfigurationRequest.getAddRobinOutsideBusinessHours()) {
				RobinActiveHoursDto robinActiveHoursDto = new RobinActiveHoursDto(businessDTO.getAccountId(), businessId, businessWebChatConfigurationRequest.getWidgetConfigId(), 0, 1);
				robinActiveHoursDtoList.add(robinActiveHoursDto);
			}
		}
		if (CollectionUtils.isNotEmpty(robinActiveHoursDtoList)) {
			robinActiveHoursService.saveAllRobinActiveHourConfig(robinActiveHoursDtoList);
			robinActiveHoursDtoList.clear();
		}
	}
	@Override
	public Boolean isReceivedDuringBusinessHours(Long businessNumber){
		BusinessDTO businessDto = getBusinessDto(businessNumber);
		Boolean isReceivedDuringBusinessHours=null;
		BusinessTimingDTO businessTimingDTO = businessService.getBusinessTimings(businessDto.getBusinessId(),false);
		if (businessTimingDTO != null){
			isReceivedDuringBusinessHours = BusinessHoursUtility.isReceivedDuringBusinessHr(businessTimingDTO,new Date());
		}
		return isReceivedDuringBusinessHours;
	}

	private void saveChatBotCustomHours(BusinessWebChatConfigurationRequest request){
      if(Objects.nonNull(request) && CollectionUtils.isNotEmpty(request.getChatbotHours())){
		  BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(request.getBusinessId());
		  robinActiveHoursService.processChatBotHours(request,businessDTO);
	  }
	}

	private void removeChatBotCustomHours(BusinessWebChatConfigurationRequest request){
		if(Objects.nonNull(request) && CollectionUtils.isNotEmpty(request.getChatbotHours())){
			BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(request.getBusinessId());
			robinActiveHoursService.removeChatBotHours(request,businessDTO);
		}
	}
}
