package com.birdeye.messenger.service.impl;

import java.util.Objects;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CampaignEventHandler extends SMSReceiveEventHandler {

    private final MessengerEvent EVENT = MessengerEvent.CAMPAIGN_SMS;

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if(Objects.isNull(messageDTO.getMessageTag())) {
            messageDTO.setMessageTag(MessageTag.CAMPAIGN);
        }
        return messageDTO.getMessageTag();
    }

    @Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		if (Objects.isNull(lastMessageMetaData.getLastReceivedMessageSource())) {
			Integer lastReceivedMessageSource = messengerContactService.getLastReceivedMessageSource(messengerContact,
					businessDTO.getRoutingId());
			lastMessageMetaData.setLastReceivedMessageSource(lastReceivedMessageSource);
		}
		if (Objects.isNull(lastMessageMetaData.getLastMessageSource())) {
			lastMessageMetaData.setLastMessageSource(
					messengerContactService.getLastMessageSource(messengerContact.getId(), businessDTO.getRoutingId()));
		}
		lastMessageMetaData.setLastMessageChannel(MessageDocument.Channel.CAMPAIGN.name());
		messengerContact.setLastMsgOn(smsMessageDTO.getSmsDTO().getSentOn());
		messengerContact.setUpdatedAt(smsMessageDTO.getSmsDTO().getSentOn());
	}

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

}
