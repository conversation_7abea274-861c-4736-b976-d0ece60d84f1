package com.birdeye.messenger.service.impl;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.service.FacebookMessageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class FacebookMessageServiceImpl implements FacebookMessageService {

	private final FacebookMessageRepository facebookMessageRepository;

	@Override
	public FacebookMessage getFacebookMessageUsingMessageId(Integer messageId) {
		return facebookMessageRepository.findById(messageId)
				.orElseThrow(() -> new IllegalArgumentException("Invalid Facebook Message Id:" + messageId));
	}

	@Override
	@Transactional
    public void deleteFacebookMessagesByMcId(Integer mcId){
	      try {
              facebookMessageRepository.deleteByCustomerId(mcId);
          } catch (Exception e) {
              log.error("error : {} occurred in deleteFacebookMessagesByMcId", e.getMessage());
          }
      }

	@Override
	@Transactional
	public void updateFacebookMessageBody(String messageBody,Integer messageId){
		try {
			facebookMessageRepository.updateFacebookMessageBody(messageBody,messageId);
		} catch (Exception e) {
			log.error("error : {} occurred in updatingFacebookMessage", e.getMessage());
		}
	}
}
