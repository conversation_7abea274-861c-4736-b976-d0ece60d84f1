package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomChannelMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CampaignTemplateRequest;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomChannelMessageDTO;
import com.birdeye.messenger.dto.CustomSendRequestDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessageResponse.Message;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.CustomChannelMessageService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomChannelSendEventHandler extends MessageEventHandlerAbstract {

	private final MessengerEvent EVENT = MessengerEvent.CUSTOM_CHANNEL_SEND;

	@Autowired
	private ContactService contactService;

	@Autowired
	private RedisLockService redisLockService;

	@Autowired
	private KafkaService kafkaService;

	@Autowired
	private FacebookEventService facebookEventService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Autowired
	private NLPService nlpService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	private CustomChannelMessageService customChannelMessageService;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private SendMessageService sendMessageService;
	
	private final WhatsappMessageService whatsappMessageService;

	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if (Objects.isNull(messageId)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			messageId = dto.getConversationDTO().getId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
			messageDTO.setMessengerContact(messengerContact);
		}
		messengerContact.setTemplateId(sendMessageDTO.getTemplateId());
		return messengerContact;
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		SendMessageDTO dto = (SendMessageDTO) messageDTO;
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(),
				getMessengerContact(messageDTO).getId());

		if (CollectionUtils.isNotEmpty(dto.getMessengerMediaFileList())) {
			messageDocumentDTO.setMediaFiles(dto.getMessengerMediaFileList().stream()
					.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile.getFileExtension(),
							messengerMediaFile.getUrl(), messengerMediaFile.getContentSize(),
							messengerMediaFile.getName(), messengerMediaFile.getContentType()))
					.collect(Collectors.toList()));
		}

		if (dto.getTemplateId() != null) {
			messageDocumentDTO.setTemplateId(dto.getTemplateId());
		}

		if (BooleanUtils.isTrue(messageDTO.isSecureFaq())) {
			messageDocumentDTO.setSecureFaq(messageDTO.isSecureFaq());
		}
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		return messageDocumentDTO;
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		//BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		ConversationDTO conversationDTO = sendMessageDTO.getConversationDTO();
		if (StringUtils.isEmpty(sendMessageDTO.getBody())
				&& CollectionUtils.isNotEmpty(sendMessageDTO.getMessengerMediaFileList())) {
			messengerContact.setLastMessage("Sent an attachment");
		} else {
			messengerContact.setLastMessage(sendMessageDTO.getBody());
		}
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(),
				conversationDTO.getRecipient(), conversationDTO.getSender());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		messengerContact.setLastResponseAt(sendMessageDTO.getConversationDTO().getSentOn());
		messengerContact.setLastMsgOn(new Date());
		messengerContact.setUpdatedAt(new Date());
		UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.CUSTOM.name());
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.CUSTOM.getSourceId());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		if (StringUtils.isNotBlank(sendMessageDTO.getCustomChannel())) {
			lastMessageMetadataPOJO.setLastMessageCustomChannel(sendMessageDTO.getCustomChannel());
		}
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
	}

	@Override
	protected MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		return null; // No email notifications for custom channel
	}

	private void validate(SendMessageDTO sendMessageDTO) throws Exception {
		BusinessDTO businessDTO = getBusinessDTO(sendMessageDTO);

		// Validate business and customer
		if (businessDTO == null || sendMessageDTO.getCustomerId() == null) {
			throw new InputValidationException("Business ID and Customer ID are required");
		}

		// Validate custom channel
		if (StringUtils.isBlank(sendMessageDTO.getCustomChannel())) {
			throw new InputValidationException("Custom channel name is required");
		}

		// Validate message body or media
		if (StringUtils.isBlank(sendMessageDTO.getBody()) && CollectionUtils.isEmpty(sendMessageDTO.getMediaUrls())) {
			throw new InputValidationException("Message body or media is required");
		}

		// Check for profanity
		if (StringUtils.isNotBlank(sendMessageDTO.getBody()) && nlpService.isTextProfane(sendMessageDTO.getBody())) {
			log.info("CustomChannelSendHandler: Input message not saved due to profanity filtering for business: {}",
					businessDTO.getBusinessId());
			throw new InputValidationException("Message not saved as it qualified as profane");
		}
	}

	private void saveMediaFiles(SendMessageDTO sendMessageDTO, Integer conversationId) {
		if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			for (MessengerMediaFile mediaFile : sendMessageDTO.getMediaUrls()) {
				MessengerMediaFileDTO mediaFileDTO = new MessengerMediaFileDTO(mediaFile);
				mediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
				sendMessageService.saveMediaFile(mediaFileDTO, conversationId);
			}
		}
	}

	@Override
	public MessageResponse handle(MessageDTO messageDTO) throws Exception {
		if (!messageDTO.isBOT()) {
			messageDTO.setMsgTypeForResTimeCalc("S");
		}
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		MessengerContact contact = getMessengerContact(messageDTO);
		sendMessageDTO.setCustomerId(contact.getCustomerId());
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);

		// Validate the message
		validate(sendMessageDTO);

		// Check if messenger is enabled
		Integer isMessengerEnabled = businessService.isMessengerEnabled(businessDTO.getAccountId());
		if (isMessengerEnabled != 1) {
			log.error("Messenger is not enabled for account: {}", businessDTO.getAccountId());
			return null;
		}

		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.warn("Lock is already acquired for the key: {}", lockKey);
				//REJECT this event
				throw new RedisLockException("Unable to acquire lock for customer: " + customerDTO.getId());
			}

			// Process the message first
			SortedSet<Message> messages = sendMessage(messageDTO);
			if (messages.isEmpty()) {
				return null;
			}

			// Create send response
			int routeId = businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId()
					: businessDTO.getBusinessId();
			SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages,
					facebookEventService.isFBSendAvailable(getMessengerContact(messageDTO), routeId), true);
			WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(getMessengerContact(messageDTO), routeId);
			sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
			sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
			sendResponse.setLastMsgSource(Source.CUSTOM.getSourceId());
			sendResponse.setMessages(messages);

			// Handle pulse survey context after message is sent
			try {
				PulseSurveyContext context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO,
						businessDTO);
				ContactDocument contactDocument = new ContactDocument();
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())) {
					contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					contactDocument.setOngoingPulseSurvey(false);
				}
				messengerContactService.updateContactOnES(messageDTO.getMessengerContact().getId(), contactDocument,
						messageDTO.getBusinessDTO().getAccountId());
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}

			return sendResponse;

		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private SortedSet<Message> sendMessage(MessageDTO messageDTO) throws Exception {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		UserDTO userDTO = getUserDTO(messageDTO);
		sendMessageDTO.setSource(Source.CUSTOM.getSourceId());
		sendMessageDTO.setFromBusinessId(getMessengerContact(sendMessageDTO).getBusinessId());
		messageDTO.setSentThrough(SentThrough.WEB);

		SortedSet<Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
		
		// Handle media files similar to email send handler
		List<MessengerMediaFileDTO> messengerMediaFiles = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			String text = sendMessageDTO.getBody();
			for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
				MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
				sendMessageDTO.setMediaurl(mediaFile.getUrl());
				if (media < sendMessageDTO.getMediaUrls().size() - 1) {
					sendMessageDTO.setBody(text);
				}
				MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(mediaFile);
				messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
				messengerMediaFileDTO.setData(mediaFile.getData());
				messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
				messengerMediaFiles.add(messengerMediaFileDTO);
				if (media != 0) {
					messageDTO.setPartOfExistingMessage(true);
				}
			}
			messageDTO.setMessengerMediaFileList(messengerMediaFiles);
		}

		CustomChannelMessage customChannelMessage;
		if (sendMessageDTO.getTemplateId() != null) {
			// Save campaign message
			customChannelMessage = customChannelMessageService.saveCampaignCustomChannelMessage(
				sendMessageDTO,
				String.valueOf(businessDTO.getBusinessNumber()),
				String.valueOf(getMessengerContact(sendMessageDTO).getId())
			);
			CampaignTemplateRequest request = campaignRequest(sendMessageDTO, businessDTO, customChannelMessage.getId());
			// Push to Kafka topic for async processing
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.CAMPAIGN_TEMPLATE_COOK, request);
		} else {
			// Save regular message
			CustomChannelMessageDTO customChannelMessageDTO = new CustomChannelMessageDTO();
			customChannelMessageDTO.setBusinessId(sendMessageDTO.getBusinessDTO().getBusinessId());
			customChannelMessageDTO.setCustomerId(sendMessageDTO.getCustomerId());
			customChannelMessageDTO.setBody(sendMessageDTO.getBody());
			customChannelMessageDTO.setCustomChannel(sendMessageDTO.getCustomChannel());
			customChannelMessageDTO.setTimestamp(new Date());
			customChannelMessageDTO.setEmailSubject(sendMessageDTO.getEmailSubject());
			customChannelMessageDTO.setBusinessDTO(sendMessageDTO.getBusinessDTO());
			if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
				customChannelMessageDTO.setMediaUrl(sendMessageDTO.getMediaUrls().stream()
					.map(MessengerMediaFile::getUrl)
					.collect(Collectors.toList()));
			}

			customChannelMessage = customChannelMessageService.saveCustomChannelMessage(
				customChannelMessageDTO,
				String.valueOf(businessDTO.getBusinessNumber()),
				String.valueOf(getMessengerContact(sendMessageDTO).getId()),
				"SEND"
			);
		}

		// Create conversation
		ConversationDTO conversationDTO = getConversationDTO(sendMessageDTO, businessDTO,
				customerDTO, customChannelMessage);
		sendMessageDTO.setConversationDTO(conversationDTO);

		// Save media files if present
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			saveMediaFiles(sendMessageDTO, conversationDTO.getId());
		}

		// Save the message in messenger_message table
		messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);

		// Publish activity if channel has changed
		publishActivity(messageDTO);

		super.handle(sendMessageDTO);

		// Send to subscription service (currently just logging)
		// Only for non template messages . for template messages it will be done when
		// we receive the callback
		if (sendMessageDTO.getTemplateId() == null) {
			CustomSendRequestDTO sendRequest = buildCustomSendRequest(sendMessageDTO);
			//log.info("CustomSend request to subscription service: " + JSONUtils.toJSON(sendRequest));
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SUBSCRIPTION_SERVICE_SEND_MESSAGE, sendRequest);
			
		}
		
		Message message = new Message(userDTO, conversationDTO, getMessengerMediaFiles(messengerMediaFiles),
				businessDTO.getTimeZoneId());
		message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
		messages.add(message);

		return messages;
	}

	private ConversationDTO getConversationDTO(SendMessageDTO sendMessageDTO,
			BusinessDTO businessDTO, CustomerDTO customerDTO, CustomChannelMessage customChannelMessage) {
		// Save the custom channel message

		// Create conversation DTO for the message
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setMessengerContactId(getMessengerContact(sendMessageDTO).getId());
		conversationDTO.setBusinessId(businessDTO.getBusinessId());
		conversationDTO.setChannel(MessageDocument.Channel.CUSTOM);
		conversationDTO.setCustomChannel(sendMessageDTO.getCustomChannel());
		conversationDTO.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
		conversationDTO.setMessageType(MessageDocument.MessageType.CHAT);
		conversationDTO.setSentOn(new Date());
		conversationDTO.setCreateDate(new Date());
		conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
		conversationDTO.setRecipient(String.valueOf(getMessengerContact(sendMessageDTO).getId()));
		conversationDTO.setSource(Source.CUSTOM.getSourceId());
		conversationDTO.setId(customChannelMessage.getId());
		conversationDTO.setBody(customChannelMessage.getMessageBody());
		conversationDTO.setSubject(customChannelMessage.getEmailSubject());
		conversationDTO.setEncrypted(customChannelMessage.getEncrypted());
		conversationDTO.setTemplateId(sendMessageDTO.getTemplateId());
		conversationDTO.setSentThrough(sendMessageDTO.getSentThrough());

		return conversationDTO;
	}

	private void publishActivity(MessageDTO messageDTO) {
		addActivityIfChannelChanged(messageDTO);
		if (messageDTO.getActivityMessage() != null) {
			conversationActivityService.persistActivityForChannel(messageDTO);
		}
	}

	private void addActivityIfChannelChanged(MessageDTO messageDTO) {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);

		String lastChannel = lastMessageMetadataPOJO.getLastMessageChannel();
		String lastCustomChannel = lastMessageMetadataPOJO.getLastMessageCustomChannel();

		log.info("[addActivityIfChannelChanged] last channel: {}, last custom channel: {}", lastChannel,
				lastCustomChannel);

		// Check if channel has changed or if custom channel has changed
		if (lastChannel == null || !MessageDocument.Channel.CUSTOM.name().equals(lastChannel)
				|| (lastCustomChannel != null && !lastCustomChannel.equals(sendMessageDTO.getCustomChannel()))) {

			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			ActivityDto activityDto = ActivityDto.builder().mcId(messengerContact.getId())
					.created(new Date(sendMessageDTO.getConversationDTO().getSentOn().getTime() - 1000))
					.actorId(messengerContact.getCustomerId())
					.activityType(ActivityType.CUSTOM_CHANNEL_CONVERSATION_STARTED).from(messengerContact.getId())
					.businessId(businessDTO.getBusinessId()).to(businessDTO.getBusinessId())
					.accountId(businessDTO.getAccountId()).source(Source.CUSTOM.getSourceId())
					.customChannel(sendMessageDTO.getCustomChannel()).build();

			messageDTO.setActivityMessage(activityDto);
		}
		log.info("[addActivityIfChannelChanged] Activity added: {}", messageDTO.getActivityMessage());
	}

	private CustomSendRequestDTO buildCustomSendRequest(SendMessageDTO sendMessageDTO) {
		CustomerDTO customerDTO = sendMessageDTO.getCustomerDTO();
		BusinessDTO businessDTO = sendMessageDTO.getBusinessDTO();

		CustomSendRequestDTO request = new CustomSendRequestDTO();
		request.setLocationId(businessDTO.getBusinessId().longValue());

		CustomSendRequestDTO.Contact contact = new CustomSendRequestDTO.Contact();
		contact.setName(customerDTO.getDisplayName());
		contact.setEmail(customerDTO.getEmailId());
		contact.setPhone(customerDTO.getPhone());
		contact.setCustomerId(customerDTO.getId());
		request.setContact(contact);

		request.setChannel(sendMessageDTO.getCustomChannel());

		CustomSendRequestDTO.Content content = new CustomSendRequestDTO.Content();
		content.setBody(sendMessageDTO.getBody());
		if (CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			content.setAttachmentURL(sendMessageDTO.getMediaUrls().stream().map(MessengerMediaFile::getUrl)
					.collect(Collectors.toList()));
		}
		request.setContent(content);

		request.setContentType(sendMessageDTO.getTemplateId() != null ? "Text/HTML" : "Text/Plain");

		CustomSendRequestDTO.Metadata metadata = new CustomSendRequestDTO.Metadata();
		metadata.setTimestamp(System.currentTimeMillis());
		request.setMetadata(metadata);

		return request;
	}

	private CampaignTemplateRequest campaignRequest(SendMessageDTO messageDTO, BusinessDTO businessDTO, Integer externalID) {
		CampaignTemplateRequest request = new CampaignTemplateRequest();
		request.setCustomerId(messageDTO.getCustomerId());
		request.setTemplateId(messageDTO.getTemplateId());
		request.setUserId(messageDTO.getUserId());
		request.setBusinessId(businessDTO.getBusinessId().longValue());
		request.setAccountId(businessDTO.getAccountId());
		request.setEmployeeEmailId(messageDTO.getUserDTO().getEmailId());
		request.setSource("CUSTOM");
		request.setTemplateType(messageDTO.getTemplateType());
		request.setSurveyId(messageDTO.getSurveyId());
		request.setExternalUId(externalID);
		return request;
	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		if (Objects.isNull(businessDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
			businessDTO = communicationHelperService.getBusinessDTO(businessId);
			dto.setBusinessDTO(businessDTO);
		}
		return businessDTO;
	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (customerDTO == null) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
			dto.setCustomerDTO(customerDTO);
		}
		return customerDTO;
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}

	@Override
	UserDTO getUserDTO(MessageDTO messageDTO) {
		UserDTO userDTO = messageDTO.getUserDTO();
		if (Objects.isNull(userDTO)) {
			SendMessageDTO dto = (SendMessageDTO) messageDTO;
			userDTO = communicationHelperService.getUserDTO(dto.getUserId());
			dto.setUserDTO(userDTO);
		}
		return userDTO;
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		return MessageTag.INBOX;
	}

	private List<MessengerMediaFile> getMessengerMediaFiles(List<MessengerMediaFileDTO> messengerMediaFiles) {
		List<MessengerMediaFile> mediaFileList = new ArrayList<MessengerMediaFile>();
		if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
			for (MessengerMediaFileDTO mediaFileDTO : messengerMediaFiles) {
				MessengerMediaFile mediaFile = new MessengerMediaFile(mediaFileDTO);
				mediaFileList.add(mediaFile);
			}
		}
		return mediaFileList;
	}
}