package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomChannelMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomSendRequestDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.TemplateCallbackRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.CustomChannelMessageService;
import com.birdeye.messenger.service.CustomChannelTemplateCallbackService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CustomChannelTemplateCallbackServiceImpl implements CustomChannelTemplateCallbackService {
    
    @Autowired
    private CustomChannelMessageService customChannelMessageService;
    
    @Autowired
    private MessengerContactService messengerContactService;
    
    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private ContactService contactService;
    
    @Autowired
    private ElasticSearchExternalService elasticSearchService;
    
    @Autowired
	private KafkaService kafkaService;
    
    @Autowired
    private FirebaseService fcmService;
    
    @Override
    public void handleTemplateCallback(TemplateCallbackRequest request) {
        
    
    	
    	// Fetch the message
        CustomChannelMessage message = customChannelMessageService.findById(Integer.valueOf(request.getExternalUId()));
        if (message == null) {
       
            log.error("Message not found for externalUId: {}", request.getExternalUId());
            throw new RuntimeException("Message not found for externalUId: " + request.getExternalUId());
        }
        
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(message.getBusinessId());
        MessengerContact messengerContact = messengerContactService.findById(message.getMcId());
        
        // Fetch customer data
        CustomerDTO customerDTO = contactService.findByIdNoCaching(messengerContact.getCustomerId());
        if (customerDTO == null) {
            log.error("Customer not found for customerId: {}", messengerContact.getCustomerId());
            throw new RuntimeException("Customer not found for customerId: " + messengerContact.getCustomerId());
        }
        

        // If there's a failure reason, update message status and doc
        if (StringUtils.isNotBlank(request.getFailureReason())) {
            String failureReason = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getErrorMessage(request.getFailureReason(), message.getCustomChannel()+" message");
                
            message.setFailureReason(failureReason);
            customChannelMessageService.save(message);
            updateMessageDocument(message, messengerContact, businessDTO, failureReason);
        } else {
            //Only if no failure send to subscription service.
            CustomSendRequestDTO sendRequest = buildCustomSendRequest(message, request.getData(),customerDTO);
            //log.info("CustomSend request to subscription service: " + JSONUtils.toJSON(sendRequest));
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SUBSCRIPTION_SERVICE_SEND_MESSAGE, sendRequest);
        }
        
       
    }
    
	private void updateMessageDocument(CustomChannelMessage message, MessengerContact messengerContact,
			BusinessDTO businessDTO, String failureReason) {
		ESFindByFieldRequest<MessageDocument> esFindRequest = new ESFindByFieldRequest.Builder<MessageDocument>()
				.setIndex(Constants.Elastic.MESSAGE_INDEX).setRoutingId(businessDTO.getAccountId())
				.setDocumentType(MessageDocument.class).setShouldFields("_id", String.valueOf(message.getId() + "_cc"))
				.setMinShouldMatch(1).build();
		List<MessageDocument> messages = elasticSearchService.findDocumentByField(esFindRequest);
		if (CollectionUtils.isEmpty(messages) || messages.size() > 1) {
			log.error("No custom channel message doc found in ES for messageId: {}", message.getId());
			return;
		}
		MessageDocument messageDoc = messages.get(0);
		if (failureReason != null) {
			messageDoc.setMsg_status(failureReason);
		}

		messengerContactService.updateMessageOnES(messageDoc, businessDTO.getAccountId());

		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(businessDTO.getAccountId());
		firebaseDto.setBusinessId(businessDTO.getBusinessId());
		firebaseDto.setMcId(messengerContact.getId());
		fcmService.mirrorOnWeb(firebaseDto);
	}
    

    private CustomSendRequestDTO buildCustomSendRequest(CustomChannelMessage message, String templateBody, CustomerDTO customerDTO) {
        CustomSendRequestDTO sendRequest = new CustomSendRequestDTO();
        sendRequest.setLocationId(message.getBusinessId().longValue());
        
        CustomSendRequestDTO.Contact contact = new CustomSendRequestDTO.Contact();
        // No name/email/phone fields available on CustomChannelMessage entity
        contact.setName(customerDTO.getName());
        contact.setEmail(customerDTO.getEmailId());
        contact.setPhone(customerDTO.getPhone());
        contact.setCustomerId(customerDTO.getId());
        sendRequest.setContact(contact);
        
        sendRequest.setChannel(message.getCustomChannel());
        
        CustomSendRequestDTO.Content content = new CustomSendRequestDTO.Content();
        content.setBody(templateBody);
        sendRequest.setContent(content);
        
        sendRequest.setContentType("Text/HTML");
        
        CustomSendRequestDTO.Metadata metadata = new CustomSendRequestDTO.Metadata();
        metadata.setTimestamp(System.currentTimeMillis());
        sendRequest.setMetadata(metadata);
        
        return sendRequest;
    }
} 