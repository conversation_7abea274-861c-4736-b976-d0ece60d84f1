package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.TwitterMessage;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendTwitterMessage;
import com.birdeye.messenger.dto.SendTwitterMessageResponse;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatus;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatusRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.twitter.MessageCreate;
import com.birdeye.messenger.dto.twitter.TwitterMessageRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.SocialMessageTag;
import com.birdeye.messenger.enums.TwitterMessageStatusEnum;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.TwitterException;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.TwitterEventService;
import com.birdeye.messenger.service.TwitterMessageService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MediaUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class TwitterEventServiceImpl implements TwitterEventService{
    
    private final SocialService socialService;
    private final TwitterMessageService twitterMessageService;
    private final MessengerContactService messengerContactService;
    private final RedisHandler redisHandler;

    @Override
    public TwitterMessage saveTwitterMessage(MessageDTO messageDTO,String senderId,String recipientId,
                                             String messageId,TwitterMessageStatusEnum status) {

        Integer encrypted = 0;
        String message;
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (messageDTO instanceof SendMessageDTO) {
            message = ((SendMessageDTO) messageDTO).getBody();
        } else {
            TwitterMessageRequest twitterMessageRequest = (TwitterMessageRequest) messageDTO;
            MessageCreate messaging = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
            message = messaging.getMessage_data().getText();
            messengerContact.setLastMessage(message);
            if (StringUtils.isNotBlank(message) && messaging.isEcho()) {
                messengerContact.setLastMessage("You: " + message);
            }
        }

        try {
            if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotBlank(message)) {
                message = EncryptionUtil.encrypt(message, StringUtils.join(senderId, recipientId),
                        StringUtils.join(recipientId, senderId));
                encrypted = 1;
            }
        } catch (Exception e) {
            log.info("Encryption for recieved twitter message failed: {}", e);
        }
        TwitterMessage twitterMessage = new TwitterMessage();
        twitterMessage.setCreateDate(new Date());
        twitterMessage.setMessengerContactId(messengerContact.getId());
        twitterMessage.setRecipientTwitterId(recipientId);
        twitterMessage.setSenderTwitterId(senderId);
        if (StringUtils.isNotBlank(message)) {
            twitterMessage.setMessageBody(message);
        }
        if (messageId.substring(0, 2).equalsIgnoreCase("m_"))
            twitterMessage.setMessageId(messageId.subSequence(2, messageId.length()).toString());
        else
            twitterMessage.setMessageId(messageId);
        twitterMessage.setSentOn(new Date());
        twitterMessage.setEncrypted(encrypted);
        twitterMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
        if (messageDTO.getMessengerMediaFileDTO() != null
                && StringUtils.isNotBlank(messageDTO.getMessengerMediaFileDTO().getUrl())) {
            twitterMessage.setMediaURL(messageDTO.getMessengerMediaFileDTO().getUrl());
        }
        try {
            twitterMessageService.saveAndFlushTwitterMessage(twitterMessage);
        } catch (Exception e) {
            log.error("Duplicate Twitter message message id: {}: ", messageId);
            // throw new DuplicateEntryException(ErrorCode.DUPLICATE_MESSAGE);
        }
        return twitterMessage;

    }

    @Override
    public SendTwitterMessageResponse sendTwitterCallToSocial(TwitterSendEventHandler handler,MessageDTO messageDTO,
                                                           String pageId) {
        SendTwitterMessage sendTwitterMessage = null;
        String body = ((SendMessageDTO) messageDTO).getBody();
        if (StringUtils.isBlank(pageId)) {
            throw new NotFoundException(ErrorCode.TWITTER_PAGE_NOT_FOUND);
        }
        // TODO get senderId from businessSMS table
        ResponseEntity<String> res = null;
        MessengerMediaFileDTO messengerMediaFileDTO = messageDTO.getMessengerMediaFileDTO();

        String receiverId = handler.getMessengerContact(messageDTO).getTwitterConversationId();
        if (messengerMediaFileDTO != null) {
            String base64 = null;
            if(Objects.isNull(((SendMessageDTO)messageDTO).getMediaId())){
                base64 =MediaUtils.convertImageUrlToBase64(messengerMediaFileDTO.getUrl());
            }
            sendTwitterMessage = new SendTwitterMessage(body, pageId, receiverId,SocialMessageTag.HUMAN_AGENT.getName(),((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId(),((SendMessageDTO)messageDTO).getMediaId(),base64);
            // send message to Twitter
            res = socialService.sendTwitterMessage(sendTwitterMessage);
            if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
                String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
                if(responseBodyAsString.contains("403"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_SEND_FAILED_USER_NOT_FOLLOWING, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1547"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_USER_BLOCKED_BUSINESS, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1546"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_USER_BLOCKED_BY_BUSINESS, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1548"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_FOLLOW_BUSINESS_USER_TO_SEND_MESSAGE, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_API_ERROR, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
            }
        }
        if (StringUtils.isNotEmpty(body) && Objects.isNull(res)) {
            sendTwitterMessage = new SendTwitterMessage(body, pageId, receiverId, SocialMessageTag.HUMAN_AGENT.getName(),((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId(),null,null);
            // send message to Twitter
            res = socialService.sendTwitterMessage(sendTwitterMessage);
            if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
                String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
                if(responseBodyAsString.contains("403"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_SEND_FAILED_USER_NOT_FOLLOWING, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1547"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_USER_BLOCKED_BUSINESS, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1546"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_USER_BLOCKED_BY_BUSINESS, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else if(responseBodyAsString.contains("1548"))
                    throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_FOLLOW_BUSINESS_USER_TO_SEND_MESSAGE, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
                else
                throw new TwitterException(new ErrorMessageBuilder(ErrorCode.TWITTER_API_ERROR, ComponentCodeEnum.TWITTER, HttpStatus.BAD_REQUEST));
            }
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),
                SendTwitterMessageResponse.class);
    }

    @Override
    public Boolean isTwitterSendAvailable(MessengerContact messengerContact, Integer routeId) {
        // Getting last message metadata
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        if (lastMessageMetaData == null || lastMessageMetaData.getLastIgReceivedAt() == null) {
            // fallback get from es
            return isTwitterSendAvailableHelper(messengerContact, routeId);
        } else {
            return calculateTwitterSendAvailable(messengerContact.getLeadSource(),
                    lastMessageMetaData.getLastIgReceivedAt());
        }
    }

    private Boolean calculateTwitterSendAvailable(LeadSource leadSource,String lastTwitterReceivedAt) {
        boolean isLastTwitterMessage = false;
        if (leadSource == LeadSource.TWITTER && StringUtils.isBlank(lastTwitterReceivedAt)) {
            return true;
        } else if (StringUtils.isNotBlank(lastTwitterReceivedAt)) {
            Calendar calLastReceived = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                calLastReceived.setTime(sdf.parse(lastTwitterReceivedAt));
                calLastReceived.add(Calendar.HOUR, 24 * 7);

                if (calLastReceived.getTime().before(Calendar.getInstance().getTime())) {
                    isLastTwitterMessage = true;
                }

            } catch (Exception e) {
                log.error("exception in parsing date of calculateTwitterSendAvailable method: ", e);
            }
        }
        return isLastTwitterMessage;
    }

    private Boolean isTwitterSendAvailableHelper(MessengerContact messengerContact, Integer routeId) {
        MessengerFilter messengerFilter = new MessengerFilter();
        messengerFilter.setStartIndex(0);
        messengerFilter.setCount(1);
        messengerFilter.setConversationId(messengerContact.getId());
        messengerFilter.setAccountId(routeId);
        List<String> typeList = new ArrayList<>();
        typeList.add("SMS_RECEIVE");
        typeList.add("MMS_RECEIVE");
        Map<String, Object> params = new HashMap<>();
        params.put("msg_type", ControllerUtil.getJsonTextFromObject(typeList));
        params.put("source", "18");
        messengerFilter.setParams(params);
        String lastTwitterReceivedAt = "";
        // last Twitter Received message
        List<MessageDocument> messages = messengerContactService.getMessagesFromES(messengerFilter);
        if (CollectionUtils.isNotEmpty(messages)) {
            lastTwitterReceivedAt = messages.get(0).getCr_date();
        }
        
        return calculateTwitterSendAvailable(messengerContact.getLeadSource(), lastTwitterReceivedAt);
    }

    @Override
    public String getTwitterIntegrationStatus(Integer businessId) {
        String response = redisHandler.getTwitterStatusFromCache(String.valueOf(businessId));
        if (StringUtils.isNotBlank(response)) {
            return ControllerUtil.getObjectFromJsonText(response, Map.class).get("status").toString();
        }
        SocialChannelIntegrationStatusRequest request = new SocialChannelIntegrationStatusRequest();
        request.setBusinessId(businessId);
        request.setTwitterIntegrationStatus(true);
        SocialChannelIntegrationStatus responseFromSocial = socialService.getAllSocialChannelIntegrationStatus(request);
        if (Objects.nonNull(responseFromSocial)) {
            String status = responseFromSocial.getTwitterIntegrationStatus();
            if (status != null) {
                try {
                    redisHandler.updateTwitterStatusCache(String.valueOf(businessId), status);
                } catch (JsonProcessingException e) {

                }
            }
        }
        return response;
    }
    

    @Override
    public Map<String,String> getBusinessIdAndBirdeyeCdnAttachmentUrl(Map<String,Object> request,String businessTwitterId){
        return socialService.getBusinessIdAndBirdeyeCdnAttachmentUrl(request,businessTwitterId);
    }
}
