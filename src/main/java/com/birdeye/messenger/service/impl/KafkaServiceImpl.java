package com.birdeye.messenger.service.impl;

import java.io.IOException;
import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.service.KafkaService;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class KafkaServiceImpl implements KafkaService {

    @Autowired
    @Qualifier("kafkaTemplate")
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    @Qualifier("nexusKafkaTemplate")
    private KafkaTemplate<String, String> nexusKafkaTempate;

    @Override
    public void publishToKafkaAsync(KafkaTopicEnum topicEnum, Serializable key, Serializable value) {
        String sKey = writeAsString(key);
        String sValue = writeAsString(value);
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        CompletableFuture<SendResult<String, String>> kafkaResponse = kafkaTemplate.send(topicEnum.getTopicValue(), sKey, sValue);
		try {
			kafkaResponse.thenAcceptAsync(result -> {
				if (contextMap != null) {
		            MDC.setContextMap(contextMap);
		        }
		        // Log information about the successful send operation
		        log.info("Sent to Kafka topic {} on offset {} and partition {}",
		                topicEnum.getTopicValue(),
		                result.getRecordMetadata().offset(),
		                result.getRecordMetadata().partition());
		        MDC.clear();
		    }).exceptionally(exception -> {
		        log.error("Unable to push to Kafka topic {}", topicEnum.getTopicValue(), exception);
		        return null;
		    });
		} catch (Exception e) {
			log.error("Unable to push to kafka topic {}", topicEnum.getTopicValue(), e);
		}
    }

    @Override
    public void publishToKafkaAsync(KafkaTopicEnum topicEnum, Integer partition, Serializable key, Serializable value) {
        String sKey = writeAsString(key);
        String sValue = writeAsString(value);
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        CompletableFuture<SendResult<String, String>> kafkaResponse = kafkaTemplate.send(topicEnum.getTopicValue(), partition, sKey, sValue);
        try {
			kafkaResponse.thenAcceptAsync(result -> {
				if (contextMap != null) {
		            MDC.setContextMap(contextMap);
		        }
		        // Log information about the successful send operation
		        log.info("Sent to Kafka topic {} on offset {} and partition {}",
		        		topicEnum.getTopicValue(),
		                result.getRecordMetadata().offset(),
		                result.getRecordMetadata().partition());
		        MDC.clear();
		    }).exceptionally(exception -> {
		        log.error("Unable to push to Kafka topic {}", topicEnum.getTopicValue(), exception);
		        return null;
		    });
		} catch (Exception e) {
			log.error("Unable to push to kafka topic {}", topicEnum.getTopicValue(), e);
		}
    }

    private String writeAsString(Serializable input) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(input);
        } catch (IOException e) {
            log.error("Unable to serialize Object {} to String", input, e);
            throw new BadRequestException(ErrorCode.INTERNAL_SERVER_ERROR, e);
        }
    }

    @Override
    public void publishToKafkaAsync(KafkaTopicEnum topicEnum, Serializable value) {
        String sValue = writeAsString(value);
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        CompletableFuture<SendResult<String, String>> kafkaResponse = kafkaTemplate.send(topicEnum.getTopicValue(), sValue);
        try {
			kafkaResponse.thenAcceptAsync(result -> {
				if (contextMap != null) {
		            MDC.setContextMap(contextMap);
		        }
		        // Log information about the successful send operation
		        log.info("Sent to Kafka topic {} on offset {} and partition {}",
		        		topicEnum.getTopicValue(),
		                result.getRecordMetadata().offset(),
		                result.getRecordMetadata().partition());
		        MDC.clear();
		    }).exceptionally(exception -> {
		        log.error("Unable to push to Kafka topic {}", topicEnum.getTopicValue(), exception);
		        return null;
		    });
		} catch (Exception e) {
			log.error("Unable to push to kafka topic {}", topicEnum.getTopicValue(), e);
		}
    }

    @Override
    public void publishToNexusKafkaAsync(KafkaTopicEnum topicEnum, Serializable value) {
        String sValue = writeAsString(value);
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        CompletableFuture<SendResult<String, String>> kafkaResponse = nexusKafkaTempate.send(topicEnum.getTopicValue(), sValue);
        try {
			kafkaResponse.thenAcceptAsync(result -> {
				if (contextMap != null) {
		            MDC.setContextMap(contextMap);
		        }
		        // Log information about the successful send operation
		        log.info("Sent to Kafka topic {} on offset {} and partition {}",
		        		topicEnum.getTopicValue(),
		                result.getRecordMetadata().offset(),
		                result.getRecordMetadata().partition());
		        MDC.clear();
		    }).exceptionally(exception -> {
		        log.error("Unable to push to Kafka topic {}", topicEnum.getTopicValue(), exception);
		        return null;
		    });
		} catch (Exception e) {
			log.error("Unable to push to kafka topic {}", topicEnum.getTopicValue(), e);
		}
    }

    @Override
    public void publishToNexusKafkaAsync(KafkaTopicEnum topicEnum, Serializable key, Serializable value) {
        String sKey = writeAsString(key);
        String sValue = writeAsString(value);
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        CompletableFuture<SendResult<String, String>> kafkaResponse = nexusKafkaTempate.send(topicEnum.getTopicValue(), sKey, sValue);
        try {
			kafkaResponse.thenAcceptAsync(result -> {
				if (contextMap != null) {
		            MDC.setContextMap(contextMap);
		        }
		        // Log information about the successful send operation
		        log.info("Sent to Kafka topic {} on offset {} and partition {}",
		        		topicEnum.getTopicValue(),
		                result.getRecordMetadata().offset(),
		                result.getRecordMetadata().partition());
		        MDC.clear();
		    }).exceptionally(exception -> {
		        log.error("Unable to push to Kafka topic {}", topicEnum.getTopicValue(), exception);
		        return null;
		    });
		} catch (Exception e) {
			log.error("Unable to push to kafka topic {}", topicEnum.getTopicValue(), e);
		}
    }
}
