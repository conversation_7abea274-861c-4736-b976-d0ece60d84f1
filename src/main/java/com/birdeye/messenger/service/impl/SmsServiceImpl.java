package com.birdeye.messenger.service.impl;

import java.util.List;
import java.util.Objects;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dao.repository.SmsRepository;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.util.LogUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dto.ConversationWrapperMessage;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.PhoneNoValidator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class SmsServiceImpl implements SmsService {

	private final SmsRepository smsrepo;

	private final ContactService contactService;

	private final BusinessService businessService;
	
	@Autowired
	private NexusService nexusService;


	@Override
	@Transactional
	public Sms saveSMS(SendMessageDTO sendMessageDTO) {
		
		// Business Information
		Integer fromBusinessId = Integer.parseInt(sendMessageDTO.getBusinessIdentifierId());
		sendMessageDTO.setFromPhone(getFormattedBusinessNumber(fromBusinessId));
		// Customer Information
		//sendMessageDTO.setToPhone(getFormattedCustomerNumberSendSms(sendMessageDTO.getCustomerId()));
        CustomerDTO customerDTO = Objects.nonNull(sendMessageDTO.getCustomerDTO()) ? sendMessageDTO.getCustomerDTO()
                : contactService.findById(sendMessageDTO.getCustomerId());
		sendMessageDTO.setToPhone(customerDTO.getPhoneE164());
		Sms sms = DtoToEntityConverter.from(sendMessageDTO);
		log.info("addNewContactAndSendSms: sendMessageDTO {}", sendMessageDTO);
		// Mandatory for messenger SMS. Encryption/Decryption should be done
		// with unformatted numbers.
		encrypt(sms);
		smsrepo.save(sms);
		return sms;
	}

	/**
	 * SMS table has both from & to, messages doc should also keep both
	 * Messenger Contact can also plan to keep snapshot of from/to
	 */

	@Override
	public List<Sms> findByCustomerId(Integer customerId) {
		return smsrepo.findByCustomerId(customerId);
	}

	@Override
	public void encrypt(Sms sms) {
		try {
			if (StringUtils.isNotEmpty(sms.getMessageBodyUnencrypted())) {
				sms.setMessageBody(EncryptionUtil.encrypt(sms.getMessageBodyUnencrypted(),
						StringUtils.join(sms.getFromNumber(), sms.getToNumber()),
						StringUtils.join(sms.getToNumber(), sms.getFromNumber())));
				sms.setEncrypted(1);
			}
		} catch (Exception e) {
			log.error("Encryption failed for sms body: {} with business {}", sms.getMessageBody(), sms.getBusinessId());
			sms.setEncrypted(0);
			sms.setMessageBody(sms.getMessageBodyUnencrypted());
		}
	}

    @Override
    @Transactional
    public void deleteSmsUsingCustomerId(Integer customerId) {
        try {
            smsrepo.deleteByCustomerId(customerId);
        } catch (Exception e) {
            log.error("error : {} occurred in deleteSmsUsingCustomerId", e.getMessage());
        }
    }

    // TODO: Use Business Service. Need to integrate with Caching support
	@Override
	public String getFormattedBusinessNumber(Integer businessId){
		long startTime = System.currentTimeMillis();
//		String fromNumber = pdr.getBusinessSmsNumberById(businessId);
		String fromNumber = businessService.getBusinessSmsNumberById(businessId);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getBusinessSmsNumberById", startTime, endTime);
		startTime = System.currentTimeMillis();
//		String businesCountryCode = pdr.getCountryCodeForBusiness(businessId);
		String businesCountryCode = getCountryCodeForBusiness(businessId); //TODO: Null check
		endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getCountryCodeForBusiness", startTime, endTime);
		return PhoneNoValidator.getFormattedBusinessPhoneNumber(fromNumber, businesCountryCode);
	}
	@Override
	public String getFormattedBusinessNumber(Integer businessId,String phoneNumber){
//		String businesCountryCode = pdr.getCountryCodeForBusiness(businessId);
		String businesCountryCode = getCountryCodeForBusiness(businessId);
		return PhoneNoValidator.getFormattedBusinessPhoneNumber(phoneNumber, businesCountryCode);
	}

	// NOTES:SMS can have single media url,
	// however current messenger UI supports multiple attatchments to be
	// uploaded which gets processed one at a time.
	// Webchat is receive SMS usecase.
	// For WebChat we don't need any 160 Characters limitation as part of JIRA
	// [BIRDEYE-26926]
	// TODO: SMS DTOs can be prepared beforehand to just use a converter
	// service.
	public Sms saveSmsFromCustomer(SmsDTO smsMessage) {
		// Business SMS
		smsMessage.setToNumber(getFormattedBusinessNumber(smsMessage.getBusinessId()));
		// Customer Information. Customer phone and code should already be available in dto before. save query.
		//smsMessage.setFromNumber(getFormattedCustomerNumber(smsMessage.getCustomerId()));
		CustomerDTO customerDTO = contactService.findById(smsMessage.getCustomerId());
		smsMessage.setFromNumber(customerDTO.getPhoneE164());
		Sms sms = DtoToEntityConverter.from(smsMessage);
		encrypt(sms);
		// TODO : -2 Second logic pending.
		long startTime = System.currentTimeMillis();
		smsrepo.saveAndFlush(sms);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("saveSms", startTime, endTime);
		smsMessage.setSmsId(sms.getSmsId());
		return sms;
	}
	
	@Override
	public Sms saveSmsToCustomer(SmsDTO smsDTO) {
		// Business number should be formatted
		if (smsDTO.getFromNumber() == null) {
			smsDTO.setFromNumber(getFormattedBusinessNumber(smsDTO.getBusinessId()));
		}
		if (smsDTO.getToNumber() == null) {
			// Customer phone should be E164 formatted
			//smsDTO.setToNumber(getFormattedCustomerNumberSendSms(smsDTO.getCustomerId()));
			CustomerDTO customerDTO = contactService.findById(smsDTO.getCustomerId());
			smsDTO.setToNumber(customerDTO.getPhoneE164());
		}
		Sms sms = DtoToEntityConverter.from(smsDTO);
		log.debug("SMS dto to be saved. {}", smsDTO);
		encrypt(sms);
		smsrepo.save(sms);
		return sms;
	}

	@Override
	public Sms saveSms(SMSMessageDTO smsMessageDTO) {
		SmsDTO smsDTO = DtoToEntityConverter.from(smsMessageDTO);
		return saveSmsFromCustomer(smsDTO);
	}

	@Override
	public Sms findById(Integer id) {
		return smsrepo.findById(id).orElseThrow(() -> new NotFoundException(ErrorCode.NO_SMS_FOUND));
	}

    @Override
    public List<Sms> findByMessageSid(String messageSid) {
		return smsrepo.findByMessageSid(messageSid);
    }

    @Override
	public void saveSMS(Sms sms) {
		smsrepo.saveAndFlush(sms);
	}


    //TODO: DUPLICATE This should use saveSmsFromCustomer.
    @Override
	public Sms saveSms(SmsDTO smsMessage) {
		// Business SMS
		smsMessage.setToNumber(getFormattedBusinessNumber(smsMessage.getBusinessId()));
		//smsMessage.setFromNumber(getFormattedCustomerNumber(smsMessage.getCustomerId()));
		CustomerDTO customerDTO = contactService.findById(smsMessage.getCustomerId());
		smsMessage.setFromNumber(customerDTO.getPhoneE164());
		Sms sms = DtoToEntityConverter.from(smsMessage);
		encrypt(sms);
		//TODO : -2 Second logic pending.
		smsrepo.save(sms);
		return sms;
	}


	/** Save Chatbot Reply messages unencrypted, as its an ongoing message that does not require encryption
	 *  Hence reducing decryption overhead of a stringified JSON chatbot reply
	*/
	@Override
	public Sms saveSmsForChatbotReply(SmsDTO smsDTO) {
		// Business SMS
		smsDTO.setToNumber(getFormattedBusinessNumber(smsDTO.getBusinessId()));
		//smsDTO.setFromNumber(getFormattedCustomerNumber(smsDTO.getCustomerId()));
		CustomerDTO customerDTO = contactService.findById(smsDTO.getCustomerId());
		smsDTO.setFromNumber(customerDTO.getPhoneE164());
		Sms sms = DtoToEntityConverter.from(smsDTO);
		sms.setRecordingDuration(smsDTO.getRecordingDuration());
		smsrepo.save(sms);
		return sms;
	}

	@Override
	public Sms saveAndSend(SmsDTO smsDTO,BusinessDTO businessDTO) {
		Sms saved = saveSmsToCustomer(smsDTO);
		ConversationWrapperMessage conversation = DtoToEntityConverter.nexusRequest(saved,businessDTO.getAccountId());
        if(Boolean.FALSE.equals(smsDTO.getSpam())) {
			nexusService.sendSMS(conversation);
		}
		append10DlcBrandingToMessageBody(saved,businessDTO);
		saved.setRecordingDuration(smsDTO.getRecordingDuration());
		return saved;
	}

	@Override
	public Sms saveSms(SmsDataDto smsDataDto) {
		Sms sms = DtoToEntityConverter.from(smsDataDto);
		sms.setReviewRequestId(smsDataDto.getReviewRequestId());
		sms.setSource(smsDataDto.getSource());
		sms.setMessageBody(smsDataDto.getMessageBodyUnencrypted());
		sms.setSmsType(smsDataDto.getRType());
		smsrepo.save(sms);
		return sms;
	}
	private String getCountryCodeForBusiness(Integer businessId) {
		BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
		if(StringUtils.isNotBlank(businessDTO.getCountryCode())){
			return businessDTO.getCountryCode();
		}
		return null ;
	}

	@Override
	public Sms findByReviewRequestId(Long externalUid) {
		return smsrepo.findByReviewRequestId(externalUid);
	}

	@Override
	public Sms findByReviewRequestIdAndType(String externalUid, String rType) {
		Long reviewRequestId = Long.parseLong(externalUid);
		return smsrepo.findByReviewRequestIdAndSmsType(reviewRequestId, rType);
	}
	
	private void append10DlcBrandingToMessageBody(Sms sms,BusinessDTO businessDTO){
		log.info("Append Ten Dlc Branding for sms {} account {}",sms.getSmsId(),businessDTO.getAccountId());
		String messageBody = sms.getMessageBodyUnencrypted();
		TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(businessDTO.getEnterpriseNumber());
		if(Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()){
			String status = tenDlcStatusDTO.getStatus();
			TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
			if (status != null && (status.equals("not_started") || status.equals("in_progress") || status.equals("failed"))
					&& tollFreeInfo != null && tollFreeInfo.isAvailable()) {
				messageBody = messageBody.concat("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				sms.setMessageBodyUnencrypted(messageBody);
				encrypt(sms);
			}
		}
	}

	@Override
	@Transactional
	public void updateSmsMessageBody(String messageBody,Integer messageId){
		try {
			smsrepo.updateSmsMessageBody(messageBody,messageId);
		} catch (Exception e) {
			log.error("error : {} occurred in updatingSmsMessageBody", e.getMessage());
		}
	}
}
