package com.birdeye.messenger.service.impl;

import static com.birdeye.messenger.util.ActivityBuilder.activity;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.core.DateRange;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessagingLink;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppOnboardingStatus;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dao.secure.messaging.SecureMessagingAuthDAO;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ContactFreeMarkerData;
import com.birdeye.messenger.dto.ContactMessageDTO;
import com.birdeye.messenger.dto.ConvStateForResTimeCalc;
import com.birdeye.messenger.dto.ConversationByIdRequest;
import com.birdeye.messenger.dto.ConversationBySocialId;
import com.birdeye.messenger.dto.ConversationCountFreemiumAccount;
import com.birdeye.messenger.dto.ConversationDetails;
import com.birdeye.messenger.dto.ConversationFilter;
import com.birdeye.messenger.dto.ConversationFilter.ReviewFilter;
import com.birdeye.messenger.dto.ConversationFilter.SurveyFilter;
import com.birdeye.messenger.dto.ConversationRequest;
import com.birdeye.messenger.dto.ConversationStatus;
import com.birdeye.messenger.dto.ConversationStatusRequest;
import com.birdeye.messenger.dto.ConversationWebhookEventDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.CustomerLite;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.GenericFirebaseMessage;
import com.birdeye.messenger.dto.GetCountResponse;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.ModuleAccess;
import com.birdeye.messenger.dto.TeamAssigneeDto;
import com.birdeye.messenger.dto.TidyUpCloseEvent;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.BulkOp;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WebhookEventEnum;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.MessengerContactMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.EventPublisherService;
import com.birdeye.messenger.service.FacebookMessageService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WebhookService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.ConversationCount;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.google.common.base.Preconditions;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConversationServiceImpl implements ConversationService {

	private final NexusService nexusService;
	private final MessengerContactService messengerContactService;
	private final MessengerContactRepository messengerContactRepository;
	private final FacebookMessageService facebookMessageService;
	private final SmsService smsService;
	private final ThreadPoolTaskExecutor poolExecutor;
	private final KafkaService kafkaService;
	private final BusinessService businessService;
	private final CommunicationHelperService communicationHelperService;
	private final WebhookService webhookService;
	private final FirebaseService fcmService;
	private final ConversationActivityService conversationActivityService;
	private final UserService userService;
	private final MessengerMessageService messengerMessageService;
	private final EventPublisherService eventPublisherService;
	private final ContactService contactService;
	private final RedisLockService redisLockService;
	private final ElasticSearchExternalService elasticSearchService;
    private final SecureMessagingAuthDAO secureMessagingAuthDAO;
    private final IWhatsAppTemplateService whatsAppTemplateService;
    @Lazy
    private final MigrationService migrationService;

	@Override
	@Async
	public void updateConversationStatus(MessengerContact messengerContact, Integer status,
			List<Integer> viewedByUsersFromES, Integer accountId, MessageRequest messageRequest) {
		if (messengerContact.getTag().equals(MessengerTagEnum.CAMPAIGN.getId())) {
			return;
		}

		List<ContactDocument> contactDocument = messengerContactService
				.getContactFromES(new MessangerBaseFilter(accountId, messengerContact.getId(), 1));
		ContactDocument contactFromES = contactDocument.get(0);
		if(null != status) {
			messengerContact.setTag(status);
			contactFromES.setC_tag(MessageTag.INBOX.getCode());
		}
		if(null != viewedByUsersFromES) {
			String userIds = StringUtils.join(viewedByUsersFromES, ',');
			messengerContact.setViewedBy(userIds);
			contactFromES.setViewedBy(viewedByUsersFromES);
		}
		messengerContactRepository.save(messengerContact);
		messengerContactService.updateContactOnES(messengerContact.getId(), contactFromES, accountId);
		fcmService.mirrorOnMobile(contactFromES);
		/*Map<String, Object> tag = new HashMap<>();
		tag.put("_t", new Date().getTime());*/
		if (!Objects.nonNull(messageRequest) || !messageRequest.isSwitchChatLocation()) {
			FirebaseDto firebaseDto = DtoToEntityConverter.fromContactDoc(contactFromES);
			nexusService.updateFirebaseDb(new GenericFirebaseMessage<>(firebaseDto, "messenger/" + accountId));
		}
	}
	
	private static ActivityType getActivityType(Integer from, Integer to) {
		/*if (MessengerTagEnum.UNREAD.getId() == to) {
			return ActivityType.UNREAD;
		}*/
		if (MessengerTagEnum.DONE.getId() == to) {
			return ActivityType.CLOSED;
		}
		if (MessengerTagEnum.INBOX.getId() == to && MessengerTagEnum.DONE.getId() == from) {
			return ActivityType.REOPENED;
		}
		return null;
	}
	
	/**
	 * Update Tag
	 * 1. messenger_contact update
	 * 2. ES doc update (a) Conversation (b) Activity
	 * 3. Firebase event db sync
	 */
	@Override
    public void updateConversationTag(Integer accountId, Integer userId, Integer conversationId, Integer status) {
		log.debug("updateConversationTag: accountId {}, userId {}, mcId {}, tag {}", accountId, userId, conversationId, status);
		if(Objects.isNull(status)) return;
		MessengerContact messengerContact = messengerContactService.findById(conversationId);
		int oldTag = messengerContact.getTag();
		int newTag = status;
		Integer currentTag = messengerContact.getTag();
		// Null tag handling. No support from Campaign --> Inbox via UI path. That should only happen via messenger context.
		if (Objects.nonNull(currentTag) && (currentTag.equals(status))) {
			return;
		}

		// ES update only for Tag.
		ContactDocument contactFromES = new ContactDocument();
		
		List<Integer> viewedByUsers = StringUtils.isNotBlank(messengerContact.getViewedBy())
				? MessengerUtil.convertCommaSeparatedStringToList(messengerContact.getViewedBy())
				: new ArrayList<>();
				
		if(status.equals(MessengerTagEnum.UNREAD.getId())) {
			if (contactFromES.getC_read()) {
				//Fetch all active users for this location
				Map<Integer, TeamAssigneeDto> userDetails = communicationHelperService.getValidUserDTOs(userId,
						accountId, messengerContact.getBusinessId());
				viewedByUsers = new ArrayList<>(userDetails.keySet());
			}
			viewedByUsers.remove(userId);
			contactFromES.setViewedBy(viewedByUsers);
			messengerContact.setViewedBy(StringUtils.join(viewedByUsers, ','));
			messengerContact.setIsRead(false);
			contactFromES.setC_read(false);
		}else if(status.equals(MessengerTagEnum.INBOX.getId())) {
			viewedByUsers.add(userId);
			contactFromES.setViewedBy(viewedByUsers);
			messengerContact.setViewedBy(StringUtils.join(viewedByUsers, ','));
		}
		
		//TODO: Validate tags?
		messengerContact.setTag(status);
		messengerContact.setUpdatedAt(new Date());
		
		contactFromES.setC_tag(status);
		
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		contactFromES.setUpdatedAt(df.format(messengerContact.getUpdatedAt()));
		messengerContactService.updateContactOnES(messengerContact.getId(), contactFromES, accountId);
		
		messengerContactRepository.save(messengerContact);

		// No notification, Firebase DB sync. Needs to be moved to FCMService
		/*Map<String, Object> tag = new HashMap<>();
		tag.put(messengerContact.getBusinessId() + "|_t", new Date().getTime());*/
		FirebaseDto firebaseDto = DtoToEntityConverter.fromContactDoc(contactFromES);
		nexusService.updateFirebaseDb(new GenericFirebaseMessage<>(firebaseDto, "messenger/" + accountId));


		UserDTO userDTO = userService.getUserDTO(userId);
		ActivityType activityType = getActivityType(currentTag, status);

		// NO ES STORE & DB SYNC FOR UNREAD ACTIVITY
		if (activityType != null) {
			List<ContactDocument> contactDocument = messengerContactService
					.getContactFromES(new MessangerBaseFilter(accountId, conversationId, 1));
			// SYNC DB FOR UNREAD ACTIVITY ONLY WEB
			// TODO : FOR MOBILE
			// ON PRODUCTION THERE IS NO SUPPORT FOR UNREAD ACTIVITY
			ConversationActivity activity = conversationActivityService.create(activityType, userDTO);
			messengerMessageService.saveMessengerActivity(activity, conversationId, userDTO);

			MessageDocument activityDoc = activity()
					.withActivityTypeAndMId(activityType, String.valueOf(activity.getId()))
					.createdBy(
							new MessageDocument.UserDetail(userDTO.getId(), MessengerUtil.buildUserName(userDTO)))
					.at(activity.getCreatedDate())
					.forContactIdAndAccountId(String.valueOf(messengerContact.getId()), accountId, messengerContact.getBusinessId()).build();
			MessageDocument messageDocument = messengerContactService.addNewMessageDocOnES(activityDoc,
					activityDoc.getM_id() + "_a");


			if (CollectionUtils.isEmpty(contactDocument) || messageDocument == null) {
				log.info("Contact document / Message document is empty while updating tag");
				return;
			}
			fcmService.pushToFireBase(contactDocument.get(0), messageDocument, null, null);
			ConversationWebhookEventDTO conversationWebhookDTO = webhookService
					.mapMessengerContactToConversationWebhookDTO(messengerContact);
			webhookService.publishMessengerWebhookEvent(conversationWebhookDTO, accountId,
					WebhookEventEnum.CONVERSATION_UPDATED.getName(), messengerContact.getBusinessId());
		}
		
		// Event update.
		try {
			String currentAssignmentType = messengerContact.getAssignmentType()==null?Constants.Assignment.USER_ASSIGNED:messengerContact.getAssignmentType().name();
			IdentityDTO assignee = new IdentityDTO(messengerContact.getCurrentAssignee(), messengerContact.getCurrentAssigneeName(), currentAssignmentType, messengerContact.getCurrentAssigneeEmailId());
			ConversationTagChangeEvent conversationChangeTagEvent = new ConversationTagChangeEvent(accountId,
					messengerContact.getBusinessId(), oldTag, newTag, userId, conversationId, assignee);
			
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.TAG_UPDATE, conversationId, conversationChangeTagEvent);
		} catch (Exception e) {
			log.error("Some error occured while pushing tag update event",e);
		}
	}
	
	@Override
	public ConversationDetails getCustomerConversationDetailsFromElastic(Integer messageId, Integer customerId,
			BusinessDTO businessDTO) throws ParseException {

		Integer enterpriseId = businessDTO.getEnterpriseId() == null ? businessDTO.getBusinessId() : businessDTO.getEnterpriseId();
		
		List<ContactDocument> contactDocuments;
		List<MessageDocument> messageDocument = new ArrayList<>();
		ContactDocument contactDocument = new ContactDocument();

		
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("size", 1);
		dataModel.put("from", 0);
		dataModel.put("c_id", customerId.toString());
		dataModel.put("smsId", messageId.toString());
		

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_SEARCH, dataModel).build();

		contactDocuments = elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
		if (!CollectionUtils.isEmpty(contactDocuments)) {
			contactDocument = contactDocuments.get(0);
		}
		// TODO : What if no contactDocuments found

		esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSAGE_BY_ID, dataModel).build();

		messageDocument = elasticSearchService.searchByQuery(esRequest, MessageDocument.class);
		contactDocument.setC_messages(messageDocument);

		return new ConversationDetails(contactDocument, businessDTO);
	}

	@Override
	public ContactMessageDTO getContactMessage(Integer messageId, String messageType) {

		Integer businessId = null;
		Integer customerId = null;
		Integer messengerContactId = null;
		String messageBody = null;
		Integer isMessageEncrypted = null;
		String sender = "";
		String reciever = "";

		if (StringUtils.isNotBlank(messageType) && (messageType.equalsIgnoreCase("facebook")
				|| messageType.equalsIgnoreCase("FACEBOOK_SEND") || messageType.equalsIgnoreCase("FACEBOOK_RECEIVE"))) {

			FacebookMessage facebookMessage = facebookMessageService.getFacebookMessageUsingMessageId(messageId);
			businessId = facebookMessage.getBusinessId();
			customerId = facebookMessage.getCustomerId();
			messengerContactId = facebookMessage.getCustomerId();
			messageBody = facebookMessage.getMessageBody();
			isMessageEncrypted = facebookMessage.getEncrypted();
			sender = facebookMessage.getSenderFacebookId();
			reciever = facebookMessage.getRecipientFacebookId();

			if (businessId == null) {
				log.error("No business found for message with messageId: {}", messageId);
				throw new IllegalArgumentException("No business found for message with messageId: " + messageId);
			}
			if (customerId == null) {
				log.error("No customer found for message with messageId: {}", messageId);
				throw new IllegalArgumentException("No customer found for message with messageId: " + messageId);
			}
		} else {
			Sms sms = smsService.findById(messageId);
			businessId = sms.getBusinessId();
			customerId = sms.getCustomerId();
			messageBody = sms.getMessageBody();
			isMessageEncrypted = sms.getEncrypted();
			sender = sms.getFromNumber();
			reciever = sms.getToNumber();
			MessengerContact messengerContact = messengerContactService.findByCustomerId(customerId);
			if (messengerContact != null)
				messengerContactId = messengerContact.getId();
		}
		// TODO: How can we save decryption?
		messageBody = EncryptionUtil.decryptMessage(isMessageEncrypted, messageBody, sender, reciever);
		return new ContactMessageDTO(businessId, customerId, messageBody, messengerContactId,messageType,messageId);
	}

	@Override
	@SuppressWarnings("unchecked")
    public ConversationResponse getConversationV2(ConversationRequest conversationRequest, Integer accountId,
            Integer userId) {
		log.debug("getConversationV2: request body {}", conversationRequest);
		
		if(conversationRequest.getStartIndex() != null && conversationRequest.getStartIndex() < 0) {
			// BIRDEYE-116299
			log.info("getConversationV2: Negative start index for user {} and account {}", userId, accountId);
			return null;
		}
		modifyForReadUnreadConversation(conversationRequest,userId);
		Boolean validDateFilters = conversationRequest.getTidyUpFilters() != null?checkForDateIntersectionAndSetApplicableDates(conversationRequest):true;
		if(Boolean.FALSE.equals(validDateFilters)) {
			return null;
		}
		
		ImmutablePair<List<ContactDocument>, Long> conversationResponseTuple = getConversationsFromES(conversationRequest, accountId);
		List<ContactDocument> contactDocuments = conversationResponseTuple.getLeft();
		
		ConversationResponse response = new ConversationResponse();
		response.setCount(conversationResponseTuple.getRight());
		
		if(Objects.nonNull(conversationRequest.getMoveConversationToTop())) {
			reOrderConversations(contactDocuments, conversationRequest.getMoveConversationToTop(), accountId, conversationRequest.getFilters()!=null ? conversationRequest.getFilters().getInboxSnapShotTime() : null);
		}
		
		if(CollectionUtils.isNotEmpty(contactDocuments)) {
			List<MessengerContactMessage> respConversations = prepareConversationsForResponse(contactDocuments,userId,accountId,conversationRequest);
			response.setMessengerContactMessages(respConversations);
		}
		return response;
	}

	private List<MessengerContactMessage> prepareConversationsForResponse(List<ContactDocument> contactDocuments, Integer userId, Integer accountId,ConversationRequest request) {
		List<Integer> ids = contactDocuments.stream().map(c->c.getM_c_id()).collect(Collectors.toList());
		final Map<Integer, MessengerContact> idToMC = messengerContactService.findByIdsIn(ids);
		List<Integer> bidsOfLoadedConversations = contactDocuments.stream().map(c -> c.getB_id()).distinct().collect(Collectors.toList());
		List<WhatsAppOnboardingStatus> businessOnboardingStatus = whatsAppTemplateService.getWhatsappOnboardingStatusByBusinessIds(accountId, bidsOfLoadedConversations);
		List<Integer> bidsOfWhatsappOnboarded = CollectionUtils.isNotEmpty(businessOnboardingStatus)
		        ? businessOnboardingStatus.stream().map(WhatsAppOnboardingStatus::getBusinessId).distinct().collect(Collectors.toList())
		        : Collections.emptyList();
		
		Map<Integer, BizLite> businessDataForMessenger = businessService.getBizLite(bidsOfLoadedConversations, accountId);
		boolean excludeUnattributedConversation=request.isExcludeUnattributedConversation();
        List<Integer> cIds = contactDocuments.stream().map(ContactDocument::getC_id).collect(Collectors.toList());
        Map<String, SecureMessagingLink> secureMessagingLinkResponseMap = secureMessagingAuthDAO
                .getMessengerContactSecureMessagingLinkResponseDTOsByMcIdsAndCIds(ids, cIds);
        List<MessengerContactMessage> conversations = contactDocuments.stream().map(doc -> {
			if(doc.getC_id()==null && BooleanUtils.isTrue(excludeUnattributedConversation)) {
				return null;
			}
			MessengerContact messengerContact = idToMC.get(doc.getM_c_id());
			String facebookId = Objects.nonNull(messengerContact) ? messengerContact.getFacebookId() : null;
			String googleConvId = Objects.nonNull(messengerContact) ? messengerContact.getGoogleConversationId() : null;
			String instagramConvId = Objects.nonNull(messengerContact) ? messengerContact.getInstagramConversationId() : null;
			String appleConvId = Objects.nonNull(messengerContact) ? messengerContact.getAppleConversationId() : null;
			String twitterConvId = Objects.nonNull(messengerContact) ? messengerContact.getTwitterConversationId() : null;
			String whatsappConvId = Objects.nonNull(messengerContact) ? messengerContact.getWhatsappConversationId() : null;
			if (CollectionUtils.isNotEmpty(doc.getViewedBy()))
				doc.setViewedBy(doc.getViewedBy());
			MessengerContactMessage messengerContactMessage = new MessengerContactMessage(doc, 1, facebookId, userId, request.getFilters().getType(),request.getAccess().isReviews(),request.getAccess().isSurveys(),googleConvId,instagramConvId,appleConvId,twitterConvId,whatsappConvId);
			if (ObjectUtils.isNotEmpty(messengerContactMessage.getCustomer()) && ObjectUtils.isEmpty(messengerContactMessage.getCustomer().getWhatsappConvId())) {
				if (CollectionUtils.isNotEmpty(bidsOfWhatsappOnboarded) && bidsOfWhatsappOnboarded.contains(messengerContactMessage.getBusinessId())) {
					String waCustId = messengerContactMessage.getCustomer().getPhone();
					messengerContactMessage.getCustomer().setWhatsappConvId(waCustId);
				}
			}
			setBusinessName(messengerContactMessage, businessDataForMessenger); //TODO cache BusinessName for businessIds
            if (Objects.nonNull(messengerContact)) {
                Integer mcId = messengerContact.getId();
                Integer cId = messengerContact.getCustomerId();
                if (!MessengerUtil.checkIfAnyValueIsNull(mcId, cId)) {
                    messengerContactMessage.setSecureMessagingInitiated(Objects
                            .nonNull(secureMessagingLinkResponseMap.get(String.valueOf(mcId + "-" + cId))));
                }
            }
			return messengerContactMessage;
		}).filter(doc->doc!=null).collect(Collectors.toList());

		return conversations;
	}

	private ImmutablePair<List<ContactDocument>, Long> getConversationsFromES(ConversationRequest conversationRequest, Integer accountId) {
		ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(conversationRequest);
		Integer withoutScript = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("enable_new_polling_query", 1);
		if(Objects.nonNull(conversationRequest.getFilters().getInboxSnapShotTime()) && Boolean.TRUE.equals(conversationRequest.getFilters().isConversationPollCount())){
			if(withoutScript==1) {
				freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_POLLING_WITHOUT_SCRIPT);
			}else{
				freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_POLLING);
			}
		}else if(Objects.nonNull(conversationRequest.getFilters().getInboxSnapShotTime())){
            if(withoutScript==1) {
				freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V5);
            }else{
                freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V4);
            }
		}else {
			freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V3);
		}
		log.debug("getConversationV2: request {}", freeMarkerData);
		boolean success = false;
		Long total = null;
		List<ContactDocument> contactDocuments = new ArrayList<ContactDocument>();
		freeMarkerData.setSourceExcludes("\"reviews\""+","+"\"surveyResponses\""+","+"\"payments\""+","+"\"appointments\"");
		if(conversationRequest.getFilters()!=null && conversationRequest.getFilters().getType()!=ConversationView.MESSAGE) {
			ElasticData<ContactDocument> conversationsWithInnerHits = messengerContactService.getConversationDataWithInnerHits(freeMarkerData, accountId);
			success = conversationsWithInnerHits.isSucceeded();
			if(!success) {
				log.error("getConversationV2: Failed to fetch data from ES");
				throw new MessengerException("Failed to fetch conversation data from ES");
			}
			total = conversationsWithInnerHits.getTotal();
			if (CollectionUtils.isNotEmpty(conversationsWithInnerHits.getResults())) {
				contactDocuments = conversationsWithInnerHits.getResults();
			}
		} else {
			ElasticData<ContactDocument> conversations = messengerContactService.getConversationData(freeMarkerData, accountId);
			success = conversations.isSucceeded();
			if(!success) {
				log.error("getConversationV2: Failed to fetch data from ES");
				throw new MessengerException("Failed to fetch conversation data from ES");
			}
			total = conversations.getTotal();
			contactDocuments = conversations.getResults();
		}
		return ImmutablePair.of(contactDocuments, total);
	}

	private void modifyForReadUnreadConversation(ConversationRequest conversationRequest, Integer userId) {
		
		if("UNREAD".equals(conversationRequest.getFilters().getConversationStatus())) {
			conversationRequest.getFilters().setConversationStatus("OPEN");
			if(conversationRequest.isWeb()) {
				conversationRequest.getFilters().setUserId(String.valueOf(userId));
				conversationRequest.getFilters().setIsRead(true);
				conversationRequest.getFilters().setReadStatus("UNREAD");
			}
		}
		
		String readStatus = conversationRequest.getTidyUpFilters() != null ? conversationRequest.getTidyUpFilters().getReadStatus() : null;
		if ("UNREAD".equalsIgnoreCase(readStatus)) {
			if(conversationRequest.isWeb()) {
				conversationRequest.getFilters().setUserId(String.valueOf(userId));
				conversationRequest.getFilters().setIsRead(true);
				conversationRequest.getFilters().setReadStatus("UNREAD");
			}
		} else if ("READ".equalsIgnoreCase(readStatus)) {
			if(conversationRequest.isWeb()) {
				conversationRequest.getFilters().setUserId(String.valueOf(userId));
				conversationRequest.getFilters().setIsRead(true);
				conversationRequest.getFilters().setReadStatus("READ");
			}
		}
	}

	@Override
	public boolean checkForDateIntersectionAndSetApplicableDates(ConversationRequest conversationRequest) {
		Assert.notNull(conversationRequest.getTidyUpFilters(), "Tidy up filters can't be null");
		ConversationView viewType = conversationRequest.getFilters().getType();
		String tidyUpFilterStartDate = StringUtils.isBlank(conversationRequest.getTidyUpFilters().getOptStartDate())?"1971-01-01 01:00:00":conversationRequest.getTidyUpFilters().getOptStartDate();
		String tidyUpFilterEndDate = StringUtils.isBlank(conversationRequest.getTidyUpFilters().getEndDate())?DateUtils.getCurrentDateTime(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS):conversationRequest.getTidyUpFilters().getEndDate();
		if((viewType == ConversationView.RECENT || viewType == ConversationView.LEAD || viewType == ConversationView.MESSAGE) && conversationRequest.getFilters().allTime()) {
			conversationRequest.getFilters().setStartDate(tidyUpFilterStartDate);
			conversationRequest.getFilters().setEndDate(tidyUpFilterEndDate);
			return true;
		} else if(viewType == ConversationView.REVIEW && conversationRequest.getFilters().getReviewFilter()!=null && "all".equalsIgnoreCase(conversationRequest.getFilters().getReviewFilter().getTimePeriodSelected()))
		{
			ReviewFilter reviewFilter = conversationRequest.getFilters().getReviewFilter()!=null?conversationRequest.getFilters().getReviewFilter():new ReviewFilter();
			reviewFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterStartDate));
			reviewFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterEndDate));
			conversationRequest.getFilters().setReviewFilter(reviewFilter);
			return true;
		}else if(viewType == ConversationView.SURVEY && conversationRequest.getFilters().getSurveyFilter()!=null && "all".equalsIgnoreCase(conversationRequest.getFilters().getSurveyFilter().getTimePeriodSelected()))
		{
			SurveyFilter surveyFilter = conversationRequest.getFilters().getSurveyFilter()!=null?conversationRequest.getFilters().getSurveyFilter():new SurveyFilter();
			surveyFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterStartDate));
			surveyFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterEndDate));
			conversationRequest.getFilters().setSurveyFilter(surveyFilter);
			return true;
		}
		else if(viewType == ConversationView.PAYMENT && conversationRequest.getFilters().getPaymentFilter()!=null && "all".equalsIgnoreCase(conversationRequest.getFilters().getPaymentFilter().getTimePeriodSelected())) {
			ConversationFilter.PaymentFilter paymentFilter = conversationRequest.getFilters().getPaymentFilter()!=null?conversationRequest.getFilters().getPaymentFilter():new ConversationFilter.PaymentFilter();
			paymentFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterStartDate));
			paymentFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterEndDate));
			conversationRequest.getFilters().setPaymentFilter(paymentFilter);
			return true;
		}
		else if(viewType == ConversationView.APPOINTMENT && conversationRequest.getFilters().getAppointmentFilter()!=null && "all".equalsIgnoreCase(conversationRequest.getFilters().getAppointmentFilter().getTimePeriodSelected())) {
			ConversationFilter.AppointmentFilter appointmentFilter = conversationRequest.getFilters().getAppointmentFilter()!=null?conversationRequest.getFilters().getAppointmentFilter():new ConversationFilter.AppointmentFilter();
			appointmentFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterStartDate));
			appointmentFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(tidyUpFilterEndDate));
			conversationRequest.getFilters().setAppointmentFilter(appointmentFilter);
			return true;
		}
		else if(viewType == ConversationView.SPAM && conversationRequest.getFilters().getSpamFilter()!=null && "all".equalsIgnoreCase(conversationRequest.getFilters().getSpamFilter().getTimePeriodSelected())) {
			ConversationFilter.SpamFilter spamFilter = conversationRequest.getFilters().getSpamFilter()!=null?conversationRequest.getFilters().getSpamFilter():new ConversationFilter.SpamFilter();
			spamFilter.setStartDate(tidyUpFilterStartDate);
			spamFilter.setEndDate(tidyUpFilterEndDate);
			conversationRequest.getFilters().setSpamFilter(spamFilter);
			return true;
		}
		else if(viewType == ConversationView.RECENT || viewType == ConversationView.LEAD || viewType == ConversationView.MESSAGE) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getStartDate(), conversationRequest.getFilters().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				conversationRequest.getFilters().setStartDate(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
				conversationRequest.getFilters().setEndDate(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		} else if(viewType == ConversationView.REVIEW) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getReviewFilter().getStartDate(), conversationRequest.getFilters().getReviewFilter().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			ReviewFilter reviewFilter = conversationRequest.getFilters().getReviewFilter()!=null?conversationRequest.getFilters().getReviewFilter():new ReviewFilter();
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				reviewFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				reviewFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				conversationRequest.getFilters().setReviewFilter(reviewFilter);
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		}else if(viewType == ConversationView.SURVEY) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getSurveyFilter().getStartDate(), conversationRequest.getFilters().getSurveyFilter().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			SurveyFilter surveyFilter = conversationRequest.getFilters().getSurveyFilter()!=null?conversationRequest.getFilters().getSurveyFilter():new SurveyFilter();
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				surveyFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				surveyFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				conversationRequest.getFilters().setSurveyFilter(surveyFilter);
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		}
		else if(viewType == ConversationView.PAYMENT) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getPaymentFilter().getStartDate(), conversationRequest.getFilters().getPaymentFilter().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			ConversationFilter.PaymentFilter paymentFilter = conversationRequest.getFilters().getPaymentFilter()!=null?conversationRequest.getFilters().getPaymentFilter():new ConversationFilter.PaymentFilter();
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				paymentFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				paymentFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				conversationRequest.getFilters().setPaymentFilter(paymentFilter);
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		}
		else if(viewType == ConversationView.APPOINTMENT) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getAppointmentFilter().getStartDate(), conversationRequest.getFilters().getAppointmentFilter().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			ConversationFilter.AppointmentFilter appointmentFilter = conversationRequest.getFilters().getAppointmentFilter()!=null?conversationRequest.getFilters().getAppointmentFilter():new ConversationFilter.AppointmentFilter();
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				appointmentFilter.setStartTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				appointmentFilter.setEndTimestamp(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS)));
				conversationRequest.getFilters().setAppointmentFilter(appointmentFilter);
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		}
		else if(viewType == ConversationView.SPAM) {
			DateRange conversationFilterDateRange = new DateRange(conversationRequest.getFilters().getSpamFilter().getStartDate(), conversationRequest.getFilters().getSpamFilter().getEndDate(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			DateRange tidyUpFilterDateRange = new DateRange(tidyUpFilterStartDate, tidyUpFilterEndDate, Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			ConversationFilter.SpamFilter spamFilter = conversationRequest.getFilters().getSpamFilter()!=null?conversationRequest.getFilters().getSpamFilter():new ConversationFilter.SpamFilter();
			Optional<DateRange> intersectionOpt = conversationFilterDateRange.intersection(tidyUpFilterDateRange);
			if(intersectionOpt.isPresent()) {
				DateRange applicableRange = intersectionOpt.get();
				spamFilter.setStartDate(applicableRange.getStart(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
				spamFilter.setEndDate(applicableRange.getEnd(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
				conversationRequest.getFilters().setSpamFilter(spamFilter);
				return true;
			} else {
				log.info("No intersection between tidy up range and conversation range for request:{}",conversationFilterDateRange);
				return false;
			}
		}
		return false;
	}

	/**
	 * Business name stored in contact document is not updated whenever business name is changed. Hence need to populated it again dynamically.
	 * @param messengerContactMessage
	 * @param businessDataForMessenger
	 */
	private void setBusinessName(MessengerContactMessage messengerContactMessage, Map<Integer, BizLite> businessDataForMessenger) {
		if (Objects.nonNull(businessDataForMessenger)) {
			BizLite bizLite = businessDataForMessenger.get(messengerContactMessage.getBusinessId());
			if (Objects.nonNull(bizLite)) {
				messengerContactMessage.setBusinessName(bizLite.getDisplayName());
			}
			else {
				log.info("getConversationV2: bizLite not found for businessID {}", messengerContactMessage.getBusinessId());
			}
		}
	}

	private void reOrderConversations(List<ContactDocument> contactDocuments, Integer contactToMovedToTop, Integer accountId, Long inboxSnapShotTime) {
		Integer index = null;
		for (int i = 0; i < contactDocuments.size(); i++) {
			ContactDocument doc = contactDocuments.get(i);
			if (doc.getM_c_id().equals(contactToMovedToTop)) {
				index = i;
			}
		}
		if (Objects.nonNull(index)) {
			//Do not push conv to top for inboxv2 performance
			if(Objects.isNull(inboxSnapShotTime)) {
				ContactDocument removedDoc = contactDocuments.get(index);
				if (contactDocuments.remove(removedDoc)) {
					contactDocuments.add(0, removedDoc);
				}
			}
			
		} else {
			MessengerFilter filter = new MessengerFilter();
			filter.setStartIndex(0);
			filter.setCount(1);
			filter.setAccountId(accountId);
			filter.setConversationId(contactToMovedToTop);
			List<ContactDocument> contactDocs = messengerContactService.getContactFromES(filter);
			if (CollectionUtils.isNotEmpty(contactDocs)) {
				ContactDocument contactDocument = contactDocs.get(0);
				contactDocuments.add(0, contactDocument);
			}
		}
	}

	@Override
	public Object getConversationsCount(ConversationRequest conversationRequest, Integer accountId,
			Integer userId) throws InterruptedException, ExecutionException {
		ConcurrentHashMap<String, Long> conversationCountMap=new ConcurrentHashMap<String, Long>();
		List<Future<?>> futures = new ArrayList<Future<?>>();
		
		ConversationRequest allCountRequest = createConversationCountRequest(conversationRequest,"ALL");
		Future<?> allResponse=poolExecutor.submit(() -> getConversationCount(allCountRequest,"ALL", accountId, userId,conversationCountMap));
		futures.add(allResponse);
		
		ConversationRequest allOpenCountRequest = createConversationCountRequest(conversationRequest,"OPEN");
		Future<?> allOpenResponse=poolExecutor.submit(() -> getConversationCount(allOpenCountRequest,"OPEN", accountId, userId,conversationCountMap));
		futures.add(allOpenResponse);
		
		ConversationRequest myOpenCountRequest = createConversationCountRequest(conversationRequest,"OPEN");
		myOpenCountRequest.getFilters().setAssignedTo(Collections.singletonList(userId));
		Future<?> myOpenResponse=poolExecutor.submit(() -> getConversationCount(myOpenCountRequest,"ASSIGNEDTO", accountId, userId,conversationCountMap));
		futures.add(myOpenResponse);
		// Blocking till all submited task is completed
		for (Future<?> future:futures) {
		   future.get();
		}
		log.debug("get conversation count response for accountId: {} and userId: {} is: {}", accountId,userId,conversationCountMap);
		return conversationCountMap;
	}
	
	
	@Override
	public Object getConversationsCountV2(ConversationRequest conversationRequest, Integer accountId,
			Integer userId) throws InterruptedException, ExecutionException {
		
		ConversationCount conversationCount = new ConversationCount()
				.setContext(accountId, userId)
				.setConversationRequest(conversationRequest)
				.setConversationCountFunc(this::getConversationV2)	
				.setTeamConversationCountFunc(account -> request -> team -> user -> addTeamsConversationsCount(account, request, team, user))				
				.setExcludedView(ConversationView.getMatchingEnums(conversationRequest.getExcludedConversationType()));
		
		GetCountResponse countResponse =  conversationCount.getConversationCountMap(poolExecutor);
	
		return countResponse;
		
	}

	private void addTeamsConversationsCount(Integer accountId,ConversationRequest conversationRequest,
			Map<String, Long> teams, Integer userId) {
		ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(conversationRequest);
		log.debug("getConversationV2: request {}", freeMarkerData);
		freeMarkerData.setFtlFile(Constants.Elastic.GET_TEAMS_CONVERSATIONS_COUNT);
		freeMarkerData.setCount(Constants.Elastic.COUNT_LIMIT);
		if(conversationRequest.isWeb()) {
			freeMarkerData.setUserId(userId.toString());
			freeMarkerData.setIsRead(true);
		}
		
		ElasticData conversationData = messengerContactService.getConversationData(freeMarkerData, accountId);
		SearchResponse response = conversationData.getSearchResponse();
		Terms teamsConvAggregations = response.getAggregations().get("teams_conv_count");
		List<? extends Bucket> buckets = teamsConvAggregations.getBuckets();
		buckets.forEach(bucket -> {
			teams.put(""+bucket.getKey(),bucket.getDocCount());
		});
	}

	private ConversationRequest createConversationCountRequest(ConversationRequest from,String conversationStatus) {
		ConversationRequest to=new ConversationRequest();
		to.setStartIndex(0);
		to.setCount(0);
		to.setFilters(new ConversationFilter());
		to.getFilters().setBusinessIds(from.getFilters().getBusinessIds());
		to.getFilters().setConversationStatus(conversationStatus);
		to.setWeb(from.isWeb());
		to.setAccess(new ModuleAccess(from.getAccess().isReviews(), from.getAccess().isSurveys()));
		return to;
	}

	private void getConversationCount(ConversationRequest conversationRequest,String conversationType,Integer accountId, Integer userId, Map<String, Long> conversationCountMap) {
        ConversationResponse conversationResponse = getConversationV2(conversationRequest, accountId, userId);
		conversationCountMap.put(conversationType, Objects.isNull(conversationResponse) || conversationResponse.getCount() == null ? 0 : conversationResponse.getCount());
	}

	@Override
	public MessengerContactMessage getConversationV2(Integer messengerContactId, Integer accountId, Integer userId, ConversationByIdRequest request) {

		MessengerContact messengerContact = messengerContactService.findById(messengerContactId);
		if(messengerContact == null) return null;
		List<Integer> userLocIds = request.getBusinessIds();
		if (CollectionUtils.isNotEmpty(userLocIds) && !userLocIds.contains(messengerContact.getBusinessId())) {
			log.info("getConversationV2: User {} doesn't have access to the location for mcId {}", userId, messengerContactId);
			throw new NotAuthorizedException(ErrorCode.USR_HAS_NO_LOC_ACCESS, ErrorCode.USR_HAS_NO_LOC_ACCESS.getErrorMessage());
		}
		ModuleAccess access = request.getAccess()==null?new ModuleAccess():request.getAccess();
		MessengerFilter filter = new MessengerFilter();
		filter.setStartIndex(0);
		filter.setCount(1);
		filter.setAccountId(accountId);
		filter.setConversationId(messengerContactId);
		List<ContactDocument> contactFromES = messengerContactService.getContactFromES(filter);
		if(CollectionUtils.isNotEmpty(contactFromES)) {
			List<Integer> bidsOfLoadedConversations = Collections.singletonList(messengerContact.getBusinessId());
			List<WhatsAppOnboardingStatus> businessOnboardingStatus = whatsAppTemplateService.getWhatsappOnboardingStatusByBusinessIds(accountId, bidsOfLoadedConversations);
			List<Integer> bidsOfWhatsappOnboarded = CollectionUtils.isNotEmpty(businessOnboardingStatus)
					? businessOnboardingStatus.stream().map(WhatsAppOnboardingStatus::getBusinessId).distinct().collect(Collectors.toList())
							: Collections.emptyList();
			ContactDocument doc = contactFromES.get(0);
			String facebookId = Objects.nonNull(messengerContact) ? messengerContact.getFacebookId() : null;
			String googleId = Objects.nonNull(messengerContact) ? messengerContact.getGoogleConversationId() : null;
			String instagramConvId = Objects.nonNull(messengerContact) ? messengerContact.getInstagramConversationId() : null;
			String appleConvId = Objects.nonNull(messengerContact) ? messengerContact.getAppleConversationId() : null;
			String twitterConvId = Objects.nonNull(messengerContact) ? messengerContact.getTwitterConversationId() : null;
			String whatsappConvId = Objects.nonNull(messengerContact) ? messengerContact.getWhatsappConversationId() : null;
			MessengerContactMessage messengerContactMessage = new MessengerContactMessage(doc, 1, facebookId, userId,
					ConversationView.RECENT, access.isReviews(), access.isSurveys(), googleId, instagramConvId,
					appleConvId,twitterConvId,whatsappConvId);
			if (ObjectUtils.isNotEmpty(messengerContactMessage.getCustomer()) && ObjectUtils.isEmpty(messengerContactMessage.getCustomer().getWhatsappConvId())) {
				if (CollectionUtils.isNotEmpty(bidsOfWhatsappOnboarded) && bidsOfWhatsappOnboarded.contains(messengerContactMessage.getBusinessId())) {
					String waCustId = messengerContactMessage.getCustomer().getPhone();
					messengerContactMessage.getCustomer().setWhatsappConvId(waCustId);
				}
			}
			Integer mcId = messengerContact.getId();
			Integer cId = messengerContact.getCustomerId();
			if (!MessengerUtil.checkIfAnyValueIsNull(mcId, cId)) {
				messengerContactMessage.setSecureMessagingInitiated(Objects
						.nonNull(secureMessagingAuthDAO.getMessengerContactSecureMessagingLinkByMcIdAndCId(mcId, cId)));
			}
			return messengerContactMessage;
		}
		return null;
	}
	
	@Override
	@Transactional
	public void updateStatus(Integer accountId, Integer userId, Integer conversationId,
			ConversationStatusRequest contactMessageRequest,Boolean isPublicApi) {
		log.debug("updateStatus: accountId {}, userId {}, mcId {}, contactMessageRequest {}", accountId, userId,
				conversationId, contactMessageRequest);
		if (Objects.isNull(contactMessageRequest))
			return;
		MessengerContact messengerContact = messengerContactService.findById(conversationId);
		validateMessengerContact(messengerContact);
        String lockKey = "";
        Optional<Lock> lockOpt = Optional.empty();
        if(messengerContact.getCustomerId()!=null) {
            lockKey = Constants.CUSTOMER_ID_PREFIX + messengerContact.getCustomerId();
        }else {
            lockKey = Constants.REVIEWER_ID_PREFIX + messengerContact.getReviewerId();
        }
        lockOpt = redisLockService.tryLock(lockKey,100,TimeUnit.MILLISECONDS);
        if (!lockOpt.isPresent()) {
        	log.warn(new ErrorMessageBuilder(ErrorCode.SMS_SEND_LOCK, ComponentCodeEnum.SMS)
					.message("Unable to acquire lock key: {}", lockKey).build());
            throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
        }
        try {
		int oldTag = messengerContact.getTag();
		Integer currentTag = messengerContact.getTag();
		
		Boolean read = contactMessageRequest.getRead();
		Boolean open = contactMessageRequest.getOpen();
		Integer status = null;
		if (null != open) {
			if (open) {
				// Reopen request : Change tag value to INBOX (1)
				status = MessengerTagEnum.INBOX.getId();
			} else {
				// Close request : Change tag value to DONE/CLOSED(3)
				status = MessengerTagEnum.DONE.getId();
			}
		}
		// Null tag handling. No support from Campaign --> Inbox via UI path. That should only happen via messenger context.
		if (Objects.nonNull(currentTag) && (currentTag.equals(status))) {
			log.info("Update status: {}  from and to are same | accountId: {} and conversationId: {}",status,accountId,conversationId);
			if(Boolean.TRUE.equals(isPublicApi)) {
				return;
			}
		}
		List<ContactDocument> contactDocument = messengerContactService
				.getContactFromES(new MessangerBaseFilter(accountId, conversationId, 1));
		ContactDocument contactFromES = contactDocument.get(0);
		
		if (null != read) {
			
			List<Integer> viewedByUsers = contactFromES.getViewedBy();
			if (viewedByUsers == null) {
				viewedByUsers = new ArrayList<>();
			}
			if (read) {
				if(MessengerTagEnum.UNREAD.getId() == messengerContact.getTag()) {
					messengerContact.setTag(MessengerTagEnum.INBOX.getId());	
					contactFromES.setC_tag(MessengerTagEnum.INBOX.getId());
				}
				// Add current user in viewedBy list
				if (!viewedByUsers.contains(userId)) {
					viewedByUsers.add(userId);
				}
			} else {
				if (messengerContact.getIsRead() != null && messengerContact.getIsRead()) {
					//Fetch all active users for this location
					Map<Integer, TeamAssigneeDto> userDetails = communicationHelperService.getValidUserDTOs(userId,
							accountId, messengerContact.getBusinessId());
					viewedByUsers = new ArrayList<>(userDetails.keySet());
				}
				// Remove current user from viewedBy list
				viewedByUsers.remove(userId);
				if(MessengerTagEnum.DONE.getId() != messengerContact.getTag() && 
						CollectionUtils.isEmpty(viewedByUsers)) {
					messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
					contactFromES.setC_tag(MessengerTagEnum.UNREAD.getId());
				}
				messengerContact.setIsRead(false);
				contactFromES.setC_read(false);
			}
			String viewedBy = null;
			if(viewedByUsers.size() > 0) {
				viewedBy = StringUtils.join(viewedByUsers, ',');
			}
			messengerContact.setViewedBy(viewedBy);
			contactFromES.setViewedBy(viewedByUsers);
		}
		
		if (null != status) {
			messengerContact.setTag(status);
			contactFromES.setC_tag(status);
		}
		messengerContact.setUpdatedAt(new Date());
		messengerContactRepository.save(messengerContact);
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		contactFromES.setUpdatedAt(df.format(messengerContact.getUpdatedAt()));
		boolean contactUpdated = messengerContactService.updateContactOnES(messengerContact.getId(), contactFromES, accountId);
		if(!contactUpdated) {
            throw new MessengerException(ErrorCode.ERROR_UPDATING_CONVERSATION);
        }
		fcmService.mirrorOnMobile(contactFromES);

		// No notification, Firebase DB sync.
		BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
		//fcmService.mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
		FirebaseDto firebaseDto = DtoToEntityConverter.fromContactDoc(contactFromES);
		firebaseDto.setAccountId(businessDTO.getAccountId());
		firebaseDto.setBusinessId(businessDTO.getBusinessId());
		fcmService.mirrorOnWeb(firebaseDto);

		if (null != status) {
			UserDTO userDTO = userService.getUserDTO(userId);
			ActivityType activityType = getActivityType(currentTag, status);
			// NO ES STORE & DB SYNC FOR UNREAD ACTIVITY
			if (activityType != null) {
				// SYNC DB FOR UNREAD ACTIVITY ONLY WEB
				// TODO : FOR MOBILE
				// ON PRODUCTION THERE IS NO SUPPORT FOR UNREAD ACTIVITY
				ConversationActivity activity = conversationActivityService.create(activityType, userDTO);
				messengerMessageService.saveMessengerActivity(activity, conversationId, userDTO);

				MessageDocument activityDoc = activity()
						.withActivityTypeAndMId(activityType, String.valueOf(activity.getId()))
						.createdBy(
								new MessageDocument.UserDetail(userDTO.getId(), MessengerUtil.buildUserName(userDTO)))
						.at(activity.getCreatedDate())
						.forContactIdAndAccountId(String.valueOf(messengerContact.getId()), accountId, messengerContact.getBusinessId()).build();
				MessageDocument messageDocument = messengerContactService.addNewMessageDocOnES(activityDoc,
						activityDoc.getM_id() + "_a");

				if (CollectionUtils.isEmpty(contactDocument) || messageDocument == null) {
					log.info("Contact document / Message document is empty while updating tag");
					return;
				}
				if(ActivityType.CLOSED.equals(activityType) && (Objects.isNull(messengerContact.getRtmPauseTagging())) ||Boolean.FALSE.equals(messengerContact.getRtmPauseTagging())){
					messengerContact.setCurrentStateForResTimeCalc(ConvStateForResTimeCalc.getInitialState());
					messengerContactRepository.save(messengerContact);
				}
				fcmService.pushToFireBase(contactDocument.get(0), messageDocument, null, null);
				eventPublisherService.produceEvent(activityType, accountId, Collections.singletonList(conversationId), contactDocument.get(0));
			}

			// Event update.
			try {
				String currentAssignmentType = messengerContact.getAssignmentType() == null
						? Constants.Assignment.USER_ASSIGNED
						: messengerContact.getAssignmentType().name();
				IdentityDTO assignee = new IdentityDTO(messengerContact.getCurrentAssignee(),
						messengerContact.getCurrentAssigneeName(), currentAssignmentType,
						messengerContact.getCurrentAssigneeEmailId());

				ConversationTagChangeEvent conversationChangeTagEvent = new ConversationTagChangeEvent(accountId,
						messengerContact.getBusinessId(), oldTag, messengerContact.getTag(), userId, conversationId,
						assignee);

				kafkaService.publishToKafkaAsync(KafkaTopicEnum.TAG_UPDATE, conversationId, conversationChangeTagEvent);
			} catch (Exception e) {
                log.error("Some error occured while pushing tag update event", e);
                }
             }
         } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }
        }
    }
	
	@Override
	public void updateMultipleConversationsInES(List<Integer> conversationIds, BulkOp op, Integer accountId, Integer userId) {
		Builder builder = null;
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> scriptData = new HashMap<>();
		data.put("conversationIds", ControllerUtil.toCommaSeparatedString(conversationIds));
		data.put("size", conversationIds.size());

		builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.CONTACT_INDEX)
				.queryTemplateFile(Constants.Elastic.GET_CONVERSATIONS_BY_ID).freeMarkerDataModel(data).routingId(accountId);

		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		String updatedAt = df.format(new Date());
		
		if(op == BulkOp.CLOSE) {
			scriptData.put("inline", "ctx._source.c_tag=3;ctx._source.updatedAt='"+updatedAt+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
		} else if(op == BulkOp.READ) {
			String inlineValue = "if(ctx._source.containsKey('viewedBy')){if(!ctx._source.viewedBy.contains("+userId+")){ctx._source.viewedBy.add("+userId+");ctx._source.updatedAt='"+updatedAt+"';ctx._source.c_tag=1;ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'}} else {ctx._source.viewedBy=["+userId+"];ctx._source.updatedAt='"+updatedAt+"';ctx._source.c_tag=1;ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'}";
			scriptData.put("inline", inlineValue);
		} else if(op == BulkOp.UNREAD) {
			String inlineValue = "if(ctx._source.containsKey('viewedBy')){ctx._source.viewedBy.removeAll(Collections.singleton("+userId+"));ctx._source.updatedAt='"+updatedAt+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'}";
			scriptData.put("inline", inlineValue);
		} else if(op == BulkOp.TAG) {
			String inlineValue = "ctx._source.c_tag=2;ctx._source.updatedAt='"+updatedAt+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'";
			scriptData.put("inline", inlineValue);
		}
		builder.scriptParam(scriptData);
		boolean response = elasticSearchService.updateByQuery(builder.build());
		if(!response) {
			log.error("Error occured while doing bulk operation :{} for user:{} accountId:{}", op.name(), userId, accountId);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		if(op == BulkOp.CLOSE) {
			eventPublisherService.produceEvent(ActivityType.CLOSED, accountId, conversationIds, null);
		}
	}

    @Override
    public boolean deleteConversation(Integer mcId, Integer routingId, RefreshPolicy refreshPolicy) {
        ESDeleteByIdRequest esDeleteByIdRequest = ESDeleteByIdRequest.builder()
                .id(String.valueOf(mcId)).index(Constants.Elastic.CONTACT_INDEX)
                .routingId(String.valueOf(routingId)).refreshPolicy(refreshPolicy).build();
        
        String id = elasticSearchService.deleteDocumentById(esDeleteByIdRequest);
        
        return StringUtils.isNotBlank(id);
    }

    @Override
    public boolean deleteConversations(List<Integer> mcIds, Integer routingId) {
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("mcids", ControllerUtil.toCommaSeparatedString(mcIds));
        dataModel.put("size", mcIds.size());
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
            .addRoutingId(routingId)
            .addTemplateAndDataModel(Constants.Elastic.DELETE_CONTACT_BY_MCIDS, dataModel)
            .addIndex(Constants.Elastic.CONTACT_INDEX)
            .build();
        return elasticSearchService.deleteByQuery(esRequest);
    }
    
    @Override
    public void removeReviewFromOldConversation(ContactDocument conversation, Integer accountId, Integer reviewId) {
		if(CollectionUtils.isNotEmpty(conversation.getReviews()) && conversation.getReviews().size()==1){
			conversation.setReviews(null);
		}else {
			List<Review> reviews=conversation.getReviews().stream().filter(r-> !reviewId.equals(r.getId())).collect(Collectors.toList());
			conversation.setReviews(reviews);
		}
	}
    @Override
    public Long getLastReviewOrSurveyOn(ContactDocument conversation, Integer accountId, Integer msgId, String msgType){
    	Long lastMsgTime=null;
    	if(Constants.REVIEW.equals(msgType)) {
    		List<Review> reviews=conversation.getReviews();
    		if(CollectionUtils.isNotEmpty(reviews)) {
    			Optional<Review> revieOpt=reviews.stream().filter(r-> !msgId.equals(r.getId())).sorted(Comparator.comparingLong(Review::getRdate).reversed()).findFirst();
    			if(revieOpt.isPresent()) {
    				lastMsgTime=revieOpt.get().getRdate();
    			}
    		}
		}
		if(Constants.SURVEY_RESPONSE.equals(msgType)) {
			List<ContactDocument.SurveyResponse> surveyResponses=conversation.getSurveyResponses();
			if(CollectionUtils.isNotEmpty(surveyResponses)) {
				Optional<ContactDocument.SurveyResponse> surveyResponseOptional=surveyResponses.stream().filter(sr-> !msgId.equals(sr.getId())).sorted(Comparator.comparingLong(ContactDocument.SurveyResponse::getRespDate).reversed()).findFirst();
				if(surveyResponseOptional.isPresent()) {
					lastMsgTime=surveyResponseOptional.get().getRespDate();
				}
			}
		}

    	return lastMsgTime;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<ConversationStatus> getConversationStatus(Integer userId, List<Integer> cIdsFromUi, Integer accountId) {

		// initial checks -----
		Preconditions.checkArgument(null != userId && null != accountId, String.format("userId %d and accountId %d cannot be empty", userId, accountId));
		Preconditions.checkArgument(CollectionUtils.isNotEmpty(cIdsFromUi), "cIds must be provided");

		cIdsFromUi = new ArrayList(new HashSet<>(cIdsFromUi)); // remove duplicates

		// check with customer-service first if contact is available or not
		final List<CustomerLite> customerList = contactService.getByIds(new ArrayList<>(cIdsFromUi));
		List<Integer> cIdsReturnedByContactService = customerList.stream().map(CustomerLite::getId).collect(Collectors.toList());
		List<ConversationStatus> deletedConversations = new ArrayList<>();
		if(cIdsFromUi.removeAll(cIdsReturnedByContactService) || CollectionUtils.isEmpty(customerList)) {
			log.info("getConversationStatus: deleted conversations from contact-service", cIdsFromUi);
			deletedConversations = cIdsFromUi.stream()
					.map(cId -> new ConversationStatus(cId, "DELETED", null, 1, null,null))
					.collect(Collectors.toList());
			if(CollectionUtils.isEmpty(customerList)) return deletedConversations;
		}

		// prepare payload for Elastic query -------------------
		Map<String, Object> data = new HashMap<>();
		data.put("size", cIdsReturnedByContactService.size());
		data.put("cIds", ControllerUtil.toCommaSeparatedString(cIdsReturnedByContactService));
		Map<String, Object> esReq = new HashMap<>();
		esReq.put("data", data);
		ElasticData<ContactDocument> result = elasticSearchService.
				getDataFromElastic(new ESRequest.
						Builder(new ESRequest())
						.addIndex(Constants.Elastic.CONTACT_INDEX)
						.addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATIONS_READ_UNREAD_STATUS, esReq)
						.addSize(cIdsReturnedByContactService.size())
						.addRoutingId(accountId)
						.build(), ContactDocument.class
				);

		if(!result.isSucceeded()) throw new MessengerException("getConversationStatus: Failed to execute elastic query " + Constants.Elastic.GET_CONVERSATIONS_READ_UNREAD_STATUS);
		if(result.getTotal() == 0) log.info("getConversationStatus: no result found for the provided cIds", cIdsReturnedByContactService);
		List<ContactDocument> results = result.getResults();
		List<ConversationStatus> response = results.stream()
				.map(doc -> new ConversationStatus(doc, userId))
				.collect(Collectors.toList());

		response.addAll(deletedConversations);
		List<Integer> foundCIds = results.stream().map(ContactDocument::getC_id).collect(Collectors.toList());
		boolean removed = cIdsReturnedByContactService.removeAll(foundCIds);
		if(removed || CollectionUtils.isEmpty(foundCIds)) {
			log.info("getConversationStatus: docs/read_unread_status not found for cIds", cIdsReturnedByContactService);
			List<ConversationStatus> notFound = cIdsReturnedByContactService.stream()
					.map(cId -> new ConversationStatus(cId, "NOT_FOUND", null, 0, null,null))
					.collect(Collectors.toList());
			response.addAll(notFound);
			if(CollectionUtils.isEmpty(foundCIds)) return response;
		}
		response = setOptOutStatus(response, customerList);
		return response;
	}

	private List<ConversationStatus> setOptOutStatus(List<ConversationStatus> data, List<CustomerLite> customerLiteList) {
		Map<Integer, ConversationStatus> responseByCIds = new HashMap<>();
		for(ConversationStatus datum : data) {
			if(responseByCIds.isEmpty() || !responseByCIds.containsKey(datum.getCId())) {
				responseByCIds.put(datum.getCId(), datum);
			}
		}
		if(CollectionUtils.isEmpty(customerLiteList)) return data;
		customerLiteList.forEach(customerLite -> {
			ConversationStatus conversationStatus = responseByCIds.get(customerLite.getId());
			if(Objects.nonNull(conversationStatus)) {
				if (BooleanUtils.isFalse(customerLite.getMarketingSmsEnabled())
						&& BooleanUtils.isFalse(customerLite.getFeedbackSmsEnabled())
						&& BooleanUtils.isFalse(customerLite.getServiceSmsEnabled())
						&& BooleanUtils.isFalse(customerLite.getMarketingEmailEnabled())
						&& BooleanUtils.isFalse(customerLite.getFeedbackEmailEnabled())
						&& BooleanUtils.isFalse(customerLite.getServiceEmailEnabled())) {
					conversationStatus.setOptout(1);
				}
				if(Objects.nonNull(customerLite.getBlocked())) {
					conversationStatus.setBlocked(Boolean.TRUE.equals(customerLite.getBlocked()) ? 1 : 0);
				}
			}
		});
		return new ArrayList<>(responseByCIds.values());
	}

	@Override
	public MessengerContact getMcIdForCustomer(Integer cId, Integer businessId, String type) {
		Integer customerId = cId;
		if("ecId".equals(type)) {
			log.debug("getMcIdForCustomer: requested mcId for ecId [{}] and businessId [{}]", cId,businessId);
			customerId = contactService.findCustomerIdByEcIdAndBusinessId(cId, businessId);
			if(null == customerId) throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
		}
		MessengerContact messengerContact = messengerContactService.findByCustomerId(customerId);
		if (messengerContact != null) {
			try {
				BusinessDTO businessDTO = businessService.getBusinessLiteDTO(messengerContact.getBusinessId());
				Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(businessDTO.getAccountId(), messengerContact.getId());
				if (!contactDocumentOptional.isPresent()) {
					migrationService.migrateByMessengerContactId(messengerContact.getId(), true);
				}
			} catch (Exception e) {
				log.error("Error migrating CD for mcId: {}", messengerContact.getId(), e);
			}
			MessengerContact response = new MessengerContact();
			response.setId(messengerContact.getId());
			response.setUpdatedAt(null);
			return response;
		}
		//Create new MessengerContact
		Optional<Lock> lockOpt = Optional.empty();
		String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + customerId;
		lockOpt = redisLockService.tryLock(lockKeyCustomer,2,TimeUnit.SECONDS);
		try {
			if(lockOpt.isPresent()) {
				CustomerDTO customerDTO = null;
				try {
					customerDTO = contactService.findByIdNoCaching(customerId);
				} catch (Exception e) {
					log.info("Exception from contact-service while fetching data for customerId " + customerId, e);
					return null;
				}
				BusinessDTO businessDTO = businessService.getBusinessLiteDTO(customerDTO.getBusinessId());
				Preconditions.checkArgument(Objects.nonNull(customerDTO) && Objects.nonNull(businessDTO), "customerDTO %s and businessDTO %s must not be null", customerDTO, businessDTO);
				messengerContact = messengerContactService.createContact(customerId, customerDTO.getBusinessId());
				messengerContact.setLastMessage("");
				messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX, null);
				
				MessengerContact response = new MessengerContact(); // should I create another DTO. Is it required ??
				response.setId(messengerContact.getId());
				response.setUpdatedAt(null);
				return response;
			}
			else {
				throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
		}finally {
			if(lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());	
			}
		}
	}
	
	private void validateMessengerContact(MessengerContact messengerContact) {
        if(messengerContact==null) {
            throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        if(messengerContact.getCustomerId()==null && (messengerContact.getReviewerId()==null && messengerContact.getPaymentReferenceId()==null)) {
            throw new MessengerException(ErrorCode.INVALID_MESSENGER_CONTACT);
        }
    }

	@Override
	public Object getdashboardInboxCount(ConversationRequest conversationRequest, Integer accountId, Integer userId) throws InterruptedException, ExecutionException {
		ConcurrentHashMap<String, Long> quickFilters=new ConcurrentHashMap<String, Long>();
		List<Future<?>> futures = new ArrayList<Future<?>>();
		
		ConversationRequest messagesOpenCountRequest = createConversationCountRequest(conversationRequest,"UNREAD");
		messagesOpenCountRequest.getFilters().setType(ConversationView.MESSAGE);
		Future<?> messagesOpenResponse=poolExecutor.submit(() -> getConversationCount(messagesOpenCountRequest,"messageUnread", accountId, userId,quickFilters));
		futures.add(messagesOpenResponse);

		ConversationRequest assignedToMeOpenCountRequest = createConversationCountRequest(conversationRequest,"OPEN");
		assignedToMeOpenCountRequest.getFilters().setType(ConversationView.RECENT);
		assignedToMeOpenCountRequest.getFilters().setAssignedTo(Collections.singletonList(userId));
		Future<?> assignedToMeOpenCountResponse=poolExecutor.submit(() -> getConversationCount(assignedToMeOpenCountRequest,"assignedToMe", accountId, userId,quickFilters));
		futures.add(assignedToMeOpenCountResponse);
		
		
		ConversationRequest leadOpenCountRequest = createConversationCountRequest(conversationRequest,"OPEN");
		leadOpenCountRequest.getFilters().setType(ConversationView.LEAD);
		Future<?> leadOpenCountResponse=poolExecutor.submit(() -> getConversationCount(leadOpenCountRequest,"leadOpen", accountId, userId,quickFilters));
		futures.add(leadOpenCountResponse);
		
		if (conversationRequest.isReserveWithGoogle()) {
			ConversationRequest appointmentOpenCountRequest = createConversationCountRequest(conversationRequest,"OPEN");
			appointmentOpenCountRequest.getFilters().setType(ConversationView.APPOINTMENT);
			Future<?> appointmentOpenCountResponse=poolExecutor.submit(() -> getConversationCount(appointmentOpenCountRequest,"appointmentOpen", accountId, userId,quickFilters));
			futures.add(appointmentOpenCountResponse);
		}
		Future<?> assignmentCountLast30DaysResponse=poolExecutor.submit(() -> getAssignmentCountLast30DaysResponse(accountId, userId, quickFilters));
		futures.add(assignmentCountLast30DaysResponse);
		
		// Blocking till all submited task is completed
		for (Future<?> future:futures) {
		   future.get();
		}
		return quickFilters;


	}

	@Override
	public Long getConversationCountForFreemiumAccount(
			ConversationCountFreemiumAccount conversationCountFreemiumAccount) {
		log.debug("getConversationCountForFreemiumAccount: request body {}", conversationCountFreemiumAccount);
		Long count=0l;
		String birdeyePhoneNumber = String.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("birdeye_customer_happiness_phone","+***********"));

		Map<String, Object> templateData = new HashMap<>();
		templateData.put("accountId", conversationCountFreemiumAccount.getAccountId());
		templateData.put("startDate", DateUtils.getDateAsStringInUTC(conversationCountFreemiumAccount.getStartTime(), Constants.FORMAT_YYYY_MM_DD_HH_MM_SS));
		templateData.put("birdeyePhoneNumber", birdeyePhoneNumber);
		Map<String, Object> data = new HashMap<>();
		data.put("data", templateData);
		ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(conversationCountFreemiumAccount.getAccountId())
				.addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATION_COUNT_FOR_FREEMIUM_ACCOUNT, data).addSize(0)
				.build();
		SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
		if (searchResponse.status().getStatus() != 200) {
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		Terms conv_cnt = searchResponse.getAggregations().get("conv_cnt");
		if (Objects.nonNull(conv_cnt)) {
			List<? extends Bucket> buckets = conv_cnt.getBuckets();
			List<Integer> mcIds = buckets.stream().map(bucket -> Integer.valueOf(bucket.getKeyAsString()))
					.collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(mcIds)) {
				count=messengerContactRepository.findCountByIds(mcIds);
			}
		}
		return count;
	}
	@Override
    @SuppressWarnings("unchecked")
	public void generateEventForClosedConversationTidyUp(TidyUpCloseEvent request) {
		
		Map<String, Object> data = new HashMap<>();
		data.put("size", request.getConversationIds().size());
		data.put("mcIds", ControllerUtil.toCommaSeparatedString(request.getConversationIds()));
		Map<String, Object> esReq = new HashMap<>();
		esReq.put("data", data);
		ElasticData<ContactDocument> result = elasticSearchService.
				getDataFromElastic(new ESRequest.
						Builder(new ESRequest())
						.addIndex(Constants.Elastic.CONTACT_INDEX)
						.addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATION_CAMPAIGN_CLOSE_AUTOMATION, esReq)
						.addSize(request.getConversationIds().size())
						.addRoutingId(request.getAccountId())
						.build(), ContactDocument.class
				);
		List<ContactDocument> contactDocuments = result.getResults();
		contactDocuments.stream().forEach(contact -> {
			eventPublisherService.produceEvent(ActivityType.CLOSED, request.getAccountId(), Collections.singletonList(contact.getM_c_id()), contact);
		});
	}
	private void getAssignmentCountLast30DaysResponse(Integer accountId, Integer userId,
			Map<String, Long> assignmentCountMap) {

		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("userId", userId);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);
		ESRequest esRequestAC = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addTemplateAndDataModel(Constants.Elastic.GET_ASSIGNMENT_COUNT_LAST_30_DAYS, dataModel)
				.addRoutingId(accountId).build();
		SearchResponse searchResponseActiveCon = elasticSearchService.getSearchResult(esRequestAC);
		Aggregations assignedConAggregations = searchResponseActiveCon.getAggregations();
		ParsedCardinality assignedConvCount= assignedConAggregations.get("assigned_conv");

		assignmentCountMap.put("assignedConversationCount", assignedConvCount.getValue());
	}


	@Override
	public MessengerContact getOrCreateMCForSocial(ConversationBySocialId request) {
		String channel = request.getChannel();
		long startTime = System.currentTimeMillis();
		MessengerContact messengerContact = null;
		switch (channel) {
			case "FACEBOOK":
				messengerContact = messengerContactRepository.findByFacebookId(request.getSocialUserId(), request.getBusinessId());
				break;
			case "INSTAGRAM":
				messengerContact = messengerContactRepository.findByInstagramConversationId(request.getSocialUserId(), request.getBusinessId());
				break;
			case "GOOGLE":
				messengerContact = messengerContactRepository.findByGoogleConversationAndSubaccount(request.getSocialUserId(), request.getBusinessId()).get();
				break;
			case "APPLE":
				messengerContact = messengerContactService.getByAppleConversationIdAndSubaccountId(request.getSocialUserId(), request.getBusinessId()).get();
				break;
			case "TWITTER":
				messengerContact = messengerContactRepository.findByTwitterConversationId(request.getSocialUserId(), request.getBusinessId());
				break;
			default:
				throw new IllegalArgumentException("Invalid channel: " + channel);
		}
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("findingMessengerContactFor", startTime, endTime);
		if (messengerContact != null) {
			log.info("messenger contact already exist for social user with mcId: {}",messengerContact.getId());
			MessengerContact response = updateLastReceivedAt(messengerContact,request);
			return response;
		}
		BusinessDTO businessDTO = businessService.getBusinessDTO(request.getBusinessId());
		UserDetailsMessage userDetailsMessage = new UserDetailsMessage(request.getFirst_name(),null,request.getProfile_pic(),request.getSocialUserId());
		KontactoRequest kontactoRequest = null;
		switch(channel){
			case "FACEBOOK":
				kontactoRequest = createKontactoRequest(userDetailsMessage,request,businessDTO,"fb",KontactoRequest.FACEBOOK);
				break;
			case "INSTAGRAM":
				kontactoRequest = createKontactoRequest(userDetailsMessage,request,businessDTO,"ig",KontactoRequest.INSTAGRAM);
				break;
			case "GOOGLE":
				kontactoRequest = createKontactoRequest(userDetailsMessage,request,businessDTO,"gbmdummy",KontactoRequest.GOOGLE);
				break;
			case "APPLE":
				kontactoRequest = createKontactoRequest(userDetailsMessage,request,businessDTO,"appledummy",KontactoRequest.APPLE);
				break;
			case "TWITTER":
				kontactoRequest = createKontactoRequest(userDetailsMessage,request,businessDTO,"x-dummy",KontactoRequest.TWITTER);
				break;
			default:
				throw new IllegalArgumentException("Invalid channel: " + channel);
		}
		log.debug("Requesting contact service to get/create customer for" + request.getChannel() + "user Id {}", request.getSocialUserId());
		//need to confirm the id in user id for customer get or create -1 or -8
		CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
				businessDTO.getRoutingId(),
				-1);
		log.debug("Customer retrieved Id {} from contact service for" + request.getChannel() + " user Id {}", customerDTO.getId(),
				request.getSocialUserId());
		Preconditions.checkArgument(Objects.nonNull(customerDTO) && Objects.nonNull(businessDTO), "customerDTO %s and businessDTO %s must not be null", customerDTO, businessDTO);
		Optional<Lock> lockOpt = Optional.empty();
		String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
		lockOpt = redisLockService.tryLock(lockKeyCustomer,2,TimeUnit.SECONDS);
		try {
			if(lockOpt.isPresent()) {
				startTime = System.currentTimeMillis();
				messengerContact = messengerContactService.createContact(customerDTO.getId(), request);
				endTime = System.currentTimeMillis();
				LogUtil.logExecutionTime("creatingMessengerContact", startTime, endTime);
				messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX, null);
				MessengerContact response = new MessengerContact();
				response.setId(messengerContact.getId());
				response.setUpdatedAt(null);
				return response;
			}else {
				throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
		}finally {
			if(lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private KontactoRequest createKontactoRequest(UserDetailsMessage userDetailsMessage,ConversationBySocialId request,
												  BusinessDTO businessDTO,String mailSuffix,String source) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String custName = userDetailsMessage.getFirst_name();
		kontactoRequest.setName(custName);
		kontactoRequest.setEmailId(request.getSocialUserId() + "@" + mailSuffix + ".com");
		kontactoRequest.setSource(source);
		KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
		locationInfo.setCountryCode(businessDTO.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		kontactoRequest.setBusinessId(request.getBusinessId());
		return kontactoRequest;
	}
	
	@Transactional
	private MessengerContact updateLastReceivedAt(MessengerContact messengerContact,ConversationBySocialId request){
		Optional<Lock> lockOpt = Optional.empty();
		String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + messengerContact.getCustomerId();
		lockOpt = redisLockService.tryLock(lockKeyCustomer,2,TimeUnit.SECONDS);
		try {
			if(lockOpt.isPresent()) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date feedDate = Objects.nonNull(request.getFeedDate()) ? new Date(request.getFeedDate()) : new Date();
				LastMessageMetaData lastMessageMetadataPOJO = new LastMessageMetaData();
				if(Source.INSTAGRAM.name().equals(request.getChannel())){
					lastMessageMetadataPOJO.setLastIgSentAt(sdf.format(feedDate));
					lastMessageMetadataPOJO.setLastIgReceivedAt(sdf.format(feedDate));
				}else{
					lastMessageMetadataPOJO.setLastFbSentAt(sdf.format(feedDate));
					lastMessageMetadataPOJO.setLastFbReceivedAt(sdf.format(feedDate));
				}
				messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
				Long startTime = System.currentTimeMillis();
				MessengerContact response = messengerContactService.saveOrUpdateMessengerContact(messengerContact);
				Long endTime = System.currentTimeMillis();
                LogUtil.logExecutionTime("update messenger contact",startTime,endTime);
				BusinessDTO businessDTO = businessService.getBusinessDTO(request.getBusinessId());
				ContactDocument contactDocument = new ContactDocument();
				contactDocument.setLastMessageMetaData(lastMessageMetadataPOJO);
				messengerContactService.updateContactOnES(messengerContact.getId(),contactDocument,businessDTO.getAccountId());
				return messengerContact;
			}else {
				throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
		}finally {
			if(lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	@Override
	public Object getConversationsPollingCount(ConversationRequest conversationRequest, Integer accountId, Integer userId) throws InterruptedException, ExecutionException {
		ConversationResponse response = getConversationV2(conversationRequest, accountId, userId);
        response.setMessengerContactMessages(new ArrayList<>());
		response.setBusinessFbIntegrationStatusMap(new HashMap<>());
        return response;
	}
}
