package com.birdeye.messenger.service.impl;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.PulseSurveyAudit;
import com.birdeye.messenger.dao.repository.PulseSurveyAuditRepository;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.service.PulseSurveyAuditService;
import com.birdeye.messenger.util.JSONUtils;

/**
 * <AUTHOR>
 *
 */
@Service
public class PulseSurveyAuditServiceImpl implements PulseSurveyAuditService {

	@Autowired
	private PulseSurveyAuditRepository pulseSurveyAuditRepository;
	
	@Override
	public PulseSurveyAudit auditPulseSurveyResponse(Integer id, MessageDTO messageDTO) {
		PulseSurveyAudit audit = new PulseSurveyAudit(id, JSONUtils.toJSON(messageDTO));
		pulseSurveyAuditRepository.saveAndFlush(audit);
		return audit;
	}
	
	@Override
	public PulseSurveyAudit getPulseSurveyAudit(Integer id) {
		Optional<PulseSurveyAudit> audit = pulseSurveyAuditRepository.findById(id);
		if (audit.isPresent()) {
			return audit.get();
		}
		return null;
	}
}
