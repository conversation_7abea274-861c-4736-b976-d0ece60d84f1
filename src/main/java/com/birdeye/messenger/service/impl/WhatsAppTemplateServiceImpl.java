package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.whatsapp.*;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppOnboardingStatus;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppTemplateTokens;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppTemplates;
import com.birdeye.messenger.dao.repository.WhatsAppOnboardingStatusRepository;
import com.birdeye.messenger.dao.repository.WhatsAppTemplateTokensRepository;
import com.birdeye.messenger.dao.repository.WhatsAppTemplatesRepository;
import com.birdeye.messenger.dto.Base64File;
import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateUpdateWebhook.ExternalWhatsAppTemplateChangeDto;
import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateUpdateWebhook.ExternalWhatsAppTemplateUpdateDto;
import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateUpdateWebhook.ExternalWhatsAppTemplateValueDto;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.DoupService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import com.birdeye.messenger.util.MediaBase64Util;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LocaleUtil;
import com.birdeye.messenger.util.MessengerUtil;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class WhatsAppTemplateServiceImpl implements IWhatsAppTemplateService {
	
	@Autowired
	private WhatsAppTemplatesRepository whatsAppTemplatesRepo;
	
	@Autowired
	private WhatsAppTemplateTokensRepository whatsAppTemplateTokensRepo;
	
	@Autowired
	private WhatsAppOnboardingStatusRepository whatsAppOnboardingStatusRepo;
	
	@Autowired
	private SocialService socialService;
	
	@Autowired
	private DoupService doupService;
	
	enum TemplateState {
		active, in_review, inactive, rejected, needs_personalization;
	}
	

	/**
	 * Once the location is onboarded, check if templates already exist for the given WABA ID.
	 * If not, fetch all templates from Social for the given WABA ID in the event.
	 * Then, create or update the templates for all account IDs associated with the WABA ID.
	 */
	@Override
	public void processWhatsAppBusinessLocationOnboardEvent(WhatsAppBusinessLocationStatusEvent locationStatusEvent) {
		saveWhatsAppOnboardingStatus(locationStatusEvent);

		if (StringUtils.isNotBlank(locationStatusEvent.getType()) && locationStatusEvent.getType().equalsIgnoreCase("ONBOARD")) {
			if (StringUtils.isNotBlank(locationStatusEvent.getWaba_status()) && locationStatusEvent.getWaba_status().equalsIgnoreCase("APPROVED")) {
				List<WhatsAppTemplates> existingTemplates = whatsAppTemplatesRepo.findByAccountIdAndWabaIdAndStatus(locationStatusEvent.getAccountId(), 
						locationStatusEvent.getWabaId(), TemplateState.active.name());
				if (CollectionUtils.isEmpty(existingTemplates)) {

					// Fetch all account IDs associated with the WABA ID
					List<Integer> accountIds = whatsAppOnboardingStatusRepo.findAccountIdByWabaIdIn(Arrays.asList(locationStatusEvent.getWabaId()));

					List<WhatsAppTemplates> templatesList = whatsAppTemplatesRepo.findByAccountIdIn(accountIds);

					Map<String, WhatsAppTemplates> accIdTempIdToTempObjMap = new HashMap<>();
					if (CollectionUtils.isNotEmpty(templatesList)) {
						// Create a map of account ID and template ID to template object
						accIdTempIdToTempObjMap = templatesList.stream().collect(Collectors.toMap(
										template -> template.getAccountId() + "::" + template.getExtTemplateId(), template -> template, (n, o) -> n));
					}

					// Fetch all templates from Social for the given WABA ID
					List<ExternalWhatsAppTemplateDto> whatsAppTemplates = socialService.getAllTemplatesByWabaId(locationStatusEvent.getWabaId());

					for (Integer accountId : accountIds) {

						for (ExternalWhatsAppTemplateDto templateDto : whatsAppTemplates) {

							if (accIdTempIdToTempObjMap.containsKey(accountId + "::" + templateDto.getId())) {
								// If template found in DB, update it
								WhatsAppTemplates template = accIdTempIdToTempObjMap.get(accountId + "::" + templateDto.getId());
								updateExistingWhatsAppTemplate(templateDto, template);
							} else {
								// If template not found in DB, create a new one
								createNewWhatsAppTemplate(templateDto, accountId, locationStatusEvent.getWabaId());
							}
						}
					}
				} else {
					log.info("Templates already exist for wabaId: {}", locationStatusEvent.getWabaId());
				}

			} else {
				//TODO: PLAN - delete or mark all existing templates as inactive, waba gets disconnected
//				log.info("Location status is not connected, marking all existing templates as inactive: {}", locationStatusEvent.getWabaId());
//				whatsAppTemplatesRepo.UpdateStatusByWabaIdAndAccountId(TemplateState.inactive.name(), locationStatusEvent.getWabaId(), locationStatusEvent.getAccountId());
			}
		}

	}

	/**
	 * @param locationStatusEvent
	 */
	private void saveWhatsAppOnboardingStatus(WhatsAppBusinessLocationStatusEvent locationStatusEvent) {
		
		if (locationStatusEvent.getType().equalsIgnoreCase("ONBOARD")) {
			Optional<WhatsAppOnboardingStatus> onboardingStatusOp = whatsAppOnboardingStatusRepo.findByWabaIdAndPhoneNumberId(locationStatusEvent.getWabaId(),
					locationStatusEvent.getPhoneNumberId());
			
			if (!onboardingStatusOp.isPresent()) {
				WhatsAppOnboardingStatus onboardingStatus = new WhatsAppOnboardingStatus();
				onboardingStatus.setAccountId(locationStatusEvent.getAccountId());
				onboardingStatus.setBusinessId(locationStatusEvent.getBusinessId());
				onboardingStatus.setWabaId(locationStatusEvent.getWabaId());
				onboardingStatus.setWabaName(locationStatusEvent.getWabaName());
				onboardingStatus.setPhoneNumberId(locationStatusEvent.getPhoneNumberId());
				onboardingStatus.setWabaStatus(locationStatusEvent.getWaba_status());
				onboardingStatus.setStatus(locationStatusEvent.isStatus());
				onboardingStatus.setBusinessVerified(locationStatusEvent.getIsBusinessVerified());
				onboardingStatus.setMessagingLimit(locationStatusEvent.getMessagingLimit());
				onboardingStatus.setMetaBusinessId(locationStatusEvent.getMetaBusinessId());

				whatsAppOnboardingStatusRepo.save(onboardingStatus);
			} else {
				WhatsAppOnboardingStatus onboardingStatus = onboardingStatusOp.get();
				onboardingStatus.setBusinessId(locationStatusEvent.getBusinessId());

				if (StringUtils.isNotBlank(locationStatusEvent.getWabaName())) {
					onboardingStatus.setWabaName(locationStatusEvent.getWabaName());
				}
				if (StringUtils.isNotBlank(locationStatusEvent.getPhoneNumberId())) {
					onboardingStatus.setPhoneNumberId(locationStatusEvent.getPhoneNumberId());
				}
				if (StringUtils.isNotBlank(locationStatusEvent.getWaba_status())) {
					onboardingStatus.setWabaStatus(locationStatusEvent.getWaba_status());
				}
				if (locationStatusEvent.getIsBusinessVerified() != null) {
					onboardingStatus.setBusinessVerified(locationStatusEvent.getIsBusinessVerified());
				}
				if (StringUtils.isNotBlank(locationStatusEvent.getMessagingLimit())) {
					onboardingStatus.setMessagingLimit(locationStatusEvent.getMessagingLimit());
				}
				if (StringUtils.isNotBlank(locationStatusEvent.getMetaBusinessId())) {
					onboardingStatus.setMetaBusinessId(locationStatusEvent.getMetaBusinessId());
				}
				
				onboardingStatus.setStatus(locationStatusEvent.isStatus());

				whatsAppOnboardingStatusRepo.save(onboardingStatus);
			}

		} else if (locationStatusEvent.getType().equalsIgnoreCase("MESSAGING_LIMIT_UPDATE")) {
			Optional<WhatsAppOnboardingStatus> onboardingStatusOp = whatsAppOnboardingStatusRepo.findByAccountIdAndBusinessId(locationStatusEvent.getAccountId(), 
					locationStatusEvent.getBusinessId());
			
			if (onboardingStatusOp.isPresent()) {
				WhatsAppOnboardingStatus onboardingStatus = onboardingStatusOp.get();
				
				if (StringUtils.isNotBlank(locationStatusEvent.getType())) {
					onboardingStatus.setMessagingLimit(locationStatusEvent.getMessagingLimit());
				}

				whatsAppOnboardingStatusRepo.save(onboardingStatus);
			}
		}
	}

	/**
	 * return templates list to ui
	 */
	@Override
	public Map<String, Object> whatsAppTemplateList(Integer accountId, WhatsAppTemplateFilterRequestDto request, Integer page, Integer size, String sortBy, String order) {
		
		validateTemplateFilterRequest(request);

		String sortListBy = getTemplateListSortBy(sortBy);
        Sort sort = "asc".equalsIgnoreCase(order)? Sort.by(sortListBy).ascending():Sort.by(sortListBy).descending();		
        		
        Pageable pageReq = PageRequest.of(page, size, sort);
        
        Page<WhatsAppTemplateDto> templatePage = null;
        
        /**
         * if sortBy is wabaName then we need to fetch the templates from onboarding status table
         * because currently we are not storing wabaName in whatsapp_templates table
         */
		if (sortBy.equalsIgnoreCase("wabaName")) {
			templatePage = whatsAppOnboardingStatusRepo.getWhatsappTemplatesListSortByWabaName(accountId, request.getSearchStr(), request.getTemplateType(), 
					request.getStatus(), request.getLanguage(), request.getWabaName(), request.getBusinessId(), pageReq);
		} else {
			templatePage = whatsAppTemplatesRepo.getWhatsappTemplatesList(accountId, request.getSearchStr(), request.getTemplateType(), request.getStatus(), 
	        		request.getLanguage(), request.getWabaName(), request.getBusinessId(), pageReq);
		}
		
		List<WhatsAppTemplateDto> allTemplates = templatePage.getContent();
		
		List<WhatsAppOnboardingStatus> onboardingStatus = whatsAppOnboardingStatusRepo.findByAccountId(accountId);
		Map<String, List<Integer>> wabaIdToBusinessIdMap = onboardingStatus.stream().collect(Collectors.groupingBy(WhatsAppOnboardingStatus::getWabaId,
						Collectors.mapping(WhatsAppOnboardingStatus::getBusinessId, Collectors.toList())));
		
		List<WhatsAppTemplateTokens> templateTokens = whatsAppTemplateTokensRepo.findByTemplateIdIn(allTemplates.stream().map(WhatsAppTemplateDto::getId).collect(Collectors.toList()));
		Map<Integer, Integer> templateIdToTokensMap = templateTokens.stream().collect(Collectors.groupingBy(
						WhatsAppTemplateTokens::getTemplateId, Collectors.collectingAndThen(Collectors.toList(), List::size)));

		for (WhatsAppTemplateDto template : allTemplates) {
			template.setBusinessIds(wabaIdToBusinessIdMap.get(template.getWabaId()));
			template.setTokenCount(templateIdToTokensMap.getOrDefault(template.getId(), 0));
		}
		
		Map<String, Object> response = new HashMap<>();
		response.put("templates", allTemplates);
		response.put("totalCount", templatePage.getTotalElements());

		return response;
	}
	
	private void validateTemplateFilterRequest(WhatsAppTemplateFilterRequestDto request) {

		if (StringUtils.isBlank(request.getSearchStr())) {
			request.setSearchStr(null);
		}

		if (CollectionUtils.isEmpty(request.getTemplateType())) {
			request.setTemplateType(null);
		}

		if (CollectionUtils.isEmpty(request.getStatus())) {
			request.setStatus(null);
		}

		if (CollectionUtils.isEmpty(request.getLanguage())) {
			request.setLanguage(null);
		}

		if (CollectionUtils.isEmpty(request.getWabaName())) {
			request.setWabaName(null);
		}
	}
	
	private String getTemplateListSortBy(String sortBy) {
		switch (sortBy) {
			case "templateType":
				return "category";
			case "updatedOn":
				return "updated";
			case "name":
				return "templateName";
			case "status":
				return "status";
			case "language":
				return "language";
			case "wabaName":
				return "wabaName";
			default:
				return "updated";
		}
	}

	/**
	 * get templates details by id
	 * 
	 */
	@Override
	public WhatsAppTemplateDto getWhatsAppTemplateDetail(Integer templateId) {
		
		WhatsAppTemplateDto template = whatsAppTemplatesRepo.getWhatsappTemplateById(templateId).orElse(null);
		
		if (template != null) {
			List<WhatsAppTemplateTokens> templateTokens = whatsAppTemplateTokensRepo.findByTemplateId(template.getId());
			template.setTokenCount(templateTokens.size());
			
			if (CollectionUtils.isNotEmpty(templateTokens)) {
				Map<Integer, WACustomFieldDto> tokenMappings = new HashMap<>();
				for (int i = 0; i < templateTokens.size(); i++) {
					WhatsAppTemplateTokens templateToken = templateTokens.get(i);
					WACustomFieldDto waCustomFieldDto = new WACustomFieldDto();
					waCustomFieldDto.setId(templateToken.getCustomFieldId());
					waCustomFieldDto.setType(templateToken.getCustomFieldType());
					waCustomFieldDto.setFieldName(templateToken.getCustomFieldName());
					waCustomFieldDto.setComponentType(templateToken.getComponentType());
					
					tokenMappings.put(i, waCustomFieldDto);
				}
				template.setTokenMappings(tokenMappings);
			}
			
			template.setLanguage(LocaleUtil.formatLocale(template.getLanguage()));
		} else {
			throw new MessengerException("Invalid templateId");
		}
		
		return template;
	}

	/**
	 * create non-existing templates
	 * 
	 * @param templateDto
	 * @param accountId
	 * @param wabaId
	 * @param template
	 */
	private void createNewWhatsAppTemplate(ExternalWhatsAppTemplateDto templateDto, Integer accountId, String wabaId) {
		
		WhatsAppTemplates template = new WhatsAppTemplates();
		template.setAccountId(accountId);
		template.setWabaId(wabaId);
		template.setExtTemplateId(templateDto.getId());
		template.setTemplateName(templateDto.getName());
		template.setLanguage(templateDto.getLanguage());
		template.setExtStatus(templateDto.getStatus());
		template.setCategory(templateDto.getCategory());
		template.setExtParameterFormat(templateDto.getParameter_format());
		
		template = whatsAppTemplatesRepo.save(template);
		
		for (ExternalWhatsAppTemplateComponentDto componentDto : templateDto.getComponents()) {
			switch (componentDto.getType()) {
				case "HEADER" :
					template.setHeaderFormat(componentDto.getFormat());
					setTemplateHeader(templateDto, accountId, template, componentDto);
					break;
				case "BODY" :
					template.setBodyText(extractBodyText(template, componentDto));
					break;
				case "FOOTER" :
					template.setFooterText(componentDto.getText());
					break;
				case "BUTTONS" :
					template.setButtonData(JSONUtils.toJSON(componentDto.getButtons()));
					break;
			}
		}
		
		template.setStatus(getTemplateState(templateDto.getStatus(), template));
		template.setUpdatedBy(-1);
		
		whatsAppTemplatesRepo.save(template);
	}

	/**
	 * @param templateDto
	 * @param accountId
	 * @param template
	 * @param componentDto
	 */
	private void setTemplateHeader(ExternalWhatsAppTemplateDto templateDto, Integer accountId, WhatsAppTemplates template, ExternalWhatsAppTemplateComponentDto componentDto) {		
		
		switch (componentDto.getFormat()) {
			case "DOCUMENT":
			case "IMAGE":
			case "VIDEO":
				if (MapUtils.isNotEmpty(componentDto.getExample())) {
					List<Object> headerHandle = componentDto.getExample().get("header_handle");
					if (CollectionUtils.isNotEmpty(headerHandle)) {
						template.setHeaderMediaUrl(getFileBirdeyeCdnUrl(templateDto.getName(), accountId, headerHandle.get(0).toString()));
					}
				}
				break;
			case "TEXT":
				template.setHeaderText(extractHeaderText(template, componentDto));
				break;
			case "LOCATION":
			default:

		}
	}
	
	/**
	 * get birdeye template status for the given whatsApp status
	 * 
	 * @param status
	 * @return
	 */
	private String getTemplateState(String extStatus, WhatsAppTemplates template) {
		switch (extStatus) {
			case "APPROVED":
				boolean tokensNotInitialized = whatsAppTemplateTokensRepo.areTemplateTokensNotInitialized(template.getId())==1;
				if (tokensNotInitialized || 
						(!Objects.isNull(template.getHeaderFormat()) && template.getHeaderFormat().equals("LOCATION") && Objects.isNull(template.getHeaderLocationData()))) {
					return TemplateState.needs_personalization.name();
				}
				return TemplateState.active.name();
			case "REJECTED":
				return TemplateState.rejected.name();
			case "PENDING":
				return TemplateState.in_review.name();
			default:
				return TemplateState.in_review.name();
		}
	}
	
	private String getFileBirdeyeCdnUrl(String templateName, Integer accountId, String imageUrl) {
		String fileCdnUrl = null;
		if (StringUtils.isNotBlank(imageUrl)) {
			try {
				String mimeType = MediaBase64Util.getMimeTypeFromFileUrl(imageUrl);
				if (StringUtils.isNotBlank(mimeType) && mimeType.equals("video/quicktime")) {
					mimeType = "video/mp4";
				}
				String base64File = MediaBase64Util.convertMediaUrlToBase64(imageUrl);

				Base64File fileContent = new Base64File();
				fileContent.setData(base64File);
				fileContent.setFileType(mimeType);
				String ex = MessengerUtil.getExtensionFromMimeType(mimeType);
				fileContent.setFileName(templateName + "." + ex);

				fileCdnUrl = doupService.uploadBase64FileForBusiness(accountId, fileContent);
			} catch (Exception e) {
				log.error("Error while uploading image to Birdeye CDN for template: {}", templateName, e);
			}
		}
		return fileCdnUrl;
	}

	/**
	 * check if any field updated, if yes then update in db
	 * 
	 * @param templateDto
	 * @param existingTemplate
	 */
	private void updateExistingWhatsAppTemplate(ExternalWhatsAppTemplateDto templateDto, WhatsAppTemplates existingTemplate) {
		boolean isUpdated = false;

		// Check and update template name
		if (!templateDto.getName().equals(existingTemplate.getTemplateName())) {
			existingTemplate.setTemplateName(templateDto.getName());
			isUpdated = true;
		}

		// Check and update language
		if (!templateDto.getLanguage().equals(existingTemplate.getLanguage())) {
			existingTemplate.setLanguage(templateDto.getLanguage());
			isUpdated = true;
		}

		// Check and update category
		if (!templateDto.getCategory().equals(existingTemplate.getCategory())) {
			existingTemplate.setCategory(templateDto.getCategory());
			isUpdated = true;
		}

		// Check and update header format
		for (ExternalWhatsAppTemplateComponentDto componentDto : templateDto.getComponents()) {
			switch (componentDto.getType()) {
			case "HEADER":
				if (!componentDto.getFormat().equals(existingTemplate.getHeaderFormat())
						|| ("TEXT".equals(existingTemplate.getHeaderFormat()) && checkIfHeaderTextChanged(componentDto.getText(), existingTemplate.getHeaderText()))) {
					existingTemplate.setHeaderFormat(componentDto.getFormat());
					setTemplateHeader(templateDto, existingTemplate.getAccountId(), existingTemplate, componentDto);
					isUpdated = true;
				}
				break;

			case "BODY":
				// check if body text changed or parameter format changed
				if (checkIfBodyTextChanged(componentDto.getText(), existingTemplate.getBodyText())
						|| !templateDto.getParameter_format().equals(existingTemplate.getExtParameterFormat())) {

					log.info("Body text changed for templateId: {}. Updating body text.", existingTemplate.getId());
					existingTemplate.setExtParameterFormat(templateDto.getParameter_format());
					existingTemplate.setBodyText(extractBodyText(existingTemplate, componentDto));
					isUpdated = true;
				}
				break;

			case "FOOTER":
				if (!componentDto.getText().equals(existingTemplate.getFooterText())) {
					existingTemplate.setFooterText(componentDto.getText());
					isUpdated = true;
				}
				break;

			case "BUTTONS":
				String newButtonData = JSONUtils.toJSON(componentDto.getButtons());
				if (!newButtonData.equals(existingTemplate.getButtonData())) {
					existingTemplate.setButtonData(newButtonData);
					isUpdated = true;
				}
				break;
			}
		}
		
		// Check and update status
		if (!templateDto.getStatus().equals(existingTemplate.getExtStatus())) {
			existingTemplate.setExtStatus(templateDto.getStatus());
			isUpdated = true;
		}

		if (isUpdated) {
			existingTemplate.setStatus(getTemplateState(templateDto.getStatus(), existingTemplate));
			whatsAppTemplatesRepo.save(existingTemplate);
		}

	}

	/**
	 * first this will remove the tokens, then check the body text updated
	 * 
	 * @param componentBodyText
	 * @param existingBodyText
	 * @return
	 */
	private boolean checkIfBodyTextChanged(String componentBodyText, String existingBodyText) {
		// Regex to match tokens
		String tokenRegex = "\\{\\{[^}]+}}|\\[[^]]+\\]";
		
		// Remove actual tokens from the texts and replace with {} to check if token count changed
        String componentBodyTextWithoutTokens = componentBodyText.replaceAll(tokenRegex, "{}").trim();
        String existingBodyTextWithoutTokens = existingBodyText.replaceAll(tokenRegex, "{}").trim();
        
        // Compare the texts without tokens
		return !componentBodyTextWithoutTokens.equals(existingBodyTextWithoutTokens);
	}

	private boolean checkIfHeaderTextChanged(String componentHeaderText, String existingHeaderText) {
		// Regex to match tokens
		String tokenRegex = "\\{\\{[^}]+}}|\\[[^]]+\\]";

		// Remove actual tokens from the texts and replace with {} to check if token count changed
		String componentBodyTextWithoutTokens = componentHeaderText.replaceAll(tokenRegex, "{}").trim();
		String existingBodyTextWithoutTokens = existingHeaderText.replaceAll(tokenRegex, "{}").trim();

		// Compare the texts without tokens
		return !componentBodyTextWithoutTokens.equals(existingBodyTextWithoutTokens);
	}

	/**
	 * this will create the tokens in whatsapp_template_token table
	 * 
	 * @param templateId
	 * @param componentDto
	 * @return
	 */
	private String extractBodyText(WhatsAppTemplates template, ExternalWhatsAppTemplateComponentDto componentDto) {
		
		whatsAppTemplateTokensRepo.deleteByTemplateId(template.getId());
		
		Pattern tokenPattern = Pattern.compile("\\{\\{([^}]+)}}");
        Matcher searchTokenInBodyText = tokenPattern.matcher(componentDto.getText());
        StringBuffer bodyText = new StringBuffer();
        
		if (!searchTokenInBodyText.find()) {
			return componentDto.getText();
		}
		
		searchTokenInBodyText.reset();

		int counter = 0;
		List<WhatsAppTemplateTokens> templateTokens = new ArrayList<>();
        while (searchTokenInBodyText.find()) {
        	counter++;
        	String token = searchTokenInBodyText.group(1).trim();
        	
        	WhatsAppTemplateTokens templateToken = new WhatsAppTemplateTokens();
        	templateToken.setComponentType("body");
        	templateToken.setTemplateId(template.getId());
        	templateToken.setExtToken(token);
        	templateToken.setCustomFieldName("variable" + counter);
        	
            searchTokenInBodyText.appendReplacement(bodyText, "[variable" + counter + "]");
            
            templateTokens.add(templateToken);
		}
        
		whatsAppTemplateTokensRepo.saveAllAndFlush(templateTokens);

		searchTokenInBodyText.appendTail(bodyText);

		return bodyText.toString();
	}
	
	private String extractHeaderText(WhatsAppTemplates template, ExternalWhatsAppTemplateComponentDto componentDto) {
		
		whatsAppTemplateTokensRepo.deleteByTemplateId(template.getId());
		
		Pattern tokenPattern = Pattern.compile("\\{\\{([^}]+)}}");
        Matcher searchTokenInHeaderText = tokenPattern.matcher(componentDto.getText());
        StringBuffer headerText = new StringBuffer();
        
		if (!searchTokenInHeaderText.find()) {
			return componentDto.getText();
		}
		
		searchTokenInHeaderText.reset();

		int counter = 0;
		List<WhatsAppTemplateTokens> templateTokens = new ArrayList<>();
        while (searchTokenInHeaderText.find()) {
        	counter++;
        	String token = searchTokenInHeaderText.group(1).trim();
        	
        	WhatsAppTemplateTokens templateToken = new WhatsAppTemplateTokens();
        	templateToken.setComponentType("header");
        	templateToken.setTemplateId(template.getId());
        	templateToken.setExtToken(token);
        	templateToken.setCustomFieldName("variable" + counter);
        	
        	searchTokenInHeaderText.appendReplacement(headerText, "[variable" + counter + "]");
            
            templateTokens.add(templateToken);
		}
        
		whatsAppTemplateTokensRepo.saveAllAndFlush(templateTokens);

		searchTokenInHeaderText.appendTail(headerText);

		return headerText.toString();
	}

	/**
	 * fetches all templates from Social for the given WABA ID in the event. <br>
	 * updates the template if an existing one with the same WABA ID is found.
	 * otherwise, creates a new template.
	 * 
	 */
	@Override
	public void processWhatsAppTemplateStatusUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook statusUpdateEvent) {
		
		Set<String> wabaIds = new HashSet<>();
		
		if (CollectionUtils.isNotEmpty(statusUpdateEvent.getEntry())) {
			for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : statusUpdateEvent.getEntry()) {
				wabaIds.add(templateUpdateDto.getId());
			}
		}
		
		if (CollectionUtils.isNotEmpty(wabaIds)) {
			
			// Fetch all account IDs associated with the WABA ID
			List<WhatsAppOnboardingStatus> onboardingStatuses = whatsAppOnboardingStatusRepo.findByWabaIdIn(new ArrayList<>(wabaIds));
			
			Map<String, Set<Integer>> wabaIdToAccountIdsMap = onboardingStatuses.stream().collect(Collectors.groupingBy(WhatsAppOnboardingStatus::getWabaId, 
							Collectors.mapping(WhatsAppOnboardingStatus::getAccountId, Collectors.toSet())));
			
			Set<Integer> accountIds = onboardingStatuses.stream().map(WhatsAppOnboardingStatus::getAccountId).collect(Collectors.toSet());
			List<WhatsAppTemplates> templatesList = whatsAppTemplatesRepo.findByAccountIdIn(new ArrayList<>(accountIds));
			
			Map<String, WhatsAppTemplates> accIdTempIdToTempObjMap = new HashMap<>();
			if (CollectionUtils.isNotEmpty(templatesList)) {
				// Create a map of account ID and template ID to template object
				accIdTempIdToTempObjMap = templatesList.stream().collect(Collectors.toMap(template -> 
							template.getAccountId() + "::" + template.getExtTemplateId(), template -> template, (n, o) -> n));
			}
			
			for (String wabaId : wabaIds) { 
				// Fetch all templates from Social for the given WABA ID
				List<ExternalWhatsAppTemplateDto> whatsAppTemplates = socialService.getAllTemplatesByWabaId(wabaId);

				if (CollectionUtils.isNotEmpty(wabaIdToAccountIdsMap.get(wabaId))) {
					for (Integer accountId : wabaIdToAccountIdsMap.get(wabaId)) {
		
						for (ExternalWhatsAppTemplateDto templateDto : whatsAppTemplates) {
							
							if (accIdTempIdToTempObjMap.containsKey(accountId + "::" + templateDto.getId())) {
								// If template found in DB, update it
								WhatsAppTemplates template = accIdTempIdToTempObjMap.get(accountId + "::" + templateDto.getId());
								updateExistingWhatsAppTemplate(templateDto, template);
							} else {
								// If template not found in DB, create a new one
								createNewWhatsAppTemplate(templateDto, accountId, wabaId);
								
							}
						}
					}
				}
			}
		}
	}

	/**
	 * If any template is updated via whatsApp account, change its status to <code>IN_REVIEW</code>.
	 */
	@Override
	public void processWhatsAppTemplateMessageUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook templateUpdateWebhook) {
		Set<String> wabaIds = new HashSet<>();
		Set<String> extTemplatesIds = new HashSet<>();
		
		for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : templateUpdateWebhook.getEntry()) {
			
			wabaIds.add(templateUpdateDto.getId());
			for (ExternalWhatsAppTemplateChangeDto templateChangesDto : templateUpdateDto.getChanges()) {
				ExternalWhatsAppTemplateValueDto templateValueDto = templateChangesDto.getValue();
				String extTemplateId = templateValueDto.getMessage_template_id();
				extTemplatesIds.add(extTemplateId);
			}
		}
		
		if(CollectionUtils.isNotEmpty(extTemplatesIds) && CollectionUtils.isNotEmpty(wabaIds)) {
			whatsAppTemplatesRepo.UpdateStatusByWabaIdAndExtTemplateId(TemplateState.in_review.name(), new ArrayList<String>(wabaIds), new ArrayList<String>(extTemplatesIds));
		}
		
	}

	@Override
	@Transactional
	public void saveWhatsAppTemplateTokens(Integer templateId, WhatsAppTemplateDto templateDto, Integer userId) {
		WhatsAppTemplates template = whatsAppTemplatesRepo.findById(templateId).orElse(null);
		
		if (template != null) {
			
			// setting body tokens
			List<WhatsAppTemplateTokens> templateTokens = whatsAppTemplateTokensRepo.findByTemplateId(template.getId());
			Map<String, List<WhatsAppTemplateTokens>> tokensByComponentType = templateTokens.stream().collect(Collectors.groupingBy(WhatsAppTemplateTokens::getComponentType));

			if (tokensByComponentType.containsKey("body") && CollectionUtils.isNotEmpty(tokensByComponentType.get("body"))) {
				
				List<WhatsAppTemplateTokens> templateBodyTokens = tokensByComponentType.get("body");

				for (Entry<Integer, WACustomFieldDto> tokenMapping : templateDto.getTokenMappings().entrySet()) {
					WhatsAppTemplateTokens templateToken = templateBodyTokens.get(tokenMapping.getKey());
					
					template.setBodyText(template.getBodyText().replace(templateToken.getCustomFieldName(), tokenMapping.getValue().getFieldName()));
					
					templateToken.setCustomFieldId(tokenMapping.getValue().getId());
					templateToken.setCustomFieldType(tokenMapping.getValue().getType());
					templateToken.setCustomFieldName(tokenMapping.getValue().getFieldName());
				}
				
				whatsAppTemplateTokensRepo.saveAllAndFlush(templateBodyTokens);
			}
			
			// setting header tokens
			if (StringUtils.isNotBlank(templateDto.getHeaderFormat())) {
				switch (template.getHeaderFormat()) {
					case "DOCUMENT" :
					case "IMAGE" :
					case "VIDEO" :
						if (StringUtils.isNotBlank(templateDto.getHeaderMediaUrl())) {
							template.setHeaderMediaUrl(templateDto.getHeaderMediaUrl());
						}
						break;
					case "TEXT" :
		
						if (tokensByComponentType.containsKey("header") && CollectionUtils.isNotEmpty(tokensByComponentType.get("header"))) {
							
							WhatsAppTemplateTokens templateHeaderToken = tokensByComponentType.get("header").get(0);
								
							template.setHeaderText(template.getHeaderText().replace(templateHeaderToken.getCustomFieldName(), templateDto.getTemplateHeaderToken().getFieldName()));
		
							templateHeaderToken.setCustomFieldId(templateDto.getTemplateHeaderToken().getId()!=0?templateDto.getTemplateHeaderToken().getId():null);
							templateHeaderToken.setCustomFieldType(templateDto.getTemplateHeaderToken().getType());
							templateHeaderToken.setCustomFieldName(templateDto.getTemplateHeaderToken().getFieldName());
							
							whatsAppTemplateTokensRepo.saveAllAndFlush(Arrays.asList(templateHeaderToken));
		
						}
						break;
					case "LOCATION" :
						template.setHeaderLocationData(JSONUtils.toJSON(templateDto.getHeaderLocation()));
						break;
					default:
						break;
	
				}
			}
			
			template.setStatus(getTemplateState(template.getExtStatus(), template));
			template.setUpdatedBy(userId);
			whatsAppTemplatesRepo.save(template);
		} else {
			throw new MessengerException("Invalid templateId");
		}
		
	}

	@Override
	public Integer getWABusinessId(String businessPhoneNumberId) {
		Optional<WhatsAppOnboardingStatus> waOnboardingStatus = whatsAppOnboardingStatusRepo.findByPhoneNumberIdAndStatus(businessPhoneNumberId, true);
		if (waOnboardingStatus.isPresent()) {
			return waOnboardingStatus.get().getBusinessId();
		} else {
			return null;
		}
	}
	
	@Override
	public String getWAPhoneNumberIdByBusinessId(Integer businessId) {
		Optional<WhatsAppOnboardingStatus> waOnboardingStatus = whatsAppOnboardingStatusRepo.findByBusinessIdAndStatus(businessId, true);
		if (waOnboardingStatus.isPresent()) {
			return waOnboardingStatus.get().getPhoneNumberId();
		} else {
			return null;
		}
	}
	
	@Override
	public WhatsAppTemplateDto getWhatsAppTemplateDetailByName(String templateName, Integer businessId) {
		Optional<WhatsAppTemplates> template = whatsAppTemplatesRepo.findByTemplateNameAndAccountId(templateName, businessId);
		WhatsAppTemplateDto templateDto = null;
		if (template.isPresent()) {
			List<WhatsAppTemplateTokens> templateTokens = whatsAppTemplateTokensRepo.findByTemplateId(template.get().getId());

			templateDto = new WhatsAppTemplateDto();
			templateDto.setId(template.get().getId());
			templateDto.setName(template.get().getTemplateName());
			templateDto.setTemplateType(template.get().getCategory());
			templateDto.setBody(template.get().getBodyText());
			templateDto.setLanguage(template.get().getLanguage());
			templateDto.setStatus(template.get().getStatus());
			templateDto.setHeaderFormat(template.get().getHeaderFormat());
			templateDto.setHeaderMediaUrl(template.get().getHeaderMediaUrl());
			templateDto.setButtons(template.get().getButtonData());
			templateDto.setTokenCount(templateTokens.size());
			templateDto.setTemplatesToken(templateTokens);
			templateDto.setParameterFormat(template.get().getExtParameterFormat());
			templateDto.setFooterText(template.get().getFooterText());
			templateDto.setHeaderText(template.get().getHeaderText());

		} else {
			throw new MessengerException("Invalid template name");
		}

		return templateDto;
	}
	
	@Override
	public void deleteWhatsAppTemplate(Integer templateId) {
		whatsAppTemplatesRepo.deleteById(templateId);
		whatsAppTemplateTokensRepo.deleteByTemplateId(templateId);
		log.info("Deleted template with ID: {}", templateId);
	}

	@Override
	public List<WhatsAppOnboardingStatus> getWhatsappOnboardingStatusByBusinessIds(Integer accountId, List<Integer> businessId) {
		List<WhatsAppOnboardingStatus> onboardingStatusOp = whatsAppOnboardingStatusRepo.findByAccountIdAndBusinessIdIn(accountId, businessId);
		if (CollectionUtils.isNotEmpty(onboardingStatusOp)) {
			return onboardingStatusOp;
		} else {
			return Collections.emptyList();
		}
	}

	@Override
	public WhatsappOnboardingStatusDto whatsappAccountOnboardStatus(Integer accountId, Integer businessId) {
		List<WhatsAppOnboardingStatus> onboardStatus = getWhatsappOnboardingStatusByBusinessIds(accountId, Arrays.asList(businessId));
		if (CollectionUtils.isNotEmpty(onboardStatus)) {
			WhatsappOnboardingStatusDto statusDto = WhatsappOnboardingStatusDto.builder()
					.accountId(onboardStatus.get(0).getAccountId())
					.businessId(onboardStatus.get(0).getBusinessId())
					.status(onboardStatus.get(0).getStatus()).build();
			return statusDto;
		} else {
			return null;
		}
	}

	public void processWhatsAppBusinessVerificationUpdateEvent(WhatsAppBusinessLocationStatusEvent businessVerificationEvent) {
		whatsAppOnboardingStatusRepo.updateBusinessVerifiedByWabaId(businessVerificationEvent.getWabaId(), businessVerificationEvent.getIsBusinessVerified());
	}

	@Override
	public void processWhatsAppTemplateCategoryUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook categoryUpdateEvent) {
		Set<String> wabaIds = new HashSet<>();
		Set<String> extTemplatesIds = new HashSet<>();

		for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : categoryUpdateEvent.getEntry()) {

			wabaIds.add(templateUpdateDto.getId());
			for (ExternalWhatsAppTemplateChangeDto templateChangesDto : templateUpdateDto.getChanges()) {
				ExternalWhatsAppTemplateValueDto templateValueDto = templateChangesDto.getValue();
				String extTemplateId = templateValueDto.getMessage_template_id();
				extTemplatesIds.add(extTemplateId);
			}
		}

		List<WhatsAppTemplates> whatsappTemplates = whatsAppTemplatesRepo.findByWabaIdInAndExtTemplateIdIn(new ArrayList<>(wabaIds), new ArrayList<>(extTemplatesIds));

		if (CollectionUtils.isNotEmpty(whatsappTemplates)) {

			Map<String, WhatsAppTemplates> wabaIdToTemplateMap = whatsappTemplates.stream().collect(Collectors.toMap(
					template -> template.getWabaId() + "::" + template.getExtTemplateId(), template -> template, (n, o) -> n));

			for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : categoryUpdateEvent.getEntry()) {
				String wabaId = templateUpdateDto.getId();
				for (ExternalWhatsAppTemplateChangeDto templateChangesDto : templateUpdateDto.getChanges()) {
					ExternalWhatsAppTemplateValueDto templateValueDto = templateChangesDto.getValue();
					String extTemplateId = templateValueDto.getMessage_template_id();

					WhatsAppTemplates template = wabaIdToTemplateMap.get(wabaId + "::" + extTemplateId);
					if (template != null) {
						template.setCategory(templateValueDto.getNew_category());
						template.setStatus(TemplateState.in_review.name());
					}
				}
			}

			whatsAppTemplatesRepo.saveAll(whatsappTemplates);
		}
	}

	@Override
	public void processWhatsAppTemplateQualityUpdateEvent(ExternalWhatsAppTemplateUpdateWebhook qualityUpdateEvent) {
		Set<String> wabaIds = new HashSet<>();
		Set<String> extTemplatesIds = new HashSet<>();

		for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : qualityUpdateEvent.getEntry()) {

			wabaIds.add(templateUpdateDto.getId());
			for (ExternalWhatsAppTemplateChangeDto templateChangesDto : templateUpdateDto.getChanges()) {
				ExternalWhatsAppTemplateValueDto templateValueDto = templateChangesDto.getValue();
				String extTemplateId = templateValueDto.getMessage_template_id();
				extTemplatesIds.add(extTemplateId);
			}
		}

		List<WhatsAppTemplates> whatsappTemplates = whatsAppTemplatesRepo.findByWabaIdInAndExtTemplateIdIn(new ArrayList<>(wabaIds), new ArrayList<>(extTemplatesIds));

		if (CollectionUtils.isNotEmpty(whatsappTemplates)) {

			Map<String, WhatsAppTemplates> wabaIdToTemplateMap = whatsappTemplates.stream().collect(Collectors.toMap(
					template -> template.getWabaId() + "::" + template.getExtTemplateId(), template -> template, (n, o) -> n));

			for (ExternalWhatsAppTemplateUpdateDto templateUpdateDto : qualityUpdateEvent.getEntry()) {
				String wabaId = templateUpdateDto.getId();
				for (ExternalWhatsAppTemplateChangeDto templateChangesDto : templateUpdateDto.getChanges()) {
					ExternalWhatsAppTemplateValueDto templateValueDto = templateChangesDto.getValue();
					String extTemplateId = templateValueDto.getMessage_template_id();

					WhatsAppTemplates template = wabaIdToTemplateMap.get(wabaId + "::" + extTemplateId);
					if (template != null) {
						template.setTemplateQuality(templateValueDto.getNew_quality_score());
					}
				}
			}

			whatsAppTemplatesRepo.saveAll(whatsappTemplates);
		}
		
	}

	@Override
	public void deleteWhatsAppOnboardingStatus(Integer id) {
		whatsAppOnboardingStatusRepo.deleteById(id);
	}

	@Override
	public Map<String, Object> getWabaNameListByAccountId(Integer accountId) {
		Map<String, Object> response = new HashMap<>();

		// Fetch WABA name list asynchronously
		CompletableFuture<Void> wabaNameListFuture = CompletableFuture.runAsync(() -> {
			List<WhatsAppOnboardingStatus> onboardingStatusList = whatsAppOnboardingStatusRepo.findByAccountId(accountId);
			if (CollectionUtils.isNotEmpty(onboardingStatusList)) {
				response.put("wabaNameList", onboardingStatusList.stream().map(WhatsAppOnboardingStatus::getWabaName).collect(Collectors.toCollection(TreeSet::new)));
			} else {
				response.put("wabaNameList", Collections.emptyList());
			}
		});

		// Fetch language list asynchronously
		CompletableFuture<Void> languageListFuture = CompletableFuture.runAsync(() -> {
			Set<String> languageCodes = whatsAppTemplatesRepo.findLanguageByAccountId(accountId);
			if (CollectionUtils.isNotEmpty(languageCodes)) {
				List<Map<String, String>> languageList = new ArrayList<>();
				for (String languageCode : languageCodes) {
					Map<String, String> languageMap = new HashMap<>();
					languageMap.put("displayName", LocaleUtil.formatLocale(languageCode));
					languageMap.put("language", languageCode);
					languageList.add(languageMap);
				}
				languageList.sort(Comparator.comparing(map -> map.get("displayName")));
				response.put("languageList", languageList);
			} else {
				response.put("languageList", Collections.emptyList());
			}
		});

		// Wait for both tasks to complete
		CompletableFuture.allOf(wabaNameListFuture, languageListFuture).join();

		return response;
	}

}
