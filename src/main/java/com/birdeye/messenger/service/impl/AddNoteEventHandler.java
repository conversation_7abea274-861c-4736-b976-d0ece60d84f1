package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.AddNoteService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class AddNoteEventHandler extends MessageEventHandlerAbstract{
	@Autowired
    protected SendMessageService sendMessageService;
	private final MessengerEvent EVENT = MessengerEvent.INTERNAL_NOTES;
	@Autowired
	protected AddNoteService addNoteService;

	@Autowired
    protected FacebookEventService facebookEventService;
	
	private final WhatsappMessageService whatsappMessageService;

	
    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = communicationHelperService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }
    
    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
            MessengerContact messengerContact = getMessengerContact(messageDTO);
            //customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
            if(messengerContact.getCustomerId()!=null) {
            	customerDTO = contactService.findByIdNoCaching(messengerContact.getCustomerId());
            }
            dto.setCustomerDTO(customerDTO);
        }
        return customerDTO;
    }
    
    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
    	UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
        	userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }
    
    
    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
    	messageDTO.setMessageTag(MessageTag.INBOX);
    	return messageDTO.getMessageTag();
    }
    
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }
    
	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageType("INTERNAL_NOTE");
        lastMessageMetaData.setLastMessageUserId(userDTO.getId());
        lastMessageMetaData.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
        messengerContact.setLastMsgOn(new Date());
        messengerContact.setUpdatedAt(new Date());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        //messengerContact.setLastMessage("You (Note): "+sendMessageDTO.getBody());
        //messengerContact.setLastMessage(" added an internal note: "+ sendMessageDTO.getBody());
        messengerContact.setLastMessage(sendMessageDTO.getBody());
        messengerContact.setEncrypted(0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {	
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		UserDTO userDTO=getUserDTO(messageDTO);
        if(userDTO==null) {
        	throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
        }
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.NOTE);
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
		notificationRequest.setMsgId(Integer.valueOf(messageDTO.getMessageDocument().getM_id()));
        notificationRequest.setUserDTO(userDTO);
        notificationRequest.setCount(1);
        return notificationRequest;
	}		

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
        	SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
	}
	
	
	@Override
    public SendResponse handle(MessageDTO messageDTO) throws Exception {
		log.debug("Processing add note request {} ", messageDTO);
		SendResponse.Message message=addNote(messageDTO);
		int routeId=getBusinessDTO(messageDTO).getEnterpriseId()!=null?getBusinessDTO(messageDTO).getEnterpriseId():getBusinessDTO(messageDTO).getBusinessId();
		SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, message,facebookEventService.isFBSendAvailable(getMessengerContact(messageDTO), routeId),true);
		
		WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(getMessengerContact(messageDTO), routeId);
		sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
		sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
		
    	return sendResponse;
	}
	
	private SendResponse.Message addNote(MessageDTO messageDTO) throws Exception {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		sendMessageDTO.setMessengerContact(messengerContactService.getOrCreateContact(getBusinessDTO(sendMessageDTO).getBusinessId(), Integer.parseInt(sendMessageDTO.getToCustomerId()), messageDTO.getBusinessDTO().getAccountId()));
		ConversationDTO conversationDTO = addNoteService.saveNote(sendMessageDTO);
		sendMessageDTO.setConversationDTO(conversationDTO);
		sendMessageDTO.setSendEmailNotification(true);
		if(BooleanUtils.isFalse(messageDTO.isOpenConversation()) && sendMessageDTO.getMessengerContact().getTag().equals(MessageTag.ARCHIVED.getCode())) {
			sendMessageDTO.setUpdateTag(false);
		}
		super.handle(sendMessageDTO);
		return new SendResponse.Message(getUserDTO(messageDTO), conversationDTO, new MessengerMediaFile(),
				messageDTO.getBusinessDTO().getTimeZoneId());
	}
	
	@Override
	public void publishEvent(MessageDTO messageDTO) {
		//Do Nothing
	}

}
