package com.birdeye.messenger.service.impl;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.MessageUpdateDTO;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.BulkUpdateService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.RedisHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class BulkUpdateServiceImpl implements BulkUpdateService {
	
	private static final Logger logger = LoggerFactory.getLogger(BulkUpdateServiceImpl.class);
	
	private static final Integer SIZE = 10000;

	@Autowired
	private RedisHandler redisHandler;

	@Autowired
	private MessengerContactRepository messengerContactRepository;
	
	@Autowired
    private KafkaService kafkaService;
	
	@Override
	public void updateConversationMessages() {
		while (true) {
			int size = SIZE;
			Integer conversationId = getLastProcessedConversationId();
			if (!updateConversationMessages(conversationId, size))
				break;
		}
	}
	
	@Override
	public void updateFailedConversationMessages() {
		String conversationPagesFailed = getConversationPagesFailed();
		if (StringUtils.isNotEmpty(conversationPagesFailed)) {
			List<String> pageDetails = Arrays.asList(conversationPagesFailed.split(","));
			if (pageDetails != null)
				for (String pageDetail : pageDetails) {
					String[] s = pageDetail.split("-");
					if (!updateConversationMessages(Integer.valueOf(s[0]), Integer.valueOf(s[1])))
						break;
				}
		}
	}

	private boolean updateConversationMessages(Integer conversationId, int size) {
		try {
			long delay = (long) (0.1 * size);
			if (!updateConversationMessagesInES(conversationId, size))
				return false;
			logger.info("Waiting (in ms) in eConversationMessages : " + delay);
			Thread.currentThread().sleep(delay);
		} catch (Exception e) {
			logger.error("Exception encountered in updateConversationMessages : ", e);
		}
		return true;
	}
	
	private boolean updateConversationMessagesInES(Integer conversationId, int size) {
		boolean moreRecords = true;
		try {
			List<Object[]> conversationDetails = fetchConversations(conversationId, size);
			if (conversationDetails != null && conversationDetails.size() > 0) {
				pushToKafka(conversationDetails);
			} else {
				moreRecords = false;
			}
		} catch (Exception e) {
			logger.error("Exception encountered in updateConversationMessagesInES : ", e);
			updateConversationPagesFailed(conversationId + "-" + size);
		}
		return moreRecords;
	}

	private List<Object[]> fetchConversations(Integer conversationId, int size) {
		List<Object[]> conversationDetails = messengerContactRepository.fetchPaginatedCoversationsWithEnterprise(conversationId, size);
		if (conversationDetails != null && conversationDetails.size() > 0) {
			Integer maxConversationId = (Integer) conversationDetails.get(conversationDetails.size() - 1)[0];
			updateLastProcessedConversationId(maxConversationId);
		}
		return conversationDetails;
	}	
	
	private void pushToKafka(List<Object[]> conversationDetails) {
		if (conversationDetails != null)
			for (Object[] conversationDetail : conversationDetails)
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.MESSAGE_UPDATE, null, new MessageUpdateDTO((Integer) conversationDetail[0], (Integer) conversationDetail[1], (conversationDetail[2] == null ? (Integer) conversationDetail[1] : (Integer) conversationDetail[2])));
	}
	
	private int getLastProcessedConversationId() {
		return redisHandler.getLastProcessedConversationId();
	}
	
	private void updateLastProcessedConversationId(int conversationId) {
		redisHandler.updateLastProcessedConvesationId(conversationId);
	}
	
	private void updateConversationPagesFailed(String pageDetail) {
		String conversationPagesFailed = getConversationPagesFailed();
		if (StringUtils.isEmpty(conversationPagesFailed))
			conversationPagesFailed = pageDetail;
		else
			conversationPagesFailed += "," + pageDetail;
		redisHandler.updateConversationPagesFailed(conversationPagesFailed);
	}
	
	private String getConversationPagesFailed() {
		return redisHandler.getConversationPagesFailed();
	}
}
