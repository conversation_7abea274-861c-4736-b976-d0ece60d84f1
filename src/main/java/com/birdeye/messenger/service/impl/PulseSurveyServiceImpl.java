package com.birdeye.messenger.service.impl;

import java.util.Arrays;
import java.util.Date;

import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dao.entity.PulseSurveyAudit;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.PulseSurveyMessage;
import com.birdeye.messenger.dto.PulseSurveyQuestionRequest;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.PulseSurveyStatusEnum;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.PulseSurveyAuditService;
import com.birdeye.messenger.service.PulseSurveyContextService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.SamayService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PulseSurveyServiceImpl implements PulseSurveyService {

	private final PulseSurveyContextService pulseSurveyContextService;

	private final KafkaService kafkaService;
	
	private final PulseSurveyAuditService pulseSurveyAuditService;
	
	private final SamayService samayService;

	@Override
	public PulseSurveyContext handlePulseSurveyContext(CampaignSMSDto smsEvent, CustomerDTO customer, BusinessDTO businessDto
			) throws Exception {
		PulseSurveyContext existingContext = pulseSurveyContextService.getPulseSurveyContext(
				customer.getId(),
				Arrays.asList(PulseSurveyStatusEnum.ACTIVE.getName(), PulseSurveyStatusEnum.IN_PROGRESS.getName()));
		if (existingContext != null) {
			// Terminate existing context, evict cache
			log.info("Existing PulseSurveyContext found for businessId: {} and customerId: {}",
					businessDto.getBusinessId(), customer.getId());
			updatePulseSurveyContextStatus(existingContext,
					PulseSurveyStatusEnum.TERMINATED.getName());
			existingContext.setStatus(PulseSurveyStatusEnum.TERMINATED.getName());
		}
		if (smsEvent != null && (smsEvent.getSurveyType() != null)
				&& CampaignSMSDto.NPS.equalsIgnoreCase(smsEvent.getSurveyType())) {
			// Create PulseSurveyContext if its a NPS survey campaign message
			log.info("Creating PulseSurveyContext for businessId: {} and customerId: {}", businessDto.getBusinessId(),
					customer.getId());
			PulseSurveyContext newContext = pulseSurveyContextService.createPulseSurveyContext(smsEvent, businessDto.getBusinessId(), customer.getId());
			//Submit to scheduler : X hours
			Long timeOut = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("pulse_survey_timeout_1_hours", 10));
			samayService.submitToScheduler(newContext, "hours", timeOut);

			return newContext;
		}
		return existingContext;
	}

	@Override
	public PulseSurveyContext pulseContextCheckSmsReceive(SMSMessageDTO smsMessageDTO, CustomerDTO customerDto, BusinessDTO businessDTO) throws Exception{
		PulseSurveyContext existingContext = pulseSurveyContextService.getPulseSurveyContext(customerDto.getId(),
				Arrays.asList(PulseSurveyStatusEnum.ACTIVE.getName(), PulseSurveyStatusEnum.IN_PROGRESS.getName()));

		if (!ObjectUtils.isEmpty(existingContext)) {
			//Audit Received Response
			PulseSurveyAudit audit = pulseSurveyAuditService.auditPulseSurveyResponse(existingContext.getId(), smsMessageDTO);
    		
			// Send an event to Survey
			log.info("Receive SMS. Active PulseSurveyContext found for businessId {} and customerId {}.",
					businessDTO.getBusinessId(), customerDto.getId());
			PulseSurveyMessage message = new PulseSurveyMessage(existingContext.getRrId(),
					existingContext.getBusinessId(), existingContext.getCustomerId(), existingContext.getSurveyId(),
					smsMessageDTO.getBody(), customerDto, audit.getId());

			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SURVEY_TEXT_REPLY, null, message);

			// Update ExistingContext status to In-Progress
			existingContext.setStatus(PulseSurveyStatusEnum.IN_PROGRESS.getName());
			existingContext.setLastUpdatedDate(new Date());
			pulseSurveyContextService.savePulseSurveyContext(existingContext);

			// Per Response : Submit to Scheduler : Z minutes
			Long timeOut = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("pulse_survey_timeout_2_minutes", 5));
			samayService.submitToScheduler(existingContext, "minutes", timeOut);
		} else {
			/** No Active or In-Progress context found check for Last Status
			If Terminated - change to Finished and record post termination receive items **/
			PulseSurveyContext lastContext = pulseSurveyContextService.getLastPulseSurveyContext(customerDto.getId());
			if (!ObjectUtils.isEmpty(lastContext) && PulseSurveyStatusEnum.TERMINATED.getName().equals(lastContext.getStatus())) {
				//Mark Context as Finished in DB
				lastContext.setStatus(PulseSurveyStatusEnum.FINISHED.getName());
				lastContext.setLastUpdatedDate(new Date());
				pulseSurveyContextService.savePulseSurveyContext(lastContext);
				pulseSurveyContextService.clearPulseSurveyContextCache(lastContext.getCustomerId());
			}
		}
		return existingContext;
	}
	
	@Override
	public PulseSurveyContext updatePulseSurveyContextForSurveyEvent(PulseSurveyQuestionRequest request, BusinessDTO businessDto, String responseStatus) {
        PulseSurveyContext existingContext = pulseSurveyContextService.getPulseSurveyContext(request.getCustomerId(),
                Arrays.asList(PulseSurveyStatusEnum.ACTIVE.getName(), PulseSurveyStatusEnum.IN_PROGRESS.getName()));
		if (existingContext != null) {
			if (request.getQuestionId() != null) {
				existingContext.setQuestionId(request.getQuestionId());
				existingContext.setStep(existingContext.getStep() + 1);
			}
			updatePulseSurveyContextStatus(existingContext, request.getStatus());
		} else {
			log.warn("No Existing PulseSurveyContext found for businessID: {} And customerId: {}", request.getFromBusinessId(), request.getCustomerId());
		}
		return existingContext;
	}

	@Override
	public PulseSurveyContext getPulseSurveyContextById(Integer contextId) {
		PulseSurveyContext context = pulseSurveyContextService.getPulseSurveyContextById(contextId);
		return context;
	}

	@Override
	public void updatePulseSurveyContextStatus(PulseSurveyContext existingContext, String status) {
		pulseSurveyContextService.updatePulseSurveyContextStatus(existingContext, status);
		clearPulseSurveyContextCache(existingContext.getCustomerId());
	}

	@Override
	public void clearPulseSurveyContextCache(Integer customerId) {
		pulseSurveyContextService.clearPulseSurveyContextCache(customerId);
		
	}

//	@Override
//	public PulseSurveyContext findPulseSurveyContextBySmsId(Integer smsId) {
//		PulseSurveyContext existingContext = pulseSurveyContextService.getPulseSurveyContextBySmsId(smsId);
//		return existingContext;
//	}

}
