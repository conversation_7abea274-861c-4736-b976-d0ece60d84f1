package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.InboxAudit;
import com.birdeye.messenger.dao.repository.InboxAuditRepository;
import com.birdeye.messenger.service.InboxAuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class InboxAuditServiceImpl implements InboxAuditService {

    private final InboxAuditRepository inboxAuditRepository;

    @Override
    public void auditEvent(String payload, String type) {
        InboxAudit inboxAudit = new InboxAudit();
        inboxAudit.setPayload(payload);
        inboxAudit.setType(type);
        inboxAuditRepository.save(inboxAudit);
    }
}
