package com.birdeye.messenger.service.impl;

import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.service.RedisLockService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class RedisLockServiceImpl implements RedisLockService {


    private static final long DEFAULT_EXPIRE_UNUSED = 60000L;

    private final RedisLockRegistry redisLockRegistry;

    @Override
    public  Optional<Lock>  tryLock(String lockKey) {
        try {
            Lock lock = obtainLock(lockKey);
            boolean isLockAquired=lock.tryLock();
            if(!isLockAquired) {
            	log.info("lock : {} not acq key : {}", lock, lockKey);
            	return Optional.empty();
            }
            log.info("lock : {} acq key : {}", lock, lockKey);
            return Optional.of(lock);
        } catch (Exception e) {
            log.error("Exception while locking :{}", e);
            return Optional.empty();
        }
    }
    @Override
    public Optional<Lock> tryLock(String lockKey, long time, TimeUnit timeUnit) {
        try {
            Lock lock = obtainLock(lockKey);
            boolean isLockAquired=lock.tryLock(time, timeUnit);
            if(!isLockAquired) {
            	log.info("lock : {} not acq key : {}", lock, lockKey);
            	return Optional.empty();
            }
            log.info("lock : {} acq key : {}", lock, lockKey);
            return Optional.of(lock);
        } catch (Exception e) {
            log.error("Exception while locking :{}", e);
            return Optional.empty();
        }

    }

    @Override
    public void unlock(Lock lock) {
        try {
            lock.unlock();
            redisLockRegistry.expireUnusedOlderThan(DEFAULT_EXPIRE_UNUSED);
            log.info("Lock released: {}", lock);
        } catch (Exception e) {
            log.error("Exception while unlocking :{}", e);
        }

    }

    private Lock obtainLock(String lockKey) {
        return redisLockRegistry.obtain(lockKey);
    }
}
