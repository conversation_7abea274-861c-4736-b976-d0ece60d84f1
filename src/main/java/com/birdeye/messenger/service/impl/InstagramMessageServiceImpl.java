package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.InstagramMessageDto;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.instagram.LikeUnlikeRequest;
import com.birdeye.messenger.dto.instagram.Reaction;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.service.KafkaService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dao.repository.InstagramMessageRepository;
import com.birdeye.messenger.service.FacebookMessageService;
import com.birdeye.messenger.service.InstagramMessageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class InstagramMessageServiceImpl implements InstagramMessageService {

	private final InstagramMessageRepository instagramMessageRepository;
    
    private final KafkaService kafkaService;

	@Override
    @Transactional
    public void deleteInstagramMessagesByMcId(Integer mcId){
        try {
            instagramMessageRepository.deleteByMessengerContactId(mcId);
        } catch (Exception e) {
            log.error("error : {} occurred in deleteInstagramMessagesByMcId", e.getMessage());
        }
    }

    @Override
    public InstagramMessage findByMessageId(String messageId){
        return instagramMessageRepository.findByMessageId(messageId);
    }

    @Override
    public InstagramMessage findById(Integer id){
        return instagramMessageRepository.findById(id).get();
    }

    @Override
    @Transactional
    public void updateInstagramMessageBody(String messageBody,Integer messageId){
        try {
            instagramMessageRepository.updateInstagramMessageBody(messageBody,messageId);
        } catch (Exception e) {
            log.error("error : {} occurred in updatingInstagramMessage", e.getMessage());
        }
    }

    @Override
    public void  checkAndPushLikeUnlikeEvent(MessageDTO messageDTO,BusinessDTO businessDTO){
        InstagramMessageRequest instagramMessageRequest=(InstagramMessageRequest)messageDTO;
        if(Objects.nonNull(instagramMessageRequest.getEntry().get(0).getMessaging()) &&
                Objects.nonNull(instagramMessageRequest.getEntry().get(0).getMessaging().get(0)) && Objects.nonNull(instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getReaction())){
            Reaction reaction=instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getReaction();
            InstagramMessage instagramMessage=instagramMessageRepository.findByMessageId(reaction.getMid());
            if(Objects.isNull(businessDTO)){
                return;
            }
            if(Objects.nonNull(instagramMessage) && Objects.nonNull(instagramMessage.getSenderInstagramId()) && Objects.nonNull(instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getRecipient()) &&
                    Objects.nonNull(instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getRecipient().getId()) &&
                    instagramMessage.getSenderInstagramId().equals(instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getRecipient().getId())){
                String messageId=instagramMessage.getId().toString().concat("_ig");
                LikeUnlikeRequest likeUnlikeRequest=new LikeUnlikeRequest();
                if(Constants.REACT.equals(reaction.getAction())){
                    likeUnlikeRequest.setLike(true);
                }else if(Constants.UNREACT.equals(reaction.getReaction())){
                    likeUnlikeRequest.setLike(false);
                }
                likeUnlikeRequest.setMessageId(messageId);
                likeUnlikeRequest.setUserId(-18);
                likeUnlikeRequest.setSource(Source.INSTAGRAM.toString());
                likeUnlikeRequest.setBusinessId(businessDTO.getBusinessId());
                likeUnlikeRequest.setAccountId(businessDTO.getAccountId());
                likeUnlikeRequest.setPublishSocialEvent(true);
                likeUnlikeRequest.setMessengerContactId(instagramMessage.getMessengerContactId());
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.MESSAGE_LIKE_UNLIKE,messageDTO.getBusinessDTO().getAccountId(),
                        likeUnlikeRequest);
            }
        }
    }

    @Override
    @Cacheable(cacheNames = Constants.GET_INSTAGRAM_MESSAGE_ID_CACHE, key="'MID-'.concat(#messageId)", unless="#result == null")
    public InstagramMessageDto getInstagramMessageId(Integer messageId){
        InstagramMessage instagramMessage =  instagramMessageRepository.findById(messageId).get();
        return  new InstagramMessageDto(instagramMessage.getMessengerContactId(),instagramMessage.getSenderInstagramId(),instagramMessage.getRecipientInstagramId(),instagramMessage.getMessageBody(),instagramMessage.getMessageId());
    }
}
