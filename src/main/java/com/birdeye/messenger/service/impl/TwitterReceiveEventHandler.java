package com.birdeye.messenger.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.TwitterMessage;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.twitter.Attachment;
import com.birdeye.messenger.dto.twitter.Media;
import com.birdeye.messenger.dto.twitter.MessageCreate;
import com.birdeye.messenger.dto.twitter.TwitterMessageRequest;
import com.birdeye.messenger.dto.twitter.User;
import com.birdeye.messenger.dto.twitter.VideoInfo;
import com.birdeye.messenger.dto.twitter.VideoVariant;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.SUPPORTED_UPLOAD_FILES;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.TwitterMessageStatusEnum;
import com.birdeye.messenger.external.service.BusinessServiceImpl;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.TwitterEventService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TwitterReceiveEventHandler extends MessageEventHandlerAbstract{
    private final MessengerEvent EVENT = MessengerEvent.TWITTER_RECEIVE;

    @Autowired
    protected MessengerContactRepository messengerContactRepository;

    @Autowired
    protected SocialService socialService;

    @Autowired
    protected TwitterEventService twitterEventService;

    @Autowired
    private ContactService contactService;

    @Autowired
    private BusinessServiceImpl businessServiceImpl;

    @Autowired
    private MessengerMessageService messengerMessageService;

    @Autowired
    private MessengerMediaFileService messengerMediaFileService;

    @Autowired
    private PulseSurveyService pulseSurveyService;

    @Autowired
    private RedisLockService redisLockService;
    
    @Autowired
    private SpamDetectionService spamDetectionService;

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
            MessageCreate msg = dto.getDirect_message_events().get(0).getMessage_create();
            String businessTwitterPageId;
            //add echo condition as well
            if (msg.getTarget() !=null && msg.getTarget().getRecipient_id() !=null && !msg.isEcho()) {
                businessTwitterPageId = msg.getTarget().getRecipient_id();
            }else {
                businessTwitterPageId = msg.getSender_id();
            }
            Map<String,Object> request = new HashMap<>();
            request.put("businessId",true);
            request.put("url",null);
            
            if(Objects.nonNull(msg.getMessage_data()) && Objects.nonNull(msg.getMessage_data().getAttachment()) && Objects.nonNull(msg.getMessage_data().getAttachment().getMedia())){
                Media media = msg.getMessage_data().getAttachment().getMedia();
                String type = media.getType();
                if("photo".equals(type)){
                    String url = media.getMedia_url_https();
                    if (StringUtils.isNotBlank(url)) {
                        request.put("url", url);
                        request.put("content_type","image/jpeg");
                    }
                }else if ("video".equals(type) || "animated_gif".equals(type)) {
                    VideoInfo videoInfo = media.getVideo_info();
                    List<VideoVariant> variants = videoInfo.getVariants();
                    for (VideoVariant variant : variants) {
                        if ("video/mp4".equals(variant.getContent_type())) {
                            request.put("content_type",variant.getContent_type());
                            request.put("url", variant.getUrl());
                            break;
                        }
                    }
                }
            }
            
            Map<?,?> response = twitterEventService.getBusinessIdAndBirdeyeCdnAttachmentUrl(request,businessTwitterPageId);
            if(StringUtils.isNotBlank((String) response.get("url"))){
                String attachmentUrl = (String) response.get("url");
                dto.getDirect_message_events().get(0).getMessage_create().getMessage_data().getAttachment().getMedia().setMedia_url_https(attachmentUrl);
                msg.getMessage_data().setText((removeLastURL(msg.getMessage_data().getText())));
            }
            Integer businessId = (Integer) response.get("businessId");
            if(businessId!=null) {
                businessDTO = communicationHelperService.getBusinessDTO(businessId);
            } else {
                businessDTO = null;
                log.error("[Twitter Receive Event]: No valid business mapping found for twitter page: {} ",
                        businessTwitterPageId);
            }
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }

    @Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		TwitterMessageRequest twitterMessageRequest = (TwitterMessageRequest) messageDTO;
		MessageCreate message = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (Objects.isNull(customerDTO)) {
			MessengerContact contact = getMessengerContact(messageDTO);
			if (contact.getCustomerId() != null && Objects.isNull(messageDTO.getCustomerDTO())) {
				customerDTO = contactService.findById(contact.getCustomerId());
				messageDTO.setCustomerDTO(customerDTO);
			}
			if (Objects.isNull(customerDTO)) {
				customerDTO = messageDTO.getCustomerDTO();
			}
			MessageTag messageTag = getMessageTag(messageDTO);
			if (message.getTarget() != null && !message.isEcho()) {
				spamDetectionService.spamDetectionAllChannels(messageDTO, contact, customerDTO, messageTag);
			}
		}
		return customerDTO;
	}

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        TwitterMessageRequest request = (TwitterMessageRequest) messageDTO;
        MessageCreate message = request.getDirect_message_events().get(0).getMessage_create();
        if (message != null && message.isEcho()) {
            return MessageTag.INBOX;
        } else if (request.getSendEmailNotification()) {
            return MessageTag.UNREAD;
        } else {
            return MessageTag.INBOX;
        }
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(),
                getMessengerContact(messageDTO).getId());
        if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
            messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
                    .map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
                            messengerMediaFile.getExtension()))
                    .collect(
                            Collectors.toList()));
        }
        messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
        messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        if (messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)) {
            messageDocumentDTO.setSpam(true);
        } else {
            messageDocumentDTO.setSpam(false);
        }
        return messageDocumentDTO;
    }

    @Override
    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setMsgId(dto.getConversationDTO().getId());
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        if (dto.getConversationDTO().getCreateDate() != null) {
            notificationRequest.setLastMsgTime(dto.getConversationDTO().getCreateDate().getTime());
        } else {
            notificationRequest.setLastMsgTime(new Date().getTime());
            log.info("onReceiveTwitterMessage: Both message sentOn and createDate found null for businessId {} smsID {} customer {}",
                    businessDTO.getBusinessId(), dto.getConversationDTO().getId(), dto.getCustomerDTO().getPhone());
        }
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.TWITTER.name());
        lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.TWITTER.getSourceId());
        lastMessageMetadataPOJO.setLastMessageSource(Source.TWITTER.getSourceId());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        lastMessageMetadataPOJO.setLastFbReceivedAt(sdf.format(new Date()));
        TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
        MessageCreate messaging = dto.getDirect_message_events().get(0).getMessage_create();
        Date lastMsgOn = new Date();
        String date = sdf.format(lastMsgOn);
        if (messaging != null && messaging.isEcho()) {
            lastMessageMetadataPOJO.setLastFbSentAt(date);
        } else {
            lastMessageMetadataPOJO.setLastFbReceivedAt(date);
        }
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        if (messaging != null && messaging.isEcho()) {
            messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        }
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
        messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
        MessageCreate msg = dto.getDirect_message_events().get(0).getMessage_create();
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(dto.getConversationDTO().getBody())
                && StringUtils.isNotEmpty(dto.getConversationDTO().getMediaUrl())) {
                messengerContact.setLastMessage("Received an attachment");
                if (msg != null && msg.isEcho()) {
                    messengerContact.setLastMessage("Sent an attachment");
                }
        } else {
            // messengerContact.setLastMessage(dto.getConversationDTO().getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact,
                dto.getConversationDTO().getEncrypted(), messengerContact.getTwitterConversationId(),
                businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }

    


    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        messageDTO.setMsgTypeForResTimeCalc("R"); // default - since its a receive handler. See processTwitterReceivedMessage() for actual type
        messageDTO.setSource(Source.TWITTER.getSourceId());
        TwitterMessageRequest twitterMessageRequest = (TwitterMessageRequest) messageDTO;
        log.info("Twitter receive request body: {} ",twitterMessageRequest);
        if (twitterMessageRequest.getDirect_message_events().get(0)==null ||
                twitterMessageRequest.getDirect_message_events().get(0).getMessage_create() == null || twitterMessageRequest.getDirect_message_events().get(0).getMessage_create().getMessage_data()==null) {
            log.info("Ignoring Unsupported event for Twitter recieve ");
            return null;
        }
        MessageCreate message = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
        if (message.getTarget() == null || isUnsupportedWebhookEvent(message)) {
            log.info("Ignoring seen/unsupported event for Twitter receive ");
            return null;
        }
        if(twitterMessageRequest.getFor_user_id().equals(message.getSender_id())){
            twitterMessageRequest.getDirect_message_events().get(0).getMessage_create().setEcho(true);
        }
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if(Objects.isNull(businessDTO)) {
            return null;
        }
        Integer isMessengerEnabled = businessServiceImpl.isMessengerEnabled(messageDTO.getBusinessDTO().getAccountId());
        if (!Integer.valueOf(0).equals(isMessengerEnabled)) {
            Optional<Lock> lockOpt = Optional.empty();
            try {
                String twitterUserId = message.getSender_id();
                TwitterMessageStatusEnum twitterMessageStatus= TwitterMessageStatusEnum.received;
                if (message.getTarget() != null && message.isEcho()) {
                    twitterUserId = message.getTarget().getRecipient_id();
                    twitterMessageStatus = TwitterMessageStatusEnum.sent;
                }

                String lockKey = Constants.Twitter_USER_ID_PREFIX + twitterUserId;
                lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
                if (!lockOpt.isPresent()) {
                    log.info("Lock is already acquired for the key:{}", lockKey);
                    kafkaService.publishToKafkaAsync(KafkaTopicEnum.TWITTER_RECEIVE_EVENT_DELAYED_QUEUE,
                            twitterMessageStatus);
                    return null;
                }


                if (messageDTO.getBusinessDTO() != null && messageDTO.getBusinessDTO().getBusinessId() != null && BusinessDTO.isActive(messageDTO.getBusinessDTO())) {
                    messageDTO.setBusinessDTO(businessDTO);
                }else {
                    log.error("BusinessDTO returned is null or inactive by core-service for request {} ", twitterMessageRequest);
                    return null;
                }
                CustomerDTO customerDTO = getCustomerDTO(messageDTO);
                if(null != message.getMessage_data() && Objects.nonNull(message.getMessage_data().getAttachment())) {
                    // Event with attachment
                    Attachment attachment = message.getMessage_data().getAttachment();
                        MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO();
                        messengerMediaFileDTO.setUrl(attachment.getMedia().getMedia_url_https());
                        updateMessengerMediaFIleDTO(messengerMediaFileDTO);
                        messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
                        // Either MediaFile or Text must be present to proceed.
                        if (messageDTO.getMessengerMediaFileDTO() != null || StringUtils.isNotBlank(message.getMessage_data().getText()))
                            processTwitterReceivedMessage(messageDTO, twitterMessageStatus);
                    
                } else {
                    processTwitterReceivedMessage(messageDTO, twitterMessageStatus);
                }
                if(twitterMessageRequest.getConversationDTO()==null || twitterMessageRequest.getConversationDTO().getId()==null) {
                    log.error("Error in saving twitter message for request {} ", twitterMessageRequest);
                    return null;
                }
                // handle PulseSurveyContext
                PulseSurveyContext context = null;
                try {
                    context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
                    if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
                        customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
                    } else {
                        customerDTO.setOngoingPulseSurvey(false);
                    }
                    messageDTO.setCustomerDTO(customerDTO);
                } catch (Exception ex) {
                    log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
                }
                super.handle(messageDTO);
            } finally {
                if (lockOpt.isPresent()) {
                    redisLockService.unlock(lockOpt.get());
                }
            }
        }else {
            log.info("Message Discarded. Messenger event received but messenger in not enabled for business {}", messageDTO.getBusinessDTO().getBusinessId());
        }
        return null;
    }

    private boolean isUnsupportedWebhookEvent(MessageCreate message) {
        boolean isUnsupportedWebhookEvent=false;
//        if(messaging.getMessage()!=null && (BooleanUtils.isTrue(messaging.getMessage().getIs_deleted()) || BooleanUtils.isTrue(messaging.getMessage().getIs_unsupported()))){
//            isUnsupportedWebhookEvent=true;
//        }
//        if(messaging.getMessage()!=null && (messaging.getMessage().getReply_to()!=null && messaging.getMessage().getReply_to().getStory()!=null)){
//            String text = "Replied to your story: " + messaging.getMessage().getText();
//            messaging.getMessage().setText(text);
//        }
        return isUnsupportedWebhookEvent;
    }

    private MessageDTO processTwitterReceivedMessage(MessageDTO messageDTO,TwitterMessageStatusEnum twitterMessageStatus) throws Exception {
        if(TwitterMessageStatusEnum.sent.equals(twitterMessageStatus)) messageDTO.setMsgTypeForResTimeCalc("S");
        TwitterMessageRequest twitterMessageRequest=(TwitterMessageRequest) messageDTO;
        MessageCreate message = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
        messageDTO.setMessengerContact(getMessengerContact(messageDTO));
        TwitterMessage twitterMessage=twitterEventService.saveTwitterMessage(messageDTO,message.getSender_id(),message.getTarget().getRecipient_id(), twitterMessageRequest.getDirect_message_events().get(0).getId(), twitterMessageStatus);
        log.debug("Twitter message Id:{} for Messenger contact Id {}",twitterMessage.getId(),messageDTO.getMessengerContact().getId());
        //Don't allow to proceed further if is_echo is true and message is send from Business dashboard
        if(twitterMessage.getId()!=null) {
            MessengerMessageMetaData messageMetaData=new MessengerMessageMetaData();
            messageMetaData.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);
            if(TwitterMessageStatusEnum.sent.equals(twitterMessageStatus)) {
                messageMetaData.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
            }
            ConversationDTO conversationDTO = new ConversationDTO(twitterMessage,messageMetaData);
            twitterMessageRequest.setConversationDTO(conversationDTO);
            messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), twitterMessage.getId());
            messengerMessageService.saveMessengerMessage(conversationDTO, null);
            messageDTO.setSendEmailNotification(true);
            if(TwitterMessageStatusEnum.sent.equals(twitterMessageStatus))
                messageDTO.setSendEmailNotification(false);
            return messageDTO;
        }
        return null;
    }

    private void updateMessengerMediaFIleDTO(MessengerMediaFileDTO messengerMediaFileDTO) {
        for (SUPPORTED_UPLOAD_FILES filetype : SUPPORTED_UPLOAD_FILES.values()) {
            if (messengerMediaFileDTO.getUrl() != null
                    && messengerMediaFileDTO.getUrl().contains(filetype.getExtension())) {
                messengerMediaFileDTO.setFileExtension(filetype.getExtension());
                if (messengerMediaFileDTO.getFileExtension().equalsIgnoreCase("mp4")) {
                    messengerMediaFileDTO.setContentType("video/mp4");
                    break;
                } else {
                    File file = new File(messengerMediaFileDTO.getUrl());
                    URLConnection connection = null;
                    try {
                        connection = file.toURL().openConnection();
                    } catch (IOException e) {
                        log.error("Error in obtaining file mime type for received attachment. Error message : {}",
                                e.getMessage());
                    }
                    String mimeType = connection.getContentType();
                    messengerMediaFileDTO.setContentType(mimeType);
                    messengerMediaFileDTO.setContentSize(String.valueOf(connection.getContentLength()));
                    break;
                }
            } else {
                messengerMediaFileDTO.setFileExtension("others");
            }
        }
    }

    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
            MessageCreate message = dto.getDirect_message_events().get(0).getMessage_create();
            String customerTwitterId;
            String pageId = message.getTarget().getRecipient_id();
            String userId = message.getSender_id();
            //handle echo
            if (message.getMessage_data() !=null && message.isEcho()) {
                customerTwitterId = message.getTarget().getRecipient_id();
                pageId = message.getSender_id();
            }else {
                customerTwitterId = message.getSender_id();
                pageId = message.getTarget().getRecipient_id();
            }
            userId = customerTwitterId;
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactRepository.findByTwitterConversationId(customerTwitterId, businessDTO.getBusinessId());
            if(messengerContact==null) {
                // not found in db then create new
                //	receive event make new messenger contact
                log.info("fetching user details from users array in request for twitter user Id {}: ",userId);
                User twitterUser = dto.getUsers().get(userId);
                //Name all user's as Annonymous user if user not found in array.
                if(Objects.isNull(twitterUser)) {
                   twitterUser.setName(Constants.ANONYMOUS_USER);
                }
                KontactoRequest kontactoRequest=createKontactoRequest(twitterUser,userId,businessDTO.getCountryCode());
                kontactoRequest.setBusinessId(businessDTO.getBusinessId());
                log.info("Requesting contact service to get/create customer for twitter user Id {}",userId);
                CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, businessDTO.getRoutingId(),
                        -8);
                log.info("Customer retrieved Id {} from contact service for twitter user Id {}",customerDTO.getId(),userId);

                messageDTO.setCustomerDTO(customerDTO);
                messengerContact=getOrCreateMessengerContactFromTwitter(twitterUser,userId,messageDTO);
            }
            // Messenger Contact reference for Twitter Message
            messageDTO.setMessengerContact(messengerContact);
        }

        return messengerContact;
    }

    private KontactoRequest createKontactoRequest(User twitterUser,String twitterUserId,String countryCode) {
        KontactoRequest kontactoRequest = new KontactoRequest();
        String custName = ControllerUtil.truncateLongContactName(twitterUser.getName());
        kontactoRequest.setName(custName);
        kontactoRequest.setEmailId(twitterUserId+"@x-dummy.com");
        kontactoRequest.setSource(KontactoRequest.TWITTER);
        KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
        locationInfo.setCountryCode(countryCode);
        kontactoRequest.setLocation(locationInfo);
        return kontactoRequest;
    }

    private MessengerContact getOrCreateMessengerContactFromTwitter(User twitterUser,String userId,MessageDTO messageDTO) {
        // get twitter user details
        MessengerContact messengerContact = messengerContactRepository.findByTwitterConversationId(userId,
                messageDTO.getBusinessDTO().getBusinessId());
        if (Objects.isNull(messengerContact)) {
            messengerContact = new MessengerContact();
            messengerContact.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
            messengerContact.setTwitterConversationId(userId);
            messengerContact.setImageUrl(twitterUser.getProfile_image_url_https());
            messengerContact.setCustomerId(messageDTO.getCustomerDTO().getId());
            messengerContact.setCreatedAt(new Date());
            messengerContactRepository.saveAndFlush(messengerContact);
        }
        return messengerContact;
    }

    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            TwitterMessageRequest twitterMessageRequest = (TwitterMessageRequest) messageDTO;
            MessageCreate msg = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
            if (msg != null && BooleanUtils.isTrue(msg.isEcho())) {
                userDTO = communicationHelperService.getUserDTO(Constants.Twitter_DUMMY_USER);
                twitterMessageRequest.setUserDTO(userDTO);
            }
        }
        return userDTO;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if (Objects.isNull(messageId)) {
            TwitterMessageRequest dto = (TwitterMessageRequest) messageDTO;
            messageId = dto.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        // Do Nothing
    }

    private String removeLastURL(String message) {
        String[] words = message.split(" ");
        String lastWord = words[words.length - 1];
        if (isURL(lastWord)) {
            message = String.join(" ", Arrays.copyOf(words, words.length - 1));
        }
        return message;
    }
    
    private boolean isURL(String text) {
        String urlPattern = "https?://\\S+";
        return text.matches(urlPattern);
    }

}
