package com.birdeye.messenger.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.TeamAssigneeDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommunicationHelperServiceImpl implements CommunicationHelperService {

    private final UserService userService;
    private final BusinessService businessService;
    private final ContactService contactService;

    @Override
    public BusinessDTO getBusinessDTO(Integer businessId) {
        return businessService.getBusinessDTO(businessId);
    }

    @Override
    public CustomerDTO getCustomerDTO(Integer customerId) {
    	if (Objects.nonNull(customerId)) {
    		return contactService.findById(customerId);
    	}
    	return null;
    }

    @Override
    public UserDTO getUserDTO(Integer userId) {
        return userService.getUserDTO(userId);
    }
    
	@Override
	public Map<Integer, List<Integer>> getValidUserDTOs(Integer userId, Integer accountId, Set<Integer> businessId) {
		return userService.getValidUserDTOs(userId, accountId, businessId);
	}
    
	@Override
	public Map<Integer, TeamAssigneeDto> getValidUserDTOs(Integer userId, Integer accountId, Integer businessId) {
		return userService.getValidUserDTOs(userId, accountId, businessId);
	}


}
