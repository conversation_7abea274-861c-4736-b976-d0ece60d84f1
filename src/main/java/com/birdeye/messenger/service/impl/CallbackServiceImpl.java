package com.birdeye.messenger.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dao.entity.WhatsappMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.CustomerUnsubscribe;
import com.birdeye.messenger.dto.EmailSubscribeStatus;
import com.birdeye.messenger.dto.EmailUnsubscribeStatus;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.SmsMessageStatus;
import com.birdeye.messenger.dto.TenDlcStatusDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.whatsapp.Status;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.WhatsappMessageStatusEnum;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.CallbackService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.BeanUtils;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CallbackServiceImpl implements CallbackService {

	private final BusinessService businessService;
	private final MessengerContactService messengerContactService;
	private final SmsService smsService;
	private final ContactService contactService;
	private final KafkaService kafkaService;
	private final EmailService emailService;
	private final FirebaseService fcmService;
	private final PulseSurveyService pulseSurveyService;
	private final RedisLockService redisLockService;
	private final NexusService nexusService;
	private final ElasticSearchExternalService elasticSearchExternalService;
	private final WhatsappMessageService whatsappMessageService;
	
	@Override
	public void messengerSMSDeliveryStatusUpdate(SmsMessageStatus request) throws Exception {
		String campaignTemplateTypes = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("campaign_callback_template_types", "messenger");
		if (campaignTemplateTypes.contains(request.getRequestType())
				|| "messenger".equalsIgnoreCase(request.getRequestType())) {
			Sms sms = null;
            if(request.getParams().get("rType").equals("messenger")|| request.getParams().get("rType").equals("messenger_p")) {
				 sms = smsService.findById(Integer.valueOf(request.getExternalUid()));
			} else {
				sms = smsService.findByReviewRequestIdAndType(request.getExternalUid(),request.getParams().get("rType").toString());
				if (sms == null && null != request.getReviewRequestId()) {
					log.info("messengerSMSDeliveryStatusUpdate : Old update from twilio : {}", request);
					sms = smsService.findByReviewRequestId(request.getReviewRequestId());
				}
			}
            if (sms == null) {
            	log.info("messengerSMSDeliveryStatusUpdate : No sms found for request : {}", request);
            	return;
            }
			BusinessDTO businessDto = businessService.getBusinessLiteDTO(sms.getBusinessId());
			if (Objects.isNull(businessDto)) {
				log.info(
						"smsCallback event dropped as business returned is null for bId {}",
						sms.getBusinessId());
				return;
			}

			boolean messengerEnabled = businessService.getMessengerEnabled(businessDto.getAccountId());
			if (!MessengerUtil.checkIfMessengerEnabledorAccountTypeisDirect(businessDto, messengerEnabled)) {
				log.info(
						"Messenger is disabled for business id: {} and account Type is not direct, hence dropping sms callback event.",
						businessDto.getAccountId());
				return;
			}

			Optional<Lock> lockOpt = Optional.empty();
			String lockKey = "";
			try {
				lockKey = Constants.CUSTOMER_ID_PREFIX+sms.getCustomerId();
				lockOpt = redisLockService.tryLock(lockKey);
				if (lockOpt.isPresent()){
					lockAcquiredDeliveyStatusUpdate(request, sms,businessDto);
				} else {
					//add to delayed queue
					log.info("[SMS delivery event] Unable to acquire lock key:{}",lockKey);
					kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_DELIVERY_DELAYED_QUEUE, request);
				}
			}finally {
				if (lockOpt.isPresent()){
					redisLockService.unlock(lockOpt.get());
				}
			}
		}
	}

	private void lockAcquiredDeliveyStatusUpdate(SmsMessageStatus request, Sms sms, BusinessDTO businessDTO)
			throws Exception {
		String status = request.getStatus();
		Sms resentSms = new Sms();
		MessengerContact messengerContact = messengerContactService.findByCustomerId(sms.getCustomerId());
		if (messengerContact == null) {
			pushCallBackEventToDelayedQueue(request);
			return;
		}
		ESFindByFieldRequest<MessageDocument> esFindRequest = new ESFindByFieldRequest.Builder<MessageDocument>()
				.setIndex(Constants.Elastic.MESSAGE_INDEX)
				.setRoutingId(businessDTO.getAccountId())
				.setDocumentType(MessageDocument.class)
				.setShouldFields("_id", String.valueOf(sms.getSmsId()))
				.setShouldFields("mediaFiles.msg_id", String.valueOf(sms.getSmsId()))
				.setShouldFields("smsId",String.valueOf(sms.getSmsId()))
				.setMinShouldMatch(1)
				.build();
		esFindRequest.isValidateRequest();
		List<MessageDocument> messages=elasticSearchExternalService.findDocumentByField(esFindRequest);
		if(CollectionUtils.isEmpty(messages) || messages.size()>1) {
			log.info("No message doc found in ES data store for _id:  ", sms.getSmsId());
			return;
		}
		MessageDocument message=messages.get(0);
		ConversationDTO conversationDTO = new ConversationDTO();
		addMessageMetaData(conversationDTO);

		append10DlcBrandingToMessageBody(sms,businessDTO);
		if (StringUtils.isNotBlank(sms.getFailureReason()) && "delivered".equalsIgnoreCase(status)) {
			BeanUtils.merge(resentSms, sms);
			resentSms.setSmsId(null);
			resentSms.setMediaURL(null);
			resentSms.setSentOn(new Date());
			resentSms.setCreateDate(new Date());
			resentSms.setFailureReason(null);
			resentSms.setMessageSid(String.valueOf(sms.getSmsId()));
			smsService.saveSMS(resentSms);
			String msg_status=StringUtils.isEmpty(resentSms.getFailureReason()) ? "success" : resentSms.getFailureReason();
			updateMessageDeliveryStatus(sms, message,msg_status);
			messengerContactService.updateMessageOnES(message, businessDTO.getAccountId());
		}
		if (StringUtils.isEmpty(sms.getMessageSid())
				|| !sms.getMessageSid().equalsIgnoreCase(sms.getSmsId().toString())) {
			if ("failed".equalsIgnoreCase(status) || "undelivered".equalsIgnoreCase(status)) {
				log.info("undelivered SMSDeliveryStatus {}", request.getStatus());
				Integer errCode = request.getErrorCode();
				CustomerDTO customerDTO = null;
				if (sms.getCustomerId() != 0) {
					customerDTO = contactService.findById(sms.getCustomerId());
					if (customerDTO != null && errCode != null && !isErrorCodeToBeSkipped(errCode) && ("messenger".equalsIgnoreCase(request.getRequestType()))) {
						// Hit Kontacto to unsubscribe on topic : messenger-customer-unsub
						CustomerUnsubscribe unsubscribeRequest = new CustomerUnsubscribe(sms.getCustomerId(),
								request.getExternalStatus(), request.getUnsubscribeType(), false, null, null, null);
						kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOMER_UNSUBSCRIBE, null, unsubscribeRequest);
					}
				}
				String failureReason;
				if(errCode != null){
					failureReason = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getErrorMessage(request.getErrorCode().toString(),"message");
				}else{
					failureReason = "Failed to send message";
				}
				sms.setFailureReason(failureReason);
				smsService.saveSMS(sms);
                sms.setCreateDate(null);
                sms.setSentOn(null);

                String msg_status=StringUtils.isEmpty(sms.getFailureReason()) ? "success" : sms.getFailureReason();
				updateMessageDeliveryStatus(sms, message,msg_status);
				messengerContactService.updateMessageOnES(message, businessDTO.getAccountId());
				//Terminate PulseSurveyContext if it exist
				PulseSurveyContext context = null;
				try {
					if (customerDTO != null && customerDTO.getId()!=null) {
						context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
						if (context != null) {
							ContactDocument contactDocument = new ContactDocument();
							contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
							messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, businessDTO.getRoutingId());
						}
					}
				} catch (Exception ex) {
					log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
				}
				
			} else if ("delivered".equalsIgnoreCase(status)) {
				sms.setMessageSid(request.getExternalUid());
				log.info("delivered sms {}", sms);
				smsService.saveSMS(sms);
			}
		}
		// Promotion Update handled by Campaign Service
		// RR Update - call platform service wrapper
		// smsController.updateReviewRequestStatus
//		if ("messenger".equalsIgnoreCase(request.getRequestType())) {
//			if (sms.getReviewRequestId() != null) {
//				platformService.updateReviewRequestStatus(sms.getSmsId(), request.getFailureReason());
//			} else if (resentSms.getReviewRequestId() != null) {
//				platformService.updateReviewRequestStatus(resentSms.getSmsId(), request.getFailureReason());
//			}
//		}
	}

	private void pushCallBackEventToDelayedQueue(SmsMessageStatus request) {
		Integer retryCount = request.getRetryCount();
		String maxRetries = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(
				Constants.PROPERTY_DELAYED_EVENT_MAX_RETRIES, Constants.DEFAULT_DELAYED_EVENT_MAX_RETRIES);
		if (retryCount == null || retryCount < Integer.valueOf(maxRetries)) {
			request.setRetryCount(retryCount == null ? 1 : retryCount + 1);
			log.info("[SMS delivery event] mc not found, pushed  to delayed queue");
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_DELIVERY_DELAYED_QUEUE, request);
		} else {
			log.info("[SMS delivery event] retries exceeded. Discarding callback  and pushing to DLQ.");
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_CALLBACK_DEAD_LETTER_QUEUE, request);
		}
	}
	
	private boolean isErrorCodeToBeSkipped(Integer errCode) {
		String skipErrorCodes = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getProperty(Constants.PROPERTY_ERROR_CODES_TO_SKIP_UNSUBSCRIBE, Constants.DEFAULT_SKIP_TWILIO_ERROR_CODES);
		List<String> errorCodesList = ControllerUtil.getTokensListFromString(skipErrorCodes);
		return errorCodesList.contains(String.valueOf(errCode));

	}

	private void updateMessageDeliveryStatus(Sms sms, MessageDocument message,String msg_status) {
		if (message.getM_id().equals(sms.getSmsId().toString())) {
			message.setMsg_status(msg_status);
		}else if(Objects.nonNull(message.getSmsId()) && message.getSmsId().equals(sms.getSmsId())){
			message.setMsg_status(msg_status);
		}
		updateAttachmentDeliveryStatus(message, msg_status, sms.getSmsId().toString());

	}

	private void updateAttachmentDeliveryStatus(MessageDocument message, String msg_status, String smsid) {
		List<MediaFile> mediaFiles=message.getMediaFiles();
		if (CollectionUtils.isNotEmpty(mediaFiles)) {
			List<MediaFile> updatedMediaFIles = mediaFiles.stream().map(media -> {
				if (StringUtils.isNotBlank(media.getMsg_id()) && smsid.equals(media.getMsg_id())) {
					media.setMsg_status(msg_status);
				}
				return media;
			}).collect(Collectors.toList());
			message.setMediaFiles(updatedMediaFIles);
		}
	}

	private void addMessageMetaData(ConversationDTO conversationDTO) {
		conversationDTO.setMessageType(MessageType.CHAT);
//		conversationDTO.setChannel(Channel.VOICE_CALL);
		conversationDTO.setCommunicationDirection(CommunicationDirection.SEND);
		conversationDTO.setSentThrough(SentThrough.WEB);
	}


	@Override
	public void updateEmailStatusAfterUnsubscribe(EmailUnsubscribeStatus emailUnsubscribeStatus) throws Exception {
		if (emailUnsubscribeStatus.getRequestId() != null) {
			emailUnsubscribeFromCampaign(emailUnsubscribeStatus);
		} else {
			emailUnsubscribeFromEmailInbox(emailUnsubscribeStatus);
		}
	}

	private void emailUnsubscribeFromCampaign(EmailUnsubscribeStatus emailUnsubscribeStatus) {
		log.debug("Going to email unsubscribe for campaign reviewRequestId {} ", emailUnsubscribeStatus.getRequestId());
		Email email = emailService.findByReviewRequestId(emailUnsubscribeStatus.getRequestId());
		if (email == null) {
			log.error("No email record found for reviewRequestId {}", emailUnsubscribeStatus.getRequestId());
			return;
		}
		// setting these values bcoz these values are not coming from sendgrid side
		emailUnsubscribeStatus.setCustomerId(email.getCustomerId());
		emailUnsubscribeStatus.setBusinessId(email.getBusinessId());
		emailUnsubscribeStatus.setEmailId(String.valueOf(email.getId()));

		log.debug("Going to hit Kontacto to unsubscribe topic : messenger-customer-unsub");
		CustomerUnsubscribe unsubscribeRequest = new CustomerUnsubscribe(emailUnsubscribeStatus.getCustomerId(),
				emailUnsubscribeStatus.getExternalStatus(), emailUnsubscribeStatus.getUnsubscribeType(), true,
				emailUnsubscribeStatus.getEmailCategory(), null, null);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOMER_UNSUBSCRIBE, null, unsubscribeRequest);

		// update email unsubscribe reason in email table and es.
		email.setFailureReason(emailUnsubscribeStatus.getUnsubscribeType());
		emailService.saveEmail(email);
		updateEmailStatusInEs(email, emailUnsubscribeStatus, Channel.CAMPAIGN);
	}

	private ConversationDTO addMessageMetaDataForEmail(Channel channnel) {
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setCommunicationDirection(CommunicationDirection.SEND);
		conversationDTO.setSentThrough(SentThrough.WEB);
		conversationDTO.setChannel(channnel);
		conversationDTO.setSource(111);
		return conversationDTO;
	}

	private void updateEmailStatusInEs(Email email, EmailUnsubscribeStatus emailUnsubscribeStatus, Channel channnel) {
		// message document only update for dropped/bounce failure reason
		List<String> restrictEmailStatus = Arrays.asList("dropped", "bounce");
		if (!(restrictEmailStatus.contains(email.getFailureReason()))) {
			email.setFailureReason(null);
		}else{
			email.setFailureReason(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getErrorMessage(emailUnsubscribeStatus.getUnsubscribeType().toString(),"email"));
		}
		BusinessDTO businessDTO = businessService.getBusinessDTO(emailUnsubscribeStatus.getBusinessId());
		MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
				emailUnsubscribeStatus.getBusinessId(), emailUnsubscribeStatus.getCustomerId(),
				businessDTO.getAccountId());
		ConversationDTO conversationDTO = addMessageMetaDataForEmail(channnel);
		if(StringUtils.isNotEmpty(emailUnsubscribeStatus.getRequestType()) && ("appointment_reminder").equals(emailUnsubscribeStatus.getRequestType())){
			log.info("update failure/unsubscribe status for appointment reminder for emailID: {}",email.getId());
			ESFindByFieldRequest<MessageDocument> esFindRequest=new ESFindByFieldRequest.Builder<MessageDocument>()
					.setIndex(Constants.Elastic.MESSAGE_INDEX)
					.setRoutingId(businessDTO.getAccountId())
					.setDocumentType(MessageDocument.class)
					.setShouldFields("emailId",String.valueOf(email.getId()))
					.setMinShouldMatch(1)
					.build();
			esFindRequest.isValidateRequest();
			List<MessageDocument> messages=elasticSearchExternalService.findDocumentByField(esFindRequest);
			if(CollectionUtils.isEmpty(messages)||messages.size()>1){
				log.info("No message doc found in ES data store for _id:  ",email.getId());
				return;
			}
			MessageDocument message=messages.get(0);
			String msg_status=StringUtils.isEmpty(email.getFailureReason()) ? "success" : email.getFailureReason();
			message.setMsg_status(msg_status);
			messengerContactService.updateMessageOnES(message, businessDTO.getAccountId());
		}else{
			messengerContactService.andNewMessageOnEs(new MessageDocumentDTO(emailUnsubscribeStatus.getEmailId(),
					conversationDTO,messengerContact.getId(),email),null,null,businessDTO,MessengerEvent.EMAIL_SEND);
		}
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(businessDTO.getAccountId());
		firebaseDto.setBusinessId(businessDTO.getBusinessId());
		firebaseDto.setMcId(messengerContact.getId());
		fcmService.mirrorOnWeb(firebaseDto);
	}

	private void emailUnsubscribeFromEmailInbox(EmailUnsubscribeStatus emailUnsubscribeStatus) {
		log.info("Going to hit Kontacto to unsubscribe topic : messenger-customer-unsub");
		/**
		 * Hit Kontacto to unsubscribe topic : messenger-customer-unsub
		 * 
		 */
		log.info("External Status {}  for customer Id {} ", emailUnsubscribeStatus.getExternalStatus(),
				emailUnsubscribeStatus.getCustomerId());
		CustomerUnsubscribe unsubscribeRequest = new CustomerUnsubscribe(emailUnsubscribeStatus.getCustomerId(),
				emailUnsubscribeStatus.getExternalStatus(), emailUnsubscribeStatus.getUnsubscribeType(), true,
				emailUnsubscribeStatus.getEmailCategory(), null, null);
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOMER_UNSUBSCRIBE, null, unsubscribeRequest);
		/**
		 * update message document to update email msg status in ES
		 */
		log.info("Going to update messageDocument for emailId {} and businessId {}",
				emailUnsubscribeStatus.getEmailId(), emailUnsubscribeStatus.getBusinessId());
		Email email = emailService.findById(Integer.valueOf(emailUnsubscribeStatus.getEmailId()));
		if (email != null) {
			email.setFailureReason(emailUnsubscribeStatus.getUnsubscribeType());
			emailService.saveEmail(email);
			updateEmailStatusInEs(email, emailUnsubscribeStatus, Channel.EMAIL);
		} else {
			log.error("No email record found for emailId {}", emailUnsubscribeStatus.getEmailId());
		}
	}

	private void append10DlcBrandingToMessageBody(Sms sms,BusinessDTO businessDTO){
		log.info("Append Ten Dlc Branding for sms {} account {}",sms.getSmsId(),businessDTO.getAccountId());
		String message = StringUtils.isNotBlank(sms.getMessageBody()) ? sms.getMessageBody() : "";

		String lastMessage=message;
		boolean decryptionFailed=Boolean.FALSE;
		if(StringUtils.isNotBlank(sms.getMessageBody())&&Objects.nonNull(sms.getEncrypted())
				&&sms.getEncrypted().equals(1)){
			try{
				lastMessage=EncryptionUtil.decrypt(sms.getMessageBody(),
						StringUtils.join(sms.getFromNumber(),sms.getToNumber()),
						StringUtils.join(sms.getToNumber(),sms.getFromNumber()),false);
				message=lastMessage;
				if(StringUtils.equals(lastMessage,""))
					throw new Exception("Decryption Failed");
			}catch(Exception e){
				log.error("smsDeleiveryStatus: decryption failed request info: smsId {}",
						sms.getSmsId(),e);
				decryptionFailed=Boolean.TRUE;
			}
		}
		if(!decryptionFailed){
			TenDlcStatusDTO tenDlcStatusDTO=nexusService.checkTenDlcStatus(businessDTO.getEnterpriseNumber());
			if(Objects.nonNull(tenDlcStatusDTO)&&tenDlcStatusDTO.isUsAccount()){
				String status=tenDlcStatusDTO.getStatus();
				TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo=tenDlcStatusDTO.getTollFreeInfo();
				if(Objects.nonNull(status) && (status.equals("not_started")||status.equals("in_progress")||status.equals("failed")||status.equals("not_required_demo_account"))
						&& Objects.nonNull(tollFreeInfo) && Objects.nonNull(tollFreeInfo.getBranding()) && tollFreeInfo.isAvailable()){
					if (StringUtils.isBlank(message)) {
						message = message.concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
					} else if (!message.contains(("\n\n").concat(tollFreeInfo.getBranding()))){
						message=message.concat("\n\n").concat(tollFreeInfo.getBranding());
					}
					sms.setMessageBodyUnencrypted(message);
					sms.setMessageBody(message);
					smsService.encrypt(sms);
				}
			}
		}
	}

	@Override
	public void updateEmailSubscribeStatus(EmailSubscribeStatus emailSubscribeStatus) {
		log.debug("update email subscription from group_resubscribe : {}", emailSubscribeStatus);
		BusinessDTO businessDTO = businessService.getBusinessDTO(emailSubscribeStatus.getBusinessId());
		if (Objects.nonNull(emailSubscribeStatus.getEvent()) && "group_resubscribe".equals(emailSubscribeStatus.getEvent())) {
			contactService.updateCustomerSubscriptionStatus(emailSubscribeStatus.getCustomerId(), businessDTO.getAccountId(), null,emailSubscribeStatus.getEmailCategory(), null, null);
		}
	}
	
	@Override
	public void updateWAMessageDeliveryStatus(WhatsappMessageRequest whatsappMessageRequest) {
		Status waStatus = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getStatuses().get(0);
		Integer failureCode = null;
		String failureReason = null;
		if(Objects.nonNull(waStatus) &&  WhatsappMessageStatusEnum.failed.name().equalsIgnoreCase(waStatus.getStatus())) {
			failureReason = waStatus.getErrors().get(0).getError_data().getDetails();
			failureCode = waStatus.getErrors().get(0).getCode();
			WhatsappMessage waMsg = whatsappMessageService.findWAMessageById(waStatus.getId());
			if (Objects.isNull(waMsg)) {
				log.info("No WA Msg found {}",waStatus.getId());
				return;
			}
			BusinessDTO businessDTO = businessService.getBusinessLiteDTO(waMsg.getBusinessId());
			MessengerContact messengerContact = messengerContactService.findById(waMsg.getMessengerContactId());
			if (messengerContact == null) {
				log.info("Messenger contact not present {}", waMsg.getMessengerContactId());
				return;
			}

			Optional<Lock> lockOpt = Optional.empty();
			String lockKey = "";
			try {
				lockKey = Constants.CUSTOMER_ID_PREFIX+messengerContact.getCustomerId();
				lockOpt = redisLockService.tryLock(lockKey);
				if (lockOpt.isPresent()){
					ESFindByFieldRequest<MessageDocument> esFindRequest = new ESFindByFieldRequest.Builder<MessageDocument>()
							.setIndex(Constants.Elastic.MESSAGE_INDEX)
							.setRoutingId(businessDTO.getAccountId())
							.setDocumentType(MessageDocument.class)
							.setShouldFields("_id", String.valueOf(waMsg.getId() + "_wa"))
							.setMinShouldMatch(1)
							.build();
					esFindRequest.isValidateRequest();
					List<MessageDocument> esMessages=elasticSearchExternalService.findDocumentByField(esFindRequest);
					if(CollectionUtils.isEmpty(esMessages) || esMessages.size() > 1) {
						log.info("No message doc found in ES data store for _id:  ", waMsg + "_wa");
						return;
					}
					MessageDocument esMessageDoc = esMessages.get(0);


					log.info("undelivered updateWAMessageDeliveryStatus {}", whatsappMessageRequest.toString());
					CustomerDTO customerDTO = null;
					if (messengerContact.getCustomerId() != 0) {
						customerDTO = contactService.findById(messengerContact.getCustomerId());
						if (customerDTO != null && failureCode != null && isErrorCodeToUnsub(failureCode)) {
							// Hit Kontacto to unsubscribe on topic : messenger-customer-unsub
							CustomerUnsubscribe unsubscribeRequest = new CustomerUnsubscribe(messengerContact.getCustomerId(),
									null, null, false, null, "Unsubscribed", null);
							kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOMER_UNSUBSCRIBE, null, unsubscribeRequest);
						} else if (customerDTO != null && failureCode != null && errorCodeNumNotAvailable(failureCode)) {
							// Hit Kontacto to unsubscribe on topic : messenger-customer-unsub
							CustomerUnsubscribe unsubscribeRequest = new CustomerUnsubscribe(messengerContact.getCustomerId(),
									null, null, false, null, null, false);
							kafkaService.publishToKafkaAsync(KafkaTopicEnum.CUSTOMER_UNSUBSCRIBE, null, unsubscribeRequest);
						}
					}
					//					String mappedFailureReason;
					//					if(failureCode != null){
					//						mappedFailureReason = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getErrorMessage(String.valueOf(failureCode + "_wa"), "message");
					//					}else{
					//						mappedFailureReason = "Failed to send message";
					//					}
					waMsg.setStatus(WhatsappMessageStatusEnum.failed.name());
					waMsg.setFailureReason(failureReason);
					whatsappMessageService.saveWAMsg(waMsg);

					String msg_status = StringUtils.isEmpty(waMsg.getFailureReason()) ? "success" : waMsg.getFailureReason();
					updateWAMessageDeliveryStatus(waMsg, esMessageDoc, msg_status);
					messengerContactService.updateMessageOnES(esMessageDoc, businessDTO.getAccountId());
					
					//Terminate PulseSurveyContext if it exist
					PulseSurveyContext context = null;
					try {
						if (customerDTO != null && customerDTO.getId()!=null) {
							context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
							if (context != null) {
								ContactDocument contactDocument = new ContactDocument();
								contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
								messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, businessDTO.getRoutingId());
							}
						}
					} catch (Exception ex) {
						log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
					}
				} else {
					//add to delayed queue
					log.info("[SMS delivery event] Unable to acquire lock key:{}",lockKey);
					kafkaService.publishToKafkaAsync(KafkaTopicEnum.WHATSAPP_RECEIVE_EVENT_DELAYED_QUEUE, whatsappMessageRequest);
				}
			}finally {
				if (lockOpt.isPresent()){
					redisLockService.unlock(lockOpt.get());
				}
			}
		}
	}
	
	private boolean isErrorCodeToUnsub(Integer errCode) {
		String skipErrorCodes = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getProperty(Constants.WA_UNSUBSCRIBE_ERROR_CODE, Constants.DEFAULT_WA_UNSUBSCRIBE_ERROR_CODE);
		List<String> errorCodesList = ControllerUtil.getTokensListFromString(skipErrorCodes);
		return errorCodesList.contains(String.valueOf(errCode));

	}
	
	private boolean errorCodeNumNotAvailable(Integer errCode) {
		String skipErrorCodes = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getProperty(Constants.WA_NUM_NOT_AVAILABLE_ERROR_CODE, Constants.DEFAULT_WA_NUM_NOT_AVAILABLE_ERROR_CODE);
		List<String> errorCodesList = ControllerUtil.getTokensListFromString(skipErrorCodes);
		return errorCodesList.contains(String.valueOf(errCode));

	}
	
	
	
	private void updateWAMessageDeliveryStatus(WhatsappMessage waMsg, MessageDocument message, String msg_status) {
		message.setMsg_status(msg_status);
		updateAttachmentDeliveryStatus(message, msg_status, (waMsg.getId()+"_wa").toString());

	}
}
