package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.AppleMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.ConversationStateDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessageResponse.Message;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendAppleMessageSocialDto;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.apple.chat.AppleAttachments;
import com.birdeye.messenger.dto.apple.chat.AppleInteractiveMessageData;
import com.birdeye.messenger.dto.apple.chat.AppleMediaFiles;
import com.birdeye.messenger.dto.apple.chat.AppleQuickReplyDto;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadMessengerRequest;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadResponse;
import com.birdeye.messenger.dto.apple.chat.AttachmentPreUploadSocialRequest;
import com.birdeye.messenger.dto.apple.chat.RichLinkData;
import com.birdeye.messenger.dto.apple.chat.RobinSuggestionResponse;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.AppleChatSessionEnum;
import com.birdeye.messenger.enums.AppleInteractiveMessageType;
import com.birdeye.messenger.enums.AppleMessageStatusEnum;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.AppleInteractiveEventHandler;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.LiveChatService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.apple.messaging.AppleSocialIntegrationService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppleSendHandler extends MessageEventHandlerAbstract {
	private final List<AppleInteractiveEventHandler> appleInteractiveEventHandlers;
    private final MessengerMessageService messengerMessageService;
    private final AppleSocialIntegrationService appleSocialIntegrationService;
    private final SendMessageService sendMessageService;
    private final BusinessService businessService;
    private final MessengerMediaFileService messengerMediaFileService;
    private final CommonService commonService;
	@Lazy
	private final LiveChatService liveChatService;
    @Autowired
	private RedisHandler redisHandler;
    @Override
    public MessengerEvent getEvent() {
        return MessengerEvent.APPLE_SEND;
    }

    /*
     * GET MESSENGER CONTACT
     * Generate messageId
     * Add Entry in apple_message
     * Add Entry in messenger_message
     * Update messenger_contact
     * Update ES
     * Update Response time (Report support)
     * Notification + Sync + event + other activities
     */
    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        // Responsible for time calculation
        if(!messageDTO.isBOT()) messageDTO.setMsgTypeForResTimeCalc("S");
        BusinessDTO businessDTO=getBusinessDTO(messageDTO);
        getCustomerDTO(messageDTO);
        MessengerContact messengerContact=getMessengerContact(messageDTO);
        getUserDTO(messageDTO);

        log.info("Send Apple Message request From : {} to : {}", messageDTO.getBusinessDTO().getBusinessId(), messageDTO.getMessengerContact().getAppleConversationId());

        SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
        sendTypingEvent(getConverSationSateDto(businessDTO,messengerContact.getId()),
        		Constants.APPLE_TYPING_END_EVENT);
        SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages);
        sendResponse.setLastMsgSource(messageDTO.getSource());
        sendResponse.setMessages(messages);
        return sendResponse;
    }

    private ConversationStateDTO getConverSationSateDto(BusinessDTO businessDTO, Integer mcId) {
    	ConversationStateDTO conversationStateDTO=new ConversationStateDTO();
    	conversationStateDTO.setMc_id(mcId);
    	conversationStateDTO.setEnterpriseId(businessDTO.getEnterpriseId());
    	conversationStateDTO.setEnterpriseNumber(businessDTO.getEnterpriseNumber());
    	conversationStateDTO.setBusinessId(businessDTO.getBusinessId());
    	conversationStateDTO.setBusinessNumber(businessDTO.getBusinessNumber());
    	return conversationStateDTO;
	}

	private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		sendMessageDTO.setCustomerId(messengerContact.getCustomerId());
        sendMessageDTO.setSource(Source.APPLE.getSourceId());
        sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
		String beforeBody = sendMessageDTO.getBody();
		commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO), "apple");
		if (CollectionUtils.isNotEmpty(sendMessageDTO.getMultiMessages()) && beforeBody != null
				&& !beforeBody.equals(sendMessageDTO.getBody())) {
			List<String> messages = sendMessageDTO.getMultiMessages();
			beforeBody = StringUtils.difference(messages.get(messages.size() - 1), sendMessageDTO.getBody());
			String urlFromString = beforeBody;
			log.info("beforeBody : {},sendMessageDTO.getBody() : {},urlFromString : {}", beforeBody,
					sendMessageDTO.getBody(), urlFromString);
			urlFromString = urlFromString.replace("\n", "");
			if (MessengerUtil.isValidURL(urlFromString.trim())) {
//				urlFromString = urlFromString.replace("\n", "");
				messages.add(urlFromString);
			} else {
			messages.set(messages.size() - 1, sendMessageDTO.getBody());
			}
			sendMessageDTO.setBody(null);
		}
		log.info("multimessages : {}", sendMessageDTO.getMultiMessages());
		return handleMultiMediaAndTextMessages(sendMessageDTO);
    }

    private Message sendMessageToApple(MessageDTO messageDTO, MessengerEvent event) throws Exception {
        String messageId = generateUniqueMessageId();
        prepareAndCallSocialAppleSend(messageDTO, messageId, getMessengerContact(messageDTO));
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        UserDTO userDTO = getUserDTO(messageDTO);
		String robinSuggestion = null;
        if(AppleInteractiveMessageType.LIST_PICKER.equals(sendMessageDTO.getAppleInteractiveMessageType())) {
			if (StringUtils.isNotBlank(sendMessageDTO.getBody())) {
				SuggestionHolder suggestionHolder = JSONUtils.fromJSON(sendMessageDTO.getBody(),
						SuggestionHolder.class);
				RobinSuggestionResponse robinSuggestionResponse = new RobinSuggestionResponse(suggestionHolder);
				if (!MessengerUtil.checkIfAnyValueIsNull(robinSuggestionResponse.getTitle(),
						robinSuggestionResponse.getSubTitle(), robinSuggestionResponse.getSuggestions())) {
					robinSuggestion = JSONUtils.toJSON(robinSuggestionResponse);
                    sendMessageDTO.setBody(robinSuggestion);
				} else {
					sendMessageDTO.setBody(MessengerConstants.DEFAULT_SUGGESTION_RESPONSE_BODY);
				}

			}
		}
		ConversationDTO conversationDTO = saveAppleMessage(messageDTO, messageId);
		if (StringUtils.isNotBlank(sendMessageDTO.getPlainTextRobinResponse())) {
			conversationDTO.setMessageType(MessageType.RICH_CONTENT_CHAT);
		}
		if (AppleInteractiveMessageType.LIST_PICKER.equals(sendMessageDTO.getAppleInteractiveMessageType())) {
			conversationDTO.setMessageType(MessageType.ROBIN_SUGGESTION);
            sendMessageDTO.setBody(MessengerConstants.APPLE_ROBIN_SUGGESTION_HEADER);
        }
		messengerMediaFileService.saveMediaFiles(messageDTO.getMessengerMediaFileList(), conversationDTO.getId());
        sendMessageDTO.setConversationDTO(conversationDTO);
        sendMessageService.audit(sendMessageDTO, event, userDTO);
        super.handle(sendMessageDTO);
        updateAppleChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), userDTO);
		Message message = new Message(userDTO, conversationDTO, sendMessageDTO.getMediaUrls(),
                messageDTO.getBusinessDTO().getTimeZoneId());
        message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
		if (Objects.nonNull(messageDTO.getRichLinkData())) {
        	message.setRichLinkData(messageDTO.getRichLinkData());
        }
        return message;
    }

	private void updateAppleChatSession(Integer accountId,Integer mcId,UserDTO userDTO) {
		if(userDTO!=null && userDTO.getId()!=MessengerConstants.ROBIN_REPLY_USER) {
        	ContactDocument contactDocument=messengerContactService.getContact(accountId, mcId);
        	if(contactDocument.getAppleContextDocument()!=null) {
        		contactDocument.getAppleContextDocument().setAppleChatSession(AppleChatSessionEnum.MANUAL);
        		messengerContactService.updateContactDocumentOnES(contactDocument, contactDocument.getId().toString(), contactDocument.getE_id());
        	}
        }
	}

    private String generateUniqueMessageId() {
        return UUID.randomUUID().toString();
    }


    void prepareAndCallSocialAppleSend(MessageDTO messageDTO, String messageId, MessengerContact messengerContact) throws Exception {
    	SendMessageDTO sendMessageDTO= (SendMessageDTO) messageDTO;
    	String body = ((SendMessageDTO) messageDTO).getBody();
    	BusinessDTO businessDTO=messageDTO.getBusinessDTO();
    	boolean isSMB=businessDTO.getEnterpriseId()==null;
    	String pageId=appleSocialIntegrationService.getApplePageIdByBusinessId(businessDTO.getBusinessId(),businessDTO.getEnterpriseNumber(),isSMB);
    	SendAppleMessageSocialDto sendAppleMessageSocialDto=null;
    	List<AppleAttachments> appleAttachments=((SendMessageDTO)messageDTO).getAppleMediaUrls();
		RichLinkData richLinkData = ((SendMessageDTO) messageDTO).getRichLinkData();
		if (Objects.nonNull(richLinkData)) {
			sendAppleMessageSocialDto = SendAppleMessageSocialDto.builder().body(body).sourceId(pageId).locale("en_US")
					.destinationId(messengerContact.getAppleConversationId()).v(1).type("richLink").id(messageId)
					.richLinkData(richLinkData).build();
		} else if (CollectionUtils.isNotEmpty(appleAttachments)) {
			List<AppleMediaFiles> appleMediaFiles = appleAttachments.stream()
					.map((appleMedia) -> new AppleMediaFiles(appleMedia.getName(),
							String.valueOf(appleMedia.getContentSize()), appleMedia.getContentType(),
							appleMedia.getFileCheckSum(), appleMedia.getKey(), appleMedia.getMmcsUrl(),
							appleMedia.getOwner()))
					.collect(Collectors.toList());
			int size = appleAttachments.size();
    		if(StringUtils.isBlank(body)) {
				body = appendUnicodeString(size);
    		}
    		else {
				body += appendUnicodeString(size);
			}
			sendAppleMessageSocialDto = SendAppleMessageSocialDto.builder().body(body).sourceId(pageId).locale("en_US")
					.destinationId(messengerContact.getAppleConversationId()).v(1).type("text").id(messageId)
					.attachments(appleMediaFiles).build();
    		
    	}
		else {
			sendAppleMessageSocialDto = SendAppleMessageSocialDto.builder().body(body).sourceId(pageId).locale("en_US")
					.destinationId(messengerContact.getAppleConversationId()).v(1).type("text").id(messageId).build();

		}
		if(StringUtils.isNotBlank(sendMessageDTO.getPlainTextRobinResponse())) {
			sendAppleMessageSocialDto.setBody(sendMessageDTO.getPlainTextRobinResponse());
		}
		handleInteractiveTypeMessage(sendAppleMessageSocialDto,sendMessageDTO);
		if (Objects.nonNull(sendAppleMessageSocialDto)) {
			appleSocialIntegrationService.sendMessageToApple(sendAppleMessageSocialDto);
		}
    }


    private void  handleInteractiveTypeMessage(SendAppleMessageSocialDto sendAppleMessageSocialDto,
			SendMessageDTO sendMessageDTO) throws Exception {
    	if(sendMessageDTO.getAppleInteractiveMessageType()!=null) {
    		AppleInteractiveEventHandler handler=getHandler(sendMessageDTO.getAppleInteractiveMessageType());
        	AppleInteractiveMessageData interactiveData= handler.handle(sendMessageDTO);
        	if(interactiveData!=null) {
            	sendAppleMessageSocialDto.setInteractiveData(interactiveData);
            	sendAppleMessageSocialDto.setBody(null);
            	sendAppleMessageSocialDto.setLocale(null);
            	sendAppleMessageSocialDto.setType("interactive");
        	}	
    	}
    	
	}
    private AppleInteractiveEventHandler getHandler(AppleInteractiveMessageType event) {
		return appleInteractiveEventHandlers.stream().
				filter(messengerEventHandler -> messengerEventHandler.getEvent().equals(event))
				.findFirst().orElseThrow(() -> new MessengerException("Could not find any matching event handler"));
	}
	// UPDATE MESSENGER CONTACT
    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        UserDTO userDTO = getUserDTO(messageDTO);
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("SEND");
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
        lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.APPLE.name());
        lastMessageMetadataPOJO.setLastMessageSource(Source.APPLE.getSourceId());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        if(BooleanUtils.isNotFalse(dto.getUpdateLastResponseAt())) {
        	messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        }
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        messengerContact.setLastMessage(sendMessageDTO.getBody());

        if (StringUtils.isEmpty(sendMessageDTO.getConversationDTO().getBody())
                && StringUtils.isNotEmpty(sendMessageDTO.getConversationDTO().getMediaUrl())) {
            messengerContact.setLastMessage("Sent an attachment");
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), messengerContact.getAppleConversationId(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }


    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messengerContact)) {
            SendMessageDTO smsDTO = (SendMessageDTO) messageDTO;
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
                    businessDTO.getBusinessId(), smsDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        return messengerContact;
    }

    /**
     * No Email Notification on Send Events
     */
    @Override
    MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        return null;
    }

    @Override
    Integer getMessageId(MessageDTO messageDTO) {
        Integer messageId = messageDTO.getMessageId();
        if (Objects.isNull(messageId)) {
            SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    void publishEvent(MessageDTO messageDTO) {
        publishEventIfRepliedOnUnassignedConversation(messageDTO);
    }

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = businessService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }

    private ConversationDTO saveAppleMessage(MessageDTO messageDTO, String uniqueMessageId) {

    	List<MessengerMediaFileDTO> messengerMediaFileList=new ArrayList<MessengerMediaFileDTO>();
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();

        String senderId = businessDTO.getBusinessId().toString();
        String receiverId = messengerContact.getAppleConversationId();

        UserDTO userDTO = messageDTO.getUserDTO();

        String message = null;

        SendMessageDTO sendMessageDTO=(SendMessageDTO) messageDTO;
        message = sendMessageDTO.getBody();
        messengerMediaFileList=sendMessageDTO.getMessengerMediaFileList();
        AppleMessage appleMessage = new AppleMessage();
        appleMessage.setCreateDate(new Date());
        appleMessage.setCustomerId(messengerContact.getCustomerId());
        appleMessage.setRecipientId(receiverId);
        appleMessage.setSenderId(senderId);
        appleMessage.setMessageBody(message);
        appleMessage.setMessageId(uniqueMessageId);
        appleMessage.setStatus(AppleMessageStatusEnum.SENT.toString());
        appleMessage.setSentOn(new Date());
        appleMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());

        if (CollectionUtils.isNotEmpty(messengerMediaFileList) && StringUtils.isNotBlank(messengerMediaFileList.get(0).getUrl())) {
            appleMessage.setMediaURL(messengerMediaFileList.get(0).getUrl());
        }

        // Create Data for Messenger Message
        MessengerMessage messengerMessage = new MessengerMessage();
        messengerMessage.setAccountId(businessDTO.getAccountId());
        messengerMessage.setChannel(MessageDocument.Channel.APPLE.name());
        messengerMessage.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND.name());
        messengerMessage.setCreatedDate(new Date());
        messengerMessage.setSentThrough(MessageDocument.SentThrough.WEB.name());

        if (userDTO != null) {
            messengerMessage.setCreatedBy(userDTO.getId());
        }
        messengerMessage.setMessageType(MessageDocument.MessageType.CHAT.name());
        messengerMessage.setMessengerContactId(messengerContact.getId());
		log.info("richLinkPresent : {}", messageDTO.getRichLinkData());
		if (Objects.nonNull(messageDTO.getRichLinkData())) {
			messengerMessage.setMessageType(MessageDocument.MessageType.RICH_LINK.name());
		}
		if (Objects.nonNull(sendMessageDTO.getAppleInteractiveMessageType())
				&& AppleInteractiveMessageType.LIST_PICKER.equals(sendMessageDTO.getAppleInteractiveMessageType())
				&& !MessengerConstants.DEFAULT_SUGGESTION_RESPONSE_BODY.equals(message)) {
			messengerMessage.setMessageType(MessageDocument.MessageType.ROBIN_SUGGESTION.name());
		}
		messengerMessageService.saveAppleMessage(messengerMessage, appleMessage);

		return new ConversationDTO(appleMessage, messageDTO.getRichLinkData());
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        SendMessageDTO sendMessageDTO=(SendMessageDTO)messageDTO;
        if (Objects.isNull(customerDTO)) {
        	if(messageDTO.getMessengerContact()==null) {
        		messageDTO.setMessengerContact(messengerContactService.getOrCreateContact(messageDTO.getBusinessDTO().getBusinessId(), Integer.parseInt(sendMessageDTO.getToCustomerId()), messageDTO.getBusinessDTO().getAccountId()));
        	}
            Integer customerId = messageDTO.getMessengerContact().getCustomerId();
            customerDTO = contactService.findByIdNoCaching(customerId);
            messageDTO.setCustomerDTO(customerDTO);
        }
        if(customerDTO.getBlocked()) {
            throw new BadRequestException(ErrorCode.CONTACT_IS_BLOCKED, ErrorCode.CONTACT_IS_BLOCKED.getErrorMessage());
        }
        return customerDTO;
    }

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if(messageDTO.isBOT()) return MessageTag.UNREAD;
        return MessageTag.INBOX;
    }

    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
        UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        SendMessageDTO dto = (SendMessageDTO) messageDTO;
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
        if(CollectionUtils.isNotEmpty(dto.getMessengerMediaFileList())) {
			messageDocumentDTO.setMediaFiles(dto.getMessengerMediaFileList().stream().map(attachement -> new MessageDocument.MediaFile(attachement.getFileExtension(), attachement.getUrl(), attachement.getContentSize(), attachement.getName(), attachement.getContentType())).collect(
					Collectors.toList()));
		}
        messageDocumentDTO.setFrom(dto.getConversationDTO().getSender());
        messageDocumentDTO.setTo(dto.getConversationDTO().getRecipient());
		if (Objects.nonNull(dto.getRichLinkData())) {
			messageDocumentDTO.setRichLinkData(dto.getRichLinkData());
		}
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }
   
    SortedSet<Message> handleMultiMediaAndTextMessages(MessageDTO messageDTO) throws Exception {
        SortedSet<Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        // save media files
        List<MessengerMediaFile> mediaUrls = sendMessageDTO.getMediaUrls();
        if(CollectionUtils.isNotEmpty(mediaUrls)) {
        List<MessengerMediaFileDTO> messengerMediaFiles=new ArrayList<MessengerMediaFileDTO>();
        int mediaCount = mediaUrls.size();
        for (int currMediaIdx = 0; currMediaIdx < mediaCount; currMediaIdx++) {
			MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(currMediaIdx);
			sendMessageDTO.setMediaurl(mediaFile.getUrl());
			MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(mediaFile);
			messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
			messengerMediaFileDTO.setData(mediaFile.getData());
			messengerMediaFiles.add(messengerMediaFileDTO);
		}
		messageDTO.setMessengerMediaFileList(messengerMediaFiles);
        }
		if ((StringUtils.isNotBlank(sendMessageDTO.getBody()) || CollectionUtils.isNotEmpty(mediaUrls))) {
			allMessages.add(sendMessageToApple(sendMessageDTO, MessengerEvent.APPLE_SEND));
		}
		List<String> multiMessages = sendMessageDTO.getMultiMessages();
		if (CollectionUtils.isNotEmpty(multiMessages)) {
            for (int i = 0; i < multiMessages.size(); i++) {
                String multiMessage = multiMessages.get(i);
			    if (StringUtils.isNotBlank(multiMessage)) {
					SendMessageDTO sendMultiMessageSendMessageDTO = sendMessageDTO;
					multiMessage = multiMessage.trim();
					sendMultiMessageSendMessageDTO.setBody(multiMessage);
				sendMultiMessageSendMessageDTO.setRichLinkData(null);
				if (MessengerUtil.isValidURL(multiMessage)) {
					sendMultiMessageSendMessageDTO.setRichLinkData(commonService.generateRichLinkObjectFromData(
							multiMessage, sendMultiMessageSendMessageDTO.getFromBusinessId(),
							getBusinessDTO(messageDTO).getBusinessName()));
				}
				sendMultiMessageSendMessageDTO.setMessengerMediaFileList(null);
				sendMultiMessageSendMessageDTO.setAppleMediaUrls(null);
                if (i != 0) {
                    sendMultiMessageSendMessageDTO.setPartOfExistingMessage(true);
                }
				allMessages.add(sendMessageToApple(sendMultiMessageSendMessageDTO, MessengerEvent.APPLE_SEND));
			}
		}
		}
		return allMessages;

    }

	void sendTypingEvent(ConversationStateDTO event, String typingEvent) {
		log.info("Going to update send typing event for mc_id  {}   ", event.getMc_id());
		String lockKey = "ACQUIRE_LOCK:" + event.getMc_id();
		int retryCount = 0;
		while (!redisHandler.accureLockOnRedisKey(lockKey, "lock") && retryCount < 10) {
			try {
				retryCount++;
				Thread.sleep(100);
			} catch (InterruptedException e) {
				log.error("Interuppted while waiting to acquire lock on mcid: {} in redis, Reason: {}", event.getMc_id(), e);
			}
		}
		MessengerContact messengerContact = messengerContactService.findById(event.getMc_id());
		String messageId = generateUniqueMessageId();
		boolean isSMB = event.getEnterpriseId() == null;
		String pageId = appleSocialIntegrationService.getApplePageIdByBusinessId(event.getBusinessId(),
				event.getEnterpriseNumber(), isSMB);
		liveChatService.updateFirebaseMessageForAppleTypingEvent(event, messengerContact.getBusinessId(),
				messengerContact.getId(), false);
		SendAppleMessageSocialDto sendAppleMessageSocialDto = SendAppleMessageSocialDto.builder().sourceId(pageId)
				.destinationId(messengerContact.getAppleConversationId()).v(1).type(typingEvent).id(messageId)
				.build();
		appleSocialIntegrationService.sendMessageToApple(sendAppleMessageSocialDto);
		redisHandler.deleteKey(lockKey);
	}

	public AttachmentPreUploadResponse getPreUploadDataFromSocial(
			AttachmentPreUploadMessengerRequest attachmentPreUploadMessengerRequest, Integer businessId) {
		log.info("Get Preupload data from social : requests: {},businessId: {}", attachmentPreUploadMessengerRequest,
				businessId);
		AttachmentPreUploadResponse attachmentPreUploadResponse = null;
		BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);
		if (Objects.nonNull(businessDTO)) {
			boolean isSMB = businessDTO.getEnterpriseId() == null;
			String sourceId = appleSocialIntegrationService.getApplePageIdByBusinessId(businessDTO.getBusinessId(),
					businessDTO.getEnterpriseNumber(), isSMB);
			if ((StringUtils.isNotBlank(sourceId))) {
					AttachmentPreUploadSocialRequest attachmentPreUploadSocialRequest = new AttachmentPreUploadSocialRequest(
						sourceId, String.valueOf(attachmentPreUploadMessengerRequest.getSize()));
					log.info("apple preupload data from social called with request : {}",
							attachmentPreUploadSocialRequest);
				attachmentPreUploadResponse = appleSocialIntegrationService
							.getPreUploadDataFromSocial(attachmentPreUploadSocialRequest);
				log.info("Preupload data fetched : {}", attachmentPreUploadResponse);
			}
			;
			} else {
				log.error("Couldn't find sourceId with businessId : {}", businessId);
				throw new NotFoundException("Couldn't find sourceId with businessId" + businessId);
			}

			return attachmentPreUploadResponse;
	}

	private String appendUnicodeString(int size) {
		return StringUtils.repeat("\uFFFC", size);
	}

	public void consumeQuickReplySQSMessage(AppleQuickReplyDto appleQuickReplyDto) throws Exception {
		ContactDocument contactDocument=messengerContactService.getContact(appleQuickReplyDto.getAccountId(),appleQuickReplyDto.getMcId());
		Long lastMessageTime = Objects.nonNull(contactDocument.getL_msg_on())
				? new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS).parse(contactDocument.getL_msg_on())
						.getTime()
				: null;
		if (Objects.nonNull(lastMessageTime) && lastMessageTime.equals(appleQuickReplyDto.getLastMsgOn())) {
			Integer businessId=contactDocument.getB_id();
			Integer mcId=contactDocument.getM_c_id();
			SendMessageDTO sendMessageDTO = getQuickReplyQuestion(businessId, mcId);
			handle(sendMessageDTO);
			sendMessageDTO.setBody(null);
			sendMessageDTO.setAppleInteractiveMessageType(AppleInteractiveMessageType.QUICK_REPLY);
			MessengerContact messengerContact=messengerContactService.findById(mcId);
			prepareAndCallSocialAppleSend(sendMessageDTO, generateUniqueMessageId(), messengerContact);
		}
	}

	private SendMessageDTO getQuickReplyQuestion(Integer businessId, Integer mcId) {
		SendMessageDTO sendMessageDTO=new SendMessageDTO();
		sendMessageDTO.setBody("Was your question answered?");
		sendMessageDTO.setSentThrough(SentThrough.WEB);
		sendMessageDTO.setFromBusinessId(businessId);
		sendMessageDTO.setBusinessIdentifierId(Integer.toString(businessId));
		sendMessageDTO.setToCustomerId(mcId.toString());
		sendMessageDTO.setSource(Source.APPLE.getSourceId());
		sendMessageDTO.setUserId(MessengerConstants.ROBIN_REPLY_USER);
		return sendMessageDTO;
	}

}
