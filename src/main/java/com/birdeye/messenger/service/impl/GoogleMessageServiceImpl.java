package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.repository.GoogleMessageRepository;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.GoogleMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class GoogleMessageServiceImpl implements GoogleMessageService {

    private final GoogleMessageRepository googleMessageRepository;
    private final BusinessService businessService;

    @Override
    public Optional<GoogleMessage> findByRequestId(String requestId) {
        return googleMessageRepository.findByRequestId(requestId);
    }
    
    @Override
   	public boolean isGoogleMessagingEnabled(Integer businessId) {
   		BusinessOptionResponse businessOptions = businessService.getBusinessOptionsConfig(businessId, true);
   		if (businessOptions!=null && (businessOptions.getIsGoogleMessageEnabled()!=null && businessOptions.getIsGoogleMessageEnabled()==1))
   			return true;
   		
   		return false;
   	}

    @Override
    @Transactional
    public void deleteByCId(Integer cId) {
        try {
            googleMessageRepository.deleteGoogleMessagesByCId(cId);
        } catch (Exception e) {
            log.error("error : {} occurred in deleteByCId", e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateGoogleMessageBody(String messageBody,Integer messageId){
        try {
            googleMessageRepository.updateGoogleMessageBody(messageBody,messageId);
        } catch (Exception e) {
            log.error("error : {} occurred in updatingGoogleMessageBody", e.getMessage());
        }
    }
}


