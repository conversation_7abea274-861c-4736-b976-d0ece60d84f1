/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationSummaryMessageBody;
import com.birdeye.messenger.dao.repository.ConversationSummaryMessageBodyRepository;
import com.birdeye.messenger.dto.ConversationSummaryMessageBodyRequest;
import com.birdeye.messenger.dto.appointment.AppointmentInfo;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.CurrencyConstant;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;
import com.birdeye.messenger.service.ConversationSummaryMessageBodyService;
import com.birdeye.messenger.util.DateUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConversationSummaryMessageBodyServiceImpl implements ConversationSummaryMessageBodyService {

    @Autowired
    @Lazy
    private ConversationSummaryMessageBodyService conversationSummaryMessageBodyService;

    private final ConversationSummaryMessageBodyRepository conversationSummaryMessageBodyRepository;

    @Override
    public String formatMessageBodyForActivityType(ActivityType activityType, MessageDocument messageDocument,
            ConversationSummaryMessageType conversationSummaryMessageType) {
        log.info("formatMessageBodyForActivityType called with activityType : {}, conversationSummaryMessageType : {}",
                activityType, conversationSummaryMessageType);

        ConversationSummaryMessageBody conversationSummaryMessageBody = conversationSummaryMessageBodyService
                .getConversationSummaryMessageBodyByMessageType(activityType);

        if (Objects.nonNull(conversationSummaryMessageBody)) {
            log.info("conversationSummaryMessageBody found : {}", conversationSummaryMessageBody);
            AppointmentInfo appointmentInfo = messageDocument.getAppointmentInfo();
            PaymentInfo paymentInfo = messageDocument.getPaymentInfo();
            if (Objects.nonNull(paymentInfo) && Objects.isNull(paymentInfo.getReqCreatedAt())) {
                paymentInfo.setReqCreatedAt(messageDocument.getCr_time());
            }
            if (Objects.nonNull(appointmentInfo) && Objects.isNull(appointmentInfo.getStartTime())) {
                appointmentInfo.setStartTime(messageDocument.getCr_time());
            }
            String body = conversationSummaryMessageBody.getBody();
            return getFormattedConversationMessageBody(body,
                    ConversationSummaryMessageType.APPOINTMENT == conversationSummaryMessageType ? appointmentInfo
                            : paymentInfo);
        }
        log.info("conversationSummaryMessageBody not found");
        return null;
    }

    @Override
    @CachePut(cacheNames = Constants.CONVERSATION_SUMMARY_MESSAGE_BODY_CACHE, key = "#conversationSummaryMessageBodyRequest.messageType", unless = "#result == null")
    public ConversationSummaryMessageBody saveConversationSummaryMessageBody(
            ConversationSummaryMessageBodyRequest conversationSummaryMessageBodyRequest) {
        ConversationSummaryMessageBody conversationSummaryMessageBody = null;
        try {
            conversationSummaryMessageBody = conversationSummaryMessageBodyRequest.getConversationSummaryMessageBody();
            conversationSummaryMessageBodyRepository.save(conversationSummaryMessageBody);
            return conversationSummaryMessageBody;
        } catch (Exception e) {
            log.error("error : {} occurred while saveConversationSummaryMessageBody ", e.getMessage());
            ConversationSummaryMessageBody existingConversationSummaryMessageBody = conversationSummaryMessageBodyService
                    .getConversationSummaryMessageBodyByMessageType(
                            conversationSummaryMessageBodyRequest.getMessageType());
            if (Objects.nonNull(existingConversationSummaryMessageBody)) {
                log.info("existingConversationSummaryMessageBody found in DB : {}",
                        existingConversationSummaryMessageBody);
                existingConversationSummaryMessageBody.setBody(conversationSummaryMessageBodyRequest.getBody());
                conversationSummaryMessageBodyRepository.saveAndFlush(existingConversationSummaryMessageBody);
                return existingConversationSummaryMessageBody;
            }
        }
        return conversationSummaryMessageBody;
    }

    @Override
    @CacheEvict(cacheNames = Constants.CONVERSATION_SUMMARY_MESSAGE_BODY_CACHE, key = "#activityType")
    public void deleteConversationSummaryMessageBody(ActivityType activityType) {
        conversationSummaryMessageBodyRepository.deleteConversationSummaryMessageBodyByMessageType(activityType);
    }

    @Override
    @Cacheable(cacheNames = Constants.CONVERSATION_SUMMARY_MESSAGE_BODY_CACHE, key = "#activityType", unless = "#result == null")
    public ConversationSummaryMessageBody getConversationSummaryMessageBodyByMessageType(ActivityType activityType) {
        log.info("getConversationSummaryMessageBodyByMessageType called with activityType : {}", activityType);
        Optional<ConversationSummaryMessageBody> existingConversationSummaryMessageBody = conversationSummaryMessageBodyRepository
                .getConversationSummaryMessageBodyByMessageType(
                        activityType);
        if (existingConversationSummaryMessageBody.isPresent()) {
            log.info("getConversationSummaryMessageBodyByMessageType found : {}",
                    existingConversationSummaryMessageBody);
            return existingConversationSummaryMessageBody.get();
        }
        log.info("getConversationSummaryMessageBodyByMessageType not found for activityType : {}", activityType);
        return null;
    }

    private String getFormattedConversationMessageBody(String body, Object object) {
        log.info("getFormattedConversationMessageBody called with body : {}", body);
        String bodyWords[] = body.split(" ");
        final StringBuilder finalBody = new StringBuilder("");
        Class<?> objectClass = object.getClass();
        Field[] fields = objectClass.getDeclaredFields();
        Map<String, Object> fieldValuesMap = buildFieldValuesMap(fields, object);
        Arrays.stream(bodyWords).forEach(word -> {
            if (StringUtils.isNotBlank(word) && word.startsWith("[") && word.matches(".*[].]")) {
                try {
                    String field = word.substring(1, word.lastIndexOf(']'));
                    if (StringUtils.isNotBlank(field)) {
                        String evaluatedBody = checkIfFieldIsToBeEvaluated(field, fieldValuesMap);
                        String variableValue = fieldValuesMap.get(field).toString();
                        finalBody.append(StringUtils.isNotBlank(evaluatedBody) ? evaluatedBody
                                : StringUtils.isNotBlank(variableValue) ? variableValue + " " : "");
                    }
                } catch (Exception e) {
                    log.error("error : {} occurred in getFormattedConversationMessageBody for word : {}",
                            e.getMessage(), word);
                }
            } else {
                finalBody.append(word + " ");
            }
        });
        return finalBody.toString().trim();
    }

    private Map<String, Object> buildFieldValuesMap(Field[] fields, Object object) {
        Map<String, Object> fieldValuesMap = null;
        if (ArrayUtils.isNotEmpty(fields)) {
            fieldValuesMap = new HashMap<>(fields.length);
            for (Field field : fields) {
                try {
                    field.setAccessible(true);
                    fieldValuesMap.put(field.getName(), field.get(object));
                } catch (Exception e) {
                    log.error("error : {} occurred in buildFieldValuesMap for field : {} and objet : {}",
                            e.getMessage(), field,
                            object);
                }
            }
        }
        log.info("buildFieldValuesMap generated : {}", fieldValuesMap);
        return fieldValuesMap;
    }

    private String checkIfFieldIsToBeEvaluated(String field, Map<String, Object> fieldValuesMap) {
        final String amount = "amount";
        final String currency = "currency";
        final String amountRequested = "amountRequested";
        final String amountPending = "amountPending";

        Object currencyConstant = fieldValuesMap.get(currency);
        Object cents = fieldValuesMap.get(amount);
        Object paymentAmountRequested = fieldValuesMap.get(amountRequested);
        Object paymentAmountPending = fieldValuesMap.get(amountPending);

        switch (field) {
            case amount:
                if (Objects.nonNull(amount)) {
                    String amountInUSDOrAUD = convertCentsToUSDOrAUD((Double) cents,
                            (CurrencyConstant) currencyConstant);
                    return amountInUSDOrAUD + " ";
                }
                return null;
            case currency:
                if (Objects.nonNull(currencyConstant)) {
                    return ((CurrencyConstant) currencyConstant).getCurrencySymbol();
                }
                return CurrencyConstant.USD.getCurrencySymbol();
            case amountRequested:
                if (Objects.nonNull(paymentAmountRequested)) {
                    String amountInUSDOrAUD = convertCentsToUSDOrAUD((Double) paymentAmountRequested,
                            (CurrencyConstant) currencyConstant);
                    return amountInUSDOrAUD + " ";
                }
                return null;
            case amountPending:
                if (Objects.nonNull(paymentAmountPending)) {
                    String amountInUSDOrAUD = convertCentsToUSDOrAUD((Double) paymentAmountPending,
                            (CurrencyConstant) currencyConstant);
                    return amountInUSDOrAUD + " ";
                }
                return null;
            default:
                return null;

        }
    }

    private String convertCentsToUSDOrAUD(Double cents, CurrencyConstant currency) {
        double USD = (double) cents / (double) 100.0;
        BigDecimal bd = new BigDecimal(USD).setScale(2, RoundingMode.HALF_UP);
        switch (currency) {
            case AUD:
                bd = new BigDecimal(USD * 1.37).setScale(2, RoundingMode.HALF_UP);
                return bd.toString();
            case USD:
            default:
                return bd.toString();
        }
    }
}
