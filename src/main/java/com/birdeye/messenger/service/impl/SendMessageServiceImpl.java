package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.util.EncryptionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerAudit;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.repository.MessengerAuditRepository;
import com.birdeye.messenger.dao.repository.MessengerMediaFileRepository;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.external.service.NexusEmailService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.SendMessageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;


@Slf4j
@RequiredArgsConstructor
@Service
public class SendMessageServiceImpl implements SendMessageService{

	private final MessengerAuditRepository messengerAuditRepository;
	private final CommonService commonService;
	private final KafkaService kafkaService;
	private final MessengerMediaFileRepository messengerMediaFileRepository;

	@Autowired
	private NexusEmailService nexusEmailService;

	@Autowired
	private NexusServiceImpl nexusService;

	@Override
    public void pushSendRequestToKafka(SendMessageDTO sendMessageDTO,MessengerEvent event,UserDTO userDTO,boolean sendSms) {
		ConversationDTO conversationDTO=sendMessageDTO.getConversationDTO();
        if (userDTO != null) {
            MessengerAudit audit = new MessengerAudit(conversationDTO.getId(), sendMessageDTO.getUserDTO().getId(), event.toString());
            messengerAuditRepository.save(audit);
        }
        if(sendSms) {
			ConversationWrapperMessage conversationKafkaWrapper = new ConversationWrapperMessage(sendMessageDTO, conversationDTO);
			SendConversationRequest request = commonService.getSendSMSRequestForMessenger(conversationKafkaWrapper, sendMessageDTO.getRequestId());
			kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.COMM_SMS_SEND,conversationDTO.getBusinessId(), request);
			append10DlcBrandingToMessageBody(sendMessageDTO);
			// TODO: Move it to Nexus service.
        }

        // need to write fallback to send via nexus
    }


	@Override
	public void saveMediaFile(MessengerMediaFileDTO mediaFileDTO, Integer id) {
		if(mediaFileDTO!=null) {
			MessengerMediaFile mediaFile = new MessengerMediaFile(mediaFileDTO);
			mediaFile.setMessageId(id);
			messengerMediaFileRepository.save(mediaFile);
		}
	}

	@Override
	public void sendEmail(EmailDTO emailDTO) {
		nexusEmailService.sendMail(Integer.valueOf(emailDTO.getBusinessId()), emailDTO, emailDTO.getRequestType(), emailDTO.getExternalUid(), emailDTO.getBusinessNumber(), emailDTO.getDataObject());

	}

	@Override
	public void audit(SendMessageDTO sendMessageDTO, MessengerEvent event, UserDTO userDTO) {
		ConversationDTO conversationDTO=sendMessageDTO.getConversationDTO();
		if (userDTO != null) {
			MessengerAudit audit = new MessengerAudit(conversationDTO.getId(), sendMessageDTO.getUserDTO().getId(), event.toString());
			messengerAuditRepository.save(audit);
		}
	}

	private void append10DlcBrandingToMessageBody(SendMessageDTO sendMessageDTO){
		log.info("Append Ten Dlc Branding for sms {} ",sendMessageDTO);
		String messageBody = StringUtils.isNotBlank(sendMessageDTO.getBody()) ? sendMessageDTO.getBody() : "";
		TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(sendMessageDTO.getBusinessDTO().getEnterpriseNumber());
		if(Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()){
			String status = tenDlcStatusDTO.getStatus();
			TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
			if (Objects.nonNull(status) && (status.equals("not_started")||status.equals("in_progress")||status.equals("failed")||status.equals("not_required_demo_account"))
					&& Objects.nonNull(tollFreeInfo) && Objects.nonNull(tollFreeInfo.getBranding()) && tollFreeInfo.isAvailable()) {
				if (StringUtils.isBlank(messageBody)) {
					messageBody = messageBody.concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				} else{
					messageBody = messageBody.concat(sendMessageDTO.getIsStopUnsubscribeTextPresent() ? "\n" : "\n\n")
							.concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				}
				sendMessageDTO.setBody(messageBody);
				sendMessageDTO.getConversationDTO().setBody(messageBody);
				encrypt(sendMessageDTO.getConversationDTO());
			}
		}
		
	}

	public void encrypt(ConversationDTO conversationDTO) {
		try {
			if (StringUtils.isNotEmpty(conversationDTO.getBody())) {
				conversationDTO.setBody(EncryptionUtil.encrypt(conversationDTO.getBody(),
						StringUtils.join(conversationDTO.getSender(), conversationDTO.getRecipient()),
						StringUtils.join(conversationDTO.getRecipient(), conversationDTO.getSender())));
				conversationDTO.setEncrypted(1);
			}
		} catch (Exception e) {
			log.error("Encryption failed for Conversation : {} with business {}", conversationDTO.getId(), conversationDTO.getBusinessId());
			conversationDTO.setEncrypted(0);
		}
	}

	@Override
	public void pushSendRequestToKafkaSendSmsRetry(SendMessageDTO sendMessageDTO,MessengerEvent event,UserDTO userDTO,boolean sendSms) {
		ConversationDTO conversationDTO=sendMessageDTO.getConversationDTO();
		if (userDTO != null) {
			MessengerAudit audit = new MessengerAudit(conversationDTO.getId(), sendMessageDTO.getUserDTO().getId(), event.toString());
			messengerAuditRepository.save(audit);
		}
		if(sendSms) {
			ConversationWrapperMessage conversationKafkaWrapper = new ConversationWrapperMessage(sendMessageDTO, conversationDTO);
			SendConversationRequest request = commonService.getSendSMSRequestForMessenger(conversationKafkaWrapper, sendMessageDTO.getRequestId());
			TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(sendMessageDTO.getBusinessDTO().getEnterpriseNumber());
			if(Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()){
				String status = tenDlcStatusDTO.getStatus();
				TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
				if (status != null && (status.equals("not_started") || status.equals("in_progress") || status.equals("failed"))
						&& tollFreeInfo != null && tollFreeInfo.isAvailable()) {
					request.setBody(request.getBody().replace(("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding()),""));
				}
			}
			kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.COMM_SMS_SEND,conversationDTO.getBusinessId(), request);
		}

	}
	

}
