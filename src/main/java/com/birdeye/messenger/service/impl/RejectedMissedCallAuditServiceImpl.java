package com.birdeye.messenger.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.BusinessRejectedMissedCalls;
import com.birdeye.messenger.dao.repository.BusinessRejectedMissedCallsRepository;
import com.birdeye.messenger.dto.VoiceCallDto;
import com.birdeye.messenger.service.RejectedMissedCallAuditService;
import com.birdeye.messenger.util.DtoToEntityConverter;

/**
 * <AUTHOR>
 *
 */
@Service
public class RejectedMissedCallAuditServiceImpl implements RejectedMissedCallAuditService {

	@Autowired
	private BusinessRejectedMissedCallsRepository missedCallRepository;

	public void saveRejectedMissedCalls(VoiceCallDto dto) {
		BusinessRejectedMissedCalls entity = DtoToEntityConverter.missedCall(dto);
		missedCallRepository.saveAndFlush(entity);
	}
}
