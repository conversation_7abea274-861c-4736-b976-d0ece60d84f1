package com.birdeye.messenger.service.impl;

import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.AddContactMessengerMessage;
import com.birdeye.messenger.dto.AddNoteRequest;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CommunicationPreferencesDto;
import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.dto.ConversationStatusRequest;
import com.birdeye.messenger.dto.CustomChannelReceiveRequest;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.LocationMessage;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.PublicAddAndSendContact;
import com.birdeye.messenger.dto.PublicUpdateStatusConversationRequest;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.exception.BusinessException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.NotAuthorizedException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.AssignmentService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.CustomChannelService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerDashboardService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.PublicAPIService;
import com.birdeye.messenger.sro.TeamDTO;
import com.birdeye.messenger.util.PhoneNoValidator;
import com.birdeye.messenger.validator.EmailValidatorService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
@Service
@RequiredArgsConstructor
@Slf4j
public class PublicAPIServiceImpl  implements PublicAPIService{
 private final BusinessService businessService;
 private final MessengerContactService messengerContactService;
 private final CommonService commonService;
 private final ContactService contactService;
 private final MessengerDashboardService messengerDashboardService;
 private final MessengerEventHandlerService messengerEventHandlerService;
 private final AssignmentService assignmentService;
 private final UserService userService;
 private final ConversationService conversationService;
 private final CustomChannelService customChannelService;

	@Override
	public MessageResponse addAndSendMessage(Integer accountId, PublicAddAndSendContact request) throws Exception {
		log.debug("Public [addAndSendMessage] Request: {}",request);
		BusinessDTO business=businessService.getBusinessByBusinessNumber(Long.valueOf(request.getFromBusinessNumber()));
		if (business == null) {
			throw new InputValidationException(ErrorCode.NO_BUSINESS_FOUND);
		}
		validatePublicSendInput(request, business);
		CustomerDTO customerDto = new CustomerDTO();
		customerDto.setName(request.getName());
		customerDto.setEmailId(request.getEmailId());
		customerDto.setPhone(request.getPhone());
		LocationMessage lm = new LocationMessage();
		lm.setCountryCode(request.getCountryCode());
		customerDto.setLocation(lm);
		SendMessageDTO  sm = new SendMessageDTO(request.getBody(), business.getBusinessId());
		if(CollectionUtils.isNotEmpty(request.getMediaUrls())){
			sm.setMediaUrls(request.getMediaUrls());
		}
		AddContactMessengerMessage inboxInput = new AddContactMessengerMessage(customerDto, sm);
		if (request.getUserId()==null) {
			inboxInput.setUserId(Constants.INBOX_USER_ID);
		} else {
			inboxInput.setUserId(request.getUserId());
		}
		if (request.getUserEmailId()!=null) {
			inboxInput.setUserEmailId(request.getUserEmailId());
		}
		if (request.getChannel()!=null && request.getChannel().equalsIgnoreCase("sms")){
			inboxInput.setSource(1);
		} else if (request.getChannel()!=null && request.getChannel().equalsIgnoreCase("email")) {
			inboxInput.setSource(111);
		}
		inboxInput.setLeadSource(request.getLeadSource());
		inboxInput.setOpenConversation(request.isOpenConversation());
		inboxInput.setCustomerId(request.getCustomerId());
		if (request.getCustomerId() != null) {
			CustomerDTO contact = contactService.findById(request.getCustomerId());
			inboxInput.setFetchedContact(contact);
			isMessageAllowed(inboxInput, contact);
		}
		MessageResponse response = messengerDashboardService.addNewContactAndSendSms(accountId, inboxInput);
		if(StringUtils.isNotBlank(request.getSetStatus()) && "open".equals(request.getSetStatus())){
			ConversationStatusRequest updateStatusRequest = new ConversationStatusRequest();
			updateStatusRequest.setOpen(true);
			conversationService.updateStatus(accountId, inboxInput.getUserId(), response.getMcId(), updateStatusRequest,true);
		}else if(StringUtils.isNotBlank(request.getSetStatus()) && "close".equals(request.getSetStatus())){
			ConversationStatusRequest updateStatusRequest = new ConversationStatusRequest();
			updateStatusRequest.setOpen(false);
			conversationService.updateStatus(accountId, inboxInput.getUserId(), response.getMcId(), updateStatusRequest,true);
		}
		return  response;
	}

	private void isMessageAllowed(AddContactMessengerMessage inboxInput, CustomerDTO contact) {
		if (contact != null) {
			CommunicationPreferencesDto commPreferences = contact.getCommPreferences();
			if (inboxInput.getSource() != null && commPreferences != null) {
				if (inboxInput.getSource() == 1) {
					if (commPreferences.getSmsPreferences() != null
							&& BooleanUtils.isFalse(commPreferences.getSmsPreferences().getServiceOptin())) {
						throw new InputValidationException(new ErrorMessageBuilder(
								ErrorCode.CUSTOMER_UNSUBSCRIBED_SMS_CHANNEL, ComponentCodeEnum.SMS)
								.message("Customer - {} has opted out of sms communications", contact.getId()));
					}
				} else if (inboxInput.getSource() == 111) {
					// We do not support template emails via public api. Hence checking for
					// free text emails which are categorized into marketing channel
					if (commPreferences.getEmailPreferences() != null
							&& BooleanUtils.isFalse(commPreferences.getEmailPreferences().getMarketingOptin())) {
						throw new InputValidationException(new ErrorMessageBuilder(
								ErrorCode.CUSTOMER_UNSUBSCRIBED_EMAIL_CHANNEL, ComponentCodeEnum.EMAIL)
								.message("Customer - {} has opted out of the Marketing channel", contact.getId()));
					}
				}
			}
		}
	}

	private void validatePublicSendInput(PublicAddAndSendContact request, BusinessDTO business) throws InputValidationException {

		if (StringUtils.isBlank(request.getEmailId()) && StringUtils.isBlank(request.getPhone()) && Objects.isNull(request.getCustomerId())) {
			throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_OR_PHONE_REQUIRED);
		}

		if (StringUtils.isBlank(request.getChannel())) {
			throw new InputValidationException(ErrorCode.CHANNEL_NOT_FOUND);
		}
		if (StringUtils.isBlank(request.getBody()) && CollectionUtils.isEmpty(request.getMediaUrls())) {
			throw new InputValidationException(ErrorCode.INVALID_SMS_BODY);
		}
		
		if (StringUtils.isNotEmpty(request.getBody()) && request.getChannel()!=null && request.getChannel().equalsIgnoreCase("sms")) {
			validateMessageBody(request);

		}

		if (StringUtils.isNotBlank(request.getEmailId())) {
			if (request.getEmailId().length() > Constants.EMAIL_ID_MAX_LENGTH) {
				throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_ID_TOO_LONG);
			}

			if (request.getEmailId().length() < Constants.EMAIL_ID_MIN_LENGTH) {
				throw new InputValidationException(ErrorCode.EMAIL_ID_TOO_SHORT);
			}

			int index = request.getEmailId().trim().indexOf("@");
			String mailId = null;
			// Keeping the value to 0 as @ at first index would also mean no valid value
			if (index > 0) {
				mailId = request.getEmailId().trim().substring(0, index - 1);
			}

			if (mailId == null) {
				throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
			}

			if (request.getEmailId() != null && !request.getEmailId().isEmpty()) {
				EmailValidatorService emailValidator = new EmailValidatorService();
				boolean isValidEmail = emailValidator.validate(request.getEmailId());
				if (!isValidEmail) {
					throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
				}
			}
		}

		// validate phone number (based on country code)
		if (StringUtils.isNotBlank(request.getPhone())) {
			String countryCode = (business !=null && business.getCountryCode()!=null)
					? business.getCountryCode() : "US";
			request.setCountryCode(countryCode);

			if (!PhoneNoValidator.isValidPhoneNumber(request.getPhone().trim(), countryCode)) {
				throw new InputValidationException(ErrorCode.INVALID_PHONE_NO);
			}
		}

		if (StringUtils.isNotBlank(request.getName()) &&
				request.getName().trim().length() > 100) {
			throw new InputValidationException(ErrorCode.NAME_TOO_LARGE);
		}
		if(CollectionUtils.isNotEmpty(request.getMediaUrls())){
			request.getMediaUrls().stream()
					.filter(mf -> Objects.isNull(mf) || StringUtils.isEmpty(mf.getUrl()))
					.findFirst() // Find the first invalid MessengerMediaFile
					.ifPresent(mf -> {
						throw new InputValidationException(ErrorCode.INVALID_MEDIA_URL);
					});
		}
	}

	private void validateMessageBody(PublicAddAndSendContact request) {
		int maxLength = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getIntegerProperty(Constants.PROPERTY_MESSAGE_BODY_MAX_LENGTH, Constants.DEFAULT_MESSAGE_BODY_MAX_LENGTH);

		if (request.getBody().length() > maxLength) {
			throw new InputValidationException(ErrorCode.PUBLIC_SEND_REQUEST_BODY_LENGTH_EXCEEDED);
		}
	}

	@Override
	public MessageResponse addNote(AddNoteRequest request) throws Exception {
		log.info("Public Add Note Request: {}",request.toString());
		BusinessDTO business=businessService.getBusinessByBusinessNumber(Long.valueOf(request.getFromBusinessNumber()));
		if (business == null) {
			throw new InputValidationException(ErrorCode.NO_BUSINESS_FOUND);
		}
		validateRequest(request, business);
		Integer userId=Constants.INBOX_USER_ID;
		CustomerDTO customerDTO =null;
		if (request.getUserEmailId() != null) {
			UserDTO user = commonService.getUserIdByEmail(request.getUserEmailId(), business.getBusinessId());
			userId=user.getId();
		}
		customerDTO = getCustomer(request, business, userId);
		MessengerContact messengerContact=messengerContactService.getOrCreateContactForExistingCustomer(business.getBusinessId(), customerDTO.getId(), business.getRoutingId());
		SendMessageDTO sendMessageDTO = createAddNoteRequest(request, business, userId, messengerContact);
		return messengerEventHandlerService.handleEvent(sendMessageDTO);
	}

	private SendMessageDTO createAddNoteRequest(AddNoteRequest request, BusinessDTO business, Integer userId,
												MessengerContact messengerContact) {
		SendMessageDTO  sendMessageDTO=new SendMessageDTO();
		sendMessageDTO.setBusinessIdentifierId(business.getBusinessId().toString());
		sendMessageDTO.setFromBusinessId(business.getBusinessId());
		sendMessageDTO.setToCustomerId(messengerContact.getId().toString());
		sendMessageDTO.setSentThrough(SentThrough.WEB);
		sendMessageDTO.setEvent(MessengerEvent.INTERNAL_NOTES);
		sendMessageDTO.setUserId(userId);
		sendMessageDTO.setBody(request.getBody());
		sendMessageDTO.setOpenConversation(request.isOpenConversation());
		return sendMessageDTO;
	}

	private CustomerDTO getCustomer(AddNoteRequest request, BusinessDTO business, Integer userId) {
		CustomerDTO customerDTO;
		if(request.getCustomerId()==null) {
			String countryCode = request.getCountryCode();
			KontactoRequest kontactoRequest = createKontactoRequest(request, countryCode,business.getBusinessId());
			log.debug("Requesting contact service to get/create customer for phone {} and country code {}",
					request.getPhone(), countryCode);
			customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, business.getRoutingId(),
					userId);
			log.debug("addNewContactAndSendSms: response returned from kontacto {}", customerDTO);
		}else {
			customerDTO = contactService.findById(request.getCustomerId());
		}
		return customerDTO;
	}

	private void validateRequest(AddNoteRequest request, BusinessDTO business) {


		if (StringUtils.isBlank(request.getEmailId()) && StringUtils.isBlank(request.getPhone()) && Objects.isNull(request.getCustomerId())) {
			throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_OR_PHONE_REQUIRED);
		}

		if (StringUtils.isBlank(request.getBody())) {
			throw new InputValidationException(ErrorCode.INVALID_NOTE_BODY);
		}

		if (StringUtils.isNotBlank(request.getEmailId())) {
			if (request.getEmailId().length() > Constants.EMAIL_ID_MAX_LENGTH) {
				throw new InputValidationException(ErrorCode.CUSTOMER_EMAIL_ID_TOO_LONG);
			}

			if (request.getEmailId().length() < Constants.EMAIL_ID_MIN_LENGTH) {
				throw new InputValidationException(ErrorCode.EMAIL_ID_TOO_SHORT);
			}

			int index = request.getEmailId().trim().indexOf("@");
			String mailId = null;
			// Keeping the value to 0 as @ at first index would also mean no valid value
			if (index > 0) {
				mailId = request.getEmailId().trim().substring(0, index - 1);
			}

			if (mailId == null) {
				throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
			}

			if (request.getEmailId() != null && !request.getEmailId().isEmpty()) {
				EmailValidatorService emailValidator = new EmailValidatorService();
				boolean isValidEmail = emailValidator.validate(request.getEmailId());
				if (!isValidEmail) {
					throw new InputValidationException(ErrorCode.INVALID_CUSTOMER_EMAIL);
				}
			}
		}

		// validate phone number (based on country code)
		if (StringUtils.isNotBlank(request.getPhone())) {
			String countryCode = (business.getLocation()!=null && business.getLocation().getCountryCode()!=null)
					? business.getLocation().getCountryCode() : "US";
			request.setCountryCode(countryCode);

			if (!PhoneNoValidator.isValidPhoneNumber(request.getPhone().trim(), countryCode)) {
				throw new InputValidationException(ErrorCode.INVALID_PHONE_NO);
			}
		}

		if (StringUtils.isNotBlank(request.getName()) &&
				request.getName().trim().length() > 100) {
			throw new InputValidationException(ErrorCode.NAME_TOO_LARGE);
		}
	}

	private KontactoRequest createKontactoRequest(AddNoteRequest request, String countryCode,Integer businessId) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setName(request.getName());
		kontactoRequest.setEmailId(request.getEmailId());
		kontactoRequest.setPhone(request.getPhone());
		kontactoRequest.setSource(KontactoRequest.DASHBOARD);
		kontactoRequest.setBusinessId(businessId);
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);

		return kontactoRequest;
	}

	@Override
	public ConversationAssignmentResponseDTO assignConversation(ConversationAssignmentRequestDTO request,Integer accountId){
		validateAssignmentRequest(request,accountId);
		if (request.getUserId()==null) {
			request.setUserId(Constants.INBOX_USER_ID);
		}
		UserDTO toUser = null;
		if(Objects.nonNull(request) && Objects.nonNull(request.getTo()) && "U".equals(request.getTo().getType())){
			if(StringUtils.isNotBlank(request.getTo().getEmailId()) && Objects.isNull(request.getTo().getId())){
				toUser=commonService.getUserIdByEmail(request.getTo().getEmailId(),accountId);
				IdentityDTO to = new IdentityDTO(toUser.getId(),toUser.getName(),"U",toUser.getEmailId());
				request.setTo(to);
			}else if(Objects.nonNull(request.getTo().getId()) && StringUtils.isBlank(request.getTo().getEmailId())){
				toUser = userService.getUserDTO(request.getTo().getId());
				if (Objects.isNull(toUser)) {
					throw new BusinessException(ErrorCode.USER_NOT_FOUND);
				}
				UserDTO user = userService.getUserAndBusinessAssociation(toUser.getId(),accountId);
				if(Objects.isNull(user)){
					log.info("public assignment: User {} doesn't have access to the account {}",toUser.getId(),accountId);
					throw new NotAuthorizedException(ErrorCode.USR_HAS_NO_LOC_ACCESS,ErrorCode.USR_HAS_NO_LOC_ACCESS.getErrorMessage());
				}
				IdentityDTO to = new IdentityDTO(toUser.getId(),toUser.getName(),"U",toUser.getEmailId());
				request.setTo(to);
			}
		}else if(Objects.nonNull(request) && Objects.nonNull(request.getTo()) && "T".equals(request.getTo().getType())){
			if(StringUtils.isNotBlank(request.getTo().getName()) && Objects.isNull(request.getTo().getId())){
				TeamDTO teamDto = businessService.getTeamByTeamName(accountId,request.getTo().getName());
				IdentityDTO to = new IdentityDTO(teamDto.getTeamId(),teamDto.getTeamName(),"T",null);
				request.setTo(to);
			}
		}
		return assignmentService.assignConversation(request, request.getUserId(), accountId);
	}

	private void validateAssignmentRequest(ConversationAssignmentRequestDTO request,Integer accountId){
		if(Objects.isNull(accountId)){
			throw new InputValidationException(ErrorCode.NO_BUSINESS_FOUND);
		}
		if(Objects.isNull(request)){
			throw new InputValidationException(ErrorCode.INVALID_PUBLIC_ASSIGNMENT_REQUEST);
		}
		if(Objects.nonNull(request)&& Objects.isNull(request.getTo())){
			throw new InputValidationException(ErrorCode.INVALID_TO_IN_ASSIGNMENT_REQUEST);
		}
		if(Objects.nonNull(request) && Objects.nonNull(request.getTo())){
			if(StringUtils.isBlank(request.getTo().getType()) || (!"T".equals(request.getTo().getType()) && !"U".equals(request.getTo().getType()))){
				throw new InputValidationException(ErrorCode.INVALID_ASSIGNMENT_TYPE);
			}
			if(Objects.isNull(request.getTo().getId()) && StringUtils.isBlank(request.getTo().getEmailId()) && "U".equals(request.getTo().getType())){
				throw new InputValidationException(ErrorCode.INVALID_TO_IN_ASSIGNMENT_REQUEST);
			}
			if(Objects.isNull(request.getTo().getId()) && StringUtils.isBlank(request.getTo().getName()) && "T".equals(request.getTo().getType())){
				throw new InputValidationException(ErrorCode.INVALID_TO_IN_ASSIGNMENT_REQUEST);
			}
		}
	}

	@Override
	public void updateConversationStatus(Integer accountId, PublicUpdateStatusConversationRequest request) {
		log.info("public updateConversationStatus for : accountId {}, request {}", accountId, request);
		validateUpdateStatusRequest(accountId, request);
		MessengerContact messengerContact = null;
		if (Objects.nonNull(request.getConversationId()) && Objects.nonNull(request.getCustomerId())) {
			messengerContact = messengerContactService.findById(request.getConversationId());
		} else if (Objects.nonNull(request.getCustomerId())) {
			messengerContact = messengerContactService.findByCustomerId(request.getCustomerId());
		} else {
			messengerContact = messengerContactService.findById(request.getConversationId());
		}
		if (Objects.isNull(messengerContact)) {
            log.error("updateConversationStatus: messengerContact null for request {}", request);
            throw new NotFoundException(ErrorCode.INVALID_MESSENGER_CONTACT);
        }
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(messengerContact.getBusinessId());
        if (Objects.isNull(businessDTO)) {
            log.error("updateConversationStatus: businessDTO returned null from core-service for businessId {}", messengerContact.getBusinessId());
            throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
        }
        if (!businessDTO.getRoutingId().equals(accountId)) {
            log.error("AccountId {} from requestPayload does not match with the session token {}", messengerContact.getBusinessId(), accountId);
            throw new NotFoundException(ErrorCode.SMART_INBOX_FILTER_UNAUTHORIZED_ACCESS);
        }
        Integer userId=request.getUserId();
        if (userId != null) {
			commonService.checkUserAccountAccess(userId, accountId);
        }

        if (request.getUserEmailId() != null) {
			UserDTO user = commonService.getUserIdByEmail(request.getUserEmailId(), businessDTO.getBusinessId());
			userId=user.getId();
		}
        ConversationStatusRequest updateStatusRequest = new ConversationStatusRequest();
        updateStatusRequest.setOpen(request.getOpen());
        conversationService.updateStatus(accountId, userId, messengerContact.getId(), updateStatusRequest,true);
	}

	private void validateUpdateStatusRequest(Integer accountId, PublicUpdateStatusConversationRequest request) {
		if(Objects.isNull(accountId)){
			throw new InputValidationException(ErrorCode.NO_BUSINESS_FOUND);
		}
		if (Objects.isNull(request.getConversationId()) && Objects.isNull(request.getCustomerId())) {
			throw new InputValidationException(ErrorCode.MISSING_PARAMETERS);
		}
		if(Objects.isNull(request.getOpen())){
			throw new InputValidationException(ErrorCode.MISSING_PARAMETERS);
		}
	}

	@Override
	public MessageResponse receiveCustomChannelMessage(CustomChannelReceiveRequest request) throws Exception {
		return customChannelService.receiveMessage(request);
	}
}
