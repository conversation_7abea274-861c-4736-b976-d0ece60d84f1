package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ContactUsEventDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.EmailMessageDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.CustomerEventConsumerService;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.ReplyViaEmailService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.PhoneNoValidator;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EmailReceiveEventHandler extends MessageEventHandlerAbstract {
	private final MessengerEvent EVENT = MessengerEvent.EMAIL_RECEIVE;

	@Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private MessengerMediaFileService messengerMediaFileService;

	@Autowired
	protected MessengerContactRepository messengerContactRepository;
	
	@Autowired
	private ContactService contactService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	protected EmailService emailService;
	
	@Autowired
	private PulseSurveyService pulseSurveyService;
	
	@Autowired
	private CustomerEventConsumerService CustomerEventConsumerService;
	
	@Autowired
	private NLPService nlpService;
	
	@Autowired
    private RedisLockService redisLockService;

	@Autowired
    private CommonService commonService;
	
	@Autowired
	private ReplyViaEmailService replyViaEmailService;
	
	@Override void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.EMAIL.name());
		lastMessageMetadataPOJO.setLastMessageSource(Source.EMAIL.getSourceId());
		lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.EMAIL.getSourceId());
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastMsgOn(new Date(emailMessageDTO.getEmailDTO().getEmailReceivedTime()));
		messengerContact.setUpdatedAt(new Date(emailMessageDTO.getEmailDTO().getEmailReceivedTime()));
		messengerContact.setLastIncomingMessageTime(emailMessageDTO.getEmailDTO().getEmailReceivedTime());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		EmailDTO emailDTO = emailMessageDTO.getEmailDTO();
		ConversationDTO conversationDTO = emailMessageDTO.getConversationDTO();
		emailDTO.setEncrypted(1);
		// CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		// BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		if (StringUtils.isEmpty(emailDTO.getBody()) && !emailDTO.getEmailAttachments().isEmpty()) {
			messengerContact.setLastMessage("Received an attachment");
		} else {
			messengerContact.setLastMessage(emailDTO.getBody());
		}
		boolean isEncrypted = EncryptionUtil
				.encryptLastMessage(messengerContact, emailDTO.getEncrypted(),
						conversationDTO.getRecipient(), conversationDTO.getSender());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}


	@Override MessageTag getMessageTag(MessageDTO messageDTO) {
		if (getSendEmailNotification(messageDTO)) {
			return MessageTag.UNREAD;
		} else return MessageTag.INBOX;
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(emailMessageDTO.getConversationDTO(),
				messageDTO.getMessengerContact().getId());
		if(CollectionUtils.isNotEmpty(emailMessageDTO.getEmailDTO().getEmailAttachments()))
			messageDocumentDTO.setMediaFiles(emailMessageDTO.getEmailDTO().getEmailAttachments().stream().map(attachement -> new MessageDocument.MediaFile(attachement.getFileExtension(), attachement.getUrl(), attachement.getContentSize(), attachement.getName(), attachement.getContentType())).collect(
					Collectors.toList()));
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		CustomerDTO customerDTO = getCustomerDTO(emailMessageDTO);
		if( customerDTO.getBlocked() != null && customerDTO.getBlocked().equals(true)){
			messageDocumentDTO.setSpam(true);
		}else{
			messageDocumentDTO.setSpam(false);
		}
		return messageDocumentDTO;
	}

	void addActivityIfChannelChanged(MessageDTO messageDTO) {
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(emailMessageDTO.getMessengerContact());
		String lastChannel = lastMessageMetadataPOJO.getLastMessageChannel();
		log.info("[addActivityIfChannelChanged] channel present {}", lastChannel);
		if(lastChannel == null || !MessageDocument.Channel.EMAIL.name().equals(lastChannel)) {
			ActivityDto activityDto = ActivityDto.builder().mcId(emailMessageDTO.getMessengerContact().getId()).created(new Date(emailMessageDTO.getEmailDTO().getEmailReceivedTime() - 1000))
					.actorId(emailMessageDTO.getCustomerId()).activityType(ActivityType.EMAIL_START).from(emailMessageDTO.getMessengerContactId()).businessId(emailMessageDTO.getBusinessId())
					.to(emailMessageDTO.getBusinessId()).accountId(emailMessageDTO.getBusinessDTO().getAccountId()).source(Source.EMAIL.getSourceId()).build();
			messageDTO.setActivityMessage(activityDto);
		}
		log.info("[addActivityIfChannelChanged] Activity added {}", messageDTO.getActivityMessage());
	}

	
    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            CustomerDTO customerDTO = getCustomerDTO(messageDTO);
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), customerDTO.getId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        return messengerContact;
    }
	
	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if(Objects.isNull(messageId)) {
			EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
			messageId = emailMessageDTO.getEmailDTO().getEmailDataId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	private void addMessageMetaData(ConversationDTO conversationDTO) {
		conversationDTO.setMessageType(MessageDocument.MessageType.CHAT);
		conversationDTO.setChannel(MessageDocument.Channel.EMAIL);
		conversationDTO.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);

	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		if(Objects.isNull(emailMessageDTO.getBusinessDTO()) || Objects.isNull(emailMessageDTO.getBusinessDTO().getBusinessId())) {
			BusinessDTO businessDTO = businessService.getBusinessByBusinessNumber(emailMessageDTO.getBusinessNumber());
			if(Objects.isNull(businessDTO))
				throw new NotFoundException("[getBusinessDTO] No business found for given business Number " + emailMessageDTO.getBusinessNumber());
			messageDTO.setBusinessDTO(businessDTO);
		}
		return messageDTO.getBusinessDTO();
	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		KontactoRequest kontactoRequest = create(emailMessageDTO, getBusinessDTO(emailMessageDTO));
		 List<Integer> accountIds=contactService.getAllContactUpgradeEnabledAccounts();
	        if(CollectionUtils.isNotEmpty(accountIds)&& messageDTO.getBusinessDTO()!=null && accountIds.contains(messageDTO.getBusinessDTO().getAccountId())){
	        	CustomerDTO customerDTO = commonService.getActiveCustomerBySource(null, kontactoRequest.getEmailId(),
	    				kontactoRequest.getName(), emailMessageDTO.getBusinessDTO().getBusinessId(),
	    				emailMessageDTO.getBusinessDTO().getAccountId(), kontactoRequest.getSource());
	    		if (Objects.nonNull(customerDTO)) {
	    			emailMessageDTO.setCustomerDTO(customerDTO);
	    		}
	        }
		
		if(Objects.isNull(emailMessageDTO.getCustomerDTO()) || Objects.isNull(emailMessageDTO.getCustomerDTO().getBusinessId())) {
            // communication with Kontacto Service
            if (kontactoRequest.getEmailId()==null && kontactoRequest.getPhone()==null) {
            	//Added to avoid contacto call incase both are invalid - contact us form
            	throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_CHAT_REQUEST, ComponentCodeEnum.CUSTOMER)
						.message("Fail to create Kontacto request for email - {} & phone - {}", 
								kontactoRequest.getEmailId(), kontactoRequest.getPhone()));
            }
            CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, getBusinessDTO(emailMessageDTO).getRoutingId(), -1);
			if (customerDTO.getCommPreferences() != null
					&& customerDTO.getCommPreferences().getEmailPreferences() != null && !BooleanUtils
							.isTrue(customerDTO.getCommPreferences().getEmailPreferences().getMarketingOptin())) {
				contactService.updateCustomerSubscriptionStatus(customerDTO.getId(),
						getBusinessDTO(emailMessageDTO).getAccountId(), null, Constants.MARKETING, null, null);

			}
            if(Objects.isNull(customerDTO) || Objects.isNull(customerDTO.getId())) throw new MessengerException("Customer data not returned by contact-service for email received from: " + emailMessageDTO.getEmailDTO().getFrom());
            emailMessageDTO.setCustomerDTO(customerDTO);
		}
		return emailMessageDTO.getCustomerDTO();
	}

    private KontactoRequest create(EmailMessageDTO emailMessageDTO, BusinessDTO businessDTO) {
        KontactoRequest kontactoRequest = new KontactoRequest();
        kontactoRequest.setEmailId(emailMessageDTO.getEmailDTO().getFrom());
        kontactoRequest.setBusinessId(businessDTO.getBusinessId());
        //Identify contact-us leads on the basis of from email address
        Pattern leadsPattern = Pattern.compile("@leads\\.");

        // Check if to-email-address contain "@leads."
        boolean containsContactUsLeads = emailMessageDTO.getEmailDTO().getTo().stream()
        		.anyMatch(to -> leadsPattern.matcher(to.toLowerCase()).find());
        if (containsContactUsLeads){
        	getLeadDetailsFromBody(kontactoRequest, emailMessageDTO, businessDTO.getCountryCode());
        	kontactoRequest.setSource(KontactoRequest.CONTACT_US);
        } else {
        	kontactoRequest.setSource(KontactoRequest.EMAIL);
        }
        KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
        locationInfo.setCountryCode(businessDTO.getCountryCode());
        kontactoRequest.setLocation(locationInfo);
        return kontactoRequest;
    }

	@Override
	public MessageResponse handle(MessageDTO messageDTO) throws Exception {
		messageDTO.setMsgTypeForResTimeCalc("R");
		messageDTO.setSource(Source.EMAIL.getSourceId());
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);

		// do not proceed if messenger is not enabled for the business
		if (Integer.valueOf(0).equals(businessService.isMessengerEnabled(businessDTO.getAccountId()))) {
			log.info("[handle] Messenger not enabled for business {}", businessDTO.getAccountId());
			return new MessageResponse();
		}
		validate(emailMessageDTO);
		String disabledContactUs = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("account.disableContactUs", null);
		Set<Integer> disabledContactUsAccounts = ControllerUtil.getTokensListIntegerFromString(disabledContactUs);
		if (disabledContactUsAccounts.contains(businessDTO.getAccountId())) {
			log.info("Contact-Us form integration disabled for accountId: {}", businessDTO.getAccountId());
			return null;
		}
		//Email sent from client over notification : reply-<mcid>-<locationNumber>@inbox.birdeye.com
		boolean replyViaEmail = replyViaEmailService.checkAndReplyViaEmail(emailMessageDTO);
		if (replyViaEmail) {
			log.info("Reply via Email for accountId: {}", businessDTO.getAccountId());
			return null;
		}
		CustomerDTO customerDTO = getCustomerDTO(emailMessageDTO);
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX+customerDTO.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
    			log.info("Lock is already acquired for the key:{}", lockKey);
    			kafkaService.publishToKafkaAsync(KafkaTopicEnum.EMAIL_RECEIVE_EVENT_DELAYED_QUEUE,
    					emailMessageDTO);
    			return null;
    		}
			
			Pattern leadsPattern = Pattern.compile("@leads\\.");
			// Check if to-email-address contain "@leads."
			boolean containsContactUsLeads = emailMessageDTO.getEmailDTO().getTo().stream()
					.anyMatch(to -> leadsPattern.matcher(to.toLowerCase()).find());
			if (containsContactUsLeads){
				//Call Contact Us Flow
				ContactUsEventDTO contactUsEvent = new ContactUsEventDTO();
				contactUsEvent.setCid(customerDTO.getId());
				contactUsEvent.setBid(businessDTO.getBusinessId());
				if (emailMessageDTO.getEmailDTO().getBody() != null)
					contactUsEvent.setCustomerComment(emailMessageDTO.getEmailDTO().getBody());
				contactUsEvent.setRequestDate(new Date().getTime());
				contactUsEvent.setEmailBodyParsed(emailMessageDTO.isEmailBodyParsed());
				CustomerEventConsumerService.consumeContactUsEvent(contactUsEvent);
				return null;
			}
			emailMessageDTO.setCustomerId(customerDTO.getId());
			emailMessageDTO.setBusinessId(businessDTO.getBusinessId());
			emailMessageDTO.setBusinessDTO(businessDTO);
			//persisting email details to email table
			Email email = emailService.saveEmail(emailMessageDTO,
					String.valueOf(businessDTO.getBusinessNumber()),
					String.valueOf(getMessengerContact(messageDTO).getId()));
			emailMessageDTO.getEmailDTO().setEmailReceivedTime(email.getCreateDate().getTime());

			List<MessengerMediaFile> messengerMediaFileList = saveMediaFiles(emailMessageDTO, email);
			List<MessengerMediaFileDTO> messengerMediaFiles = getMessageMediaFilesDto(messengerMediaFileList);
			messageDTO.setMessengerMediaFileList(messengerMediaFiles);
			ConversationDTO conversationDTO = new ConversationDTO(emailMessageDTO, email);
			conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
			conversationDTO.setSender(String.valueOf(businessDTO.getBusinessNumber()));
			conversationDTO.setRecipient(String.valueOf(getMessengerContact(messageDTO).getId()));
			addMessageMetaData(conversationDTO);
			emailMessageDTO.setConversationDTO(conversationDTO);
			messengerMessageService.saveMessengerMessage(conversationDTO, null);
			//as email is received so putting notification to ON
			if(!customerDTO.getBlocked()) {
				emailMessageDTO.setSendEmailNotification(true);
			}
			//publishing activity if channel is changed
			publishActivity(messageDTO);
			// handle PulseSurveyContext
			PulseSurveyContext context = null;
			try {
				context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
					customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
				} else {
					customerDTO.setOngoingPulseSurvey(false);
				}
				messageDTO.setCustomerDTO(customerDTO);
			} catch (Exception ex) {
				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
			}
			return super.handle(messageDTO);
		} finally {
    		if (lockOpt.isPresent()) {
    			redisLockService.unlock(lockOpt.get());
    		}
    	}
	}

	private void publishActivity(MessageDTO messageDTO) {
		addActivityIfChannelChanged(messageDTO);
		if(messageDTO.getActivityMessage() != null)
			conversationActivityService.persistActivityForChannel(messageDTO);
	}

	private List<MessengerMediaFile> saveMediaFiles(EmailMessageDTO emailMessageDTO, Email email) {
		List<MessengerMediaFile> messengerMediaFileList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(emailMessageDTO.getEmailDTO().getEmailAttachments())) {
			for (MessengerMediaFileDTO messengerMediaFileDTO : emailMessageDTO.getEmailDTO().getEmailAttachments()) {
				MessengerMediaFile mediaFile = new MessengerMediaFile(messengerMediaFileDTO, email.getId());
				messengerMediaFileList.add(messengerMediaFileService.saveMedia(mediaFile));
			}
		}
		return messengerMediaFileList;
	}

	private void validate(EmailMessageDTO emailMessageDTO) {

		if(StringUtils.isBlank(emailMessageDTO.getEmailDTO().getFrom()) || StringUtils.isBlank(emailMessageDTO.getEmailDTO().getSubject())) {
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.EMAIL_VALIDATION_FAILURE, ComponentCodeEnum.EMAIL)
					.message("Email From or Subject is missing | business - {}, customer - {}", 
							emailMessageDTO.getBusinessId(), emailMessageDTO.getCustomerId()));
		}
		if(StringUtils.isNotBlank(emailMessageDTO.getEmailDTO().getBody()) && nlpService.isTextProfane(emailMessageDTO.getEmailDTO().getBody())) {
			
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.EMAIL_VALIDATION_FAILURE, ComponentCodeEnum.EMAIL)
					.message("Email body rejected in profanity filtering [{}] | business - {}", 
							 emailMessageDTO,emailMessageDTO.getBusinessDTO().getBusinessId()));
		}
		
		if(StringUtils.isNotBlank(emailMessageDTO.getEmailDTO().getBody()) && nlpService.isTextProfane(emailMessageDTO.getEmailDTO().getBody())) {
			throw new InputValidationException(new ErrorMessageBuilder(ErrorCode.EMAIL_VALIDATION_FAILURE, ComponentCodeEnum.EMAIL)
					.message("Input message not saved due to profanity filtering {} businessId: {}", emailMessageDTO,emailMessageDTO.getBusinessDTO().getBusinessId()));
		}
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
	}

	@Override public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		EmailMessageDTO emailMessageDTO = (EmailMessageDTO) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
		notificationRequest.setMsgId(emailMessageDTO.getEmailDTO().getEmailDataId());
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(10); // number of messages to be fetched from ES
		// The creation time is the last received time if last delivery time is null.
		if (emailMessageDTO.getEmailDTO().getEmailReceivedTime() != null) {
			notificationRequest.setLastMsgTime(emailMessageDTO.getEmailDTO().getEmailReceivedTime());
		} else {
			notificationRequest.setLastMsgTime(new Date().getTime());
			log.info("[getEmailNotificationMetaData] Both email sentOn and createDate found null for businessId {} emailId {}", businessDTO.getBusinessId(), emailMessageDTO.getEmailDTO().getEmailDataId());
		}
		notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
		return notificationRequest;

	}

	private List<MessengerMediaFileDTO> getMessageMediaFilesDto(List<MessengerMediaFile> messengerMediaFileList) {
		List<MessengerMediaFileDTO> messengerMediaFiles = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(messengerMediaFileList)) {
			for (MessengerMediaFile mediaFile : messengerMediaFileList) {
				MessengerMediaFileDTO messengerMediaFileDTO = new MessengerMediaFileDTO(mediaFile);
				messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
				messengerMediaFiles.add(messengerMediaFileDTO);
			}
		}
		return messengerMediaFiles;
	}
	
	private void getLeadDetailsFromBody(KontactoRequest kontactoRequest, EmailMessageDTO emailMessageDTO, String countryCode) {
		/*
		 * 1. Check for Template in email body, If Found - extract details from Template
		 * 2. If Template is not present, Extract details with each Token - separator tokens used -> [: = -]
		 * 3. If email or phone is not found, Use email Regex and PhoneNo library
		 * 
		 */
    	boolean templateExists = checkForContactUsLeadTemplate(kontactoRequest, emailMessageDTO, countryCode);
    	if (!templateExists) {
    		//extract from body
    		String body = emailMessageDTO.getEmailDTO().getBody();
    		body = body.replaceAll("\\t+", "").replaceAll("\\r+", "").replaceAll("\\n+", "\n").replaceAll(" +", " ").trim(); //formatting fix
    		String nameToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("supported_name_tokens", "Name,name,FirstName,firstName,FName,fName");
    		String emailToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("supported_email_tokens", "Email,email,EmailId,emailId");
    		String phoneToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("supported_phone_tokens", "Phone,phone,Phone Number,PhoneNumber,Phone No,PhoneNo,Mobile Number,MobileNo");
    		String commentToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("supported_comment_tokens", "Comment,comment,Body,body,Message,message");

    		List<String> nameTokenList = ControllerUtil.getTokensListFromString(nameToken);
    		List<String> emailTokenList = ControllerUtil.getTokensListFromString(emailToken);
    		List<String> phoneTokenList = ControllerUtil.getTokensListFromString(phoneToken);
    		List<String> commentTokenList = ControllerUtil.getTokensListFromString(commentToken);

    		String name = getTokenValueFromBody(body, nameTokenList);
    		if (!StringUtils.isEmpty(name)) {
    			name = name.replaceAll(",", ""); //some times in email template, comma was present at the end
    			kontactoRequest.setName(name);
    		}
    			
    		String phone = getTokenValueFromBody(body, phoneTokenList);
    		if (!StringUtils.isEmpty(phone)) {
    			phone = phone.replaceAll(",", ""); //some times in email template, comma was present at the end
    			boolean isValid = PhoneNoValidator.isValidPhoneNumber(phone, countryCode);
    			if (isValid)
    				kontactoRequest.setPhone(phone);
    		} else {
    			String phoneNumber = PhoneNoValidator.extractPhoneNumberFromString(body, countryCode);
    			if (!StringUtils.isEmpty(phoneNumber)) {
    				kontactoRequest.setPhone(phoneNumber);
    			}
    		}

    		String email = getTokenValueFromBody(body, emailTokenList);
    		if (!StringUtils.isEmpty(email)) {
    			email = email.replaceAll(",", ""); //some times in email template, comma was present at the end
    			email = MessengerUtil.getEmailAddressFromString(body);
    			kontactoRequest.setEmailId(email);
    		} else {
    			String emailId = MessengerUtil.getEmailAddressFromString(body);
    			if (!StringUtils.isEmpty(emailId)) {
    				kontactoRequest.setEmailId(emailId);
    			} else {
    				kontactoRequest.setEmailId(null);
    			}
    		}

    		String comment = getWholeMessageFromBody(body, commentTokenList);
    		if (!StringUtils.isEmpty(comment)) {
    			emailMessageDTO.getEmailDTO().setBody(comment);
    		} else {
    			emailMessageDTO.getEmailDTO().setBody(body);
    			emailMessageDTO.setEmailBodyParsed(false); //set as false to save last message in ContactDocument -> "You have a new lead"
    		}
    	}
    }

    private boolean checkForContactUsLeadTemplate(KontactoRequest kontactoRequest, EmailMessageDTO emailMessageDTO, String countryCode) {
    	String separator = "---$#$#$#$---";
    	String body = emailMessageDTO.getEmailDTO().getBody();
    	String jsonString = StringUtils.substringBetween(body, separator, separator);
    	boolean templateFound = false;
    	if (jsonString != null) {
    		jsonString = jsonString.replaceAll("\\t+", "").replaceAll("\\r+", "").replaceAll("\\n+", "").replaceAll(" +", " ").trim(); //formatting fix
    		log.info("Template Found in checkForContactUsLeadTemplate {}", jsonString);
    		try {
    			JSONObject json = new JSONObject(jsonString);
    			if (json.has("Name")) {
    				kontactoRequest.setName(json.getString("Name"));
    			}
    			if (json.has("EmailId")) {
    				String email = MessengerUtil.getEmailAddressFromString(json.getString("EmailId"));
    				kontactoRequest.setEmailId(email);
    				if (!StringUtils.isEmpty(email))
    					templateFound = true;
    			}
    			if (json.has("Phone")) {
    				String phone = PhoneNoValidator.getFormattedBusinessPhoneNumber(json.getString("Phone"), countryCode);
    				kontactoRequest.setPhone(phone);
    				if (!StringUtils.isEmpty(phone))
    					templateFound = true;
    			}
    			if (json.has("Comment")) {
    				emailMessageDTO.getEmailDTO().setBody(json.getString("Comment"));
    			} else {
    				emailMessageDTO.getEmailDTO().setBody(null);
    			}
    		} catch (JSONException e) {
    			log.warn("Json Mapping Not found in Email body with Template");
    			throw new BadRequestException("[Contact Us Lead] Invalid input");
    		}
    	} else {
    		log.info("ContactUs Lead Template not found for businessId: {}", kontactoRequest.getBusinessId());
    	}
    	return templateFound;
    }
    
    private String getTokenValueFromBody(String body,List<String> tokens) {
    	String tokenValue = null;
    	//Extract with separator and then without separator
		for (String token : tokens) {
			Matcher matcherWithSeparator = Pattern.compile(token+"\\s*?"+"[:=\\-]").matcher(body);
			while (matcherWithSeparator.find()) {
				tokenValue = matcherWithSeparator.group();
				break;
			}
			if (tokenValue!=null) {
				tokenValue = StringUtils.substringBetween(body, tokenValue, "\n");
				break;
			}
			
			Matcher matcherWithoutSeparator = Pattern.compile(token).matcher(body);
			while (matcherWithoutSeparator.find()) {
				tokenValue = matcherWithoutSeparator.group();
				break;
			}
			if (tokenValue!=null) {
				tokenValue = StringUtils.substringBetween(body, tokenValue, "\n");
				break;
			}
		}
		return tokenValue!=null ? tokenValue.trim() : null;
	}
    
    //Get Entire body after Message token
    private String getWholeMessageFromBody(String body,List<String> tokens) {
    	String tokenValue = null;
		for (String token : tokens) {
			Matcher matcherWithSeparator = Pattern.compile(token+"\\s*?"+"[:=\\-]").matcher(body);
			while (matcherWithSeparator.find()) {
				tokenValue = matcherWithSeparator.group();
				break;
			}
			if (tokenValue!=null) {
				tokenValue = substringBetween(body, tokenValue).trim();
				break;
			}
			
			Matcher matcherWithoutSeparator = Pattern.compile(token).matcher(body);
			while (matcherWithoutSeparator.find()) {
				tokenValue = matcherWithoutSeparator.group();
				break;
			}
			if (tokenValue!=null) {
				tokenValue = substringBetween(body, tokenValue).trim();
				break;
			}
		}
		return tokenValue;
	}
	
	public String substringBetween(final String str, final String open) {
        if (str == null || open == null) {
            return null;
        }
        final int start = str.indexOf(open);
        if (start != -1) {
            final int end = str.length();
            if (end != -1) {
                return str.substring(start + open.length(), end);
            }
        }
        return null;
    }
    
}
