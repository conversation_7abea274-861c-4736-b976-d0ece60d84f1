package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.enums.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.text.TextStringBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.EmailMessageConstants;
import com.birdeye.messenger.dao.entity.HtmlTemplates;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.HtmlTemplatesRepository;
import com.birdeye.messenger.dto.MessengerGlobalFilter.NotificationType;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ConversationIds;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.NexusEmailService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.NotificationService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.SamayService;
import com.birdeye.messenger.service.fetchDelta.FetchDeltaResponseService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

    private final MessengerContactService messengerContactService;
    private final HtmlTemplatesRepository htmlTemplatesRepository;
    private final NexusEmailService nexusEmailService;
    private final UserService userService;
    private final BusinessService businessService;
    private final CommonService commonService;
    private final RedisHandler redisHandler;
    private final SamayService samayService;

    @Autowired
    @Qualifier("MessengerTaskExecutor")
    private ThreadPoolTaskExecutor poolExecutor;
    private final List<FetchDeltaResponseService> fetchDeltaResponseServiceList;

    @Value("${email.reply.domain}")
    private String emailReplyDomainForNotification;

    /**
     * This method sends unresponded message notification to business users.
     * @param messageDocument :  Pass a valid MessageDocument doc in argument to add it to list of fetched doc from ES or a doc with empty fields. But it must not be null.
     * In case of instant notification we'll have a valid messageDocument. In case of deferred notifications it will just be a empty object since by then the document would be available on ES.
     * Reason : ElasticSearch does not immediately return the document saved. It takes a little time to replicate it across clusters. Hence we keep a
     * copy of document pushed to ES so that it can be appended to email body for instant notification relay.
     * @param notificationRequest all the metadata for email and filters for ES query.
     */
    @Override
    public void sendEmailNotification(MessengerGlobalFilter notificationRequest, MessageDocument messageDocument) {
        log.info("sendEmailNotification: Notification request {} accountId {} conversationId {}", notificationRequest, notificationRequest.getAccountId(), notificationRequest.getConversationId());
        MessengerContact messengerContact = messengerContactService.findById(notificationRequest.getConversationId());
        //LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        if(messengerContact==null) {
        	log.info("Cannot send Email Notification as messenger contact Id {} does not exist",notificationRequest.getConversationId());
        	return;
        }
        if ((messengerContact.getLastResponseAt() == null || new Date(notificationRequest.getLastMsgTime()).after(messengerContact.getLastResponseAt()))) {
            log.info("sendEmailNotification : Last responded at : {} and last received message time : {} ",  messengerContact.getLastResponseAt(), new Date(notificationRequest.getLastMsgTime()));

            // 1. get ContactDocument
            notificationRequest.setCustomerId(messengerContact.getCustomerId());
            notificationRequest.setStartIndex(0);
            notificationRequest.setCount(5);
            ContactDocument contact = messengerContactService.getContact(notificationRequest);

            // 2. get MessageDocuments
            // data is stored on ES on UTC Timezone
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            df.setTimeZone(TimeZone.getTimeZone("UTC"));
            if(messengerContact.getLastResponseAt() != null) {
                notificationRequest.setStartDate(df.format(messengerContact.getLastResponseAt()));
            }
            List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(notificationRequest);
            Optional<MessageDocument> duplicateDoc = messagesFromES.stream().filter(doc -> doc.getM_id().equals(messageDocument.getM_id())).findFirst();
            if(!duplicateDoc.isPresent() && StringUtils.isNotEmpty(messageDocument.getM_id())) {
                messagesFromES.add(messageDocument);
            }

            // 3. send Notification
            boolean isNotificationSent = sendNotification(notificationRequest, contact, messagesFromES);
            if(isNotificationSent) {
                messengerContact.setLastAlertAt(new Date());
                messengerContactService.saveOrUpdateMessengerContact(messengerContact);
            }
        } else {
            log.info("sendEmailNotification: No notification email sent as Last responded time {} > lastMessage time {}. MessengerContactId {}, MessageId {}, bizId {} ", messengerContact.getLastResponseAt(), new Date(notificationRequest.getLastMsgTime()), messengerContact.getId(), notificationRequest.getMsgId(), notificationRequest.getBizId());
        }
    }

    private boolean sendNotification(MessengerGlobalFilter notificationRequest, ContactDocument contactFromES, List<MessageDocument> messagesFromES) {
        log.info("sendEmailNotification: contactFromES \n\n {} \n\n\n ", contactFromES);
        SendResponse messageResponse = new SendResponse(messagesFromES, contactFromES, notificationRequest.getTimeZone() != null ? notificationRequest.getTimeZone() : "PST");
        Set<String> emailIds = notificationRequest.getEmailIds();
        try {

            EmailDTO emailDTO = new EmailDTO();
            //replace business name
            String businessName = "";
            String locationName = "";
            if (StringUtils.isNotEmpty(notificationRequest.getBusinessAlias())) {
                locationName = notificationRequest.getBusinessAlias();
            } else {
                locationName = notificationRequest.getBusinessName();
            }
            if (notificationRequest.getEnterpriseId() != null) {
                businessName = notificationRequest.getEnterpriseName();
            } else {
                businessName = locationName;
            }
            String contactEmail = messageResponse.getEmailId();
            String contactName = messageResponse.getName();
            String contactPhone = messageResponse.getPhone();

            Map<String, String> dataModel = new HashMap<>();
            dataModel.put("[BusinessName]", businessName);
            dataModel.put("[BusinessId]", notificationRequest.getBizId().toString());
            dataModel.put("[CustomerId]", notificationRequest.getConversationId().toString());
            dataModel.put("[LocationName]", locationName);
            dataModel.put("[CustomerName]", contactName);
            if(!StringUtils.endsWith(contactEmail, "@fb.com")) {
            	dataModel.put("[CustomerEmail]", contactEmail);
            }
            dataModel.put("[CustomerPhone]", contactPhone);

            List<HtmlTemplates> htmlTemplates = htmlTemplatesRepository.findByType("messenger_alert");
            HtmlTemplates htmlTemplate = htmlTemplates.get(0); //TODO: Cache it
            TextStringBuilder htmlGeneric = new TextStringBuilder(htmlTemplate.getContent());
            htmlGeneric.replaceAll("[BusinessName]", dataModel.get("[BusinessName]"));
            htmlGeneric.replaceAll("[BusinessId]", dataModel.get("[BusinessId]"));
            htmlGeneric.replaceAll("[CustomerId]", dataModel.get("[CustomerId]"));
            htmlGeneric.replaceAll("[LocationName]", dataModel.get("[LocationName]"));
            htmlGeneric.replaceAll("[CustomerName]", dataModel.get("[CustomerName]"));
            if (StringUtils.isNotEmpty(dataModel.get("[CustomerEmail]"))) {
                htmlGeneric.replaceAll("<!-- CustomerEmail", "");
                htmlGeneric.replaceAll("[CustomerEmail]", dataModel.get("[CustomerEmail]"));
                htmlGeneric.replaceAll("CustomerEmail -->", "");
            }
            htmlGeneric.replaceAll("[CustomerPhone]", dataModel.get("[CustomerPhone]"));
            StringBuilder messages = new StringBuilder();
            String imageBaseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("image_url_base", "image_url_base");
            String spaceImage = imageBaseUrl + "email/webchat/blank.gif";
            int i = 0;
            int messageCount = 0;
            String source = null;
            for (String key : messageResponse.getMessagesByDate().keySet()) {
                String timeStamp = key;
                DateFormat mdf = new SimpleDateFormat("MMM dd, yyyy hh:mm a");
                if (DateUtils.isSameDay(mdf.parse(key.substring(0, key.lastIndexOf(" "))), MessengerUtil.convertToBusinessTimeZone(new Date(),
                        TimeZone.getTimeZone(notificationRequest.getTimeZone() != null ? notificationRequest.getTimeZone() : "PST")).getTime())) {
                    if (i > 0) {
                        timeStamp = key.substring(key.lastIndexOf(":") - 2, key.length());
                    }
                    i++;
                }
                int j = 0;
                for (MessageResponse.Message msg : messageResponse.getMessagesByDate().get(key)) {
                    if (MessageDocument.CommunicationDirection.SEND.equals(msg.getDirection()) || (StringUtils.isNotEmpty(msg.getContent()) && (msg.getContent().trim().equalsIgnoreCase("START") || msg.getContent().trim().equalsIgnoreCase("STOP")))) {
                        if (Integer.valueOf(msg.getId()) > notificationRequest.getMsgId() && (msg.getCreatedBy()!=null && msg.getCreatedBy().getUserId()!=null && msg.getCreatedBy().getUserId()>0)) {
                            log.info("sendEmailNotification: no email notification sent for messengerContact {} as messageId in ES > requested for {}, messengerContact {}", msg.getId(), notificationRequest.getMsgId(), notificationRequest.getConversationId());
                            return false;
                        } else {
                            if (i == 1) {
                                i--;
                            }
                            continue;
                        }
                    }
                    if (StringUtils.isEmpty(msg.getContent()) && CollectionUtils.isEmpty(msg.getMediaFiles())) {
                        continue;
                    }
                    if (msg.getSource() != null) {
                        if (msg.getSource() == 2) {
                            source = " on your website";
                        } else if (msg.getSource() == 6) {
                            String productName = StringUtils.isNotBlank(notificationRequest.getProductName()) ? notificationRequest.getProductName() : "BirdEye"; // Default select * from parameters where name = 'product_name';
                            source = " on your " + productName + " profile";
                        } else {
                            source = null;
                        }
                    }
                    if (j < 1) {
                        messages.append("<tr>").append("<td align=\"left\" style=\"color: #999; font-size: 11px; font-weight: 500; font-family:'Roboto',Arial,sans-serif; \">")
                                .append(timeStamp).append("</td></tr>");
                    }
                    messages.append("<tr><td height=\"10\" style=\"font-size: 0; line-height: 0;\"><img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td></tr>");
                    messages.append("<tr><td align=\"left\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr><td align=\"left\" bgcolor=\"#f4f4f4\" style=\"background: #f4f4f4; border-radius: 29px;\"><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n"
                            + "<tr><td width=\"20\" style=\"font-size: 0; line-height: 0;\"><img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td><td><table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" width=\"100%\"><tr><td height=\"10\" style=\"font-size: 0; line-height: 0;\">"
                            + "<img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td></tr><tr>"
                            + "<td style=\"font-size: 14px; color: #212121; line-height: 1.57; font-family:'Roboto',Arial,sans-serif;\">\n");
                    if (CollectionUtils.isNotEmpty(msg.getMediaFiles())) {
                    	MediaFile mediaFile=msg.getMediaFiles().get(0);
                    	if(mediaFile.getA_url()!=null) {
                    		messages.append("<img src=\"").append(MessengerUtil.decode(mediaFile.getA_url(),"UTF-8")).append("\"><br>");
                    	}
                    }
                    if (StringUtils.isNotEmpty(msg.getContent())) {
                        messages.append(msg.getContent());
                    }
                    messages.append("</td></tr><tr><td height=\"10\" style=\"font-size: 0; line-height: 0;\"><img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td></tr></table></td><td width=\"20\" style=\"font-size: 0; line-height: 0;\">"
                            + "<img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td></tr></table></td></tr></table></tr>");
                    j++;
                    messageCount++;
                    messages.append("<tr><td height=\"10\" style=\"font-size: 0; line-height: 0;\"><img src=\"").append(spaceImage).append("\" width=\"1\" height=\"1\" alt=\"\" ></td></tr>");
                }
            }
            if (StringUtils.isBlank(messages)) {
                log.info("sendEmailNotification: No email notification sent to users {} for messengerContact {} as no messages found ", emailIds, notificationRequest.getConversationId());
                return false;
            }
            if (messageCount > 1) {
                htmlGeneric.replaceAll("[Header]", "You got new messages" + (source != null ? source : "") + "!");
            } else {
                htmlGeneric.replaceAll("[Header]", "You got a new message" + (source != null ? source : "") + "!");
            }
            char customerNameStartLetter;
            if (StringUtils.isNotEmpty(contactPhone) && contactPhone.equalsIgnoreCase(contactName)) {
                customerNameStartLetter = contactName.charAt(1);
            } else if (StringUtils.isNotBlank(contactName)) {
                customerNameStartLetter = contactName.charAt(0);
            } else {
                customerNameStartLetter = 'C'; // weird fallback
            }
            htmlGeneric.replaceAll("[SenderImage]", imageBaseUrl + "source/icons/" + (String.valueOf(customerNameStartLetter)).toLowerCase() + "-dummy.png");
            emailDTO.setBusinessId(notificationRequest.getBizId().toString());
            emailDTO.setFrom(notificationRequest.getBirdEyeEmailId());
            emailDTO.setFromName(StringEscapeUtils.unescapeHtml4(notificationRequest.getBusinessName())); //TODO: Find another method. This is depricated
            htmlGeneric.replaceAll("[Messages]", messages.toString());
            emailDTO.setBody(htmlGeneric.toString());
            String emailReplyDomain = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("messenger.replyToDomain", "messenger.birdeye.com");
            if (StringUtils.isNotEmpty(emailReplyDomain)) {
                emailDTO.setReplyTo(contactName + " <" + notificationRequest.getConversationId() + "@" + emailReplyDomain + ">");
            }
            emailDTO.setSubject("New messages from " + contactName + (source != null ? source : "") + "!");
            for (String emailId : emailIds) {
                emailDTO.clearAndAddTo(emailId);
				nexusEmailService.sendMail(notificationRequest.getBizId(), emailDTO, Constants.WEBCHAT_ALERT,
						String.valueOf(notificationRequest.getMsgId()), notificationRequest.getBusinessNumber(), null);
                log.info("sendEmailNotification : notification sent for businessId {} messageId {} emailId {} ", notificationRequest.getBizId(), notificationRequest.getMsgId(), emailId);
            }
            return true;
        } catch (Exception e) {
            log.error("sendEmailNotification: Exception sending notification for msgID [" + notificationRequest.getMsgId() + "] bizId [" + notificationRequest.getBizId() + "]", e);
        }
        return false;
    }

    private void sendNotificationForTeamAssignedConversation(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO, MessageDocument messageDocument, ContactDocument contact){
        log.info("process messenger notification: notificationRequest: {} ", notificationRequest);
        //TODO - Cache below call
        NotificationTypeSourceDTO nts = getNotificationTypeAndSource(notificationRequest, messageDocument);
        Map<String, Map<String,Map<String,String>>> scheduleToMapOfUserIdAndEmailId = businessService.getEmailConfigOfTeamsForNewMessage(businessDTO.getBusinessId(),contact.getTeam_id(), nts.getNotificationRequestType(), nts.getSource());
        if (MapUtils.isNotEmpty(scheduleToMapOfUserIdAndEmailId)) {
            scheduleToMapOfUserIdAndEmailId.forEach((schedule,userIdToEmailId) -> {
                Set<String> emailIds = emailIdsToNotify(userIdToEmailId.get("userIdEmailIdMap"),notificationRequest);
                if (CollectionUtils.isNotEmpty(emailIds)) {
                    notificationRequest.setEmailIds(emailIds);
                    if (schedule.equalsIgnoreCase(Constants.BusinessService.SCHEDULE_INSTANT)) {
                        notificationRequest.setMessageForTeam(true);
                        sendEmailNotificationV2(notificationRequest, messageDocument);
                    } else {
                        String notificationTriggerTime = null;
                        try {
                            notificationTriggerTime = MessengerUtil.parseEmailSendTime(businessDTO.getTimeZoneId(), schedule);
                            notificationRequest.setNotificationTriggerTime(notificationTriggerTime);
                            scheduleJobForNotification(notificationRequest,schedule);
                        } catch (ParseException | NumberFormatException e) {
                            log.error(
                                "processUnreadMessageNotification: webChat Interval or Format {} is incorrect for business {} and user emailId {} ",
                                schedule, businessDTO.getBusinessId(), emailIds);
                        }
                    }
                }
            });
        } else {
            log.info("No email present to send Email Notification");
        }
    }

	private void sendNotificationForUsrAssignedOrUnassignedConversation(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO,
                                                                     MessageDocument messageDocument){
      log.info("process messenger notification: notificationRequest: {} ", notificationRequest);
        NotificationTypeSourceDTO nts = getNotificationTypeAndSource(notificationRequest, messageDocument);
        Map<String, Map<String,Map<String,Map<String,String>>>> webChatIntervalToUserNotifications = userService.getInboxUserEmailAlertSettings(businessDTO.getBusinessId(),nts.getNotificationRequestType(),nts.getSource());
//      Map<String, Map<String,Map<String,Map<String,String>>>> webChatIntervalToUserNotifcations = userService.getInboxUserEmailAlertSettings(notificationRequest, messageDocument);
      if (MapUtils.isNotEmpty(webChatIntervalToUserNotifications)) {
          webChatIntervalToUserNotifications.forEach((webChatInterval, messengerUserNotificationMap) -> {
              Set<String> emailIds = getSendNotificationEmailIds(messengerUserNotificationMap, notificationRequest);
              if (CollectionUtils.isNotEmpty(emailIds)) {
                  notificationRequest.setEmailIds(emailIds);
                  if (webChatInterval.equalsIgnoreCase("Instant")) {
                      sendEmailNotificationV2(notificationRequest, messageDocument);
                  } else {
                      String notificationTriggerTime = null;
                      try {
                          notificationTriggerTime = MessengerUtil.parseEmailSendTime(businessDTO.getTimeZoneId(), webChatInterval);
                          notificationRequest.setNotificationTriggerTime(notificationTriggerTime);
                          scheduleJobForNotification(notificationRequest,webChatInterval);
                      } catch (ParseException | NumberFormatException e) {
                          log.error(
                              "processUnreadMessageNotification: webChat Interval or Format {} is incorrect for business {} and user emailId {} ",
                              webChatInterval, businessDTO.getBusinessId(), emailIds);
                      }
                  }
              }
          });
      }
    }
	@Override
	@Async
	public void processMessageNotification(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO, MessageDocument messageDocument) {
        log.info("process messenger notification: notificationRequest: {} ", notificationRequest);
        if (!Integer.valueOf(0).equals(businessService.isMessengerEnabled(businessDTO.getAccountId()))) {
            processEmailsForInboxCustomers(notificationRequest, businessDTO, messageDocument);
        }

	}

    /*private void processEmailsForInboxLeads(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO, MessageDocument messageDocument) {
        Map<String, List<String>> emailSendTimeToEmailIds = userService.getEmailSendTimeForNonInboxUsers(businessDTO.getBusinessId());
        if (MapUtils.isNotEmpty(emailSendTimeToEmailIds) && CollectionUtils.isNotEmpty(emailSendTimeToEmailIds.values())) {
            List<String> emailIdsForEmail = emailSendTimeToEmailIds.values().stream()
                    .flatMap(Collection::stream).
                            collect(Collectors.toList());
            notificationRequest.setEmailIds(new HashSet<>(emailIdsForEmail));
            sendEmailNotificationV2(notificationRequest, messageDocument);
            *//*emailSendTimeToEmailIds.forEach((emailSendTime, emailIds) -> {
                notificationRequest.setEmailIds(new HashSet<>(emailIds));
                if(StringUtils.equalsIgnoreCase("Instant", emailSendTime)){
                    sendEmailNotificationV2(notificationRequest, messageDocument);
                }
                else {
                    scheduleDeferredEmailNotification(notificationRequest,businessDTO, emailSendTime);
                }
            });*//*
            return;
        }
        log.info("processEmailsForInboxLeads: No emails found to send notification businessID {}", businessDTO.getBusinessId());
    }*/

    private void processEmailsForInboxCustomers(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO, MessageDocument messageDocument) {
        // FIXME - We can pass messageDTO in this method, which may have MessengerContact for some cases
        ContactDocument contact = messengerContactService.getContact(notificationRequest);
        if(Objects.nonNull(messageDocument)){
            notificationRequest.setSource(messageDocument.getSource());
        }
        if (contact.getAssignmentType() != null
                && Constants.Assignment.TEAM_ASSIGNED.equalsIgnoreCase(contact.getAssignmentType())
                && contact.getTeam_id() != null) {
            sendNotificationForTeamAssignedConversation(notificationRequest, businessDTO, messageDocument, contact);
        } else {
            sendNotificationForUsrAssignedOrUnassignedConversation(notificationRequest, businessDTO, messageDocument);
        }
        if(NotificationType.CHAT.equals(notificationRequest.getNotificationType())) {
            sendEmailNotificationForUnRespondedMessages(notificationRequest, messageDocument, businessDTO);
        }

    }

    @Override
	public Set<String> getSendNotificationEmailIds(Map<String,Map<String,Map<String,String>>> messengerUserNotificationMap,
			MessengerGlobalFilter notificationRequest) {
		notificationRequest.setStartIndex(0);
		Set<String> userEmails = new HashSet<String>();
		notificationRequest.setCount(1);
		ContactDocument contact = messengerContactService.getContact(notificationRequest);
		Integer cr_assigned_to = contact.getCr_asgn_id();
		Map<String,Map<String,String>> usersNotificationsSettingsMap=messengerUserNotificationMap.get("conversationTypeConfigMap");
		if(MapUtils.isNotEmpty(usersNotificationsSettingsMap)) {
			usersNotificationsSettingsMap.forEach((noticationType, usersMap) -> {
			if (contact.assignedToUser() && Constants.ASSINGNED_TO_ME.equals(noticationType)) {
				usersMap.forEach((userId, userEmail) -> {
					if (userId.equals(cr_assigned_to.toString())) {
						userEmails.add(userEmail);
					}
				});
			} else if (contact.assignedToUser() && Constants.ASSINGNED_TO_OTHERS.equals(noticationType)) {
				usersMap.forEach((userId, userEmail) -> {
					if (!userId.equals(cr_assigned_to.toString())) {
						userEmails.add(userEmail);
					}
				});
			} else if (contact.unassigned() && Constants.UNASSIGNED.equals(noticationType)) {
				usersMap.forEach((userId, userEmail) -> {
					userEmails.add(userEmail);
				});
			} else if(contact.unassigned() && MessengerGlobalFilter.NotificationType.NOTE.equals(notificationRequest.getNotificationType()) && Constants.ASSINGNED_TO_ME.equals(noticationType)){
                usersMap.forEach((userId, userEmail) -> {
                    userEmails.add(userEmail);
                });
            }

		});
		}
		//Business User who has added a Note/Transferred Chat Location should not be notified for his own added note
		if(MessengerGlobalFilter.NotificationType.NOTE.equals(notificationRequest.getNotificationType()) ||
				MessengerGlobalFilter.NotificationType.CHAT_TRANSFER.equals(notificationRequest.getNotificationType())){
			if(notificationRequest.getUserDTO()!=null) {
				String loggedInUserEmailId = notificationRequest.getUserDTO().getEmailId();
				userEmails.remove(loggedInUserEmailId );
			}
		}
		return userEmails;
	}


    @Override
    public Set<String> emailIdsToNotify(Map<String,String> userIdEmailIdMapDTO,MessengerGlobalFilter notificationRequest) {
        Set<String> userEmails = new HashSet<>();
        if(MapUtils.isNotEmpty(userIdEmailIdMapDTO)) {
            userIdEmailIdMapDTO.forEach((key, value) -> {
                userEmails.add(value);
            });
        }
        //Business User who has added a Note/Transferred Chat Location should not be notified for his own added note
        if(MessengerGlobalFilter.NotificationType.NOTE.equals(notificationRequest.getNotificationType()) ||
        		MessengerGlobalFilter.NotificationType.CHAT_TRANSFER.equals(notificationRequest.getNotificationType())){
        	if(notificationRequest.getUserDTO()!=null) {
        		String loggedInUserEmailID=notificationRequest.getUserDTO().getEmailId();
        		userEmails.remove(loggedInUserEmailID);
        	}
        }
        return userEmails;
    }

    /**
     * Mail alert for unresponded messages.
     */
    @Override
    @Async
    public void processUnreadMessageNotification(BusinessDTO businessDTO, MessageDocument messageDocument) {
		log.info("processUnreadMessageNotification: notificationRequest: businessId#{} and smsID {} ",
				businessDTO.getBusinessId(), messageDocument.getM_id());

		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();

		notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setMsgId(Integer.valueOf(messageDocument.getM_id()));
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES

        // The creation time is the last received time if last delivery time is null.
        Date msgDate = DateUtils.getDateInUTC(messageDocument.getCr_date(),MessageDocumentDTO.DATE_FORMAT);
		if (msgDate != null) {
			notificationRequest.setLastMsgTime(msgDate.getTime());
		} else {
			notificationRequest.setLastMsgTime(new Date().getTime());
			log.info("NotificationRequest: Both sms sentOn and createDate found null for businessId {} and smsID {} ",
					businessDTO.getBusinessId(), messageDocument.getM_id());
		}
        notificationRequest.setConversationId(Integer.valueOf(messageDocument.getC_id()));
        processMessageNotification(notificationRequest,businessDTO,messageDocument);
    }

    public void triggerEmail() {

    }


    @Override
    public void scheduleJobForNotification(MessengerGlobalFilter notificationRequest, String webchatInterval) {
        String redisKey = "INBOX_UNRESPONDED_JOB:" + notificationRequest.getBizId() + "_" + notificationRequest.getConversationId();
        if (ObjectUtils.isEmpty(redisHandler.getKeyValueFromRedis(redisKey))) {
            // Extract numeric value from webchatInterval (e.g., "15,M" -> 15L)
            long intervalMinutes = 15L; // default
            if (webchatInterval != null && webchatInterval.contains(",")) {
                try {
                    intervalMinutes = Long.parseLong(webchatInterval.split(",")[0]);
                } catch (NumberFormatException e) {
                    log.warn("Invalid webchatInterval format: {}", webchatInterval);
                }
            }
            redisHandler.setOpsForValueWithExpiry(redisKey, Constants.INBOX_UNRESPONDED_JOB, intervalMinutes, TimeUnit.MINUTES);
            samayService.publishToScheduler(notificationRequest, Long.parseLong(notificationRequest.getNotificationTriggerTime()), KafkaTopicEnum.DEFERRED_EMAIL_NOTIFICATION_QUEUE);
        } else {
            log.info("Scheduled job already present for businessId {} and conversationId {}", notificationRequest.getBizId(), notificationRequest.getConversationId());
        }
    }


        /*
         * notificationRequest : Required Fields
         * 	* ConversationId
         *  * BizId
         *  * BirdEyeEmailId
         *  * BizName
         *  * EmailIDs
         *  * Alias
         *  * EnterpriseName
         *  * businessName
         *  * MCID
         *  * NotificationType
         *  * LastMsgTime
         *  * ProductName
         *  * BusinessNumber
         */
        @Override
        public void sendEmailNotificationV2 (MessengerGlobalFilter notificationRequest, MessageDocument messageDocument)
        {
            log.debug("sendEmailNotification: Notification request {} accountId {} conversationId {}", notificationRequest,
                    notificationRequest.getAccountId(), notificationRequest.getConversationId());
            MessengerContact messengerContact = messengerContactService.findById(notificationRequest.getConversationId());
            // LastMessageMetaData lastMessageMetaData =
            // MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
            if (messengerContact == null) {
                log.info("Cannot send Email Notification as messenger contact Id {} does not exist",
                        notificationRequest.getConversationId());
                return;
            }
            Boolean messengerEnabled = !Integer.valueOf(0)
                    .equals(businessService.isMessengerEnabled(notificationRequest.getAccountId())) ? Boolean.TRUE
                            : Boolean.FALSE;
            BusinessDTO businessDTO = businessService.getBusinessDTO(notificationRequest.getBizId());
            EmailDTO emailDTO = new EmailDTO();
            emailDTO.setRecipientType(Constants.RECIPIENT_TYPE);
            emailDTO.setBusinessId(notificationRequest.getBizId().toString());
            List<String> emailList = notificationRequest.getEmailIds().stream().collect(Collectors.toList());
            emailDTO.setTo(emailList);
            String businessName = "";
            String locationName = "";
            if (StringUtils.isNotEmpty(notificationRequest.getBusinessAlias())) {
                locationName = notificationRequest.getBusinessAlias();
            } else {
                locationName = notificationRequest.getBusinessName();
            }
            if (notificationRequest.getEnterpriseId() != null) {
                businessName = notificationRequest.getEnterpriseName();
            } else {
                businessName = locationName;
                locationName = "";
            }

            notificationRequest.setConversationId(messengerContact.getId());
            notificationRequest.setStartIndex(0);
            notificationRequest.setCount(5);
            ContactDocument contact = messengerContactService.getContact(notificationRequest);
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("utmCampaign","messaging-notification") ;
            dataMap.put("businessName", businessName);
            dataMap.put("businessId", notificationRequest.getBizId().toString());
            dataMap.put("enterpriseId", notificationRequest.getEnterpriseId() != null ? notificationRequest.getEnterpriseId().toString() : null);
            dataMap.put("locationName", locationName);
            dataMap.put("mcId", String.valueOf(messengerContact.getId()));
            dataMap.put("messengerEnabled", messengerEnabled);
            dataMap.put("accountType", businessDTO.getAccountType().toLowerCase());
            dataMap.put("isAccountWhitelabled", businessDTO.isBusinessWhitelabled());
            String serverBaseURL = businessService.getWebsiteDomain(notificationRequest.getBizId());
            log.debug("Server Base URL : {}, for business Id : {}, for conversation Id : {}", serverBaseURL, notificationRequest.getBizId(), notificationRequest.getConversationId());
            dataMap.put("serverBaseURL", serverBaseURL + "/");
            String emailDate = new SimpleDateFormat("yyMMdd").format(new Date());
            dataMap.put("emailDate", emailDate);
            if (StringUtils.isNotBlank(contact.getC_phone())) {
                dataMap.put("customerPhoneNumber", contact.getC_phone());
            }
            if (contact.getC_email() != null && !StringUtils.endsWith(contact.getC_email(), "@fb.com")) {
                dataMap.put("customerEmailId", contact.getC_email());
            }
            dataMap.put("customerName", getNameForEmail(contact.getC_name(), "Customer"));

            // 1. get ContactDocument
            if ((MessengerGlobalFilter.NotificationType.CHAT.equals(notificationRequest.getNotificationType()) || MessengerGlobalFilter.NotificationType.UNRESPONDED.equals(notificationRequest.getNotificationType()))
                    && (messengerContact.getLastResponseAt() == null || new Date(notificationRequest.getLastMsgTime())
                    .after(messengerContact.getLastResponseAt()))) {
                log.debug("sendEmailNotification : Last responded at : {} and last received message time : {} ",
                        messengerContact.getLastResponseAt(), new Date(notificationRequest.getLastMsgTime()));
                // 2. get MessageDocuments
                // data is stored on ES on UTC Timezone
                DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
                df.setTimeZone(TimeZone.getTimeZone("UTC"));
//                if (messengerContact.getLastResponseAt() != null) {
//                    notificationRequest.setStartDate(df.format(messengerContact.getLastResponseAt()));
//                }
                notificationRequest.setExcludedSources(ControllerUtil.toCommaSeparatedString(commonService.getExcludedMessageSourcesList()));
                List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(notificationRequest);
                String subjectSuffix = EmailMessageConstants.SUBJECT;
                if(CollectionUtils.isNotEmpty(messagesFromES)) {
	                String m_id = messageDocument.getM_id();
	                Optional<MessageDocument> duplicateDoc = messagesFromES.stream().filter(doc -> doc.getM_id().equals(m_id))
	                        .findFirst();
	                if (!duplicateDoc.isPresent() && StringUtils.isNotEmpty(messageDocument.getM_id())) {
	                    messagesFromES.add(messageDocument);
	                }
	                subjectSuffix = getEmailSubjectSuffix(messagesFromES);
                }
                if (CommunicationDirection.RECEIVE.equals(messagesFromES.get(0).getCommunicationDirection()) &&
                		Source.APPOINTMENT.getSourceId().equals(messagesFromES.get(0).getSource())) {
                	subjectSuffix = String.format(subjectSuffix, locationName);
                }
                emailDTO.setSubject(String.format(subjectSuffix, getNameForEmail(contact.getC_name(), "Customer")));

                setReplyTo(notificationRequest, businessDTO, emailDTO, contact);
                
                List<EmailMessageDocument> emailNotificationMessages = getEmailNotificationMessages(messagesFromES,
                        NotificationType.CHAT, contact, notificationRequest.getTimeZone());
                Collections.sort(emailNotificationMessages);
                if (notificationRequest.isMessageForTeam()) {
                    dataMap.put("title", String.format(EmailMessageConstants.TEAM_TITLE,getSourceString(messageDocument.getSource())));
                    dataMap.put("teamName", contact.getTeam_name());
                } else {
                    dataMap.put("title", String.format(EmailMessageConstants.CHAT_TITLE,getSourceString(messageDocument.getSource())));
                }
                if(NotificationType.UNRESPONDED.equals(notificationRequest.getNotificationType())) {
                    dataMap.put("title", String.format(EmailMessageConstants.UNRESPONDED_MESSAGE_TITLE,notificationRequest.getInterval()));
                    String subjectSuffixUnresponded  = getEmailSubjectSuffixForUnRespondedMessageNotification(messagesFromES);
                    emailDTO.setSubject(String.format(subjectSuffixUnresponded, getNameForEmail(contact.getC_name(), "Customer"), notificationRequest.getInterval()));
                }
                //We are not sending email body in notifications for secure messages
                //Overiding the email subject here as messageDoc size 0
                if(Source.SECURE_MESSAGE.getSourceId().equals(contact.getLastMsgSource())) {
                	emailNotificationMessages=Collections.emptyList();
                    subjectSuffix = EmailMessageConstants.SUBJECT_SECURE_MESSAGE;
                    emailDTO.setSubject(String.format(subjectSuffix, getNameForEmail(contact.getC_name(), "Customer")));
                }
                dataMap.put("isMessage", true);
                dataMap.put("emailNotificationMessages", emailNotificationMessages);
                prepareVoiceMailMsgFormat(emailNotificationMessages, dataMap);

                try {
                    nexusEmailService.sendMailV2(notificationRequest.getBizId(), emailDTO, Constants.WEBCHAT_ALERT_V1,
                            Constants.MESSENGER_ALERT + "_" + messengerContact.getId(),
                            notificationRequest.getBusinessNumber(), dataMap);
                } catch (Exception e) {
                    log.error("error while pushing email data CHAT to kafka on topic : {}", e.getMessage());
                    return;
                }
                messengerContact.setLastAlertAt(new Date());
                messengerContactService.saveOrUpdateMessengerContact(messengerContact);
            } else if (MessengerGlobalFilter.NotificationType.NOTE.equals(notificationRequest.getNotificationType())) {
                List<MessageDocument> noteMessage = new ArrayList<MessageDocument>();
                if (StringUtils.isBlank(messageDocument.getM_id())) {
                    messageDocument = getNotesFromES(notificationRequest);
                    if (messageDocument == null) {
                        log.error("No Internal notes exist in ES with m_id: {}", notificationRequest.getMsgId());
                        return;
                    }
                }
                noteMessage.add(messageDocument);
                emailDTO.setSubject(getNameForEmail(notificationRequest.getUserDTO().getName(), "User") + " added an internal note with " + getNameForEmail(contact.getC_name(), "Customer") );
                List<EmailMessageDocument> emailNotificationMessages = getEmailNotificationMessages(noteMessage,
                        NotificationType.NOTE, contact, notificationRequest.getTimeZone());
                if (notificationRequest.isMessageForTeam())
                    dataMap.put("teamName", contact.getTeam_name());
                //We not not sending email body in notifications for secure messages
                if(Source.SECURE_MESSAGE.getSourceId().equals(contact.getLastMsgSource())) {
                	emailNotificationMessages=Collections.emptyList();
                }
                dataMap.put("emailNotificationMessages", emailNotificationMessages);
                dataMap.put("title", String.format(EmailMessageConstants.NOTE_ASSIGNED_TO_TITLE,
                        getNameForEmail(contact.getC_name(), "Customer")));
                try {
                    nexusEmailService.sendMailV2(notificationRequest.getBizId(), emailDTO, Constants.WEBCHAT_ALERT_V1,
                            Constants.MESSENGER_ALERT + "_" + messengerContact.getId(),
                            notificationRequest.getBusinessNumber(), dataMap);
                } catch (Exception e) {
                    log.error("error while pushing email data NOTE to kafka on topic : {}", e.getMessage());
                    return;
                }
            } else if (MessengerGlobalFilter.NotificationType.ASSIGNED_TO
                    .equals(notificationRequest.getNotificationType())) {
                dataMap.put("utmCampaign","messaging-notification-assigned") ;
                notificationRequest.setCount(5);
                notificationRequest.setQueryFile(Constants.Elastic.GET_MESSAGES_V1);
                notificationRequest.setExcludedSources(ControllerUtil.toCommaSeparatedString(commonService.getExcludedMessageSourcesList()));
                List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(notificationRequest);
                if (CollectionUtils.isEmpty(messagesFromES)) {
                    //In case of review only conversations we are not sending notification in case of assignments.
                    log.info("No message found in case of assignment notifications.Hence returning!!");
//                    return;
                }
                setEmailSubject(emailDTO, notificationRequest, messagesFromES);
                List<EmailMessageDocument> emailNotificationMessages = null;
                if (!CollectionUtils.isEmpty(messagesFromES)) {
                	emailNotificationMessages = getEmailNotificationMessages(messagesFromES, NotificationType.ASSIGNED_TO, contact, notificationRequest.getTimeZone());
                	Collections.sort(emailNotificationMessages);
                }

                if (notificationRequest.isMessageForTeam()) {
                    dataMap.put("title", EmailMessageConstants.TEAM_ASSIGNED_TO_TITLE);
                    dataMap.put("teamName", contact.getTeam_name());
                } else if(Boolean.TRUE.equals(notificationRequest.isAssignToOthers())){
                    dataMap.put("title", EmailMessageConstants.ASSIGNED_TO_OTHERS_TITLE);
                }else if (Boolean.TRUE.equals(notificationRequest.isUnassign())) {
                    dataMap.put("title", EmailMessageConstants.UNASSIGNED_TO_TITLE);
                }
                else {
                    dataMap.put("title", EmailMessageConstants.ASSIGNED_TO_TITLE);
                }
                //We not not sending email body in notifications for secure messages
                if(Source.SECURE_MESSAGE.getSourceId().equals(contact.getLastMsgSource())) {
                	emailNotificationMessages=Collections.emptyList();
                }
                dataMap.put("emailNotificationMessages", emailNotificationMessages);
                dataMap.put("isMessage", true);
                try {
                	emailDTO.setRequestSubType(Constants.WEBCHAT_ALERT_V1);
                	nexusEmailService.sendMailV2(notificationRequest.getBizId(), emailDTO, Constants.ASSIGNMENT_ALERT_V1, Constants.MESSENGER_ALERT + "_" + messengerContact.getId(),
                			notificationRequest.getBusinessNumber(), dataMap);
                } catch (Exception e) {
                    log.error("error while pushing email data ASSIGNED_TO {} to kafka on topic : {}", e.getMessage());
                    return;
                }
            } 
            else if (MessengerGlobalFilter.NotificationType.CHAT_TRANSFER.equals(notificationRequest.getNotificationType())) {
            	emailDTO.setSubject(notificationRequest.getUserDTO() != null ? String.format(EmailMessageConstants.CHAT_TRANSFER_SUBJECT, getNameForEmail(notificationRequest.getUserDTO().getName(), "User"), businessName)
            			: String.format(EmailMessageConstants.DEFAULT_CHAT_TRANSFER_SUBJECT, businessName));
            	dataMap.put("transferTitle", String.format(EmailMessageConstants.CHAT_TRANSFER_TITLE, getNameForEmail(contact.getC_name(), "Customer"), locationName));
            	String timeString = TimeZoneUtil.formatDateForTz(new Date(), Constants.DATE_FORMAT_CHAT_TRANSFER_EMAIL, businessDTO.getTimeZoneId());
            	dataMap.put("transferContent", String.format(EmailMessageConstants.CHAT_TRANSFER_CONTENT, getNameForEmail(notificationRequest.getUserDTO().getName(), "User"), timeString));
            	try {
            		nexusEmailService.sendMailV2(notificationRequest.getBizId(), emailDTO, Constants.CHAT_TRANSFER_ALERT, Constants.MESSENGER_ALERT + "_" + messengerContact.getId(),
            				notificationRequest.getBusinessNumber(), dataMap);
            	} catch (Exception e) {
            		log.error("error while pushing email data CHAT_TRANSFER {} to kafka on topic : {}", e.getMessage());
            		return;
            	}
            }else {
                log.info(
                        "sendEmailNotification: No notification email sent as Last responded time {} > lastMessage time {}. MessengerContactId {}, MessageId {}, bizId {} ",
                        messengerContact.getLastResponseAt(), new Date(notificationRequest.getLastMsgTime()),
                        messengerContact.getId(), notificationRequest.getMsgId(), notificationRequest.getBizId());
            }
        }

        private String getEmailSubjectSuffix(List<MessageDocument> messagesFromES) {
        	Integer source = messagesFromES.get(0).getSource();
        	String subjectSuffix = EmailMessageConstants.SUBJECT;
        	if (source != null) {
        		Source msgSource = Source.getValue(source);
        		switch (msgSource) {
        		case WEB_CHAT:
        		case LIVE_CHAT_RECEIVE:
        		case WEB_CHAT_BIRDEYE_PROFILE:
        		case LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE:
                case LIVE_CHAT_SEND:
                case LIVE_CHAT_BIRDEYE_PROFILE_SEND:
        			subjectSuffix = EmailMessageConstants.SUBJECT_WEB_CHAT;
        			break;
        		case FACEBOOK:
        			subjectSuffix = EmailMessageConstants.SUBJECT_FACEBOOK;
        			break;
        		case INSTAGRAM:
        			subjectSuffix = EmailMessageConstants.SUBJECT_INSTAGRAM;
        			break;
        		case EMAIL:
        			subjectSuffix = EmailMessageConstants.SUBJECT_EMAIL;
        			break;
        		case GOOGLE:
        			subjectSuffix = EmailMessageConstants.SUBJECT_GOOGLE;
        			break;
        		case VOICE_CALL:
                case VOICE_CALL_AUTOREPLY:
        			subjectSuffix = EmailMessageConstants.SUBJECT_VOICECALL;
        			break;
        		case APPLE:
        			subjectSuffix = EmailMessageConstants.SUBJECT_APPLE;	
        			break;
        		case SECURE_MESSAGE:
        			subjectSuffix = EmailMessageConstants.SUBJECT_SECURE_MESSAGE;
        			break;
        		case TWITTER:
        			subjectSuffix = EmailMessageConstants.SUBJECT_TWITTER;
        			break;
        		case APPOINTMENT:
        			if (CommunicationDirection.RECEIVE.equals(messagesFromES.get(0).getCommunicationDirection())) {
        				subjectSuffix = EmailMessageConstants.SUBJECT_APPOINTMENT;
        				break;
        			}
                case SMS:
                    subjectSuffix = EmailMessageConstants.SUBJECT_TEXT;
                    break;
				case CUSTOM:
					if (messagesFromES.get(0).getCustomChannel() != null) {
						subjectSuffix = String.format(EmailMessageConstants.SUBJECT_CUSTOM,
								messagesFromES.get(0).getCustomChannel()) + " from %s";
						break;
					}
				case WHATSAPP:
        			subjectSuffix = EmailMessageConstants.SUBJECT_WHATSAPP;
        			break;
        		default:
        			subjectSuffix = EmailMessageConstants.SUBJECT;
        			break;
        		}
        	}
        	return subjectSuffix;
        }

    private String getEmailSubjectSuffixForUnRespondedMessageNotification(List<MessageDocument> messagesFromES) {
        Integer source = messagesFromES.get(0).getSource();
        String subjectSuffix = EmailMessageConstants.SUBJECT;
        if (source != null) {
            Source msgSource = Source.getValue(source);
            switch (msgSource) {
                case WEB_CHAT:
                case LIVE_CHAT_RECEIVE:
                case WEB_CHAT_BIRDEYE_PROFILE:
                case LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE:
                case LIVE_CHAT_SEND:
                case LIVE_CHAT_BIRDEYE_PROFILE_SEND:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_WEB_CHAT;
                    break;
                case FACEBOOK:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_FACEBOOK;
                    break;
                case INSTAGRAM:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_INSTAGRAM;
                    break;
                case EMAIL:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_EMAIL;
                    break;
                case GOOGLE:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_GOOGLE;
                    break;
                case VOICE_CALL:
                case VOICE_CALL_AUTOREPLY:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_VOICECALL;
                    break;
                case APPLE:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_APPLE;
                    break;
                case SECURE_MESSAGE:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_SECURE_MESSAGE;
                    break;
                case TWITTER:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_TWITTER;
                    break;
                case APPOINTMENT:
                    if (CommunicationDirection.RECEIVE.equals(messagesFromES.get(0).getCommunicationDirection())) {
                        subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_APPOINTMENT;
                        break;
                    }
                case SMS:
                    subjectSuffix = EmailMessageConstants.UNRESPONDED_SUBJECT_TEXT;
                    break;
                default:
                    subjectSuffix = EmailMessageConstants.SUBJECT;
                    break;
            }
        }
        return subjectSuffix;
    }

        private void setReplyTo(MessengerGlobalFilter notificationRequest, BusinessDTO businessDTO, EmailDTO emailDTO,
        		ContactDocument contact) {
        	String emailReplyDomain = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("messenger.replyToDomain", "messenger.birdeye.com");
        	if (StringUtils.isNotEmpty(emailReplyDomain) && !"Whitelabel".equals(businessDTO.getAccountType())) {
        		emailDTO.setReplyTo(contact.getC_name() + " <" + notificationRequest.getConversationId() + "@"
        				+ emailReplyDomain + ">");
        	}
        	Integer accountId = businessDTO.getRoutingId();
        	String replyToNotificationAccount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("account.replyToNotification", "717879");
        	if (replyToNotificationAccount != null && replyToNotificationAccount.contains(String.valueOf(accountId))) {
        		emailDTO.setReplyTo("reply-".concat(String.valueOf(contact.getM_c_id())).concat("-").concat(String.valueOf(contact.getB_num()))
        				.concat(emailReplyDomainForNotification));
        	}
        }

        //Only for voicemail
        private List<EmailMessageDocument> prepareVoiceMailMsgFormat
        (List < EmailMessageDocument > emailNotificationMessages,
                Map < String, Object > dataMap){
            if (emailNotificationMessages.size() == 1 &&
                    emailNotificationMessages.get(0).getMediaFiles() != null &&
                    "voicemail".equalsIgnoreCase(emailNotificationMessages.get(0).getMediaFiles().get(0).getA_name())) {
                dataMap.put("callback", true);
                dataMap.put("title", EmailMessageConstants.VOICECALL_TITLE);
            }
            return null;
        }

        private MessageDocument getNotesFromES (MessengerGlobalFilter notificationRequest){
            MessageDocument messageDocument = null;
            MessengerFilter filter = new MessengerFilter();
            Map<String, Object> params = new HashMap<>();
            filter.setAccountId(notificationRequest.getAccountId());
            filter.setStartIndex(0);
            filter.setCount(1);
            filter.setConversationId(notificationRequest.getConversationId());
            params.put("m_id", notificationRequest.getMsgId());
            params.put("msg_type", ControllerUtil.getJsonTextFromObject(Arrays.asList(MessageDocument.MessageType.INTERNAL_NOTES.name())));
            filter.setParams(params);
            filter.setQueryFile(Constants.Elastic.GET_MESSAGES_V1);
            List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(filter);
            if (CollectionUtils.isNotEmpty(messagesFromES)) {
                messageDocument = messagesFromES.get(0);
            }
            return messageDocument;
        }
        private List<EmailMessageDocument> getEmailNotificationMessages
        (List < MessageDocument > messageDocuments, NotificationType notificationType, ContactDocument
        contactDocument, String timeZone){
            List<EmailMessageDocument> emailMessageDocuments = new ArrayList<EmailMessageDocument>();
            MessengerContact messengerContact = messengerContactService.findById(contactDocument.getM_c_id());
            for (MessageDocument messageDocument : messageDocuments) {
                //This check is to filter out auto reply message from unread email notification's
//                if (NotificationType.CHAT.equals(notificationType) && isSendMessage(messageDocument)) {
//                    continue;
//                }
                EmailMessageDocument emailMessageDocument = new EmailMessageDocument();
                emailMessageDocument.setSentON(messageDocument.getCr_date());
                emailMessageDocument.setCreatedDate(MessengerUtil.formatMessageCreatedTime(messageDocument.getCr_date(), timeZone));
                emailMessageDocument.setMsgBody(MessengerUtil.decryptMessage(messageDocument));

                if((messengerContact.getLastResponseAt() == null || new Date(DateUtils.getEpochFrom_YYYY_MM_DD_HH_MM_SS(messageDocument.getCr_date()))
                        .after(messengerContact.getLastResponseAt()))){
                    emailMessageDocument.setNewMessage(true);
                }

                // handled for email notifications formating
                if (StringUtils.isNotBlank(emailMessageDocument.getMsgBody())) {
                    emailMessageDocument.setMsgBody(StringUtils.replace(emailMessageDocument.getMsgBody(), "\n", "<br />"));
                }
                //Voicemail Message changes
                if (StringUtils.isNotBlank(messageDocument.getVoiceMailUrl())) {
                    if (emailMessageDocument.getMsgBody() == null)
                        emailMessageDocument.setMsgBody("Voicemail transcription is not available");

                    MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile("mp3", messageDocument.getVoiceMailUrl(), "-1", "voicemail", "audio/mpeg");
                    List<MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                    emailMessageDocument.setMediaFiles(mediaFiles);
                }
                //Instagram Story
                if (StringUtils.isNotBlank(messageDocument.getStoryMentionUrl())) {
                	if (emailMessageDocument.getMsgBody() == null)
                		emailMessageDocument.setMsgBody("Mentioned you in their story");
                }
                //WhatsApp Contact
                if (MessageDocument.MessageType.WA_CONTACT.equals(messageDocument.getMessageType())) {
                	if (emailMessageDocument.getMsgBody() == null)
                		emailMessageDocument.setMsgBody("Received contact information");
                }
                //WhatsApp Location
                if (MessageDocument.MessageType.WA_LOCATION.equals(messageDocument.getMessageType())) {
                	if (emailMessageDocument.getMsgBody() == null)
                		emailMessageDocument.setMsgBody("Received location information");
                }
                if (Objects.isNull(messageDocument.getMessageType())) {
                    emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(messageDocument.getU_id(), messageDocument.getU_name()));
                    if (StringUtils.isNotBlank(messageDocument.getA_url())) {
                        MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile(messageDocument.getA_ext(), messageDocument.getA_url(), messageDocument.getA_size(), messageDocument.getA_name(), messageDocument.getA_contype());
                        List<MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                        mediaFiles = truncateAttachmentName(mediaFiles);
                        emailMessageDocument.setMediaFiles(mediaFiles);
                    }
                    if (("SMS_RECEIVE".equals(messageDocument.getMsg_type())
                            || "MMS_RECEIVE".equals(messageDocument.getMsg_type()))) {
                        emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                        emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                        emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);
                    }
                    if ("SMS_SEND".equals(messageDocument.getMsg_type())
                            || "MMS_SEND".equals(messageDocument.getMsg_type())) {
                        emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                        emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
                    }
                } else {
                    emailMessageDocument.setMessageType(messageDocument.getMessageType());
                    emailMessageDocument.setCommunicationDirection(messageDocument.getCommunicationDirection());
                    if (CollectionUtils.isNotEmpty(messageDocument.getMediaFiles())) {
                        List<MediaFile> mediaFiles = truncateAttachmentName(messageDocument.getMediaFiles());
                        emailMessageDocument.setMediaFiles(mediaFiles);
                    }
                    emailMessageDocument.setCreatedBy(messageDocument.getCreatedBy());
                    if (MessageDocument.MessageType.CHAT.equals(messageDocument.getMessageType())
                            && MessageDocument.CommunicationDirection.RECEIVE
                            .equals(messageDocument.getCommunicationDirection())) {
                        emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                    }
                }
                emailMessageDocuments.add(emailMessageDocument);
            }
            return emailMessageDocuments;
        }

        public static List<MediaFile> truncateAttachmentName (List < MediaFile > mediaFiles) {
            mediaFiles.forEach(mediaFile -> {
                String attachmentName = mediaFile.getA_name();
                if (StringUtils.isNotBlank(attachmentName)) {
                    int index = attachmentName.indexOf('?');
                    if (index != -1) {
                        attachmentName = attachmentName.substring(0, index);
                    }
                    if (attachmentName.length() > 23) {
                        attachmentName = attachmentName.substring(0, 20);
                        attachmentName = attachmentName + "...";
                    }
                    mediaFile.setA_name(attachmentName);
                }
            });
            return mediaFiles;
        }

        private boolean isSendMessage (MessageDocument messageDocument){
            boolean flag = false;
            String msg_Type = messageDocument.getMsg_type();
            if ("SMS_SEND".equals(msg_Type) || "MMS_SEND".equals(msg_Type) || MessageDocument.CommunicationDirection.SEND.equals(messageDocument.getCommunicationDirection())) {
                flag = true;
            }
            return flag;
        }

        public static String getNameForEmail (String fullName, String defaultName){
            Objects.requireNonNull(defaultName);
            if (StringUtils.isNotBlank(fullName)) {
                if (fullName.startsWith("(") || fullName.startsWith("+")) {
                    return fullName;
                } else {
                    return fullName;
                }
            }
            return defaultName;
        }


		@Override
		public List<Integer> getRemovedConversations(NotificationRequest notificationRequest, Integer userId,
				Integer accountId) {
			ConversationFilter filter = notificationRequest.getFilters();
			List<Integer> renderedConversationIds = notificationRequest.getRenderedConversationIds();
			if (CollectionUtils.isNotEmpty(renderedConversationIds)) {
				ConversationRequest compareConversationRequest = new ConversationRequest();
				compareConversationRequest.setStartIndex(0);
				compareConversationRequest.setCount(500); //Support Top 50 Only
				compareConversationRequest.setMoveConversationToTop(null);
                compareConversationRequest.setWeb(notificationRequest.isWeb());
                compareConversationRequest.setAssignmentType(notificationRequest.getAssignmentType());
                compareConversationRequest.setAccess(notificationRequest.getAccess());

                ConversationFilter filters = compareConversationRequest.getFilters();
                filters.setUnAssigned(filter.getUnAssigned());
                filters.setUnResponded(filter.getUnResponded());
                filters.setSortOrder(filter.getSortOrder());
                filters.setBusinessIds(filter.getBusinessIds());
                filters.setAssignedTo(filter.getAssignedTo());
                filters.setTeamAssignedTo(filter.getTeamAssignedTo());
                filters.setConversationStatus(filter.getConversationStatus());
                filters.setTimePeriodSelected(filter.getTimePeriodSelected());
                filters.setType(filter.getType());
                filters.setSearchTerm(notificationRequest.getFilters().getSearchTerm());
                log.info("getRemovedConversations: compareConversationRequest filter : {}",
						compareConversationRequest);

				ContactFreeMarkerData freeMarkerData = new ContactFreeMarkerData(compareConversationRequest);
				freeMarkerData.setSource("\"m_c_id\"");
				freeMarkerData.setFtlFile(Constants.Elastic.GET_CONVERSATIONS_V3);
				freeMarkerData.setRenderedConversationIds(ControllerUtil.toCommaSeparatedString(renderedConversationIds));
				ElasticData conversationData = messengerContactService.getConversationIdsFromES(freeMarkerData, accountId);

				if (!conversationData.isSucceeded()) {
					log.error("getRemovedConversations: Failed to fetch data from ES");
					throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
				}

				List<ConversationIds> conversationIds = conversationData.getResults();
				log.info("getRemovedConversations:  presentConversationIds : {}", conversationIds);
				List<Integer> removedConversationIds = new ArrayList<>(renderedConversationIds);
				if (CollectionUtils.isNotEmpty(conversationIds)) {
					List<Integer> openConversationIds = conversationIds.stream().map(ConversationIds::getM_c_id).collect(Collectors.toList());
					removedConversationIds.removeAll(openConversationIds);
				}

				log.info("getRemovedConversations:  removedConversationIds : {}", removedConversationIds);
				return removedConversationIds;
			}
			return  null;
		}

		/**
		 * Send Email Notification for Scheduled Job - get applicable emailIds and then send
		 */
		@Override
		public void sendEmailNotificationScheduled(MessengerGlobalFilter notificationRequest,
				MessageDocument messageDocument) {
			log.info("sendEmailNotificationScheduled: Notification request {} accountId {} conversationId {}",
					notificationRequest, notificationRequest.getAccountId(), notificationRequest.getConversationId());
			MessengerContact messengerContact = messengerContactService
					.findById(notificationRequest.getConversationId());
			if (messengerContact == null) {
				log.info("Cannot send Email Notification as messenger contact Id {} does not exist",
						notificationRequest.getConversationId());
				return;
			}
			ContactDocument contact = messengerContactService.getContact(notificationRequest);
            NotificationTypeSourceDTO nts = getNotificationTypeAndSource(notificationRequest, messageDocument);
            if (contact.getAssignmentType() != null
					&& Constants.Assignment.TEAM_ASSIGNED.equalsIgnoreCase(contact.getAssignmentType())
					&& contact.getTeam_id() != null) {
				// get Team applicable emailIds
				Map<String, Map<String, Map<String, String>>> scheduleToMapOfUserIdAndEmailId = businessService
						.getEmailConfigOfTeamsForNewMessage(notificationRequest.getBizId(), contact.getTeam_id(), nts.getNotificationRequestType(), nts.getSource());
				if (MapUtils.isNotEmpty(scheduleToMapOfUserIdAndEmailId)) {
					scheduleToMapOfUserIdAndEmailId.forEach((schedule, userIdToEmailId) -> {
						Set<String> emailIds = emailIdsToNotify(userIdToEmailId.get("userIdEmailIdMap"),
								notificationRequest);
						if (CollectionUtils.isNotEmpty(emailIds)) {
							notificationRequest.setEmailIds(emailIds);
							if (!(schedule.equalsIgnoreCase(Constants.BusinessService.SCHEDULE_INSTANT))) {
								notificationRequest.setMessageForTeam(true);
								sendEmailNotificationV2(notificationRequest, messageDocument);
							}
						}
					});
				} else {
					log.info("No email present to send Email Notification");
				}
			} else {
				// get User applicable emailIds
				Map<String, Map<String, Map<String, Map<String, String>>>> webChatIntervalToUserNotifcations = userService
						.getInboxUserEmailAlertSettings(notificationRequest.getBizId(),nts.getNotificationRequestType(),nts.getSource());
				if (MapUtils.isNotEmpty(webChatIntervalToUserNotifcations)) {
					webChatIntervalToUserNotifcations.forEach((webChatInterval, messengerUserNotificationMap) -> {
						Set<String> emailIds = getSendNotificationEmailIds(messengerUserNotificationMap,
								notificationRequest);
						if (CollectionUtils.isNotEmpty(emailIds)) {
							notificationRequest.setEmailIds(emailIds);
							if (!(webChatInterval.equalsIgnoreCase(Constants.BusinessService.SCHEDULE_INSTANT))) {
								sendEmailNotificationV2(notificationRequest, messageDocument);
							}
						}
					});
				} else {
					log.info("No email present to send Email Notification");
				}
			}
		}

        @Override
        public MessengerNotificationMessage getConversations(NotificationRequest notificationRequest, Integer userId,
                Integer accountId) throws Exception {
            final MessengerNotificationMessage notification = new MessengerNotificationMessage();
            Set<String> fields = CollectionUtils.isNotEmpty(notificationRequest.getFields())
                    ? notificationRequest.getFields()
                    : FetchDeltaResponseType.getFetchDeltaResponseTypeActions();

            if (fields.contains(FetchDeltaResponseType.LAST_MESSAGE_TIME.getStringValue())) {
                notification.setLastMessageTime(new Date().getTime());
            }

            // Reduce lastMessageTime by 1000ms- Message miss issue- EXR
            if (Objects.nonNull(notificationRequest.getFilters().getLastMessageTime())) {
                notificationRequest.getFilters()
                        .setLastMessageTime(notificationRequest.getFilters().getLastMessageTime() - 1000l);
            }
            Set<FetchDeltaResponseService> fetchDeltaResponseServices = fields.stream()
                    .map(field -> getFetchDeltaResponseService(field)).filter(service -> Objects.nonNull(service))
                    .collect(Collectors.toSet());
            final CompletableFuture<?> futures[] = new CompletableFuture[fetchDeltaResponseServices.size()];
            if (CollectionUtils.isNotEmpty(fetchDeltaResponseServices)) {
                AtomicInteger integer = new AtomicInteger();
                fetchDeltaResponseServices.stream().forEach(service -> {
                    futures[integer.getAndIncrement()] = CompletableFuture.runAsync(
                            () -> service.setResponse(notificationRequest, fields, notification, accountId, userId),
                            poolExecutor);
                });
            }
            CompletableFuture.allOf(futures).join();
            return notification;
        }

        private FetchDeltaResponseService getFetchDeltaResponseService(
                String field) {
            return fetchDeltaResponseServiceList.stream()
                    .filter((service) -> service.getFetchDeltaResponseType().contains(field)).findFirst().orElse(null);
        }


    @Override
    public void sendEmailNotificationUnResponded(MessengerGlobalFilter notificationRequest, MessageDocument messageDocument) {
        log.info("sendEmailNotificationUnResponded: Notification request {} accountId {} conversationId {}",
                notificationRequest, notificationRequest.getAccountId(), notificationRequest.getConversationId());

        // Only process CHAT notification type
        if (!MessengerGlobalFilter.NotificationType.CHAT.equals(notificationRequest.getNotificationType())) {
            log.info("sendEmailNotificationUnResponded: Skipping non-CHAT notification type: {}", notificationRequest.getNotificationType());
            return;
        }


        MessengerContact messengerContact = messengerContactService.findById(notificationRequest.getConversationId());
        if (messengerContact == null) {
            log.info("Cannot send Email Notification as messenger contact Id {} does not exist", notificationRequest.getConversationId());
            return;
        }

        // Check if there's been a response after the message time (same dropping condition as existing functions)
        if (messengerContact.getLastResponseAt() != null &&
            !new Date(notificationRequest.getLastMsgTime()).after(messengerContact.getLastResponseAt())) {
            log.info("sendEmailNotificationUnResponded: No notification email sent as Last responded time {} > lastMessage time {}. MessengerContactId {}, MessageId {}, bizId {}",
                    messengerContact.getLastResponseAt(), new Date(notificationRequest.getLastMsgTime()),
                    messengerContact.getId(), notificationRequest.getMsgId(), notificationRequest.getBizId());
            return;
        }

        // Check interval-based notification limiting
        if (!shouldSendNotificationBasedOnInterval(notificationRequest)) {
            log.info("sendEmailNotificationUnResponded: Notification not sent due to interval limiting for conversationId {} bizId {}",
                    notificationRequest.getConversationId(), notificationRequest.getBizId());
            return;
        }

        notificationRequest.setNotificationType(NotificationType.UNRESPONDED);

        ContactDocument contact = messengerContactService.getContact(notificationRequest);
        NotificationTypeSourceDTO nts = getNotificationTypeAndSource(notificationRequest, messageDocument);

        if (contact.getAssignmentType() != null
                && Constants.Assignment.TEAM_ASSIGNED.equalsIgnoreCase(contact.getAssignmentType())
                && contact.getTeam_id() != null) {
            // get Team applicable emailIds
            Map<String, Map<String, Map<String, String>>> scheduleToMapOfUserIdAndEmailId = businessService
                    .getEmailConfigOfTeamsForNewMessage(notificationRequest.getBizId(), contact.getTeam_id(), nts.getNotificationRequestType(), nts.getSource());
            if (MapUtils.isNotEmpty(scheduleToMapOfUserIdAndEmailId)) {
                scheduleToMapOfUserIdAndEmailId.forEach((schedule, userIdToEmailId) -> {
                    Set<String> emailIds = emailIdsToNotify(userIdToEmailId.get("userIdEmailIdMap"), notificationRequest);
                    if (CollectionUtils.isNotEmpty(emailIds) && schedule.equalsIgnoreCase(notificationRequest.getUnrespondedNotificationInterval())) {
                        notificationRequest.setEmailIds(emailIds);
                        notificationRequest.setMessageForTeam(true);
                        sendEmailNotificationV2(notificationRequest, messageDocument);
                        // Update Redis to track that notification was sent
                        updateNotificationSentTime(notificationRequest);
                    }
                });
            } else {
                log.info("No email present to send Email Notification");
            }
        } else {
            // get User applicable emailIds
            //notificationrequest interval-> 15,M unhi users ke liye chalegi
            Map<String, Map<String, Map<String, Map<String, String>>>> webChatIntervalToUserNotifications = userService
                    .getInboxUserEmailAlertSettings(notificationRequest.getBizId(), nts.getNotificationRequestType(), nts.getSource());
            if (MapUtils.isNotEmpty(webChatIntervalToUserNotifications)) {
                webChatIntervalToUserNotifications.forEach((webChatInterval, messengerUserNotificationMap) -> {
                    Set<String> emailIds = getSendNotificationEmailIds(messengerUserNotificationMap, notificationRequest);
                    if (CollectionUtils.isNotEmpty(emailIds) && webChatInterval.equalsIgnoreCase(notificationRequest.getUnrespondedNotificationInterval())) {
                        notificationRequest.setEmailIds(emailIds);
                        sendEmailNotificationV2(notificationRequest, messageDocument);
                        // Update Redis to track that notification was sent
                        updateNotificationSentTime(notificationRequest);
                    }
                });
            } else {
                log.info("No email present to send Email Notification");
            }
        }
    }

    private void sendEmailNotificationForUnRespondedMessages(MessengerGlobalFilter notificationRequest, MessageDocument messageDocument, BusinessDTO businessDTO) {
            log.info("sendEmailNotificationScheduled: Notification request {} accountId {} conversationId {}",
                    notificationRequest, notificationRequest.getAccountId(), notificationRequest.getConversationId());
            MessengerContact messengerContact = messengerContactService
                    .findById(notificationRequest.getConversationId());
            if (messengerContact == null) {
                log.info("Cannot send Email Notification as messenger contact Id {} does not exist",
                        notificationRequest.getConversationId());
                return;
            }
            ContactDocument contact = messengerContactService.getContact(notificationRequest);
            NotificationTypeSourceDTO nts = new NotificationTypeSourceDTO(NotificationRequestType.NOT_RESPONDED.toString(), messageDocument.getSource());
            if (contact.getAssignmentType() != null
                    && Constants.Assignment.TEAM_ASSIGNED.equalsIgnoreCase(contact.getAssignmentType())
                    && contact.getTeam_id() != null) {
                // get Team applicable emailIds
                Map<String, Map<String, Map<String, String>>> scheduleToMapOfUserIdAndEmailId = businessService
                        .getEmailConfigOfTeamsForNewMessage(notificationRequest.getBizId(), contact.getTeam_id(), nts.getNotificationRequestType(), nts.getSource());
                if (MapUtils.isNotEmpty(scheduleToMapOfUserIdAndEmailId)) {
                    scheduleToMapOfUserIdAndEmailId.forEach((schedule, userIdToEmailId) -> {
                        Set<String> emailIds = emailIdsToNotify(userIdToEmailId.get("userIdEmailIdMap"),
                                notificationRequest);
                        if (CollectionUtils.isNotEmpty(emailIds)) {
                            notificationRequest.setEmailIds(emailIds);
                                String notificationTriggerTime = null;
                                try {
                                    notificationTriggerTime = MessengerUtil.parseEmailSendTime(businessDTO.getTimeZoneId(), schedule);
                                    notificationRequest.setNotificationTriggerTime(notificationTriggerTime);
                                    scheduleJobForNotificationUnRespondedMessages(notificationRequest,schedule);
                                } catch (ParseException | NumberFormatException e) {
                                    log.error(
                                            "processUnreadMessageNotification: webChat Interval or Format {} is incorrect for business {} and user emailId {} ",
                                            schedule, businessDTO.getBusinessId(), emailIds);
                                }
                            }
                    });
                } else {
                    log.info("No email present to send Email Notification");
                }
            } else {
                // get User applicable emailIds
                Map<String, Map<String, Map<String, Map<String, String>>>> webChatIntervalToUserNotifcations = userService
                        .getInboxUserEmailAlertSettings(notificationRequest.getBizId(),nts.getNotificationRequestType(),nts.getSource());
                if (MapUtils.isNotEmpty(webChatIntervalToUserNotifcations)) {
                    webChatIntervalToUserNotifcations.forEach((webChatInterval, messengerUserNotificationMap) -> {
                        Set<String> emailIds = getSendNotificationEmailIds(messengerUserNotificationMap,
                                notificationRequest);
                        if (CollectionUtils.isNotEmpty(emailIds)) {
                            notificationRequest.setEmailIds(emailIds);
                                String notificationTriggerTime = null;
                                try {
                                    notificationTriggerTime = MessengerUtil.parseEmailSendTime(businessDTO.getTimeZoneId(), webChatInterval);
                                    notificationRequest.setNotificationTriggerTime(notificationTriggerTime);
                                    scheduleJobForNotificationUnRespondedMessages(notificationRequest,webChatInterval);
                                } catch (ParseException | NumberFormatException e) {
                                    log.error(
                                            "processUnreadMessageNotification: webChat Interval or Format {} is incorrect for business {} and user emailId {} ",
                                            webChatInterval, businessDTO.getBusinessId(), emailIds);
                                }
                        }
                    });
                } else {
                    log.info("No email present to send Email Notification");
                }
            }
    }

    public void scheduleJobForNotificationUnRespondedMessages(MessengerGlobalFilter notificationRequest, String Interval) {
        String redisKey = "INBOX_UNRESPONDED_JOB:" + notificationRequest.getBizId()+ "_" +notificationRequest.getConversationId();
        //Find scheduled job in cache, if job exists for businessId and conversationId, Ignore this event.
        if (true) {
//            redisHandler.setOpsForValueWithExpiry(redisKey, Constants.INBOX_UNRESPONDED_MESSAGES_NOTIFICATION_JOB, 5l, TimeUnit.MINUTES);
            notificationRequest.setUnrespondedNotificationInterval(Interval);
            samayService.publishToScheduler(notificationRequest,Long.parseLong(notificationRequest.getNotificationTriggerTime()), KafkaTopicEnum.EMAIL_NOTIFICATION_UNRESPONDED_MESSAGES_QUEUE);
        } else {
            log.info("Scheduled job already present for businessId {} and conversationId {}", notificationRequest.getBizId(), notificationRequest.getConversationId());
        }
    }

    /**
     * Check if notification should be sent based on the configured interval.
     * This prevents sending multiple notifications within the specified interval.
     */
    private boolean shouldSendNotificationBasedOnInterval(MessengerGlobalFilter notificationRequest) {
        String interval = notificationRequest.getUnrespondedNotificationInterval();
        if (StringUtils.isBlank(interval)) {
            // If no interval is configured, allow notification
            return true;
        }

        // Parse interval (e.g., "15,M" -> 15 minutes)
        long intervalMinutes = parseIntervalToMinutes(interval);
        notificationRequest.setInterval(String.valueOf(intervalMinutes));
        if (intervalMinutes <= 0) {
            log.warn("Invalid interval format: {}, allowing notification", interval);
            return true;
        }

        // Check if we have sent a notification within this interval
        String redisKey = Constants.UNRESPONDED_NOTIFICATION_INTERVAL_KEY + ":" + notificationRequest.getBizId() + "_" + notificationRequest.getConversationId() + "_" + intervalMinutes;
        String lastNotificationTime = redisHandler.getKeyValueFromRedis(redisKey);

        if (StringUtils.isNotBlank(lastNotificationTime)) {
            try {
                long lastNotificationTimestamp = Long.parseLong(lastNotificationTime);
                long currentTime = System.currentTimeMillis();
                long timeDifferenceMinutes = (currentTime - lastNotificationTimestamp) / (60 * 1000);

                if (timeDifferenceMinutes < intervalMinutes) {
                    log.info("shouldSendNotificationBasedOnInterval: Notification blocked. Last notification was {} minutes ago, interval is {} minutes",
                            timeDifferenceMinutes, intervalMinutes);
                    return false;
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid timestamp in Redis for key {}: {}", redisKey, lastNotificationTime);
            }
        }

        return true;
    }

    /**
     * Update Redis to track when notification was sent for interval limiting
     */
    private void updateNotificationSentTime(MessengerGlobalFilter notificationRequest) {
        String interval = notificationRequest.getUnrespondedNotificationInterval();
        if (StringUtils.isBlank(interval)) {
            return;
        }

        long intervalMinutes = parseIntervalToMinutes(interval);
        if (intervalMinutes > 0) {
            String redisKey = Constants.UNRESPONDED_NOTIFICATION_INTERVAL_KEY + ":" + notificationRequest.getBizId() + "_" + notificationRequest.getConversationId() + "_" + intervalMinutes;
            String currentTimestamp = String.valueOf(System.currentTimeMillis());

            // Set expiry slightly longer than the interval to ensure proper cleanup
            long expiryMinutes = intervalMinutes;
            redisHandler.setOpsForValueWithExpiry(redisKey, currentTimestamp, expiryMinutes, TimeUnit.MINUTES);

            log.info("updateNotificationSentTime: Updated notification time for conversationId {} bizId {} with expiry {} minutes",
                    notificationRequest.getConversationId(), notificationRequest.getBizId(), expiryMinutes);
        }
    }

    /**
     * Parse interval string to minutes (e.g., "15,M" -> 15, "2,H" -> 120)
     */
    private long parseIntervalToMinutes(String interval) {
        if (StringUtils.isBlank(interval) || !interval.contains(",")) {
            return 0;
        }

        try {
            String[] parts = interval.split(",");
            if (parts.length != 2) {
                return 0;
            }

            long value = Long.parseLong(parts[0].trim());
            String unit = parts[1].trim().toUpperCase();

            switch (unit) {
                case "M":
                    return value;
                case "H":
                    return value * 60;
                default:
                    log.warn("Unknown interval unit: {}", unit);
                    return 0;
            }
        } catch (NumberFormatException e) {
            log.warn("Invalid interval format: {}", interval);
            return 0;
        }
    }

    private NotificationTypeSourceDTO getNotificationTypeAndSource(
            MessengerGlobalFilter notificationRequest,
            MessageDocument messageDocument) {

        if (MessengerGlobalFilter.NotificationType.NOTE.equals(notificationRequest.getNotificationType())) {
            return new NotificationTypeSourceDTO(
                    NotificationRequestType.INTERNAL_NOTE_ADDED.toString(),
                    messageDocument.getSource()
            );
        } else if (MessengerGlobalFilter.NotificationType.CHAT.equals(notificationRequest.getNotificationType())) {
            return new NotificationTypeSourceDTO(
                    NotificationRequestType.NEW_MESSAGE.toString(),
                    messageDocument.getSource()
            );
        } else if (NotificationType.ASSIGNED_TO.equals(notificationRequest.getNotificationType())) {
            return new NotificationTypeSourceDTO(
                    NotificationRequestType.ASSIGNED_TO_CONVERSATION.toString(),
                    messageDocument.getSource()
            );
        } else if (NotificationType.UNRESPONDED.equals(notificationRequest.getNotificationType())) {
            return new NotificationTypeSourceDTO(
                    NotificationRequestType.NOT_RESPONDED.toString(),
                    messageDocument.getSource()
            );
        }
        return null;
    }

    private void setEmailSubject(EmailDTO emailDTO, MessengerGlobalFilter notificationRequest, List<MessageDocument> messagesFromES) {
        String sourceType = getSourceType(messagesFromES);
        //need to handle you issue
        if (notificationRequest.isMessageForTeam()) {
            emailDTO.setSubject(sourceType != null ?
                    String.format(EmailMessageConstants.TEAM_ASSIGNED_TO_SUBJECT, sourceType) :
                    EmailMessageConstants.DEFAULT_TEAM_ASSIGNED_TO_SUBJECT);
        } else if(Boolean.TRUE.equals(notificationRequest.isAssignToOthers())) {
            emailDTO.setSubject(sourceType != null ?
                    String.format(EmailMessageConstants.ASSIGNED_TO_OTHERS_SUBJECT, sourceType) :
                    EmailMessageConstants.DEFAULT_ASSIGNED_TO_OTHERS_SUBJECT);
        }
        else if (Boolean.TRUE.equals(notificationRequest.isUnassign())) {
            emailDTO.setSubject(sourceType != null ?
                    String.format(EmailMessageConstants.UNASSIGNED_TO_SUBJECT, sourceType) :
                    EmailMessageConstants.DEFAULT_UNASSIGNED_TO_SUBJECT);
        } else {
            emailDTO.setSubject(sourceType != null ?
                    String.format(EmailMessageConstants.ASSIGNED_TO_SUBJECT, sourceType) :
                    EmailMessageConstants.DEFAULT_ASSIGNED_TO_SUBJECT);
        }
    }

    private String getSourceType(List<MessageDocument> messagesFromES) {
        if (CollectionUtils.isEmpty(messagesFromES)) {
            return null;
        }

        for (MessageDocument message : messagesFromES) {
            if (message != null &&
                    message.getSource() != null &&
                    message.getSource() != 0) {

                return getSourceString(message.getSource());
            }
        }

        return null; // No valid source found in any message
    }

    private String getSourceString(Integer sourceId){
        Source source = Source.getValue(sourceId);
        if (source != null) {
            switch (source) {
                case WEB_CHAT:
                case LIVE_CHAT_RECEIVE:
                case WEB_CHAT_BIRDEYE_PROFILE:
                case LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE:
                case LIVE_CHAT_SEND:
                case LIVE_CHAT_BIRDEYE_PROFILE_SEND:
                    return "chatbot";
                case SMS:
                    return "text";
                case VOICE_CALL:
                case VOICE_CALL_AUTOREPLY:
                    return "Voicecall";
                default:
                    return source.name().toLowerCase();
            }
        }
        return null;
    }



}
