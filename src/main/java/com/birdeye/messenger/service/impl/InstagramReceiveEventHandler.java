package com.birdeye.messenger.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.facebook.Attachments;
import com.birdeye.messenger.dto.instagram.GetInstagramDetailsMessage;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.instagram.InstagramUserDetailsMessage;
import com.birdeye.messenger.dto.instagram.Messaging;
import com.birdeye.messenger.enums.InstagramMessageStatusEnum;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.SUPPORTED_UPLOAD_FILES;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.service.BusinessServiceImpl;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.InstagramEventService;
import com.birdeye.messenger.service.InstagramMessageService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class InstagramReceiveEventHandler extends MessageEventHandlerAbstract{
	
	private final MessengerEvent EVENT = MessengerEvent.INSTAGRAM_MSG_RECEIVE;

	@Autowired
    protected MessengerContactRepository messengerContactRepository;
	
	@Autowired
	protected SocialService socialService;
	
	@Autowired
    protected InstagramEventService instagramEventService;
	
	@Autowired
	private ContactService contactService;
	
	@Autowired
	private BusinessServiceImpl businessServiceImpl;
	
	@Autowired
	private MessengerMessageService messengerMessageService;
	
	@Autowired
	private MessengerMediaFileService messengerMediaFileService;
	
	@Autowired
	private PulseSurveyService pulseSurveyService;

	@Autowired
	private RedisLockService redisLockService;
	
	@Autowired
    private SpamDetectionService spamDetectionService;
	
	@Autowired
	private InstagramMessageService instagramMessageService;

	@Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
        	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
        	Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
        	String businessInstagramId;
        	if (msg.getMessage() !=null && msg.getMessage().getIs_echo()) {
        		businessInstagramId = msg.getSender().getId();
        	}else {
				businessInstagramId = msg.getRecipient().getId();
			}
            Integer businessId = instagramEventService.getBusinessIdByInstagramPageId(businessInstagramId);
            if(businessId!=null) {
            	businessDTO = communicationHelperService.getBusinessDTO(businessId);	
			} else {
				businessDTO = null;
				log.error("[Instagram Receive Event]: No valid business mapping found for instagram page: {} ",
						businessInstagramId);
			}
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }
    
    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
    	InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
    	Messaging messaging = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
    	CustomerDTO customerDTO = messageDTO.getCustomerDTO();
    	if (Objects.isNull(customerDTO)) {
    		MessengerContact contact= getMessengerContact(messageDTO);
    		if(contact.getCustomerId()!=null && Objects.isNull(messageDTO.getCustomerDTO())) {
    			customerDTO = contactService.findById(contact.getCustomerId());;
    			messageDTO.setCustomerDTO(customerDTO);
    		}
    		if (Objects.isNull(customerDTO)) {
            	customerDTO = messageDTO.getCustomerDTO();
            }
    		MessageTag messageTag = getMessageTag(messageDTO);
    		if (messaging.getMessage() != null && !messaging.getMessage().getIs_echo()) {
    			spamDetectionService.spamDetectionAllChannels(messageDTO, contact, customerDTO, messageTag);
        	}
    	}
    	return customerDTO;
    }
    
    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
    	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
    	Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
		if(msg.getMessage()!=null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
			return MessageTag.INBOX;
		}
		else if (dto.getSendEmailNotification()) {
            return MessageTag.UNREAD;
        } else {
        	return MessageTag.INBOX;
        }
    }
    /*
     * Setting reciepientId as from and senderId as To bcz it is being set this way in Platform.
     * Fixing this would require additional migration
     * Data is correctly persisted in DB
     *  
     */
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
    	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
    	MessageDocumentDTO messageDocumentDTO=new  MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
    	messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
    	messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
		if(StringUtils.isNotBlank(messageDTO.getStoryReplyUrl())){
			messageDocumentDTO.setStoryReplyUrl(messageDTO.getStoryReplyUrl());
		}
		if(StringUtils.isNotBlank(messageDTO.getStoryMentionUrl())){
			messageDocumentDTO.setStoryMentionUrl(messageDTO.getStoryMentionUrl());
		}
    	messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		if( messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)){
			messageDocumentDTO.setSpam(true);
		}else{
			messageDocumentDTO.setSpam(false);
		}
        return messageDocumentDTO;
    }
	

    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
    	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setMsgId(dto.getConversationDTO().getId());
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        if (dto.getConversationDTO().getCreateDate() != null) {
            notificationRequest.setLastMsgTime(dto.getConversationDTO().getCreateDate().getTime());
        } else {
            notificationRequest.setLastMsgTime(new Date().getTime());
            log.info("onReceiveSMS: Both sms sentOn and createDate found null for businessId {} smsID {} customer {}", businessDTO.getBusinessId(), dto.getConversationDTO().getId(), dto.getCustomerDTO().getPhone());
        }
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }
    
	@Override
	public MessengerEvent getEvent() {
		 return EVENT;
	}

    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.INSTAGRAM.name());
		lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.INSTAGRAM.getSourceId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.INSTAGRAM.getSourceId());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		lastMessageMetadataPOJO.setLastIgReceivedAt(sdf.format(new Date()));
		InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
		Messaging messaging = dto.getEntry().get(0).getMessaging().get(0);
		Date lastMsgOn = new Date();
		String date = sdf.format(lastMsgOn);
		if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {
			lastMessageMetadataPOJO.setLastIgSentAt(date);
		} else {
			lastMessageMetadataPOJO.setLastIgReceivedAt(date);
		}
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		//TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
        /*
		 * LastMessageMetaData lastMessageMetaData =
		 * MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		 * lastMessageMetaData.setLastMessageSource(Source.FACEBOOK.getSourceId());
		 * lastMessageMetaData.setLastReceivedMessageSource(Source.FACEBOOK.getSourceId(
		 * )); SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 * lastMessageMetaData.setLastFbSentAt(sdf.format(new Date()));
		 * messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(
		 * lastMessageMetaData));
		 */
    	if (messaging.getMessage() !=null && messaging.getMessage().getIs_echo()) {
    		messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
    	} 
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
		messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
    }

    @Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
		Messaging msg = dto.getEntry().get(0).getMessaging().get(0);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		if (StringUtils.isEmpty(dto.getConversationDTO().getBody())
				&& StringUtils.isNotEmpty(dto.getConversationDTO().getMediaUrl())) {
			messengerContact.setLastMessage("Received an attachment");
			if (msg.getMessage() != null && msg.getMessage().getIs_echo()) {
				messengerContact.setLastMessage("Sent an attachment");
			}
		} else {
//            messengerContact.setLastMessage(dto.getConversationDTO().getBody());
		}
		if(StringUtils.isNotBlank(messageDTO.getStoryMentionUrl())){
			messengerContact.setLastMessage("Mentioned you in their story");
		}
		if(StringUtils.isNotBlank(messageDTO.getStoryReplyUrl())){
			messengerContact.setLastMessage("Replied to your story");
		}
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact,
				dto.getConversationDTO().getEncrypted(), messengerContact.getInstagramConversationId(),
				businessDTO.getBusinessNumber());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messageDTO.getMessengerContact())) {
        	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
        	Messaging messaging = dto.getEntry().get(0).getMessaging().get(0);
        	String customerInstagramId;
			String pageId = messaging.getRecipient().getId();
			String userId = messaging.getSender().getId();
        	if (messaging.getMessage() !=null && messaging.getMessage().getIs_echo()) {
        		customerInstagramId=messaging.getRecipient().getId();
				pageId = messaging.getSender().getId();
        	}else {
        		customerInstagramId = messaging.getSender().getId();
				pageId = messaging.getRecipient().getId();
        	}
        	userId = customerInstagramId;
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            messengerContact = messengerContactRepository.findByInstagramConversationId(customerInstagramId, businessDTO.getBusinessId());
    		if(messengerContact==null) {
    			 // not found in db then create new
    			 //	receive event make new messenger contact
    				log.info("fetching user details from social for instagram user Id {}: ",userId);
    				InstagramUserDetailsMessage instagramUserDetailsMessage=socialService.getInstagramUserInfo(new GetInstagramDetailsMessage(userId,pageId));
    				//Name all user's as Annonymous user if IG return null value for name.
    				if(instagramUserDetailsMessage!=null && StringUtils.isBlank(instagramUserDetailsMessage.getName())) {
    					instagramUserDetailsMessage.setName(Constants.ANONYMOUS_USER);
    				}
    				KontactoRequest kontactoRequest=createKontactoRequest(instagramUserDetailsMessage,userId,businessDTO.getCountryCode());
    				kontactoRequest.setBusinessId(businessDTO.getBusinessId());
    				log.info("Requesting contact service to get/create customer for instagram user Id {}",userId);
    				CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, businessDTO.getRoutingId(),
    						-8);
    				log.info("Customer retrieved Id {} from contact service for instagram user Id {}",customerDTO.getId(),userId);
    				
    				messageDTO.setCustomerDTO(customerDTO);
    				messengerContact=getOrCreateMessengerContactFromInstagram(instagramUserDetailsMessage,
    						userId,messageDTO);
    		}
    		// Messenger Contact reference for FB Message
    		messageDTO.setMessengerContact(messengerContact);
        }
		
        return messengerContact;
    }
    private KontactoRequest createKontactoRequest(InstagramUserDetailsMessage instagramUserDetailsMessage,String instagramUserId,String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String custName = ControllerUtil.truncateLongContactName(instagramUserDetailsMessage.getName());
		kontactoRequest.setName(custName);
		kontactoRequest.setEmailId(instagramUserId+"@ig.com");
		kontactoRequest.setSource(KontactoRequest.INSTAGRAM);
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}
    @Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
        	InstagramMessageRequest dto = (InstagramMessageRequest) messageDTO;
            messageId = dto.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }
    
    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
    	messageDTO.setMsgTypeForResTimeCalc("R"); // default - since its a receive handler. See processFBReceivedMessage() for actual type
		messageDTO.setSource(Source.INSTAGRAM.getSourceId());
    	InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
    	log.info("Instagram recieve request body: {} ",instagramMessageRequest);
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		if (instagramMessageRequest.getEntry().get(0).getMessaging()==null || 
    			instagramMessageRequest.getEntry().get(0).getMessaging().get(0) == null || instagramMessageRequest.getEntry().get(0).getMessaging().get(0).getMessage()==null) {
			log.info("Unsupported event for IG recieve checking for reaction");
			instagramMessageService.checkAndPushLikeUnlikeEvent(messageDTO,businessDTO);
    		return null;
    	}
    	Messaging messaging = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
    	if (messaging.getRead() != null || isUnsupportedWebhookEvent(messaging)) {
    		log.info("Ignoring seen/unsupported event for IG receive ");
    		return null;
    	}
		isStoryMentionOrReply(messageDTO);
    	if(Objects.isNull(businessDTO)) {
    		return null;
    	}
    	Integer isMessengerEnabled = businessServiceImpl.isMessengerEnabled(messageDTO.getBusinessDTO().getAccountId());
        if (!Integer.valueOf(0).equals(isMessengerEnabled)) {
    		Optional<Lock> lockOpt = Optional.empty();
    		try {
    			String instaUserId = messaging.getSender().getId();
    			InstagramMessageStatusEnum instagramMessageStatus= InstagramMessageStatusEnum.received;
    			if (messaging.getMessage() != null && messaging.getMessage().getIs_echo()) {
    				instaUserId = messaging.getRecipient().getId();
    				instagramMessageStatus= InstagramMessageStatusEnum.sent;
    			}
    			
    			String lockKey = Constants.INSTA_USER_ID_PREFIX + instaUserId;
    			lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
    			if (!lockOpt.isPresent()) {
    				log.info("Lock is already acquired for the key:{}", lockKey);
    				kafkaService.publishToKafkaAsync(KafkaTopicEnum.INSTAGRAM_RECEIVE_EVENT_DELAYED_QUEUE,
    						instagramMessageRequest);
    				return null;
    			}
    		
    			
    			if (messageDTO.getBusinessDTO() != null && messageDTO.getBusinessDTO().getBusinessId() != null && BusinessDTO.isActive(messageDTO.getBusinessDTO())) {
    				messageDTO.setBusinessDTO(businessDTO);
    			}else {
    				log.error("BusinessDTO returned is null or inactive by core-service for request {} ", instagramMessageRequest);
    				return null;
    			}
    			CustomerDTO customerDTO = getCustomerDTO(messageDTO);
    			if(null != messaging.getMessage() && CollectionUtils.isNotEmpty(messaging.getMessage().getAttachments())) {
    				// Event with attachment
    				List<Attachments> attachments =messaging.getMessage().getAttachments();
    				for(Attachments attachment: attachments) {
    					if(!"image".equalsIgnoreCase(attachment.getType())){
    						log.info("Attachment is not of type image checking story mention hence returning...");
							if(Constants.STORY_MENTION.equalsIgnoreCase(attachment.getType())){
								processIGReceivedMessage(messageDTO, instagramMessageStatus);
							}
    						continue;
    					}
    					MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(new MessengerMediaFile(attachment));
    					updateMessengerMediaFIleDTO(messengerMediaFileDTO);
    					messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
    					// Either MediaFile or Text must be present to proceed.
    					if (messageDTO.getMessengerMediaFileDTO() != null || StringUtils.isNotBlank(messaging.getMessage().getText()))
    						processIGReceivedMessage(messageDTO, instagramMessageStatus);
    				}
    			} else {
    				processIGReceivedMessage(messageDTO, instagramMessageStatus);
    			}
    			if(instagramMessageRequest.getConversationDTO()==null || instagramMessageRequest.getConversationDTO().getId()==null) {
    				log.error("Error in saving instagram message for request {} ", instagramMessageRequest);
    				return null;
    			}
    			
    			// handle PulseSurveyContext
    			PulseSurveyContext context = null;
    			try {
    				context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, messageDTO.getBusinessDTO());
    				if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
    					customerDTO.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
    				} else {
    					customerDTO.setOngoingPulseSurvey(false);
    				}
    				messageDTO.setCustomerDTO(customerDTO);
    			} catch (Exception ex) {
    				log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
    			}
    			super.handle(messageDTO);
    		} finally {
    			if (lockOpt.isPresent()) {
    				redisLockService.unlock(lockOpt.get());
    			}
    		}
    	}else {
    		log.info("Message Discarded. Messenger event received but messenger in not enabled for business {}", messageDTO.getBusinessDTO().getBusinessId());
    	}
    	return null;
    }

	private boolean isUnsupportedWebhookEvent(Messaging messaging) {
		boolean isUnsupportedWebhookEvent=false;
		if(messaging.getMessage()!=null && (BooleanUtils.isTrue(messaging.getMessage().getIs_deleted()) || BooleanUtils.isTrue(messaging.getMessage().getIs_unsupported()))){
			isUnsupportedWebhookEvent=true;
		}
		return isUnsupportedWebhookEvent;
	}

	private void updateMessengerMediaFIleDTO(MessengerMediaFileDTO messengerMediaFileDTO) {
    	for (SUPPORTED_UPLOAD_FILES filetype : SUPPORTED_UPLOAD_FILES.values()) {
        	if(messengerMediaFileDTO.getUrl() != null && messengerMediaFileDTO.getUrl().contains(filetype.getExtension())){
        		messengerMediaFileDTO.setFileExtension(filetype.getExtension());
        		if (messengerMediaFileDTO.getFileExtension().equalsIgnoreCase("mp4")){
        			messengerMediaFileDTO.setContentType("video/mp4");
        			break;
        		} else {
        			File file = new File(messengerMediaFileDTO.getUrl());
        			URLConnection connection = null;
        			try {
        				connection = file.toURL().openConnection();
        			} catch (IOException e) {
        				log.error("Error in obtaining file mime type for received attachment. Error message : {}", e.getMessage());
        			}
        			String mimeType = connection.getContentType();
        			messengerMediaFileDTO.setContentType(mimeType);
        			messengerMediaFileDTO.setContentSize(String.valueOf(connection.getContentLength()));
        			break;
        		}
        	} else {
        		messengerMediaFileDTO.setFileExtension("others");
        	}
        }
   }

	private MessengerContact getOrCreateMessengerContactFromInstagram(InstagramUserDetailsMessage instagramUserDetailsMessage,String userId,MessageDTO messageDTO) {
		// get instagram user details
		MessengerContact messengerContact = messengerContactRepository.findByFacebookId(userId,
				messageDTO.getBusinessDTO().getBusinessId());
		if (Objects.isNull(messengerContact)) {
			messengerContact = new MessengerContact();
			messengerContact.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
			messengerContact.setInstagramConversationId(userId);
			messengerContact.setImageUrl(instagramUserDetailsMessage.getProfile_pic());
			messengerContact.setCustomerId(messageDTO.getCustomerDTO().getId());
			messengerContact.setCreatedAt(new Date());
			messengerContactRepository.saveAndFlush(messengerContact);
		}
		return messengerContact;
    }
    
	private MessageDTO processIGReceivedMessage(MessageDTO messageDTO,InstagramMessageStatusEnum instagramMessageStatus) throws Exception {
    	if(InstagramMessageStatusEnum.sent.equals(instagramMessageStatus)) messageDTO.setMsgTypeForResTimeCalc("S");
		InstagramMessageRequest instagramMessageRequest=(InstagramMessageRequest) messageDTO;
		Messaging messaging = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
				messageDTO.setMessengerContact(getMessengerContact(messageDTO));
				InstagramMessage instagramMessage=instagramEventService.saveInstagramMessage(messageDTO,messaging.getSender().getId(),messaging.getRecipient().getId(), messaging.getMessage().getMid(), instagramMessageStatus);
				log.debug("Instagram message Id:{} for Messenger contact Id {}",instagramMessage.getId(),messageDTO.getMessengerContact().getId());
				//Don't allow to proceed further if is_echo is true and message is send from Business dashboard
				if(instagramMessage.getId()!=null) {
					MessengerMessageMetaData messageMetaData=new MessengerMessageMetaData();
					messageMetaData.setCommunicationDirection(CommunicationDirection.RECEIVE);
					if(InstagramMessageStatusEnum.sent.equals(instagramMessageStatus)) {
						messageMetaData.setCommunicationDirection(CommunicationDirection.SEND);
					}
					ConversationDTO conversationDTO = new ConversationDTO(instagramMessage,messageMetaData);
					instagramMessageRequest.setConversationDTO(conversationDTO);
					messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), instagramMessage.getId());
					messengerMessageService.saveMessengerMessage(conversationDTO, null);
					messageDTO.setSendEmailNotification(true);
					if(InstagramMessageStatusEnum.sent.equals(instagramMessageStatus))
						messageDTO.setSendEmailNotification(false);
					return messageDTO;
				}
		return null;
	}

    UserDTO getUserDTO(MessageDTO messageDTO) {
    	UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
        	InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
        	Messaging msg = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
			if(msg.getMessage()!=null && BooleanUtils.isTrue(msg.getMessage().getIs_echo())) {
	        	userDTO = communicationHelperService.getUserDTO(Constants.INSTAGRAM_DUMMY_USER);
	        	instagramMessageRequest.setUserDTO(userDTO);
			}
        }
        return userDTO;
    }

	@Override
	void publishEvent(MessageDTO messageDTO) {
		// Do Nothing
	}

	private void isStoryMentionOrReply(MessageDTO messageDTO) {
		InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
		Messaging messaging = instagramMessageRequest.getEntry().get(0).getMessaging().get(0);
		if(Objects.nonNull(messaging.getMessage()) && (Objects.nonNull(messaging.getMessage().getReply_to()) && Objects.nonNull(messaging.getMessage().getReply_to().getStory()))){
			messageDTO.setStoryReplyUrl(messaging.getMessage().getReply_to().getStory().getUrl());
		}
		if(Objects.nonNull(messaging.getMessage()) && CollectionUtils.isNotEmpty(messaging.getMessage().getAttachments()) &&
				Constants.STORY_MENTION.equals(messaging.getMessage().getAttachments().get(0).getType()) && Objects.nonNull(messaging.getMessage().getAttachments().get(0).getPayload())){
			messageDTO.setStoryMentionUrl(messaging.getMessage().getAttachments().get(0).getPayload().getUrl());
		}

	}

}