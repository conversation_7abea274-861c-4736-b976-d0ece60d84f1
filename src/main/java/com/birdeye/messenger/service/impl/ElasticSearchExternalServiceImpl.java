package com.birdeye.messenger.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import net.logstash.logback.argument.StructuredArguments;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest.OpType;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.ClearScrollResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.tasks.TaskSubmissionResponse;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.LoggingDeprecationHandler;
import org.elasticsearch.common.xcontent.NamedXContentRegistry;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.common.xcontent.XContentParser;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.get.GetResult;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.ReindexRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.SearchModule;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.SortBuilder;
import org.jboss.logging.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.birdeye.messenger.annotations.TrackExecutionTime;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ESRequest.Builder;
import com.birdeye.messenger.dto.ESRequest.Upsert;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.Identifiable;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocumentTemp;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.es.sro.ESInsertRequest;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.es.sro.ElasticUpdateQueryRequest;
import com.birdeye.messenger.es.sro.ElasticUpdateQueryRequest.SmartInboxOwnerBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FreemarkerTemplateService;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ElasticSearchExternalServiceImpl implements ElasticSearchExternalService {

	private final RestHighLevelClient client;
	private final ObjectMapper objectMapper;

	private final String ES_INDEX = Constants.ES_INDEX;

	@Autowired
	private FreemarkerTemplateService freemarkerTemplateService;

	@Override
	public <T> List<T> searchByQuery(ESRequest esRequest, Class<T> documentType) {
		List<T> resultDocuments = new ArrayList<>();
		SearchResponse searchResponse = getSearchResult(esRequest);
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> resultDocuments
						.add(objectMapper.convertValue(hit.getSourceAsMap(), documentType)));
			}
		}
		return resultDocuments;
	}

	@Override
	public SearchResponse searchConversations(String query, String routing) {
		ESRequest esRequest = createElasticRequestForQueryExecution(query, routing, Constants.Elastic.CONTACT_INDEX);
		return getESSearchResultForQuery(esRequest);
	}

	@Override
	public SearchResponse searchMessages(String query, String routing) {
		ESRequest esRequest = createElasticRequestForQueryExecution(query, routing, Constants.Elastic.MESSAGE_INDEX);
		return getESSearchResultForQuery(esRequest);
	}

	private ESRequest createElasticRequestForQueryExecution(String query, String routing, String index) {
		Builder baseRequestBuilder = new ESRequest.Builder(new ESRequest()).addIndex(index)
				.addTemplateAndDataModel(query, null);

		if (routing != null) {
			baseRequestBuilder.addRoutingId(Integer.parseInt(routing));
		}
		return baseRequestBuilder.build();
	}

	@Override
	public <T> ElasticData getDataFromElastic(ESRequest esRequest, Class<T> documentType) {
		SearchResponse searchResponse = getSearchResult(esRequest);
		ElasticData<T> elasticData = new ElasticData<>();
		List<T> resultDocuments = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			elasticData.setSearchResponse(searchResponse);
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> resultDocuments
						.add(objectMapper.convertValue(hit.getSourceAsMap(), documentType)));
			}

			elasticData.setTotal(searchResponse.getHits().getTotalHits().value);
			elasticData.setResults(resultDocuments);
			elasticData.setSucceeded(true);
		}
		return elasticData;
	}

	@Override
	public <T> ElasticData getDataFromElasticWithInnerHits(ESRequest esRequest, Class<T> documentType) {
		SearchResponse searchResponse = getSearchResult(esRequest);

		ElasticData<T> elasticData = new ElasticData<>();
		List<T> resultDocuments = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> {
					Map<String, SearchHits> innerHitsMap = hit.getInnerHits();
					ContactDocument doc = (ContactDocument) objectMapper.convertValue(hit.getSourceAsMap(),
							documentType);
					if (!Objects.isNull(innerHitsMap)) {
						SearchHits innerReviews = innerHitsMap.get("reviews");
						SearchHits innerSurveys = innerHitsMap.get("surveyResponses");
						SearchHits innerPayments = innerHitsMap.get("payments");
						SearchHits innerAppointments = innerHitsMap.get("appointments");
						if (!Objects.isNull(innerReviews)) {
							List<ContactDocument.Review> listReview = new ArrayList<>();
							Arrays.stream(innerReviews.getHits()).forEach(innerReview -> {
								ContactDocument.Review rdoc = (ContactDocument.Review) objectMapper
										.convertValue(innerReview.getSourceAsMap(), ContactDocument.Review.class);
								listReview.add(rdoc);
							});
							doc.setReviews(listReview);
						}
						if (!Objects.isNull(innerSurveys)) {
							List<ContactDocument.SurveyResponse> listSurvey = new ArrayList<>();
							Arrays.stream(innerSurveys.getHits()).forEach(innerSurvey -> {
								ContactDocument.SurveyResponse rdoc = (ContactDocument.SurveyResponse) objectMapper
										.convertValue(innerSurvey.getSourceAsMap(),
												ContactDocument.SurveyResponse.class);
								listSurvey.add(rdoc);
							});
							doc.setSurveyResponses(listSurvey);
						}
						if (!Objects.isNull(innerPayments)) {
							List<ContactDocument.Payment> listPayments = new ArrayList<>();
							Arrays.stream(innerPayments.getHits()).forEach(innerPayment -> {
								ContactDocument.Payment rdoc = (ContactDocument.Payment) objectMapper
										.convertValue(innerPayment.getSourceAsMap(), ContactDocument.Payment.class);
								listPayments.add(rdoc);
							});
							doc.setPayments(listPayments);
						}
						if (!Objects.isNull(innerAppointments)) {
							List<ContactDocument.Appointment> listAppointments = new ArrayList<>();
							Arrays.stream(innerAppointments.getHits()).forEach(innerAppointment -> {
								ContactDocument.Appointment rdoc = (ContactDocument.Appointment) objectMapper
										.convertValue(innerAppointment.getSourceAsMap(),
												ContactDocument.Appointment.class);
								listAppointments.add(rdoc);
							});
							doc.setAppointments(listAppointments);
						}
					}
					resultDocuments.add((T) doc);
				});
			}

			elasticData.setTotal(searchResponse.getHits().getTotalHits().value);
			elasticData.setResults(resultDocuments);
			elasticData.setSucceeded(true);

		}
		return elasticData;
	}

	@Override
	public <T> List<T> searchByQueryForBidMigration(ESRequest esRequest, Class<T> documentType) {
		SearchResponse searchResponse = getSearchResult(esRequest);

		List<T> resultDocuments = new ArrayList<>();
		if (Objects.nonNull(searchResponse)) {
			SearchHit[] searchHit = searchResponse.getHits().getHits();

			if (searchHit.length > 0) {
				Arrays.stream(searchHit).forEach(hit -> {
					String id = hit.getId();
					MessageDocumentTemp out = (MessageDocumentTemp) objectMapper.convertValue(hit.getSourceAsMap(),
							documentType);
					out.set_id(id);
					resultDocuments.add((T) out);
				});
			}
		}
		return resultDocuments;
	}

	@Override
	public <T extends Identifiable> boolean bulkUpsert(BulkUpsertPayload<T> payload) {
		return performBulkRequestWithRefresh(payload, OpType.INDEX, RefreshPolicy.IMMEDIATE);
		// return bulkUpsertWithRefresh(payload, RefreshPolicy.IMMEDIATE);
	}

	@Override
	public SearchResponse getSearchResult(ESRequest esRequest) {
		Assert.notNull(esRequest, "Request cannot be null");
		Assert.notNull(esRequest.getIndex(), "Index cannot be null");
		Assert.notNull(esRequest.getQueryTemplateFile(), "QueryTemplateFile cannot be null");
		SearchResponse searchResponse = null;
		try {
			String query = freemarkerTemplateService.processTemplate(esRequest.getQueryTemplateFile(),
					esRequest.getFreeMarkerDataModel());
			SearchRequest searchRequest = prepareSearchRequest(esRequest, query);
			long start = System.currentTimeMillis();
			searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			long end = System.currentTimeMillis();
			TimeValue timeTaken = searchResponse.getTook();
			LogUtil.logESExecutionTime("getSearchResult", esRequest.getIndex(), start, end, timeTaken.duration());
			float timeInSec = (float) (end - start) / (float) 1000;
			if (timeInSec >= 2.0) {
				log.info("ES_QUERY_EXECUTION TIME is [ {} sec ] for INDEX [ {} ] and QUERY [ {} ] ", timeInSec,
						esRequest.getIndex(), query);
			}
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				log.error("ES hit failed while executing for request {} since response is null.", esRequest);
				return null;
			}
		} catch (IOException e) {
			log.error("[getSearchResult] exception occurred during ES search", e);
		} catch (Exception e) {
			log.error("Exception while executing ES request {} :: {}", esRequest, e);
		}
		return searchResponse;
	}

	private SearchRequest prepareSearchRequest(ESRequest esRequest, String query) {
		SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
		SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());

		SearchRequest searchRequest = null;
		try {
			XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(
					new NamedXContentRegistry(searchModule.getNamedXContents()), LoggingDeprecationHandler.INSTANCE,
					query);
			searchSourceBuilder.parseXContent(parser);
			searchSourceBuilder.trackTotalHits(true);

			searchRequest = new SearchRequest(esRequest.getIndex()).source(searchSourceBuilder);
			if (esRequest.getRoutingId() != null) {
				searchRequest.routing(String.valueOf(esRequest.getRoutingId()));
			}
			if (StringUtils.isNotBlank(esRequest.getScroll())) {
				searchRequest.scroll(esRequest.getScroll().trim());
			}
		} catch (IOException e1) {
			log.error("Error in prepareSearchRequest for XContentParser");
		}
		return searchRequest;
	}

	@Override
	public SearchResponse readMoreFromSearch(String scrollId, String sessionTime) {
		SearchResponse searchScrollResponse = null;
		try {
			SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
			scrollRequest.scroll(sessionTime);
			searchScrollResponse = client.scroll(scrollRequest, RequestOptions.DEFAULT);
			scrollId = searchScrollResponse.getScrollId();
			return searchScrollResponse;
		} catch (IOException e) {
			log.error("Exception occurred during ES readMoreFromSearch call {}", e);
		}
		return null;
	}

	@Override
	public Long getDocumentCount(ESRequest esRequest) {
		log.info("Req getDocCount {}", JSONUtils.toJSON(esRequest));
        SearchResponse searchResponse = getSearchResult(esRequest);
		if (Objects.nonNull(searchResponse)) {
			return searchResponse.getHits().getTotalHits().value;
		}
		return null;
	}

	@Override
	public boolean updateDocument(ESRequest esRequest, boolean upsert) {
        Boolean isSucceeded = false;
		try {
			log.info("Req updateDoc{}", JSONUtils.toJSON(esRequest));
			setLastUpdateDateInDocument(esRequest);
			Upsert<?> payload = esRequest.getPayload();
			Map<String, Object> documentMapper = objectMapper.convertValue(payload.getDocument(), Map.class);

			UpdateRequest updateRequest = new UpdateRequest();
			updateRequest.index(esRequest.getIndex());
			updateRequest.id(payload.getDocumentId());
			updateRequest.doc(documentMapper);
			if (esRequest.getRoutingId() != null) {
				updateRequest.routing(String.valueOf(esRequest.getRoutingId()));
			}
			updateRequest.docAsUpsert(upsert);
			updateRequest.retryOnConflict(4);
			updateRequest.setRefreshPolicy(RefreshPolicy.IMMEDIATE);
			String includes[] = esRequest.getIncludes();
			String excludes[] = esRequest.getExcludes();
			if (ArrayUtils.isNotEmpty(includes) || ArrayUtils.isNotEmpty(excludes)) {
				updateRequest.fetchSource(new FetchSourceContext(true, includes, excludes));
				updateRequest.getShouldStoreResult();
			}
			long startTime = System.currentTimeMillis();
			UpdateResponse updateResponse = client.update(updateRequest, RequestOptions.DEFAULT);
			GetResult getResult = updateResponse.getGetResult();
			if (Objects.nonNull(updateRequest.fetchSource()) && Objects.nonNull(payload)
					&& Objects.nonNull(getResult)) {
				Map<String, Object> result = getResult.sourceAsMap();
				Object document = payload.getDocument();
				if (document instanceof ContactDocument) {
					ContactDocument contactDocument = (ContactDocument) document;
					ContactDocument updatedContactDocument = JSONUtils.fromJSON(JSONUtils.toJSON(result),
							ContactDocument.class);
					contactDocument.setLatestSurveyScore(updatedContactDocument.getLatestSurveyScore());
				}
			}
			long endTime = System.currentTimeMillis();
			LogUtil.logESExecutionTime("updateDocumentES", esRequest.getIndex(), startTime, endTime, null);
			isSucceeded = true;
		} catch (Exception ex) {
			log.error("Exception occurred during ES updateDocument call {}", ex);
		}
		return isSucceeded;
	}

	private void setLastUpdateDateInDocument(ESRequest esRequest) {
		if (Constants.Elastic.MESSAGE_INDEX.equals(esRequest.getIndex())) {
			// Map and Update lastUpdateDate field
			MessageDocument doc = (MessageDocument) esRequest.getPayload().getDocument();
			doc.setLastUpdateDate((new Date()).getTime());
		}
		if (Constants.Elastic.CONTACT_INDEX.equals(esRequest.getIndex())) {
			// Map and Update lastUpdateDate field
			ContactDocument doc = (ContactDocument) esRequest.getPayload().getDocument();
			doc.setLastUpdateDate((new Date()).getTime());
		}
	}

	@Override
	public boolean deleteByQuery(ESRequest esRequest) {
		return deleteByQueryWithRefresh(esRequest, true);
	}

	@Override
	public boolean updateByQuery(ESUpdateByQueryRequest esRequest) {
		return updateByQueryWithRefresh(esRequest, true);
	}

	@Override
	public boolean updateByQueryWithRefresh(ESUpdateByQueryRequest esRequest, boolean refresh) {
		Assert.notNull(esRequest, "Request cannot be null");
		Assert.notNull(esRequest.getIndex(), "Index cannot be null");
		Assert.notNull(esRequest.getQueryTemplateFile(), "QueryTemplateFile cannot be null");

        Boolean isSucceeded = false;
		try {
			log.info("Req updateByQuery : {} & refresh : {}", JSONUtils.toJSON(esRequest), refresh);
			String query = freemarkerTemplateService.processTemplate(esRequest.getQueryTemplateFile(),
					esRequest.getFreeMarkerDataModel());

			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());

			XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(
					new NamedXContentRegistry(searchModule.getNamedXContents()), LoggingDeprecationHandler.INSTANCE,
					query);
			searchSourceBuilder.parseXContent(parser);

			UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(esRequest.getIndex());
			updateByQueryRequest.getSearchRequest().source(searchSourceBuilder);

			if (esRequest.getRoutingId() != null) {
				updateByQueryRequest.getSearchRequest().routing(String.valueOf(esRequest.getRoutingId()));
			}
			updateByQueryRequest.setScript(new Script(ScriptType.INLINE, "painless",
					esRequest.getScriptParam().get("inline").toString(),
					esRequest.getParams() == null ? new HashMap<>() : esRequest.getParams()));
			updateByQueryRequest.setConflicts("proceed");
			updateByQueryRequest.setRefresh(refresh);
			if (esRequest.getRoutingId() != null) {
				updateByQueryRequest.setRouting(String.valueOf(esRequest.getRoutingId()));
			}
			long startTime = System.currentTimeMillis();
			BulkByScrollResponse response = client.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
			long endTime = System.currentTimeMillis();
			TimeValue timeTaken = response.getTook();
			LogUtil.logESExecutionTime("updateByQuery", esRequest.getIndex(), startTime, endTime, timeTaken.duration());
			float timeInSec = (float) (endTime - startTime) / (float) 1000;
			if (timeInSec >= 2.0) {
				log.info("UPDATEBYQUERY_EXECUTION TIME is [ {} sec ] for INDEX [ {} ] & Total [ {} ] & Updated [ {} ] & QUERY [ {} ]",
						timeInSec, esRequest.getIndex(), response.getTotal(), response.getUpdated(), query);
			}
			if (response != null && CollectionUtils.isNotEmpty(response.getBulkFailures())) {
				log.error("Exception occurred during ES updateByQuery call {}", response.getBulkFailures().get(0));
			}
			if (response != null && CollectionUtils.isNotEmpty(response.getSearchFailures())) {
				log.error("Exception occurred during ES updateByQuery call {}", response.getSearchFailures().get(0));
			}
			isSucceeded = true;
		} catch (Exception ex) {
			log.error("Exception occurred during ES updateByQuery call {}", ex);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return isSucceeded;
	}

	@Override
	public <T extends Identifiable> boolean performBulkRequest(BulkUpsertPayload<T> bulkUpsertPayload, OpType opType) {
		return performBulkRequestWithRefresh(bulkUpsertPayload, opType, RefreshPolicy.IMMEDIATE);
	}

	private <T> void setLastUpdateDateInDocument(T d, String index) {
		if (Constants.Elastic.MESSAGE_INDEX.equals(index)) {
			// Map and Update lastUpdateDate field
			MessageDocument doc = (MessageDocument) d;
			doc.setLastUpdateDate((new Date()).getTime());
		}
		if (Constants.Elastic.CONTACT_INDEX.equals(index)) {
			// Map and Update lastUpdateDate field
			ContactDocument doc = (ContactDocument) d;
			doc.setLastUpdateDate((new Date()).getTime());
		}
	}

	private <T extends Identifiable> void validate(BulkUpsertPayload<T> payload, OpType opType) {
		Assert.notNull(payload, "Payload must not be null");
		Assert.notNull(opType, "OpType must not be null");
		Assert.notEmpty(payload.getDocs(), "Docs in payload must not be null");
		Assert.notNull(payload.getAccountId(), "AccountId in payload must not be null");
		Assert.notNull(payload.getIndex(), "Index in payload must not be null");
	}

	@Override
	public SearchResponse getESSearchResultForQuery(ESRequest esRequest) {
		Assert.notNull(esRequest, "Request cannot be null");
		Assert.notNull(esRequest.getIndex(), "Index cannot be null");

		String query = esRequest.getQueryTemplateFile();
		SearchResponse searchResponse = null;
		SearchRequest searchRequest = prepareSearchRequest(esRequest, query);
		try {
			long start = System.currentTimeMillis();
			searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			long end = System.currentTimeMillis();
			float timeInSec = (float) (end - start) / (float) 1000;
			if (timeInSec >= 2.0) {
				log.info("ES_QUERY_EXECUTION TIME is [ {} sec ] for the QUERY [ {} ] ", timeInSec,
						esRequest.getQueryTemplateFile());
			}
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				log.error("ES hit failed while executing for request {} since response is null.", esRequest);
				return null;
			}
		} catch (IOException e) {
			log.error("[getSearchResult] exception occurred during ES search", e);
		} catch (Exception e) {
			log.error("Exception while executing ES request {} :: {}", esRequest, e);
		}
		return searchResponse;
	}

	@Override
	@TrackExecutionTime(apiCall = "getResultUsingQueryBuilder")
	public <T> List<T> getResultUsingQueryBuilder(ESQueryBuilderRequest<T> esQueryBuilderRequest) {
		SearchRequest searchRequest = buildSearchRequestUsingESQueryBuilderRequest(esQueryBuilderRequest);
		List<T> documents = null;
		try {
			log.info("getSearchResultUsingQueryBuilder: {}", JSONUtils.toJSON(searchRequest));
			SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				log.error(
						"getResultUsingQueryBuilder ES hit failed while executing for request {} since response is null.",
						esQueryBuilderRequest);
				return null;
			}
			SearchHits searchHits = searchResponse.getHits();
			documents = Arrays.stream(searchHits.getHits()).map(
					hit -> getElasticDocument(hit, esQueryBuilderRequest.getDocumentType(),
							esQueryBuilderRequest.isInclude_Id()))
					.filter(Objects::nonNull).collect(Collectors.toList());
		} catch (IOException e) {
			log.error("[getResultUsingQueryBuilder] exception occurred during ES search", e.getMessage());
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			log.error("Exception while executing ES request {} :: {}", esQueryBuilderRequest, e.getMessage());
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}

		return documents;
	}

	private <T> T getElasticDocument(SearchHit searchHit, Class<T> documentType, boolean include_Id) {
		Map<String, Object> documentMap = searchHit.getSourceAsMap();
		if (include_Id) {
			documentMap.put("id", searchHit.getId());
		}
		return JSONUtils.fromJSON(JSONUtils.toJSON(documentMap), documentType);
	}

	private <T> SearchRequest buildSearchRequestUsingESQueryBuilderRequest(
			ESQueryBuilderRequest<T> esQueryBuilderRequest) {
		QueryBuilder queryBuilder = esQueryBuilderRequest.getQueryBuilder();
		String index = esQueryBuilderRequest.getIndex();
		String routingId = esQueryBuilderRequest.getRoutingId();
		Integer size = esQueryBuilderRequest.getSize();
		Integer from = esQueryBuilderRequest.getPage() * size;
		SortBuilder<?> sortBuilder = esQueryBuilderRequest.getSortBuilder();
		String includes[] = esQueryBuilderRequest.getIncludes();
		String excludes[] = esQueryBuilderRequest.getExcludes();

		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
		sourceBuilder.query(queryBuilder);
		sourceBuilder.from(from);
		sourceBuilder.size(size);
		if (Objects.nonNull(sortBuilder)) {
			sourceBuilder.sort(sortBuilder);
		}
		if (ArrayUtils.isNotEmpty(includes)) {
			sourceBuilder.fetchSource(new FetchSourceContext(true, includes, null));
		} else if (ArrayUtils.isNotEmpty(excludes)) {
			sourceBuilder.fetchSource(new FetchSourceContext(true, null, excludes));
		}
		SearchRequest searchRequest = new SearchRequest(index);
		searchRequest.source(sourceBuilder);

		if (StringUtils.isNotBlank(routingId)) {
			searchRequest.routing(routingId);
		}
		MDC.put(ES_INDEX, index);
		return searchRequest;
	}

	@Override
	@TrackExecutionTime(apiCall = "insertESDocument")
	public <T> IndexResponse insertESDocument(ESInsertRequest<T> esInsertRequest) {
		IndexRequest indexRequest = buildIndexRequestFromESInsertRequest(esInsertRequest);
		IndexResponse indexResponse = null;
		try {
			log.info("insertESDocument indexRequest : {}", JSONUtils.toJSON(indexRequest));
			indexResponse = client.index(indexRequest, RequestOptions.DEFAULT);
		} catch (IOException e) {
			log.error("IO error : {} occurred in insertESDocument with request : {}", e.getMessage(), esInsertRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			log.error("error : {} occurred in insertESDocument with request : {}", e.getMessage(), esInsertRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return indexResponse;
	}

	private <T> IndexRequest buildIndexRequestFromESInsertRequest(ESInsertRequest<T> esInsertRequest) {
		String index = esInsertRequest.getIndex();
		String routingId = esInsertRequest.getRoutingId();
		T document = esInsertRequest.getDocument();
		String id = esInsertRequest.getId();
		RefreshPolicy refreshPolicy = esInsertRequest.getRefreshPolicy();
		IndexRequest indexRequest = new IndexRequest(index);
		if (StringUtils.isNotBlank(id)) {
			indexRequest.id(id);
		}
		if (StringUtils.isNotEmpty(routingId)) {
			indexRequest.routing(routingId);
		}
		String jsonDocument = JSONUtils.toJSON(document);
		indexRequest.source(jsonDocument, XContentType.JSON);
		if (Objects.nonNull(refreshPolicy)) {
			indexRequest.setRefreshPolicy(refreshPolicy);
		}
		MDC.put(ES_INDEX, index);
		return indexRequest;
	}

	@Override
	@TrackExecutionTime(apiCall = "findDocumentById")
	public <T> T findDocumentById(ESFindByIdRequest<T> esFindByIdRequest) {
		log.info("findDocumentById - id : {}", esFindByIdRequest.getId());
		T document = null;
		GetRequest getRequest = buildGetRequestFromESFindByIDRequest(esFindByIdRequest);
		try {
			GetResponse getResponse = client.get(getRequest, RequestOptions.DEFAULT);
			document = JSONUtils.fromJSON(JSONUtils.toJSON(getResponse.getSourceAsMap()),
					esFindByIdRequest.getDocumentType());
		} catch (IOException e) {
			log.error("IO error : {} occurred in findDocumentById with request : {}", e.getMessage(), getRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			log.error("error : {} occurred in findDocumentById with request : {}", e.getMessage(), getRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return document;
	}
	@Override
	@TrackExecutionTime(apiCall = "findDocumentByField")
	public <T> List<T> findDocumentByField(ESFindByFieldRequest<T> esFindRequest) {

		SearchRequest searchRequest = buildGetRequestFromESFindRequest(esFindRequest);

		List<T> documents = null;
		try {
			log.info("getResultUsingQueryBuilder esFindReq : {}",JSONUtils.toJSON(esFindRequest));
			SearchResponse searchResponse = client.search(searchRequest, RequestOptions.DEFAULT);
			if (searchResponse == null || searchResponse.status().getStatus() != 200) {
				log.error(
						"getResultUsingQueryBuilder ES hit failed while executing for request {} since response is null.",
						searchRequest);
				return Collections.emptyList();
			}
			SearchHits searchHits = searchResponse.getHits();
			documents = Arrays.stream(searchHits.getHits()).map(
					hit -> getElasticDocument(hit, esFindRequest.getDocumentType(), false))
					.filter(Objects::nonNull).collect(Collectors.toList());
		} catch (Exception e) {
			log.error("Exception while executing ES request {} :: {}", searchRequest, e.getMessage());
		}

		return documents;
	}

	private <T> GetRequest buildGetRequestFromESFindByIDRequest(ESFindByIdRequest<T> esFindByIdRequest) {
		String id = esFindByIdRequest.getId();
		String index = esFindByIdRequest.getIndex();
		String routingId = esFindByIdRequest.getRoutingId();
		String includes[] = esFindByIdRequest.getIncludes();
		String excludes[] = esFindByIdRequest.getExcludes();

		GetRequest getRequest = new GetRequest(index, id);
		if (StringUtils.isNotBlank(routingId)) {
			getRequest.routing(routingId);
		}
		if (ArrayUtils.isNotEmpty(includes)) {
			getRequest.fetchSourceContext(new FetchSourceContext(true, includes, null));

		} else if (ArrayUtils.isNotEmpty(excludes)) {
			getRequest.fetchSourceContext(new FetchSourceContext(true, null, excludes));
		}
		MDC.put(ES_INDEX, index);
		return getRequest;
	}

	private <T> SearchRequest buildGetRequestFromESFindRequest(ESFindByFieldRequest<T> esFindRequest) {
		BoolQueryBuilder boolQuery = esFindRequest.getBoolQuery();
		String index = esFindRequest.getIndex();
		String routingId = String.valueOf(esFindRequest.getRoutingId());
		String includes[] = esFindRequest.getIncludes();
		String excludes[] = esFindRequest.getExcludes();

		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

		SearchRequest searchRequest = new SearchRequest(index);

		if (ArrayUtils.isNotEmpty(includes)) {
			sourceBuilder.fetchSource(includes, null);
		} else if (ArrayUtils.isNotEmpty(excludes)) {
			sourceBuilder.fetchSource(null, excludes);
		} else {
			sourceBuilder.fetchSource(true);
		}

		if (boolQuery.hasClauses()) {
			sourceBuilder.query(boolQuery);
		}

		if (StringUtils.isNotBlank(routingId)) {
			searchRequest.routing(routingId);
		}
		searchRequest.source(sourceBuilder);
		MDC.put(ES_INDEX, index);

		return searchRequest;
	}

	@Override
	@TrackExecutionTime(apiCall = "deleteDocumentById")
	public String deleteDocumentById(ESDeleteByIdRequest esDeleteByIdRequest) {
		log.info("deleteDocumentById - id : {}", esDeleteByIdRequest.getId());
		String id = null;
		DeleteRequest deleteByIdRequest = buildDeleteRequestFromESDeleteByIDRequest(esDeleteByIdRequest);
		try {
			DeleteResponse deleteResponse = client.delete(deleteByIdRequest, RequestOptions.DEFAULT);
			id = deleteResponse.getId();
		} catch (IOException e) {
			log.error("IO error : {} occurred in deleteDocumentById with request : {}", e.getMessage(),
					deleteByIdRequest);
		} catch (Exception e) {
			log.error("error : {} occurred in deleteDocumentById with request : {}", e.getMessage(), deleteByIdRequest);
		}
		return id;
	}

	private DeleteRequest buildDeleteRequestFromESDeleteByIDRequest(ESDeleteByIdRequest esDeleteByIdRequest) {
		String id = esDeleteByIdRequest.getId();
		String index = esDeleteByIdRequest.getIndex();
		String routingId = esDeleteByIdRequest.getRoutingId();
		RefreshPolicy refreshPolicy = esDeleteByIdRequest.getRefreshPolicy();

		DeleteRequest deleteRequest = new DeleteRequest(index, id);

		if (StringUtils.isNotBlank(routingId)) {
			deleteRequest.routing(routingId);
		}
		if (Objects.nonNull(refreshPolicy)) {
			deleteRequest.setRefreshPolicy(refreshPolicy);
		}
		MDC.put(ES_INDEX, index);
		return deleteRequest;
	}

	@Override
	@TrackExecutionTime(apiCall = "upsertESDocument", maximumTimeThreshold = 5)
	public <T> T upsertESDocument(ESUpsertRequest<T> esUpsertRequest) {
		UpdateRequest updateRequest = buildUpdateRequestWithESUpsertRequest(esUpsertRequest);
		T updatedDocument = esUpsertRequest.getDocument();
		try {
			log.info("upsertESDoc esUpsertReq : {}", JSONUtils.toJSON(esUpsertRequest));
			UpdateResponse updateResponse = client.update(updateRequest, RequestOptions.DEFAULT);
			if (Objects.nonNull(updateRequest.fetchSource())) {
				updatedDocument = (T) JSONUtils.fromJSON(JSONUtils.toJSON(updateResponse.getGetResult().sourceAsMap()),
						esUpsertRequest.getDocument().getClass());
			}
		} catch (IOException e) {
			log.error("IO error : {} occurred in upsertESDocument with request : {}", e.getMessage(),
					updateRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			log.error("error : {} occurred in upsertESDocument with request : {}", e.getMessage(), updateRequest);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return updatedDocument;
	}

	private <T> UpdateRequest buildUpdateRequestWithESUpsertRequest(ESUpsertRequest<T> esUpsertRequest) {
		String id = esUpsertRequest.getId();
		String index = esUpsertRequest.getIndex();
		String routingId = esUpsertRequest.getRoutingId();
		T document = esUpsertRequest.getDocument();
		String includes[] = esUpsertRequest.getIncludes();
		String excludes[] = esUpsertRequest.getExcludes();
		boolean upsert = esUpsertRequest.isUpsert();
		RefreshPolicy refreshPolicy = esUpsertRequest.getRefreshPolicy();
		int retryOnConflict = esUpsertRequest.getRetryOnConflict();
		boolean fetchSource = esUpsertRequest.isFetchSource();

		UpdateRequest updateRequest = new UpdateRequest(index, id);
		updateRequest.retryOnConflict(retryOnConflict);

		if (Objects.nonNull(refreshPolicy)) {
			updateRequest.setRefreshPolicy(refreshPolicy);
		}
		updateRequest.docAsUpsert(upsert);

		if (StringUtils.isNotBlank(routingId)) {
			updateRequest.routing(routingId);
		}

		if (ArrayUtils.isNotEmpty(includes)) {
			updateRequest.fetchSource(new FetchSourceContext(true, includes, null));
		} else if (ArrayUtils.isNotEmpty(excludes)) {
			updateRequest.fetchSource(new FetchSourceContext(true, null, excludes));
		} else if (fetchSource) {
			updateRequest.fetchSource(fetchSource);
		}

		if (Objects.nonNull(document) && StringUtils.isNotBlank(index)) {
			setLastUpdateDateInDocument(document, index);
		}

		String updatedJsonDocument = JSONUtils.toJSON(document);

		updateRequest.doc(updatedJsonDocument, XContentType.JSON);

		MDC.put(ES_INDEX, index);
		return updateRequest;
	}

	@Override
	@TrackExecutionTime(apiCall = "updateSmartInboxOwner")
	public BulkByScrollResponse updateSmartInboxOwner(SmartInboxOwnerBuilder smartInboxOwnerBuilder) {

		ElasticUpdateQueryRequest elasticUpdateQueryRequest = smartInboxOwnerBuilder.build();
		BulkByScrollResponse updateResponse = null;
		try {
			updateResponse = elasticUpdateQueryRequest.executeQuery(client);
			if (updateResponse != null && CollectionUtils.isNotEmpty(updateResponse.getBulkFailures())) {
				log.error("Exception occurred during ES updateByQuery call {}",
						updateResponse.getBulkFailures().get(0));
			}
			if (updateResponse != null && CollectionUtils.isNotEmpty(updateResponse.getSearchFailures())) {
				log.error("Exception occurred during ES updateByQuery call {}",
						updateResponse.getSearchFailures().get(0));
			}

		} catch (IOException e) {
			log.error("Exception occurred during ES updateByQuery call {}", e);
		}
		MDC.put(ES_INDEX, smartInboxOwnerBuilder.getIndex());

		return updateResponse;

	}

	@Async
	@Override
	public void deleteDocumentByQueryAsync(ESDeleteByIdRequest deleteByIdRequests) {
		DeleteByQueryRequest request = deleteByIdRequests.buildDeleteByQueryRequest();
		request.setRefresh(false);
		try {
			BulkByScrollResponse response = client.deleteByQuery(request, RequestOptions.DEFAULT);

			if (response != null && CollectionUtils.isNotEmpty(response.getBulkFailures())) {
				log.error("Bulk failure occurred during ES deleteByQuery call {}", response.getBulkFailures().get(0));
			}
			if (response != null && CollectionUtils.isNotEmpty(response.getSearchFailures())) {
				log.error("Search failure occurred during ES updateByQuery call {}",
						response.getSearchFailures().get(0));
			}

		} catch (IOException e) {
			log.error("Exception occurred during ES deleteByQuery call {}", e);
		}
		MDC.put(ES_INDEX, deleteByIdRequests.getIndex());
	}

	@Override
	public boolean deleteByQueryWithRefresh(ESRequest esRequest, boolean refresh) {
		Assert.notNull(esRequest, "Request cannot be null");
		Assert.notNull(esRequest.getIndex(), "Index cannot be null");
		Assert.notNull(esRequest.getQueryTemplateFile(), "QueryTemplateFile cannot be null");

		Boolean isSucceeded = false;
		try {
			String query = freemarkerTemplateService.processTemplate(esRequest.getQueryTemplateFile(),
					esRequest.getFreeMarkerDataModel());

			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			SearchModule searchModule = new SearchModule(Settings.EMPTY, false, Collections.emptyList());

			XContentParser parser = XContentFactory.xContent(XContentType.JSON).createParser(
					new NamedXContentRegistry(searchModule.getNamedXContents()), LoggingDeprecationHandler.INSTANCE,
					query);
			searchSourceBuilder.parseXContent(parser);
			searchSourceBuilder.trackTotalHits(true);

			DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(esRequest.getIndex());
			deleteByQueryRequest.getSearchRequest().source(searchSourceBuilder);
			if (esRequest.getRoutingId() != null) {
				deleteByQueryRequest.setRouting(String.valueOf(esRequest.getRoutingId()));
			}
			deleteByQueryRequest.setConflicts("proceed");
			deleteByQueryRequest.setRefresh(refresh);
			long startTime = System.currentTimeMillis();
			BulkByScrollResponse deleteByQueryResponse = client.deleteByQuery(deleteByQueryRequest,
					RequestOptions.DEFAULT);
			long endTime = System.currentTimeMillis();
			TimeValue timeTaken = deleteByQueryResponse.getTook();
			LogUtil.logESExecutionTime("deleteByQuery", esRequest.getIndex(), startTime, endTime, timeTaken.duration());
			float timeInSec = (float) (endTime - startTime) / (float) 1000;
			if (timeInSec >= 2.0) {
				log.info("DELETEBYQUERY_EXECUTION TIME is [ {} sec ] for INDEX [ {} ] & Total [ {} ] & Deleted [ {} ] & QUERY [ {} ] ",
						timeInSec, esRequest.getIndex(), deleteByQueryResponse.getTotal(), deleteByQueryResponse.getDeleted(), query);
			}
			isSucceeded = true;
		} catch (Exception ex) {
			log.error("Exception occurred during ES deleteByQuery call {}", ex);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return isSucceeded;
	}


	public boolean reindexByQuery(BoolQueryBuilder esRequest, String sourceIndex, String targetIndex) {
		Assert.notNull(esRequest, "Request cannot be null");
		Assert.notNull(sourceIndex, "Index cannot be null");
		Assert.notNull(targetIndex, "NewIndex cannot be null");

		boolean isSucceeded = false;
		try {
			log.info("Request for reindexByQuery: {}", JSONUtils.toJSON(esRequest));
			ReindexRequest reindexRequest = new ReindexRequest()
					.setSourceIndices(sourceIndex)
					.setDestIndex(targetIndex)
					.setScroll(TimeValue.timeValueMinutes(30L))
					.setSourceQuery(esRequest)
					.setRefresh(false)
					.setSourceBatchSize(5000);

			reindexRequest.setConflicts("proceed");
			TaskSubmissionResponse reindexSubmission = client.submitReindexTask(reindexRequest, RequestOptions.DEFAULT);

			String taskId = reindexSubmission.getTask();
			isSucceeded = true;
			log.info("Reindex task submitted. task Id : {}", taskId);

		} catch (Exception e) {
			log.error("Exception occurred during ES reindexByQuery call", e);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		return isSucceeded;
	}

	@Override
	public <T extends Identifiable> boolean performBulkRequestWithRefresh(BulkUpsertPayload<T> bulkUpsertPayload,
			OpType opType, RefreshPolicy refreshPolicy) {
		validate(bulkUpsertPayload, opType);
		ObjectMapper o = new ObjectMapper();
		List<T> docs = bulkUpsertPayload.getDocs();
		BulkRequest request = new BulkRequest();
		docs.forEach(d -> {
			setLastUpdateDateInDocument(d, bulkUpsertPayload.getIndex());
			try {
				if (OpType.INDEX.equals(opType)) {
					request.add(new IndexRequest(bulkUpsertPayload.getIndex()).id(d.getId().toString())
							.source(o.writeValueAsString(d), XContentType.JSON));
				} else if (OpType.UPDATE.equals(opType)) {
					UpdateRequest updateRequest = new UpdateRequest(bulkUpsertPayload.getIndex(), d.getId().toString());
					updateRequest.doc(o.writeValueAsString(d), XContentType.JSON);
					request.add(updateRequest);
				} else {
					throw new MessengerException(ErrorCode.ES_BULK_OPERATION_INVALID_OPTYPE);
				}

			} catch (Exception e) {
				log.error("Exception while creating bulk request :{}", e);
				return;
			}
		});
		request.routing(bulkUpsertPayload.getRoutingId().toString());
		try {
			long startTime = System.currentTimeMillis();
			request.setRefreshPolicy(refreshPolicy);
			BulkResponse responses = client.bulk(request, RequestOptions.DEFAULT);
			long endTime = System.currentTimeMillis();
			TimeValue timeTaken = responses.getTook();
			LogUtil.logESExecutionTime("performBulkRequest", bulkUpsertPayload.getIndex(), startTime, endTime,
					timeTaken.duration());

			if (responses.hasFailures()) {
				return false;
			}
		} catch (Exception e) {
			log.error("Exception while upserting docs in ES:{}", e);
			return false;
		}
		return true;
	}

	@Override
	public boolean clearScrollContext(String scrollId) {
		boolean success = false;
		ClearScrollRequest clearScroll = new ClearScrollRequest();
		clearScroll.addScrollId(scrollId);
		try {
			ClearScrollResponse response = client.clearScroll(clearScroll, RequestOptions.DEFAULT);
			success = response.isSucceeded();
		} catch (Exception e) {
			log.error("Exception while clearing scroll context:{}", e);
		}
		return success;
	}

	@Override
	public SearchResponse getSearchResult(SearchRequest searchRequest) {
		SearchResponse searchResponse = null;
      try {
		searchResponse=client.search(searchRequest, RequestOptions.DEFAULT);
	} catch (IOException e) {
		log.error("[getSearchResult] exception occurred during ES search", e);
	} catch (Exception e) {
		log.error("Exception while executing ES request {} :: {}", searchRequest, e);
	}
	return searchResponse;
	}
}
