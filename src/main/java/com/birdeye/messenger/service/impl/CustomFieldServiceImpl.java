package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.CustomFieldsWidget;
import com.birdeye.messenger.dao.repository.CustomFieldWidgetRepository;
import com.birdeye.messenger.service.CustomFieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class CustomFieldServiceImpl implements CustomFieldService {
    @Autowired
    private CustomFieldWidgetRepository customFieldWidgetRepository;

    @Override
    @Cacheable(cacheNames = Constants.WEBCHAT_CUSTOM_FIELDS, key="'Widget_id-'.concat(#widgetId)", unless = "#result==null or #result.size()==0")
    public List<CustomFieldsWidget> getWebchatCustomFields(Integer widgetId, Long businessId) {
        return customFieldWidgetRepository.findByWidgetIdAndBusinessId(widgetId, businessId);
    }

    @Override
    @Transactional
    public void deleteWebchatCustomFields(Integer widgetId, Long businessId) {
        customFieldWidgetRepository.deleteByWidgetIdAndBusinessId(widgetId, businessId);
    }
}
