package com.birdeye.messenger.service.impl;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.appointment.AppointmentInfo;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.external.service.*;
import com.birdeye.messenger.service.*;
import com.birdeye.messenger.util.AppointmentUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.google.common.base.Preconditions;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Service
@Slf4j
@RequiredArgsConstructor
public class AppointmentEventConsumerServiceImpl implements AppointmentEventConsumerService {

    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final RedisHandler redisHandler;
    private final FirebaseService fcmService;
    private final ConversationActivityService conversationActivityService;
    private final ContactService contactService;
    private final UserService userService;
    private final SmsService smsService;
    private final NotificationService notificationService;
    @Autowired
    private RedisLockService redisLockService;

    @Autowired
    private KafkaService kafkaService;
    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    private final AppointmentConfirmationService appointmentConfirmationService;
    private final AppointmentService appointmentService;


    @Override
    public void consumeAppointmentEvent(ContactAppointmentEventDTO request) {
        BusinessDTO business = businessService.getBusinessLiteDTO(request.getBid());
        Objects.requireNonNull(business);
        CustomerDTO customer = contactService.findById(request.getCid());
        // fetch business for given location in request
        Optional<Lock> lockOpt = Optional.empty();
        String lockKey = Constants.CUSTOMER_ID_PREFIX + customer.getId().toString();
        try {
        	lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
            if (lockOpt.isPresent()) {
            	String phoneNumber = smsService.getFormattedBusinessNumber(business.getBusinessId());
            	
            	String customerName = customer.getFirstName();
                String customerComment = customerName.concat(" has requested an appointment on ").concat(request.getAppointmentDate())
                        .concat(" at ").concat(request.getAppointmentTime());
                if (!ObjectUtils.isEmpty(request.getCustomerComment())) {
                    customerComment = customerComment.concat("\n").concat("Note : ").concat(request.getCustomerComment());
                }
                if (!ObjectUtils.isEmpty(customer.getPhoneE164())) {
                    customerComment = customerComment.concat("\n").concat("Mobile : ").concat(customer.getPhoneE164());
                }
                if (!ObjectUtils.isEmpty(customer.getEmailId())) {
                    customerComment = customerComment.concat("\n").concat("Email : ").concat(customer.getEmailId());
                }
                request.setCustomerComment(customerComment);
                
            	SmsDTO appointmentSmsDto = SmsDTO.getSmsDTOAppointment(customer, request, phoneNumber, Source.APPOINTMENT);
            	
                MessengerContact messengerContact = messengerContactService.findOrCreate(business, MessageTag.UNREAD.getCode(), customer);
                if (messengerContact.getTag() != MessageTag.UNREAD.getCode())
                    messengerContact.setTag(MessageTag.UNREAD.getCode());
                
                Date receivedDate = new Date(request.getRequestDate());
                // Last message meta data
                LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
                lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
                lastMessageMetaData.setLastReceivedMessageSource(Source.APPOINTMENT.getSourceId());
                lastMessageMetaData.setLastMessageSource(Source.APPOINTMENT.getSourceId());
                messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
                // setting isRead false and viewedBy to null as a new message is received
                messengerContact.setIsRead(false);
                messengerContact.setViewedBy(null);
                messengerContact.setLastMessage(request.getCustomerComment());
                messengerContact.setCreatedAt(receivedDate);
                messengerContact.setLastMsgOn(receivedDate);
                messengerContact.setUpdatedAt(new Date());
                messengerContactService.encryptLastMessage(business, appointmentSmsDto, messengerContact, customer);
                messengerContact = messengerContactService.saveOrUpdateMessengerContact(messengerContact);
                UserDTO userDTO = new UserDTO();
                ContactDocument contactDocument = createOrUpdateContactEs(request, business, messengerContact, customer, userDTO, receivedDate);
                

                Sms sms = smsService.saveSmsFromCustomer(appointmentSmsDto);
                log.info("consumeAppointmentEvent sms saved {}", appointmentSmsDto.getSmsId());
                // ES update for new message.
                MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(appointmentSmsDto, messengerContact.getId());
                MessageDocument messageDocument = messengerContactService.andNewMessageOnEs(messageDocumentDTO,
                        null, userDTO, business, MessengerEvent.SMS_RECEIVE);
                // Mirroring + Email Notification
                MessengerGlobalFilter notificationMetaData = getEmailNotificationMetaData(appointmentSmsDto.getCreateDate(), messageDocumentDTO, business);
                log.info("consumeAppointmentEvent getEmailNotificationMetaData {}", notificationMetaData);
                notificationService.processMessageNotification(notificationMetaData, business, messageDocument);
                fcmService.pushToFireBase(contactDocument, messageDocument, true);

                return;
            } else {
                log.info("Lock is already acquired for the key:{}", lockKey);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.MICROSITE_APPOINTMENT_EVENT_ADD_DELAYED_QUEUE, request);
            }
        } catch (Exception e) {
            log.error("Exception while consuming appointment event in inbox:{}", request.getAppointmentId(), e);
        } finally {
            if (lockOpt.isPresent()) {
                redisLockService.unlock(lockOpt.get());
            }
        }
    }

    public MessengerGlobalFilter getEmailNotificationMetaData(Date crDate, MessageDocumentDTO messageDocumentDTO, BusinessDTO business) {
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(business.getBusinessId());
        notificationRequest.setBusinessName(business.getBusinessName());
        notificationRequest.setBusinessAlias(business.getBusinessAlias());
        notificationRequest.setEnterpriseName(business.getEnterpriseName());
        notificationRequest.setBusinessNumber(business.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(business.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(business.getEnterpriseId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setTimeZone(business.getTimeZone());
        notificationRequest.setMsgId(Integer.valueOf(messageDocumentDTO.getM_id()));
        notificationRequest.setProductName(business.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        notificationRequest.setLastMsgTime(crDate.getTime());
        notificationRequest.setConversationId(messageDocumentDTO.getMcid());
        return notificationRequest;

    }
    ContactDocument createOrUpdateContactEs(ContactAppointmentEventDTO request, BusinessDTO business, MessengerContact messengerContact, CustomerDTO customer, UserDTO userDTO, Date receivedDate) {
        ContactDocument contactDocument = new ContactDocument();
        Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(business.getAccountId(), messengerContact.getId());
        boolean contactUpdated = false;
        if (contactDocumentOptional.isPresent()) {
            ContactDocument contactDocumentEs = contactDocumentOptional.get();
            contactDocument = messengerContactService.contactDocumentBuilder(messengerContact, customer, business, MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);
            AppointmentEventDto appointmentEventDto = new AppointmentEventDto();
            appointmentEventDto.setAppointmentId(request.getAppointmentId().longValue());
            appointmentEventDto.setSource(AppointmentSource.MICROSITE.getValue());
            appointmentEventDto.setActionBy(AppointmentActionBy.CUSTOMER.getValue());
            appointmentEventDto.setEventTime(receivedDate.getTime());
            appointmentEventDto.setStatus(AppointmentStatus.SUCCESS.getValue());
            markConversationAsOpenAndUnread(contactDocument, messengerContact);
            // as if appointment is added or updated conversation if hidden should be marked
            // as unhidden.
            contactDocument.setHide(false);
            contactDocument.setL_appointment_activity_on(appointmentEventDto.getEventTime());
            contactDocument.setAppointments(contactDocumentEs.getAppointments());
            AppointmentUtil.updateAppointmentDetails(appointmentEventDto, contactDocument, userDTO, request.getCustomerComment());
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            contactDocument.setUpdatedAt(simpleDateFormat.format(new Date()));
            contactUpdated = messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, business.getRoutingId(), business.getEnterpriseId(), business.getBusinessId());
        } else {
            contactDocument = messengerContactService.contactDocumentBuilder(messengerContact, customer, business, MessageTag.getMessageTagById(messengerContact.getTag()), userDTO);
            List<ContactDocument.Appointment> appointments = new ArrayList<>();
            ContactDocument.Appointment appointment = new ContactDocument.Appointment();
            AppointmentUtil.addAppointment(request, appointment, request.getCustomerComment());
            appointments.add(appointment);
            contactDocument.setAppointments(appointments);
            contactDocument.setL_appointment_activity_on(receivedDate.getTime());
            contactUpdated = messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, business.getRoutingId(), business.getEnterpriseId(), business.getBusinessId());
        }
        if (!contactUpdated) {
            throw new MessengerException(ErrorCode.ERROR_UPDATING_CONVERSATION);
        }
        return contactDocument;
    }
    @Override
    public void consumeAppointmentEventV1(AppointmentEventDto request) {
    	if (request != null || ObjectUtils.containsConstant(AppointmentActionEventType.values(), request.getAction(), true)) {
    		BusinessDTO business = businessService.getBusinessLiteDTO(request.getBusinessId());
    		if (business == null) {
    			log.error(
    					"consumeAppointmentEventV1: appointment event not saved as business returned by core-service is null for businessId {}",
    					request.getBusinessId());
    			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
    		}
    		CustomerDTO customerDTO = contactService.findById(request.getCustomerId());
    		if (customerDTO == null) {
    			log.error(
    					"consumeAppointmentEventV1: appointment event not saved as customer returned by kontacto-service is null for customerId {}",
    					request.getCustomerId());
    			throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
    		}

    		String lockKey = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId().toString();
    		Optional<Lock> lockOpt = Optional.empty();
    		try {
    			lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
    			if (lockOpt.isPresent()) {
    				//Rescheduled : Action should be "rescheduled"
    				if (request.getParentId()!=null) {
    					request.setAction(ActivityType.RESCHEDULED.getLabel());
    				}
    				MessengerContact messengerContact = messengerContactService.findOrCreate(business, MessageTag.INBOX.getCode(), customerDTO);
    				List<Long> viewedByUsers = StringUtils.isNotBlank(messengerContact.getViewedBy())
    						? MessengerUtil.convertCommaSeparatedStringToLongList(messengerContact.getViewedBy())
    								: new ArrayList<>();

    				if (messengerContact.getTag() != MessageTag.UNREAD.getCode()) {
    					if (!request.getActionBy().equals(AppointmentActionBy.BUSINESS.getValue())) {
    						messengerContact.setTag(MessageTag.UNREAD.getCode());
    						messengerContact.setIsRead(false);
    						messengerContact.setViewedBy(null);
    					} else {
    						if(BooleanUtils.isTrue(messengerContact.getIsNew())) {
    							messengerContact.setIsRead(true);
    						}
    						else if (request.getUserId()!=null) {
    							viewedByUsers.add(request.getUserId());
    							String userIds = StringUtils.join(viewedByUsers, ',');
    							messengerContact.setViewedBy(userIds);
    						}
    					}
    				}
    				Date receivedDate = new Date(request.getEventTime());
    				// Last message meta data
    				LastMessageMetaData lastMessageMetaData = updateLastMessageMetaDataForAppointment(request, messengerContact);
    				ContactDocument contactDocument = new ContactDocument();
    				boolean isNew =messengerContact.getIsNew() != Boolean.TRUE ? Boolean.FALSE : messengerContact.getIsNew(); 
    				messengerContact.setCreatedAt(receivedDate);
    				messengerContact.setUpdatedAt(new Date());
    				messengerContact =messengerContactService.saveOrUpdateMessengerContact(messengerContact);
    				//save to contact es
    				UserDTO userDTO = null;
    				if (request.getUserId() != null) {
    					userDTO = userService.getUserDTO(request.getUserId().intValue());
    				}
    				if (!BooleanUtils.isTrue(isNew)) {
    					contactDocument = updateExistingConversation(lastMessageMetaData, userDTO,
    							request, messengerContact, business, customerDTO);
    				} else {
    					Optional<ContactDocument> contactDocumentOptional = createContactDocument(request, business,
    							messengerContact, business.getAccountId(), customerDTO, userDTO);
    					contactDocument = contactDocumentOptional.get();
    				}
    				//save to message es
    				boolean markIsActive = request.getAction().equals(AppointmentActionEventType.REMINDER_SENT.getValue()) ? false : true;
    				markIsActive =  markIsActive==false || request.getAction().equals(AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue()) ? false: true;
    				AppointmentInfo appointmentInfo = AppointmentUtil.prepareAppointmentInfo(request, markIsActive);
    				Date created = new Date(appointmentInfo.getEventTime());
    				ActivityDto appointmentActivity = ActivityDto.builder().mcId(messengerContact.getId()).created(created).updated(new Date()).actorId(userDTO!= null ? userDTO.getId(): null).actorName(userDTO!= null ? userDTO.getName(): null)
    						.activityType(ActivityType.from(request.getAction()))
    						.appointmentInfo(appointmentInfo)
    						.type(MessageDocument.Type.CREATE)
    						.accountId(business.getAccountId()).businessId(business.getBusinessId())
    						.build();
    				conversationActivityService.persistActivityInDatabase(appointmentActivity, userDTO);
    				if (!BooleanUtils.isTrue(isNew) && markIsActive) {
    					messengerContactService.markInActivePreviousAppointmentCards(request.getAccountId(), messengerContact.getId(), request.getAppointmentId(), request.getParentId());
    				}

    				log.info("[persistActivityInES][{}] - Received event for activityDto {}", appointmentActivity.getActivityType(), appointmentActivity);
    				MessageDocument messageDocument = new MessageDocument(appointmentActivity);
    				if(request.getAction().equals(AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue())) {
    					messageDocument.setChannel(MessageDocument.Channel.CAMPAIGN);
    				}else {
    					messageDocument.setChannel(MessageDocument.Channel.APPOINTMENT);
    				}
    				if(Objects.nonNull(request.getSmsId())){
    					messageDocument.setSmsId(request.getSmsId());
    				}
    				if(Objects.nonNull(request.getEmailId())){
    					messageDocument.setEmailId(request.getEmailId());
    				}
    				messageDocument = messengerContactService.addNewMessageDocOnES(messageDocument,
    						messageDocument.getM_id() + "_a");

                    //Contact Upgrade - Appointment event for Active
                    List<Integer> accountIds=contactService.getAllContactUpgradeEnabledAccounts();
                    if(CollectionUtils.isNotEmpty(accountIds) && request.getAccountId()!=null && accountIds.contains(request.getAccountId())){
                        customerDTO =commonService.getActiveCustomerAndUpdateConversationStateForAppointment(customerDTO, request.getBusinessId(),
                                request.getAccountId(), contactDocument);
                    }

                    //push notification as true only for booked event
                    boolean sendWebNotification = request.getAction().equals(AppointmentActionEventType.BOOKED.getValue()) && request.getActionBy().equals(AppointmentActionBy.CUSTOMER.getValue()) ? true : false;
                    Integer loggedInUserId=request.getUserId()!=null?request.getUserId().intValue():null;
                    fcmService.pushToFireBase(contactDocument, messageDocument, sendWebNotification,false,loggedInUserId,null);
                    AppointmentConfirmationContextCachedDataDto appointmentConfirmationContext = appointmentConfirmationService.checkAndGetAppointmentConfirmationContext(business, messengerContact);
                    if((request.getAction().equals(AppointmentActionEventType.REMINDER_SENT.getValue()) || request.getAction().equals(AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue())) && request.getStatus().equals(AppointmentStatus.SUCCESS.getValue()) && Objects.nonNull(request.getSmsId()) && Objects.nonNull(request.getReviewRequestId()) && request.isAppointmentConfirmOptionEnabled()){
                        if(Objects.nonNull(appointmentConfirmationContext) && request.getEventTime() > appointmentConfirmationContext.getEventTime()) {
                            appointmentConfirmationService.updateAppointmentConfirmationContext(business.getAccountId().toString(), business.getBusinessId().toString(), messengerContact.getId().toString(), request.getAltAppointmentId(), request.getReviewRequestId(), request.getEventTime(),request.getPmsAppointmentId());
                        }else if(Objects.isNull(appointmentConfirmationContext) && request.getStartTime() > request.getEventTime()){
                            appointmentConfirmationService.updateAppointmentConfirmationContext(business.getAccountId().toString(), business.getBusinessId().toString(), messengerContact.getId().toString(), request.getAltAppointmentId(), request.getReviewRequestId(), request.getEventTime(),request.getPmsAppointmentId());
                        }
                    }else if(Objects.isNull(request.getEmailId())){
                        appointmentConfirmationService.checkAndDeleteAppointmentConfirmationContext(business,messengerContact);
                    }
    			} else {
    				log.info("Lock is already acquired for the key:{}", lockKey);
    				kafkaService.publishToKafkaAsync(KafkaTopicEnum.APPOINTMENT_EVENT_ADD_DELAYED_QUEUE, request);
    			}
    		} catch (Exception e) {
    			log.error("Exception while consuming appointment event in inbox:{}", request.getAppointmentId(), e);
    		} finally {
    			if (lockOpt.isPresent()) {
    				redisLockService.unlock(lockOpt.get());
    			}
    		}
    	} else {
    		log.error("Empty appointment event received in request or action type is not whitelisted for appointment event");
    		throw new BadRequestException(ErrorCode.EMPTY_APPOINTMENT_EVENT_RECEIVED);
    	}
    }

	private LastMessageMetaData updateLastMessageMetaDataForAppointment(AppointmentEventDto request,
			MessengerContact messengerContact) {
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		if (request.getUserId() != null) {
		    lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.SEND.toString());
		} else {
		    lastMessageMetaData.setLastMessageType(MessageDocument.CommunicationDirection.RECEIVE.toString());
		    lastMessageMetaData.setLastReceivedMessageSource(Source.APPOINTMENT.getSourceId());
		}
		lastMessageMetaData.setLastMessageSource(Source.APPOINTMENT.getSourceId());
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetaData));
		return lastMessageMetaData;
	}

    private Optional<ContactDocument> createContactDocument(AppointmentEventDto appointmentEventDto, BusinessDTO businessDTO, MessengerContact mc, Integer accountId, CustomerDTO customer, UserDTO userDTO) {
    	LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument()).addBusinessInfo(businessDTO)
        		.addMessengerContactInfo(mc).addCustomerInfo(customer).addAppointmentInfo(appointmentEventDto, userDTO)
        		.addLastMessageSenderDetail(userDTO, lastMessageMetadataPOJO.getLastMessageType())
        		.addViewedStatus(mc);
        ContactDocument cd = contactBuilder.build();
        cd.setL_appointment_activity_on(appointmentEventDto.getEventTime());
        cd.setC_tag(mc.getTag());
        if (!mc.getIsRead()) {
            markConversationAsOpenAndUnread(cd, mc);
        }
        cd.setHide(false);
        cd.setL_appointment_activity_on(appointmentEventDto.getEventTime());
        boolean updated = messengerContactService.upsertContactDocumentOnES(cd, mc.getId().toString(), accountId);
        redisHandler
                .updateLastEventCache(businessDTO.getEnterpriseId() != null ? businessDTO.getEnterpriseId().toString()
                        : businessDTO.getBusinessId().toString());
        if (!updated) {
            throw new MessengerException(ErrorCode.ERROR_UPDATING_CONVERSATION);
        }
        return Optional.of(cd);
    }

    private ContactDocument updateExistingConversation(LastMessageMetaData lastMessageMetaData, UserDTO userDTO,
    		AppointmentEventDto appointmentEventDto, MessengerContact messengerContact, BusinessDTO business,
    		CustomerDTO customerDTO) {
        Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(appointmentEventDto.getAccountId(), messengerContact.getId());
        if (contactDocumentOptional.isPresent()) {
            ContactDocument contactDocument = contactDocumentOptional.get();
            ContactDocument.Builder contactBuilder = new ContactDocument.Builder(contactDocument);
            contactBuilder.addLastMessageSenderDetail(userDTO, lastMessageMetaData.getLastMessageType());
            contactBuilder.addViewedStatus(messengerContact);
            contactDocument = contactBuilder.build();
//            removed because this code overiding the read/unread on business action
//            if (Objects.nonNull(messengerContact.getIsRead()) && !messengerContact.getIsRead()) {
//                markConversationAsOpenAndUnread(contactDocument, messengerContact);
//            }
            // as if appointment is added or updated conversation if hidden should be marked
            // as unhidden.
            contactDocument.setHide(false);
            contactDocument.setB_alias(business.getBusinessAlias());
            contactDocument.setL_appointment_activity_on(appointmentEventDto.getEventTime());
            AppointmentUtil.updateAppointmentDetails(appointmentEventDto, contactDocument, userDTO, null);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            contactDocument.setUpdatedAt(simpleDateFormat.format(new Date()));
            if(Objects.nonNull(customerDTO.getIsActive())) {
            	contactDocument.setIsActive(customerDTO.getIsActive());
            }
            boolean contactUpdated = messengerContactService.updateContactOnES(messengerContact.getId(), contactDocument, business.getRoutingId(), business.getEnterpriseId(), business.getBusinessId());
            if (!contactUpdated) {
                throw new MessengerException(ErrorCode.ERROR_UPDATING_CONVERSATION);
            }
            return contactDocument;
        } else {
            log.error("No contact Document exist in ES for Mcid:{} , businessId:{}", messengerContact.getId(), messengerContact.getBusinessId());
            throw new NotFoundException(ErrorCode.NO_CONTACT_DOCUMENT_EXIST_IN_ES);
        }
    }

    private void markConversationAsOpenAndUnread(ContactDocument contactDocument, MessengerContact messengerContact) {
        contactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
        contactDocument.setViewedBy(new ArrayList<>());
        contactDocument.setC_read(false);
        messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);
    }

	@Override
	public void appointmentSpecialistImageUpdate(AppointmentSpecialistImageUpdateRequest request) {
		Preconditions.checkArgument(request.getAccountId()!=null, "AccountId must be present");
		Preconditions.checkArgument(request.getSpecialistId()!=null, "SpecialistId must be present");
		Preconditions.checkArgument(StringUtils.isNotEmpty(request.getImageUrl()), "Specialist imageUrl must be present");
		
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("accountId", request.getAccountId());
		messageFilter.put("specialistId", request.getSpecialistId());

		Map<String, Object> data = new HashMap<>();
		data.put("messageFilter", messageFilter);

		ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
		builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.UPDATE_SPECIALIST_IMAGE_BY_ID).freeMarkerDataModel(data);

		Map<String, Object> scriptData = new HashMap<>();
		scriptData.put("inline", "ctx._source.appointmentInfo.specialistImageUrl=params.specialistImageUrl;ctx._source.lastUpdateDate=params.lastUpdateDate");
		builder.scriptParam(scriptData);

		Map<String, Object> params = new HashMap<>();
		params.put("specialistImageUrl", request.getImageUrl());
		params.put("lastUpdateDate", (new Date()).getTime());
		builder.params(params);

		boolean updateByQueryResponse = elasticSearchService.updateByQueryWithRefresh(builder.build(),false);
		if (!updateByQueryResponse){
			log.error("Error in updating specialist imageUrl for specialistId:{}", request.getSpecialistId());
			return;

        }
        log.info("Specialist imageUrl updated for specialistId:{}", request.getSpecialistId());
    }

    @Override
    public void createAppointmentConfirmationContext(AppointmentConfirmCreateContextRequest request) {
        if (Objects.nonNull(request) && Objects.nonNull(request.getAppointmentId()) && Objects.nonNull(request.getAccountId()) && Objects.nonNull(request.getBusinessId())) {
            AppointmentApiResponseDto appointmentApiResponseDto = appointmentService.getAppointmentDetails(request.getAppointmentId(),
                    request.getAccountId());
            BusinessDTO business = businessService.getBusinessDTO(request.getBusinessId());
            if(Objects.isNull(business)){
                log.warn("getMessageV2: businessDTO returned null from core-service for businessId {}",
                        request.getBusinessId());
                throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
            }
            CustomerDTO customerDTO = contactService.findById(request.getCustomerId());
            if (customerDTO == null) {
                log.error(
                        "consumeAppointmentEventV1: appointment event not saved as customer returned by kontacto-service is null for customerId {}",
                        request.getCustomerId());
                throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
            }
            MessengerContact messengerContact = messengerContactService.findOrCreate(business, MessageTag.INBOX.getCode(), customerDTO);
            AppointmentConfirmationContextCachedDataDto appointmentConfirmationContext = appointmentConfirmationService.checkAndGetAppointmentConfirmationContext(business, messengerContact);
            if (Objects.nonNull(appointmentConfirmationContext) && request.getSentOn().getTime() > appointmentConfirmationContext.getEventTime()) {
                appointmentConfirmationService.updateAppointmentConfirmationContext(business.getAccountId().toString(), business.getBusinessId().toString(), messengerContact.getId().toString(), appointmentApiResponseDto.getExtAppointmentId(), request.getReviewRequestId(), request.getSentOn().getTime(), appointmentApiResponseDto.getPmsId());
            } else if (Objects.isNull(appointmentConfirmationContext) && appointmentApiResponseDto.getStartTime() > request.getSentOn().getTime()) {
                appointmentConfirmationService.updateAppointmentConfirmationContext(business.getAccountId().toString(), business.getBusinessId().toString(), messengerContact.getId().toString(), appointmentApiResponseDto.getExtAppointmentId(), request.getReviewRequestId(), request.getSentOn().getTime(), appointmentApiResponseDto.getPmsId());
            }
        }
    }
}
