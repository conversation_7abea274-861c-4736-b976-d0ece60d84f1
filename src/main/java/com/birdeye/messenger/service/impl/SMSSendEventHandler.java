package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.AppointmentConfirmationService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.PulseSurveyContextService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SamayService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class SMSSendEventHandler extends MessageEventHandlerAbstract{
	
	@Autowired
    protected SendMessageService sendMessageService;
	private final MessengerEvent EVENT = MessengerEvent.SMS_SEND;
	@Autowired
	protected SmsService smsService;
	@Autowired
	protected MessengerMessageService messengerMessageService;
	@Autowired
    protected FacebookEventService facebookEventService;
	@Autowired
    protected CommonService commonService;
	@Autowired
	protected KafkaService kafkaService;
	
	@Autowired
	protected PulseSurveyContextService pulseSurveyContextService;
	
	@Autowired
	protected PulseSurveyService pulseSurveyService;
	
	@Autowired
	private RedisLockService redisLockService;
	
	@Autowired
	private SamayService samayService;

	@Autowired
	private AppointmentConfirmationService appointmentConfirmationService;

	@Autowired
	private RedisHandler redisHandler;

	@Autowired
	private ElasticSearchExternalService elasticsearchService;

	private final WhatsappMessageService whatsappMessageService;

    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = communicationHelperService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }
    
    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        CustomerDTO customerDTO = messageDTO.getCustomerDTO();
        if (Objects.isNull(customerDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
            //customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
			customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
            dto.setCustomerDTO(customerDTO);
        }
        return customerDTO;
    }
    
    @Override
    UserDTO getUserDTO(MessageDTO messageDTO) {
    	UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
        	userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }
    
    
    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
    	if(messageDTO.getSource().equals(Source.WEB_CHAT.getSourceId())) {
    		messageDTO.setMessageTag(MessageTag.UNREAD);
		}
    	else {
    		messageDTO.setMessageTag(MessageTag.INBOX);
		}
    	return messageDTO.getMessageTag();
    }
    
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
    	SendMessageDTO dto = (SendMessageDTO) messageDTO;
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
		if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
    		messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
    				.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
    						messengerMediaFile.getExtension(),messengerMediaFile.getMessageId().toString()))
    				.collect(Collectors.toList()));
    	}
		messageDocumentDTO.setSecureFaq(messageDTO.isSecureFaq());
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		
		return messageDocumentDTO;
    }
    
	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        /*LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageSource(Source.SMS.getSourceId());*/
        //TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
        //messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
        messengerContact.setLastResponseAt(((SendMessageDTO) messageDTO).getConversationDTO().getSentOn());
        messengerContact.setLastMsgOn(new Date());
        messengerContact.setUpdatedAt(new Date());
		UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.SMS.name());
		lastMessageMetadataPOJO.setLastMessageSource(Source.SMS.getSourceId());
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
            messengerContact.setLastMessage("Sent an attachment");
        } else {
            messengerContact.setLastMessage(sendMessageDTO.getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), getCustomerDTO(messageDTO).getPhone(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        if(messengerContact.getBlocked()) {
			throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.CONTACT_IS_BLOCKED, 
					ComponentCodeEnum.CUSTOMER, HttpStatus.BAD_REQUEST));
		}
        return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
        	SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
	}
	
	
	@Override
	public SendResponse handle(MessageDTO messageDTO) throws Exception {
		if(!messageDTO.isBOT()) {
			messageDTO.setMsgTypeForResTimeCalc("S");
		}
		SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
		if(messages.isEmpty()) {
			return null;
		}
		int routeId=getBusinessDTO(messageDTO).getEnterpriseId()!=null?getBusinessDTO(messageDTO).getEnterpriseId():getBusinessDTO(messageDTO).getBusinessId();
		SendResponse sendResponse = new SendResponse((SendMessageDTO) messageDTO, messages, facebookEventService.isFBSendAvailable(getMessengerContact(messageDTO), routeId), true);
		WARestrictedFlags waRestrictedFlags = whatsappMessageService.isWAFreeflowSendAvailable(getMessengerContact(messageDTO), routeId);
		sendResponse.setReplyFromWAReceived(waRestrictedFlags.getReplyFromWAReceived());
		sendResponse.setRestrictWAReply(waRestrictedFlags.getRestrictWAReply());
		sendResponse.setMessages(messages);
		sendResponse.setLastMsgSource(messageDTO.getSource());

		return sendResponse;
	}
	
	private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
		SendMessageDTO sendMessageDTO =(SendMessageDTO) messageDTO;
		getBusinessDTO(messageDTO);
		sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
		CustomerDTO customerDto = getCustomerDTO(messageDTO);
		if(Objects.isNull(customerDto) || Objects.isNull(customerDto.getPhoneE164())) {
			return new TreeSet<>();
		}
		Optional<Lock> lockOpt = Optional.empty();
		String lockKey = "";
		try {
			lockKey = Constants.CUSTOMER_ID_PREFIX+customerDto.getId();
			lockOpt = redisLockService.tryLock(lockKey);
			if (lockOpt.isPresent()){
				return lockAcquiredSendSms(messageDTO, sendMessageDTO, customerDto);
			}else {
				//Reject such events
				log.warn(new ErrorMessageBuilder(ErrorCode.SMS_SEND_LOCK, ComponentCodeEnum.SMS)
						.message("Unable to acquire lock key: {}", lockKey).build());
				throw new RedisLockException(ErrorCode.SMS_SEND_LOCK);
			}
		}finally {
			if (lockOpt.isPresent()){
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

	private SortedSet<MessageResponse.Message> lockAcquiredSendSms(MessageDTO messageDTO, SendMessageDTO sendMessageDTO,
			CustomerDTO customerDto) throws Exception {
		commonService.setMessageBody(sendMessageDTO, customerDto, "sms");
		if(!Source.MOBILE.getSourceId().equals(messageDTO.getSource())) {
			sendMessageDTO.setSource(Source.SMS.getSourceId());
		}
		sendMessageDTO.setFromBusinessId(getMessengerContact(messageDTO).getBusinessId());
		MessengerEvent event= MessengerEvent.SMS_SEND;
		messageDTO.setSentThrough(SentThrough.WEB);
		if (StringUtils.isNotBlank(sendMessageDTO.getMediaurl()) || CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			event= MessengerEvent.MMS_SEND;
		}
		SortedSet<MessageResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
		try {
			if (event.equals(MessengerEvent.MMS_SEND)) {
				sendMessageDTO.setMessengerMediaFiles(new ArrayList<MessengerMediaFile>());
				String text=sendMessageDTO.getBody();
				for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
					MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
					sendMessageDTO.setMediaurl(mediaFile.getUrl());
					if (media < sendMessageDTO.getMediaUrls().size() - 1) {
						sendMessageDTO.setBody(null);
					} else {
						sendMessageDTO.setBody(text);
					}
					MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
					messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
					messageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
					if(media!=0) {
						messageDTO.setPartOfExistingMessage(true);
					}
					sendSms(messageDTO,event);
				}
			} else {
				sendSms(messageDTO,event);
			}
		} catch (Exception e) {
			log.error("Error occurred while sending SMS message: {}",e);
		}
		if(sendMessageDTO.getConversationDTO() == null || sendMessageDTO.getConversationDTO().getId() == null) {
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		super.handle(sendMessageDTO);
		MessageResponse.Message message = new SendResponse.Message(sendMessageDTO.getUserDTO(), sendMessageDTO.getConversationDTO(), sendMessageDTO.getMessengerMediaFiles(), messageDTO.getBusinessDTO().getTimeZoneId());
		message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
		robinService.updateChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), sendMessageDTO.getUserDTO(),Source.SMS.name());
		messages.add(message);
		appointmentConfirmationService.checkAndDeleteAppointmentConfirmationContext(sendMessageDTO.getBusinessDTO(),getMessengerContact(messageDTO));
		return messages;
	}
	
	private boolean hasExistingOutboundMessages(MessageDTO messageDTO) {
		try {
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);

			ESFindByFieldRequest<MessageDocument> esFindRequest = new ESFindByFieldRequest.Builder<MessageDocument>()
				.setIndex(Constants.Elastic.MESSAGE_INDEX)
				.setRoutingId(businessDTO.getAccountId())
				.setDocumentType(MessageDocument.class)
				.setMustFields("c_id", String.valueOf(getMessengerContact(messageDTO).getId()))
				.setMustFields("source", "1") // SMS source
				.setMustFields("communicationDirection", "SEND")
				.build();

			List<MessageDocument> results = elasticsearchService.findDocumentByField(esFindRequest);
			return results != null && !results.isEmpty();
		} catch (Exception e) {
			log.error("Error checking for existing outbound messages for messenger contact {}: {}", getMessengerContact(messageDTO).getId(), e.getMessage());
			return true; 
		}
	}

	private void sendSms(MessageDTO messageDTO, MessengerEvent event) throws Exception {
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		UserDTO userDTO = getUserDTO(messageDTO);
		//Removing Txt STOP to unsub
//		appendUnsubTextForFirstInteraction(messageDTO, sendMessageDTO);
		ConversationDTO conversationDTO = new ConversationDTO(smsService.saveSMS(sendMessageDTO));
		PulseSurveyContext context = handlePulseSurveyContextOnSend(messageDTO, conversationDTO.getId());
		log.info("Send SMS, Returned Context {}", context);
		if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
			messageDTO.getCustomerDTO().setOngoingPulseSurvey(true);
		} else {
			messageDTO.getCustomerDTO().setOngoingPulseSurvey(false);
		}
		conversationDTO.setSentThrough(messageDTO.getSentThrough());
		conversationDTO.setMessengerContactId(getMessengerContact(messageDTO).getId());
		messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);
		sendMessageService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), conversationDTO.getId());
		sendMessageDTO.setConversationDTO(conversationDTO);
		sendMessageService.pushSendRequestToKafka(sendMessageDTO,event,getUserDTO(messageDTO),true);
		if(Objects.nonNull(messageDTO.getMessengerMediaFileDTO())) {
			MessengerMediaFile  messengerMediaFile=new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO());
			messengerMediaFile.setExtension(messageDTO.getMessengerMediaFileDTO().getFileExtension());
			messengerMediaFile.setMessageId(conversationDTO.getId());
			messageDTO.getMessengerMediaFiles().add(messengerMediaFile);
		}
	}

	private void appendUnsubTextForFirstInteraction(MessageDTO messageDTO, SendMessageDTO sendMessageDTO) {
		
		if (sendMessageDTO.getTemplateId() != null) {
	        return;
	    }
		// Check if this is the first outbound message for this messenger contact
		String redisKey = Constants.FIRST_OUTBOUND_MESSAGE_CACHE + ":" + getMessengerContact(messageDTO).getId();
		String hasFirstMessage = redisHandler.getKeyValueFromRedis(redisKey);

		if (hasFirstMessage == null) {
			// If not in Redis, check ES for existing messages
			boolean hasExistingMessages = hasExistingOutboundMessages(messageDTO);

			if (!hasExistingMessages) {
				// This is the first outbound message, append unsubscribe text
				if (StringUtils.isNotBlank(sendMessageDTO.getBody())) {
					sendMessageDTO.setBody(sendMessageDTO.getBody() + "\n\n" + CacheManager.getInstance().getCache(SystemPropertiesCache.class)
							.getProperty(Constants.STOP_UNSUBSCRIBE_TEXT, Constants.STOP_UNSUBSCRIBE_TEXT_DEFAULT));
					sendMessageDTO.setIsStopUnsubscribeTextPresent(true);
				}
				// Set flag in Redis with 5-day TTL
				redisHandler.setOpsForValueWithExpiry(redisKey, "true", (long)Constants.UNSUBSCRIBE_CACHE_TTL_DAYS, TimeUnit.DAYS);
				log.info("Appended unsub text for MC:{}", getMessengerContact(messageDTO).getId());
			} else {
				// Messenger contact has existing messages, set flag in Redis to prevent future checks
				redisHandler.setOpsForValueWithExpiry(redisKey, "true", (long)Constants.UNSUBSCRIBE_CACHE_TTL_DAYS, TimeUnit.DAYS);
			}
		}
	}

	private PulseSurveyContext handlePulseSurveyContextOnSend(MessageDTO messageDTO, Integer smsId) throws Exception {
		CustomerDTO customerDto = getCustomerDTO(messageDTO);
		messageDTO.setCustomerDTO(customerDto);
		//Terminate existing PulseSurveyContext for ongoing PulseSurvey
		PulseSurveyContext exContext = null;
		if(((SendMessageDTO) messageDTO).getOngoingPulseSurvey()!=null && ((SendMessageDTO) messageDTO).getOngoingPulseSurvey()) {
			log.info("Send SMS, Terminating Active Pulse Survey for customerId {}", customerDto.getId());
			exContext = pulseSurveyService.handlePulseSurveyContext(null, customerDto, ((SendMessageDTO) messageDTO).getBusinessDTO());
		}
		//Create context for New NPS message sent from Inbox, Initiated from Inbox then Auto
		if("nps".equalsIgnoreCase(((SendMessageDTO) messageDTO).getSurveyType())) {
			CampaignSMSDto pulseEvent = new CampaignSMSDto(smsId, 1, 
					((SendMessageDTO) messageDTO).getRequestId(), null, null, null, ((SendMessageDTO) messageDTO).getSurveyId(), 1, customerDto.getId());
			exContext = pulseSurveyContextService.createPulseSurveyContext(pulseEvent, ((SendMessageDTO) messageDTO).getFromBusinessId(), customerDto.getId());
			//Submit to Scheduler : X hours
			Long timeOut = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("pulse_survey_timeout_1_hours", 10));
			samayService.submitToScheduler(exContext, "hours",timeOut);
			
		}
		return exContext;
	}
	
	@Override
	public void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}
	
	@Override
	protected boolean isFireBaseSyncEnabled(MessageDTO messageDTO) {
		//handle for robin autoreply in livechat
		boolean isFireBaseSyncEnabled = true;
		if (messageDTO.getUserDTO()!=null && messageDTO.getUserDTO().getId() == MessengerConstants.ROBIN_REPLY_USER) {
			isFireBaseSyncEnabled = false;
		}
		return isFireBaseSyncEnabled;
	}
}
