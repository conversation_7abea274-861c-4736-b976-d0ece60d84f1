package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.ESRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.SurveyEventAudit;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.MessengerData;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.SurveyResponse;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.BusinessAccountType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SurveyService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SurveyEventAuditService;
import com.birdeye.messenger.service.SurveyEventHandlerService;
import com.birdeye.messenger.sro.SurveyEvent;
import com.birdeye.messenger.sro.SurveyNameChangeEvent;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SurveyEventHandlerServiceImpl implements SurveyEventHandlerService {

    @Autowired
    private MessengerContactService messengerContactService;

    @Autowired
    private ContactService  contactService;

    @Autowired
    private BusinessService businessService;

    @Autowired
	private MessengerMessageService messengerMessageService;

	@Autowired
	private MessageService messageService;

	@Autowired
	private ConversationService conversationService;

	@Autowired
    private SurveyEventAuditService surveyEventAuditService;

	@Autowired
    private ElasticSearchExternalService esService;

    @Autowired
    private FirebaseService fcmService;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private RedisLockService redisLockService;

    @Autowired
	private KafkaService kafkaService;
    
    @Autowired
    private ElasticSearchExternalService elasticSearchExternalService;

    @Override
    @Transactional
    public void handleSurveyEvent(SurveyEvent surveyEvent) throws Exception {

    	Optional<Lock> lockOpt = Optional.empty();
        String lockKey = "";
        try {
            SurveyEvent.After surveyEventAfter = surveyEvent.getData().getAfter();
            Long event_time = surveyEvent.getData().getTs_ms();
            BusinessDTO businessDTO = validateRequest(surveyEventAfter);
            if (Objects.isNull(businessDTO)){
                return;
            }
            boolean isMessengerEnabled = businessService.getMessengerEnabled(businessDTO.getAccountId());
            if (((businessDTO.getAccountType() != null && !businessDTO.getAccountType().equals(BusinessAccountType.DIRECT.getType())) ||  (businessDTO.getType() != null && "Product".equals(businessDTO.getType())))  && !isMessengerEnabled){
                log.info("Messenger is disabled for business id and account Type is not direct:{}",businessDTO.getAccountId());
                return;
            }

            boolean isLocationUpdated = isLocationUpdated(surveyEvent);
            if (isLocationUpdated){
                //this is used to handle the case where survey is send from L1 and user select L2 in response
                //BIRDEYE-78946
            	//BIRDEYE-149068 - if before customerId is null, then there wouldn't be any surveys to delete
                deleteSurveyResponseFromInbox(surveyEvent);
            }
            Integer businessId = businessDTO.getBusinessId();
            Integer accountId = businessDTO.getAccountId();
            Integer customerId = surveyEventAfter.getCustomerId();
            lockKey = Constants.CUSTOMER_ID_PREFIX + customerId;
            lockOpt = redisLockService.tryLock(lockKey);
            if (lockOpt.isPresent()){
                List<MessengerContact> messengerContacts =
                        messengerContactService.findByCustomerIdAndBusinessId(customerId, businessId);
                MessengerData messengerData  = null;
                if (CollectionUtils.isNotEmpty(messengerContacts)) {
                	messengerData  = updateExistingConversation(surveyEventAfter,event_time, messengerContacts.get(0), businessDTO, accountId);
                } else {
                	messengerData = createNewConversation(surveyEventAfter,event_time, businessDTO, accountId);
                }
                if (isMessengerEnabled && messengerData != null){
                    fcmService.pushToFireBase(messengerData.getContactDocument(), messengerData.getMessageDocument(), false);
                }
            }else{
                log.info("Lock is already acquired for the key:{}",lockKey);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.SURVEY_EVENT_DELAYED_QUEUE,surveyEvent);
            }
        }catch (Exception e){
            auditFailedEvents(surveyEvent.getData().getAfter(),e.getMessage());
            log.error("Exception while creating or updating survey response in inbox:{}",surveyEvent.getData().getAfter().getId(),e);
            return;
        }finally {
            if (lockOpt.isPresent()){
                redisLockService.unlock(lockOpt.get());
            }
        }
    }

    private MessengerData updateExistingConversation(SurveyEvent.After surveyEventAfter, Long event_time, MessengerContact messengerContact, BusinessDTO businessDTO, Integer accountId) {
        ContactDocument contactDocument = messengerContactService.getContact(accountId, messengerContact.getId());
        MessengerData response = new MessengerData();
            Optional<String> surveyNameOptional = surveyService.getNameBySurveyId(surveyEventAfter.getSurveyId(), businessDTO.getBusinessId());
            if (!surveyNameOptional.isPresent()){
            	log.error("Survey name not found :{}", surveyEventAfter.getSurveyId());
            	return null;
            }
            Integer mcId = messengerContact.getId();
            //BIRDEYE-96528 : Check for changes, only then proceed -> to tackle out of order events
            if (checkForCriticalUpdate(contactDocument, surveyEventAfter,event_time)) {
            	updateLatestSurveyData(surveyEventAfter, contactDocument, messengerContact);
            	markConversationAsOpenAndUnread(contactDocument,messengerContact);
            	contactDocument.setHide(false);
            	updateSurveyDetails(surveyEventAfter,contactDocument,surveyNameOptional.get(),event_time);
            	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            	contactDocument.setUpdatedAt(simpleDateFormat.format(new Date()));
            	messengerContact.setUpdatedAt(new Date());
            	messengerContactService.saveOrUpdateMessengerContact(messengerContact);
            	// create or update messengerMessage
            	createMessengerMessage(surveyEventAfter,messengerContact.getId(),accountId);
                boolean updated = messengerContactService.upsertContactDocumentOnESWithRefresh(contactDocument,
                        mcId.toString(), accountId, false);
                if (updated) {
            		MessageDocument messageDocument = messengerContactService.addSurveyResponseToMessages(surveyEventAfter,accountId,surveyEventAfter.getBusinessId(),messengerContact.getId(),surveyNameOptional.get());
            		if (Objects.isNull(messageDocument)){
            			log.error("Survey name not found :{}", surveyEventAfter.getSurveyId());
            			return null;
            		}
            		response.setMessageDocument(messageDocument);
            	}else {
            		log.error("Error while updating contact Document in ES for survey resp Id:{} , AccountId:{}", surveyEventAfter.getId(), accountId);
            		return null;
            	}
            	response.setContactDocument(contactDocument);
            } else {
            	log.error("No change found for survey update. Rejecting the event :{}", surveyEventAfter);
            	return null;
            }
            return response;
    }

    private boolean checkForCriticalUpdate(ContactDocument contactDocument, SurveyEvent.After surveyEvent,Long event_time) {
    	boolean criticalUpdate = false;
    	Optional<ContactDocument.SurveyResponse> existingSurveyResponse = Optional.empty();
        List<ContactDocument.SurveyResponse> surveyResponses =  contactDocument.getSurveyResponses();
        if (CollectionUtils.isNotEmpty(surveyResponses)){
            existingSurveyResponse =  surveyResponses.stream().filter(sr -> sr.getId().equals(surveyEvent.getId())).findFirst();
        }
        if (existingSurveyResponse.isPresent()){
        	SurveyResponse response = existingSurveyResponse.get();
        	if (!response.getSurveyId().equals(surveyEvent.getSurveyId())) {
        		criticalUpdate = true;
        	}
        	if (!response.getRespDate().equals(surveyEvent.getResponseDate().getTime())) {
        		criticalUpdate = true;
        	}
        	if (response.getOverallScore()!=null && surveyEvent.getOverallScore()!=null && 
        			!response.getOverallScore().equals(surveyEvent.getOverallScore())) {
        		criticalUpdate = true;
        	}
        	if (response.getOverallScore()==null && surveyEvent.getOverallScore()!=null) {
        		//overall score update after create
        		criticalUpdate = true;
        	}
        	if (response.getEvent_time()!=null && event_time<response.getEvent_time()) {
        		// Reject old events from delay queue
        		criticalUpdate = false;
        	}
        } else {
        	//new survey response case
        	criticalUpdate = true;
        }
        return criticalUpdate;
	}

	private void updateLatestSurveyData(SurveyEvent.After surveyEventAfter, ContactDocument contactDocument, MessengerContact messengerContact) {
        if ((contactDocument.getL_survey_rsp_on() == null) || (contactDocument.getL_survey_rsp_on() < surveyEventAfter.getResponseDate().getTime())){
            contactDocument.setL_survey_rsp_on(surveyEventAfter.getResponseDate().getTime());
            messengerContact.setLastSurveyDate(surveyEventAfter.getResponseDate());
        }
    }
    private void markConversationAsOpenAndUnread(ContactDocument contactDocument,MessengerContact messengerContact){
        contactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
        contactDocument.setViewedBy(new ArrayList<>());
        contactDocument.setC_read(false);
        messengerContact.setTag(MessengerTagEnum.UNREAD.getId());
        messengerContact.setViewedBy(null);
        messengerContact.setIsRead(false);
    }

    private MessengerData createNewConversation (SurveyEvent.After surveyEventAfter, Long event_time, BusinessDTO businessDTO, Integer
    accountId){
        MessengerContact mc = null;
        CustomerDTO customerDTO = contactService.findById(surveyEventAfter.getCustomerId());
        MessengerData response = new MessengerData();
        if (Objects.isNull(customerDTO)){
        	log.error("Customer not found :{}", surveyEventAfter.getCustomerId());
        	return null;
        }
        Optional<String> surveyNameOptional = surveyService.getNameBySurveyId(surveyEventAfter.getSurveyId(), businessDTO.getBusinessId());
        if (!surveyNameOptional.isPresent()){
        	log.error("Survey name not found :{}", surveyEventAfter.getCustomerId());
        	return null;
        }
        mc = messengerContactService.createMessengerContactAndMessengerMessage(surveyEventAfter,accountId,customerDTO);
        if(Objects.isNull(mc)){
        	log.error("Error while creating messenger contact and message :{}", surveyEventAfter.getCustomerId());
        	return null;
        }
        if (Objects.nonNull(businessDTO)){
            Optional<ContactDocument> contactDocumentOptional = createContactDocument(surveyEventAfter,event_time,businessDTO,mc,customerDTO,accountId,surveyNameOptional.get());
            if (contactDocumentOptional.isPresent()){
                MessageDocument messageDocument =  messengerContactService.addSurveyResponseToMessages(surveyEventAfter,accountId,surveyEventAfter.getBusinessId(),mc.getId(),surveyNameOptional.get());
                if (Objects.isNull(messageDocument)){
                	log.error("Error while creating messenger message :{}", surveyEventAfter.getCustomerId());
                	return null;
                }
                response.setContactDocument(contactDocumentOptional.get());
                response.setMessageDocument(messageDocument);
                return response;
            }else {
            	log.error("Error while updating contact Document in ES for surveyRespId:{} , AccountId:{}", surveyEventAfter.getId(),accountId);
            	return null;
            }
        }
		return null;
    }

    private BusinessDTO validateRequest(SurveyEvent.After surveyEventAfter){
        if (Objects.isNull(surveyEventAfter.getCustomerId())) {
            log.info("Customer Id is not present for response Id:{}. Hence returning !!", surveyEventAfter.getId());
            return null;
            //throw new MessengerException(ErrorCode.CUSTOMER_NOT_FOUND);
        }
        Integer businessId = surveyEventAfter.getBusinessId();
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
        if (Objects.isNull(businessDTO)) {
        	log.info("No business found :{}", businessId);
            return null;
        }
        return businessDTO;
    }

    private void updateSurveyDetails(SurveyEvent.After surveyEvent, ContactDocument contactDocument,String surveyName, Long event_time){
        Optional<ContactDocument.SurveyResponse> existingSurveyResponse = Optional.empty();
        List<ContactDocument.SurveyResponse> surveyResponses =  contactDocument.getSurveyResponses();
        if (CollectionUtils.isNotEmpty(surveyResponses)){
            existingSurveyResponse =  surveyResponses.stream().filter(sr -> sr.getId().equals(surveyEvent.getId())).findFirst();
        }
        if (existingSurveyResponse.isPresent()){
            DTOtoDocumentConverter(surveyEvent,existingSurveyResponse.get(),surveyName,event_time);
        }else {
            ContactDocument.SurveyResponse surveyResponse =  DTOtoDocumentConverter(surveyEvent,null,surveyName,event_time);
            List<ContactDocument.SurveyResponse> surveyResponseList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(contactDocument.getSurveyResponses())){
                surveyResponseList = contactDocument.getSurveyResponses();
            }
            surveyResponseList.add(surveyResponse);
            contactDocument.setSurveyResponses(surveyResponseList);
        }
    }

    private ContactDocument.SurveyResponse DTOtoDocumentConverter(SurveyEvent.After surveyEvent, ContactDocument.SurveyResponse surveyResponse,String surveyName, Long event_time){
        if (Objects.isNull(surveyResponse)){
            surveyResponse = new ContactDocument.SurveyResponse();
        }
        surveyResponse.setId(surveyEvent.getId());
        surveyResponse.setSurveyId(surveyEvent.getSurveyId());
        surveyResponse.setRespDate(surveyEvent.getResponseDate().getTime());
        surveyResponse.setEvent_time(event_time);
        //this is to get via survey API
        surveyResponse.setSurveyName(surveyName);
        if(surveyEvent.getOverallScore()!=null) {
            DecimalFormat decimalFormat = new DecimalFormat("#.#");
        	surveyResponse.setOverallScore(Float.valueOf(decimalFormat.format(surveyEvent.getOverallScore())));
        }

        return surveyResponse;
    }

    private void  createMessengerMessage(SurveyEvent.After surveyEvent,Integer mcId,Integer accountId){
        MessengerMessage messengerMessage =  messengerMessageService.findByAccountIdAndSurveyResponseId(accountId,surveyEvent.getId());
        if (Objects.nonNull(messengerMessage)){
            //update with current timestamp
            messengerMessage.setCreatedDate(new Date());
            messengerMessageService.updateMessengerMessage(messengerMessage);
        }else {
            messengerMessageService.saveMessengerMessageSurveyDetails(surveyEvent,mcId,accountId);
        }
    }

    private Optional<ContactDocument> createContactDocument(SurveyEvent.After surveyEvent,Long event_time, BusinessDTO businessDTO,MessengerContact mc,CustomerDTO customerDTO,Integer accountId,String surveyName){
        ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
        contactBuilder.addBusinessInfo(businessDTO).addMessengerContactInfo(mc).addCustomerInfo(customerDTO).addSurveyDetails(surveyEvent,surveyName,event_time);
        ContactDocument cd = contactBuilder.build();

        cd.setL_survey_rsp_on(surveyEvent.getResponseDate().getTime());
        cd.setC_tag(MessengerTagEnum.UNREAD.getId());
        cd.setViewedBy(new ArrayList<>());
        cd.setC_read(false);
        DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        cd.setUpdatedAt(df.format(new Date()));
        cd.setHide(false);
        boolean updated = messengerContactService.upsertContactDocumentOnESWithRefresh(cd,mc.getId().toString(),accountId,false);
        if (updated){
            return Optional.of(cd);
        }
        return Optional.empty();
    }

    private void auditFailedEvents(SurveyEvent.After surveyEvent,String message){

        SurveyEventAudit surveyEventAudit = new SurveyEventAudit(surveyEvent.getSurveyId(),surveyEvent.getId(),surveyEvent.getBusinessId(),surveyEvent.getCustomerId(),"Failed",message,new Date());
        surveyEventAuditService.saveSurveyEventAudit(surveyEventAudit);
    }

    @Override
    public void updateSurveyName(SurveyNameChangeEvent surveyNameChangeEvent) throws Exception {
        BusinessDTO businessDTO = validateRequest(surveyNameChangeEvent);
        if (businessDTO == null){
            log.info("validation failed!! Hence returning.");
            return;
        }
        boolean updated = updateSurveyNameForTokenCase(surveyNameChangeEvent);
        if(!updated){
            boolean status = updateSurveyNameInContactDocument(surveyNameChangeEvent.getData().getAfter(),businessDTO.getRoutingId());
            if (status){
                updateSurveyNameInMessengerMessage(surveyNameChangeEvent.getData().getAfter(),businessDTO.getRoutingId());
            }
        }

    }

    private boolean updateSurveyNameForTokenCase(SurveyNameChangeEvent surveyNameChangeEvent) {
        String surveyName = StringUtils.isNotEmpty(surveyNameChangeEvent.getData().getAfter().getSurveyName()) ?
                surveyNameChangeEvent.getData().getAfter().getSurveyName() : "";
        Integer surveyId = surveyNameChangeEvent.getData().getAfter().getId();
        if(StringUtils.isNotEmpty(surveyName) && (surveyName.contains("[Business Name]") || surveyName.contains("[Location address]"))) {
            Set<Integer> businessIds = new HashSet<>();
            Map<String, Object> data = new HashMap<>();
            data.put("surveyId", String.valueOf(surveyId));
            Integer accountId = surveyNameChangeEvent.getData().getAfter().getBusinessId();
            ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
                    .addRoutingId(accountId)
                    .addTemplateAndDataModel(Constants.Elastic.GET_DISTINCT_BID_AGG_FOR_SURVEY_ID, data).build();
            SearchResponse searchResponse = esService.getSearchResult(esRequest);
            if(searchResponse != null && searchResponse.getAggregations() != null){
                Terms bizIdsAgg = searchResponse.getAggregations().get("bizIds");
                if(CollectionUtils.isNotEmpty(bizIdsAgg.getBuckets())){
                    for(Terms.Bucket aggBucket : bizIdsAgg.getBuckets()) {
                        businessIds.add(aggBucket.getKeyAsNumber().intValue());
                    }
                } else {
                    log.info("no bid bucket found!");
                    return false;
                }
            } else {
                log.info("ES search response is null!");
                return false;
            }
            if(CollectionUtils.isEmpty(businessIds)){
                log.info("no business Ids found for the survey Id: {}", surveyId);
                return false;
            }
            Map<Integer, String> businessIdSurveyNameMap = surveyService.getTokensReplacedSurveyNames(surveyId, businessIds);
            for(Integer businessId : businessIds){
                if(businessIdSurveyNameMap.containsKey(businessId)) {
                    boolean updatedForContactIndex = updateSurveyNameInContactDocumentForBusinessId(surveyId, businessIdSurveyNameMap.get(businessId), businessId, accountId);
                    if(updatedForContactIndex) {
                        updateSurveyNameInMessengerMessageForBusinessId(surveyId, businessIdSurveyNameMap.get(businessId), businessId, accountId);
                    }
                }
            }
        } else {
            log.info("survey name is either empty and doesn't contains tokens!");
            return false;
        }
        return true;
    }


    private BusinessDTO validateRequest(SurveyNameChangeEvent event){
        if (!event.getData().getOp().equals("u")){
            log.info("We need to capture only update event!!");
            return null;
        }
        if (event.getData().getBefore().getSurveyName().equals(event.getData().getAfter().getSurveyName())){
            log.info("survey name not updated!! , Hence returning!!");
            return null;
        }
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(event.getData().getAfter().getBusinessId());
        if (Objects.isNull(businessDTO)){
        	log.error("No business found : {}", event.getData().getAfter().getBusinessId());
        	return null;
        }
        return businessDTO;
    }

    private boolean updateSurveyNameInContactDocument(SurveyNameChangeEvent.After event,Integer routingId){
        log.info("updating survey name in contact document for survey Id: {}",event.getId());
        Map<String, Object> data = new HashMap<>();
        data.put("surveyId", event.getId().toString());
        com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
        builder.index(Constants.Elastic.CONTACT_INDEX).queryTemplateFile(Constants.Elastic.GET_CONVERSATIONS_BY_SURVEY_ID).freeMarkerDataModel(data);
        Map<String, Object> scriptData = new HashMap<>();
        scriptData.put("inline", "for ( item in ctx._source.surveyResponses ) { if(item.surveyId == "+event.getId()+") { item.surveyName = "+"'"+ event.getSurveyName()+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'"  +"} }" );
        builder.scriptParam(scriptData);
        builder.routingId(routingId);
        boolean updateByQueryResponse = esService.updateByQueryWithRefresh(builder.build(),false);
        if (!updateByQueryResponse){
            log.info("[Contact Document] Survey name updation failed for id:{},error:{}",event.getId());
            return false;
        }
        log.info("[Contact Document] Survey name updation success for id:{}",event.getId());
        return true;
    }

    private boolean updateSurveyNameInContactDocumentForBusinessId(Integer surveyId, String surveyName, Integer businessId, Integer routingId){
        log.info("updating survey name in contact document for survey Id: {} and business Id: {}", surveyId, businessId);
        Map<String, Object> data = new HashMap<>();
        data.put("surveyId", String.valueOf(surveyId));
        data.put("businessId", String.valueOf(businessId));
        com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
        builder.index(Constants.Elastic.CONTACT_INDEX).queryTemplateFile(Constants.Elastic.GET_CONVERSATIONS_BY_BID_AND_SURVEY_ID).freeMarkerDataModel(data);
        Map<String, Object> scriptData = new HashMap<>();
        scriptData.put("inline", "for ( item in ctx._source.surveyResponses ) { if(item.surveyId == "+surveyId+") { item.surveyName = "+"'"+surveyName+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'"  +"} }" );
        builder.scriptParam(scriptData);
        builder.routingId(routingId);
        boolean updateByQueryResponse = esService.updateByQueryWithRefresh(builder.build(),false);
        if (!updateByQueryResponse){
            log.info("[Contact Document] Survey name update failed for survey Id:{} and business Id: {}", surveyId, businessId);
            return false;
        }
        log.info("[Contact Document] Survey name update success for survey Id:{} and businessId: {}", surveyId, businessId);
        return true;
    }

    private boolean updateSurveyNameInMessengerMessage(SurveyNameChangeEvent.After event, Integer routingId) {
        log.info("updating survey name in Messenger message for survey Id: {}",event.getId());
        Map<String, Object> data = new HashMap<>();
        data.put("surveyId", event.getId().toString());
        com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
        builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_BY_SURVEY_ID).freeMarkerDataModel(data);
        Map<String, Object> scriptData = new HashMap<>();
        scriptData.put("inline", "ctx._source.survey_detail.surveyName = "+"'"+ event.getSurveyName()+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
        builder.scriptParam(scriptData);
        builder.routingId(routingId);
        boolean updateByQueryResponse = esService.updateByQueryWithRefresh(builder.build(),false);
        if (!updateByQueryResponse){
            log.info("[Messenger Message] Survey name updation failed for id:{},error:{}",event.getId());
            return false;
        }
        log.info("[Messenger Message] Survey name updation success for id:{}",event.getId());
        return true;

    }

    private boolean updateSurveyNameInMessengerMessageForBusinessId(Integer surveyId, String surveyName, Integer businessId, Integer routingId) {
        log.info("updating survey name in Messenger message for survey Id: {} and business Id: {}", surveyId, businessId);
        Map<String, Object> data = new HashMap<>();
        data.put("surveyId", String.valueOf(surveyId));
        data.put("businessId", String.valueOf(businessId));
        com.birdeye.messenger.es.sro.ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest());
        builder.index(Constants.Elastic.MESSAGE_INDEX).queryTemplateFile(Constants.Elastic.GET_MESSAGES_BY_BID_AND_SURVEY_ID).freeMarkerDataModel(data);
        Map<String, Object> scriptData = new HashMap<>();
        scriptData.put("inline", "ctx._source.survey_detail.surveyName = "+"'"+ surveyName+"';ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'");
        builder.scriptParam(scriptData);
        builder.routingId(routingId);
        boolean updateByQueryResponse = esService.updateByQueryWithRefresh(builder.build(),false);
        if (!updateByQueryResponse){
            log.info("[Messenger Message] Survey name update failed for survey Id: {} and business Id: {}", surveyId, businessId);
            return false;
        }
        log.info("[Messenger Message] Survey name update success for survey Id: {} and business Id: {}", surveyId, businessId);
        return true;

    }

    private boolean isLocationUpdated(SurveyEvent surveyEvent){
        if (surveyEvent.getData().getBefore() != null && surveyEvent.getData().getOp().equals("u")){
			if (!(surveyEvent.getData().getBefore().getBusinessId()
					.equals(surveyEvent.getData().getAfter().getBusinessId()))
					&& surveyEvent.getData().getBefore().getCustomerId() != null) {
				   return true;
            }
        }
        return false;
    }

    private void deleteSurveyResponseFromInbox(SurveyEvent surveyEvent) {
        Integer businessId = surveyEvent.getData().getBefore().getBusinessId();
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
        if (businessDTO == null){
        	log.info("No business found :{}", businessId);
        	return;
        }
        Integer accountId = businessDTO.getAccountId();
        Integer customerId = surveyEvent.getData().getBefore().getCustomerId();
        Integer surveyResponseId = surveyEvent.getData().getBefore().getId();
        List<MessengerContact> mcs = messengerContactService.findByCustomerIdAndBusinessId(customerId,businessId);
        if (CollectionUtils.isNotEmpty(mcs)){
            MessengerContact mc = mcs.get(0);
            Integer mcId=mc.getId();  
            Optional<ContactDocument> contactDocumentOptional= messengerContactService.getContactDocument(accountId,mcId);
            if (contactDocumentOptional.isPresent()){
                ContactDocument contactDocument = contactDocumentOptional.get();
                if (isSurveyOnlyConversation(contactDocument)){
                    if (contactDocument.getSurveyResponses().get(0).getId().equals(surveyResponseId)){
                        //delete messenger contact
                        boolean deleted = messengerContactService.deleteMessengerContact(mcId);
                        if (deleted) {
                            conversationService.deleteConversation(mc.getId(),accountId,RefreshPolicy.IMMEDIATE);
                            messageService.deleteMessagesByMcIdWithRefresh(mcId, accountId, false);
                            messageService.deleteMessagesDataFromDBOnCustomerDelete(mcId, customerId);
                        }
                    }
                }else{
                    Long lastSurveyRespOn =  conversationService.getLastReviewOrSurveyOn(contactDocument,accountId,surveyResponseId,Constants.SURVEY_RESPONSE);
                    contactDocument.setL_survey_rsp_on(lastSurveyRespOn);
                    mc.setLastSurveyDate(lastSurveyRespOn == null? null: new Date(lastSurveyRespOn));
                    List<ContactDocument.SurveyResponse> surveyResponses =  contactDocument.getSurveyResponses().stream().filter(sr -> !surveyResponseId.equals(sr.getId())).collect(Collectors.toList());
                    contactDocument.setSurveyResponses(surveyResponses);
                    messengerContactService.saveOrUpdateMessengerContact(mc);
                    messengerMessageService.deleteBySurveyResponseId(surveyResponseId);
                    messengerContactService.upsertContactDocumentOnESWithRefresh(contactDocument, contactDocument.getM_c_id().toString(),accountId, false);
                    messageService.deleteSurveyResponseById(surveyResponseId,accountId);
                }
            }
        }

    }

    private boolean isSurveyOnlyConversation(ContactDocument contactDocument) {
        if (StringUtils.isNotEmpty(contactDocument.getL_msg())){
            return false;
        }
        if (CollectionUtils.isNotEmpty(contactDocument.getReviews())){
            return false;
        }
        if (CollectionUtils.isNotEmpty(contactDocument.getSurveyResponses()) && contactDocument.getSurveyResponses().size() == 1){
            return true;
        }
        return false;
    }

}
