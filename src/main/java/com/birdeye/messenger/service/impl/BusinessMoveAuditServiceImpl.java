package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.BusinessMoveAudit;
import com.birdeye.messenger.dao.repository.BusinessMoveAuditRepository;
import com.birdeye.messenger.service.BusinessMoveAuditService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BusinessMoveAuditServiceImpl implements BusinessMoveAuditService {

	@Autowired
	BusinessMoveAuditRepository businessMoveAuditRepository;

	@Override
	public BusinessMoveAudit saveBusinessMoveAudit(BusinessMoveAudit businessMoveAudit) {
		return businessMoveAuditRepository.save(businessMoveAudit);
	}

	@Override
	public BusinessMoveAudit findBusinessMoveAudit(Integer sourceBusinessId, String eventType) {
		return businessMoveAuditRepository.findBusinessMoveAudit(sourceBusinessId,eventType);
	}

	@Override
	public BusinessMoveAudit findBusinessMoveAuditById(Integer id) {
		return businessMoveAuditRepository.findById(id).orElse(null);
	}
}
