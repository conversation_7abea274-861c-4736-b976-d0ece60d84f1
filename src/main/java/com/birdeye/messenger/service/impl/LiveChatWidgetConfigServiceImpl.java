package com.birdeye.messenger.service.impl;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.LiveChatWidgetConfig;
import com.birdeye.messenger.dao.repository.LiveChatMessageConfigRepository;
import com.birdeye.messenger.service.LiveChatWidgetConfigService;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LiveChatWidgetConfigServiceImpl implements LiveChatWidgetConfigService {

    private final LiveChatMessageConfigRepository liveChatMessageConfigRepository;
   
    @Override
    public LiveChatWidgetConfig getLiveChatMessageConfig(Integer widgetId) {
        return liveChatMessageConfigRepository.findByWidgetId(widgetId);
    }

    @Override
    public LiveChatWidgetConfig saveLiveChatConfig(LiveChatWidgetConfig liveChatWidgetConfig) {
        return liveChatMessageConfigRepository.save(liveChatWidgetConfig);
    }

    @Override
    public void deleteByWidgetId(Integer widgetId) {
        liveChatMessageConfigRepository.deleteByWidgetId(widgetId);
    }

}
