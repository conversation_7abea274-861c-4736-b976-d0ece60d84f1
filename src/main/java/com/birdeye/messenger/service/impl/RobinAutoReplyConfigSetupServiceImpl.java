package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.robin.GetRobinReplyConfigResponse;
import com.birdeye.messenger.dto.robin.RobinAutoReplySetupRequest;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.RobinAutoReplyConfigRepositoryService;
import com.birdeye.messenger.service.RobinAutoReplyConfigSetupService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobinAutoReplyConfigSetupServiceImpl implements RobinAutoReplyConfigSetupService {

    private final RobinAutoReplyConfigRepositoryService robinAutoReplyConfigRepositoryService;

    @Autowired
    private BusinessService businessService;
    @Override
    public void createUpdateRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest) {
       validateRequest(robinAutoReplySetupRequest);
       robinAutoReplyConfigRepositoryService.createUpdateRobinAutoReplyConfiguration(robinAutoReplySetupRequest);
        if(BooleanUtils.isTrue(robinAutoReplySetupRequest.isAllConfig())) {
            robinAutoReplyConfigRepositoryService.updateAllLocation(robinAutoReplySetupRequest);
        }
    }

    private void validateRequest(RobinAutoReplySetupRequest robinAutoReplySetupRequest) {
        if(CollectionUtils.isNotEmpty(robinAutoReplySetupRequest.getBusinessId())){
            robinAutoReplySetupRequest.getBusinessId().removeAll(Collections.singleton(null));
        }
        if(CollectionUtils.isNotEmpty(robinAutoReplySetupRequest.getChannel())){
            robinAutoReplySetupRequest.getChannel().removeAll(Collections.singleton(null));
        }
        if(CollectionUtils.isEmpty(robinAutoReplySetupRequest.getBusinessId())){
            throw new BadRequestException("no businessId present in the request");
        }
        if(CollectionUtils.isNotEmpty(robinAutoReplySetupRequest.getChannel())){
            robinAutoReplySetupRequest.getChannel().stream().filter(channel -> channel.equals(Source.CONTACT_US.name()) && Boolean.TRUE.equals(robinAutoReplySetupRequest.isEnableRobin()))
                    .findFirst()
                    .ifPresent(channel -> {
                        throw new IllegalArgumentException("Robin Cannot be enabled on the channel: " + channel);
                    });
        }
        if(CollectionUtils.isEmpty(robinAutoReplySetupRequest.getChannel())){
            log.info("No channel specified for Robin's auto-reply setup request. Enabling for all channels.");
            List<String> channel = new ArrayList<>();
            channel.add("ALL");
            robinAutoReplySetupRequest.setChannel(channel);
        }
        
        List<String> allowedChannels = Arrays.asList(Source.ALL.name(),Source.FACEBOOK.name(), Source.INSTAGRAM.name(),Source.SMS.name(), Source.GOOGLE.name(),
                Source.APPLE.name(), Source.VOICE_CALL.name(), Source.TWITTER.name(), Source.CONTACT_US.name());
        
        robinAutoReplySetupRequest.getChannel().stream().filter(channel -> !allowedChannels.contains(channel))
        .findFirst()
        .ifPresent(channel -> {
            throw new IllegalArgumentException("Invalid channel: " + channel);
        });
    }

    @Override
    public void deleteRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest) {
        validateRequest(robinAutoReplySetupRequest);
        robinAutoReplyConfigRepositoryService.deleteRobinAutoReplyConfiguration(robinAutoReplySetupRequest);
    }

    @Override
    public Map<Integer, GetRobinReplyConfigResponse> getAllRobinAutoReplyConfig(RobinAutoReplySetupRequest request) {
        validateRequest(request);
        return robinAutoReplyConfigRepositoryService.getAllRobinAutoReplyConfig(request);
    }

    @Override
    public List<GetRobinReplyConfigResponse> getEffectiveRobinAutoReplyConfig(RobinAutoReplySetupRequest request) {
        validateRequest(request);
        List<GetRobinReplyConfigResponse> response = robinAutoReplyConfigRepositoryService.getRobinAutoReplyEffectiveConfig(request);
        return response;
    }

    @Override
    public void evictRobinAutoReplyConfigCache(Integer businessId) {
        BusinessDTO businessDTO = businessService.getBusinessLiteDTO(businessId);
        robinAutoReplyConfigRepositoryService.evictConfigCache(businessDTO.getAccountId());
    }
}
