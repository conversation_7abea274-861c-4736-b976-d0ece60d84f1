package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;
import com.birdeye.messenger.dao.repository.GPTResponseFeedbackAuditRepository;
import com.birdeye.messenger.service.GPTResponseFeedbackService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class GPTResponseFeedbackServiceImpl implements GPTResponseFeedbackService {

	@Autowired
	private GPTResponseFeedbackAuditRepository auditRepo;
	
	@Override
	public GPTResponseFeedbackAudit audit(Integer accountId, Integer businessId, Integer mcId, String queryChannel,
			String queryText) {
		GPTResponseFeedbackAudit audit = new GPTResponseFeedbackAudit();
		audit.setAccountId(accountId);
		audit.setBusinessId(businessId);
		audit.setMcId(mcId);
		audit.setQueryChannel(queryChannel);
		audit.setQuery(queryText);
		audit.setCreatedAt(new Date());
		audit.setUpdatedAt(new Date());
		return save(audit);
	}

	@Override
	public GPTResponseFeedbackAudit save(GPTResponseFeedbackAudit audit) {
		return auditRepo.saveAndFlush(audit);
	}

	@Override
	public GPTResponseFeedbackAudit findById(Integer eventId) {
		Optional<GPTResponseFeedbackAudit> existingAudit = auditRepo.findById(eventId);
		if (!existingAudit.isPresent()){
			log.info("Existing GPTResponseFeedbackAudit not found for id :{}", eventId);
			return null;
		}
		return existingAudit.get();
	}

}
