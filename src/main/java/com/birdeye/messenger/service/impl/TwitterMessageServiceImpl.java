package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dao.entity.TwitterMessage;
import com.birdeye.messenger.dao.repository.TwitterMessageRepository;
import com.birdeye.messenger.service.TwitterMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TwitterMessageServiceImpl implements TwitterMessageService{
    
    @Autowired
    private TwitterMessageRepository twitterMessageRepository;

    @Override
    public TwitterMessage saveAndFlushTwitterMessage(TwitterMessage twitterMessage){
        try {
            return twitterMessageRepository.saveAndFlush(twitterMessage);
        } catch (Exception e) {
            log.error("Duplicate Twitter message: {} ", twitterMessage);
            // throw new DuplicateEntryException(ErrorCode.DUPLICATE_MESSAGE);
        }
        return null;
    }

    @Override
    @Transactional
    public void updateTwitterMessageBody(String messageBody,Integer messageId){
        try {
            twitterMessageRepository.updateTwitterMessageBody(messageBody,messageId);
        } catch (Exception e) {
            log.error("error : {} occurred in updatingFacebookMessage", e.getMessage());
        }
    }
}
