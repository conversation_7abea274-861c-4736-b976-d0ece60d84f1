package com.birdeye.messenger.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.ThankYouNoteEvent;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.StatusEnum;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.ReferrerThankYouNoteConsumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReferrerThankYouNoteConsumerImpl implements ReferrerThankYouNoteConsumer {

    private final MessengerContactService messengerContactService;
    private final ContactService contactService;
    private final FirebaseService firebaseService;
    private final ConversationActivityService conversationActivityService;
    private final BusinessService businessService;
    private final ElasticSearchExternalService elasticSearchService;

    @Override
    public void consumeEvent(ThankYouNoteEvent thankYouNoteEvent) {
        log.info("ReferrerThankYouNoteConsumer: {}", thankYouNoteEvent);

        Integer referrerCid = thankYouNoteEvent.getReferrerCid();
        Integer leadCid = thankYouNoteEvent.getCid();
        Integer accountId = thankYouNoteEvent.getAccountId();
        Integer businessId = thankYouNoteEvent.getBusinessId();

        BusinessDTO businessDTO = businessService.getBusinessDTO(businessId);

        Assert.notNull(businessDTO, "No business found for id " + businessId);
        Assert.isTrue(businessDTO.getAccountId().equals(accountId), String.format("%d business does not belong to account %d", businessDTO.getBusinessId(), accountId));

        MessengerContact referrerMc = messengerContactService.findByCustomerId(referrerCid);
        Assert.notNull(referrerMc, "No messengerContact found for customer id " + referrerCid);

        MessengerContact leadMc = messengerContactService.findByCustomerId(leadCid);
        Assert.notNull(leadMc, "No messengerContact found for customer id " + leadCid);

        CustomerDTO referrer = contactService.findById(referrerCid);
        Assert.notNull(referrer, "No customer found for  id " + referrerCid);

        CustomerDTO lead = contactService.findById(leadCid);
        Assert.notNull(leadMc, "No customer found for  id " + leadCid);

        boolean thankYouNoteActivityExists = conversationActivityService.checkIfReferralActivityExists(referrerMc.getId(), leadMc.getId(), Collections.singletonList(ActivityType.THANK_YOU_NOTE_SENT_REFERRER.getLabel()));

        if(thankYouNoteActivityExists) {
            log.info("ReferrerThankYouNoteConsumer: Thank you note activity already exists {} ", thankYouNoteEvent);
            return;
        }

        ActivityDto referrerActivity = ActivityDto.builder().mcId(referrerMc.getId()).created(new Date())
                .activityType(ActivityType.THANK_YOU_NOTE_SENT_REFERRER)
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .thankYouNoteStatus(thankYouNoteEvent.getThankYouNoteStatus())
                .referrerMcId(referrerMc.getId()).referrerName(referrer.getFirstName()).referrerCId(referrer.getId())
                .referredLeadMcId(leadMc.getId()).referredLeadName(lead.getFirstName()).referredLeadCId(lead.getId())
                .accountId(accountId).businessId(businessId)
                .build();

        conversationActivityService.persistActivityInDatabase(referrerActivity, null);
        conversationActivityService.persistActivityInES(referrerActivity);

        updateReferralActivities(accountId, referrerMc.getId(), leadMc.getId(), thankYouNoteEvent.getThankYouNoteStatus());

        //firebaseService.mirrorOnWeb(accountId, businessId);
        FirebaseDto firebaseDto = new FirebaseDto();
        firebaseDto.setAccountId(accountId);
        firebaseDto.setBusinessId(businessId);
        firebaseDto.setMcId(leadMc.getId());
        firebaseService.mirrorOnWeb(firebaseDto);

    }

    private void updateReferralActivities(Integer accountId, Integer referrerMcId, Integer leadMcId, StatusEnum status) {

        Date updateDate = new Date();

        conversationActivityService.updateReferralActivity(Arrays.asList(ActivityType.REFERRER_LEAD_GEN.getLabel(), ActivityType.REFERRAL_LEAD_GEN.getLabel()),
                referrerMcId, leadMcId, StatusEnum.SUCCESS, updateDate);

        Map<String, Object> params = new HashMap<>();
        params.put("referrerMcId", referrerMcId);
        params.put("leadMcId", leadMcId);
        MessengerFilter data = new MessengerFilter();
        data.setParams(params);
        Map<String, Object> freeMakerData = new HashMap<>();
        freeMakerData.put("data", data);
        Map<String, Object> scriptData = new HashMap<>();
        Long updateTimeStamp = updateDate.getTime();
        String inlineValue = "ctx._source.thankYouNoteStatus=" + "\"" + status + "\"" +
                "; ctx._source.u_time=" + "\"" + updateTimeStamp + "\"" + "; ctx._source.type=" + "\"" + MessageDocument.Type.UPDATE.getType() + "\"" +
                "; ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'";
        scriptData.put("inline", inlineValue);

        ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest())
                .index(Constants.Elastic.MESSAGE_INDEX)
                .queryTemplateFile(Constants.Elastic.GET_REFERRAL_ACTIVITIES_BY_REFERRER_AND_LEAD_ID)
                .routingId(accountId)
                .freeMarkerDataModel(freeMakerData)
                .scriptParam(scriptData);

        boolean response = elasticSearchService.updateByQuery(builder.build());
        if(!response) {
            log.error("updateReferralActivities: Error occurred while doing bulk operation for referrerMcId {} and leadMcId {}", referrerMcId, leadMcId);
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }

    }

}
