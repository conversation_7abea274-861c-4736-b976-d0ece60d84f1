package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.SendFacebookMessage;
import com.birdeye.messenger.dto.SendFacebookMessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.facebook.Messaging;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.FacebookMessageStatusEnum;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.SocialMessageTag;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.FacebookException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class FacebookEventServiceImpl implements FacebookEventService {

	private final SocialService socialService;
	private final FacebookMessageRepository facebookMessageRepository;
	private final MessengerContactService messengerContactService;
	private final RedisHandler redisHandler;

	@Override
	public SendFacebookMessageResponse sendFBCallToSocial(FacebookSendEventHandler handler,MessageDTO messageDTO,String pageId) {
		SendFacebookMessage sendFbMessage=null;
//		String pageId =platformDbRepository.getFacebookPageIdByBusinessId(handler.getBusinessDTO(messageDTO).getBusinessId());
		String body= ((SendMessageDTO) messageDTO).getBody();
		if(StringUtils.isBlank(pageId)) {
			throw new NotFoundException(ErrorCode.FACEBOOKPAGE_NOT_FOUND);
		}
		ResponseEntity<String> res=null;
		MessengerMediaFileDTO messengerMediaFileDTO = messageDTO.getMessengerMediaFileDTO();
		String receiverId=handler.getMessengerContact(messageDTO).getFacebookId();
	      if(messengerMediaFileDTO!=null) {
	    	  String fileType="file";
	    	  if(messengerMediaFileDTO.getContentType()!=null && messengerMediaFileDTO.getContentType().contains("image"))
	    		  fileType="image";
	    	  sendFbMessage=new SendFacebookMessage(fileType, null, pageId, receiverId, messengerMediaFileDTO.getUrl(), SocialMessageTag.HUMAN_AGENT.getName(),((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId());
	    	// send message to facebook
	    	  res = socialService.sendFacebookMessage(sendFbMessage);
	    	  if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
	    		  String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
	    		  if(responseBodyAsString.contains("2018109"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ATTACHMENT_SIZE_EXCEEDS, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018065"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PAGE_MESSAGING_PERMISSION, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018108"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PERSON_NOT_RECEIVING, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("1545041"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PERSON_NOT_AVAILABLE, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("190") || responseBodyAsString.contains("1205"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ACCESS_TOKEN_EXPIRED, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018278"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.SENT_OUTSIDE_ALLOWED_WINDOW, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("10900"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ALREADY_REPLIED_PRIVATELY, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.FACEBOOK_API_ERROR, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    	  }
	      }
	      if(StringUtils.isNotEmpty(body)) {
	    	  sendFbMessage=new SendFacebookMessage(null, body, pageId, receiverId, null, SocialMessageTag.HUMAN_AGENT.getName(),((SendMessageDTO) messageDTO).getType(),((SendMessageDTO) messageDTO).getSocialFeedId());
	    	  // send message to facebook
	    	  res = socialService.sendFacebookMessage(sendFbMessage);
	    	  if (res != null && res.getStatusCode() != null && res.getStatusCode().value() != 200) {
	    		  String responseBodyAsString = StringUtils.isNotBlank(res.toString()) ? res.toString() : "";
	    		  if(responseBodyAsString.contains("2018109"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ATTACHMENT_SIZE_EXCEEDS, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018065"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PAGE_MESSAGING_PERMISSION, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018108"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PERSON_NOT_RECEIVING, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("1545041"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.PERSON_NOT_AVAILABLE, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("190") || responseBodyAsString.contains("1205"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ACCESS_TOKEN_EXPIRED, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("2018278"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.SENT_OUTSIDE_ALLOWED_WINDOW, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else if(responseBodyAsString.contains("10900"))
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.ALREADY_REPLIED_PRIVATELY, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    		  else
	    			  throw new FacebookException(new ErrorMessageBuilder(ErrorCode.FACEBOOK_API_ERROR, ComponentCodeEnum.FB, HttpStatus.BAD_REQUEST));
	    	  }
	      }
	      return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), SendFacebookMessageResponse.class);
	}

	@Override
	public FacebookMessage saveFacebookMessage(MessageDTO messageDTO,String senderId,String recipientId,String messageId,FacebookMessageStatusEnum status) {
		Integer encrypted=0;
		String message;
		MessengerContact messengerContact= messageDTO.getMessengerContact();
		if(messageDTO instanceof SendMessageDTO) {
			message= ((SendMessageDTO) messageDTO).getBody();
		} else {
			FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
	    	Messaging messaging = facebookMessageRequest.getEntry().get(0).getMessaging().get(0);
			message=messaging.getMessage().getText();
			messengerContact.setLastMessage(message);
			if(StringUtils.isNotBlank(message) && messaging.getMessage().getIs_echo()) {
				messengerContact.setLastMessage("You: " +message);
			}
		}

		try {
			if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotBlank(message)) {
				message = EncryptionUtil.encrypt(message,StringUtils.join(senderId, recipientId),StringUtils.join(recipientId, senderId));
				encrypted = 1;
			}
		}catch (Exception e) {
			log.info("Encryption for recieved Facebook message failed: {}", e);
		}
		FacebookMessage fbMessage=new FacebookMessage();
		fbMessage.setCreateDate(new Date());
		fbMessage.setCustomerId(messengerContact.getId());
		fbMessage.setRecipientFacebookId(recipientId);
		fbMessage.setSenderFacebookId(senderId);
		if(StringUtils.isNotBlank(message)) {
			fbMessage.setMessageBody(message);
		}
		if(messageId.substring(0, 2).equalsIgnoreCase("m_"))
			fbMessage.setMessageId(messageId.subSequence(2, messageId.length()).toString());
		else
			fbMessage.setMessageId(messageId);
		fbMessage.setStatus(status.toString());
		fbMessage.setSentOn(new Date());
		fbMessage.setEncrypted(encrypted);
		fbMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
		if(messageDTO.getMessengerMediaFileDTO()!=null && StringUtils.isNotBlank(messageDTO.getMessengerMediaFileDTO().getUrl())) {
			fbMessage.setMediaURL(messageDTO.getMessengerMediaFileDTO().getUrl());
		}
		try {
			facebookMessageRepository.saveAndFlush(fbMessage);
		} catch (Exception e) {
			log.error("Duplicate facebook message message id: {}: ",messageId);
			//throw new DuplicateEntryException(ErrorCode.DUPLICATE_MESSAGE);
		}
		return fbMessage;
	}

	@Override
	@Transactional
	public Boolean isFBSendAvailable(MessengerContact messengerContact,Integer routeId) {
		// Getting last message metadata
    	LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
    	if(lastMessageMetaData==null || lastMessageMetaData.getLastFbSentAt()==null || lastMessageMetaData.getLastFbReceivedAt()==null) {
    		// fallback get from es
    		return isFBSendAvailableHelper(messengerContact, routeId);
    	} else {
    		return calculateFBSendAvailable(messengerContact.getLeadSource(),lastMessageMetaData.getLastFbSentAt());
    	}
	}

	private Boolean calculateFBSendAvailable(LeadSource leadSource,String lastFbReceivedAt) {
		boolean isLastFacebookMessage =false;
        if (leadSource==LeadSource.FACEBOOK && StringUtils.isBlank(lastFbReceivedAt)) {
            return true;
        }
        else if(StringUtils.isNotBlank(lastFbReceivedAt)) {
			Calendar calLastReceived = Calendar.getInstance();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				calLastReceived.setTime(sdf.parse(lastFbReceivedAt));
				calLastReceived.add(Calendar.HOUR,24*7);

				if (calLastReceived.getTime().before(Calendar.getInstance().getTime())){
					isLastFacebookMessage=true;
				}

			} catch (Exception e) {
				log.error("exception in parsing date of calculateFBSendAvailable method: ",e);
			}
        }
		return isLastFacebookMessage;
	}

	private Boolean isFBSendAvailableHelper(MessengerContact messengerContact,Integer routeId) {
		MessengerFilter messengerFilter = new MessengerFilter();
		messengerFilter.setStartIndex(0);
		messengerFilter.setCount(1);
		messengerFilter.setConversationId(messengerContact.getId());
		messengerFilter.setAccountId(routeId);
		List<String> typeList=new ArrayList<>();
    	typeList.add("SMS_RECEIVE");
        typeList.add("MMS_RECEIVE");
        Map<String, Object> params= new HashMap<>();
        params.put("msg_type",ControllerUtil.getJsonTextFromObject(typeList));
        params.put("source", "110");
        messengerFilter.setParams(params);
        String lastFbReceivedAt="";
        // last facebook Received message
		List<MessageDocument> messages = messengerContactService.getMessagesFromES(messengerFilter);
		if(CollectionUtils.isNotEmpty(messages)) {
			lastFbReceivedAt=messages.get(0).getCr_date();
		}
		//TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
		//messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
		//messengerContactRepository.save(messengerContact);
		return calculateFBSendAvailable(messengerContact.getLeadSource(),lastFbReceivedAt);
	}


	@Transactional
	public void updateFbMessageStatus(Messaging messaging,Integer mcId) {
		if (messaging.getDelivery() != null) {
			if(CollectionUtils.isNotEmpty(messaging.getDelivery().getMids())) {
				FacebookMessage  facebookMessage= facebookMessageRepository.getMessageByMessageId(messaging.getDelivery().getMids().get(0));
				if(facebookMessage!=null) {
					facebookMessage.setStatus("delivered");
					facebookMessageRepository.saveAndFlush(facebookMessage);
					log.info("Status updated with messageId :" + messaging.getDelivery().getMids().get(0));
				} else {
					log.info("Message not found for messageId :" + messaging.getDelivery().getMids().get(0));
				}
			}
		} else {
			Date readTime =new Date(messaging.getRead().getWatermark());
			facebookMessageRepository.updateAllMessageWithStatusDelivered(messaging.getSender().getId(),
					messaging.getRecipient().getId(),readTime,mcId);
			log.info("Status updated for contact with facebookId  :" + messaging.getSender().getId());
		}
	}


	@Override
	public boolean isFacebookUserReachable(MessengerContact messengerContact, Integer accountId) {
		boolean isFacebookUserReachable=true;
		if(StringUtils.isBlank(messengerContact.getFacebookId())) return false;
		//find customer's last facebook received Message's pageId
		MessengerFilter esQueryData = new MessengerFilter();
		esQueryData.setStartIndex(0);
		esQueryData.setCount(1);
		esQueryData.setConversationId(messengerContact.getId());
		esQueryData.setAccountId(accountId);
		Map<String, Object> params= new HashMap<>();
		params.put("msg_type",ControllerUtil.getJsonTextFromObject(Arrays.asList("SMS_RECEIVE", "MMS_RECEIVE")));
		params.put("source", "110");
		esQueryData.setParams(params);
		List<MessageDocument> message = messengerContactService.getMessagesFromES(esQueryData);
		if(CollectionUtils.isNotEmpty(message)) {
//			String currFbPage = platformDbRepository.getFacebookPageIdByBusinessId(messengerContact.getBusinessId());
			String currFbPage = socialService.getFacebookPageIdByBusinessId(messengerContact.getBusinessId());
			String prevFbpage = message.get(0).getFrom();
			if(currFbPage==null) {
				log.info("isFacebookUserReachable: No facebook page is mapped to business {}", messengerContact.getBusinessId());
				isFacebookUserReachable = false;
			}
			else if(!currFbPage.equals(prevFbpage)) {
				log.info("isFacebookUserReachable: business {} remapped from facebook page {} to {}",accountId, prevFbpage, currFbPage);
				isFacebookUserReachable = false;
			}
		}
		return isFacebookUserReachable;
	}

	@Override
	public  String getFacebookIntegrationStatus(Integer selectedBusinessId, Integer messengerEnabled) {
		String response = redisHandler.getFbStatusFromCache(String.valueOf(selectedBusinessId));
		if (StringUtils.isNotBlank(response)) {
			return ControllerUtil.getObjectFromJsonText(response, Map.class).get("status").toString();
		}
		Map<?, ?> responseFromSocial = null;
		if (messengerEnabled != null && messengerEnabled != 0) {
			responseFromSocial = socialService.getFacebookIntegrationStatusFromSocial(selectedBusinessId);
			if (MapUtils.isNotEmpty(responseFromSocial)) {
				Object status = responseFromSocial.get("status");
				if (status != null) {
					response = responseFromSocial.get("status").toString();
					try {
						redisHandler.updateFbStatusCache(String.valueOf(selectedBusinessId), responseFromSocial);
					} catch (JsonProcessingException e) {

					}
				}
			}
		}
		return response;
	}
}
