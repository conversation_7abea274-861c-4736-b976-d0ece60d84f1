package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.*;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.enums.AppointmentActionBy;
import com.birdeye.messenger.enums.AppointmentActionEventType;
import com.birdeye.messenger.enums.BusinessAccountType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.InputValidationException;

import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.external.service.AppointmentService;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CampaignMessageServiceImpl implements CampaignMessageService {

	private final MessengerContactService messengerContactService;
	private final BusinessService businessService;
	private final UserService userService;
	private final SmsService smsService;
	private final MessengerMessageService messengerMessageService;
	private final ContactService contactService;
	private final EmailService emailService;
	private final FirebaseService fcmService;
	private final PulseSurveyService pulseSurveyService;
	private final RedisLockService redisLockService;
	private final KafkaService kafkaService;
	private final RedisHandler redisHandler;
	private final AppointmentEventConsumerService appointmentEventConsumerService;
	private final AppointmentService appointmentService;
	private final ElasticSearchExternalService elasticSearchExternalService;
	private final NexusService nexusService;

	/**
	 * Process Campaign SMS Event
	 * 1. Update Messenger Contact
	 * 2. Update ES for conversation & messages
	 * 3. Firebase Realtime push
	 */

	@Override
	public void processCampaignEvent(CampaignSMSDto smsEvent, boolean campaignEventFoundInRedis) {
		Optional<Lock> lockOpt = Optional.empty();
		String lockKey = "";
		// Sms sms = smsService.findById(smsEvent.getSmsId());
		smsEvent.getSmsData().setReviewRequestId(smsEvent.getReviewRequestId());
		smsEvent.getSmsData().setSource(0);
		smsEvent.getSmsData().setRType(smsEvent.getRType());
		Sms sms = smsService.findByReviewRequestIdAndType(smsEvent.getSmsData().getReviewRequestId().toString(),
				smsEvent.getSmsData().getRType());
		if (null == sms) {
			log.info("Creating sms for campaign event : {}", smsEvent);
			sms = smsService.saveSms(smsEvent.getSmsData());
		}
		BusinessDTO businessDto = businessService.getBusinessLiteDTO(sms.getBusinessId());
		if (Objects.isNull(businessDto)) {
			log.error(
					"processCampaignEvent: campaign event not saved as business returned by core-service is null for businessId {}",
					sms.getBusinessId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}

		boolean messengerEnabled = businessService.getMessengerEnabled(businessDto.getAccountId());
		if (!MessengerUtil.checkIfMessengerEnabledorAccountTypeisDirect(businessDto, messengerEnabled)) {
			log.info("Messenger is disabled for business id and account Type is not direct:{}",
					businessDto.getAccountId());
			return;
		}

		append10DlcBrandingToMessageBody(sms, businessDto);
		if (smsEvent.getAppointmentId() != null
				&& smsEvent.getRequestType().equals(AppointmentActionEventType.REMINDER_SENT.getValue())) {
			processAppointmentCampaignEvent(smsEvent.getAppointmentId(), smsEvent.getCampaignId(), businessDto,
					smsEvent.getUserId(), sms, null,smsEvent.isAppointmentConfirmOptionEnabled());
			return;
		}
		try {
			lockKey = Constants.CUSTOMER_ID_PREFIX + sms.getCustomerId();
			if (campaignEventFoundInRedis) {
				log.info("SMS Receive -> Camapign Event trigger for customerId : {}, reviewRequestId : {}",
						smsEvent.getCustomerId(), smsEvent.getSmsData().getReviewRequestId());
				// Lock with 2sec for Redis Campaign Events - promotional
				lockOpt = redisLockService.tryLock(lockKey, 2, TimeUnit.SECONDS);
			} else {
				lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
			}

			if (lockOpt.isPresent()) {
				processCampaignSMSEvent(smsEvent, sms, businessDto, messengerEnabled, campaignEventFoundInRedis);
			} else {
				// add to delayed queue
				log.info("[Campaign sms event] Unable to acquire lock key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CAMPAIGN_SMS_EVENT_DELAYED_QUEUE, smsEvent);
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
		String keyForCampaignCustomer = Constants.CAMPAIGN_CUSTOMER_ID + sms.getCustomerId();
		String campaignEventFromRedis = redisHandler.getKeyValueFromRedis(keyForCampaignCustomer);
		if (StringUtils.isNotEmpty(campaignEventFromRedis)) {
			redisHandler.deleteKey(keyForCampaignCustomer);
			log.info("Deleted campaign event found in redis, customerId {}", sms.getCustomerId());
		}
	}

	private void processCampaignSMSEvent(CampaignSMSDto smsEvent, Sms sms, BusinessDTO businessDto,
										 boolean messengerEnabled, boolean campaignEventFoundInRedis) {
		CustomerDTO customer = contactService.findById(sms.getCustomerId());
		// fetch business for given location in request

		UserDTO userDTO = smsEvent.isOnGoingCampaign() ? new UserDTO(0)
				: userService.getUserDTO(smsEvent.getUserId());
		// Quick Send Message
		boolean quickSend = false;
		if (smsEvent.getCampaignId() != null && smsEvent.getCampaignId() == -100) {
			quickSend = true;
		}

		SmsDTO smsDTO = SmsDTO.forCampaignSMS(sms, quickSend);
		MessengerData data = messengerContactService.processMessengerContactForCampaignSms(businessDto, userDTO,
				customer, smsDTO);
		messengerMessageService.saveMessengerMessage(smsDTO, userDTO);
		// ****** Push event to firebase ******
		if (messengerEnabled && campaignEventFoundInRedis) {
			fcmService.pushToFireBase(data.getContactDocument(), data.getMessageDocument(), null, null);
		}
		// handle PulseSurveyContext - fail-safe case
		// if (smsEvent != null && (smsEvent.getSurveyType() != null)
		// && CampaignSMSDto.NPS.equalsIgnoreCase(smsEvent.getSurveyType())){
		// try {
		// PulseSurveyContext checkContext =
		// pulseSurveyService.findPulseSurveyContextBySmsId(sms.getSmsId());
		// if (ObjectUtils.isEmpty(checkContext)) {
		// checkContext = pulseSurveyService.handlePulseSurveyContext(smsEvent,
		// customer, businessDto);
		// if (checkContext != null) {
		// ContactDocument exContactDocument = new ContactDocument();
		// if (PulseSurveyContext.isOngoingPulseSurvey(checkContext.getStatus())){
		// exContactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(checkContext.getStatus()));
		// } else {
		// exContactDocument.setOngoingPulseSurvey(false);
		// }
		// messengerContactService.updateContactOnES(data.getMessengerContact().getId(),
		// exContactDocument, businessDto.getRoutingId());
		// }
		// } else {
		// log.info("Rejecting the event as PulseSurveyContext found for smsId {}",
		// sms.getSmsId());
		// }
		// } catch (Exception ex) {
		// log.error("Getting exception while executing handlePulseSurveyContext method
		// {}", ex);
		// }
		// }
	}

	@Override
	public void processEmailCampaignEvent(EmailCampaignEventResponse emailCampaignEvent) {
		Optional<Lock> lockOpt = Optional.empty();
		if (emailCampaignEvent != null && emailCampaignEvent.getAppointmentId() != null
				&& emailCampaignEvent.getRequestType().equals(AppointmentActionEventType.REMINDER_SENT.getValue())) {
			// Quick Send Message
			boolean quickSend = false;
			if (emailCampaignEvent.getCampaignId() != null && emailCampaignEvent.getCampaignId() == -100) {
				quickSend = true;
			}
			Email email = createEmailEntry(emailCampaignEvent, quickSend);
			BusinessDTO businessDto = businessService.getBusinessDTO(emailCampaignEvent.getBusinessId());
			if (Objects.isNull(businessDto)) {
				log.error(
						"processEmailCampaignEvent: campaign event not saved as business returned by core-service is null for businessId {}",
						email.getBusinessId());
				throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
			}
			processAppointmentCampaignEvent(emailCampaignEvent.getAppointmentId(), emailCampaignEvent.getCampaignId(),
					businessDto, emailCampaignEvent.getUserId(), null, email,false);
			return;
		}
		String lockKey = Constants.CUSTOMER_ID_PREFIX + emailCampaignEvent.getCustomerId();
		lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
		try {
			if (lockOpt.isPresent()) {
				if (emailCampaignEvent.getExternalUId() == null) {
					updateEmailCampaignEvent(emailCampaignEvent);
				} else {
					updateCampaignEmailDeliveryStatus(emailCampaignEvent);
				}
			} else {
				// add to delayed queue
				log.info("[Campaign Email Event]Lock is not acquired for key:{} ", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CAMPAIGN_EMAIL_EVENT_DELAYED_QUEUE, emailCampaignEvent);
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}

	}

	private void updateCampaignEmailDeliveryStatus(EmailCampaignEventResponse emailCampaignEvent) {
		log.info("Going to update campaign email delivery status for emailId {} and faliure reason {}",
				emailCampaignEvent.getExternalUId(), emailCampaignEvent.getFailureReason());
		Email email = emailService.findById(Integer.valueOf(emailCampaignEvent.getExternalUId()));
		if (email != null) {
			if(Objects.nonNull(emailCampaignEvent.getFailureReason())){
				email.setFailureReason(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getErrorMessage(emailCampaignEvent.getFailureReason().toString(),"email"));
			}else{
				email.setFailureReason(emailCampaignEvent.getFailureReason());
			}
			email.setReviewRequestId(Long.valueOf(emailCampaignEvent.getReviewRequestId()));
			emailService.saveEmail(email);

			BusinessDTO businessDTO = businessService.getBusinessDTO(email.getBusinessId());
			// Update es only when failure reason exist.
			if (StringUtils.isNotBlank(emailCampaignEvent.getFailureReason())) {
				MessengerContact messengerContact = messengerContactService
						.getOrCreateContactForExistingCustomer(email.getBusinessId(), email.getCustomerId(),
								businessDTO.getAccountId());
				ConversationDTO conversationDTO = addMessageMetaDataForEmail();
				messengerContactService
						.addNewMessageOnEsWithRefresh(
								new MessageDocumentDTO(String.valueOf(email.getId()), conversationDTO,
										messengerContact.getId(), email),
								null, null, businessDTO, MessengerEvent.EMAIL_SEND, false);
			}
		} else {
			log.error("No email record found for emailId {}", emailCampaignEvent.getExternalUId());
		}
	}

	private ConversationDTO addMessageMetaDataForEmail() {
		ConversationDTO conversationDTO = new ConversationDTO();
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setCommunicationDirection(CommunicationDirection.SEND);
		conversationDTO.setSentThrough(SentThrough.WEB);
		conversationDTO.setSource(Source.EMAIL.getSourceId());
		return conversationDTO;
	}

	private Email createEmailEntry(EmailCampaignEventResponse emailCampaignEvent, boolean quickSend) {
		Email email = new Email(emailCampaignEvent.getBusinessId(), emailCampaignEvent.getCustomerId(),
				emailCampaignEvent.getFromEmailId(), emailCampaignEvent.getToEmailId(),
				emailCampaignEvent.getTemplateName(),
				emailCampaignEvent.getFailureReason(), emailCampaignEvent.getTemplateId(),
				Long.valueOf(emailCampaignEvent.getReviewRequestId()), quickSend);
		email.setEncrypted(0);
		if (emailCampaignEvent.getCreatedAt() != null)
			email.setCreateDate(emailCampaignEvent.getCreatedAt());
		if (emailCampaignEvent.getSentOn() != null)
			email.setSentOn(emailCampaignEvent.getSentOn());
		emailService.saveEmail(email);
		return email;
	}

	private void updateEmailCampaignEvent(EmailCampaignEventResponse emailCampaignEvent) {
		log.info("Going to create email entry and update es for review request Id {}",
				emailCampaignEvent.getReviewRequestId());
		validateRequest(emailCampaignEvent);
		// Quick Send Message
		boolean quickSend = false;
		if (emailCampaignEvent.getCampaignId() != null && emailCampaignEvent.getCampaignId() == -100) {
			quickSend = true;
		}
		Email email = createEmailEntry(emailCampaignEvent, quickSend);
		BusinessDTO businessDto = businessService.getBusinessDTO(email.getBusinessId());
		if (Objects.isNull(businessDto)) {
			log.error(
					"processEmailCampaignEvent: campaign event not saved as business returned by core-service is null for businessId {}",
					email.getBusinessId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}

		boolean messengerEnabled = businessService.getMessengerEnabled(businessDto.getAccountId());
		if (!MessengerUtil.checkIfMessengerEnabledorAccountTypeisDirect(businessDto, messengerEnabled)) {
			log.info("Messenger is disabled for business id and account Type is not direct:{}",
					businessDto.getAccountId());
			return;
		}

		CustomerDTO customer = contactService.findById(email.getCustomerId());
		ConversationDTO conversationDTO = new ConversationDTO(email);
		conversationDTO.setSentThrough(SentThrough.WEB);

		UserDTO userDTO = emailCampaignEvent.isOnGoingCampaign() ? new UserDTO(0)
				: userService.getUserDTO(emailCampaignEvent.getUserId());

		if (!quickSend) {
			conversationDTO.setChannel(Channel.CAMPAIGN);
		}
		long startTime = System.currentTimeMillis();
		messengerContactService.processMessengerContactForCampaignEmail(businessDto, userDTO,
				customer, email, conversationDTO);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("processMessengerContactForCampaignEmail", startTime, endTime);
		messengerMessageService.saveMessengerMessage(conversationDTO, userDTO);

	}

	private void validateRequest(EmailCampaignEventResponse emailCampaignEvent) {
		if (StringUtils.isBlank(emailCampaignEvent.getTemplateName())) {
			throw new InputValidationException(
					new ErrorMessageBuilder(ErrorCode.TEMPLATE_NAME_NULL_OR_EMPTY, ComponentCodeEnum.REVIEW)
							.message("Template name can't be null {} for reviewRequestId {}",
									emailCampaignEvent.getTemplateName(),
									emailCampaignEvent.getReviewRequestId()));
		}
	}

	@Override
	public void handlePulseSurveyContext(CampaignSMSDto smsEvent) {
		// Sms sms = smsService.findById(smsEvent.getSmsId());
		SmsDTO sms = smsEvent.getSmsData();
		log.info("Sms Dto received from campaign service is : {}", sms);
		CustomerDTO customer = contactService.findById(sms.getCustomerId());
		// fetch business for given location in request
		BusinessDTO businessDto = businessService.getBusinessLiteDTO(sms.getBusinessId());
		// Get Messenger Contact for customerId
		MessengerContact messengerContact = messengerContactService.findByCustomerId(sms.getCustomerId());
		// handle PulseSurveyContext
		Optional<Lock> lockOpt = Optional.empty();
		try {
			String lockKey = Constants.CUSTOMER_ID_PREFIX + smsEvent.getCustomerId();
			lockOpt = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.CAMPAIGN_PULSE_SURVEY_DELAYED_QUEUE,
						smsEvent);
				return;
			}

			PulseSurveyContext context = pulseSurveyService.handlePulseSurveyContext(smsEvent, customer, businessDto);
			ContactDocument contactDocument = new ContactDocument();
			if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())) {
				contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
			} else {
				contactDocument.setOngoingPulseSurvey(false);
			}
			if (messengerContact != null) {
				ESUpsertRequest<ContactDocument> esUpsertRequest = ESUpsertRequest.<ContactDocument>builder()
						.document(contactDocument).routingId(String.valueOf(businessDto.getAccountId()))
						.id(String.valueOf(messengerContact.getId())).refreshPolicy(RefreshPolicy.NONE)
						.index(Constants.Elastic.CONTACT_INDEX).upsert(true).build();
				elasticSearchExternalService.upsertESDocument(esUpsertRequest);
			}
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}


	@Override
	public void saveCampaignEventOnRedis(CampaignSMSDto campaignSMSDto) {
		if (campaignSMSDto.getCustomerId() == null) {
			log.error("No customerId present in request for smsId {}", campaignSMSDto.getSmsId());
			return;
		}
		String redisKey = Constants.CAMPAIGN_CUSTOMER_ID + campaignSMSDto.getCustomerId();
		redisHandler.setOpsForValueWithExpiry(redisKey, campaignSMSDto, 4l, TimeUnit.HOURS);
	}

	private void processAppointmentCampaignEvent(Long appointmentId, Integer campaignId, BusinessDTO businessDTO,
												 Integer userId, Sms sms, Email email,boolean appointmentConfirmOptionEnabled) {
		AppointmentApiResponseDto appointmentApiResponseDto = appointmentService.getAppointmentDetails(appointmentId,
				businessDTO.getAccountId());
		if (appointmentApiResponseDto != null) {
			AppointmentEventDto appointmentEventDto = new AppointmentEventDto();
			appointmentEventDto.setAppointmentId(appointmentApiResponseDto.getAppointmentId().longValue());
			appointmentEventDto.setAltAppointmentId(appointmentApiResponseDto.getExtAppointmentId());
			appointmentEventDto.setEventTime(new Date().getTime());
			appointmentEventDto.setSource(appointmentApiResponseDto.getSource());
			if (campaignId == -100) {
				appointmentEventDto.setAction(AppointmentActionEventType.REMINDER_SENT.getValue());
			} else {
				appointmentEventDto.setAction(AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue());
			}
			appointmentEventDto.setActionBy(AppointmentActionBy.BUSINESS.getValue());
			appointmentEventDto.setStatus(appointmentApiResponseDto.getStatus());
			appointmentEventDto.setAccountId(businessDTO.getAccountId());
			appointmentEventDto.setBusinessId(businessDTO.getBusinessId());
			appointmentEventDto.setCustomerId(appointmentApiResponseDto.getContactDetails().getCid());
			if (appointmentApiResponseDto.getServiceDetails() != null &&
					appointmentApiResponseDto.getServiceDetails().getServiceId() != null &&
					appointmentApiResponseDto.getServiceDetails().getService() != null) {
				appointmentEventDto
						.setServiceId(appointmentApiResponseDto.getServiceDetails().getServiceId().longValue());
				appointmentEventDto.setServiceName(appointmentApiResponseDto.getServiceDetails().getService());
			}
			appointmentEventDto.setAltWidgetId(appointmentApiResponseDto.getWidgetId());
			appointmentEventDto.setFirstName(appointmentApiResponseDto.getPatientDetails().getFirstName());
			appointmentEventDto.setLastName(appointmentApiResponseDto.getPatientDetails().getLastName());
			appointmentEventDto.setStartTime(appointmentApiResponseDto.getStartTime());
			appointmentEventDto.setEndTime(appointmentApiResponseDto.getEndTime());
			appointmentEventDto.setSelfBooking(appointmentApiResponseDto.getBookingForSelf());
			if(Objects.nonNull(appointmentApiResponseDto.getSpecialistDetails())){
				if(Objects.nonNull(appointmentApiResponseDto.getSpecialistDetails().getSpecialistId())){
					appointmentEventDto.setSpecialistId(appointmentApiResponseDto.getSpecialistDetails().getSpecialistId().longValue());
				}
				if(Objects.nonNull(appointmentApiResponseDto.getSpecialistDetails().getSpecialistName())){
					appointmentEventDto.setSpecialistName(appointmentApiResponseDto.getSpecialistDetails().getSpecialistName());
				}
				if(Objects.nonNull(appointmentApiResponseDto.getSpecialistDetails().getImageUrl())){
					appointmentEventDto.setSpecialistImageUrl(appointmentApiResponseDto.getSpecialistDetails().getImageUrl());
				}
				if(Objects.nonNull(appointmentApiResponseDto.getSpecialistDetails().getSpeciality())){
					appointmentEventDto.setSpecialization(appointmentApiResponseDto.getSpecialistDetails().getSpeciality());
				}
			}
			if (appointmentApiResponseDto.getInsuranceDetails() != null) {
				appointmentEventDto.setInsuranceId(appointmentApiResponseDto.getInsuranceDetails().getInsuranceId());
				appointmentEventDto
						.setInsuranceName(appointmentApiResponseDto.getInsuranceDetails().getInsuranceName());
				appointmentEventDto
						.setInsurancePlanId(appointmentApiResponseDto.getInsuranceDetails().getInsurancePlanId());
				appointmentEventDto
						.setInsurancePlanName(appointmentApiResponseDto.getInsuranceDetails().getInsurancePlanName());
			}
			if (userId != null) {
				appointmentEventDto.setUserId(userId.longValue());
			}
			if (Objects.nonNull(sms)) {
				appointmentEventDto.setSmsComment(sms.getMessageBody());
				if(sms.getSentOn() != null) {
					appointmentEventDto.setEventTime(sms.getSentOn().getTime());
				}
				appointmentEventDto.setSmsId(sms.getSmsId());
			}
			if (Objects.nonNull(email) ) {
				if(email.getSentOn() != null){
					appointmentEventDto.setEventTime(email.getSentOn().getTime());
				}
				appointmentEventDto.setEmailId(email.getId());
			}
			if(Objects.nonNull(sms) && Objects.nonNull(sms.getReviewRequestId())) {
				appointmentEventDto.setReviewRequestId(sms.getReviewRequestId());
			}
			appointmentEventDto.setAppointmentConfirmOptionEnabled(appointmentConfirmOptionEnabled);
			if(StringUtils.isNotBlank(appointmentApiResponseDto.getPmsId())){
				appointmentEventDto.setPmsAppointmentId(appointmentApiResponseDto.getPmsId());
			}
			appointmentEventConsumerService.consumeAppointmentEventV1(appointmentEventDto);
		} else {
			throw new BadRequestException(ErrorCode.NO_APPOINTMENT_DETAILS_FOUND);
		}

	}

	private void append10DlcBrandingToMessageBody(Sms sms, BusinessDTO businessDTO) {
		log.debug("Append Ten Dlc Branding for sms {} account {}", sms.getSmsId(), businessDTO.getAccountId());
		String messageBody = sms.getMessageBody();
		TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(businessDTO.getEnterpriseNumber());
		if (Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()) {
			String status = tenDlcStatusDTO.getStatus();
			TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
			if (Objects.nonNull(status)
					&& (status.equals("not_started") || status.equals("in_progress") || status.equals("failed")
					|| status.equals("not_required_demo_account"))
					&& Objects.nonNull(tollFreeInfo) && Objects.nonNull(tollFreeInfo.getBranding())
					&& tollFreeInfo.isAvailable()) {
				messageBody = messageBody.concat("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				sms.setMessageBodyUnencrypted(messageBody);
				sms.setMessageBody(messageBody);
			}
		}
	}

}
