package com.birdeye.messenger.service.impl;

import java.util.List;

import com.amazonaws.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ChatTranscriptAccountConfig;
import com.birdeye.messenger.dao.repository.ChatTranscriptAccountConfigRepository;
import com.birdeye.messenger.dto.ChatTranscriptRequest;
import com.birdeye.messenger.service.ChatTranscriptRepositoryService;

import lombok.extern.slf4j.Slf4j;


@Service
@Slf4j
public class ChatTranscriptRepositoryServiceImpl implements ChatTranscriptRepositoryService {

    @Autowired
    ChatTranscriptAccountConfigRepository chatTranscriptAccountConfigRepository;

    @Override
    public void enableChatTranscript(ChatTranscriptRequest chatTranscriptRequest) {
        log.debug("Enable ChatTranscript for account  : {}",chatTranscriptRequest);
        List<ChatTranscriptAccountConfig> chatTranscriptAccountConfigs = chatTranscriptAccountConfigRepository.findAllByAccountId(chatTranscriptRequest.getAccountId());
        if(CollectionUtils.isNullOrEmpty(chatTranscriptAccountConfigs)){
        	ChatTranscriptAccountConfig chatTranscriptAccountConfig = new ChatTranscriptAccountConfig(chatTranscriptRequest.getAccountId(),chatTranscriptRequest.getTranscriptFormat());
        	chatTranscriptAccountConfigRepository.save(chatTranscriptAccountConfig);
        } else {
        	log.info("Enable - config already present updating it for : {}", chatTranscriptRequest);
            ChatTranscriptAccountConfig chatTranscriptAccountConfig = chatTranscriptAccountConfigs.get(0);
            chatTranscriptAccountConfig.setTranscriptFormat(chatTranscriptRequest.getTranscriptFormat());
            chatTranscriptAccountConfigRepository.save(chatTranscriptAccountConfig);
        }
    }


    @Override
    @Cacheable(cacheNames = Constants.CHAT_TRANSCRIPT_ACCOUNTS_CACHE, key="'ACCID-'.concat(#accountId)", unless="#result == null")
    public List<ChatTranscriptAccountConfig> getChatTranscriptAccountConfig(Integer accountId) {
        log.debug("Get ChatTranscript account config for accoutId : {}",accountId);
        List<ChatTranscriptAccountConfig> chatTranscriptAccountConfigList = chatTranscriptAccountConfigRepository.findAllByAccountId(accountId);
        return chatTranscriptAccountConfigList;
    }

    @Override
    public void disableChatTranscript(ChatTranscriptRequest chatTranscriptRequest) {
        log.info("Disable ChatTranscript for account  : {}",chatTranscriptRequest);
        chatTranscriptAccountConfigRepository.deleteAllByAccountId(chatTranscriptRequest.getAccountId());
    }

    @Override
    @CacheEvict(cacheNames = Constants.CHAT_TRANSCRIPT_ACCOUNTS_CACHE, key="'ACCID-'.concat(#chatTranscriptRequest.getAccountId())")
    public void evictChatTranscriptConfigCache(ChatTranscriptRequest chatTranscriptRequest) {
        log.info("Clearing ChatTranscriptConfigCache");
    }
}
