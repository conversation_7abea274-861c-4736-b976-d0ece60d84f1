package com.birdeye.messenger.service.impl;

import jakarta.transaction.Transactional;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.LiveChatMessage;
import com.birdeye.messenger.dao.repository.LiveChatMessageRepository;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.enums.MessageStatusEnum;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.LiveChatMessageService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class LiveChatMessageServiceImpl implements LiveChatMessageService {

	@Autowired
    private SmsService smsService;

	@Autowired
	private ContactService contactService;

	@Autowired
	private LiveChatMessageRepository liveChatMessageRepository;

	@Override
	public LiveChatMessage saveLiveChatMessageFromCustomer(LiveChatMessageObject liveChatMessageObject,MessageStatusEnum messageStatus) {

		// Business SMS
		liveChatMessageObject.setRecepient(smsService.getFormattedBusinessNumber(liveChatMessageObject.getBusinessId()));
		// Customer Information. Customer phone and code should already be available in dto before. save query.
		//smsMessage.setFromNumber(getFormattedCustomerNumber(smsMessage.getCustomerId()));
		CustomerDTO customerDTO = contactService.findById(liveChatMessageObject.getCustomerId());
		if(BooleanUtils.isTrue(liveChatMessageObject.getEmailMandatory())) {
			liveChatMessageObject.setSender(customerDTO.getEmailId());
		}else if(StringUtils.isEmpty(customerDTO.getPhoneE164()) && (StringUtils.isNotEmpty(liveChatMessageObject.getEmail()) && liveChatMessageObject.getEmail().contains("@anonymous-webchat.com"))){
			liveChatMessageObject.setSender(customerDTO.getEmailId());
		}else if(StringUtils.isEmpty(customerDTO.getPhoneE164()) && (StringUtils.isNotEmpty(customerDTO.getEmailId()))){
			liveChatMessageObject.setSender(customerDTO.getEmailId());
		}else {
			liveChatMessageObject.setSender(customerDTO.getPhoneE164());
		}
		LiveChatMessage liveChatMessage = DtoToEntityConverter.from(liveChatMessageObject);
		liveChatMessage.setStatus(messageStatus.toString());
		liveChatMessage.setEmailMandatory(liveChatMessageObject.getEmailMandatory());
		encrypt(liveChatMessage);
		liveChatMessageRepository.save(liveChatMessage);
		liveChatMessage.setMessagerContactId(liveChatMessageObject.getMessengerContactId());
		return liveChatMessage;

	}

	@Override
	public void encrypt(LiveChatMessage liveChatMessage) {
		try {
			if (StringUtils.isNotEmpty(liveChatMessage.getMessageBodyUnencrypted())) {
				liveChatMessage.setMessageBody(EncryptionUtil.encrypt(liveChatMessage.getMessageBodyUnencrypted(),
						StringUtils.join(liveChatMessage.getSender(), liveChatMessage.getRecepient()),
						StringUtils.join(liveChatMessage.getRecepient(), liveChatMessage.getSender())));
				liveChatMessage.setEncrypted(1);
			}
		} catch (Exception e) {
			log.error("Encryption failed for sms body: {} with business {}", liveChatMessage.getMessageBody(), liveChatMessage.getBusinessId());
			liveChatMessage.setEncrypted(0);
			liveChatMessage.setMessageBody(liveChatMessage.getMessageBodyUnencrypted());
		}
	}

	@Override
	@Transactional
	public LiveChatMessage saveLiveChatMessage(SendMessageDTO sendMessageDTO,MessageStatusEnum messageStatus) {

		// Business Information
		log.info("saveLiveChatMessage: sendMessageDTO {}", sendMessageDTO);
		Integer fromBusinessId = Integer.parseInt(sendMessageDTO.getBusinessIdentifierId());
		sendMessageDTO.setFromPhone(smsService.getFormattedBusinessNumber(fromBusinessId));
		// Customer Information
		//sendMessageDTO.setToPhone(getFormattedCustomerNumberSendSms(sendMessageDTO.getCustomerId()));
		CustomerDTO customerDTO = contactService.findById(sendMessageDTO.getCustomerId());
		LiveChatMessage liveChatMessage = DtoToEntityConverter.convert(sendMessageDTO);
		if(BooleanUtils.isTrue(sendMessageDTO.getEmailMandatory())) {
			liveChatMessage.setRecepient(customerDTO.getEmailId());
		}else {
			liveChatMessage.setRecepient(customerDTO.getPhoneE164());
		}
		if(Objects.isNull(liveChatMessage.getRecepient()) && Objects.nonNull(customerDTO.getEmailId())){
			liveChatMessage.setRecepient(customerDTO.getEmailId());
		}
		liveChatMessage.setStatus(messageStatus.toString());
		encrypt(liveChatMessage);
		liveChatMessageRepository.save(liveChatMessage);
		return liveChatMessage;
	}
}
