package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.repository.PulseSurveyContextRepository;
import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.enums.PulseSurveyStatusEnum;
import com.birdeye.messenger.service.PulseSurveyContextService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class PulseSurveyContextServiceImpl implements PulseSurveyContextService {

	@Autowired
	private PulseSurveyContextRepository pulseSurveyContextRepository;

	@Override
	@Cacheable(cacheNames = Constants.PULSE_SURVEY_CONTEXT_CACHE, key = "'PULSE-'.concat(#customerId)", unless = "#result == null")
	public PulseSurveyContext getPulseSurveyContext(Integer customerId, List<String> status) {
		List<PulseSurveyContext> existingContext = pulseSurveyContextRepository
				.findByCustomerIdAndStatusIn(customerId, status);
		return existingContext.size() > 0 ? existingContext.get(0) : null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public PulseSurveyContext createPulseSurveyContext(CampaignSMSDto smsEvent, Integer businessId, Integer customerId) {
		// Create New Context for businessId and customerId with Status as active
		PulseSurveyContext newContext = new PulseSurveyContext();
//		newContext.setSmsId(smsEvent.getSmsId());
		newContext.setBusinessId(businessId);
		newContext.setCustomerId(customerId);
		newContext.setCampaignId(smsEvent.getCampaignId());
		newContext.setRrId(smsEvent.getReviewRequestId());
		newContext.setSurveyId(smsEvent.getSurveyId());
		newContext.setQuestionId(smsEvent.getQuestionId());
		newContext.setStep(1);
		newContext.setStatus(PulseSurveyStatusEnum.ACTIVE.getName());
		newContext.setStartDate(new Date());
		newContext.setLastUpdatedDate(new Date());

		savePulseSurveyContext(newContext);
		return newContext;
	}

	@Override
	public void updatePulseSurveyContextStatus(PulseSurveyContext existingContext, String status) {
		existingContext.setStatus(status);
		existingContext.setLastUpdatedDate(new Date());
		savePulseSurveyContext(existingContext);
	}

	@Override
	public void savePulseSurveyContext(PulseSurveyContext context) {
		pulseSurveyContextRepository.save(context);
	}

	@Override
	@CacheEvict(cacheNames = Constants.PULSE_SURVEY_CONTEXT_CACHE, key = "'PULSE-'.concat(#customerId)")
	public void clearPulseSurveyContextCache(Integer customerId) {
		log.info("Clearing PULSE_SURVEY_CONTEXT_CACHE");
	}

	@Override
	public PulseSurveyContext getPulseSurveyContextById(Integer id) {
		Optional<PulseSurveyContext> existingContextOptional = pulseSurveyContextRepository.findById(id);
		if (!existingContextOptional.isPresent()){
			log.info("Existing SurveyContext not found for id :{}", id);
			return null;
		}
		return existingContextOptional.get();
	}

    @Override
    public PulseSurveyContext getLastPulseSurveyContext(Integer customerId) {
        Optional<PulseSurveyContext> existingContextOptional = pulseSurveyContextRepository
                .findByCustomerIdOrderByLastUpdateDate(customerId);
        if (!existingContextOptional.isPresent()) {
            log.info("No PulseSurveyContext found for CustomerId :{}", customerId);
            return null;
        }
        return existingContextOptional.get();
    }
}
