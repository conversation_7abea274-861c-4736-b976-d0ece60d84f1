package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.WhatsappMessage;
import com.birdeye.messenger.dao.entity.whatsapp.WhatsAppTemplateTokens;
import com.birdeye.messenger.dao.repository.WhatsappMessageRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessLocationCustomFieldsTokensDto;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.PublicDataBusinessDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendWAMessage;
import com.birdeye.messenger.dto.SendWAMessage.Template.Component;
import com.birdeye.messenger.dto.SendWAMessage.Template.Component.Parameter;
import com.birdeye.messenger.dto.SendWAMessageResponse;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.whatsapp.Message;
import com.birdeye.messenger.dto.whatsapp.WARestrictedFlags;
import com.birdeye.messenger.dto.whatsapp.WhatsAppTemplateDto;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.WhatsappMessageStatusEnum;
import com.birdeye.messenger.enums.WhatsappMsgType;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WhatsappMessageServiceImpl implements WhatsappMessageService {

	private final WhatsappMessageRepository whatsappMessageRepository;
	private final MessengerContactService messengerContactService;
	private final SocialService socialService;
	private final IWhatsAppTemplateService whatsAppTemplateService;
	private final BusinessService businessService;
	private final SmsService smsService;

	enum TemplateComponentType {
		header, body, footer;
	}
	
	enum TemplateParameterFormat {
		NAMED, POSITIONAL;
	}
	
	@Override
	public WhatsappMessage saveWhatsappMessage(MessageDTO messageDTO, String senderId, String recipientId,
			String messageId, WhatsappMessageStatusEnum status) {
		Integer encrypted = 0;
		String message = null;
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		if (messageDTO instanceof SendMessageDTO) {
			message = ((SendMessageDTO) messageDTO).getBody();
		} else {
			WhatsappMessageRequest whatsappMessageRequest = (WhatsappMessageRequest) messageDTO;
			Message waMessage = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0);
			if (waMessage.getText() != null) {
				message = waMessage.getText().getBody();
				messengerContact.setLastMessage(message);
			}
			if (WhatsappMsgType.image.name().equalsIgnoreCase(waMessage.getType()) && waMessage.getImage().getCaption() != null) {
				message = waMessage.getImage().getCaption();
				messengerContact.setLastMessage(message);
			}
			if (WhatsappMsgType.video.name().equalsIgnoreCase(waMessage.getType()) && waMessage.getVideo().getCaption() != null) {
				message = waMessage.getVideo().getCaption();
				messengerContact.setLastMessage(message);
			}
			if (WhatsappMsgType.document.name().equalsIgnoreCase(waMessage.getType()) && waMessage.getDocument().getCaption() != null) {
				message = waMessage.getDocument().getCaption();
				messengerContact.setLastMessage(message);
			}
		}

		try {
			if (MessengerUtil.isEncryptionEnabled() && StringUtils.isNotBlank(message)) {
				message = EncryptionUtil.encrypt(message, StringUtils.join(senderId, recipientId),
						StringUtils.join(recipientId, senderId));
				encrypted = 1;
			}
		} catch (Exception e) {
			log.info("Encryption for recieved WA msg failed: {}", e);
		}
		WhatsappMessage waMessage = new WhatsappMessage();
		waMessage.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
		waMessage.setMessengerContactId(messengerContact.getId());
		waMessage.setCreateDate(new Date());
		waMessage.setSenderWAId(senderId);
		waMessage.setRecipientWAId(recipientId);
		waMessage.setStatus(status.name());
		if (StringUtils.isNotBlank(message)) {
			waMessage.setMessageBody(message);
		}
		waMessage.setMessageId(messageId);
		waMessage.setSentOn(new Date());
		waMessage.setEncrypted(encrypted);

		if (messageDTO.getMessengerMediaFileDTO() != null
				&& StringUtils.isNotBlank(messageDTO.getMessengerMediaFileDTO().getUrl())) {
			waMessage.setMediaURL(messageDTO.getMessengerMediaFileDTO().getUrl());
		}
		try {
			whatsappMessageRepository.saveAndFlush(waMessage);
		} catch (Exception e) {
			log.error("Duplicate WA message, id: {}", messageId);
		}
		return waMessage;
	}

	@Override
	public WARestrictedFlags isWAFreeflowSendAvailable(MessengerContact messengerContact, int routeId) {
		// Getting last message metadata
		WARestrictedFlags waRestrictedFlags = new WARestrictedFlags();
		LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		if(lastMessageMetaData==null || lastMessageMetaData.getLastWAReceivedAt()==null) {
			// fallback get from es
			waRestrictedFlags = isWAFreeflowSendAvailableHelper(messengerContact, routeId, waRestrictedFlags);
		} else {
			waRestrictedFlags.setReplyFromWAReceived(true);
			waRestrictedFlags.setRestrictWAReply(calculateWAFreeflowSendAvailable(messengerContact.getLeadSource(),lastMessageMetaData.getLastWAReceivedAt()));
		}
		return waRestrictedFlags;
	}

	private Boolean calculateWAFreeflowSendAvailable(LeadSource leadSource, String lastWAReceivedAt) {
		boolean isLastWAMessage = true;
		if (leadSource==LeadSource.WHATSAPP && StringUtils.isBlank(lastWAReceivedAt)) {
			return true;
		}
		else if(StringUtils.isNotBlank(lastWAReceivedAt)) {
			Calendar calLastReceived = Calendar.getInstance();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				calLastReceived.setTime(sdf.parse(lastWAReceivedAt));
				calLastReceived.add(Calendar.HOUR,24);

				if (!calLastReceived.getTime().before(Calendar.getInstance().getTime())){
					isLastWAMessage=false;
				}

			} catch (Exception e) {
				log.error("exception in parsing date of calculateFBSendAvailable method: ",e);
			}
		}
		return isLastWAMessage;
	}

	private WARestrictedFlags isWAFreeflowSendAvailableHelper(MessengerContact messengerContact, int routeId, WARestrictedFlags waRestrictedFlags) {
		MessengerFilter messengerFilter = new MessengerFilter();
		messengerFilter.setStartIndex(0);
		messengerFilter.setCount(1);
		messengerFilter.setConversationId(messengerContact.getId());
		messengerFilter.setAccountId(routeId);
		List<String> typeList=new ArrayList<>();
		typeList.add("SMS_RECEIVE");
		typeList.add("MMS_RECEIVE");
		Map<String, Object> params= new HashMap<>();
		params.put("msg_type",ControllerUtil.getJsonTextFromObject(typeList));
		params.put("source", "20");
		messengerFilter.setParams(params);
		String lastWAReceivedAt="";
		// get last WA Received message
		List<MessageDocument> messages = messengerContactService.getMessagesFromES(messengerFilter);
		if(CollectionUtils.isNotEmpty(messages)) {
			waRestrictedFlags.setReplyFromWAReceived(true);
			lastWAReceivedAt=messages.get(0).getCr_date();
		} else {
			waRestrictedFlags.setReplyFromWAReceived(false);
		}
		waRestrictedFlags.setRestrictWAReply(calculateWAFreeflowSendAvailable(messengerContact.getLeadSource(), lastWAReceivedAt));
		return waRestrictedFlags;
	}

	@Override
	public SendWAMessageResponse sendWACallToSocial(MessageDTO messageDTO, String waPhoneNoId) {
		SendWAMessage sendWAMessage = null;
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		String msgType=WhatsappMsgType.text.name();
		if (sendMessageDTO.getTemplateName()!= null) {
			msgType = WhatsappMsgType.template.name();
		}
		String body = sendMessageDTO.getBody();
		SendWAMessage.Text text = new SendWAMessage.Text();
		if (body != null) {
			text.setBody(body);
		}
		
		if(StringUtils.isBlank(waPhoneNoId)) {
			throw new NotFoundException(ErrorCode.WAPHNO_NOT_FOUND);
		}
		
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		String recPhoneNumber = customerDTO.getPhoneE164();

		//update wa conv id
		if (messageDTO.getMessengerContact() != null && messageDTO.getMessengerContact().getWhatsappConversationId() == null) {
			String waConvId = recPhoneNumber.replaceAll("\\+", "");
			messageDTO.getMessengerContact().setWhatsappConversationId(waConvId);
		}

		MessengerMediaFileDTO messengerMediaFileDTO = messageDTO.getMessengerMediaFileDTO();
		if(messengerMediaFileDTO!=null) {
			if(messengerMediaFileDTO.getContentType()!=null && messengerMediaFileDTO.getContentType().contains("image")) {
				msgType=WhatsappMsgType.image.name();
				SendWAMessage.Media image = new SendWAMessage.Media();
				image.setLink(messengerMediaFileDTO.getUrl());
				if (body != null) {
					image.setCaption(ControllerUtil.truncateLongMediaCaption(body));
				}
				sendWAMessage=new SendWAMessage(businessDTO.getAccountId(), businessDTO.getBusinessId(), recPhoneNumber, msgType, null, image, null, null, null);
			}
			if(messengerMediaFileDTO.getContentType()!=null && messengerMediaFileDTO.getContentType().contains("video")) {
				msgType=WhatsappMsgType.video.name();
				SendWAMessage.Media video = new SendWAMessage.Media();
				video.setLink(messengerMediaFileDTO.getUrl());
				if (body != null) {
					video.setCaption(ControllerUtil.truncateLongMediaCaption(body));
				}
				sendWAMessage=new SendWAMessage(businessDTO.getAccountId(), businessDTO.getBusinessId(), recPhoneNumber, msgType, null, null, video, null, null);
			}

			if(messengerMediaFileDTO.getContentType()!=null && messengerMediaFileDTO.getContentType().contains("pdf")) {
				msgType=WhatsappMsgType.document.name();
				SendWAMessage.Media document = new SendWAMessage.Media();
				document.setLink(messengerMediaFileDTO.getUrl());
				document.setFilename(messengerMediaFileDTO.getName());
				if (body != null) {
					document.setCaption(ControllerUtil.truncateLongMediaCaption(body));
				}
				sendWAMessage=new SendWAMessage(businessDTO.getAccountId(), businessDTO.getBusinessId(), recPhoneNumber, msgType, null, null, null, document, null);
			}
		}
		if (sendWAMessage == null && WhatsappMsgType.text.name().equalsIgnoreCase(msgType)) {
			sendWAMessage=new SendWAMessage(businessDTO.getAccountId(), businessDTO.getBusinessId(), recPhoneNumber, msgType, text, null, null, null, null);
		}
		
		if (sendWAMessage == null && WhatsappMsgType.template.name().equalsIgnoreCase(msgType)) {
			sendWAMessage = prepareTemplateSendData(sendMessageDTO, msgType, businessDTO, customerDTO, recPhoneNumber);
		}

		return socialService.sendWhatsappMessage(sendWAMessage);
	}

	private SendWAMessage prepareTemplateSendData(SendMessageDTO sendMessageDTO, String msgType,
			BusinessDTO businessDTO, CustomerDTO customerDTO, String recPhoneNumber) {
		SendWAMessage sendWAMessage;
		WhatsAppTemplateDto whatsappTemplateDetail = whatsAppTemplateService.getWhatsAppTemplateDetailByName(sendMessageDTO.getTemplateName(), businessDTO.getAccountId());
		SendWAMessage.Template templateData = new SendWAMessage.Template();
		List<SendWAMessage.Template.Component> componentList = new ArrayList<>();
		
		SendWAMessage.Template.Language lang = new SendWAMessage.Template.Language();
		lang.setCode(whatsappTemplateDetail.getLanguage());
		
		templateData.setName(whatsappTemplateDetail.getName());
		templateData.setLanguage(lang);

		if (whatsappTemplateDetail.getHeaderFormat()!=null) {
			if ("TEXT".equals(whatsappTemplateDetail.getHeaderFormat())) {
				sendMessageDTO.setWaMsgHeader(whatsappTemplateDetail.getHeaderText());
			}
			if (whatsappTemplateDetail.getHeaderMediaUrl()!=null || whatsappTemplateDetail.getHeaderLocation()!=null) {
				componentList.add(getHeaderComponent(whatsappTemplateDetail, sendMessageDTO));
			}
		}

		if (whatsappTemplateDetail.getFooterText()!=null) {
			componentList.add(getFooterComponent(whatsappTemplateDetail, sendMessageDTO));
		}

		if (whatsappTemplateDetail.getTokenCount() > 0) {
			//tokens present
			Component component = new Component();
			List<Parameter> parameters = new ArrayList<>();
			List<WhatsAppTemplateTokens> tokensList = whatsappTemplateDetail.getTemplatesToken();
			if (TemplateParameterFormat.POSITIONAL.name().equalsIgnoreCase(whatsappTemplateDetail.getParameterFormat())) {
				sortByExtTokenId(tokensList);
				for (WhatsAppTemplateTokens token : tokensList) {
					Parameter bodyParameter = new Parameter();
					String tokenValue = prepareTemplateBody("[" + token.getCustomFieldName() + "]", businessDTO, customerDTO.getFirstName());
					bodyParameter.setType("text");
					if (StringUtils.isEmpty(tokenValue)) {
						tokenValue = " ";
					}
					bodyParameter.setText(tokenValue);
					parameters.add(bodyParameter);
				}
			} else if (TemplateParameterFormat.NAMED.name().equalsIgnoreCase(whatsappTemplateDetail.getParameterFormat())) {
				for (WhatsAppTemplateTokens token : tokensList) {
					Parameter bodyParameter = new Parameter();
					String tokenValue = prepareTemplateBody("[" + token.getCustomFieldName() + "]", businessDTO, customerDTO.getFirstName());
					bodyParameter.setType("text");
					if (StringUtils.isEmpty(tokenValue)) {
						tokenValue = " ";
					}
					bodyParameter.setText(tokenValue);
					bodyParameter.setParameter_name(token.getExtToken());
					parameters.add(bodyParameter);
				}
			}

			component.setParameters(parameters);
			component.setType(TemplateComponentType.body.name());
			componentList.add(component);
		}

		if (CollectionUtils.isNotEmpty(componentList)) {
			templateData.setComponents(componentList);
		}
		sendWAMessage=new SendWAMessage(businessDTO.getAccountId(), businessDTO.getBusinessId(), recPhoneNumber, msgType, null, null, null, null, templateData);

		if(StringUtils.isNotEmpty(whatsappTemplateDetail.getBody())){
			String templateBody = prepareTemplateBody(whatsappTemplateDetail.getBody(), businessDTO, customerDTO.getFirstName());
			sendMessageDTO.setBody(templateBody);
		}
		return sendWAMessage;
	}

	private Component getHeaderComponent(WhatsAppTemplateDto template, SendMessageDTO sendMessageDTO) {
		Component component = new Component();
		List<Parameter> parameters = new ArrayList<>();
		Parameter headerParameter = new Parameter();
		if ("IMAGE".equals(template.getHeaderFormat())) {
			headerParameter.setType(WhatsappMsgType.image.name());
			Parameter.Media image = new Parameter.Media();
			image.setLink(template.getHeaderMediaUrl());
			headerParameter.setImage(image);
		} else if ("VIDEO".equals(template.getHeaderFormat())) {
			headerParameter.setType(WhatsappMsgType.video.name());
			Parameter.Media video = new Parameter.Media();
			video.setLink(template.getHeaderMediaUrl());
			headerParameter.setVideo(video);
		} else if ("DOCUMENT".equals(template.getHeaderFormat())) {
			headerParameter.setType(WhatsappMsgType.document.name());
			Parameter.Media document = new Parameter.Media();
			document.setLink(template.getHeaderMediaUrl());
			document.setFilename(FilenameUtils.getName(template.getHeaderMediaUrl()));
			headerParameter.setDocument(document);
		} else if ("LOCATION".equals(template.getHeaderFormat())) {
			headerParameter.setType(WhatsappMsgType.location.name()); //TODO - Add Location header support later
			Parameter.Location location = Parameter.Location.builder()
					.name(null)
					.address(null)
					.latitude(null)
					.longitude(null).build();
			headerParameter.setLocation(location);
		}
		parameters.add(headerParameter);
		component.setType(TemplateComponentType.header.name());
		component.setParameters(parameters);

		if ("IMAGE".equals(template.getHeaderFormat()) || "VIDEO".equals(template.getHeaderFormat()) || "DOCUMENT".equals(template.getHeaderFormat())) {
			sendMessageDTO.setMessengerMediaFiles(new ArrayList<MessengerMediaFile>());
			MessengerMediaFile mediaFile = new MessengerMediaFile(template.getHeaderMediaUrl());
			//          sendMessageDTO.setMediaurl(mediaFile.getUrl());
			MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
			messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
			sendMessageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
		}
		return component;
	}

	private Component getFooterComponent(WhatsAppTemplateDto whatsappTemplateDetail, SendMessageDTO sendMessageDTO) {
		Component component = new Component();
		List<Parameter> parameters = new ArrayList<>();
		Parameter footerParameter = new Parameter();
		footerParameter.setType(WhatsappMsgType.text.name());
		footerParameter.setText(whatsappTemplateDetail.getFooterText());
		sendMessageDTO.setWaMsgFooter(whatsappTemplateDetail.getFooterText());

		parameters.add(footerParameter);
		component.setType(TemplateComponentType.footer.name());
		component.setParameters(parameters);
		return component;
	}

	public void sortByExtTokenId(List<WhatsAppTemplateTokens> tokensList) {
		tokensList.sort(Comparator.comparing(WhatsAppTemplateTokens::getExtToken));
	}

	private String prepareTemplateBody(String templateBody, BusinessDTO businessDTO, String customerName) {
		// Replace template tokens
		String phoneNumber  = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId(), businessDTO.getPhone());
		String textingNumber  = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
		BusinessTimingDTO businessTimingDTO = null;
		if (templateBody.contains(Constants.BUSINESS_HOURS_OLD) || templateBody.contains(Constants.BUSINESS_HOURS_NEW)){
			businessTimingDTO = businessService.getBusinessTimings(businessDTO.getBusinessId(),true);
		}
		PublicDataBusinessDTO publicBusinessDto = null;
		if (templateBody.contains(Constants.BUSINESS_SERVICES) || templateBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS)
				|| templateBody.contains(Constants.BUSINESS_LANGUAGES) || templateBody.contains(Constants.BUSINESS_SERVICES_CAP)
				|| templateBody.contains(Constants.BUSINESS_PAYMENT_OPTIONS_CAP) || templateBody.contains(Constants.BUSINESS_LANGUAGES_CAP)){
			publicBusinessDto = businessService.getPublicBusinessData(businessDTO.getBusinessId());
		}
		BusinessLocationCustomFieldsTokensDto customFields = businessService.getBusinessCustomFieldsAndTokenByBusinessID(businessDTO.getBusinessId());
		BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessDTO.getBusinessId());
		String formattedBody = ControllerUtil.replaceTokens(businessDTO, customerName,
				templateBody, phoneNumber, textingNumber, businessTimingDTO, publicBusinessDto, businessProfileData, customFields);

		return formattedBody;
	}

	@Override
	public void updateWAMessageBody(String messageBody, Integer messageId) {
		try {
			whatsappMessageRepository.updateWAMessageBody(messageBody,messageId);
		} catch (Exception e) {
			log.error("error : {} occurred in updateWAMessageBody", e.getMessage());
		}
	}
	
	@Override
	public WhatsappMessage findWAMessageById(String messageId) {
		try {
			return whatsappMessageRepository.findByMessageId(messageId);
		} catch (Exception e) {
			log.error("error : {} occurred in findWAMessageById", e.getMessage());
		}
		return null;
	}
	
	@Override
	public void saveWAMsg(WhatsappMessage waMsg) {
		whatsappMessageRepository.saveAndFlush(waMsg);
	}

	@Override
	public boolean isWAUserReachable(MessengerContact messengerContact, Integer accountId) {
		boolean isWAUserReachable=true;
		if(StringUtils.isBlank(messengerContact.getWhatsappConversationId())) return false;
		//find customer's last facebook received Message's pageId
		MessengerFilter esQueryData = new MessengerFilter();
		esQueryData.setStartIndex(0);
		esQueryData.setCount(1);
		esQueryData.setConversationId(messengerContact.getId());
		esQueryData.setAccountId(accountId);
		Map<String, Object> params= new HashMap<>();
		params.put("msg_type",ControllerUtil.getJsonTextFromObject(Arrays.asList("SMS_RECEIVE", "MMS_RECEIVE")));
		params.put("source", "20");
		esQueryData.setParams(params);
		List<MessageDocument> message = messengerContactService.getMessagesFromES(esQueryData);
		if(CollectionUtils.isNotEmpty(message)) {
			try {
				String currWAPhNoId = socialService.getWAPhoneNumberIdByBusinessId(messengerContact.getBusinessId());
				String prevWAPhNoId = message.get(0).getFrom();
				if(currWAPhNoId==null) {
					log.info("isWAUserReachable: No wa phno is mapped to business {}", messengerContact.getBusinessId());
					isWAUserReachable = false;
				}
				else if(!currWAPhNoId.equals(prevWAPhNoId)) {
					log.info("isWAUserReachable: business {} remapped from wa phno {} to {}",accountId, prevWAPhNoId, currWAPhNoId);
					isWAUserReachable = false;
				}
			} catch (Exception ex) {
				isWAUserReachable = false;
				log.error("unable to determine isWAUserReachable");
			}
		}
		return isWAUserReachable;
	}
}
