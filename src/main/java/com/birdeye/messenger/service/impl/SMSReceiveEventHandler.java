package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import static com.birdeye.messenger.constant.Constants.CONTACTS_TEXT_CATEGORIES_ENABLED;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CampaignSMSDto;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.InfoByPhoneNumberRequest;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.ReceivedSMSAttibutionCallbackRequest;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.SmsPreferencesDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.event.CampaignApplicationEventPublisher;
import com.birdeye.messenger.event.SMSReceiveEvent;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.AppointmentConfirmationService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.ReceiveMsgBypassInboxAuditService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.PhoneNoValidator;
import com.birdeye.messenger.util.TextPreferencesUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SMSReceiveEventHandler extends MessageEventHandlerAbstract {

    private final MessengerEvent EVENT = MessengerEvent.SMS_RECEIVE;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private NLPService nlpService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ContactService contactService;

    @Autowired
    private MessengerMediaFileService messengerMediaFileService;

    @Autowired
    private MessengerMessageService messengerMessageService;
    
    @Autowired
    private ConversationActivityService ConversationActivityService;

    @Autowired
    private PulseSurveyService pulseSurveyService;
    
    @Autowired
    private RedisLockService redisLockService;
    
    @Autowired
    private  KafkaService kafkaService;

    @Autowired
    private RedisHandler redisHandler;

    @Autowired
    private CampaignApplicationEventPublisher campaignApplicationEventPublisher;

    @Autowired
    private CommonService commonService;
    
    @Autowired
    private SpamDetectionService spamDetectionService;

    @Autowired
    private AppointmentConfirmationService appointmentConfirmationService;

    @Autowired
    private ReceiveMsgBypassInboxAuditService receiveMsgBypassInboxAuditService;

    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
        if (getSendEmailNotification(messageDTO)) {
            return MessageTag.UNREAD;
        } else return MessageTag.INBOX;
    }

    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
        SMSMessageDTO dto = (SMSMessageDTO) messageDTO;
        if(dto.getActivityDto()!=null) {
        	return new MessageDocumentDTO(dto.getActivityDto());
        }
        MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getSmsDTO(), getMessengerContact(messageDTO).getId());
        if(CollectionUtils.isNotEmpty(dto.getMessengerMediaFiles())) {
        	messageDocumentDTO.setMediaFiles(dto.getMessengerMediaFiles().stream().map(messengerMediaFile -> new MessageDocument.MediaFile(null, messengerMediaFile.getUrl(), messengerMediaFile.getContentSize(), messengerMediaFile.getName(), messengerMediaFile.getContentType())).collect(
        			Collectors.toList()));
        }
        messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        if( messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)){
            messageDocumentDTO.setSpam(true);
        }else{
            messageDocumentDTO.setSpam(false);
        }
        return messageDocumentDTO;
    }

    public MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
        SMSMessageDTO dto = (SMSMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
        notificationRequest.setBizId(businessDTO.getBusinessId());
        notificationRequest.setBusinessName(businessDTO.getBusinessName());
        notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
        notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
        notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
        notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
        notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
        notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
        notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setMsgId(dto.getSmsDTO().getSmsId());
        notificationRequest.setProductName(businessDTO.getProductName());
        notificationRequest.setCount(10); // number of messages to be fetched from ES
        // The creation time is the last received time if last delivery time is null.
        if (dto.getSmsDTO().getCreateDate() != null) {
            notificationRequest.setLastMsgTime(dto.getSmsDTO().getCreateDate().getTime());
        } else {
            notificationRequest.setLastMsgTime(new Date().getTime());
            log.info("SMSReceiveHandler: Both sms sentOn and createDate found null for businessId {} smsID {} customer {}", businessDTO.getBusinessId(), dto.getSmsDTO().getSmsId(), dto.getCustomerDTO().getPhone());
        }
        notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
        return notificationRequest;

    }

    @Override
    void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SMSMessageDTO dto = (SMSMessageDTO) messageDTO;
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
        lastMessageMetadataPOJO.setLastMessageChannel(Channel.SMS.name());
        lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.SMS.getSourceId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.SMS.getSourceId());
        messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

        //TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
        /*LastMessageMetaData lastMessageMetaData = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
        lastMessageMetaData.setLastMessageSource(Source.SMS.getSourceId());
        lastMessageMetaData.setLastReceivedMessageSource(Source.SMS.getSourceId());
        messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));*/
        messengerContact.setLastMsgOn(dto.getSmsDTO().getSentOn());
        messengerContact.setUpdatedAt(dto.getSmsDTO().getSentOn());
        messengerContact.setLastIncomingMessageTime(dto.getSmsDTO().getSentOn().getTime());
    }

    @Override
    void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
        SmsDTO smsDTO = smsMessageDTO.getSmsDTO();
        CustomerDTO customerDTO = getCustomerDTO(messageDTO);
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (CollectionUtils.isNotEmpty(smsMessageDTO.getMessengerMediaFiles())) {
        	if(smsMessageDTO.getMessengerMediaFiles().size()>1) {
        		messengerContact.setLastMessage("Received attachments");
        	}else {
        		messengerContact.setLastMessage("Received an attachment");
        	}
        } else {
            messengerContact.setLastMessage(smsDTO.getMessageBodyUnencrypted());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, smsDTO.getEncrypted(), customerDTO.getPhone(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
    }

    @Override
    public MessengerEvent getEvent() {
        return EVENT;
    }

    @Override
    MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            CustomerDTO customerDTO = getCustomerDTO(messageDTO);
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
            long startTime = System.currentTimeMillis();
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), customerDTO.getId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
            long endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("getOrCreateContactForExistingCustomer", startTime, endTime);
            /**
             * Conditions to be checked, before checking and marking spam and block
             * 1.Customer shouldn't be blocked and marked spam by business
             * 2.Customer name shouldn't be present in customer dto
             * 3.If unblocked by business don't check
             */
            MessageTag messageTag = getMessageTag(messageDTO);
            SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
            if(Boolean.FALSE.equals(smsMessageDTO.getIsShortCodeNumber())){
                spamDetectionService.spamDetectionAllChannels(messageDTO,messengerContact,customerDTO,messageTag);
            }
        }
        return messengerContact;
    }

    @Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
            SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
			messageId = smsMessageDTO.getSmsDTO().getSmsId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
    }

    @Override
    public MessageResponse handle(MessageDTO messageDTO) throws Exception {
        messageDTO.setMsgTypeForResTimeCalc("R");
        messageDTO.setSource(Source.SMS.getSourceId());
        SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
        
        validate(smsMessageDTO);
        
        smsFromShortNumber(smsMessageDTO);
        
        CustomerDTO customerDto = null;
        if (smsMessageDTO.isSharedTFN()) {
        	log.info("smsReceive via SharedTFN");
        	if (Objects.isNull(smsMessageDTO.getLastSentBusinessId())) {
        		log.info("Rejecting event - LastSentBusinessId is null {}", smsMessageDTO);
        		pushReceivedSMSAttributionEvent(smsMessageDTO.getAuditId(), null, 0);
        		return null;
        	}
        	//call kontacto lookup api and get customerDto
        	customerDto = contactService.lookupCustomerForTfn(smsMessageDTO);
        	if (Objects.nonNull(customerDto)) {
        		log.info("smsReceive via SharedTFN, for customerId : {}", customerDto.getId());
        		smsMessageDTO.setCustomerDTO(customerDto);
        		if (Objects.nonNull(customerDto.getBusinessId()))
        			pushReceivedSMSAttributionEvent(smsMessageDTO.getAuditId(), customerDto.getBusinessId(), 1);
        			smsMessageDTO.setBusinessDTO(businessService.getBusinessDTO(customerDto.getBusinessId()));
        	} else {
        		log.info("Rejecting event - Contact could not be attributed {}", smsMessageDTO);
        		pushReceivedSMSAttributionEvent(smsMessageDTO.getAuditId(), null, 0);
        		return null;
        	}
        }
        
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        Integer acccountId = businessDTO.getAccountId();

        //bypass inbox check for 'stop'/'start' from customer
        customerDto = shouldBypassInboxCheck(acccountId, businessDTO.getBusinessId(), smsMessageDTO.getFromNumber());

        if (!ObjectUtils.isEmpty(customerDto)) {
        	smsMessageDTO.setCustomerDTO(customerDto);
        	updateCustomerSubscribeUnsubscribeStatus(smsMessageDTO);
        	receiveMsgBypassInboxAuditService.createReceiveMsgBypassInboxAudit(acccountId, businessDTO.getBusinessId(), customerDto.getId(), "stop", "sms");
        }
        if (Integer.valueOf(0).equals(businessService.isMessengerEnabled(acccountId))) {
        	log.info("SMSReceiveHandler: Messenger not enabled for business {}", acccountId);
        	return new MessageResponse();
        }

        customerDto = getCustomerDTO(smsMessageDTO);
    	updateCustomerSubscribeUnsubscribeStatus(smsMessageDTO);
        messageDTO.setCustomerDTO(customerDto);
    	smsMessageDTO.setCustomerId(customerDto.getId());
    	smsMessageDTO.setBusinessId(businessDTO.getBusinessId());
    	Optional<Lock> lockOpt = Optional.empty();
		String lockKey = "";
		try {
			String keyForCampaignCustomer = Constants.CAMPAIGN_CUSTOMER_ID+customerDto.getId();
			String campaignEventFromRedis = redisHandler.getKeyValueFromRedis(keyForCampaignCustomer);
			if(!StringUtils.isEmpty(campaignEventFromRedis)) {
				log.info("SMSReceiveEventHandler: campaign event found in redis. CustomerId {}", customerDto.getId());
				ObjectMapper objectMapper = new ObjectMapper();
				CampaignSMSDto campaignSMSDto  = objectMapper.readValue(campaignEventFromRedis, CampaignSMSDto.class);
                campaignApplicationEventPublisher.publishCampaignEvent(campaignSMSDto);
			}
			lockKey = Constants.CUSTOMER_ID_PREFIX+customerDto.getId();
			lockOpt = redisLockService.tryLock(lockKey, 1,TimeUnit.SECONDS);
			if (lockOpt.isPresent()){
				return lockAcquiredSMSReceive(messageDTO, smsMessageDTO, customerDto, businessDTO);
			}else {
				//add to delayed queue
				log.info("[SMS Receive Event] Unable to acquire lock key:{}",lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_RECEIVE_EVENT_DELAYED_QUEUE,smsMessageDTO);
			}
		}finally {
			if (lockOpt.isPresent()){
				redisLockService.unlock(lockOpt.get());
			}
		}
		return null;
    }

    private CustomerDTO shouldBypassInboxCheck(Integer acccountId, Integer businessId, String fromNumber) {
    	Integer inboxEnabled = businessService.isMessengerEnabled(acccountId);
    	if (Integer.valueOf(0).equals(inboxEnabled)) {
    		List<CustomerDTO> customers = contactService.findAllCustomer(fromNumber, null, businessId, acccountId);
    		if (CollectionUtils.isNotEmpty(customers)) {
    			return customers.get(0);
    		}
    	}
		return null;
    }

    private MessageResponse lockAcquiredSMSReceive(MessageDTO messageDTO, SMSMessageDTO smsMessageDTO,
			CustomerDTO customerDto, BusinessDTO businessDTO) throws Exception {
		//Check for Pulse Survey context on the basis of customer_id and business_id
    	//If Context found, Push to Survey and Audit & Return
    	PulseSurveyContext context = pulseSurveyService.pulseContextCheckSmsReceive(smsMessageDTO, customerDto, businessDTO);
    	if (!ObjectUtils.isEmpty(context)) {
    		return null;
    	}
    	// ------- should we put below two lines of DB insert code under same transaction --------------- ?
        Sms sms = smsService.saveSms(smsMessageDTO);
        
        saveMediaFiles(smsMessageDTO, sms.getSmsId());
        
        ConversationDTO conversationDTO=new ConversationDTO();
        Optional<ActivityType> translateToActivity = translateToActivity(smsMessageDTO);
        
		if (translateToActivity.isPresent()) {
            messageDTO.setMsgTypeForResTimeCalc("");
			smsMessageDTO.setSendEmailNotification(false);
			ActivityDto activityDTO = ActivityDto.builder().activityType(translateToActivity.get())
					.created(new Date()).mcId(getMessengerContact(smsMessageDTO).getId()).build();
			ConversationActivity activity = ConversationActivityService.create(activityDTO);
			activityDTO.setId(activity.getId());
			smsMessageDTO.setActivityDto(activityDTO);
			UserDTO userDTO = new UserDTO();
			userDTO.setId(-7);
			smsMessageDTO.setUserDTO(userDTO);
			smsMessageDTO.setUpdateLastMessage(false);
			messengerMessageService.saveMessengerActivity(activity, activityDTO.getMcId(), userDTO);			
		} else {
        	addMessageMetaData(conversationDTO);
            SmsDTO smsDTO = new SmsDTO(sms,conversationDTO);
            smsMessageDTO.setSmsDTO(smsDTO);
            smsDTO.setMessengerContactId(getMessengerContact(smsMessageDTO).getId());
            smsDTO.setMessageBodyUnencrypted(smsMessageDTO.getBody()); // we need to re-encrypt it for messengerContact (encryption key is different for message and contact )
            messengerMessageService.saveMessengerMessage(smsDTO, null);
            smsMessageDTO.setUserDTO(new UserDTO()); // customer sends message to business and not to a businessUser
            smsMessageDTO.setSmsDTO(smsDTO);
        }
		//Publish event to Kafka when sms is received : communication history
        pushSmsReceiveEventToKafka(sms,businessDTO,Channel.SMS);
        Boolean triggerRobinAutoReplies = appointmentConfirmationService.checkAppointmentConfirmationContext(businessDTO,getMessengerContact(smsMessageDTO),smsMessageDTO.getBody());
        messageDTO.setTriggerRobinAutoReplies(triggerRobinAutoReplies);
        if(messageDTO.getActivityDto()!=null && (ActivityType.SMS_SUBSCRIBE.equals(messageDTO.getActivityDto().getActivityType()) || ActivityType.SMS_UNSUBSCRIBE.equals(messageDTO.getActivityDto().getActivityType()))) {
        	messageDTO.setUpdateTag(false);
        }
        MessageResponse messageResponse = super.handle(messageDTO);
        appointmentConfirmationService.checkAndConfirmAppointment(businessDTO,getMessengerContact(smsMessageDTO),smsMessageDTO.getBody());
        return messageResponse;
	}
    
    private Optional<ActivityType> translateToActivity(SMSMessageDTO smsMessageDTO) {
    	if (StringUtils.isNotBlank(smsMessageDTO.getBody())) {
            if (MessengerUtil.isStopWord(smsMessageDTO.getBody().trim())) {
                return Optional.of(ActivityType.SMS_UNSUBSCRIBE);
            }
            if (StringUtils.equalsIgnoreCase(smsMessageDTO.getBody().trim(), "START")) {
                return Optional.of(ActivityType.SMS_SUBSCRIBE);
            }
        }
    	return Optional.empty();
    }

    private void addMessageMetaData(ConversationDTO conversationDTO) {
		conversationDTO.setMessageType(MessageType.CHAT);
		conversationDTO.setChannel(Channel.SMS);
		conversationDTO.setCommunicationDirection(CommunicationDirection.RECEIVE);
	}

	private void saveMediaFiles(SMSMessageDTO smsMessageDTO, Integer messageId) {
        List<MessengerMediaFile> messengerMediaFiles =new ArrayList<MessengerMediaFile>();
        if (CollectionUtils.isNotEmpty(smsMessageDTO.getMedia())) {
            for (SMSMessageDTO.Media media : smsMessageDTO.getMedia()) {
                MessengerMediaFile mediaFile = new MessengerMediaFile(media, messageId);
                long startTime = System.currentTimeMillis();
                messengerMediaFiles.add(messengerMediaFileService.saveMedia(mediaFile));
                long endTime = System.currentTimeMillis();
                LogUtil.logExecutionTime("saveMediaFiles", startTime, endTime);
            }
           smsMessageDTO.setMessengerMediaFiles(messengerMediaFiles); 
        }
    }

    private void validate(SMSMessageDTO smsMessageDTO) {
        //. check for profanity
        if(StringUtils.isNotBlank(smsMessageDTO.getBody()) && nlpService.isTextProfane(smsMessageDTO.getBody())) {
            log.info("SMSReceiveHandler: Input message not saved due to profanity filtering {}", smsMessageDTO.getTwilioSMSId());
            throw new BadRequestException("Message not saved as it qualified as profane");
        }
        // check if duplicate message is present with same twiilio Id
        List<Sms> existingSms = smsService.findByMessageSid(smsMessageDTO.getTwilioSMSId());
        if(CollectionUtils.isNotEmpty(existingSms)) {
            log.info("SMSReceiveHandler: discarded as duplicate message with same Twilio Id {}", smsMessageDTO.getTwilioSMSId());
            throw new BadRequestException("Message Discarded as other message found with same messageId");
        }
    }

    private KontactoRequest create(SMSMessageDTO smsMessageDTO, BusinessDTO businessDTO) {
    	KontactoRequest kontactoRequest = new KontactoRequest();
    	kontactoRequest.setPhone(smsMessageDTO.getFromNumber());
    	kontactoRequest.setBusinessId(businessDTO.getBusinessId());
    	kontactoRequest.setSource(KontactoRequest.TEXT);
    	KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
    	locationInfo.setCountryCode(businessDTO.getCountryCode());
    	kontactoRequest.setLocation(locationInfo);
    	kontactoRequest.setSmsEnabled(1);
    	checkForOtpMsgOtherCountryCode(smsMessageDTO.getFromNumber(), smsMessageDTO.getToNumber(), smsMessageDTO.getBody(), kontactoRequest);
    	return kontactoRequest;
    }

    //BIRDEYE-140890 : Receiving otp on AU number from US number
    private void checkForOtpMsgOtherCountryCode(String customerPhoneNumber, String businessPhoneNumber, String text, KontactoRequest kontactoRequest) {
    	String phoneNumberPair = businessPhoneNumber + ":" + customerPhoneNumber;
    	String otpPairPhoneNumbers = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("business_otp_pair_phone_numbers", "b_phone:c_phone");
    	List<String> otpPairPhoneNumbersList=ControllerUtil.getTokensListFromString(otpPairPhoneNumbers);
    	if (CollectionUtils.isNotEmpty(otpPairPhoneNumbersList) && otpPairPhoneNumbersList.contains(phoneNumberPair)) {
    		kontactoRequest.setPhone(businessPhoneNumber);
    	}
    }
    
	@Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
        if(Objects.isNull(smsMessageDTO.getBusinessDTO()) || Objects.isNull(smsMessageDTO.getBusinessDTO().getBusinessId())) {
            long startTime = System.currentTimeMillis() ;
//            String businessIdString = platformDbRepository.getBusinessIdBySmsNumber(smsMessageDTO.getToNumber());
            String businessIdString = getBusinessIdBySmsNumber(smsMessageDTO.getToNumber());
            long endTime = System.currentTimeMillis() ;
            LogUtil.logExecutionTime("getBusinessIdBySmsNumber", startTime, endTime);
            if(StringUtils.isBlank(businessIdString)) throw new NotFoundException("No businessId found for given business_sms number " + smsMessageDTO.toString());
            startTime = System.currentTimeMillis();
            BusinessDTO businessDTO = communicationHelperService.getBusinessDTO(Integer.parseInt(businessIdString));
            endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("getBusinessDTO", startTime, endTime);
            if(Objects.isNull(businessDTO)) throw new NotFoundException("No business found for given request " + smsMessageDTO.toString());
            smsMessageDTO.setBusinessDTO(businessDTO);
        }
        return smsMessageDTO.getBusinessDTO();
    }

    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
        SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
        List<Integer> accountIds=contactService.getAllContactUpgradeEnabledAccounts();
        if(Objects.isNull(smsMessageDTO.getCustomerDTO()) && CollectionUtils.isNotEmpty(accountIds)&& messageDTO.getBusinessDTO()!=null && accountIds.contains(messageDTO.getBusinessDTO().getAccountId())){
        	CustomerDTO customerDTO =commonService.getActiveCustomerAndUpdateConversationState(smsMessageDTO.getFromNumber(),null,smsMessageDTO.getBusinessDTO().getBusinessId(),smsMessageDTO.getBusinessDTO().getAccountId());
            if(Objects.nonNull(customerDTO)) {
            	smsMessageDTO.setCustomerDTO(customerDTO);
            }
        }
        if(Objects.isNull(smsMessageDTO.getCustomerDTO()) || Objects.isNull(smsMessageDTO.getCustomerDTO().getBusinessId())) {
            // communication with Kontackto Service
            KontactoRequest kontactoRequest = create(smsMessageDTO, getBusinessDTO(smsMessageDTO));
            CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, getBusinessDTO(smsMessageDTO).getRoutingId(), -1);
         // check if customer wants to unsubscribe for sms based on message body
            if(Objects.isNull(customerDTO) || Objects.isNull(customerDTO.getId())) throw new NotFoundException("Customer data not returned by contact-service for Twilio messageId " + smsMessageDTO.getTwilioSMSId());
            smsMessageDTO.setCustomerDTO(customerDTO);
        }
        return smsMessageDTO.getCustomerDTO();
    }

    @Override
    protected boolean getSendEmailNotification(MessageDTO messageDTO) {
        SMSMessageDTO smsMessageDTO = (SMSMessageDTO) messageDTO;
        if(messageDTO.getMessengerContact().getSpam().equals(true)){
            return false;
        }
        if (StringUtils.isNotBlank(smsMessageDTO.getBody())) {
            if (MessengerUtil.isStopWord(smsMessageDTO.getBody().trim())) {
                smsMessageDTO.setSendEmailNotification(false);
                return false;
            }
            if (StringUtils.equalsIgnoreCase(smsMessageDTO.getBody().trim(), "START")) {
                smsMessageDTO.setSendEmailNotification(false);
                return false;
            }
        }
        smsMessageDTO.setSendEmailNotification(true);
        return true;
    }

	@Override
	void publishEvent(MessageDTO messageDTO) {
		//Do Nothing
	}

    @Override
    protected boolean isFireBaseSyncEnabled(MessageDTO messageDTO) {
        if(messageDTO.getMessengerContact().getSpam().equals(true)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private void pushSmsReceiveEventToKafka(Sms sms,BusinessDTO businessDTO,Channel channel){
        try {
            SMSReceiveEvent smsReceiveEvent = new SMSReceiveEvent(sms.getCustomerId(),sms.getBusinessId(), businessDTO.getAccountId(),sms.getCreateDate(),sms.getSmsId(),channel.toString());
            kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_RECEIVE_EVENT,smsReceiveEvent);
        }catch (Exception e){
            log.error("Exception while pushing sms receive event to kafka : {}",e);
        }

    }

    public String getBusinessIdBySmsNumber(String smsNumber) {
        InfoByPhoneNumberRequest businessRequest = new InfoByPhoneNumberRequest(InfoByPhoneNumberRequest.Type.sms, smsNumber);
        BusinessDTO businessDTO = businessService.getBusinessInfoByPhoneNumber(businessRequest);
        if(businessDTO != null && StringUtils.isNotEmpty(businessDTO.getBusinessId().toString())){
            return businessDTO.getBusinessId().toString();
        }
        return null;
    }
    
    private void pushReceivedSMSAttributionEvent(Integer auditId, Integer businessId, Integer attribution){
        try{
        	ReceivedSMSAttibutionCallbackRequest request = new ReceivedSMSAttibutionCallbackRequest(auditId, 
        			businessId, attribution);
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.SMS_ATTRIBUTION_EVENT,request);
                log.info("event pushed for sms attribution {}", request);
        }catch(Exception e){
            log.error("Exception occur while pushing sms attribution event",e);
        }
    }
    
    private ContactDocument getConversationByPhoneNumber(Integer acccountId, Integer businessId, String phoneNumber) {
    	String customerPhoneNumberWithoutCountryCode = PhoneNoValidator.removeCountryCodeFromPhoneNumber(phoneNumber);
        MessengerFilter filter = new MessengerFilter();
        Map<String, Object> params = new HashMap<>();
        params.put("businessId", businessId);
        params.put("searchTerm", customerPhoneNumberWithoutCountryCode);
        filter.setAccountId(acccountId);
        filter.setQueryFile(Constants.Elastic.GET_CONVERSATION_BY_PHONE_NUMBER);
        filter.setParams(params);
        List<ContactDocument> contactFromES = messengerContactService.getConversationFromES(filter);
        if (CollectionUtils.isEmpty(contactFromES)) {
            log.info("No conversation exisits for businessId: {} and phoneNumber: {} ", businessId, phoneNumber);
            return null;
        }
        ContactDocument conversation = contactFromES.get(0);
        return conversation;
    }
    
    private void smsFromShortNumber(SMSMessageDTO smsMessageDTO){
    	if(Boolean.TRUE.equals(smsMessageDTO.getIsShortCodeNumber())) {
    		BusinessDTO businessDTO = getBusinessDTO(smsMessageDTO);
    		String shortCodeEnabledAccount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("short_code_receive_enabled_accounts", "261968");
    		if(shortCodeEnabledAccount.contains(String.valueOf(businessDTO.getAccountId())) && Objects.nonNull(smsMessageDTO.getToNumber())){
    			if(StringUtils.isNotBlank(smsMessageDTO.getBody())){
    				String newBody = String.format("From: %s%nMessage: %s", smsMessageDTO.getFromNumber(), smsMessageDTO.getBody());
    				smsMessageDTO.setBody(newBody);
    			}
    			smsMessageDTO.setFromNumber(smsMessageDTO.getToNumber());
    		}else {
    			throw new BadRequestException("Message From Short Code Number Not Supported");
    		}
    	}
    }

	private void updateCustomerSubscribeUnsubscribeStatus(SMSMessageDTO smsMessageDTO) {

		if (Objects.nonNull(smsMessageDTO.getCustomerDTO()) && Objects.nonNull(smsMessageDTO.getCustomerDTO().getId())
				&& StringUtils.isNotBlank(smsMessageDTO.getBody())) {
			if (smsMessageDTO.getCustomerDTO().getCommPreferences() != null
					&& smsMessageDTO.getCustomerDTO().getCommPreferences().getSmsPreferences() != null) {
				SmsPreferencesDto smsPreferences = smsMessageDTO.getCustomerDTO().getCommPreferences().getSmsPreferences();

				if (MessengerUtil.isStopWord(smsMessageDTO.getBody().trim())) {
					// For STOP keyword, update all channels (opt out from everything)
					contactService.updateCustomerSubscriptionStatus(smsMessageDTO.getCustomerDTO().getId(),
							smsMessageDTO.getBusinessDTO().getAccountId(), true, null, null, null);

					// Update all preferences in the smsPreferences object
					smsPreferences.setServiceOptin(false);
					smsPreferences.setServiceOptoutType("Opted out");
					smsPreferences.setMarketingOptin(false);
					smsPreferences.setMarketingOptoutType("Opted out");
					smsPreferences.setFeedbackOptin(false);
					smsPreferences.setFeedbackOptoutType("Opted out");

					// TODO check if we Also update legacy field for backward compatibility?

					smsMessageDTO.getCustomerDTO().getCommPreferences().setSmsUnsubType("Opted out");
				} else if (!MessengerUtil.isStopWord(smsMessageDTO.getBody().trim())
						&& !(MessengerUtil.isKeywordPresent(smsMessageDTO.getBody().trim()).isPresent())
						&& !BooleanUtils.isTrue(smsPreferences.getServiceOptin())) {
					contactService.updateCustomerSubscriptionStatus(smsMessageDTO.getCustomerDTO().getId(),
							smsMessageDTO.getBusinessDTO().getAccountId(), false, null, Constants.KEYWORD_JOINSERV, null);

					// Update only the service preferences
					smsPreferences.setServiceOptin(true);
					smsPreferences.setServiceOptoutType("");
					// TODO check if we Also update legacy field for backward compatibility?
				}
			}
			String contactsTextCategoriesEnabled = businessService
					.getBusinessFeaturesByAccountId(smsMessageDTO.getBusinessDTO().getEnterpriseId() != null
							? smsMessageDTO.getBusinessDTO().getEnterpriseId()
							: smsMessageDTO.getBusinessDTO().getBusinessId())
					.getFeatures().get(CONTACTS_TEXT_CATEGORIES_ENABLED);
			if (Boolean.parseBoolean(contactsTextCategoriesEnabled)) {
				handleKeywords(smsMessageDTO);
			}

		}
	}
	
	private void handleKeywords(SMSMessageDTO smsMessageDTO) {
		Optional<String> keywordOpt = MessengerUtil.isKeywordPresent(smsMessageDTO.getBody().trim());
		if (keywordOpt.isPresent()) {
			String keyword = keywordOpt.get();

			if (smsMessageDTO.getCustomerDTO().getCommPreferences() != null
					&& smsMessageDTO.getCustomerDTO().getCommPreferences().getSmsPreferences() != null) {
				SmsPreferencesDto smsPreferences = smsMessageDTO.getCustomerDTO().getCommPreferences()
						.getSmsPreferences();
				
				smsMessageDTO.setSendEmailNotification(false);

				// Only proceed with the update if needed
				if (isUpdateNeeded(smsPreferences, keyword)) {
					boolean success = contactService.updateCustomerSubscriptionStatus(
							smsMessageDTO.getCustomerDTO().getId(), smsMessageDTO.getBusinessDTO().getAccountId(), null,
							null, keyword, null);

					// Update the smsPreferences object directly based on the keyword
					if (success) {
						// Update the appropriate channel based on keyword
						if (Constants.KEYWORD_NOMKT.equals(keyword)) {
							smsPreferences.setMarketingOptin(false);
							smsPreferences.setMarketingOptoutType("Opted out");
						} else if (Constants.KEYWORD_NOFB.equals(keyword)) {
							smsPreferences.setFeedbackOptin(false);
							smsPreferences.setFeedbackOptoutType("Opted out");
						} else if (Constants.KEYWORD_NOSERV.equals(keyword)) {
							smsPreferences.setServiceOptin(false);
							smsPreferences.setServiceOptoutType("Opted out");
						} else if (Constants.KEYWORD_JOINMKT.equals(keyword)) {
							smsPreferences.setMarketingOptin(true);
							smsPreferences.setMarketingOptoutType("");
						} else if (Constants.KEYWORD_JOINFB.equals(keyword)) {
							smsPreferences.setFeedbackOptin(true);
							smsPreferences.setFeedbackOptoutType("");
						} else if (Constants.KEYWORD_JOINSERV.equals(keyword)) {
							smsPreferences.setServiceOptin(true);
							smsPreferences.setServiceOptoutType("");
						}

						// Set the pending auto-reply only if an update was made
						smsMessageDTO.setPendingAutoReply(keyword);
						// We do not send Robin auto reply for the keywords received.
						smsMessageDTO.setMsgTypeForResTimeCalc("");
						
					}
				} else {
					// Log that no update was needed
					log.info("No update needed for keyword received {}",
							keyword);
				}
			}
		}
	}

	private boolean isUpdateNeeded(SmsPreferencesDto smsPreferences, String keyword) {

		if (Constants.KEYWORD_NOMKT.equals(keyword) && BooleanUtils.isTrue(smsPreferences.getMarketingOptin())) {
			return true;
		} else if (Constants.KEYWORD_NOFB.equals(keyword) && BooleanUtils.isTrue(smsPreferences.getFeedbackOptin())) {
			return true;
		} else if (Constants.KEYWORD_NOSERV.equals(keyword) && BooleanUtils.isTrue(smsPreferences.getServiceOptin())) {
			return true;
		} else if (Constants.KEYWORD_JOINMKT.equals(keyword)
				&& !BooleanUtils.isTrue(smsPreferences.getMarketingOptin())) {
			return true;
		} else if (Constants.KEYWORD_JOINFB.equals(keyword)
				&& !BooleanUtils.isTrue(smsPreferences.getFeedbackOptin())) {
			return true;
		} else if (Constants.KEYWORD_JOINSERV.equals(keyword)
				&& !BooleanUtils.isTrue(smsPreferences.getServiceOptin())) {
			return true;
		}
		return false;

	}
}
