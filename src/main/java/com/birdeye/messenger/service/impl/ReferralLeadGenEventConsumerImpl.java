package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.service.*;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReferralLeadGenEventConsumerImpl implements ReferralLeadGenEventConsumer {

    private final MessengerContactService messengerContactService;
    private final ContactService contactService;
    private final BusinessService businessService;
    private final ConversationActivityService conversationActivityService;
    private final FirebaseService firebaseService;
    private final RedisLockService redisLockService;
    private final KafkaService kafkaService;

    /*
        Need to check these conditions before/during processing the event :
        1. No Contact for Lead exists - done
        --------------------- point 2 - 4 relevant for migration purposes hence need to
                             know from contact-service whether a contact is lead or customer ------------
        2. Contact for Lead already created while navigating from campaign/customer tab but no messages sent
        3. Contact for Lead already created and message sent
        4. Contact now already converted to a customer -- discard the event in this case.

     */
    @Override
    public void consumeEvent(ReferralEvent event) {
        log.info("ReferralLeadGenEventConsumer : {}", event);
        BusinessOptionResponse config = businessService.getBusinessOptionsConfig(event.getLocationId(), Boolean.TRUE);
        boolean rejectColdLeads = Objects.nonNull(config) && (config.getIsSuspectSupportOn() == null || config.getIsSuspectSupportOn() == 0);
        if("suspect".equals(event.getLeadType()) && (rejectColdLeads)) {
            log.info("ReferralLeadGenEventConsumer: event dropped as lead type is {} and accountId:{}", event.getLeadType(), event.getAccountId());
            return;
        }
        ReferralSource referralSource = event.getReferralSource();
        CustomerDTO lead = contactService.findByIdNoCaching(event.getCid());
        Assert.notNull(lead, "ReferralLeadGenEventConsumer: No customer/lead returned by contact-service for id" + event.getCid());

        if(ContactState.PRE_LEAD.equals(lead.getContactState()) && rejectColdLeads) {
            log.info("ReferralLeadGenEventConsumer: event dropped as contact state is {}", lead.getContactState());
            return;
        }
        if(!lead.isLead() || !LeadSource.REFERRAL.equals(lead.getLeadSource())) {
            log.info("ReferralLeadGenEventConsumer: lead {} already converted to customer or lead source is not referral", lead);
            return;
        }

        BusinessDTO businessDTO = businessService.getBusinessDTO(event.getBusinessId());
        Assert.notNull(businessDTO, "ReferralLeadGenEventConsumer: No business returned by core-service for id" + event.getBusinessId());

        CustomerDTO referrer = contactService.findById(event.getReferrerCid());
        Assert.notNull(referrer, "ReferralLeadGenEventConsumer: No customer/lead returned by contact-service for id" + event.getReferrerCid());

        Object[] referredLeadData = null;
        Object[] referrerData = null;

        final String leadLock = Constants.CUSTOMER_ID_PREFIX + lead.getId();
        final String referrerLock = Constants.CUSTOMER_ID_PREFIX + referrer.getId();
        Optional<Lock> lockOnLeadOpt = Optional.empty();
        Optional<Lock> lockOnReferrerOpt = Optional.empty();
        lockOnLeadOpt = redisLockService.tryLock(leadLock,2,TimeUnit.SECONDS);
        try {
            if (lockOnLeadOpt.isPresent()) {
            	lockOnReferrerOpt = redisLockService.tryLock(referrerLock, 2,TimeUnit.SECONDS);
                if (lockOnReferrerOpt.isPresent()) {
                    referredLeadData = createOrUpdateReferredLeadInInbox(businessDTO, lead, referralSource);
                    referrerData = createOrUpdateReferrerInInbox(businessDTO, referrer);
                } else {
                    kafkaService.publishToKafkaAsync(KafkaTopicEnum.REFERRAL_LEAD_GEN_DELAYED_QUEUE,event);
                    throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
                }
            } else {
                kafkaService.publishToKafkaAsync(KafkaTopicEnum.REFERRAL_LEAD_GEN_DELAYED_QUEUE,event);
                throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
            }
		} finally {
			if (lockOnLeadOpt.isPresent()) {
				redisLockService.unlock(lockOnLeadOpt.get());
			}
			if (lockOnReferrerOpt.isPresent()) {
				redisLockService.unlock(lockOnReferrerOpt.get());
			}
		}

        MessengerContact referrerMc = (MessengerContact) referrerData[0];
        MessengerContact leadMc = (MessengerContact) referredLeadData[0];

        Date created = event.getLeadCreatedAt();
        ActivityDto referrerActivity = ActivityDto.builder().mcId(referrerMc.getId()).created(created).updated(created)
                .activityType(ActivityType.REFERRER_LEAD_GEN)
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .thankYouNoteStatus(StatusEnum.NOT_SENT)
                .source(Source.REFERRAL.getSourceId())
                .referralSource(event.getReferralSource())
                .referrerMcId(referrerMc.getId()).referrerName(MessengerUtil.getNameWithFallback(referrer, true, true)).referrerCId(referrer.getId())
                .referredLeadMcId(leadMc.getId()).referredLeadName(MessengerUtil.getNameWithFallback(lead, true, true)).referredLeadCId(lead.getId())
                .accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
                .build();

        saveActivity(referrerActivity);
        ContactDocument referrerContactDoc = (ContactDocument) referrerData[1];
        pushToFirebaseForUIMirroring(referrerContactDoc);

        ActivityDto referrerLeadActivity = ActivityDto.builder().mcId(leadMc.getId()).created(created).updated(created)
                .activityType(ActivityType.REFERRAL_LEAD_GEN)
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .thankYouNoteStatus(StatusEnum.NOT_SENT)
                .source(Source.REFERRAL.getSourceId())
                .referralSource(event.getReferralSource())
                .referrerMcId(referrerMc.getId()).referrerName(MessengerUtil.getNameWithFallback(referrer, true, true)).referrerCId(referrer.getId())
                .referredLeadMcId(leadMc.getId()).referredLeadName(MessengerUtil.getNameWithFallback(lead, true, true)).referredLeadCId(lead.getId())
                .accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
                .build();

        if(StringUtils.isNotBlank(event.getQuestion()) && StringUtils.isNotBlank(event.getAnswer())) {
            MessageDocument.QuestionAndAnswer questionAndAnswer = new MessageDocument.QuestionAndAnswer(event.getQuestion(), event.getAnswer());
            List<MessageDocument.QuestionAndAnswer> questionAndAnswerList = new ArrayList<>();
            questionAndAnswerList.add(questionAndAnswer);
            referrerLeadActivity.setQnaList(questionAndAnswerList);
        }

        MessageDocument leadActivityDoc = saveActivity(referrerLeadActivity);
        ContactDocument referredLeadContactDoc = (ContactDocument) referredLeadData[1];
        pushToFirebaseForUIMirroring(referredLeadContactDoc);
        firebaseService.pushFCMNotificationForWEB(referredLeadContactDoc, leadActivityDoc, businessDTO, null, null,null);
    }

    private MessageDocument saveActivity(ActivityDto activity) {
        conversationActivityService.persistActivityInDatabase(activity, null);
        return conversationActivityService.persistActivityInES(activity);
    }

    private void pushToFirebaseForUIMirroring(ContactDocument contactDocument) {
        //firebaseService.mirrorOnWeb(contactDocument.getE_id(), contactDocument.getB_id());
        FirebaseDto firebaseDto = new FirebaseDto();
        firebaseDto.setAccountId(contactDocument.getE_id());
        firebaseDto.setBusinessId(contactDocument.getB_id());
        firebaseDto.setMcId(contactDocument.getM_c_id());
        firebaseService.mirrorOnWeb(firebaseDto);
    }

    private Object[] createOrUpdateReferrerInInbox(BusinessDTO businessDTO, CustomerDTO referrer) {
        MessengerContact referrerMc = messengerContactService.getOrCreateContactForExistingCustomer(businessDTO.getBusinessId(), referrer.getId(), businessDTO.getAccountId());
        referrerMc.setReferrer(true);
        referrerMc.setLead(false); // this guy cannot be a lead since he referred someone
        referrerMc = messengerContactService.saveOrUpdateMessengerContact(referrerMc);
        MessageTag tag = MessageTag.getMessageTagById(referrerMc.getTag());
        ContactDocument referrerDoc = messengerContactService.updateContactOnES(referrerMc, referrer, businessDTO, tag, null);
        return new Object[] {referrerMc, referrerDoc};
    }

    private Object[] createOrUpdateReferredLeadInInbox(BusinessDTO businessDTO, CustomerDTO lead, ReferralSource referralSource) {
        MessengerContact leadMc = messengerContactService.getOrCreateContactForExistingCustomer(businessDTO.getBusinessId(), lead.getId(), businessDTO.getAccountId());
        if(!Boolean.TRUE.equals(leadMc.getIsNew()) && LeadSource.REFERRAL.equals(leadMc.getLeadSource())) {
            log.error("Referral Lead {} already referred by someone else", lead);
            throw new BadRequestException(String.format("Lead %d %s already referred by someone", lead.getId(), lead.getFirstName()));
        }
        leadMc.setLead(true);
        leadMc.setReferrer(false);
        leadMc.setLeadSource(LeadSource.REFERRAL);
        leadMc.setReferralSource(referralSource);
        if(StringUtils.isBlank(leadMc.getLastMessage())){
            LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(leadMc); // Do we need anything else to populate here ?
            lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.REFERRAL.getSourceId());
            lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.REFERRAL.name());
            leadMc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        }
        leadMc = messengerContactService.saveOrUpdateMessengerContact(leadMc);
        ContactDocument referredLeadDoc = messengerContactService.updateContactOnES(leadMc, lead, businessDTO, MessageTag.INBOX, null);
        return new Object[]{leadMc, referredLeadDoc};
    }

}
