/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.*;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.dao.entity.Sms;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SmsMessageRetryClient extends MessageRetryClient {

	private final SmsService smsService;
	private final BusinessService businessService;
	private final MessengerContactService messengerContactService;
	private final ContactService contactService;
	private final UserService userService;
	private final SendMessageService sendMessageService;
	private final FirebaseService fcmService;
	private final MessengerMediaFileService messengerMediaFileService;
	private final NexusService nexusService;
	private final ElasticSearchExternalService elasticSearchExternalService;

	/**
	 * SMS messages for retry are already stored in DB (sms, messenger_contact) and
	 * ES (contactDocument & messageDocument) with a delivery status as failed or
	 * similar status. On resend, we need to update the following. 1. sms -> sentOn,
	 * ( to_number need not be updated ), failureReason 2. messenger_contact ->
	 * last_message_metadata (userId, userName, messageType) , last_responded_at,
	 * updated_at 3. contactDocument -> userId, userName, lastMessageType, type,
	 * updatedAt, l_msg (after re-encryption), l_msg_on, c_phone (not required as
	 * already updated by customer event), 4. messageDocument -> u_id, u_name,
	 * msg_status, type, updatedAt, createdBy, sentThrough.
	 */

	@Override
	public MessengerMessage.Message retrySendMessage(MessageRetry messageRetry) {
		return retrySmsSend(messageRetry.getId().intValue(), messageRetry.getMcId(), messageRetry.getAccountId(),
				messageRetry.getUserId(), messageRetry.getSourceId());
	}

	private MessengerMessage.Message retrySmsSend(Integer smsId, Integer mcId, Integer accountId, Integer userId,
			Integer source) {
		Sms sms = smsService.findById(smsId);
		Optional<MessengerMediaFile> mediaFileOptional = messengerMediaFileService
				.findByMsgId(sms.getSmsId()).stream()
				.findFirst();

		// set lastMessage for contact doc & message body for nexus
		if (StringUtils.isBlank(sms.getMessageBody()) && StringUtils.isBlank(sms.getMediaURL())) {
			return null;
		}

		String lastMessage = sms.getMessageBody();
		String message = sms.getMessageBody();
		if (StringUtils.isBlank(sms.getMessageBody())) {
			message = "";
			lastMessage = "Sent an attachment";
		}

		boolean decryptionFailed = Boolean.FALSE;
		if (StringUtils.isNotBlank(sms.getMessageBody()) && Objects.nonNull(sms.getEncrypted())
				&& sms.getEncrypted().equals(1)) {
			try {
				lastMessage = EncryptionUtil.decrypt(sms.getMessageBody(),
						StringUtils.join(sms.getFromNumber(), sms.getToNumber()),
						StringUtils.join(sms.getToNumber(), sms.getFromNumber()), false);
				message = lastMessage;
				if (StringUtils.equals(lastMessage, ""))
					throw new Exception("Decryption Failed");
			} catch (Exception e) {
				log.error(
						"retrySmsSend: decryption failed request info: smsId {}, mcId {}, accountId {}, userId {}, source {}",
						smsId, mcId, accountId, userId, source, e);
				decryptionFailed = Boolean.TRUE;
			}
		}

		if (!decryptionFailed) {
			MessengerContact messengerContact = messengerContactService.findByCustomerId(sms.getCustomerId());
			BusinessDTO businessDTO = businessService.getBusinessDTO(sms.getBusinessId());
			CustomerDTO customerDTO = contactService.findByIdNoCaching(sms.getCustomerId());
            
			if (Objects.isNull(messengerContact) || Objects.isNull(businessDTO) || Objects.isNull(customerDTO)) {
				log.error(
						"retrySmsSend: MessengerContact {} or Business {} or Customer {} not found. Request info: smsId {}, mcId {}, accountId {}, userId {}, source {}",
						messengerContact, businessDTO, customerDTO, smsId, mcId, accountId, userId, source);
				return null;
			}

			Date sentTime = new Date();

			// update sms
			sms.setSentOn(sentTime);
			sms.setToNumber(customerDTO.getPhoneE164());
			sms.setCreateDate(sentTime);
			sms.setFailureReason(null);
            message = append10DlcBrandingToMessageBody(message,businessDTO);
			String encryptedMessage = message;
			if (StringUtils.isNotBlank(message)) {
				try {
					encryptedMessage = EncryptionUtil.encrypt(message,
							StringUtils.join(sms.getFromNumber(), sms.getToNumber()),
							StringUtils.join(sms.getToNumber(), sms.getFromNumber()));
					sms.setEncrypted(1);
				} catch (Exception e) {
					log.error(
							"retrySmsSend: encryption failed request info: smsId {}, mcId {}, accountId {}, userId {}, source {}",
							smsId, mcId, accountId, userId, source, e);
					return null;
				}
			}
			sms.setMessageBody(encryptedMessage);

			UserDTO userDTO = userService.getUserDTO(userId);
			String lastMessageUserName = MessengerUtil.buildUserName(userDTO);

			// update messengerContact
			messengerContact.setLastResponseAt(sentTime);
			messengerContact.setUpdatedAt(sentTime);
			messengerContact.setLastMessage(lastMessage);
			// Need to encrypt lastMessage again with new customer phone since phone number
			// is changed before retrying to send sms
			EncryptionUtil.encryptLastMessage(messengerContact, messengerContact.getEncrypted(), customerDTO.getPhone(),
					businessDTO.getBusinessNumber());

			LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
			lastMessageMetadataPOJO.setLastMessageType(MessageDocument.CommunicationDirection.SEND.name());
			lastMessageMetadataPOJO.setLastMessageUserName(lastMessageUserName);
			lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
			messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetadataPOJO));

			// DB save calls
			smsService.saveSMS(sms);
			messengerContactService.saveOrUpdateMessengerContact(messengerContact);

			String encryptedLastMessage = messengerContact.getLastMessage();

			// update Contact Document
			ContactDocument.Builder contactBuilder = new ContactDocument.Builder(new ContactDocument());
			contactBuilder.addLastMessageSenderDetail(userDTO, lastMessageMetadataPOJO.getLastMessageType());
			ContactDocument contactDocument = contactBuilder.build();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			String sentAtTime = df.format(sentTime);
			contactDocument.setUpdatedAt(sentAtTime);
			contactDocument.setC_phone(customerDTO.getPhone());
			contactDocument.setLastMessageType(MessageDocument.CommunicationDirection.SEND.name());
			contactDocument.setL_msg(encryptedLastMessage); // It should be encrypted
			contactDocument.setLastMessageMetaData(lastMessageMetadataPOJO);
			contactDocument.setL_msg_on(sentAtTime);
			
			ESFindByFieldRequest<MessageDocument> esFindRequest = new ESFindByFieldRequest.Builder<MessageDocument>()
					.setIndex(Constants.Elastic.MESSAGE_INDEX)
					.setRoutingId(businessDTO.getAccountId())
					.setDocumentType(MessageDocument.class)
					.setShouldFields("_id", String.valueOf(sms.getSmsId()))
					.setShouldFields("mediaFiles.msg_id", String.valueOf(sms.getSmsId()))
					.setMinShouldMatch(1)
					.build();
			esFindRequest.isValidateRequest();
			List<MessageDocument> messages=elasticSearchExternalService.findDocumentByField(esFindRequest);
			if(CollectionUtils.isEmpty(messages) || messages.size()>1) {
				log.info("No message doc found in ES data store for _id:  ", sms.getSmsId());
				return null;
			}

			MessageDocument.SentThrough sentThrough = source.equals(1) ? MessageDocument.SentThrough.WEB
					: MessageDocument.SentThrough.MOBILE;
			// update Message Document
			MessageDocument messageDocument=messages.get(0);
			messageDocument.setCr_date(sentAtTime);
			messageDocument.setUpdatedAt(sentAtTime);
			messageDocument.setMsg_status("success"); // Assuming sms has successfully sent
			messageDocument.setType(MessageDocument.Type.UPDATE.getType());
			messageDocument.setSentThrough(sentThrough);
			messageDocument.setCreatedBy(new MessageDocument.UserDetail(userDTO.getId(), lastMessageUserName));
			messageDocument.setSource(sms.getSource());
			messageDocument.setIs_encrypt(sms.getEncrypted());
			messageDocument.setMsg_body(encryptedMessage);
			messageDocument.setFrom(sms.getFromNumber());
			messageDocument.setTo(sms.getToNumber());
			messageDocument.setU_id(userId);
			messageDocument.setU_name(lastMessageUserName);
			updateMessageDeliveryStatus(sms, messageDocument,"success");

			// ES save calls
			messengerContactService.updateContactDocumentOnES(contactDocument, String.valueOf(messengerContact.getId()),
					businessDTO.getRoutingId());
			messengerContactService.updateMessageDocument(messageDocument, sms.getSmsId().toString(),
					businessDTO.getRoutingId());

			SendMessageDTO sendMessageDTO = new SendMessageDTO();
			sendMessageDTO.setBusinessDTO(businessDTO);
			sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
			sendMessageDTO.setCustomerId(customerDTO.getId());
			sendMessageDTO.setMediaurl(sms.getMediaURL());
			sendMessageDTO.setBody(message);
			ConversationDTO conversationDTO = new ConversationDTO(sms);
			sendMessageDTO.setConversationDTO(conversationDTO);
			conversationDTO.setSentThrough(sentThrough);
			conversationDTO.setMessengerContactId(messengerContact.getId());
			sendMessageDTO.setConversationDTO(conversationDTO);
			sendMessageDTO.setUserDTO(userDTO);
			sendMessageService.pushSendRequestToKafkaSendSmsRetry(sendMessageDTO, MessengerEvent.SMS_SEND, userDTO, true);
			//fcmService.mirrorOnWeb(businessDTO.getRoutingId(), businessDTO.getBusinessId());
			FirebaseDto firebaseDto = new FirebaseDto();
			firebaseDto.setAccountId(businessDTO.getAccountId());
			firebaseDto.setBusinessId(businessDTO.getBusinessId());
			firebaseDto.setMcId(mcId);
			fcmService.mirrorOnWeb(firebaseDto);
			return new MessengerMessage.Message(userDTO, conversationDTO, "SMS_SEND",
					mediaFileOptional.isPresent() ? mediaFileOptional.get() : null, businessDTO.getTimeZoneId());

		}
		log.info("retrySmsSend: could not decrypt the message to be sent");
		return null;
	}
	private void updateMessageDeliveryStatus(Sms sms, MessageDocument message,String msg_status) {
		if (message.getM_id().equals(sms.getSmsId().toString())) {
			message.setMsg_status(msg_status);
		}
		updateAttachmentDeliveryStatus(message, msg_status, sms.getSmsId().toString());
	}
	private void updateAttachmentDeliveryStatus(MessageDocument message,String msg_status, String smsid) {
		List<MediaFile> mediaFiles=message.getMediaFiles();
		if (CollectionUtils.isNotEmpty(mediaFiles)) {
			List<MediaFile> updatedMediaFIles = mediaFiles.stream().map(media -> {
				if (StringUtils.isNotBlank(media.getMsg_id()) && smsid.equals(media.getMsg_id())) {
					media.setMsg_status(msg_status);
				}
				return media;
			}).collect(Collectors.toList());
			message.setMediaFiles(updatedMediaFIles);
		}
	}
	private String append10DlcBrandingToMessageBody(String messageBody,BusinessDTO businessDTO){
		log.info("Append Ten Dlc Branding for sms");
		TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(businessDTO.getEnterpriseNumber());
		if(Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()){
			String status = tenDlcStatusDTO.getStatus();
			TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
			if (Objects.nonNull(status) && (status.equals("not_started")||status.equals("in_progress")||status.equals("failed")||status.equals("not_required_demo_account"))
					&& Objects.nonNull(tollFreeInfo) && Objects.nonNull(tollFreeInfo.getBranding()) && tollFreeInfo.isAvailable()) {
				if (StringUtils.isBlank(messageBody)) {
					messageBody = messageBody.concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				} else if (!messageBody.contains(("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding()))){
					messageBody=messageBody.concat("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding());
				}
			}
		}
        return messageBody;
	}

}
