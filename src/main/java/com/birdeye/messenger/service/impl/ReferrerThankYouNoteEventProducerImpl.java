package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.dto.ThankYouNoteEvent;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.ReferrerThankYouNoteEventProducer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReferrerThankYouNoteEventProducerImpl implements ReferrerThankYouNoteEventProducer {

    private final KafkaService kafkaService;

    @Override
    public void produceEvent(ThankYouNoteEvent event) {
        kafkaService.publishToKafkaAsync(KafkaTopicEnum.REFERRER_THANK_YOU_NOTE_EVENT, null, event);
    }
}
