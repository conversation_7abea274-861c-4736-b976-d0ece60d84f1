package com.birdeye.messenger.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import jakarta.transaction.Transactional;

import com.birdeye.messenger.enums.Source;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.LiveChatSessionToken;
import com.birdeye.messenger.dao.repository.LiveChatSessionTokenRepository;
import com.birdeye.messenger.enums.ChatSessionStatus;
import com.birdeye.messenger.service.LiveChatSessionService;

/**
 * To create session for a Live-Chat
 * <AUTHOR>
 *
 */
@Service
public class LiveChatSessionServiceImpl implements LiveChatSessionService {

	@Autowired
	private LiveChatSessionTokenRepository sessionRepo;
	
	/**
	 * TODO : add validation for existing session (if any)
	 * return the existing session if active (don't create a new session)
	 */
	
	@Override
	@Transactional
	public LiveChatSessionToken getSession(Integer businessId, Integer customerId, Integer accountId, ChatSessionStatus chatSessionStatus, Integer mcid, Integer source,Boolean emailMandatory,Integer widgetId) {
		Optional<LiveChatSessionToken> existingSessionToken = getExistingSession(businessId, mcid);
		if (existingSessionToken.isPresent()) {
			// Update the existing session to active and update the updated date to the latest date for inactivity refresh
			// While returning a session via Init API, it should set it as per chatbot config
			LiveChatSessionToken liveChatSessionToken = existingSessionToken.get();
			liveChatSessionToken.setStatus(chatSessionStatus);
			liveChatSessionToken.setBusinessUserReplied(false);
			liveChatSessionToken.setUpdated(new Date());
			liveChatSessionToken.setEmailMandatory(emailMandatory);
			liveChatSessionToken.setChannel(Source.getValue(source).name());
			
			return sessionRepo.save(liveChatSessionToken);
		} else {
			// Create a new session for a new customer of a business
			String sessionToken = UUID.randomUUID().toString();
			LiveChatSessionToken liveChatSessionToken = new LiveChatSessionToken();
			liveChatSessionToken.setBusinessId(businessId);
			liveChatSessionToken.setCustomerId(customerId);
			liveChatSessionToken.setMcid(mcid);
			liveChatSessionToken.setSessionId(sessionToken);
			liveChatSessionToken.setStatus(chatSessionStatus);
			// Initialise the last session state same as current status while creating a new session
			liveChatSessionToken.setLastStatus(chatSessionStatus);
			liveChatSessionToken.setAccountId(accountId);
			liveChatSessionToken.setBusinessUserReplied(false);
			liveChatSessionToken.setChannel(Source.getValue(source).name());
			liveChatSessionToken.setEmailMandatory(emailMandatory);
			liveChatSessionToken.setWidgetId(widgetId);
			return sessionRepo.save(liveChatSessionToken);
		}
	}
	
	@Override
	public Optional<LiveChatSessionToken> getExistingSession(Integer businessId, Integer mcid){
		return sessionRepo.findSessionByMcidAndBusinessId(mcid, businessId);
	}

	@Override
	public Optional<LiveChatSessionToken> findActiveSessionBySessionId(String sessionId) {
		return sessionRepo.findActiveSessionBySessionId(sessionId);
	}
	
	/**
	 * returns true if there is an active session (status - MANUAL or AUTO)
	 * returns false otherwise
	 */
	@Override
	public boolean isLiveChatSessionActive(Integer businessId, Integer mcid) {
		Optional<LiveChatSessionToken> session = getExistingSession(businessId, mcid);
		if(session.isPresent() &&
				session.get().getStatus() != ChatSessionStatus.TERMINATED) {
			return true;
		}else {
			return false;
		}
	}

	@Override
	@Transactional
	public LiveChatSessionToken save(LiveChatSessionToken liveChatSessionToken) {
		return sessionRepo.save(liveChatSessionToken);
	}

	@Override
	public Optional<LiveChatSessionToken> findBySessionId(String sessionId) {
		return sessionRepo.findBySessionId(sessionId);
	}

	@Override
	@Transactional
	public List<LiveChatSessionToken> findStaleActiveSessions(Date updatedDateBefore) {
		return sessionRepo.findStaleActiveSessions(updatedDateBefore);
	}

	/**
	 * Update sessions as TERMINATED for given sessionIds.
	 */
	@Override
	@Transactional
	public void updateStatusForStaleSessions(List<String> sessionIds) {
		sessionRepo.updateSessionsStatusToTerminated(sessionIds);
	}

}
