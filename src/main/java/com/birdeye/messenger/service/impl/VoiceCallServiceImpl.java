package com.birdeye.messenger.service.impl;

import java.util.List;

import jakarta.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.VoiceCall;
import com.birdeye.messenger.dao.repository.VoiceCallRepository;
import com.birdeye.messenger.dto.BizAppVoiceCallRequest;
import com.birdeye.messenger.dto.VoiceCallDto;
import com.birdeye.messenger.service.VoiceCallService;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class VoiceCallServiceImpl implements VoiceCallService {

	@Autowired
	private VoiceCallRepository voiceCallRepository;

	public VoiceCallDto save(VoiceCallDto dto) {
		VoiceCall entity = DtoToEntityConverter.from(dto);
		encrypt(entity);
		voiceCallRepository.save(entity);
		dto.setId(entity.getId());
		dto.setEncrypted(entity.getEncrypted());
		return dto;
	}

  @Override
  @Transactional
  public void deleteVoiceCallsUsingCustomerId(Integer customerId){
      try {
          voiceCallRepository.deleteByCustomerId(customerId);
      } catch (Exception e) {
          log.error("error : {} occurred in deleteVoiceCallsUsingCustomerId", e.getMessage());
      }
  }

	@Override
	public Integer countVoiceCallForBusinessIdsAndCreateDate(BizAppVoiceCallRequest request) {
		return voiceCallRepository.countVoiceCallForBusinessIdsAndCreateDate(request.getBusinessIds(),request.getStartDate());
	}

	@Override
	public Integer countVoiceCallForBusinessIds(BizAppVoiceCallRequest request) {
		return voiceCallRepository.countVoiceCallForBusinessIds(request.getBusinessIds());
	}

	private void encrypt(VoiceCall call) {
		try {
			if (StringUtils.isNotEmpty(call.getTranscription())) {
				call.setTranscription(EncryptionUtil.encrypt(call.getTranscription(),
						StringUtils.join(call.getFromNumber(), call.getToNumber()),
						StringUtils.join(call.getToNumber(), call.getFromNumber())));
				call.setEncrypted(1);
			}
		} catch (Exception e) {
			log.error("Encryption failed for sms body: {} with business {}", call.getTranscription(), call.getBusinessId());
			call.setEncrypted(0);
			call.setTranscription(call.getTranscription());
		}
	}
	
	@Override
    public List<VoiceCall> findByCallSid(String callSid) {
		return voiceCallRepository.findByCallSid(callSid);
    }

	@Override
	public void updateVoicecallMessageBody(String messageBody, Integer messageId) {
		try {
			voiceCallRepository.updateVoicecallMessageBody(messageBody,messageId);
		} catch (Exception e) {
			log.error("error : {} occurred in updateVoicecallMessageBody", e.getMessage());
		}
	}
}
