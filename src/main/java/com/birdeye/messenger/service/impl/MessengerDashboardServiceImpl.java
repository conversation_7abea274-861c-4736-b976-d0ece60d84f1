package com.birdeye.messenger.service.impl;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import com.birdeye.messenger.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.DocWriteRequest.OpType;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.AppleMessageRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.AddContactMessengerMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ChatTransferAuth;
import com.birdeye.messenger.dto.ChatTransferMessage;
import com.birdeye.messenger.dto.ContactMergeStatus;
import com.birdeye.messenger.dto.ContactMsg;
import com.birdeye.messenger.dto.ConversationFilter;
import com.birdeye.messenger.dto.ConversationStatusRequest;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.LikeUnlikeEventSocial;
import com.birdeye.messenger.dto.MessageDeleteEventSocial;
import com.birdeye.messenger.dto.MessageDeleteRequest;
import com.birdeye.messenger.dto.MessageDeletedBy;
import com.birdeye.messenger.dto.MessageLikedBy;
import com.birdeye.messenger.dto.MessageMoveRequest;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerLocation;
import com.birdeye.messenger.dto.MessengerNotificationMessage;
import com.birdeye.messenger.dto.NotificationFilter;
import com.birdeye.messenger.dto.NotificationRequest;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UserLoginMessage;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MediaFile;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.instagram.LikeUnlikeRequest;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.dto.secure.messaging.SecureLinkGenerationRequest;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.dto.ConversationResponse;
import com.birdeye.messenger.external.dto.MessengerContactMessage;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.payment.PaymentCardReaderService;
import com.birdeye.messenger.service.payment.PaymentManualCardEntryService;
import com.birdeye.messenger.service.secure.messaging.SecureMessagingAuthService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.JwtUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;

import freemarker.template.TemplateException;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class MessengerDashboardServiceImpl implements MessengerDashboardService {

	private final RedisHandler redisHandler;

	private final MessengerContactService messengerContactService;

	private final CommunicationService communicationService;

	private final BusinessService businessService;
	
	private final CommonService commonService;

	private final ContactService contactService;

	private static ObjectMapper mapper = new ObjectMapper();

	private final MessengerEventHandlerService messengerEventHandlerService;

	private final FacebookEventService facebookEventService;

	private final MessageService messageService;

	private final ConversationActivityService conversationActivityService;
	private final FirebaseService firebaseService;
	private final NotificationService notificationService;
	private final CommunicationHelperService communicationHelperService;
	private final PaymentCardReaderService paymentCardReaderService;
	private final PaymentManualCardEntryService paymentManualCardEntryService;

	private final ElasticSearchExternalService elasticSearchService;

	private final SecureMessagingAuthService secureMessagingAuthService;

	private final SecureMessageSendEventHandler secureMessageSendEventHandler;

	private final ConversationService conversationService;

	private final KafkaService kafkaService;
	private final SocialService socialService;
	private final RedisLockService redisLockService;
	private final InstagramMessageService instagramMessageService;
	private final FacebookMessageService facebookMessageService;
	private final GoogleMessageService googleMessageService;
	private final AppleMessageRepository appleMessageRepository;
	private final SmsService smsService;
	private final EmailService emailService;
	private final FirebaseService fcmService;
	private final TwitterMessageService twitterMessageService;
	private final UserService userService;
	private final VoiceCallService voiceCallService;
	private final WhatsappMessageService whatsappMessageService;
	

	static {
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}

	/**
	 * Polling alternative to event subscription. It checks for
	 *
	 * 1. New messages. 2. Conversation tagging. 3. FB integration status change
	 *
	 * TODO: we should just poll redis for status changes.
	 *
	 */
	@Override
	public boolean doRefresh(String accountId, MessengerGlobalFilter notificationfilter) {
		boolean doRefresh = false;

		// First time load from UI.
		if (notificationfilter.getLastMsgTime() == null) {
			return true;
		}

		// Last event time from cache.
		long msngrTriggerTime = redisHandler.getLastEventFromCache(accountId);
		if (msngrTriggerTime == 0) {
			log.debug("Last message Event not found in cache for accountId {} ", accountId);
		}
		// Change in last event time will trigger refresh.
		if (notificationfilter.getLastMsgTime() != null) {
			if (msngrTriggerTime > notificationfilter.getLastMsgTime()) {
				return true;
			}
		}

		// TODO: To be enabled once logs are available from prod and we have
		// need
		// Messengerfilter filter = new Messengerfilter();
		// if (CollectionUtils.isEmpty(notificationfilter.getBusinessIds()))
		// filter.setBusinessIds(null);
		// else
		// filter.setBusinessIds(notificationfilter.getBusinessIds());
		// if (notificationfilter.getLastMsgTime() != null) {
		// Date lastMsgOn = new Date(notificationfilter.getLastMsgTime());
		// DateFormat df = new SimpleDateFormat(LAST_MSG_FORMAT);
		// filter.setLastMsgTime(df.format(lastMsgOn));
		// log.debug("Date filter : {}", lastMsgOn);
		// }
		//
		// List<?> conversations = new ArrayList<>(); // integrate with
		// getConversations --
		// if (CollectionUtils.isNotEmpty(conversations)) {
		// doRefresh = true;
		// }

		/*
		 * From dashboard, selectedBusinessId won't be available, it will only be passed
		 * from messenger tab.
		 */
		if (notificationfilter.getSelectedBusinessId() != null
				&& StringUtils.isNotBlank(notificationfilter.getFbIntegrationStatus())) {
			String fbIntegrationStatusInDB = facebookEventService.getFacebookIntegrationStatus(
					notificationfilter.getSelectedBusinessId(),
					notificationfilter.getMessengerEnabled());
			doRefresh = doRefresh
					|| !fbIntegrationStatusInDB.equalsIgnoreCase(notificationfilter.getFbIntegrationStatus());
		}
		return doRefresh;
	}

	@Override
	public MessengerMessage getMessages(Integer accountId, Integer conversationId, MessengerFilter messengerFilter) {
		// Correct page is required for messages.
		Integer startIndex = messengerFilter.getStartIndex() == null ? 0 : messengerFilter.getStartIndex();
		if (messengerFilter.getCount() == null) {
			messengerFilter.setCount(25);
		}
		messengerFilter.setAccountId(accountId);
		messengerFilter.setConversationId(conversationId);

		// 1. Find messenger Contact
		messengerFilter.setStartIndex(0);
		List<ContactDocument> docs = messengerContactService.getContactFromES(messengerFilter);
		if (!docs.isEmpty()) {
			ContactDocument doc = docs.get(0);
			// 2. Find messages for contact.
			messengerFilter.setStartIndex(startIndex);
			messengerFilter.setStartDate(messengerFilter.getLastMsgTime());

			List<MessageDocument> messsages = messengerContactService.getMessagesFromES(messengerFilter);
			String timezone = "PST";
			// Find timezone of business

			MessengerMessage messengerMessage = new MessengerMessage(messsages, doc, timezone);

			// handle user unreachable for FB via social call.
			// lastFacebookPageId - UI, currentFacebookPageId - Social
			// String lastFacebookPageId="";
			// String currentFacebookPageId="";
			// List<BusinessFacebookPage> facebookPages =
			// em.createNamedQuery("BusinessFacebookPage.findByBusinessId",
			// BusinessFacebookPage.class)
			// .setParameter("businessId", this.businessId).getResultList();
			// if(CollectionUtils.isNotEmpty(facebookPages)){
			// currentFacebookPageId=facebookPages.get(0).getFacebookPageId();
			// }

			// if (CollectionUtils.isNotEmpty(filter.getBusinessIds())) {
			// messengerMessage
			// .setFbIntegrationStatus(getFacebookIntegrationStatus(filter.getBusinessIds().get(0)));
			// messengerMessage.setIsUserUnreachable(getUserReachableStatus(customerId,
			// BusinessControllerUtil.getBusiness(em,
			// filter.getBusinessIds().get(0), true)));
			// }

			// if (doc.getB_id() != null) {
			// messengerMessage.setLastReceivedMsgSource(
			// getLastMessageForContact(customerId,
			// businessController.findById(contact.getB_id()), true)
			// .getSource());
			// } // Use only ONE

			// // in notification we set startDate as lastmessage sent so have
			// // to find facebook status by making es call
			// messengerMessage
			// .setIsLastFacebookMessage(getLastFacebookMessageStatus(customerId,
			// enterpriseId, timezone));

			return messengerMessage;
		}
		/*
		 * else { getMessagesFromDB(customerId, filter, businessId, size); // remove
		 * this DB fallback as we reading from ES only }
		 */
		return new MessengerMessage();

	}

	@Override
	public Object getConversations(MessengerFilter filterMessage) {
		if (filterMessage.getOperation() != null && filterMessage.getOperation() == 1) {
			// get unreadBizCount, count, fbIntegrationStatus
			Map<String, Object> response = new HashMap<>();
			List<Integer> tags = new ArrayList<>();
			tags.add(MessageTag.UNREAD.getCode());
			response.put("unreadBizCount",
					conversationCount(tags, filterMessage.getBusinessIds(), filterMessage.getEnterpriseId()));
			if (filterMessage.getTag() == null) {
				tags.add(MessageTag.INBOX.getCode());
				tags.add(MessageTag.CAMPAIGN.getCode());
			} else {
				final Integer tag = filterMessage.getTag();
				tags = new ArrayList<Integer>() {
					{
						add(tag);
					}
				};
			}
			response.put("count",
					conversationCount(tags, filterMessage.getBusinessIds(), filterMessage.getEnterpriseId()));
			response.put("fbIntegrationStatus",
					facebookEventService.getFacebookIntegrationStatus(filterMessage.getBusinessIds().get(0),
							filterMessage.getMessengerEnabled()));
			return response;
		} else {
			if (StringUtils.isEmpty(filterMessage.getSearchTerm())) {
				// getConversations
				return getConversationsFromES(filterMessage);
			} else {
				return getSearchedDocuments(filterMessage);
			}
		}
	}

	private List<MessengerContactMessage> getConversationsFromES(MessengerFilter filter) {
		// Prepare token data for ES
		Map<String, Object> convFilter = new HashMap<>();

		if (filter.getCount() == null)
			filter.setCount(25);
		if (filter.getStartIndex() == null)
			filter.setStartIndex(0);

		convFilter.put("from", String.valueOf(filter.getStartIndex()));
		convFilter.put("size", String.valueOf(filter.getCount()));

		if (filter.getTag() != null) {
			if (MessengerTagEnum.ALL.getId() == filter.getTag().intValue()) {
				convFilter.put("tags", "1,2"); // UNREAD and CONVERSATION
			} else {
				convFilter.put("tags", filter.getTag().toString());
			}
		} else {
			convFilter.put("tags", "1,2");
		}

		convFilter.put("businessIds", filter.getBusinessIds().toString());
		if (StringUtils.isNotBlank(filter.getLastMsgTime())) {
			convFilter.put("startDate", filter.getLastMsgTime());
		}
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("convFilter", convFilter);

		List<ContactDocument> contactDocuments = getContactDocumentsFromElastic(filter, dataModel,
				Constants.Elastic.GET_CONVERSATIONS);

		List<MessengerContactMessage> contacts = new ArrayList<>();
		int i = 0;
		int index = -1;
		Integer total = contactDocuments.size();
		for (ContactDocument contactDocument : contactDocuments) {
			MessengerContact con = messengerContactService.findById(contactDocument.getM_c_id());
			String facebookId = getFacebookId(con);
			String googleConvId = getGoogleConversationId(con);
			String instagramConvId = getInstagramConversationId(con);
			String appleConvId = getAppleConversationId(con);
			String twitterConvId = getTwitterConversationId(con);
			String whatsappConvId = getWhatsappConversationId(con);
			log.debug("getConversationsFromES: contact : {} and id {}", con, contactDocument.getM_c_id());
			MessengerContactMessage conversation = new MessengerContactMessage(contactDocument, 1, facebookId,
					ConversationView.MESSAGE, false, false, googleConvId, instagramConvId, appleConvId, twitterConvId, whatsappConvId);
			// post process to support livechat,chatbot,notes in old app
			postProcessForOldApp(conversation, contactDocument);
			contacts.add(conversation);

			if (filter.getMoveConversationToTop() != null
					&& conversation.getCustomer().getId().equals(filter.getMoveConversationToTop())) {
				index = i;
			}
			i++;
		}

		Integer campaignSIndex = filter.getStartIndex() - total;
		if ((contacts.size() < filter.getCount() || campaignSIndex > 0) && filter.getTag() == null) {
			convFilter.put("tags", "5");
			convFilter.put("from", campaignSIndex > 0 ? campaignSIndex : 0);
			convFilter.put("size", filter.getCount() - contacts.size());

			dataModel.put("convFilter", convFilter);

			List<ContactDocument> contactDocumentsCampaign = getContactDocumentsFromElastic(filter, dataModel,
					Constants.Elastic.GET_CONVERSATIONS);
			for (ContactDocument contactDocument : contactDocumentsCampaign) {
				MessengerContact con = messengerContactService.findById(contactDocument.getM_c_id());
				String facebookId = getFacebookId(con);
				String googleConvId = getGoogleConversationId(con);
				String instagramConvId = getInstagramConversationId(con);
				String appleConvId = getAppleConversationId(con);
				String twitterConvId = getTwitterConversationId(con);
				String whatsappConvId = getWhatsappConversationId(con);
				MessengerContactMessage conversation = new MessengerContactMessage(contactDocument, 1, facebookId,
						ConversationView.MESSAGE, false, false, googleConvId, instagramConvId, appleConvId,twitterConvId,whatsappConvId);
				// post process to support livechat,chatbot,notes in old app
				postProcessForOldApp(conversation, contactDocument);
				contacts.add(conversation);

				if (filter.getMoveConversationToTop() != null
						&& conversation.getCustomer().getId().equals(filter.getMoveConversationToTop())) {
					index = i;
				}
				i++;
			}
		}

		if (filter.getMoveConversationToTop() != null) {
			// handling case when conversation is refreshed on same page
			if (index >= 0) {
				MessengerContactMessage contactMsg = contacts.get(index);
				contacts.remove(index);
				contacts.add(0, contactMsg);
			} else {
				convFilter = new HashMap<>();
				convFilter.put("size", 1);
				convFilter.put("from", 0);
				convFilter.put("tags", null);
				convFilter.put("c_id", filter.getMoveConversationToTop().toString());

				dataModel.put("convFilter", convFilter);
				// Call ES to get ContactDocument for Move Conversation to Top
				ESRequest esRequestTop = new ESRequest.Builder(new ESRequest())
						.addIndex(Constants.Elastic.CONTACT_INDEX)
						.addRoutingId(filter.getEnterpriseId())
						.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION, dataModel).build();
				List<ContactDocument> contactDocumentsList = elasticSearchService.searchByQuery(esRequestTop,
						ContactDocument.class);
				if (CollectionUtils.isNotEmpty(contactDocumentsList)) {
					ContactDocument contactDocumentTop = contactDocumentsList.get(0);
					MessengerContact con = messengerContactService.findById(contactDocumentTop.getM_c_id());
					String facebookId = getFacebookId(con);
					String googleConvId = getGoogleConversationId(con);
					String instagramConvId = getInstagramConversationId(con);
					String appleConvId = getAppleConversationId(con);
					String twitterConvId = getTwitterConversationId(con);
					String whatsappConvId = getWhatsappConversationId(con);
					MessengerContactMessage conversation = new MessengerContactMessage(contactDocumentTop, 1,
							facebookId, ConversationView.MESSAGE, false, false, googleConvId, instagramConvId,
							appleConvId,twitterConvId,whatsappConvId);
					// post process to support livechat,chatbot,notes in old app
					postProcessForOldApp(conversation, contactDocumentTop);
					contacts.add(0, conversation);
				} else {
					log.error("getConversationsFromES: contact document not found for conversationId {}",
							filter.getMoveConversationToTop().toString());
				}
			}
		}

		return contacts;
	}

	/**
	 * post processing to handle old-app support
	 * prefix "You :" to outgoing messages
	 * username + "added an internal note:" + content
	 * chatbot last message - convert to plain-text version
	 */
	private void postProcessForOldApp(MessengerContactMessage conv, ContactDocument contactDoc) {
		if (conv != null && contactDoc != null) {

			String lastMsg = conv.getRecentMessage();
			if (MessageType.INTERNAL_NOTES.name().equalsIgnoreCase(contactDoc.getLastMessageType()) ||
					"INTERNAL_NOTE".equalsIgnoreCase(contactDoc.getLastMessageType())) {
				// handle notes
				lastMsg = contactDoc.getLastMessageUserName() + " added an internal note: " + lastMsg;
			} else if (CommunicationDirection.SEND.name().equalsIgnoreCase(contactDoc.getLastMessageType())) {
				if (contactDoc.getLastMessageUserId() != null && contactDoc.getLastMessageUserId() == -10) {
					// handle chatbot responses
					lastMsg = MessengerUtil.transformChatbotReplyToPlainText(lastMsg, true);
					lastMsg = contactDoc.getLastMessageUserName() + ": " + lastMsg;
				} else if (contactDoc.getLastMessageUserId() != null) {
					// outgoing message - add prefix
					lastMsg = "You: " + lastMsg;
				}
			}
			conv.setRecentMessage(lastMsg);
		}
	}

	private String getFacebookId(MessengerContact con) {
		String facebookId = null;
		if (con != null) {
			facebookId = con.getFacebookId();
		}
		return facebookId;
	}

	private Long conversationCount(List<Integer> tags, List<Integer> businessIds, final Integer enterpriseId) {
		Map<String, String> messageFilter = new HashMap<>();
		messageFilter.put("tags", ControllerUtil.toCommaSeparatedString(tags));
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));

		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.GET_CONVERSATION_COUNT, dataModel).build();
		return elasticSearchService.getDocumentCount(esRequest);
	}

	private List<ContactDocument> getContactDocumentsFromElastic(MessengerFilter filter, Map<String, Object> dataModel,
																 String queryFileName) {
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(filter.getEnterpriseId())
				.addTemplateAndDataModel(queryFileName, dataModel).build();
		return elasticSearchService.searchByQuery(esRequest, ContactDocument.class);
	}

	private List<MessengerContactMessage> getSearchedDocuments(MessengerFilter filterMessage) {
		if (Objects.isNull(filterMessage.getStartIndex()))
			filterMessage.setStartIndex(0);
		if (Objects.isNull(filterMessage.getCount()))
			filterMessage.setCount(25);
		alterSearchTerm(filterMessage);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", filterMessage);
		dataModel.put("businessIds", ControllerUtil.toCommaSeparatedString(filterMessage.getBusinessIds()));
		List<ContactDocument> contactDocuments = getContactDocumentsFromElastic(filterMessage, dataModel,
				Constants.Elastic.SEARCH_CONVERSATIONS_V2);
		log.debug("[getSearchedDocuments] count {}", contactDocuments);
		return contactDocuments.stream().map(contactDocument -> {
			MessengerContact messengerContact = messengerContactService.findById(contactDocument.getM_c_id());
			log.info("[getSearchedDocuments] messengerContact {} ", messengerContact);
			if (Objects.nonNull(messengerContact)) {
				MessengerContactMessage conversation = new MessengerContactMessage(contactDocument, 1,
						messengerContact.getFacebookId(), ConversationView.MESSAGE, false, false,
						messengerContact.getGoogleConversationId(), messengerContact.getInstagramConversationId(),
						messengerContact.getAppleConversationId(),messengerContact.getTwitterConversationId(),messengerContact.getWhatsappConversationId());
				// post process to support livechat,chatbot,notes in old app
				postProcessForOldApp(conversation, contactDocument);
				return conversation;
			} else {
				if (Objects.nonNull(contactDocument)) {
					log.info("[getSearchedDocuments] messengerContact found NULLA for contactDocument Id {} ",
							contactDocument.getM_c_id());
				}
			}
			return null;
		}).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
	}

	private static void alterSearchTerm(MessengerFilter filterMessage) {
		String searchTerm = filterMessage.getSearchTerm();
		if (Objects.nonNull(searchTerm)) {
			filterMessage.setSearchTerm(MessengerUtil.alterSearchTerm(searchTerm));
		}
	}

	public static void main(String[] args) {
		MessengerFilter filter = new MessengerFilter();
		filter.setSearchTerm("(304) 243-9451");
		alterSearchTerm(filter);
		filter.setSearchTerm("rupali.srivastava+11998@bi");
		alterSearchTerm(filter);
	}

	@SuppressWarnings("unchecked")
	@Override
	public MessengerNotificationMessage getNotification(NotificationFilter notificationFilter,String requestSource) throws Exception {
		MessengerGlobalFilter messengerGlobalFilter = notificationFilter.getMessengerGlobalFilter();
		UserLoginMessage userMessage = notificationFilter.getUserLoginMessage();
		MessengerNotificationMessage notification = new MessengerNotificationMessage();

		if (CollectionUtils.isEmpty(messengerGlobalFilter.getBusinessIds())
				&& CollectionUtils.isEmpty(userMessage.getBusinessIds())) {
			log.info(
					"getNotification : no businessIds found (may be no location present for enterprise), request body {}, userMessage {}",
					notificationFilter, userMessage);
			return notification;
		}
		if (!userMessage.isSmb() && CollectionUtils.isNotEmpty(userMessage.getBusinessIds())) {
			userMessage.getBusinessIds().remove(userMessage.getRoutingIdentifier());
			if (CollectionUtils.isEmpty(userMessage.getBusinessIds())) {
				log.info(
						"getNotification: all the locations that user has access to do not have Twilio business_SMS number, account {} userId {}",
						userMessage.getBusinessId(), userMessage.getUserId());
				return notification;
			}
		}
		try {
			notification.setUnreadCount(conversationCount(Collections.singletonList(2), userMessage.getBusinessIds(),
					userMessage.getRoutingIdentifier()));
		} catch (Exception e) {
			log.error("getNotification: payload : notificationFilter {}  &&  userMessage {}", notificationFilter,
					userMessage); // we need to log notificationFilter when conversation count fails
			throw e;
		}
		if (Objects.nonNull(notificationFilter.getMode()) && notificationFilter.getMode().equals(1)) {
			log.debug("getNotification: liteMode called for account {} and user {}", userMessage.getRoutingIdentifier(),
					userMessage.getUserId());
			return notification;
		}
		Map<Integer, BusinessDTO> businessDataMap = businessService.getBusinessDataForMessenger(userMessage);
		if (!userMessage.isSmb()) {
			// get for all the locations that loggedIn has access to
			notification.setLocations(getEnterpriseLocations(userMessage, businessDataMap));
		}
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		MessengerFilter filter = new MessengerFilter();
		filter.setStartIndex(0);
		filter.setCount(messengerGlobalFilter.getCount() == null ? 20 : messengerGlobalFilter.getCount());
		filter.setMarkRead(false);
		filter.setTag(MessengerTagEnum.UNREAD.getId());
		filter.setEnterpriseId(userMessage.getRoutingIdentifier());
		// get for all the locations that user has access to
		filter.setBusinessIds(new ArrayList(userMessage.getBusinessIds()));
		try {
			notification.setUnreadConversations((List<MessengerContactMessage>) getConversations(filter));
		} catch (Exception e) {
			log.error("Unread message parse Exception {}", e);
		}
		filter.setTag(MessengerTagEnum.ALL.getId());
		log.debug("All conversations  fetch in progress");
		notification.setAllConversations((List<MessengerContactMessage>) getConversations(filter));
		log.debug("All conversations  fetch is completed");
		notification.setLastMsgTime(new Date().getTime());
		filter.setCount(20);
		// if UI sends businessIds then set it to that value else set to the loggedIn
		// user accessible location Ids
		if (CollectionUtils.isNotEmpty(messengerGlobalFilter.getBusinessIds())) {
			filter.setBusinessIds(new ArrayList(messengerGlobalFilter.getBusinessIds()));
		} else {
			filter.setBusinessIds(new ArrayList(userMessage.getBusinessIds()));
		}
		Date lastMsgOn = null;
		if (messengerGlobalFilter.getLastMsgTime() != null) {
			lastMsgOn = new Date(messengerGlobalFilter.getLastMsgTime());
		}
		MessengerMessage message = null;
		MessageResponse messageResponse = null;
		if (messengerGlobalFilter.getConversationId() != null) {
			if (lastMsgOn != null) {
				Calendar cal = Calendar.getInstance();
				cal.setTime(lastMsgOn);
				cal.set(Calendar.SECOND, 0);
				filter.setLastMsgTime(df.format(cal.getTime()));
				log.info("Date filter  : {0}", lastMsgOn);
			}
			messageResponse = getMessages(businessDataMap, userMessage.getBusinessId(),
					messengerGlobalFilter.getConversationId(), filter, requestSource, notificationFilter.getIsAppCompatible());
			// notification.setMessages(message.getMessages());
			notification.setMessages(messageResponse.getMessages());
			notification.setLastReceivedMsgSource(messageResponse.getLastReceivedMsgSource());
			notification.setIsLastFacebookMessage(communicationService.isFBSendAvailable(
					messengerGlobalFilter.getConversationId(), userMessage.getRoutingIdentifier(), null));
			if (CollectionUtils.isNotEmpty(messengerGlobalFilter.getBusinessIds())) {
				BusinessDTO businessDTO = businessDataMap.get(messengerGlobalFilter.getBusinessIds().get(0));
				notification.setIsUserUnreachable(communicationService
						.getUserReachableStatus(messengerGlobalFilter.getConversationId(), businessDTO));
			}
		}
		if (message != null || CollectionUtils.isNotEmpty(filter.getBusinessIds())) {
			List<Integer> businessIdsForQuery = new ArrayList<>();
			if (message == null || message.getBusinessId() == null) {
				businessIdsForQuery.addAll(filter.getBusinessIds());
			} else {
				businessIdsForQuery.add(message.getBusinessId());
			}
			notification.setUnreadBizCount(conversationCount(Collections.singletonList(2), businessIdsForQuery,
					userMessage.getRoutingIdentifier()));
			log.info("getNotification: unreadBizCount set to {} for businessIds {} routing Identifier",
					businessIdsForQuery, userMessage.getRoutingIdentifier(), notification.getUnreadCount());
			filter.setBusinessIds(businessIdsForQuery);
			filter.setTag(messengerGlobalFilter.getListTag());
			if (messengerGlobalFilter.getLastMsgTime() != null) {
				try {
					List<MessengerContactMessage> contacts;
					Calendar cal = Calendar.getInstance();
					cal.setTime(lastMsgOn);
					cal.add(Calendar.SECOND, -5);
					filter.setLastMsgTime(df.format(lastMsgOn));
					contacts = (List<MessengerContactMessage>) getConversations(filter);
					notification.setBusinessConversationList(contacts);
				} catch (Exception e) {
					log.info("Exception parsing date {}", e.getMessage());
				}
			} else {
				filter.setMoveConversationToTop(messengerGlobalFilter.getConversationId());
				List<MessengerContactMessage> contacts = (List<MessengerContactMessage>) getConversations(filter);
				notification.setBusinessConversationList(contacts);
			}
			if (CollectionUtils.isNotEmpty(messengerGlobalFilter.getBusinessIds())) {
				// we need to set messenger enabled flag in notificationFilter
				notification.setFbIntegrationStatus(
						facebookEventService.getFacebookIntegrationStatus(messengerGlobalFilter.getBusinessIds().get(0),
								messengerGlobalFilter.getMessengerEnabled()));
			}
		}
		log.info("Returning response for get notifications,total unread message count: {}",
				notification.getUnreadCount());

		return notification;
	}

	private MessageResponse getMessages(Map<Integer, BusinessDTO> businessDataMap, Integer accountId,
										Integer conversationId, MessengerFilter messengerFilter, String requestSource, Boolean isAppCompatible) throws Exception {

		// Correct page is required for messages.
		Integer startIndex = messengerFilter.getStartIndex() == null ? 0 : messengerFilter.getStartIndex();
		if (messengerFilter.getCount() == null) {
			messengerFilter.setCount(25);
		}
		messengerFilter.setAccountId(accountId);
		messengerFilter.setConversationId(conversationId);

		// 1. Find messenger Contact
		messengerFilter.setStartIndex(0);
		List<ContactDocument> docs = messengerContactService.getContactFromES(messengerFilter);
		// getConversation(businessDataMap,conversationId, messengerFilter);
		if (!docs.isEmpty()) {
			ContactDocument doc = docs.get(0);
			// 2. Find messages for contact.
			BusinessDTO businessDTO = businessDataMap.get(doc.getB_id());
			messengerFilter.setStartIndex(startIndex);
			if (messengerFilter.getLastMsgTime() != null) {
				messengerFilter.setStartDate(messengerFilter.getLastMsgTime());
			}
			messengerFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
			List<MessageDocument> messsages = messengerContactService.getMessagesFromES(messengerFilter);
			String timezone = "PST";
			// Find timezone of business
			if (doc.getB_id() != null && businessDTO != null) {
				timezone = businessDTO.getTimeZoneId();
			}
			// MessengerMessage messengerMessage = new MessengerMessage(messsages, doc,
			// timezone);
			messsages.parallelStream()
					.forEach(document -> document.setIsAppCompatible(isAppCompatible));
			MessageResponse messageResponse = new MessageResponse(messsages, timezone, conversationId, false,null, requestSource);

			// handle user unreachable for FB via social call.
			// lastFacebookPageId - UI, currentFacebookPageId - Social
			if (CollectionUtils.isNotEmpty(messengerFilter.getBusinessIds())) {
				messageResponse.setFbIntegrationStatus(facebookEventService.getFacebookIntegrationStatus(
						messengerFilter.getBusinessIds().get(0), messengerFilter.getMessengerEnabled()));
				businessDTO = businessDataMap.get(messengerFilter.getBusinessIds().get(0));
				messageResponse
						.setFBUserReachable(communicationService.getUserReachableStatus(conversationId, businessDTO));
			}

			if (doc.getB_id() != null) {
				businessDTO = businessDataMap.get(doc.getB_id());
				messageResponse.setLastReceivedMsgSource(
						getLastMessageForContact(conversationId, businessDTO, true).getSource());
			}

			messageResponse.setLastMsgSource(
					messengerContactService.getLastMessageSource(conversationId, businessDTO.getRoutingId()));
			// // in notification we set startDate as lastmessage sent so have
			// // to find facebook status by making es call
			messageResponse.setRestrictFBReply(
					getLastFacebookMessageStatus(conversationId, accountId, businessDTO.getTimeZoneId()));
			// return messengerMessage;
			return messageResponse;
		}
		/*
		 * else { getMessagesFromDB(customerId, filter, businessId, size); // remove
		 * this DB fallback as we reading from ES only }
		 */
		return new MessageResponse();

	}

	public List<MessengerLocation> getEnterpriseLocations(UserLoginMessage userMessage,
														  Map<Integer, BusinessDTO> businessDataMap) throws Exception {
		log.info("getEnterpriseLocations : business type : {}, type title : {}", userMessage.getBusinessType(),
				userMessage.getBusinessTypeTitle());
		if (CollectionUtils.isEmpty(userMessage.getBusinessIds())) {
			return new ArrayList<>();
		}
		List<MessengerLocation> locations = new ArrayList<>();
		Map<Integer, Integer> businessUnreadCountMap = new HashMap<>();
		try {
			businessUnreadCountMap = getBusinessUnreadCountMap(userMessage.getBusinessIds(),
					userMessage.getRoutingIdentifier(),
					MessengerUtil.fetchMessengerIndexSearchUrl("doc", Constants.Elastic.CONTACT_INDEX));
		} catch (IOException | TemplateException e) {
			log.error("Unable to fetch biz unreadCount from ES {}", e.getMessage());
		}
		List<Integer> bizIdsWithoutSmsNumber = new ArrayList<>();
		for (Integer bizId : userMessage.getBusinessIds()) {
			BusinessDTO businessDTO = businessDataMap.get(bizId);
			if (businessDTO.getSmsPhoneNumber() != null) {
				MessengerLocation location;
				if (businessDTO != null) {
					location = new MessengerLocation(businessDTO,
							businessUnreadCountMap.get(bizId) == null ? 0 : businessUnreadCountMap.get(bizId));
					// location.setFbIntegrationStatus(getFacebookIntegrationStatus(location.getId()));
					location.setCountryCode(businessDTO.getCountryCodes().toArray(new String[10])[0]);
					locations.add(location);
				}
			} else {
				bizIdsWithoutSmsNumber.add(bizId);
			}
		}

		if (CollectionUtils.isNotEmpty(bizIdsWithoutSmsNumber)) {
			log.info("{} business ids without sms number", bizIdsWithoutSmsNumber);
			for (Integer bizId : bizIdsWithoutSmsNumber) {
				BusinessDTO businessDTO = businessDataMap.get(bizId);
				MessengerLocation location;
				if (businessDTO != null && ("demo".equalsIgnoreCase(businessDTO.getActivationStatus())
						&& ("Cobranded".equalsIgnoreCase(businessDTO.getAccountType())
						|| "Whitelabel".equalsIgnoreCase(businessDTO.getAccountType())))) {
					location = new MessengerLocation(businessDTO,
							businessUnreadCountMap.get(bizId) == null ? 0 : businessUnreadCountMap.get(bizId));
					location.setCountryCode(businessDTO.getCountryCodes().toArray(new String[10])[0]);
					// location.setFbIntegrationStatus(getFacebookIntegrationStatus(location.getId()));
					locations.add(location);
				}

			}

		}

		Collections.sort(locations);
		return locations;
	}

	@SuppressWarnings("unchecked")
	public Map<Integer, Integer> getBusinessUnreadCountMap(List<Integer> businessIds, Integer enterpriseId,
														   String aggUrl) throws IOException, TemplateException {

		Map<Integer, Integer> countByBiz = new HashMap<>();
		Map<String, String> messageFilter = new HashMap<>();
		messageFilter.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));

		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSENGER_NOTIFICATION, dataModel).build();
		SearchResponse searchResponse = elasticSearchService.getSearchResult(esRequest);
		Terms teamsConvAggregations = searchResponse.getAggregations().get("group_by_unread");
		List<? extends Bucket> buckets = teamsConvAggregations.getBuckets();
		buckets.forEach(bucket -> {
			countByBiz.put(Integer.parseInt(bucket.getKeyAsString()), (int) bucket.getDocCount());
		});

		return countByBiz;
	}

	private Boolean getLastFacebookMessageStatus(Integer conversationId, Integer enterpriseId, String timeZoneId)
			throws Exception {
		Boolean isLastFBMessage = false;
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("size", 0);
		messageFilter.put("from", 0);
		messageFilter.put("c_id", conversationId.toString());
		messageFilter.put("source", 110);
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_2, dataModel).build();

		ElasticData messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

		if (messagesData != null) {
			Integer total = messagesData.getTotal().intValue();
			if (total > 0) {
				messageFilter.put("size", total.toString());
				dataModel.put("messageFilter", messageFilter);
				esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
						.addRoutingId(enterpriseId)
						.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_2, dataModel).build();

				messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
				if (messagesData != null) {
					List<MessageDocument> messages = messagesData.getResults();
					// timezone = StringUtils.isNotEmpty(timezone) ?
					// TimeZoneUtil.getTimeZoneID(timezone) : "PST";
					TimeZone tz = TimeZone.getTimeZone(timeZoneId);
					Date lastMessageSent = null;
					DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					for (MessageDocument message : messages) {
						if (StringUtils.isNotBlank(message.getCr_date())) {
							continue;
						}

						Date sentOnUTC = df.parse(message.getCr_date());
						Date sentOn = ControllerUtil.convertToBusinessTimeZone(sentOnUTC, tz).getTime();
						if (message.getSource() != null && message.getSource() == 110) {
							if (message.getMsg_type() != null && (message.getMsg_type().equalsIgnoreCase("SMS_RECEIVE")
									|| message.getMsg_type().equalsIgnoreCase("MMS_RECEIVE"))) {
								if (lastMessageSent == null || lastMessageSent.before(sentOnUTC)) {
									lastMessageSent = sentOnUTC;
								}
							}
						}
					}

					if (lastMessageSent != null) {
						Calendar cal = Calendar.getInstance();
						cal.setTime(lastMessageSent);
						cal.add(Calendar.HOUR, 24);
						if (cal.before(Calendar.getInstance())) {
							for (int i = 0; i < messages.size(); i++) {
								MessageDocument message = messages.get(i);
								if (message.getCr_date() == null) {
									continue;
								}
								Date sentOnUTC = df.parse(message.getCr_date());
								if (message.getMsg_type() != null
										&& (message.getMsg_type().equalsIgnoreCase("SMS_SEND")
										|| message.getMsg_type().equalsIgnoreCase("MMS_SEND"))
										&& message.getSource() != null && message.getSource() == 110
										&& sentOnUTC.after(cal.getTime())) {
									isLastFBMessage = true;
									break;
								}
							}
						}
					}
				}
			}
		}
		log.info("getLastFacebookMessageStatus :: routeId {} :: {}", enterpriseId, isLastFBMessage);
		return isLastFBMessage;
	}

	private ContactMsg getLastMessageForContact(Integer conversationId, BusinessDTO business, Boolean setMessageType)
			throws Exception {
		Map<String, Object> dataModel = new HashMap<>();
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("size", 1);
		messageFilter.put("from", 0);
		messageFilter.put("c_id", conversationId.toString());
		Integer enterpriseId = business.getEnterpriseId() == null ? business.getBusinessId()
				: business.getEnterpriseId();
		List<String> typeList = new ArrayList<>();
		typeList.add("SMS_RECEIVE");
		typeList.add("MMS_RECEIVE");
		if (setMessageType) {
			messageFilter.put("msg_type", ControllerUtil.getJsonTextFromObject(typeList));
		}
		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_2, dataModel).build();
		dataModel.put("messageFilter", messageFilter);
		ElasticData messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
		Integer source = 1;
		Boolean isLastMessage = false;
		if (messagesData != null) {
			List<MessageDocument> messages = messagesData.getResults();
			for (MessageDocument msgObj : messages) {
				source = msgObj.getSource();
				if (msgObj.getCr_date() != null) {
					Calendar cal = Calendar.getInstance();
					SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					cal.setTime(sdf.parse(msgObj.getCr_date()));
					cal.add(Calendar.HOUR, 24);
					if (cal.getTime().before(Calendar.getInstance().getTime()) && msgObj.getSource() != null
							&& msgObj.getSource() == 110) {
						isLastMessage = true;
					}
				}
			}
		}
		return new ContactMsg(source, isLastMessage);
	}

	/**
	 * Update emailId/phone to the contact if doesn't exists or merge if exists
	 *
	 * @param customerDTO
	 * @param messengerContactId
	 * @return ContactMergeStatus
	 */
	@Override
	public ContactMergeStatus updateFacebookContact(CustomerDTO customerDTO, Integer messengerContactId) {
		log.info("updateFacebookContact: customerDTO {} , messengerContactId {} ", customerDTO, messengerContactId);

		if (((StringUtils.isBlank(customerDTO.getEmailId()) && StringUtils.isBlank(customerDTO.getPhone()))
				|| Objects.isNull(customerDTO.getBusinessId()))) {
			throw new BadRequestException(
					"updateFacebookContact: Either phone or email or businessId missing. Check log");
		}

		MessengerContact facebookContact = messengerContactService.findById(messengerContactId);

		if (Objects.isNull(facebookContact) || StringUtils.isBlank(facebookContact.getFacebookId())) {
			log.error(
					"updateFacebookContact: either messenger contact [ {} ] not found or facebookId not found for mcId {}",
					facebookContact, messengerContactId);
			throw new BadRequestException("Messenger Contact id not valid");
		}

		if (!facebookContact.getBusinessId().equals(customerDTO.getBusinessId())) {
			throw new BadRequestException(
					"updateFacebookContact: businessId does not match in messengerContact and input. Check log");
		}

		customerDTO.setId(facebookContact.getCustomerId());
		// customerDTO.setBusinessId(facebookContact.getBusinessId());
		CustomerDTO responseFromKontacto = contactService.mergeCustomer(customerDTO);
		log.info("updateFacebookContact: response from kontacto {}", responseFromKontacto);
		MessengerUtil.validateCustomerDTOForUpdate(responseFromKontacto);

		String customerContactMatchResult = matchCustomerWithContact(responseFromKontacto, facebookContact);
		BusinessDTO businessDTO = businessService.getBusinessDTO(facebookContact.getBusinessId());

		switch (customerContactMatchResult) {
			case "CONTACT_CUSTOMER_MATCHED": {
				// simply update customer Data in corresponding ContactDocument as
				// FacebookContact points to same customer
				ContactDocument contactDocForUpdate = buildContactDocumentForUpdate(responseFromKontacto, null);
				boolean updateStatus = updateContactDocumentOnES(contactDocForUpdate,
						String.valueOf(facebookContact.getId()), businessDTO.getRoutingId());
				log.info(
						"updateFacebookContact: CONTACT_CUSTOMER_MATCH contact document update status [ {} ] for facebookContact {} payload {} ",
						updateStatus, facebookContact.getId(), customerDTO);
				updateContacts(responseFromKontacto, businessDTO);
				return new ContactMergeStatus(ContactMergeStatus.Status.MERGE_NOT_REQUIRED, messengerContactId,
						facebookContact.getBusinessId());
			}
			case "NO_MATCHING_CONTACT_FOUND": {
				facebookContact.setCustomerId(responseFromKontacto.getId());
				ContactDocument contactDocForUpdate = buildContactDocumentForUpdate(responseFromKontacto, null);
				boolean updateStatus = updateContactDocumentOnES(contactDocForUpdate,
						String.valueOf(facebookContact.getId()), businessDTO.getRoutingId());
				messengerContactService.saveOrUpdateMessengerContact(facebookContact);
				log.info(
						"updateFacebookContact: NO_MATCHING_CONTACT_FOUND contact document update status [ {} ] for facebookContact {} payload {} ",
						updateStatus, facebookContact.getId(), customerDTO);
				updateContacts(responseFromKontacto, businessDTO);
				return new ContactMergeStatus(ContactMergeStatus.Status.MERGE_NOT_REQUIRED, messengerContactId,
						facebookContact.getBusinessId());
			}

			case "CONTACT_CUSTOMER_MISMATCHED": {
				MessengerContact smsContact = messengerContactService.findByCustomerId(responseFromKontacto.getId());
				facebookContact.setCustomerId(smsContact.getCustomerId());
				transferMessageActivityData(smsContact, facebookContact);
				// update facebookContact
				ContactDocument contactDocForUpdate = buildContactDocumentForUpdate(responseFromKontacto,
						facebookContact);

				// merge contact to facebook contact
				// 1. update contactDocument
				updateContactDocumentOnES(contactDocForUpdate, String.valueOf(facebookContact.getId()),
						businessDTO.getRoutingId());
				// 2. add messages for sms contact id to facebook contact id
				MessengerFilter filter = new MessengerFilter();
				filter.setStartIndex(0);
				filter.setCount(10000); // need to get all messages and 10000 messages is highly unlikely unless
				// customer is user's girlfriend/boyfriend in disguise
				filter.setConversationId(smsContact.getId());
				filter.setAccountId(businessDTO.getRoutingId());
				List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(filter);

				// merge Conversation to facebook contact
				messagesFromES.forEach(messageDocument -> {
					messageDocument.setC_id(String.valueOf(facebookContact.getId()));
					ESRequest.Upsert<MessageDocument> document = new ESRequest.Upsert<>(messageDocument,
							messageDocument.getM_id());
					ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
					ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
							.addRoutingId(businessDTO.getRoutingId())
							.addTemplateAndDataModel(null, null)
							.addPayloadForUpsert(document).build();
					elasticSearchService.updateDocument(esRequest, false);
				});
				// delete sms messengerContact from ES
				boolean deleteStatus = conversationService.deleteConversation(smsContact.getId(), businessDTO.getRoutingId(), RefreshPolicy.IMMEDIATE);
				messengerContactService.saveOrUpdateMessengerContact(facebookContact);
				// remove smsContact messengerContact
				messengerContactService.deleteMessengerContact(smsContact.getId());
				log.info(
						"updateFacebookContact: CONTACT_CUSTOMER_MISMATCHED contact document update and messages merged for facebookContact {} payload from ui {}, old contact ES delete status {} payload from kontacto",
						facebookContact.getId(), customerDTO, deleteStatus, responseFromKontacto);
				updateContacts(responseFromKontacto, businessDTO);
				return new ContactMergeStatus(ContactMergeStatus.Status.MERGED, messengerContactId,
						facebookContact.getBusinessId());
			}
		}
		return new ContactMergeStatus(ContactMergeStatus.Status.NEW_CONTACT, messengerContactId,
				facebookContact.getBusinessId()); // not required
	}

	private void updateContacts(CustomerDTO responseFromKontacto, BusinessDTO businessDTO) {
		if (CollectionUtils.isNotEmpty(responseFromKontacto.getUpdateIds())) {
			responseFromKontacto.getUpdateIds().remove(responseFromKontacto.getId()); // already updated during merge
			List<MessengerContact> messengerContactList = messengerContactService
					.getContactIdsForCustomerIds(responseFromKontacto.getUpdateIds());
			messengerContactList.forEach(messengerContact -> {
				responseFromKontacto.setId(messengerContact.getCustomerId());
				ContactDocument contactDocForUpdate = buildContactDocumentForUpdate(responseFromKontacto, null);
				boolean updateStatus = updateContactDocumentOnES(contactDocForUpdate,
						String.valueOf(messengerContact.getId()), businessDTO.getRoutingId());
				log.info("updateContacts: contact ES update for mcId {} and status {}", messengerContact.getId(),
						updateStatus);
			});
		}
	}

	private void transferMessageActivityData(MessengerContact from, MessengerContact to) {
		// why this below check ? Because messenger/contact api might create
		// messenger_contact for customer with no
		// communication history and last_message and encrypted DB columns would be
		// empty for such contacts. We don't want to transfer such data.
		if (StringUtils.isNotBlank(from.getLastMessage()) && from.getLastMsgOn().compareTo(to.getLastMsgOn()) > 0) {
			to.setLastMsgOn(from.getLastMsgOn());
			to.setLastMessage(from.getLastMessage());
			to.setEncrypted(from.getEncrypted());
		}
	}

	/**
	 *
	 * @param customerDTO
	 * @param messengerContact This field is optional
	 * @return ContactDocument
	 */
	private ContactDocument buildContactDocumentForUpdate(CustomerDTO customerDTO, MessengerContact messengerContact) {
		ContactDocument.Builder builder = new ContactDocument.Builder(new ContactDocument());
		ContactDocument contactDocument = builder.editCustomerInfo(customerDTO).build();
		if (Objects.nonNull(messengerContact)) {
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			contactDocument.setL_msg_on(df.format(messengerContact.getLastMsgOn()));
			contactDocument.setUpdatedAt(df.format(messengerContact.getUpdatedAt()));
			contactDocument.setIs_encrypted(messengerContact.getEncrypted());
			contactDocument.setL_msg(messengerContact.getLastMessage());
		}
		return contactDocument;
	}

	private boolean updateContactDocumentOnES(ContactDocument contactDocument, String documentId, Integer routingId) {
		ESRequest.Upsert<ContactDocument> updateDoc = new ESRequest.Upsert<>(contactDocument, documentId);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addIndex(Constants.Elastic.CONTACT_INDEX)
				.addRoutingId(routingId)

				.addPayloadForUpsert(updateDoc).build();
		return elasticSearchService.updateDocument(esRequest, false);
	}

	private String matchCustomerWithContact(CustomerDTO responseFromKontacto, MessengerContact facebookContact) {
		if (responseFromKontacto.getId().equals(facebookContact.getCustomerId()))
			return "CONTACT_CUSTOMER_MATCHED";

		// // check if a sms messenger_contact exists with that customer returned from
		// contactService
		MessengerContact smsContact = messengerContactService.findByCustomerId(responseFromKontacto.getId());
		if (Objects.isNull(smsContact))
			return "NO_MATCHING_CONTACT_FOUND";
		return "CONTACT_CUSTOMER_MISMATCHED";
	}

	@Override
	public ConversationResponse getConversationsV2(MessengerFilter filterMessage) {
		// filter out locations with no sms number
		log.info("getConversations:Performance requestBody {}", filterMessage);
		long businessIdFilterTime = System.currentTimeMillis();
		log.info("getConversations:Performance filter business with sms fetch time: {} seconds",
				(System.currentTimeMillis() - businessIdFilterTime));
		if (CollectionUtils.isEmpty(filterMessage.getBusinessIds())) {
			log.info("getConversationsV2: requestBody after filtering by SMS phone {}", filterMessage);
			return new ConversationResponse();
		}
		long unReadCountStartTime = System.currentTimeMillis();
		Long unreadCount = conversationCount(Collections.singletonList(MessageTag.UNREAD.getCode()),
				filterMessage.getBusinessIds(), filterMessage.getEnterpriseId());
		log.info("getConversations:Performance UNREAD conversationCount fetch time: {} seconds",
				(System.currentTimeMillis() - unReadCountStartTime));
		ConversationResponse conversationResponse = new ConversationResponse();
		conversationResponse.setUnreadBizCount(unreadCount);
		List<Integer> tags = new ArrayList<>();
		if (Objects.isNull(filterMessage.getTag())) {
			tags.add(MessageTag.UNREAD.getCode());
			tags.add(MessageTag.INBOX.getCode());
		} else {
			tags.add(filterMessage.getTag());
		}
		long totalCountTime = System.currentTimeMillis();
		Long count = conversationCount(tags, filterMessage.getBusinessIds(), filterMessage.getEnterpriseId());
		log.info("getConversations:Performance TOTAL conversationCount fetch time: {} seconds",
				(System.currentTimeMillis() - totalCountTime));
		conversationResponse.setCount(count);
		Optional<Integer> businessIdOptional = filterMessage.getBusinessIds().stream().findFirst();
		if (businessIdOptional.isPresent()) {
			long facebookIntegrationStatusFetchTime = System.currentTimeMillis();
			String facebookIntegrationStatus = facebookEventService
					.getFacebookIntegrationStatus(businessIdOptional.get(), filterMessage.getMessengerEnabled());
			Map<Integer, String> businessFbIntegrationStatusMap = new HashMap<>();
			businessFbIntegrationStatusMap.put(businessIdOptional.get(), facebookIntegrationStatus);
			conversationResponse.setBusinessFbIntegrationStatusMap(businessFbIntegrationStatusMap);
			log.info("getConversations:Performance facebook integration status fetch time: {} seconds",
					(System.currentTimeMillis() - facebookIntegrationStatusFetchTime));
		}
		if (StringUtils.isEmpty(filterMessage.getSearchTerm())) {
			long fetchConversationTime = System.currentTimeMillis();
			List<MessengerContactMessage> conversationsFromESV2 = getConversationsFromESV2(filterMessage);
			conversationResponse.setMessengerContactMessages(conversationsFromESV2);
			log.info("getConversations:Performance all conversations fetch time: {} seconds",
					(System.currentTimeMillis() - fetchConversationTime));
			return conversationResponse;
		}
		List<MessengerContactMessage> searchedDocuments = getSearchedDocuments(filterMessage);
		conversationResponse.setMessengerContactMessages(searchedDocuments);
		return conversationResponse;
	}

	private List<MessengerContactMessage> getConversationsFromESV2(MessengerFilter filter) {
		// Prepare token data for ES
		Map<String, Object> convFilter = new HashMap<>();

		if (filter.getCount() == null)
			filter.setCount(25);
		if (filter.getStartIndex() == null)
			filter.setStartIndex(0);

		convFilter.put("from", String.valueOf(filter.getStartIndex()));
		convFilter.put("size", String.valueOf(filter.getCount()));

		if (filter.getTag() != null) {
			if (MessengerTagEnum.ALL.getId() == filter.getTag().intValue()) {
				convFilter.put("tags", "1,2"); // UNREAD and CONVERSATION
			} else {
				convFilter.put("tags", filter.getTag().toString());
			}
		} else {
			convFilter.put("tags", "1,2");
		}

		convFilter.put("businessIds", filter.getBusinessIds().toString());
		if (StringUtils.isNotBlank(filter.getLastMsgTime())) {
			convFilter.put("startDate", filter.getLastMsgTime());
		}
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("convFilter", convFilter);

		long conversationsFetchTime = System.currentTimeMillis();
		List<ContactDocument> contactDocuments = getContactDocumentsFromElastic(filter, dataModel,
				Constants.Elastic.GET_CONVERSATIONS);
		log.info("getConversationsFromESV2:Performance all conversations fetch time: {} seconds",
				(System.currentTimeMillis() - conversationsFetchTime));
		List<MessengerContactMessage> contacts = new ArrayList<>();
		int i = 0;
		int index = -1;
		long uiObjectsCreationTime = System.currentTimeMillis();
		for (ContactDocument contactDocument : contactDocuments) {
			MessengerContact con = messengerContactService.findById(contactDocument.getM_c_id());
			String facebookId = getFacebookId(con);
			log.debug("getConversationsFromES: contact : {} and id {}", con, contactDocument.getM_c_id());
			String googleConvId = getGoogleConversationId(con);
			String instagramConvId = getInstagramConversationId(con);
			String appleConvId = getAppleConversationId(con);
			String twitterConvId = getTwitterConversationId(con);
			String whatsappConvId = getWhatsappConversationId(con);
			MessengerContactMessage conversation = new MessengerContactMessage(contactDocument, 1, facebookId,
					ConversationView.MESSAGE, false, false, googleConvId, instagramConvId, appleConvId,twitterConvId,whatsappConvId);
			contacts.add(conversation);

			if (filter.getMoveConversationToTop() != null
					&& conversation.getCustomer().getId().equals(filter.getMoveConversationToTop())) {
				index = i;
			}
			i++;
		}
		log.info("getConversationsFromESV2:Performance conversations ui object list creation time: {} seconds",
				(System.currentTimeMillis() - uiObjectsCreationTime));
		if (filter.getMoveConversationToTop() != null) {
			// handling case when conversation is refreshed on same page
			if (index >= 0) {
				MessengerContactMessage contactMsg = contacts.get(index);
				contacts.remove(index);
				contacts.add(0, contactMsg);
			} else {
				convFilter = new HashMap<>();
				convFilter.put("size", 1);
				convFilter.put("from", 0);
				convFilter.put("tags", null);
				convFilter.put("c_id", filter.getMoveConversationToTop().toString());

				dataModel.put("convFilter", convFilter);
				// Call ES to get ContactDocument for Move Conversation to Top
				ESRequest esRequestTop = new ESRequest.Builder(new ESRequest())
						.addIndex(Constants.Elastic.CONTACT_INDEX)
						.addRoutingId(filter.getEnterpriseId())
						.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION, dataModel).build();
				ContactDocument contactDocumentTop = elasticSearchService
						.searchByQuery(esRequestTop, ContactDocument.class).get(0);

				MessengerContact con = messengerContactService.findById(contactDocumentTop.getM_c_id());
				String facebookId = getFacebookId(con);
				String googleConvId = getGoogleConversationId(con);
				String instagramConvId = getInstagramConversationId(con);
				String appleConvId = getAppleConversationId(con);
				String twitterConvId = getTwitterConversationId(con);
				String whatsappConvId = getWhatsappConversationId(con);
				contacts.add(0, new MessengerContactMessage(contactDocumentTop, 1, facebookId, ConversationView.MESSAGE,
						false, false, googleConvId, instagramConvId, appleConvId,twitterConvId,whatsappConvId));
			}
		}

		return contacts;
	}

	@Override
	public MessageResponse addNewContactAndSendSms(Integer accountId, AddContactMessengerMessage message) throws Exception {
		log.info("addNewContactAndSend requestBody {}", message);
		// 1. New Contact Request
		SendMessageDTO sendMessage = message.getSendMessage();
		Integer fromBusinessId = sendMessage.getFromBusinessId();
		BusinessDTO businessDTO = businessService.getBusinessDTO(fromBusinessId);
		if (Objects.nonNull(accountId) && !businessDTO.getAccountId().equals(accountId)) {
			log.info("Suspicious Activity, payload : {}, accountID :{}", message, accountId);
			throw new MessengerException("Suspicious Activity : add-and-send");
		}
		if (message.getUserEmailId() != null) {
			UserDTO user=commonService.getUserIdByEmail(message.getUserEmailId(), fromBusinessId);
			message.setUserId(user.getId());
		}
		CustomerDTO customerDTO =null;
		if(message.getCustomerId()==null) {
			String countryCode = message.getNewContact().getLocation().getCountryCode();
			KontactoRequest kontactoRequest = createKontactoRequest(message, countryCode);
			log.debug("Requesting contact service to get/create customer for phone {} and country code {}",
					message.getNewContact().getPhone(), countryCode);
			customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, businessDTO.getRoutingId(),
					message.getUserId());
			log.debug("addNewContactAndSendSms: response returned from kontacto {}", customerDTO);
		}else {
			customerDTO = message.getFetchedContact() != null ? message.getFetchedContact()
					: contactService.findById(message.getCustomerId());
		}

		// 2.Create MessengerContact
		MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
				fromBusinessId, customerDTO.getId(), businessDTO.getAccountId());
		messengerContact.setSpamMarkedBy(message.getUserId());
		messengerContactService.saveOrUpdateMessengerContact(messengerContact);
		MessageTag tag=MessageTag.INBOX;
		if(BooleanUtils.isFalse(message.isOpenConversation()) && messengerContact.getTag().equals(MessageTag.ARCHIVED.getCode())) {
			sendMessage.setUpdateTag(false);
			tag=MessageTag.ARCHIVED;
		}

		// Update MC on ES
		messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, tag, null);

		// 3. After Contact is created, handle template (CX, RR, Survey) flow through
		// campaign.
		// Update message body with text from Campaign service inside handleEvent SmsSend handler

		// default source is sms
		if (message.getSource() == null) {
			message.setSource(1);
		}
		// 4. After body is updated, Send SMS Request
		sendMessage.setBusinessIdentifierId(String.valueOf(fromBusinessId));
		sendMessage.setToCustomerId(String.valueOf(messengerContact.getId()));
		sendMessage.setUserId(message.getUserId());
		sendMessage.setSource(message.getSource());
		

		PaymentUiRequest paymentRequest = sendMessage.getPaymentRequest();

		MessageResponse response;
		if (Objects.nonNull(paymentRequest) && "TERMINAL".equals(paymentRequest.getMethod())) {
			response = paymentCardReaderService.handleCardReaderPayment(sendMessage);
		} else if (Objects.nonNull(paymentRequest) && ("CARD_ENTRY".equals(paymentRequest.getMethod())
				|| "CARD_ON_FILE".equals(paymentRequest.getMethod()))) {
			response = paymentManualCardEntryService.handleManualCardEntryPayment(sendMessage);
		} else if (Source.SECURE_MESSAGE.getSourceId().equals(message.getSource())) {
			SecureLinkGenerationRequest secureLinkGenerationRequest = new SecureLinkGenerationRequest(
					messengerContact.getId(), 1, businessDTO.getAccountId(), message.getUserId(), true);
			MessageResponse secureMessageResponse = secureMessagingAuthService
					.generateSecureMessagingLink(secureLinkGenerationRequest);
			sendMessage.setCustomerId(customerDTO.getId());
			response = secureMessageSendEventHandler.handle(sendMessage);
			response.getMessages().addAll(secureMessageResponse.getMessages());
		} else {
			response = messengerEventHandlerService.handleEvent(sendMessage);
		}
		response.setMcId(messengerContact.getId());
		response.setCustomerId(customerDTO.getId());
		if (BooleanUtils.isTrue(sendMessage.getSendAndClose())){
			ConversationStatusRequest updateStatusRequest = new ConversationStatusRequest();
			updateStatusRequest.setOpen(false);
			conversationService.updateStatus(accountId, sendMessage.getUserId(), messengerContact.getId(), updateStatusRequest,false);
		}
		return response;
	}

		private KontactoRequest createKontactoRequest(AddContactMessengerMessage message, String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setName(message.getNewContact().getName());
		kontactoRequest.setEmailId(message.getNewContact().getEmailId());
		kontactoRequest.setPhone(message.getNewContact().getPhone());
		kontactoRequest.setSource(KontactoRequest.DASHBOARD);
		if (message.getLeadSource() != null && message.getLeadSource() == 14) {
			kontactoRequest.setSource(KontactoRequest.CONTACT_US);
		}
		kontactoRequest.setBusinessId(message.getSendMessage().getFromBusinessId());

		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);

		return kontactoRequest;
	}

	private String getGoogleConversationId(MessengerContact con) {
		String googleConversationId = null;
		if (con != null) {
			googleConversationId = con.getGoogleConversationId();
		}
		return googleConversationId;
	}

	private String getInstagramConversationId(MessengerContact con) {
		String instagramConversationId = null;
		if (con != null) {
			instagramConversationId = con.getInstagramConversationId();
		}
		return instagramConversationId;
	}

	private String getAppleConversationId(MessengerContact con) {
		String appleConversationId = null;
		if (con != null) {
			appleConversationId = con.getAppleConversationId();
		}
		return appleConversationId;
	}

	@Override
	@Cacheable(cacheNames = Constants.MESSENGER_LOCATION_CACHE, key = "'dashboard-'.concat(#userId).concat('-').concat(#accountId)", unless = "#result == null")
	public List<MessengerLocation> getDashboardLocations(NotificationRequest request, Integer userId,
														 Integer accountId) {
		log.info("getDashboardLocations requestBody {} accountId {} & userId {}", request, accountId, userId);
		ConversationFilter filters = request.getFilters();
		List<MessengerLocation> locations = new ArrayList<>();
		// location info is only required in case of enterprise
		if (!request.getUserLoginMessage().isSmb()) {
			Map<Integer, BusinessDTO> businessDataMap = businessService
					.getBusinessDataForMessenger(filters.getBusinessIds(), userId, accountId);
			locations = businessDataMap.values().stream()
					.filter(businessDTO -> !businessDTO.getBusinessId().equals(accountId))
					.map(dto -> new MessengerLocation(dto, 0)).collect(Collectors.toList());
		}
		return locations;
	}

	@Override
	public MessageResponse switchChatLocation(ChatTransferMessage message, Integer userId, Integer accountId,String requestSource) {
		log.info("switchChatLocation requestBody {}", message);
		BusinessDTO oldBusinessDTO = businessService.getBusinessDTO(message.getOldBusinessId());
		Assert.notNull(oldBusinessDTO,
				"SwitchChatLocation: No business returned by core-service for id" + message.getOldBusinessId());

		BusinessDTO newBusinessDTO = businessService.getBusinessDTO(message.getNewBusinessId());
		Assert.notNull(newBusinessDTO,
				"SwitchChatLocation: No business returned by core-service for id" + message.getNewBusinessId());

		UserDTO userDTO = getUserDTO(userId);
		if (userDTO == null) {
			throw new NotFoundException(ErrorCode.USER_NOT_FOUND);
		}
		MessengerContact currentMessengerContact = messengerContactService.findById(message.getCurrentMcId());
		CustomerDTO currentCustomerDto = contactService.findById(currentMessengerContact.getCustomerId());

		KontactoRequest kontactoRequest = createKontactoRequestForChatTransfer(message, currentCustomerDto);
		log.info("Requesting contact service to get/create customer for phone {} and country code {}",
				message.getNewContact().getPhone(), message.getNewContact().getLocation().getCountryCode());
		CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, newBusinessDTO.getRoutingId(),
				userId);
		log.info("switchChatLocation: response returned from kontacto {}", customerDTO);

		MessengerContact messengerContact = messengerContactService.findByCustomerId(customerDTO.getId());

		if (ObjectUtils.isEmpty(messengerContact)) {
			// MessengerContact does not exist on newBusinessId
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					newBusinessDTO.getBusinessId(),
					customerDTO.getId(), accountId);
		}

		// Tag should be unread
		MessageTag tag = MessageTag.UNREAD;

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE"); // Setting as RECEIVE so as not to add prefix in
		// conversation
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastMsgOn(new Date());
		messengerContact.setLastMessage("Added from "
				+ (StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias()) ? oldBusinessDTO.getBusinessAlias()
				: oldBusinessDTO.getBusinessName()));
		messengerContact.setTag(tag.getCode());
		messengerContact.setUpdatedAt(new Date());
		messengerContact.setEncrypted(0);
		messengerContact.setViewedBy(userId != null ? String.valueOf(userId) : null);
		messengerContact = messengerContactService.saveOrUpdateMessengerContact(messengerContact);

		ContactDocument chatTransferDoc = messengerContactService.updateContactOnES(messengerContact, customerDTO,
				newBusinessDTO, tag, userDTO);

		Date created = new Date();
		String actorName = userDTO != null ? userDTO.getName() : null;
		String authToken = null;
		if (message.isSaveChatHistory()) {
			// generate jwtToken
			long nowMillis = System.currentTimeMillis();
			ChatTransferAuth authInfo = ChatTransferAuth.builder()
					.accountId(accountId)
					.fromBusinessId(message.getOldBusinessId())
					.toBusinessId(message.getNewBusinessId())
					.fromMcId(message.getCurrentMcId())
					.toMcId(messengerContact.getId())
					.timeStamp(nowMillis)
					.build();
			try {
				authToken = JwtUtil.createJWTAuth(authInfo, Constants.MESSENGER_SERVICE);
			} catch (Exception e) {

			}
		}
		// Activity for oldBusinessId
		ActivityDto oldBusinessChatTransferActivity = ActivityDto.builder().mcId(message.getCurrentMcId())
				.created(created).updated(created)
				.activityType(ActivityType.CHAT_LOCATION_TRANSFER)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(userId).actorName(actorName)
				.source(Source.SMS.getSourceId())
				.fromBusinessId(message.getOldBusinessId())
				.fromBusinessName(
						StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias()) ? oldBusinessDTO.getBusinessAlias()
								: oldBusinessDTO.getBusinessName())
				.fromMcId(message.getCurrentMcId())
				.toBusinessId(message.getNewBusinessId())
				.toBusinessName(
						StringUtils.isNotBlank(newBusinessDTO.getBusinessAlias()) ? newBusinessDTO.getBusinessAlias()
								: newBusinessDTO.getBusinessName())
				.toMcId(messengerContact.getId())
				.accountId(accountId).businessId(oldBusinessDTO.getBusinessId())
				.transferAuthToken(authToken)
				.build();

		saveActivity(oldBusinessChatTransferActivity);

		// Activity for newBusinessId
		ActivityDto newBusinessChatTransferActivity = ActivityDto.builder().mcId(messengerContact.getId())
				.created(created).updated(created)
				.activityType(ActivityType.CHAT_LOCATION_TRANSFER)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.actorId(userId).actorName(actorName)
				.source(Source.SMS.getSourceId())
				.fromBusinessId(message.getOldBusinessId())
				.fromBusinessName(
						StringUtils.isNotBlank(oldBusinessDTO.getBusinessAlias()) ? oldBusinessDTO.getBusinessAlias()
								: oldBusinessDTO.getBusinessName())
				.fromMcId(message.getCurrentMcId())
				.toBusinessId(message.getNewBusinessId())
				.toBusinessName(
						StringUtils.isNotBlank(newBusinessDTO.getBusinessAlias()) ? newBusinessDTO.getBusinessAlias()
								: newBusinessDTO.getBusinessName())
				.toMcId(messengerContact.getId())
				.accountId(accountId).businessId(newBusinessDTO.getBusinessId())
				.transferAuthToken(authToken)
				.build();

		MessageDocument transferActivityDoc = saveActivity(newBusinessChatTransferActivity);

		// Get All Messages including Activity
		MessageRequest getMessageRequest = new MessageRequest();
		getMessageRequest.setMessengerContactId(messengerContact.getId());
		getMessageRequest.setBusinessId(newBusinessDTO.getBusinessId());
		getMessageRequest.setWeb(message.isWeb());
		getMessageRequest.setShowReviews(message.isShowReviews());
		getMessageRequest.setAccess(message.getAccess());
		getMessageRequest.setSwitchChatLocation(true);
		MessageResponse response = messageService.getMessageV2(getMessageRequest, false, userId, accountId,requestSource);

		//push to firebase on account bucket
		firebaseService.pushToFireBaseAccountBucket(chatTransferDoc, transferActivityDoc, userId, null,true);

		// Email Notification
		MessengerGlobalFilter notificationMetaData = getEmailNotificationMetaData(newBusinessDTO, messengerContact,
				userDTO);
		notificationService.processMessageNotification(notificationMetaData, newBusinessDTO, transferActivityDoc);
		return response;

	}

	private UserDTO getUserDTO(Integer userId) {
		UserDTO userDTO = null;
		if (!Objects.isNull(userId)) {
			userDTO = communicationHelperService.getUserDTO(userId);
		}
		return userDTO;
	}

	private KontactoRequest createKontactoRequestForChatTransfer(ChatTransferMessage message,
																 CustomerDTO currentCustomerDto) {
		String countryCode = message.getNewContact().getLocation().getCountryCode();
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setName(message.getNewContact().getName());
		kontactoRequest.setEmailId(message.getNewContact().getEmailId());
		kontactoRequest.setPhone(message.getNewContact().getPhone());
		kontactoRequest.setSource(currentCustomerDto.getSource());
		kontactoRequest.setBusinessId(message.getNewBusinessId());

		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);

		return kontactoRequest;
	}

	private MessageDocument saveActivity(ActivityDto activity) {
		conversationActivityService.persistActivityInDatabase(activity, null);
		return conversationActivityService.persistActivityInES(activity);
	}

	@Override
	public MessengerGlobalFilter getEmailNotificationMetaData(BusinessDTO businessDTO,
															  MessengerContact messengerContact, UserDTO userDTO) {
		// SMSMessageDTO dto = (SMSMessageDTO) messageDTO;

		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT_TRANSFER);
		notificationRequest.setTimeZone(businessDTO.getTimeZone());
		// notificationRequest.setMsgId(dto.getSmsDTO().getSmsId());
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(10); // number of messages to be fetched from ES
		notificationRequest.setUserDTO(userDTO);
		// The creation time is the last received time if last delivery time is null.
		// if (dto.getSmsDTO().getCreateDate() != null) {
		// notificationRequest.setLastMsgTime(dto.getSmsDTO().getCreateDate().getTime());
		// } else {
		notificationRequest.setLastMsgTime(new Date().getTime());
		// }
		notificationRequest.setConversationId(messengerContact.getId());
		return notificationRequest;

	}

	@Override
	public void updateMessageLikeUnlike(LikeUnlikeRequest request){
		if(Objects.nonNull(request.getMessageId()) && Source.INSTAGRAM.equals(Source.getByName(request.getSource()))){
			MessageDocument messageDocument=getMessageById(request.getMessageId(),request.getAccountId());
			if(Objects.isNull(messageDocument)){
				log.info("Message not found for message_id: {} and accountId: {}",request.getMessageId(),request.getAccountId());
				return;
			}
			MessengerContact mc = messengerContactService.findById(request.getMessengerContactId());
			Optional<Lock> lockOpt = Optional.empty();
			try{
				String lockKey = Constants.CUSTOMER_ID_PREFIX + mc.getCustomerId();
				lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
				if (!lockOpt.isPresent()) {
					log.info("Lock is already acquired for the key:{}", lockKey);
					throw new RedisLockException(ErrorCode.SMS_SEND_LOCK);
				}
				if(request.isLike() != messageDocument.isLiked()){
					MessageLikedBy messageLikedBy=new MessageLikedBy();
					messageDocument.setLiked(request.isLike());
					messageLikedBy.setLikedByUser(request.getUserId());
					messageLikedBy.setLastUpdatedAt(new Date().getTime());
					Date updateDate= new Date();
					DateFormat df= new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
					String updateAtTime=df.format(updateDate);
					messageDocument.setUpdatedAt(updateAtTime);
					messageDocument.setU_time(new Date().getTime());
					messageDocument.setMessageLikedBy(messageLikedBy);
					messageDocument.setType(MessageDocument.Type.UPDATE.getType());
					//need to handle, consistency
					if(request.isPublishSocialEvent()){
						publishMessageLikeUnlikeEventSocial(request,messageDocument.getM_id());
					}
					messengerContactService.updateMessageOnES(messageDocument,request.getAccountId());
					mirrorOnWeb(request.getAccountId(), request.getBusinessId(),messageDocument);
				}
			}finally {
				if (lockOpt.isPresent()) {
					redisLockService.unlock(lockOpt.get());
				}
			}
		}
	}

	public MessageDocument getMessageById(String messageId,Integer accountId){
		Map<String,Object> dataModel=new HashMap<>();
		dataModel.put("_id",messageId);
		ESRequest esRequest=new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(accountId)
				.addTemplateAndDataModel(Constants.Elastic.MESSAGE_BY_ID_V2,dataModel)
				.build();
		List<MessageDocument> messageDocumentFromEs = elasticSearchService.searchByQuery(esRequest,MessageDocument.class);
		if(CollectionUtils.isNotEmpty(messageDocumentFromEs)){
			return messageDocumentFromEs.get(0);
		}
		return null;
	}
	
	private void publishMessageLikeUnlikeEventSocial(LikeUnlikeRequest request,String m_id){
		InstagramMessage instagramMessage=instagramMessageService.findById(Integer.valueOf(m_id));
		LikeUnlikeEventSocial likeUnlikeEventSocial=new LikeUnlikeEventSocial();
		likeUnlikeEventSocial.setBusinessId(request.getBusinessId());
		likeUnlikeEventSocial.setIsLiked(request.isLike());
		likeUnlikeEventSocial.setChannel(request.getSource());
		likeUnlikeEventSocial.setAuthorId(instagramMessage.getSenderInstagramId());
		likeUnlikeEventSocial.setFeedId(request.getMessageId());
		likeUnlikeEventSocial.setMId(instagramMessage.getMessageId());
		socialService.sendMessageLikeUnlikeEvent(likeUnlikeEventSocial);
	}
	
	@Override
	public void deleteMessage(MessageDeleteRequest request, Integer accountId, Integer userId) {
		if (request.getMessageId() == null) {
			log.info("messageId is null,cannot delete this message for request: {}",request);
			return; 
		}
		MessengerContact mc = messengerContactService.findById(request.getMessengerContactId());
		Optional<Lock> lockOpt = Optional.empty();

		try {
			String messageId = request.getMessageId();
			String lockKey = Constants.CUSTOMER_ID_PREFIX + mc.getCustomerId();
			lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);

			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key: {}", lockKey);
				throw new RedisLockException(ErrorCode.SMS_SEND_LOCK);
			}

			MessengerFilter filter = createMessengerFilter(accountId, request);
			List<MessageDocument> messagesFromES = messengerContactService.getMessagesFromES(filter);

			if (CollectionUtils.isEmpty(messagesFromES)) {
				log.info("Messages not found for message_id: {} and accountId: {}", messageId, accountId);
				return;
			}

			MessageDocument messageDocument = findMessageInList(messagesFromES, messageId);

			if (messageDocument == null) {
				messageDocument = getMessageById(request.getMessageId(), accountId);
			}

			if (request.isDelete() && messageDocument != null) {
				updateMessageAndContactEs(request, userId, messageDocument, messagesFromES, accountId);
				mirrorOnWeb(accountId, request.getBusinessId(),messageDocument);
			}
			if(request.isPublishSocialEvent() && ( Source.INSTAGRAM.name().equals(request.getSource()) || Source.TWITTER.name().equals(request.getSource()) || Source.FACEBOOK.name().equals(request.getSource()))){
				publishMessageDeleteEventSocial(request,messageDocument.getM_id());
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}
	private MessengerFilter createMessengerFilter(Integer accountId, MessageDeleteRequest request) {
		MessengerFilter filter = new MessengerFilter();
		filter.setAccountId(accountId);
		filter.setStartIndex(0);
		filter.setCount(100);
		filter.setConversationId(request.getMessengerContactId());
		Map<String, Object> params = new HashMap<>();
		params.put("messageType", ControllerUtil.getJsonTextFromObject(Arrays.asList(MessageType.CHAT.name())));
		filter.setParams(params);
		filter.setQueryFile(Constants.Elastic.GET_MESSAGES_WITHOUT_AUTO_REPLY);
		return filter;
	}

	private MessageDocument findMessageInList(List<MessageDocument> messagesFromES, String messageId) {
		return messagesFromES.stream()
				.filter(message -> message.getId().equals(messageId))
				.findFirst()
				.orElse(null);
	}

	private void updateMessageAndContactEs(MessageDeleteRequest request,Integer userId,MessageDocument messageDocument,List<MessageDocument> messagesFromES,Integer accountId) {
		messageDocument.setDeleted(true);
		messageDocument.setMsg_body("");

		MessageDeletedBy messageDeletedBy= new MessageDeletedBy();
		messageDeletedBy.setDeletedByUser(userId);
		messageDeletedBy.setLastUpdatedAt(new Date().getTime());

		Date updateDate = new Date();
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		String updatedAtTime = df.format(updateDate);

		messageDocument.setUpdatedAt(updatedAtTime);
		messageDocument.setU_time(new Date().getTime());
		messageDocument.setMessageDeletedBy(messageDeletedBy);
		messageDocument.setIs_encrypt(0);
		
		if(StringUtils.isNotBlank(messageDocument.getStoryMentionUrl())){
			messageDocument.setStoryMentionUrl("");
		}
		if(StringUtils.isNotBlank(messageDocument.getStoryReplyUrl())){
			messageDocument.setStoryReplyUrl("");
		}
		
		if(Objects.nonNull(messageDocument.getMediaFiles())){
			MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile();
			messageDocument.setMediaFiles(Collections.singletonList(mediaFile));
		}
		if(Objects.nonNull(messageDocument.getA_url())){
			messageDocument.setA_url("");
			messageDocument.setA_size("");
			messageDocument.setA_contype("");
			messageDocument.setA_name("");
			messageDocument.setA_ext("");

		}
		messageDocument.setType(MessageDocument.Type.UPDATE.getType());
		updateMessageAndContactDb(request,messageDocument);
		messengerContactService.updateMessageOnES(messageDocument, accountId);

		if (messagesFromES.get(0).getId().equals(request.getMessageId())){
			if(Objects.nonNull(messagesFromES.get(0).getU_id()) && (messagesFromES.get(0).getU_id() == -1 || messagesFromES.get(0).getU_id() == -11)){
				return;
			}
			ContactDocument contactDocument = new ContactDocument();
			LastMessageMetaData lastMessageMetaData = new LastMessageMetaData();
			contactDocument.setL_msg("This message was deleted.");
			contactDocument.setIs_encrypted(0);
			lastMessageMetaData.setLastMessageDeleted(true);
			lastMessageMetaData.setLastMessageDeletedBy(userId);
			contactDocument.setLastMessageMetaData(lastMessageMetaData);
			contactDocument.setUpdatedAt(df.format(new Date()));
			contactDocument.setLastUpdateDate(new Date().getTime());
			messengerContactService.updateContactOnES(request.getMessengerContactId(), contactDocument, accountId);
		}
	}
	
	@Transactional
	private void updateMessageAndContactDb(MessageDeleteRequest request,MessageDocument messageDocument){
		Integer messageId =Integer.valueOf(messageDocument.getM_id());
		switch (request.getSource()) {
			case "FACEBOOK":
				facebookMessageService.updateFacebookMessageBody("",messageId);
				break;
			case "INSTAGRAM":
				instagramMessageService.updateInstagramMessageBody("",messageId);
				break;
			case "GOOGLE":
				googleMessageService.updateGoogleMessageBody("",messageId);
				break;
			case "APPLE":
				appleMessageRepository.updateAppleMessageBody("",messageId);
				break;
			case "SMS":
				smsService.updateSmsMessageBody("",messageId);
				break;
			case "EMAIL":
				emailService.updateEmailMessageBody("",messageId);
				break;
			case "TWITTER":
				twitterMessageService.updateTwitterMessageBody("",messageId);
				break;
			case "VOICE_CALL":
				voiceCallService.updateVoicecallMessageBody("",messageId);
				break;
			case "WHATSAPP":
				whatsappMessageService.updateWAMessageBody("",messageId);
				break;
			default:
				throw new IllegalArgumentException("Invalid channel: " + request.getSource());
		}
	}


		private void mirrorOnWeb(Integer accountId, Integer businessId,MessageDocument messageDocument) {
		FirebaseDto firebaseDto = new FirebaseDto();
		firebaseDto.setAccountId(accountId);
		firebaseDto.setBusinessId(businessId);
		firebaseDto.setTimestamp(new Date().getTime());
		firebaseDto.setLastMsgTimeStamp(messageDocument.getLastUpdateDate());
		firebaseDto.setMcId(Integer.valueOf(messageDocument.getC_id()));
		fcmService.mirrorOnWeb(firebaseDto);
	}

	private void publishMessageDeleteEventSocial(MessageDeleteRequest request,String m_id){
		MessageDeleteEventSocial eventSocial=new MessageDeleteEventSocial();
		eventSocial.setBusinessId(request.getBusinessId());
		eventSocial.setDeleted(request.isDelete());
		eventSocial.setChannel(request.getSource());
		eventSocial.setFeedId(request.getMessageId());
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.MESSAGE_DELETE_EVENT_SOCIAL,
				request.getAccountId(), eventSocial);
	}

	private String getTwitterConversationId(MessengerContact con) {
		String twitterConversationId = null;
		if (con != null) {
			twitterConversationId = con.getTwitterConversationId();
		}
		return twitterConversationId;
	}

	@Override
	public Integer moveMessage(MessageMoveRequest request, Integer accountId, Integer userId) {
		Optional<Lock> lockOpt1 = Optional.empty();
		Optional<Lock> lockOpt2 = Optional.empty();
		try {
			String lockKey1 = Constants.CUSTOMER_ID_PREFIX + request.getFromCid();
			lockOpt1 = redisLockService.tryLock(lockKey1, 200, TimeUnit.MILLISECONDS);
			
			String lockKey2 = Constants.CUSTOMER_ID_PREFIX + request.getToCid();
			lockOpt2 = redisLockService.tryLock(lockKey2, 200, TimeUnit.MILLISECONDS);
			
			if (!lockOpt1.isPresent() || !lockOpt2.isPresent()) {
				log.info("Lock is already acquired on either of the keys: {} or {}", lockKey1,lockKey2);
				throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
			BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
			boolQueryBuilder
					.must(QueryBuilders.termsQuery("c_id", Arrays.asList(request.getFromCid(), request.getToCid())));
			SortBuilder<?> sortBuilder = SortBuilders.fieldSort("l_msg_on").order(SortOrder.DESC);
			ESQueryBuilderRequest<ContactDocument> esQueryBuilderRequest = ESQueryBuilderRequest
					.buildESQueryBuilderRequest(0, 2, ContactDocument.class, accountId, Constants.Elastic.CONTACT_INDEX,
							boolQueryBuilder, sortBuilder, null, null);
			List<ContactDocument> conversations = elasticSearchService
					.getResultUsingQueryBuilder(esQueryBuilderRequest);
			Map<Integer, ContactDocument> conversationMap = conversations.stream()
					.collect(Collectors.toMap(ContactDocument::getC_id, c -> c));
			BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
			queryBuilder.must(QueryBuilders.termsQuery("_id", request.getMessageId()));
			ESQueryBuilderRequest<MessageDocument> esQueryBuilderRequest1 = ESQueryBuilderRequest
					.buildESQueryBuilderRequest(0, 1, MessageDocument.class, accountId, Constants.Elastic.MESSAGE_INDEX,
							queryBuilder, null, null, null);
			List<MessageDocument> messageDocuments = elasticSearchService
					.getResultUsingQueryBuilder(esQueryBuilderRequest1);
			if (CollectionUtils.isEmpty(messageDocuments)) {
				log.info("No message found for messageId " + request.getMessageId());
				throw new NotFoundException(ErrorCode.MESSAGE_NOT_FOUND);
			}
			MessageDocument messageDocument = messageDocuments.get(0);
			String msgBody = MessengerUtil.decryptMessage(messageDocument);
			ContactDocument fromConv = conversationMap.get(request.getFromCid());
			ContactDocument toConv = conversationMap.get(request.getToCid());
			boolean isToConversationExist = Objects.nonNull(toConv);
			if (!isToConversationExist) {
				toConv = createConversation(request, msgBody, conversationMap);
			}
			updateMessageCid(messageDocument, request, accountId, toConv);
			updateLastMessage(toConv, fromConv, request, isToConversationExist, accountId, msgBody);
			Date swatichFromActivityDate = new Date(messageDocument.getCr_time() + 1000l);
			Date swatichToActivityDate = new Date(messageDocument.getCr_time());
			commonService.createConversationSwitchActivities(accountId, swatichFromActivityDate, swatichToActivityDate,
					fromConv, toConv);
			commonService.updateConversationActiveState(toConv, conversations);
			firebaseService.twinOnWeb(accountId, fromConv.getB_id(), toConv.getM_c_id());
			return toConv.getM_c_id();
		} finally {
			if (lockOpt1.isPresent()) {
				redisLockService.unlock(lockOpt1.get());
			}
			if (lockOpt2.isPresent()) {
				redisLockService.unlock(lockOpt2.get());
			}
		}
	}

	private void updateLastMessage(ContactDocument toConv, ContactDocument fromConv,MessageMoveRequest request, boolean isToConversationExist,
			Integer accountId,String msgBody) {
		List<ContactDocument> contactDocumentToBeUpserted = new ArrayList<>();
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		if(isToConversationExist) {
			// Update the latest message in the conversation where message is moved.
			BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
			queryBuilder.must(QueryBuilders.termQuery("c_id", toConv.getM_c_id().toString()));
			queryBuilder.must(QueryBuilders.termQuery("messageType", MessageType.CHAT.toString()));
			SortBuilder<?> sortBuilder = SortBuilders.fieldSort("cr_date").order(SortOrder.DESC);
			ESQueryBuilderRequest<MessageDocument> esQueryBuilderRequest1  = ESQueryBuilderRequest
					.buildESQueryBuilderRequest(0, 1, MessageDocument.class, accountId,
							Constants.Elastic.MESSAGE_INDEX, queryBuilder, sortBuilder, null, null);
			List<MessageDocument> messageDocuments = elasticSearchService
					.getResultUsingQueryBuilder(esQueryBuilderRequest1);
			if(CollectionUtils.isEmpty(messageDocuments)) {
				log.info("No message found for messageId " + request.getMessageId());
				throw new NotFoundException(ErrorCode.MESSAGE_NOT_FOUND);
			}
			if(messageDocuments.get(0).getId().equals(request.getMessageId())) {
				String lastMessage = getLastMessage(messageDocuments.get(0));
				MessengerContact toMessengerContact=messengerContactService.findByCustomerId(request.getToCid());
				toMessengerContact.setLastMessage(lastMessage);
				boolean isEncrpypted = EncryptionUtil.encryptLastMessageWithMcId(toMessengerContact, 1, toConv.getB_num().toString());
				toMessengerContact.setEncrypted(isEncrpypted ? 1 : 0);
				messengerContactService.saveOrUpdateMessengerContact(toMessengerContact);
				ContactDocument contactForES = new ContactDocument();
				contactForES.setM_c_id(toMessengerContact.getId());
				contactForES.setIs_encrypted(isEncrpypted ? 1 : 0);
				contactForES.setUpdatedAt(df.format(new Date()));
				contactForES.setL_msg(toMessengerContact.getLastMessage());
				contactDocumentToBeUpserted.add(contactForES);
			}
		}
		//update last message of the conversation from which message is moved
		BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
		queryBuilder.must(QueryBuilders.termQuery("c_id", fromConv.getM_c_id().toString()));
		queryBuilder.must(QueryBuilders.termQuery("messageType", MessageType.CHAT.toString()));
		SortBuilder<?> sortBuilder = SortBuilders.fieldSort("cr_date").order(SortOrder.DESC);
		ESQueryBuilderRequest<MessageDocument> esQueryBuilderRequest1  = ESQueryBuilderRequest
				.buildESQueryBuilderRequest(0, 1, MessageDocument.class, accountId,
						Constants.Elastic.MESSAGE_INDEX, queryBuilder, sortBuilder, null, null);
		List<MessageDocument> messageDocuments = elasticSearchService
				.getResultUsingQueryBuilder(esQueryBuilderRequest1);
		if(CollectionUtils.isEmpty(messageDocuments)) {
			ContactDocument contactForES = new ContactDocument();
			contactForES.setM_c_id(fromConv.getM_c_id());
			contactForES.setIs_encrypted(0);
			contactForES.setUpdatedAt(df.format(new Date()));
			contactForES.setL_msg("Conversation switched to "+toConv.getC_name());
			contactDocumentToBeUpserted.add(contactForES);
		}else {
			String lastMessage = getLastMessage(messageDocuments.get(0));
			MessengerContact fromMessengerContact=messengerContactService.findByCustomerId(request.getFromCid());
			fromMessengerContact.setLastMessage(lastMessage);
			boolean isEncrpypted = EncryptionUtil.encryptLastMessageWithMcId(fromMessengerContact, 1, fromConv.getB_num().toString());
			fromMessengerContact.setEncrypted(isEncrpypted ? 1 : 0);
			messengerContactService.saveOrUpdateMessengerContact(fromMessengerContact);
			ContactDocument contactForES = new ContactDocument();
			contactForES.setM_c_id(fromMessengerContact.getId());
			contactForES.setIs_encrypted(isEncrpypted ? 1 : 0);
			contactForES.setUpdatedAt(df.format(new Date()));
			contactForES.setL_msg(fromMessengerContact.getLastMessage());
			contactDocumentToBeUpserted.add(contactForES);
		}
		if (CollectionUtils.isNotEmpty(contactDocumentToBeUpserted)) {
			long start = System.currentTimeMillis();
			log.info("updating conversation active state in bulk");
			BulkUpsertPayload<ContactDocument> bulkUpsertContacts = new BulkUpsertPayload<>(contactDocumentToBeUpserted,
					accountId, accountId, Constants.Elastic.CONTACT_INDEX);
			try {
				elasticSearchService.performBulkRequestWithRefresh(bulkUpsertContacts, OpType.UPDATE,
						RefreshPolicy.IMMEDIATE);
			} catch (Exception e) {
				log.error("Exception while updating conversations active state in bulk", e);
			}
			long elapsedTime = System.currentTimeMillis() - start;
			log.info("Profiled time taken to execute updating conversations active state in bulk: {} milliseconds",
					elapsedTime);
		}
		
	}

	private String getLastMessage(MessageDocument messageDocument) {
		String lastMessage = "";
		List<MediaFile> mediaFiles = messageDocument.getMediaFiles();
		if (CollectionUtils.isNotEmpty(mediaFiles)) {
			if (mediaFiles.size() > 1) {
				if(CommunicationDirection.SEND.equals(messageDocument.getCommunicationDirection())) {
					lastMessage = "Sent attachments";
				}else {
					lastMessage = "Received attachments";
				}
			} else {
				if(CommunicationDirection.SEND.equals(messageDocument.getCommunicationDirection())) {
					lastMessage = "Sent an attachment";
				}else {
					lastMessage = "Received an attachment";
				}
			}
		} else {
			lastMessage = MessengerUtil.decryptMessage(messageDocument);
		}
		return lastMessage;
	}

	private ContactDocument createConversation(MessageMoveRequest request, String msgBody,
			Map<Integer, ContactDocument> conversationMap) {
		CustomerDTO customerDTO = null;
		try {
			customerDTO = contactService.findByIdNoCaching(request.getToCid());
		} catch (Exception e) {
			log.info("Exception from contact-service while fetching data for customerId " + request.getToCid(), e);
			throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
		}
		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(customerDTO.getBusinessId());
		Preconditions.checkArgument(Objects.nonNull(customerDTO) && Objects.nonNull(businessDTO),
				"customerDTO %s and businessDTO %s must not be null", customerDTO, businessDTO);
		MessengerContact messengerContact = messengerContactService.createContact(request.getToCid(),
				customerDTO.getBusinessId());
		messengerContact.setLastMessage(msgBody);
		boolean isEncrpypted = EncryptionUtil.encryptLastMessageWithMcId(messengerContact, 1, msgBody);
		messengerContact.setEncrypted(isEncrpypted ? 1 : 0);
		messengerContactService.saveOrUpdateMessengerContact(messengerContact);
		return messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX,
				null);
	}

	private void updateMessageCid(MessageDocument messageDocument, MessageMoveRequest request, Integer accountId, ContactDocument toConv) {
		messageDocument.setC_id(toConv.getM_c_id().toString());
		 ESRequest.Upsert<MessageDocument> documentForUpsert = new ESRequest.Upsert<>(messageDocument,
                 request.getMessageId());

         ESRequest.Builder esRequestBuilder = new ESRequest.Builder(new ESRequest());
         ESRequest esRequest = esRequestBuilder.addIndex(Constants.Elastic.MESSAGE_INDEX)
                 .addRoutingId(accountId).addTemplateAndDataModel(null, null)
                 .addPayloadForUpsert(documentForUpsert).build();
         elasticSearchService.updateDocument(esRequest, true);
	}
	
	private String getWhatsappConversationId(MessengerContact con) {
		String waConversationId = null;
		if (con != null) {
			waConversationId = con.getWhatsappConversationId();
		}
		return waConversationId;
	}

}
