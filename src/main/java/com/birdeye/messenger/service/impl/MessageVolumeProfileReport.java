package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.MessageVolumeProfileReportDataPoint;
import com.birdeye.messenger.dto.MessageVolumeProfileReportResponse;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ReportDataPoint;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.stereotype.Service;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class MessageVolumeProfileReport implements ReportService {

    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    public MessageVolumeProfileReportResponse getReport(ReportFilter filter) {
        return buildESRequestForReceiveMessages(filter);
    }

    @Override
    public Enum getReportType() {
        return ReportType.MESSAGE_VOLUME_PROFILE;
    }

    private MessageVolumeProfileReportResponse buildESRequestForReceiveMessages(ReportFilter filter) {

        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.MESSAGE_VOLUME_PROFILE,filter.getSources());
        if(CollectionUtils.isEmpty(sourceIds)){
            sourceIds.add(-1);
        }
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        templateData.put("accountTimeZoneId", filter.getAccountTimeZoneId());
        templateData.put("includeExtendedBounds", filter.isIncludeExtendedBounds());
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.MESSAGE_VOLUME_PROFILE_REPORT, data)
                .addSize(0)
                .build();

        SearchResponse searchResult = elasticSearchService.getSearchResult(request);


        if (searchResult.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch Message volume Profile Report");
        } else {
            MessageVolumeProfileReportResponse response = new MessageVolumeProfileReportResponse();
            ParsedStringTerms totalSendReceiveMessageAggregation = searchResult.getAggregations().get("communicationDirection_aggregation");
            if(CollectionUtils.isNotEmpty(totalSendReceiveMessageAggregation.getBuckets())){
                for(int i = 0; i < totalSendReceiveMessageAggregation.getBuckets().size(); i++){
                    if("SEND".equals(totalSendReceiveMessageAggregation.getBuckets().get(i).getKeyAsString())){
                        response.setSentMsgs(totalSendReceiveMessageAggregation.getBuckets().get(i).getDocCount());
                    }else if("RECEIVE".equals(totalSendReceiveMessageAggregation.getBuckets().get(i).getKeyAsString())){
                        response.setReceivedMsgs(totalSendReceiveMessageAggregation.getBuckets().get(i).getDocCount());
                    }
                }
            }
            response.setGroupByType(filter.getInterval());
            Aggregations aggregations = searchResult.getAggregations();
            ParsedLongTerms sourceAggregation = searchResult.getAggregations().get("source_aggregation");
            Map<String,MessageVolumeProfileReportResponse.MessageVolumeProfileReportChannelWise> channelWiseData = new HashMap<>();
            for(String source:filter.getSources()){
                channelWiseData.put(source.toLowerCase(),new MessageVolumeProfileReportResponse.MessageVolumeProfileReportChannelWise());
            }
            if(Objects.nonNull(sourceAggregation) && CollectionUtils.isNotEmpty(sourceAggregation.getBuckets())){
                for(Terms.Bucket bucket: sourceAggregation.getBuckets()){
                    Integer source=Integer.valueOf(bucket.getKeyAsString());
                    ParsedStringTerms sendReceiveSourceAggregation = bucket.getAggregations().get("messagesCount");
                    Long sendMessagesCount = 0L,receiveMessagesCount = 0L;
                    if(CollectionUtils.isNotEmpty(sendReceiveSourceAggregation.getBuckets())){
                        for(int i = 0; i < sendReceiveSourceAggregation.getBuckets().size(); i++){
                            if("SEND".equals(sendReceiveSourceAggregation.getBuckets().get(i).getKeyAsString())){
                                sendMessagesCount = (sendReceiveSourceAggregation.getBuckets().get(i).getDocCount());
                            }else if("RECEIVE".equals(sendReceiveSourceAggregation.getBuckets().get(i).getKeyAsString())){
                                receiveMessagesCount = (sendReceiveSourceAggregation.getBuckets().get(i).getDocCount());
                            }
                        }
                    }
                    channelWiseData.put(Source.getValue(source).name().toLowerCase(),new MessageVolumeProfileReportResponse.MessageVolumeProfileReportChannelWise(sendMessagesCount,receiveMessagesCount));
                }
            }
            response.setChannelWiseData(channelWiseData);
            ParsedDateHistogram dataPointAggregation = aggregations.get("dataPoints");
            List<MessageVolumeProfileReportDataPoint> dataPoints = new ArrayList<>();
            if(Objects.nonNull(dataPointAggregation) && CollectionUtils.isNotEmpty(dataPointAggregation.getBuckets())){
                for(int i=0;i<dataPointAggregation.getBuckets().size();i++){
                    MessageVolumeProfileReportDataPoint messageVolumeProfileReportDataPoint=new MessageVolumeProfileReportDataPoint();
                    DateTimeFormatter formatter=DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    String dateKeyAsString=dataPointAggregation.getBuckets().get(i).getKeyAsString();
                    String[] parts=dateKeyAsString.split("(?=T)");
                    dateKeyAsString=parts[0];
                    LocalDate date=LocalDate.parse(dateKeyAsString);
                    String label=date.format(formatter);
                    messageVolumeProfileReportDataPoint.setLabel(label);
                    ParsedStringTerms sendReceiveDataPointAggregation = dataPointAggregation.getBuckets().get(i).getAggregations().get("messagesCount");
                    Long sendMessagesCount = 0L,receiveMessagesCount = 0L;
                    if(CollectionUtils.isNotEmpty(sendReceiveDataPointAggregation.getBuckets())){
                        for(int j = 0; j < sendReceiveDataPointAggregation.getBuckets().size(); j++){
                            if("SEND".equals(sendReceiveDataPointAggregation.getBuckets().get(j).getKeyAsString())){
                                sendMessagesCount = (sendReceiveDataPointAggregation.getBuckets().get(j).getDocCount());
                            }else if("RECEIVE".equals(sendReceiveDataPointAggregation.getBuckets().get(j).getKeyAsString())){
                                receiveMessagesCount = (sendReceiveDataPointAggregation.getBuckets().get(j).getDocCount());
                            }
                        }
                    }
                    messageVolumeProfileReportDataPoint.setSentMsgs(sendMessagesCount);
                    messageVolumeProfileReportDataPoint.setReceivedMsgs(receiveMessagesCount);
                    dataPoints.add(messageVolumeProfileReportDataPoint);
                }
            }
            response.setDataPoints(dataPoints);
            return response;
        }
    }


    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filters.getEndDateInMillis() - filters.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        if(CollectionUtils.isEmpty(filters.getSources())){
            List<String> supportedSource = new ArrayList<>();
            supportedSource.add("facebook");
            supportedSource.add("instagram");
            supportedSource.add("twitter");
            filters.setSources(supportedSource);
        }
        MessageVolumeProfileReportResponse messageVolumeProfileReportResponse =  getReport(filters);
        if(CollectionUtils.isNotEmpty(messageVolumeProfileReportResponse.getDataPoints())){
            customizeLabels(messageVolumeProfileReportResponse.getDataPoints(),startDate,endDate,groupByType,false);
        }
        messageVolumeProfileReportResponse.setDateDiff(days);
        return  messageVolumeProfileReportResponse;
    }

    private  void customizeLabels(List<? extends ReportDataPoint> dataPoints,Date startDate,Date endDate,
                                       GroupByType groupBy,boolean convertTimeZone) throws Exception {
        // customize labels
        // prepare hyphen separated date columns for week and quarter types only
        // set bucket start date and end date for all types
        boolean prepareRangeColumns = false;
        if (CollectionUtils.isNotEmpty(dataPoints) && GroupByType.WEEK.equals(groupBy)
                || GroupByType.QUARTER.equals(groupBy)) {
            prepareRangeColumns = true;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(groupBy.getLabelFormat());
        SimpleDateFormat weekStartSdf = new SimpleDateFormat(Constants.FORMAT_MMM_DD);
        SimpleDateFormat weekEndSdf = new SimpleDateFormat(Constants.FORMAT_MMM_DD_YYYY);
        SimpleDateFormat inputSdf = new SimpleDateFormat(Constants.FORMAT_MM_DD_YYYY);

        String firstLabel = "";
        String lastLabel = "";
        String lastLabelWeek = "";
        if (convertTimeZone) {
            firstLabel = sdf.format(DateUtils.convertToPacificTimeZone(startDate).getTime());
            lastLabel = sdf.format(DateUtils.convertToPacificTimeZone(endDate).getTime());
            lastLabelWeek = weekEndSdf.format(DateUtils.convertToPacificTimeZone(endDate).getTime());
        } else {
            firstLabel = dataPoints.get(0).getLabel();
            lastLabel = sdf.format(endDate.getTime());
            lastLabelWeek = weekEndSdf.format(endDate.getTime());
        }

        int len = dataPoints.size();
        if (len > 0) {
            dataPoints.get(0).setLabel(firstLabel);
            Calendar cal = Calendar.getInstance();
            if (convertTimeZone) {
                cal.setTime(DateUtils.convertToPacificTimeZone(startDate).getTime());
            } else {
                cal.setTime(inputSdf.parse(firstLabel));
            }
            for (int index = 0; index < len - 1; index++) {
                ReportDataPoint dataPoint = dataPoints.get(index);
                String prefix = sdf.format(cal.getTime());
                String weekPrefix = weekStartSdf.format(cal.getTime());
                dataPoint.setStartDate(inputSdf.format(cal.getTime()));
                Date nxtDate = inputSdf.parse((String) dataPoints.get(index + 1).getLabel());
                cal.setTime(nxtDate);
                cal.add(Calendar.DATE, -1);
                String suffix = sdf.format(cal.getTime());
                String weekSuffix = weekEndSdf.format(cal.getTime());
                dataPoint.setEndDate(inputSdf.format(cal.getTime()));
                String lbl = prefix + Constants.GRAPH_TABLE_LABEL_SEPARATOR + suffix;
                if (prepareRangeColumns) {
                    dataPoint.setLabel(lbl);
                    if (GroupByType.QUARTER.equals(groupBy)) {
                        int quarter = DateUtils.getQuarter(cal);
                        dataPoint.setShortLabel("Q" + quarter + " " + cal.get(Calendar.YEAR));
                    } else {
                        dataPoint.setShortLabel(weekPrefix + "-" + weekSuffix);
                    }
                } else {
                    dataPoint.setLabel(prefix);
                    if (GroupByType.DAY.equals(groupBy)) {
                        dataPoint.setShortLabel(weekSuffix);
                    } else {
                        dataPoint.setShortLabel(prefix);
                    }
                }
                cal.add(Calendar.DATE, 1);
            }
            String finalLabel = sdf.format(cal.getTime()) + Constants.GRAPH_TABLE_LABEL_SEPARATOR + lastLabel;
            ReportDataPoint reportDataPoints = dataPoints.get(len - 1);
            reportDataPoints.setStartDate(inputSdf.format(cal.getTime()));
            reportDataPoints.setEndDate(inputSdf.format(DateUtils.convertToPacificTimeZone(endDate).getTime()));
            if (prepareRangeColumns) {
                reportDataPoints.setLabel(finalLabel);
                if (GroupByType.QUARTER.equals(groupBy)) {
                    int quarter = DateUtils.getQuarter(cal);
                    reportDataPoints.setShortLabel("Q" + quarter + " " + cal.get(Calendar.YEAR));
                } else {
                    reportDataPoints.setShortLabel(weekStartSdf.format(cal.getTime()) + "-" + lastLabelWeek);
                }
            } else {
                reportDataPoints.setLabel(sdf.format(cal.getTime()));
                if (GroupByType.DAY.equals(groupBy)) {
                    reportDataPoints.setShortLabel(weekEndSdf.format(cal.getTime()));
                } else {
                    reportDataPoints.setShortLabel(sdf.format(cal.getTime()));
                }
            }
        }
    }
    
}
