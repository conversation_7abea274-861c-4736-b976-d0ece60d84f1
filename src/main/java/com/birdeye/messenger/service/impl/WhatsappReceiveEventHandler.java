package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dao.entity.WhatsappMessage;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.Base64File;
import com.birdeye.messenger.dto.Base64FileMessage;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.whatsapp.Contact;
import com.birdeye.messenger.dto.whatsapp.Message;
import com.birdeye.messenger.dto.whatsapp.Message.Reaction;
import com.birdeye.messenger.dto.whatsapp.Text;
import com.birdeye.messenger.dto.whatsapp.Value;
import com.birdeye.messenger.dto.whatsapp.WAMediaResponse;
import com.birdeye.messenger.dto.whatsapp.WhatsappMessageRequest;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.enums.WhatsappMessageStatusEnum;
import com.birdeye.messenger.enums.WhatsappMsgType;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.NLPService;
import com.birdeye.messenger.external.service.PlatformService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.external.service.SpamDetectionService;
import com.birdeye.messenger.service.CallbackService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.IWhatsAppTemplateService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.WhatsappMessageService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WhatsappReceiveEventHandler extends MessageEventHandlerAbstract {

	private final MessengerEvent EVENT = MessengerEvent.WHATSAPP_RECEIVE;
	
	private final NLPService nlpService;

	private final MessengerContactRepository messengerContactRepository;

	private final RedisLockService redisLockService;

	private final BusinessService businessService;

	private final PulseSurveyService pulseSurveyService;

	private final WhatsappMessageService whatsappMessageService;

	private final MessengerMessageService messengerMessageService;

	private final IWhatsAppTemplateService whatsAppTemplateService;
	
	private final SpamDetectionService spamDetectionService;
	
	private final SocialService socialService;
	
	private final PlatformService platformService;
	
	private final MessengerMediaFileService messengerMediaFileService;
	
	private final CallbackService callbackService;
	
	private final ConversationActivityService conversationActivityService;
	
	private final ElasticSearchExternalService elasticSearchService;

	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	public MessageResponse handle(MessageDTO messageDTO) throws Exception {
		messageDTO.setMsgTypeForResTimeCalc("R"); // default - since its a receive handler
		messageDTO.setSource(Source.WHATSAPP.getSourceId());
		WhatsappMessageRequest whatsappMessageRequest = (WhatsappMessageRequest) messageDTO;
		log.info("WA receive request: {} ", whatsappMessageRequest);
		validateWaEventType(whatsappMessageRequest);
		//Update Message status
		if (!Objects.isNull(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getStatuses())) {
			log.info("WA - msg status received");
			callbackService.updateWAMessageDeliveryStatus(whatsappMessageRequest);
			return null;
		}
		//Update Message reaction
		if (!Objects.isNull(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getReaction())) {
			log.info("WA - msg reaction received");
			updateWAMessageReactionFromUser(whatsappMessageRequest);
			return null;
		}
		Optional<Lock> lockOpt = Optional.empty();
		try {
			Contact contact = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getContacts().get(0);
			Message waMessage = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0);

			String customerWAId = contact.getWa_id(); //customer wa id
			String customerWAPhoneNumber = waMessage.getFrom();
			messageDTO.setCustomerWAPhoneNumber(customerWAPhoneNumber);
			
			String lockKey = Constants.WHATSAPP_USER_ID_PREFIX + customerWAId;
			lockOpt = redisLockService.tryLock(lockKey, 200, TimeUnit.MILLISECONDS);
			if (!lockOpt.isPresent()) {
				log.info("Lock is already acquired for the key:{}", lockKey);
				kafkaService.publishToKafkaAsync(KafkaTopicEnum.WHATSAPP_RECEIVE_EVENT_DELAYED_QUEUE,
						whatsappMessageRequest);
				return null;
			}
			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			if (Objects.isNull(businessDTO) || !BusinessDTO.isActive(businessDTO)) {
				log.error("BusinessDTO returned null by core-service for request {} ", whatsappMessageRequest.getEntry().get(0).getId());
				return null;
			}
			Integer isMessengerEnabled = businessService.isMessengerEnabled(messageDTO.getBusinessDTO().getAccountId());
			if (!Integer.valueOf(0).equals(isMessengerEnabled)) {
				CustomerDTO customerDTO = getCustomerDTO(messageDTO);
				if (customerDTO.getCommPreferences()!=null) {
					customerDTO.getCommPreferences().setIsPhoneAndWhatsAppNumberSame(customerDTO.getIsPhoneAndWhatsAppNumberSame());
				}
				updateCustomerSubscribeUnsubscribeStatus(whatsappMessageRequest);
				getMessengerContact(messageDTO);
				processWAReceivedMessage(messageDTO);

//				if (whatsappMessageRequest.getConversationDTO() == null
//						|| whatsappMessageRequest.getConversationDTO().getId() == null) {
//					log.error("Error in saving WA msg for request {} ", whatsappMessageRequest);
//					return null;
//				}
				// handle PulseSurveyContext
				PulseSurveyContext context = null;
				try {
					context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO,
							messageDTO.getBusinessDTO());
					if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())) {
						customerDTO.setOngoingPulseSurvey(
								PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
					} else {
						customerDTO.setOngoingPulseSurvey(false);
					}
					messageDTO.setCustomerDTO(customerDTO);
				} catch (Exception ex) {
					log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
				}
				super.handle(messageDTO);
			} else {
				log.info("WAMsg Discarded. Inbox not enabled for business {}", messageDTO.getBusinessDTO().getBusinessId());
			}
		} finally {
			if (lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
		return null;
	}

	private void validateWaEventType(WhatsappMessageRequest whatsappMessageRequest) {
		if (Objects.isNull(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getContacts())) {
			log.info("whatsapp - contact cant be null");
			return;
		}
		if (Objects.isNull(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages())) {
			log.info("whatsapp - message cant be null");
			return;
		}

		if (Objects.nonNull(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getText()) && nlpService.isTextProfane(whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getText().getBody())){
			log.info("WAReceiveHandler: Profane msg detected {}", whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getText().getBody());
			throw new BadRequestException("Message not saved as it qualified as profane");
		}
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("RECEIVE");
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.WHATSAPP.name());
		lastMessageMetadataPOJO.setLastReceivedMessageSource(Source.WHATSAPP.getSourceId());
		lastMessageMetadataPOJO.setLastMessageSource(Source.WHATSAPP.getSourceId());

		WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
		Message message = dto.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0);
		Date lastMsgOn = new Date(message.getTimestamp()*1000);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String date = sdf.format(lastMsgOn);
		lastMessageMetadataPOJO.setLastWAReceivedAt(date);

		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		messengerContact.setLastMsgOn(lastMsgOn);
		messengerContact.setUpdatedAt(lastMsgOn);
		messengerContact.setLastIncomingMessageTime(lastMsgOn.getTime());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
		ConversationDTO conversationDTO = dto.getConversationDTO();
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
			if (messageDTO.getMessengerMediaFiles().size() > 1) {
				messengerContact.setLastMessage("Received attachments");
			} else {
				messengerContact.setLastMessage("Received an attachment");
			}
		}
		if (MessageType.WA_CONTACT.equals(conversationDTO.getMessageType())) {
			messengerContact.setLastMessage("Received contact information");
		}
		if (MessageType.WA_LOCATION.equals(conversationDTO.getMessageType())) {
			messengerContact.setLastMessage("Received location information");
		}
		boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact,
				dto.getConversationDTO().getEncrypted(), messengerContact.getWhatsappConversationId(),
				businessDTO.getBusinessNumber());
		messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		if (Objects.isNull(messageDTO.getMessengerContact())) {
			WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
			Contact contact = dto.getEntry().get(0).getChanges().get(0).getValue().getContacts().get(0);
			Message message = dto.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0);

			String customerWAId = contact.getWa_id(); //customer wa id
			String customerWAPhoneNumber = message.getFrom();

			BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactRepository.findByWhatsappConversationId(customerWAId, businessDTO.getBusinessId());

			if (messengerContact == null) {
				String custName = contact.getProfile().getName();
				KontactoRequest kontactoRequest = createKontactoRequest(custName, customerWAPhoneNumber, businessDTO.getCountryCode());
				kontactoRequest.setBusinessId(businessDTO.getBusinessId());
				log.info("Kontacto - get/create customer - customerWAId {}", customerWAId);
				CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest,
						businessDTO.getRoutingId(), -8);
				log.info("Customer Id {} from Kontacto for customerWAId {}", customerDTO.getId(), customerWAId);

				customerDTO.setCustomerWAPhoneNumber(customerWAPhoneNumber);
				messageDTO.setCustomerDTO(customerDTO);
				messengerContact = createMessengerContactFromWhatsapp(customerWAId, messageDTO);
			}
		}
		messageDTO.setMessengerContact(messengerContact);

		return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		MessengerGlobalFilter notificationRequest = new MessengerGlobalFilter();
		notificationRequest.setBizId(businessDTO.getBusinessId());
		notificationRequest.setBusinessName(businessDTO.getBusinessName());
		notificationRequest.setBusinessAlias(businessDTO.getBusinessAlias());
		notificationRequest.setEnterpriseName(businessDTO.getEnterpriseName());
		notificationRequest.setBusinessNumber(businessDTO.getBusinessNumber());
		notificationRequest.setBirdEyeEmailId(businessDTO.getBirdEyeEmailId());
		notificationRequest.setEnterpriseId(businessDTO.getEnterpriseId());
		notificationRequest.setTimeZone(businessDTO.getTimeZoneId());
		notificationRequest.setNotificationType(MessengerGlobalFilter.NotificationType.CHAT);
		notificationRequest.setMsgId(dto.getConversationDTO().getId());
		notificationRequest.setProductName(businessDTO.getProductName());
		notificationRequest.setCount(10); // number of messages to be fetched from ES
		// The creation time is the last received time if last delivery time is null.
		if (dto.getConversationDTO().getCreateDate() != null) {
			notificationRequest.setLastMsgTime(dto.getConversationDTO().getCreateDate().getTime());
		} else {
			notificationRequest.setLastMsgTime(new Date().getTime());
			log.info("onReceiveSMS: Both sms sentOn and createDate found null for businessId {} smsID {} customer {}",
					businessDTO.getBusinessId(), dto.getConversationDTO().getId(), dto.getCustomerDTO().getPhone());
		}
		notificationRequest.setConversationId(getMessengerContact(messageDTO).getId());
		return notificationRequest;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
		if (Objects.isNull(messageId)) {
			WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
			messageId = dto.getConversationDTO().getId();
			messageDTO.setMessageId(messageId);
		}
		return messageId;
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {

	}

	@Override
	CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		if (Objects.isNull(customerDTO)) {
			MessengerContact contact = getMessengerContact(messageDTO);
			if (contact.getCustomerId() != null && Objects.isNull(messageDTO.getCustomerDTO())) {
				customerDTO = contactService.findByIdNoCaching(contact.getCustomerId());
				messageDTO.setCustomerDTO(customerDTO);
			}
			if (Objects.isNull(customerDTO)) {
				customerDTO = messageDTO.getCustomerDTO();
			}
			MessageTag messageTag = getMessageTag(messageDTO);
			spamDetectionService.spamDetectionAllChannels(messageDTO, contact, customerDTO, messageTag);
		}
		return customerDTO;
	}

	private KontactoRequest createKontactoRequest(String custName, String customerWAPhoneNumber, String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		custName = ControllerUtil.truncateLongContactName(custName);
		kontactoRequest.setName(custName);
		kontactoRequest.setPhone(customerWAPhoneNumber);
		kontactoRequest.setSource(KontactoRequest.WHATSAPP);
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	private MessengerContact createMessengerContactFromWhatsapp(String customerWAId, MessageDTO messageDTO) {
		MessengerContact messengerContact = messengerContactRepository.findByWhatsappConversationId(customerWAId,
				messageDTO.getBusinessDTO().getBusinessId());
		if (Objects.isNull(messengerContact)) {
			messengerContact = new MessengerContact();
			messengerContact.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
			messengerContact.setWhatsappConversationId(customerWAId);
			messengerContact.setCustomerId(messageDTO.getCustomerDTO().getId());
			messengerContact.setCreatedAt(new Date());
			messengerContactRepository.saveAndFlush(messengerContact);
		}
		return messengerContact;
	}

	@Override
	BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		if (Objects.isNull(businessDTO)) {
			WhatsappMessageRequest whatsappMessageRequest = (WhatsappMessageRequest) messageDTO;
			Value value = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue();
			String businessWAPhoneNumberId = value.getMetadata().getPhone_number_id();

			
			Integer businessId = whatsAppTemplateService.getWABusinessId(businessWAPhoneNumberId);
			if (businessId != null) {
				businessDTO = communicationHelperService.getBusinessDTO(businessId);
			} else {
				businessDTO = null;
				log.warn("No valid business found for WA : {} ", businessWAPhoneNumberId);
			}
			whatsappMessageRequest.setBusinessDTO(businessDTO);
		}
		return businessDTO;
	}

	private void processWAReceivedMessage(MessageDTO messageDTO) {
		WhatsappMessageRequest whatsappMessageRequest = (WhatsappMessageRequest) messageDTO;
		Value value = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue();
		String businessWAPhoneNumberId = value.getMetadata().getPhone_number_id();

		Contact contact = value.getContacts().get(0);
		String customerWAId = contact.getWa_id(); //customer wa id

		Message waMessage = value.getMessages().get(0);
		String body = null;
		if (Objects.nonNull(waMessage.getText())) {
			body = waMessage.getText().getBody();
		}
		if (WhatsappMsgType.button.name().equalsIgnoreCase(waMessage.getType())) {
			body = waMessage.getButton().getText();
			Text text = new Text();
			text.setBody(body);
			waMessage.setText(text);
		}
		if (WhatsappMsgType.contacts.name().equalsIgnoreCase(waMessage.getType())) {
			body = JSONUtils.toJSON(waMessage.getContacts().get(0));
			Text text = new Text();
			text.setBody(body);
			waMessage.setText(text);
		}

		if (WhatsappMsgType.location.name().equalsIgnoreCase(waMessage.getType())) {
			body = JSONUtils.toJSON(waMessage.getLocation());
			Text text = new Text();
			text.setBody(body);
			waMessage.setText(text);
		}

		ConversationDTO conversationDTO=new ConversationDTO();
		Optional<ActivityType> translateToActivity = translateToActivity(body);

		if (translateToActivity.isPresent()) {
			messageDTO.setMsgTypeForResTimeCalc("");
			whatsappMessageRequest.setSendEmailNotification(false);
			ActivityDto activityDTO = ActivityDto.builder().activityType(translateToActivity.get())
					.created(new Date()).mcId(getMessengerContact(whatsappMessageRequest).getId()).build();
			ConversationActivity activity = conversationActivityService.create(activityDTO);
			activityDTO.setId(activity.getId());
			whatsappMessageRequest.setActivityDto(activityDTO);
			UserDTO userDTO = new UserDTO();
			userDTO.setId(-7);
			whatsappMessageRequest.setUserDTO(userDTO);
			whatsappMessageRequest.setUpdateLastMessage(false);
			messengerMessageService.saveMessengerActivity(activity, activityDTO.getMcId(), userDTO);			
		} else {

			WhatsappMessage waMessageDto = whatsappMessageService.saveWhatsappMessage(messageDTO, customerWAId,
					businessWAPhoneNumberId, waMessage.getId(), WhatsappMessageStatusEnum.received);

			if (waMessageDto.getId() != null) {
				// Event with attachment
				saveMediaFiles(messageDTO, waMessage, waMessageDto.getId(), businessWAPhoneNumberId);
				MessengerMessageMetaData messageMetaData = new MessengerMessageMetaData();
				messageMetaData.setCommunicationDirection(CommunicationDirection.RECEIVE);
				conversationDTO = new ConversationDTO(waMessageDto, messageMetaData);
				whatsappMessageRequest.setConversationDTO(conversationDTO);
				if (WhatsappMsgType.contacts.name().equalsIgnoreCase(waMessage.getType())) {
					conversationDTO.setMessageType(MessageType.WA_CONTACT);
				}

				if (WhatsappMsgType.location.name().equalsIgnoreCase(waMessage.getType())) {
					conversationDTO.setMessageType(MessageType.WA_LOCATION);
				}
				messengerMessageService.saveMessengerMessage(conversationDTO, null);
				messageDTO.setSendEmailNotification(true);
			}
		}
	}

	//BIRD-130434 : Get Media from Social
	//Upload media to birdeye cdn
	//Use birdeye url
	private void saveMediaFiles(MessageDTO messageDTO, Message waMessage, Integer wamId, String businessWAPhoneNumberId) {
		String mediaId = null;
		if (WhatsappMsgType.image.name().equalsIgnoreCase(waMessage.getType())) {
			mediaId = waMessage.getImage().getId();
		}
		if (WhatsappMsgType.video.name().equalsIgnoreCase(waMessage.getType())) {
			mediaId = waMessage.getVideo().getId();
		}
		if (WhatsappMsgType.document.name().equalsIgnoreCase(waMessage.getType())) {
			mediaId = waMessage.getDocument().getId();
		}
		if (WhatsappMsgType.audio.name().equalsIgnoreCase(waMessage.getType())) {
			mediaId = waMessage.getAudio().getId();
		}
		if (WhatsappMsgType.sticker.name().equalsIgnoreCase(waMessage.getType())) {
			mediaId = waMessage.getSticker().getId();
		}


		if (mediaId == null) {
			//no media received
			return;
		}
		
		WAMediaResponse mediaResponse = socialService.getWAMediaById(mediaId, businessWAPhoneNumberId);
		if (mediaResponse == null) {
			return;
		}
		Base64File fileContent = new Base64File();
		fileContent.setData(mediaResponse.getMediaData());
		fileContent.setFileType(mediaResponse.getMimeType());
		String ex = MessengerUtil.getExtensionFromMimeType(mediaResponse.getMimeType());
		fileContent.setFileName(mediaId+"."+ex);

		Base64FileMessage fileMsg = new Base64FileMessage();
		fileMsg.setMultipleFiles(Collections.singletonList(fileContent));

		BusinessDTO businessDTO = messageDTO.getBusinessDTO();

		String url = platformService.uploadBase64FileForBusiness(businessDTO.getBusinessId(), true, fileMsg);

		List<MessengerMediaFile> messengerMediaFiles = new ArrayList<MessengerMediaFile>();
		Map<String, String> urlExtensionMap = new HashMap<String, String>();
		if (StringUtils.isNotEmpty(url)) {
			MessengerMediaFile mediaFile =  new MessengerMediaFile(url);
			mediaFile.setMessageId(wamId);
			messengerMediaFiles.add(mediaFile);
			urlExtensionMap.put(mediaFile.getUrl(), mediaFile.getExtension());

			if (CollectionUtils.isNotEmpty(messengerMediaFiles)) {
				messengerMediaFileService.saveAll(messengerMediaFiles);
				messengerMediaFiles.forEach(media -> {
					media.setExtension(urlExtensionMap.get(media.getUrl()));
				});
				messageDTO.setMessengerMediaFiles(messengerMediaFiles);
			}
		}
	}

	@Override
	MessageTag getMessageTag(MessageDTO messageDTO) {
		if (getSendEmailNotification(messageDTO)) {
			return MessageTag.UNREAD;
		} else return MessageTag.INBOX;
	}

	@Override
	MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
		WhatsappMessageRequest dto = (WhatsappMessageRequest) messageDTO;
		if(dto.getActivityDto()!=null) {
			return new MessageDocumentDTO(dto.getActivityDto());
		}
		MessageDocumentDTO messageDocumentDTO = new MessageDocumentDTO(dto.getConversationDTO(),
				getMessengerContact(messageDTO).getId());
		if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
			messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
					.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
							messengerMediaFile.getExtension()))
					.collect(
							Collectors.toList()));
		}
		messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
		messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
		messageDTO.setMessageDocumentDTO(messageDocumentDTO);
		MessengerContact messengerContact = getMessengerContact(messageDTO);
		if (messengerContact.getSpam() != null && messengerContact.getSpam().equals(true)) {
			messageDocumentDTO.setSpam(true);
		} else {
			messageDocumentDTO.setSpam(false);
		}
		return messageDocumentDTO;
	}
	
	private void updateCustomerSubscribeUnsubscribeStatus(WhatsappMessageRequest waMsgDto){
		try{
			String body = null;
			if (Objects.nonNull(waMsgDto.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getText())) {
				body = waMsgDto.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getText().getBody();
			}
			if (Objects.nonNull(waMsgDto.getCustomerDTO()) && Objects.nonNull(waMsgDto.getCustomerDTO().getId()) && StringUtils.isNotBlank(body)) {
				if (waMsgDto.getCustomerDTO().getCommPreferences() != null) {
					if (MessengerUtil.isStopWord(body.trim())
							&& BooleanUtils.isTrue(waMsgDto.getCustomerDTO().getCommPreferences().getWhatsappOptin())) {
						contactService.updateCustomerSubscriptionStatus(waMsgDto.getCustomerDTO().getId(),
								waMsgDto.getBusinessDTO().getAccountId(), null, null, null, true);
						waMsgDto.getCustomerDTO().getCommPreferences().setWhatsappOptin(false);
						waMsgDto.getCustomerDTO().getCommPreferences().setWhatsappUnsubType("Opted out");
					} else if (!MessengerUtil.isStopWord(body.trim()) && !BooleanUtils
							.isTrue(waMsgDto.getCustomerDTO().getCommPreferences().getWhatsappOptin())) {
						contactService.updateCustomerSubscriptionStatus(waMsgDto.getCustomerDTO().getId(),
								waMsgDto.getBusinessDTO().getAccountId(), null, null, null, false);
						waMsgDto.getCustomerDTO().getCommPreferences().setWhatsappOptin(true);
						waMsgDto.getCustomerDTO().getCommPreferences().setWhatsappUnsubType("");
					}
				}
			}
		} catch(Exception e){
			log.info("customer subscribe/unsubsrcibe status failed for customerID: {}",waMsgDto.getCustomerDTO().getId());
		}
	}
	
	private Optional<ActivityType> translateToActivity(String body) {
		if (StringUtils.isNotBlank(body)) {
			if (MessengerUtil.isStopWord(body.trim())) {
				return Optional.of(ActivityType.WHATSAPP_UNSUBSCRIBE);
			}
			if (StringUtils.equalsIgnoreCase(body.trim(), "START")) {
				return Optional.of(ActivityType.WHATSAPP_SUBSCRIBE);
			}
		}
		return Optional.empty();
	}
	
	private void updateWAMessageReactionFromUser(WhatsappMessageRequest whatsappMessageRequest) {
		Reaction waReaction = whatsappMessageRequest.getEntry().get(0).getChanges().get(0).getValue().getMessages().get(0).getReaction();

		WhatsappMessage waMsg = whatsappMessageService.findWAMessageById(waReaction.getMessage_id());

		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(waMsg.getBusinessId());
		MessengerContact messengerContact = messengerContactService.findById(waMsg.getMessengerContactId());
		if (messengerContact == null) {
			log.info("Messenger contact not present {}", waMsg.getMessengerContactId());
			return;
		}

		String id = String.valueOf(waMsg.getId() + "_wa");
		Integer routingId = businessDTO.getRoutingId();

		ESFindByIdRequest<MessageDocument> esFindByIdRequest = ESFindByIdRequest.<MessageDocument>builder()
				.id(id).documentType(MessageDocument.class)
				.index(Constants.Elastic.MESSAGE_INDEX).routingId(String.valueOf(routingId)).build();
		MessageDocument messageDocument = elasticSearchService.findDocumentById(esFindByIdRequest);
		messageDocument.setLiked(true);
		messageDocument.setType(MessageDocument.Type.UPDATE.getType());
		ESRequest.Upsert<MessageDocument> upsert = new ESRequest.Upsert<>(messageDocument, id);
		ESRequest esRequest = new ESRequest.Builder(new ESRequest())
				.addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(businessDTO.getRoutingId())
				.addPayloadForUpsert(upsert).build();
		boolean updated = elasticSearchService.updateDocument(esRequest, true);
		log.info("Like updated for MsgId {} : {}", waReaction.getMessage_id(), updated);

	}
}
