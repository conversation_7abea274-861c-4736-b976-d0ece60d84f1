/**
 * 
 */
package com.birdeye.messenger.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import jakarta.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dao.repository.EmailRepository;
import com.birdeye.messenger.dto.EmailMessageDTO;
import com.birdeye.messenger.dto.EmailMessageDocument;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.EmailService;
import com.birdeye.messenger.util.DtoToEntityConverter;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailServiceImpl implements EmailService {

	@Autowired
	private EmailRepository emailRepository;

	@Override
	public Email saveEmail(SendMessageDTO sendMessageDTO, String sender, String recipient) {
		Email email = DtoToEntityConverter.toEmail(sendMessageDTO);		
		encrypt(email, sender, recipient);
		emailRepository.saveAndFlush(email);
		return email;
	}

	@Override 
	public Email saveEmail(EmailMessageDTO emailMessageDTO, String sender, String recipient) {
		Email email = DtoToEntityConverter.toEmail(emailMessageDTO);
		encrypt(email, sender, recipient);
		emailRepository.saveAndFlush(email);
		return email;
	}

	@Override
	public List<Email> findByCustomerId(Integer customerId) {
		return emailRepository.findByCustomerId(customerId);
	}

	@Override
	public void saveEmail(Email email) {
		emailRepository.saveAndFlush(email);

	}

	@Override
	public Email findById(Integer id) {
		return emailRepository.findById(id).orElseThrow(() -> new NotFoundException(ErrorCode.NO_EMAIL_FOUND));
	}

	private void encrypt(Email email, String sender, String recipient) {
		try {
			if (StringUtils.isNotEmpty(email.getMessageBody())) {
				email.setMessageBody(EncryptionUtil.encrypt(email.getMessageBody(),
						StringUtils.join(sender, recipient), StringUtils.join(recipient, sender)));
				email.setEncrypted(1);
			}
		} catch (Exception e) {
			log.error("Encryption failed for email body: {} with business {}", email.getMessageBody(),
					email.getBusinessId());
			email.setEncrypted(0);
			email.setMessageBody(email.getMessageBody());
		}
	}

	@Override
	public Email saveCampaignEmail(SendMessageDTO sendMessageDTO, String sender, String recipient) {
		Email email = DtoToEntityConverter.campaignEmail(sendMessageDTO);
		encrypt(email, sender, recipient);
		emailRepository.saveAndFlush(email);
		return email;
	}

	@Override
	public Email findByReviewRequestId(Long reviewRequestId) {
		Email email = emailRepository.findByReviewRequestId(reviewRequestId);
		return email;
	}

    @Override
    @Transactional
    public void deleteEmailByCustomerId(Integer customerId) {
        try {
            emailRepository.deleteByCustomerId(customerId);
        } catch (Exception e) {
            log.error("error : {} occurred in deleteEmailByCustomerId", e.getMessage());
        }
    }

    @Override
    public List<EmailMessageDocument> prepareFormattedEmailMessages(List<MessageDocument> messageDocuments, ContactDocument contactDocument, String timeZone) {
        List<EmailMessageDocument> emailMessageDocuments = new ArrayList<>();
        for (MessageDocument messageDocument : messageDocuments) {
            EmailMessageDocument emailMessageDocument = new EmailMessageDocument();
            emailMessageDocument.setSentON(messageDocument.getCr_date());
            emailMessageDocument.setCreatedDate(MessengerUtil.formatMessageCreatedTime(messageDocument.getCr_date(), timeZone));
            emailMessageDocument.setMsgBody(MessengerUtil.decryptMessage(messageDocument));

            // handled for email notifications formating
            if (StringUtils.isNotBlank(emailMessageDocument.getMsgBody())) {
                emailMessageDocument.setMsgBody(StringUtils.replace(emailMessageDocument.getMsgBody(), "\n", "<br />"));
            }
            //Voicemail Message changes
            if (StringUtils.isNotBlank(messageDocument.getVoiceMailUrl())) {
                if (emailMessageDocument.getMsgBody() == null) {
                    emailMessageDocument.setMsgBody("Voicemail transcription is not available");
                }
                MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile("mp3", messageDocument.getVoiceMailUrl(), "-1", "voicemail", "audio/mpeg");
                List<MessageDocument.MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                emailMessageDocument.setMediaFiles(mediaFiles);
            }
            if (Objects.isNull(messageDocument.getMessageType())) {
                emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(messageDocument.getU_id(), messageDocument.getU_name()));
                if (StringUtils.isNotBlank(messageDocument.getA_url())) {
                    MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile(messageDocument.getA_ext(), messageDocument.getA_url(), messageDocument.getA_size(), messageDocument.getA_name(), messageDocument.getA_contype());
                    List<MessageDocument.MediaFile> mediaFiles = Collections.singletonList(mediaFile);
                    mediaFiles = NotificationServiceImpl.truncateAttachmentName(mediaFiles);
                    emailMessageDocument.setMediaFiles(mediaFiles);
                }
                if (("SMS_RECEIVE".equals(messageDocument.getMsg_type())
                        || "MMS_RECEIVE".equals(messageDocument.getMsg_type()))) {
                    emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                    emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                    emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.RECEIVE);
                }
                if ("SMS_SEND".equals(messageDocument.getMsg_type())
                        || "MMS_SEND".equals(messageDocument.getMsg_type())) {
                    emailMessageDocument.setMessageType(MessageDocument.MessageType.CHAT);
                    emailMessageDocument.setCommunicationDirection(MessageDocument.CommunicationDirection.SEND);
                }
            } else {
                emailMessageDocument.setMessageType(messageDocument.getMessageType());
                emailMessageDocument.setCommunicationDirection(messageDocument.getCommunicationDirection());
                if (CollectionUtils.isNotEmpty(messageDocument.getMediaFiles())) {
                    List<MessageDocument.MediaFile> mediaFiles = NotificationServiceImpl.truncateAttachmentName(messageDocument.getMediaFiles());
                    emailMessageDocument.setMediaFiles(mediaFiles);
                }
                emailMessageDocument.setCreatedBy(messageDocument.getCreatedBy());
                if (MessageDocument.MessageType.CHAT.equals(messageDocument.getMessageType())
                        && MessageDocument.CommunicationDirection.RECEIVE
                        .equals(messageDocument.getCommunicationDirection())) {
                    emailMessageDocument.setCreatedBy(new MessageDocument.UserDetail(0, contactDocument.getC_name()));
                }
            }
            emailMessageDocuments.add(emailMessageDocument);
            // Create separate message which contains both, Media Files & Message Body.
            // JIRA : BIRDEYE-77920
            if (StringUtils.isNotBlank(emailMessageDocument.getMsgBody()) && CollectionUtils.isNotEmpty(emailMessageDocument.getMediaFiles())){
                EmailMessageDocument separatedOne = new EmailMessageDocument();

                separatedOne.setMessageType(emailMessageDocument.getMessageType());
                separatedOne.setCreatedBy(emailMessageDocument.getCreatedBy());
                separatedOne.setMsgBody(emailMessageDocument.getMsgBody());
                separatedOne.setCommunicationDirection(emailMessageDocument.getCommunicationDirection());
                separatedOne.setSentON(emailMessageDocument.getSentON());
                separatedOne.setCreatedDate(emailMessageDocument.getCreatedDate());
                emailMessageDocument.setMsgBody(null);

                emailMessageDocuments.add(separatedOne);
            }
        }
        Collections.sort(emailMessageDocuments);
        return emailMessageDocuments;
    }

    @Override
    @Transactional
    public void updateEmailMessageBody(String messageBody,Integer messageId){
        try {
            emailRepository.updateEmailMessageBody(messageBody,messageId);
        } catch (Exception e) {
            log.error("error : {} occurred in updatingEmailMessageBody", e.getMessage());
        }
    }
}
