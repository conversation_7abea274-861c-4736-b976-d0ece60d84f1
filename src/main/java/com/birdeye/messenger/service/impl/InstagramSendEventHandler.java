package com.birdeye.messenger.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.elastic.RobinSessionDocument;
import com.birdeye.messenger.enums.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.InstagramMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.PulseSurveyContext;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.MessageDocumentDTO;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessengerGlobalFilter;
import com.birdeye.messenger.dto.MessengerMediaFileDTO;
import com.birdeye.messenger.dto.MessengerMessageMetaData;
import com.birdeye.messenger.dto.SendInstagramMessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.SentThrough;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.FacebookException;
import com.birdeye.messenger.exception.InstagramException;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.InstagramEventService;
import com.birdeye.messenger.service.MessengerMediaFileService;
import com.birdeye.messenger.service.PulseSurveyService;
import com.birdeye.messenger.service.SendMessageService;
import com.birdeye.messenger.util.EncryptionUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class InstagramSendEventHandler extends MessageEventHandlerAbstract{
	
	private final MessengerEvent EVENT = MessengerEvent.INSTAGRAM_SEND;
	@Autowired
    protected InstagramEventService  instagramEventService;
	@Autowired
    protected SendMessageService sendMessageService;
	@Autowired
    protected MessengerMediaFileService messengerMediaFileService;
	@Autowired
    protected MessengerMessageService messengerMessageService;
	@Autowired
    protected CommonService commonService;
	@Autowired
	protected PulseSurveyService pulseSurveyService;
	
	@Autowired
	protected SocialService socialService;
	
    @Override
    BusinessDTO getBusinessDTO(MessageDTO messageDTO) {
        BusinessDTO businessDTO = messageDTO.getBusinessDTO();
        if (Objects.isNull(businessDTO)) {
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            Integer businessId = Integer.valueOf(dto.getBusinessIdentifierId());
            businessDTO = communicationHelperService.getBusinessDTO(businessId);
            dto.setBusinessDTO(businessDTO);
        }
        return businessDTO;
    }
    
    @Override
    CustomerDTO getCustomerDTO(MessageDTO messageDTO) {
	    	CustomerDTO customerDTO = messageDTO.getCustomerDTO();
	    	if (Objects.isNull(customerDTO)) {
	    		SendMessageDTO dto = (SendMessageDTO) messageDTO;
	    		///customerDTO = communicationHelperService.getCustomerDTO(dto.getCustomerId());
				customerDTO = contactService.findByIdNoCaching(dto.getCustomerId());
	    		dto.setCustomerDTO(customerDTO);
	    	}
	    	if(customerDTO!=null && BooleanUtils.isTrue(customerDTO.getBlocked())) {
				log.info("contact is blocked. Payload {}", messageDTO);
				throw new BadRequestException(ErrorCode.CONTACT_IS_BLOCKED, ErrorCode.CONTACT_IS_BLOCKED.getErrorMessage());
			}
	    	return customerDTO;
    }
    
    UserDTO getUserDTO(MessageDTO messageDTO) {
    	UserDTO userDTO = messageDTO.getUserDTO();
        if (Objects.isNull(userDTO)) {
        	SendMessageDTO dto = (SendMessageDTO) messageDTO;
        	userDTO = communicationHelperService.getUserDTO(dto.getUserId());
            dto.setUserDTO(userDTO);
        }
        return userDTO;
    }
    
    @Override
    MessageTag getMessageTag(MessageDTO messageDTO) {
    	return MessageTag.INBOX;
    }
    
    /*
     * Setting reciepientId as from and senderId as To bcz it is being set this way in Platform.
     * Fixing this would require additional migration
     * Data is correctly persisted in DB
     *  
     */
    @Override
    MessageDocumentDTO getMessageDocumentDTO(MessageDTO messageDTO) {
    	SendMessageDTO dto = (SendMessageDTO) messageDTO;
    	MessageDocumentDTO messageDocumentDTO= new MessageDocumentDTO(dto.getConversationDTO(), getMessengerContact(messageDTO).getId());
    	if (CollectionUtils.isNotEmpty(messageDTO.getMessengerMediaFiles())) {
    		messageDocumentDTO.setMediaFiles(messageDTO.getMessengerMediaFiles().stream()
    				.map(messengerMediaFile -> new MessageDocument.MediaFile(messengerMediaFile,
    						messengerMediaFile.getExtension(),messengerMediaFile.getMessageId().toString()))
    				.collect(Collectors.toList()));
    	}
    	messageDocumentDTO.setFrom(dto.getConversationDTO().getRecipient());
    	messageDocumentDTO.setTo(dto.getConversationDTO().getSender());
    	messageDTO.setMessageDocumentDTO(messageDocumentDTO);
        return messageDocumentDTO;
    }
    
	@Override
	public MessengerEvent getEvent() {
		return EVENT;
	}

	@Override
	void updateLastMessageMetaData(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
		UserDTO userDTO = getUserDTO(messageDTO);
		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		lastMessageMetadataPOJO.setLastMessageType("SEND");
		lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
		lastMessageMetadataPOJO.setLastMessageUserName(MessengerUtil.buildUserName(userDTO));
		lastMessageMetadataPOJO.setLastMessageChannel(MessageDocument.Channel.INSTAGRAM.name());
		lastMessageMetadataPOJO.setLastMessageSource(Source.INSTAGRAM.getSourceId());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		lastMessageMetadataPOJO.setLastIgSentAt(sdf.format(new Date()));
		messengerContact.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
		SendMessageDTO  dto = (SendMessageDTO) messageDTO;
        //TODO: remove this comment once all SEND and RECEIVE api migrated to messenger-service
        /*
		 * LastMessageMetaData lastMessageMetaData =
		 * MessengerUtil.getLastMessageMetadataPOJO(messengerContact);
		 * lastMessageMetaData.setLastMessageSource(Source.FACEBOOK.getSourceId());
		 * SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 * lastMessageMetaData.setLastFbSentAt(sdf.format(new Date()));
		 * messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(
		 * lastMessageMetaData));
		 */
        messengerContact.setLastResponseAt(dto.getConversationDTO().getSentOn());
        messengerContact.setLastMsgOn(dto.getConversationDTO().getCreateDate());
        messengerContact.setUpdatedAt(dto.getConversationDTO().getCreateDate());
	}

	@Override
	void alterAndUpdateLastMessage(MessageDTO messageDTO) {
        MessengerContact messengerContact = getMessengerContact(messageDTO);
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        BusinessDTO businessDTO = getBusinessDTO(messageDTO);
        if (StringUtils.isEmpty(sendMessageDTO.getBody()) && StringUtils.isNotEmpty(sendMessageDTO.getMediaurl())) {
            messengerContact.setLastMessage("Sent an attachment");
        } else {
            messengerContact.setLastMessage(sendMessageDTO.getBody());
        }
        boolean isEncrypted = EncryptionUtil.encryptLastMessage(messengerContact, sendMessageDTO.getEncrypted(), messengerContact.getInstagramConversationId(), businessDTO.getBusinessNumber());
        messengerContact.setEncrypted(isEncrypted ? 1 : 0);
	}

	@Override
	MessengerContact getMessengerContact(MessageDTO messageDTO) {
        MessengerContact messengerContact = messageDTO.getMessengerContact();
        SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
        if (Objects.isNull(messageDTO.getMessengerContact())) {
            BusinessDTO businessDTO = getBusinessDTO(messageDTO);
			messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					businessDTO.getBusinessId(), sendMessageDTO.getCustomerId(), businessDTO.getAccountId());
            messageDTO.setMessengerContact(messengerContact);
        }
        if(messengerContact!=null && BooleanUtils.isTrue(messengerContact.getBlocked())) {
        	throw new BadRequestException(ErrorCode.CONTACT_IS_BLOCKED, ErrorCode.CONTACT_IS_BLOCKED.getErrorMessage());
		}
        return messengerContact;
	}

	@Override
	MessengerGlobalFilter getEmailNotificationMetaData(MessageDTO messageDTO) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	Integer getMessageId(MessageDTO messageDTO) {
		Integer messageId = messageDTO.getMessageId();
        if(Objects.isNull(messageId)) {
        	SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
            messageId = sendMessageDTO.getConversationDTO().getId();
            messageDTO.setMessageId(messageId);
        }
        return messageId;
	}
	
	@Override
    public SendResponse handle(MessageDTO messageDTO) throws Exception {
		if(!messageDTO.isBOT()) {
			messageDTO.setMsgTypeForResTimeCalc("S");
		}		
		BusinessDTO businessDTO = getBusinessDTO(messageDTO);
		messageDTO.setBusinessDTO(businessDTO);
		MessengerContact messengerContact=getMessengerContact(messageDTO);
		messageDTO.setMessengerContact(messengerContact);
		SortedSet<MessageResponse.Message> messages = sendMessage(messageDTO);
		// TODO set flag with updated approach
		int routeId=businessDTO.getEnterpriseId()!=null?businessDTO.getEnterpriseId():businessDTO.getBusinessId();
		SendResponse sendResponse = new SendResponse(messengerContact.getId(), messages, instagramEventService.isInstagramSendAvailable(messengerContact, routeId));
		sendResponse.setLastMsgSource(messageDTO.getSource());
		sendResponse.setMessages(messages);
		CustomerDTO customerDTO = getCustomerDTO(messageDTO);
		// handle PulseSurveyContext
		PulseSurveyContext context = null;
		try {
			context = pulseSurveyService.handlePulseSurveyContext(null, customerDTO, businessDTO);
		} catch (Exception ex) {
			log.error("Getting exception while executing handlePulseSurveyContext method {}", ex);
		}
		ContactDocument contactDocument = new ContactDocument();
		if (context != null && PulseSurveyContext.isOngoingPulseSurvey(context.getStatus())){
			contactDocument.setOngoingPulseSurvey(PulseSurveyContext.isOngoingPulseSurvey(context.getStatus()));
		} else {
			contactDocument.setOngoingPulseSurvey(false);
		}
		messengerContactService.updateContactOnES(messageDTO.getMessengerContact().getId(), contactDocument, messageDTO.getBusinessDTO().getAccountId());
		return sendResponse;
	}

	private SortedSet<MessageResponse.Message> sendMessage(MessageDTO messageDTO) throws Exception {
		// TODO: Refactor : Avoid casting and have separate dto & request.
		getBusinessDTO(messageDTO);
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		sendMessageDTO.setCustomerId(getMessengerContact(messageDTO).getCustomerId());
		commonService.setMessageBody(sendMessageDTO, getCustomerDTO(messageDTO),"instagram");
		sendMessageDTO.setSource(Source.INSTAGRAM.getSourceId());
		sendMessageDTO.setFromBusinessId(getMessengerContact(sendMessageDTO).getBusinessId());
		MessengerEvent event = MessengerEvent.SMS_SEND;
		SortedSet<SendResponse.Message> messages = new TreeSet<>(MessageResponse.getMessageComparator());
		if (StringUtils.isNotBlank(sendMessageDTO.getMediaurl())
				|| CollectionUtils.isNotEmpty(sendMessageDTO.getMediaUrls())) {
			event = MessengerEvent.MMS_SEND;
		}
		try {
			if (event.equals(MessengerEvent.MMS_SEND)) {
				log.info("Processing Instagram attachtment SEND for contact {} ",sendMessageDTO.getToCustomerId());
				// Process mms data.
				String text=sendMessageDTO.getBody();
				sendMessageDTO.setMessengerMediaFiles(new ArrayList<MessengerMediaFile>());
				for (int media = 0; media < sendMessageDTO.getMediaUrls().size(); media++) {
					MessengerMediaFile mediaFile = sendMessageDTO.getMediaUrls().get(media);
					sendMessageDTO.setMediaurl(mediaFile.getUrl());
					if (media < sendMessageDTO.getMediaUrls().size()) {
						sendMessageDTO.setBody(null);
					} 
					MessengerMediaFileDTO messengerMediaFileDTO=new MessengerMediaFileDTO(mediaFile);
					messengerMediaFileDTO.setFileExtension(FilenameUtils.getExtension(mediaFile.getUrl()));
					sendMessageDTO.setMessengerMediaFileDTO(messengerMediaFileDTO);
					if(media!=0) {
						messageDTO.setPartOfExistingMessage(true);
					}
					sendInstagramMessage(sendMessageDTO, event);
				}
				// Process Text body if present.
				if(StringUtils.isNotEmpty(text)){
					log.info("Preparing Text message for Facebook attachtment SEND for contact {} ",sendMessageDTO.getToCustomerId());
					sendMessageDTO.setMessengerMediaFileDTO(null);
					sendMessageDTO.setBody(text);
					sendInstagramMessage(sendMessageDTO,event);
				}
				
			} else {
				log.info("Processing Instagram TEXT SEND for contact {} ",sendMessageDTO.getToCustomerId());
				sendInstagramMessage(sendMessageDTO, event);
			}
		} catch (Exception e) {
			log.error("Error occurred while sending IG message: {}",e);
		}
		if(sendMessageDTO.getConversationDTO() == null || sendMessageDTO.getConversationDTO().getId() == null) {
			throw new InstagramException(ErrorCode.IG_API_ERROR);
		}
		super.handle(sendMessageDTO);
		MessageResponse.Message message = new SendResponse.Message(sendMessageDTO.getUserDTO(), sendMessageDTO.getConversationDTO(), sendMessageDTO.getMessengerMediaFiles(), messageDTO.getBusinessDTO().getTimeZoneId());
		message.setPaymentInfo(messageDTO.getMessageDocumentDTO().getPaymentInfo());
		messages.add(message);
		robinService.updateChatSession(sendMessageDTO.getBusinessDTO().getAccountId(),sendMessageDTO.getMessengerContact().getId(), sendMessageDTO.getUserDTO(),Source.INSTAGRAM.name());
		return messages;
	}
	
	private void sendInstagramMessage(MessageDTO messageDTO,MessengerEvent event) throws Exception {
		String pageId =socialService.getInstagramPageIdByBusinessId(getBusinessDTO(messageDTO).getBusinessId());
		//1. send instagram message using soical service
		SendInstagramMessageResponse sendInstagramMessageResponse =instagramEventService.sendIGCallToSocial(this,messageDTO,pageId);
		if(sendInstagramMessageResponse == null || sendInstagramMessageResponse.getMessage_id() == null) {
			return;
		}
		//2. save messge in db
		SendMessageDTO sendMessageDTO = (SendMessageDTO) messageDTO;
		UserDTO userDTO=getUserDTO(messageDTO);
		InstagramMessage instagramMessage=instagramEventService.saveInstagramMessage(messageDTO,pageId,getMessengerContact(sendMessageDTO).getFacebookId(), sendInstagramMessageResponse.getMessage_id(), InstagramMessageStatusEnum.sent);
		MessengerMessageMetaData messageMetaData=new MessengerMessageMetaData();
		messageMetaData.setSentThrough(SentThrough.WEB);
		messageMetaData.setCommunicationDirection(CommunicationDirection.SEND);
		ConversationDTO conversationDTO = new ConversationDTO(instagramMessage,messageMetaData);
		log.info("Messenger contact Id {} ,conversation Id: {}  ", messageDTO.getMessengerContact().getId(),conversationDTO.getId());
		messengerMessageService.saveMessengerMessage(conversationDTO,userDTO);		
		messengerMediaFileService.saveMediaFile(messageDTO.getMessengerMediaFileDTO(), instagramMessage.getId());
		if(Objects.nonNull(messageDTO.getMessengerMediaFileDTO())) {
			MessengerMediaFile  messengerMediaFile=new MessengerMediaFile(messageDTO.getMessengerMediaFileDTO());
			messengerMediaFile.setMessageId(instagramMessage.getId());
			messengerMediaFile.setExtension(messageDTO.getMessengerMediaFileDTO().getFileExtension());
			messageDTO.getMessengerMediaFiles().add(messengerMediaFile);
		}
		sendMessageDTO.setConversationDTO(conversationDTO);
		sendMessageService.pushSendRequestToKafka(sendMessageDTO,event,userDTO,false);
	}

	@Override
	void publishEvent(MessageDTO messageDTO) {
		publishEventIfRepliedOnUnassignedConversation(messageDTO);
	}

}
