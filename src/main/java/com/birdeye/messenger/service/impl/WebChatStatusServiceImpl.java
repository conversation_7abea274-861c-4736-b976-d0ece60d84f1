package com.birdeye.messenger.service.impl;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.BusinessWebchatEvent;
import com.birdeye.messenger.dao.repository.BusinessWebchatEventRepository;
import com.birdeye.messenger.dto.WebChatEventRequestDTO;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.WebChatStatusService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class WebChatStatusServiceImpl implements WebChatStatusService {

    private final BusinessWebchatEventRepository webChatEventRepo;
    
    private final KafkaService kafkaService;

    @Override
    public void consumeEvent(WebChatEventRequestDTO webChatEventRequestDTO) {
    	if (webChatEventRequestDTO.getWidgetId()==null || webChatEventRequestDTO.getWebsite()==null) {
    		return;
    	}
    	if (webChatEventRequestDTO.getWidgetId() == 1) {
    		log.info("Reject BusinessWebchatEvent for id = 1 and website: {}", webChatEventRequestDTO.getWebsite());
    		return;
    	}
    	if (webChatEventRequestDTO.getWebsite().contains("birdeye.com")) {
    		log.info("BusinessWebchatEvent from birdeye : {}", webChatEventRequestDTO);
    		return;
    	}
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.WEBCHAT_EVENT, webChatEventRequestDTO);
    }

	@Override
	public void saveWebchatEvent(WebChatEventRequestDTO webChatEventRequestDTO) {
		BusinessWebchatEvent businessWebChatEvent = new BusinessWebchatEvent();
        businessWebChatEvent.setWebsite(webChatEventRequestDTO.getWebsite());
        businessWebChatEvent.setWidgetId(webChatEventRequestDTO.getWidgetId());
        webChatEventRepo.save(businessWebChatEvent);
	}

}