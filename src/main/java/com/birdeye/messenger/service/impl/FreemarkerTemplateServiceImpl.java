package com.birdeye.messenger.service.impl;

import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.FreemarkerTemplateService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class FreemarkerTemplateServiceImpl implements FreemarkerTemplateService {

    private final Configuration freeMarker;

    @Override
    public String processTemplate(String templateName, Map<String, Object> dataModel) {
        StringWriter writer = new StringWriter(50);
        try {
            Template template = freeMarker.getTemplate(templateName);
            template.process(dataModel, writer);
            return writer.toString();
        } catch (IOException | TemplateException e) {
            throw new MessengerException("processTemplate: Error processing template : [" + templateName + "]", e);
        }
    }
}
