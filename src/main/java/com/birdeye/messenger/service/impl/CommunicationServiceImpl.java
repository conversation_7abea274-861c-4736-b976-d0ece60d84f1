package com.birdeye.messenger.service.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.mobile.ConversationDetails;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.dto.MessengerMessage;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.SocialService;
import com.birdeye.messenger.service.CommunicationService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FacebookEventService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MigrationService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@Service
public class CommunicationServiceImpl implements CommunicationService {

    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final ContactService contactService;
    private final CommunicationHelperService communicationHelperService;
    private final FacebookEventService facebookEventService;
    private final ConversationService conversationService;
    private final MessageService messageService;

	private final SocialService socialService;
	@Lazy
	private final MigrationService migrationService;

	private static ObjectMapper mapper = new ObjectMapper();
	private final ElasticSearchExternalService elasticSearchService;
	
	static{
		mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
	}
	
	private Boolean calculateFBSendAvailable(String lastFbReceivedAt,String lastFbSentAt) {
		log.info("calculateFBSendAvailable: lastFbReceivedAt: {} and  lastFbSentAt {} ", lastFbReceivedAt, lastFbSentAt);
		Boolean isLastFacebookMessage =false;
		if(StringUtils.isNotBlank(lastFbReceivedAt)){
			Calendar lastReceivedPlus24Hour = Calendar.getInstance();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			try {
				lastReceivedPlus24Hour.setTime(sdf.parse(lastFbReceivedAt));
				lastReceivedPlus24Hour.add(Calendar.HOUR,24);
				if(lastReceivedPlus24Hour.getTime().before(Calendar.getInstance().getTime())){
					if(StringUtils.isBlank(lastFbSentAt)) {
						isLastFacebookMessage=false;
					}
					else{
						Calendar timeLastSentToFacebook = Calendar.getInstance();
						timeLastSentToFacebook.setTime(sdf.parse(lastFbSentAt));
						log.info("calculateFBSendAvailable: lastFbReceivedAt + 24 hour : {} ", lastReceivedPlus24Hour.getTime());
						/* setting again to lastFbReceivedMsg value in order to check atleast one 
						 message sent after 24 hours of last message received from facebook */
						//lastReceivedPlus24Hour.setTime(sdf.parse(lastFbReceivedAt));
						if(timeLastSentToFacebook.getTime().after(lastReceivedPlus24Hour.getTime())) {
							isLastFacebookMessage=true;
						}
					}
				}
			}
			catch (Exception e) {
				log.error("exception in parsing date of isLastFacebookMessage method: ",e);
			}
		}
		return isLastFacebookMessage;
	}
	
	@Override
	public Boolean isFBSendAvailable(Integer messengerContactId,Integer routeId,LastMessageMetaData lastMessageMetaData) {
		MessengerFilter messengerFilter = new MessengerFilter();
		messengerFilter.setStartIndex(0);
		messengerFilter.setCount(1);
		messengerFilter.setConversationId(messengerContactId);
		messengerFilter.setAccountId(routeId);
		List<String> typeList=new ArrayList<>();
    	typeList.add("SMS_RECEIVE");
        typeList.add("MMS_RECEIVE");
        Map<String, Object> params= new HashMap<>();
        params.put("msg_type",ControllerUtil.getJsonTextFromObject(typeList));
        params.put("source", Source.FACEBOOK.getSourceId());
        messengerFilter.setParams(params);
        String lastFbReceivedAt="";
        String lastFbSentAt="";
        // last facebook Receieved message 
		List<MessageDocument> messsages = messengerContactService.getMessagesFromES(messengerFilter);
		if(CollectionUtils.isNotEmpty(messsages)) {
			lastFbReceivedAt=messsages.get(0).getCr_date();
			typeList=new ArrayList<>();
	    	typeList.add("SMS_SEND");
	        typeList.add("MMS_SEND");
	        params.put("msg_type",ControllerUtil.getJsonTextFromObject(typeList));
	        // last facebook sent message 
	        messsages = messengerContactService.getMessagesFromES(messengerFilter);
	        lastFbSentAt=CollectionUtils.isNotEmpty(messsages)?messsages.get(0).getCr_date():""	;	        
		}
		//TODO: uncomment below code once all SEND,RECEIVE api is moved to service
		/*if(lastMessageMetaData==null) {
			lastMessageMetaData=new LastMessageMetaData();
		}
		lastMessageMetaData.setLastFbSentAt(lastFbSentAt);
		lastMessageMetaData.setLastFbReceivedAt(lastFbReceivedAt);
		messengerContact.setLastMessageMetaData(ControllerUtil.getJsonTextFromObject(lastMessageMetaData));
		messengerContactRepository.save(messengerContact);*/
		return calculateFBSendAvailable(lastFbReceivedAt, lastFbSentAt);
	}

	@Override
	public Boolean isFBSendAvailable(MessengerContact messengerContact,Integer routeId,LastMessageMetaData lastMessageMetaData) {
		return isFBSendAvailable(messengerContact.getId(), routeId, lastMessageMetaData);
	}

	@Override
	public Object getMessage(MessengerFilter messengerFilter, String platform) throws Exception {
		// if last message time is present start with 0 index
        if (messengerFilter.getLastMsgTime() != null) // remove
        	messengerFilter.setStartIndex(0);
        // if size is not present set size as default size i.e 25
        if (messengerFilter.getCount() == null)
        	messengerFilter.setCount(25);
        if (messengerFilter.getLastMsgTime() != null) {
        	messengerFilter.setStartDate(messengerFilter.getLastMsgTime());
        }
        Integer conversationId;
        if(messengerFilter.getParams().get("conversationId")!=null) {
        	conversationId=Integer.parseInt(messengerFilter.getParams().get("conversationId").toString());
        }else {
        	throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
        }
        messengerFilter.setConversationId(conversationId);

        // find BusinessDTO using business core service
        BusinessDTO businessDTO;
        if(messengerFilter.getParams().get("businessId")!=null) {
        	businessDTO=businessService.getBusinessDTO(Integer.parseInt(messengerFilter.getParams().get("businessId").toString()));
        }else {
        	throw new NotFoundException(ErrorCode.NO_BUSINESS_ID_SPECIFIED);
        }
        if(Objects.isNull(businessDTO)) {
        	log.error("getMessage: businessDTO returned null from core-service for businessId {}", Integer.parseInt(messengerFilter.getParams().get("businessId").toString()));
        	throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		messengerFilter.setAccountId(businessDTO.getEnterpriseId()!=null?businessDTO.getEnterpriseId():businessDTO.getBusinessId());
		ContactDocument contactDocument=null;
		Integer startIndex=messengerFilter.getStartIndex();
		// Setting startIndex as 0 for contact search
		messengerFilter.setStartIndex(0);
		List<ContactDocument> contactDocuments=messengerContactService.getContactFromES(messengerFilter);
		
		if(CollectionUtils.isNotEmpty(contactDocuments)) {
			contactDocument=contactDocuments.get(0);
		}else {
			throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		}
		// set start index if previously present in filter
		if (startIndex != null)
			messengerFilter.setStartIndex(startIndex);
       	MessengerContact messengerContact = messengerContactService.findById(conversationId);
		if(Objects.isNull(messengerContact)) throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		List<MessageDocument> messageDocuments = messengerContactService.getMessagesFromES(messengerFilter);
		if (messengerContact.getTag().equals(MessengerTagEnum.UNREAD.getId())) {
			conversationService.updateConversationStatus(messengerContact, MessengerTagEnum.INBOX.getId(), null, businessDTO.getRoutingId(), null);
		}
		Object response;
		if(platform.equalsIgnoreCase("WEB")) {
			response = getMessengerMessage(contactDocument, messageDocuments, messengerFilter.getCount()==messageDocuments.size(), businessDTO, messengerContact, messengerFilter.getMessengerEnabled());
		}
		else {
			response = getConversationDetail(contactDocument, messageDocuments, businessDTO.getTimeZoneId());
		}

		return response;
	}

	private ConversationDetails getConversationDetail(ContactDocument contactDocument, List<MessageDocument> messageDocuments, String timeZoneId) {
		ConversationDetails cd = new ConversationDetails(contactDocument, messageDocuments, timeZoneId);
		/**
		 * post processing to handle old-app support
		 * username + "added an internal note: \n" + content
		 * change type INTERNAL_NOTE --> CHAT
		 * 	chatbot reply -- plain-text version, convert type of all messages --> CHAT
		 */
		
		if(cd!=null && CollectionUtils.isNotEmpty(cd.getMsgsByDate())) {
			cd.getMsgsByDate().forEach(mbd -> {
				mbd.getMessages().stream().forEach(m -> {
					
					// format internal note as plain-text
					if(MessengerEvent.INTERNAL_NOTES.name().equalsIgnoreCase(m.getMessageType())) {
						String body = m.getSentBy()+" added an internal note: \n" + m.getBody();
						m.setMessageType(MessengerEvent.SMS_SEND.name());
						m.setBody(body);
						m.setSource(Source.SMS.getSourceId());
					}
					
					// change type of outgoing messages to CHAT
					// and convert chatbot replies to plain text version
					if(m.getSenderId()!=null) { // outgoing message
						String plainContent = m.getBody();
						if(m.getSenderId()==-10) { // chatbot message
							plainContent = MessengerUtil.transformChatbotReplyToPlainText(m.getBody(), false);							
						}
						m.setBody(plainContent);
						// outgoing messages need to be of type SMS_SEND for old app
						m.setMessageType(MessengerEvent.SMS_SEND.name());
					}
				});
			});
		}
		return cd;
	}

	private MessengerMessage getMessengerMessage(ContactDocument contactDocument, List<MessageDocument> messageDocuments, boolean hasMore, BusinessDTO businessDTO, MessengerContact messengerContact, Integer isMessengerEnabled) throws Exception {
		MessengerMessage messengerMessage = new MessengerMessage(contactDocument, messageDocuments, hasMore, businessDTO.getTimeZoneId());
		setFBFlags(messengerMessage, messengerContact, businessDTO,isMessengerEnabled);
		return messengerMessage;
	}

	private void setFBFlags(MessengerMessage messengerMessage,MessengerContact messengerContact,BusinessDTO businessDTO,Integer messengerEnabled) throws Exception {
		// setting last facebook message
		Integer routeId=businessDTO.getEnterpriseId()!=null?businessDTO.getEnterpriseId():businessDTO.getBusinessId();
		messengerMessage.setIsLastFacebookMessage(facebookEventService.isFBSendAvailable(messengerContact, routeId));
		// TODO use getLastReceivedMessageSource's overridden method once all SEND,RECEIVE apis are moved to service
		messengerMessage.setLastReceivedMsgSource(messengerContactService.getLastReceivedMessageSource(messengerContact.getId(), routeId));
		messengerMessage.setIsUserUnreachable(getUserReachableStatus(messengerContact.getId(), businessDTO)); // TODO remove it to use from FacebookEventService
		messengerMessage.setFbIntegrationStatus(facebookEventService.getFacebookIntegrationStatus(businessDTO.getBusinessId(), messengerEnabled));
	}
	
	@Override
	public Boolean getUserReachableStatus(Integer customerId, BusinessDTO business) throws Exception {
		Boolean isUserUnreachable = false;
		Map<String, Object> messageFilter = new HashMap<>();
		messageFilter.put("size", 0);
		messageFilter.put("from", 0);
		messageFilter.put("c_id", customerId.toString());
		messageFilter.put("source", 110);
		Integer enterpriseId = business.getEnterpriseId() == null ? business.getBusinessId() : business.getEnterpriseId();
		Map<String, Object> dataModel = new HashMap<>();
		dataModel.put("messageFilter", messageFilter);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(enterpriseId)
				.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_2, dataModel).build();
		
		ElasticData messagesData = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
		if (messagesData != null) {
			String lastFacebookPageId = "";
			String currentFacebookPageId = "";
//			currentFacebookPageId=platformDbRepository.getFacebookPageIdByBusinessId(business.getBusinessId());
			currentFacebookPageId=socialService.getFacebookPageIdByBusinessId(business.getBusinessId());
			Date lastMessage = null;
			Integer total = messagesData.getTotal().intValue();
			messageFilter.put("size", total.toString());
			dataModel.put("messageFilter", messageFilter);
			esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
					.addRoutingId(enterpriseId)
					.addTemplateAndDataModel(Constants.Elastic.MESSENGER_CONVERSATION_2, dataModel).build();
			messagesData=elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
			if (messagesData != null) {
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				List<MessageDocument> messages = messagesData.getResults();
				for (MessageDocument message : messages) {
					if (message.getCr_date() == null) {
						continue;
					}
					Date sentOnUTC = df.parse(message.getCr_date());
					if (lastMessage == null || lastMessage.before(sentOnUTC)) {
						lastMessage = sentOnUTC;
						if (message.getMsg_type() != null && (message.getMsg_type().equalsIgnoreCase("SMS_RECEIVE")
								|| message.getMsg_type().equalsIgnoreCase("MMS_RECEIVE"))) {
							lastFacebookPageId = message.getFrom();
						}
//						else {
//							lastFacebookPageId = message.getTo();
//						}
					}
				}
			}
			if (StringUtils.isNotBlank(lastFacebookPageId) && StringUtils.isNotBlank(currentFacebookPageId)) {
				if (!lastFacebookPageId.equalsIgnoreCase(currentFacebookPageId)) {
					isUserUnreachable = true;
				}
			} else if (StringUtils.isBlank(currentFacebookPageId) && StringUtils.isNotBlank(lastFacebookPageId)) {
				isUserUnreachable = true;
			}
		}
		return isUserUnreachable;
	}

	@Override
	public MessengerContact getMessengerContact(Integer ecId, Long businessNumber, boolean isBizNumber) {
		BusinessDTO businessDTO = null;
		if (isBizNumber) {
			businessDTO = businessService.getBusinessByBusinessNumber(businessNumber);
		}
		else {
			businessDTO = communicationHelperService.getBusinessDTO(businessNumber.intValue());
		}
		Integer customerIdByEcIdAndBusinessId = contactService.findCustomerIdByEcIdAndBusinessId(ecId, businessDTO.getBusinessId());
		if(Objects.isNull(customerIdByEcIdAndBusinessId)) {
			throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
		}
		MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(businessDTO.getBusinessId(), customerIdByEcIdAndBusinessId, businessDTO.getAccountId());
		if (messengerContact.getIsNew() != null && messengerContact.getIsNew()) { // means its a new messengerContact
			messengerContact.setUpdatedAt(messengerContact.getCreatedAt());
			messengerContactService.saveOrUpdateMessengerContact(messengerContact);
			//CustomerDTO customerDTO = communicationHelperService.getCustomerDTO(customerIdByEcIdAndBusinessId);
			CustomerDTO customerDTO = contactService.findById(customerIdByEcIdAndBusinessId);
			messengerContact.setLastMessage("");
			messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX, null);
		}
		try {
			Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(businessDTO.getAccountId(), messengerContact.getId());
			if (!contactDocumentOptional.isPresent()) {
				migrationService.migrateByMessengerContactId(messengerContact.getId(), true);
			}
		} catch (Exception e) {
			log.error("Error migrating CD for mcId: {}", messengerContact.getId(), e);
		}
		MessengerContact response = new MessengerContact();
		response.setId(messengerContact.getId());
		response.setUpdatedAt(null);
		return response;
	}

	@Override
	public Object getMcIdByReviewId(Integer reviewId,Integer accountId) {
		return messageService.getMcIdByReviewId(reviewId,accountId);
	}


}
