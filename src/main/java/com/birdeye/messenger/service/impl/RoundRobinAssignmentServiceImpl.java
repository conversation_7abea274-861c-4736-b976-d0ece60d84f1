package com.birdeye.messenger.service.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.sro.GetBusinessUser;
import com.birdeye.messenger.util.LogUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoundRobinAssignmentServiceImpl implements RoundRobinAssignmentService {

	private static final String INBOX_ROUND_ROBIN = "InboxRoundRobin:";
	private static final String DATA_KEY = ":data";

	private final TeamConfigForRoundRobinRepositoryService teamConfigForRoundRobinRepositoryService;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private RedisHandler redisHandler;

	@Autowired
	private AssignmentService assignmentService;
	

	@Override
	public TeamConfigForRoundRobin getTeamConfigForRoundRobin(RoundRobinActivateRequest roundRobinActivateRequest) {
		TeamConfigForRoundRobin teamConfigForRoundRobin = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobinByAccountId(roundRobinActivateRequest.getAccountId());
		if(teamConfigForRoundRobin != null){
			return teamConfigForRoundRobin;
		}
		return null;
	}
	
	@Override
	@Transactional
	public void createRoundRobinConfig(RoundRobinActivateRequest roundRobinActivateRequest) {
        log.info("Activate RoundRobinConfig for accountId: {}, teamId: {}",roundRobinActivateRequest.getAccountId(),roundRobinActivateRequest.getTeamId());
		List<Integer> teamUsers = businessService.getAllUsersOfTeams(roundRobinActivateRequest.getTeamId(), roundRobinActivateRequest.getAccountId());
        if(CollectionUtils.isEmpty(teamUsers)){
			throw new NotFoundException("No users present in team");
		}
		TeamConfigForRoundRobin teamConfigForRoundRobin = new TeamConfigForRoundRobin();
		teamConfigForRoundRobin.setTeamId(roundRobinActivateRequest.getTeamId());
		teamConfigForRoundRobin.setAccountId(roundRobinActivateRequest.getAccountId());
		teamConfigForRoundRobin.setCreatedDate(new Date());
		teamConfigForRoundRobin.setUpdatedDate(new Date());
		teamConfigForRoundRobinRepositoryService.saveTeamConfigForRoundRobin(roundRobinActivateRequest.getAccountId(), teamConfigForRoundRobin);
	}

	@Override
	@Transactional
	public void updateRoundRobinConfig(RoundRobinActivateRequest roundRobinActivateRequest, TeamConfigForRoundRobin teamConfigForRoundRobin) {
		log.info("Update request for RoundRobinConfig for accountId: {}, teamId: {}",roundRobinActivateRequest.getAccountId(),roundRobinActivateRequest.getTeamId());
		List<Integer> teamUsers = businessService.getAllUsersOfTeams(roundRobinActivateRequest.getTeamId(), roundRobinActivateRequest.getAccountId());
		if(CollectionUtils.isEmpty(teamUsers)){
			throw new NotFoundException("No users present in team");
		}
		teamConfigForRoundRobin.setTeamId(roundRobinActivateRequest.getTeamId());
		teamConfigForRoundRobin.setUpdatedDate(new Date());
		teamConfigForRoundRobinRepositoryService.saveTeamConfigForRoundRobin(teamConfigForRoundRobin.getAccountId(), teamConfigForRoundRobin);
	}

	@Override
	@Transactional
	public void disableRoundRobin(Integer accountId) {
		log.info("Delete request for RoundRobinConfig for accountId: {}",accountId);
		TeamConfigForRoundRobin config = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobinByAccountId(accountId);
		if (config == null){
			log.error("Round Robin Config not present for accountId: {}",accountId);
			throw new MessengerException("Round Robin Config not present for the account");
		}
		teamConfigForRoundRobinRepositoryService.deleteTeamConfigForRoundRobin(accountId);
		
		teamConfigForRoundRobinRepositoryService.evictTeamConfigCache(accountId);
	}

	@Override
	@Transactional
	public void deleteTeam(DeleteTeamEvent deleteTeamEvent) {
		Integer accountId = deleteTeamEvent.getAccountId();
		Integer teamId = deleteTeamEvent.getTeamId();
		TeamConfigForRoundRobin teamConfigForRoundRobin = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobin(accountId, teamId);
		if(teamConfigForRoundRobin != null){
			log.info("Delete RoundRobin team for the accountId: {}, teamId: {}",accountId,teamId);
			teamConfigForRoundRobinRepositoryService.deleteTeamConfigForRoundRobin(accountId);
			teamConfigForRoundRobinRepositoryService.evictTeamConfigCache(accountId);
		} else{
			log.info("RoundRobin Config is not present for accountId: {}, teamId: {}",accountId,teamId);
		}
	}

	/**
	 *Used for RoundRobin assignment of unassigned conversations - Webchat/Livechat
	 */
	@Override
	public void goForRoundRobinAssignment(BusinessDTO businessDto, ContactDocument contactDocument) {
		Assert.notNull(businessDto, "BusinessDTO can't be null");
		Assert.notNull(contactDocument, "ContactDocument can't be null");
		long startTime = System.currentTimeMillis();
		Integer mcId = contactDocument.getM_c_id();
		Integer accountId=businessDto.getAccountId();

		log.info("RoundRobinAssignmment request received for accountID: {}, mcID: {}", accountId, contactDocument.getM_c_id());
		TeamConfigForRoundRobin config = teamConfigForRoundRobinRepositoryService.findTeamConfigForRoundRobinByAccountId(accountId);
		if (config == null){
			log.info("Account not activated for RoundRobinAssignment : {}", businessDto.getAccountId());
			return;
		}
		//check for Unassigned conversation, if Assigned -> Return
		if (contactDocument.getCr_asgn_id() != -100000) {
			log.info("RoundRobinAssignment - Conversation already assigned for accountID: {}, mcID: {}", businessDto.getAccountId(), mcId);
			return;
		}
		UserDTO userDto = getUserDtoForAssignment(businessDto, config);
		if (mcId != null && mcId != 0 && userDto != null) {
			goForConversationAssignment(businessDto, mcId, userDto);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("goForRoundRobinAssignment", startTime, endTime);
		}
	}

	/**
	 * Get list of users with location access + status and get next user using lua
	 * @param businessDto
	 * @param accountId
	 * @return
	 */
	private UserDTO getUserDtoForAssignment(BusinessDTO businessDto, TeamConfigForRoundRobin config) {
		long startTime = System.currentTimeMillis();
		//Get Users for Location+Team to which the conversation belongs
		UserResponse teamLocationUsers = checkForUserLocationAccess(businessDto, config.getTeamId());
		if (teamLocationUsers == null) {
			log.info("No applicable user found for RoundRobinAssignment. businessId {}, team {}", businessDto.getAccountId(), config.getTeamId());
			return null;
		}
		List<UserDTO> listUserDto = teamLocationUsers.getUsers();
		String stringUserDto = listUserDto.stream().map(UserDTO::formatUserDtoForLua)
                .collect(Collectors.joining(","));
		Long nextAssigneeId = getNextAssigneeForRoundRobin(businessDto, stringUserDto);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getNextAssigneeForRoundRobin", startTime, endTime);
		if (nextAssigneeId == -1) {
			log.info("No applicable user found for RoundRobinAssignment. businessId {}, team {}", businessDto.getAccountId(), config.getTeamId());
			return null;
		}
		List<UserDTO> nextUserDto = listUserDto.stream().filter(l -> l.getId().equals(Integer.valueOf(nextAssigneeId.intValue()))).collect(Collectors.toList());
		return nextUserDto.get(0);
	}

	private UserResponse checkForUserLocationAccess(BusinessDTO businessDto, Integer teamId) {
		Integer accountId = businessDto.getAccountId();
		GetBusinessUser request = new GetBusinessUser();
		request.setBusinessIds(Collections.singletonList(businessDto.getBusinessId()));
		request.setIncludeTeamId(teamId);
		UserResponse teamLocationUsers = businessService.getBusinessUsers(accountId, request);
		if (teamLocationUsers!=null && CollectionUtils.isNotEmpty(teamLocationUsers.getUsers())) {
			return teamLocationUsers;
		}
		return null;
	}

	/**
	 * Get next userId for RoundRobin assignment
	 * @param  
	 * @param locationId 
	 * @param stringUserDto 
	 */
	private Long getNextAssigneeForRoundRobin(BusinessDTO businessDto, String stringUserDto) {
		String accountId = businessDto.getAccountId().toString();
		String locationId = businessDto.getBusinessId().toString();
		try {
			String dataKey = INBOX_ROUND_ROBIN+accountId+":"+locationId+DATA_KEY;
			DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
			redisScript.setScriptSource(new ResourceScriptSource(new ClassPathResource("scripts/GetNextAssigneeRoundRobin.lua")));
			//Specify the return type
			redisScript.setResultType(Long.class);

			return redisHandler.executeLuaScript(redisScript, Arrays.asList(dataKey), stringUserDto);
		} catch (Exception ex) {
			log.error("Error in executing script for getNextAssigneeForRoundRobin {}", ex.getMessage());
		}
		return null;
	}
	
	private void goForConversationAssignment(BusinessDTO businessDto, Integer mcId, UserDTO userDto) {
		ConversationAssignmentRequestDTO request = new ConversationAssignmentRequestDTO();
		IdentityDTO replier = new IdentityDTO(userDto.getId(),userDto.getName(), "U", userDto.getEmailId());
		//Do not send any notification - It would be sent from the receive path
		request.setDoNotNotify(true);
		request.setFrom(null);
		request.setMcId(mcId);
		request.setTo(replier);
		assignmentService.assignConversation(request, null, businessDto.getAccountId());
	}
}
