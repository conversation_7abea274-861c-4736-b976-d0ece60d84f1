package com.birdeye.messenger.service;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.external.dto.SuggestionHolder;
import com.birdeye.messenger.sro.ReviewEvent;

public interface FirebaseService {


	/**
	 * Firebase is getting used for
	 * 
	 * a) data mirroing - Firebase DB
	 * 
	 * b) push/chrome notification - FCM integration
	 * 
	 * @param contactDocument - Conversation doc with customer context.
	 * @param messageDocument - Message doc with current message (sms/fb) details
	 * @param notification - If push/chrome notification is required.
	 */
	void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, boolean notification);
	
	void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId,MessageDocument lastMessage);
	
	void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId,
			MessageDocument lastMessage, boolean notification);

	void mirrorOnWeb(Integer accountId, Integer businessId);
	void mirrorOnMobile(ContactDocument contactDocument, MessageDocument messageDocument);

	void pushTypingEventToFirebase(ConversationStateDTO event);

	void mirrorOnMobile(ContactDocument conversation);

    void mirrorOnWeb(FirebaseDto firebaseDto);

    void pushFCMNotificationForWEB(ContactDocument contactDocument, MessageDocument messageDocument, BusinessDTO businessDTO, Integer loggedInUserId, MessageDocument lastMessage , ReviewEvent.Review review);

    void bulkActionRefreshStatusOnWeb(Integer accountId, Integer userId);
	
	void mirrorOnMobileTidyUpConversations(Map<Integer, List<Integer>> mobileMirrorData, Integer accountId);

	void pushToFirebase(ContactDocument contactDocument, SuggestionHolder suggestionHolder, Boolean isReceivedDuringBusinessHours,Boolean showTimer);


	void pushInstallationStatusToFirebase(Integer widgetId, FirebaseWebchatInstallationResponse response);

	void pushToFireBaseSync(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId,
			MessageDocument lastMessage,boolean notification);

	void pushToFireBase(ContactDocument contactDocument, MessageDocument messageDocument, boolean sendWebNotification,
			boolean sendMobileNotification, Integer loggedInUserId, MessageDocument lastMessage);

    void mirrorOnSecureChat(ContactDocument contactDocument, MessageDocument messageDocument);

	void pushToFireBaseAccountBucket(ContactDocument contactDocument, MessageDocument messageDocument, Integer loggedInUserId,
						MessageDocument lastMessage, boolean notification);

	void twinOnWeb(Integer accountId, Integer businessId, Integer mcId);
	
	void pushSwitchWebchatConversationEventToFirebase(SwitchConversationFirebaseEvent switchConversationFirebaseEvent);

}
