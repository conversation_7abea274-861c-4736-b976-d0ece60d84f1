package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.TwitterMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendTwitterMessageResponse;
import com.birdeye.messenger.service.impl.TwitterSendEventHandler;
import com.birdeye.messenger.enums.TwitterMessageStatusEnum;

import java.util.Map;

public interface TwitterEventService{
    SendTwitterMessageResponse sendTwitterCallToSocial(TwitterSendEventHandler handler,MessageDTO messageDTO,
                                                       String pageId);

    Boolean isTwitterSendAvailable(MessengerContact messengerContact,Integer routeId);

    TwitterMessage saveTwitterMessage(MessageDTO messageDTO,String senderId,String recipientId,String messageId,
                                      TwitterMessageStatusEnum status);

    String getTwitterIntegrationStatus(Integer businessId);

    
    Map<String,String> getBusinessIdAndBirdeyeCdnAttachmentUrl(Map<String,Object> request,String businessTwitterId);
}

