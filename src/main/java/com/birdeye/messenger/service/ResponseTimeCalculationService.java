package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.ResponseTimeOutBox;

public interface ResponseTimeCalculationService {

    ResponseTimeOutBox processResTimeMessage(MessengerContact messengerContact, String currMsgId, Long currMsgTime, Integer source, String currMsgType);

    void saveResponseTime(ResponseTimeOutBox responseTimeOutBox);
}
