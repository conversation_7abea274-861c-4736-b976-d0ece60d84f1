package com.birdeye.messenger.service;

import java.util.Map;

import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ReceptionistTextToSpeechTokenReplaceRequest;
import com.birdeye.messenger.dto.ReceptionistTextToSpeechTokenReplaceResponse;
import com.birdeye.messenger.dto.VoiceCallDto;
import com.birdeye.messenger.sro.BusinessMissedCallSetting;
import com.birdeye.messenger.sro.ReceptionistConfigurationMessage;

/**
 * <AUTHOR>
 *
 */
public interface ReceptionistService {
	
	public static final String SENT_VIA_BIRDEYE = "( Sent via Birdeye.com )";

	Map<?,?> getReceptionistConfiguration(long enterpriseId) throws Exception;

	void saveOrUpdateReceptionistConfiguration(Long enterpriseId,
			ReceptionistConfigurationMessage request);

	Map<String, Object> getReceptionistGreeting(String businessPhoneNumber) throws Exception;
	
	String getReceptionistAutoReplyMessage(BusinessDTO business, VoiceCallDto dto, String cName);
	
	boolean isReceptionistEnabledCheck(BusinessDTO businessDTO);

	String getFormattedGreeting(BusinessDTO businessDTO, String greeting);

	void clearReceptionistCache();

	String extractFirstNameOnly(String fullName);
	
	String getReceptionistMissedCallAutoReplyInbox(BusinessDTO businessDTO);

	String getReceptionistMissedCallAutoReplyNonInbox(BusinessDTO businessDTO);

	String getReceptionistConfiguredMissedCallAutoReply(BusinessDTO businessDTO, String cName);

	BusinessReceptionistConfiguration getBusinessReceptionistConfiguration(BusinessDTO businessDTO);

	void updateMissedCallSetting(BusinessMissedCallSetting request);

	ReceptionistTextToSpeechTokenReplaceResponse replaceTokensForTextToSpeechMessage(ReceptionistTextToSpeechTokenReplaceRequest request);

}