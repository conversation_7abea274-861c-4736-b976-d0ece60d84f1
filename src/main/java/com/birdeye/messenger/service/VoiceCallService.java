package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dao.entity.VoiceCall;
import com.birdeye.messenger.dto.BizAppVoiceCallRequest;
import com.birdeye.messenger.dto.VoiceCallDto;

/**
 * Service interface for voice call related operations.
 * 
 * <AUTHOR>
 *
 */
public interface VoiceCallService {
	
	public VoiceCallDto save(VoiceCallDto dto);

    void deleteVoiceCallsUsingCustomerId(Integer customerId);

    Integer countVoiceCallForBusinessIdsAndCreateDate(BizAppVoiceCallRequest request);

    Integer countVoiceCallForBusinessIds(BizAppVoiceCallRequest request);

	List<VoiceCall> findByCallSid(String callSid);

    void updateVoicecallMessageBody(String messageBody, Integer messageId);
}
