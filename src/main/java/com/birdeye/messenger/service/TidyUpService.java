package com.birdeye.messenger.service;

import java.text.DateFormat;
import java.util.List;

import com.birdeye.messenger.dto.GetMessengerContactDTO;
import com.birdeye.messenger.external.dto.TidyUpResponse;
import com.birdeye.messenger.sro.TidyUpConversationRequest;

/**
 * <AUTHOR>
 *
 */
public interface TidyUpService {

	TidyUpResponse tidyUpConversations(TidyUpConversationRequest request, Integer userId, Integer accountId);


	void generateActivityAndMirror(List<GetMessengerContactDTO> messengerContacts, Integer userId,
			Integer accountId, DateFormat df, Integer status, String bucket);

	void triggerAsyncUnreadOperation(TidyUpConversationRequest request,
			List<GetMessengerContactDTO> messengerContactsIsReadTrue, Integer userId, Integer accountId);

}