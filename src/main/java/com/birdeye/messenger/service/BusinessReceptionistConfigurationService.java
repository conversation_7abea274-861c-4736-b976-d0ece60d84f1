package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.dto.BusinessDTO;

/**
 * <AUTHOR>
 *
 */
public interface BusinessReceptionistConfigurationService {

	BusinessReceptionistConfiguration getReceptionistConfiguration(Long enterpriseId);

	BusinessReceptionistConfiguration getDefaultBusinessChatWidgetConfig();

	void saveReceptionistConfig(BusinessReceptionistConfiguration config);

	BusinessReceptionistConfiguration getConfigOfAccountFromLocation(BusinessDTO businessDTO);
	
	BusinessReceptionistConfiguration getMissedCallConfigInbox();

	void clearDefaultBusinessChatWidgetConfig();

	BusinessReceptionistConfiguration getMissedCallConfigNonInbox();

	void clearMissedCallInboxConfig();

	void clearMissedCallNonInboxConfig();
	
}
