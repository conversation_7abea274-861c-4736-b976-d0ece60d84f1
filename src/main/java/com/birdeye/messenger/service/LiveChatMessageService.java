package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.LiveChatMessage;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.enums.MessageStatusEnum;

public interface LiveChatMessageService {
  	LiveChatMessage saveLiveChatMessageFromCustomer(LiveChatMessageObject incomingCustomerMessage,MessageStatusEnum messageStatus);

	void encrypt(LiveChatMessage liveChatMessage);

	LiveChatMessage saveLiveChatMessage(SendMessageDTO sendMessageDTO,MessageStatusEnum messageStatus);

}
