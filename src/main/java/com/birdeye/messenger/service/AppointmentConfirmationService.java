package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.AppointmentConfirmationContextCachedDataDto;
import com.birdeye.messenger.dto.BusinessDTO;

public interface AppointmentConfirmationService {

    void checkAndConfirmAppointment(BusinessDTO business, MessengerContact messengerContact, String message);

    void checkAndDeleteAppointmentConfirmationContext(BusinessDTO business,MessengerContact messengerContact);

    void updateAppointmentConfirmationContext(String accountId, String businessId, String mcId, String altAppointmentId, Long reviewRequestId,Long eventTime, String pmsAppointmentId);

    Boolean checkAppointmentConfirmationContext(BusinessDTO business, MessengerContact messengerContact, String message);

    AppointmentConfirmationContextCachedDataDto  checkAndGetAppointmentConfirmationContext(BusinessDTO business, MessengerContact messengerContact);

    }
