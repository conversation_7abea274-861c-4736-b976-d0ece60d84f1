package com.birdeye.messenger.service;

import java.util.Arrays;
import java.util.Objects;

import com.birdeye.messenger.exception.MessengerException;

public enum SmsSource {
	//TODO: Need to have separate value for MSGR_SMS
	// @formatter:off
	MSGR_SMS(1,"SMS sent via messenger"),
	RECEIVE_SMS(1,"SMS received from Twilio webhook."),
	CAMPAIGN_SMS(0,"SMS sent via Campaign"),
	BIZSITE_WEBCHAT(2,"SMS received via business site webchat"),
	MSITE_WEBCHAT(6,"SMS received via microsite webchat");
	// @formatter:on

	private SmsSource(Integer value, String description) {
		this.value = value;
		this.description = description;
	}

	private Integer value;
	private String description;

	public Integer getValue() {
		return value;
	}

	public String getDescription() {
		return description;
	}
	
	public static SmsSource fromValue(Integer value) {
		Objects.requireNonNull(value);
		return Arrays.stream(SmsSource.values()).filter(source -> value.equals(source.getValue())).findFirst()
				.orElseThrow(() -> new MessengerException("Invalid value of SMS source Id"));
	}
}