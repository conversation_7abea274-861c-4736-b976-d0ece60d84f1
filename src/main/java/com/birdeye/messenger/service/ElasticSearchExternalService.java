package com.birdeye.messenger.service;

import java.util.List;

import org.elasticsearch.action.DocWriteRequest.OpType;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;

import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.Identifiable;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.es.sro.ESInsertRequest;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.es.sro.ElasticUpdateQueryRequest.SmartInboxOwnerBuilder;

public interface ElasticSearchExternalService {

	<T> List<T> searchByQuery(ESRequest esRequest, Class<T> documentType);

	boolean deleteByQuery(ESRequest esRequest);

	boolean updateDocument(ESRequest esRequest, boolean docAsUpsert);

	SearchResponse searchConversations(String query, String routing);

	SearchResponse searchMessages(String query, String routing);

	<T> ElasticData getDataFromElastic(ESRequest esRequest, Class<T> documentType);

	<T> ElasticData getDataFromElasticWithInnerHits(ESRequest esRequest, Class<T> documentType);

	<T> List<T> searchByQueryForBidMigration(ESRequest esRequest, Class<T> documentType);

	<T extends Identifiable> boolean bulkUpsert(BulkUpsertPayload<T> payload);

	Long getDocumentCount(ESRequest esRequest);

	SearchResponse getSearchResult(ESRequest esRequest);

	boolean updateByQuery(ESUpdateByQueryRequest esRequest);

	SearchResponse getESSearchResultForQuery(ESRequest esRequest);

	SearchResponse readMoreFromSearch(String scrollId, String sessionTime);

	<T extends Identifiable> boolean performBulkRequest(BulkUpsertPayload<T> bulkUpsertPayload, OpType opType);

	<T> List<T> getResultUsingQueryBuilder(ESQueryBuilderRequest<T> esQueryBuilderRequest);

	<T> IndexResponse insertESDocument(ESInsertRequest<T> esInsertRequest);

	<T> T findDocumentById(ESFindByIdRequest<T> esFindByIdRequest);

	String deleteDocumentById(ESDeleteByIdRequest esDeleteByIdRequest);

	<T> T upsertESDocument(ESUpsertRequest<T> esUpsertRequest);

	public boolean updateByQueryWithRefresh(ESUpdateByQueryRequest esRequest, boolean refresh);

	<T> List<T> findDocumentByField(ESFindByFieldRequest<T> esFindRequest);

	BulkByScrollResponse updateSmartInboxOwner(SmartInboxOwnerBuilder smartInboxOwnerBuilder);
	
	void deleteDocumentByQueryAsync(ESDeleteByIdRequest deleteByIdRequests);

	boolean deleteByQueryWithRefresh(ESRequest esRequest, boolean refresh);

	boolean reindexByQuery(BoolQueryBuilder esRequest, String sourceIndex, String targetIndex);

	<T extends Identifiable> boolean performBulkRequestWithRefresh(BulkUpsertPayload<T> bulkUpsertPayload,
			OpType opType, RefreshPolicy refreshPolicy);

	boolean clearScrollContext(String scrollId);

	SearchResponse getSearchResult(SearchRequest searchRequest);
}
