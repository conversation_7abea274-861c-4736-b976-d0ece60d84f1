package com.birdeye.messenger.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class RedisHandler {

	@Autowired
	RedisTemplate<String, String> redisTemplate;

	@Autowired
	@Qualifier("redisPlatformTemplate")
	RedisTemplate<String, String> redisPlatformTemplate;

	/**
	 * Update redis cache for changes in messages, conversation tagging,
	 * integration status failure etc.
	 *
	 * @param accountId
	 *                  - Enterprise/SMB ID
	 */

	private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

	public void updateLastEventCache(String accountId) {
		try {
			String key = Constants.REDIS_KEY_MESSENGER_TRIGGER_TIME + ":" + accountId;
			redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()), 30, TimeUnit.MINUTES);
		} catch (Exception e) {
			log.error("Redis Exception : accountId {} updateLastEventCache {}", accountId, e);
		}
	}

	public long getLastEventFromCache(String accountId) {
		String key = Constants.REDIS_KEY_MESSENGER_TRIGGER_TIME + ":" + accountId;
		String value = null;
		try {
			value = redisTemplate.opsForValue().get(key);
		} catch (Exception e) {
			log.error("Redis Exception : accountId {} updateLastEventCache {}", accountId, e);
		}
		if (value == null) {
			return 0;
		} else {
			return Long.parseLong(value);
		}
	}

	/**
	 *
	 * Get value for given field from hash at specified key.
	 *
	 * @see <a href="http://redis.io/commands/hget">Redis Documentation:
	 *      HGET</a>
	 *
	 * @param key
	 *              must not be null.
	 * @param field
	 *              must not be null
	 * @return
	 */
	public Object getData(String key, String field) {
		// RedisHashCommands
		try {
			return redisTemplate.opsForHash().get(key, field);
		} catch (Exception e) {
			log.error("Redis Exception : getData for key {} exception {}", key, e);
		}
		return null;
	}

	public void putData(String key, String hashKey, Object value) {
		// RedisHashCommands
		try {
			redisTemplate.opsForHash().put(key, hashKey, value);
		} catch (Exception e) {
			log.error("Redis Exception : putData key {} hashKey {} exception {}", key, hashKey, e);
		}
	}

	public void putList(String key, List<String> values) {
		// RedisListCommands
		redisTemplate.opsForList().rightPushAll(key, values);
	}

	/**
	 * Update FB Status in Redis cache for businessId to be used in
	 * notifications, refresh and conversations
	 *
	 * @param <T>
	 * @param businessId
	 * @param value
	 * @throws JsonProcessingException
	 */
	public <T> void updateFbStatusCache(String businessId, T value) throws JsonProcessingException {
		try {
			String val = convertToJSONStr(value);
			String key = Constants.BIZ_FB_STATUS + ":" + businessId;
			redisTemplate.opsForValue().set(key, val, 30, TimeUnit.MINUTES);
		} catch (Exception e) {
			log.error("Redis Exception : updateFbStatusCache businessId {} exception {}", businessId, e);
		}
	}

	/**
	 * Update IG Status in Redis cache for businessId to be used in
	 * notifications, refresh and conversations
	 *
	 * @param <T>
	 * @param businessId
	 * @param value
	 * @throws JsonProcessingException
	 */
	public <T> void updateIgStatusCache(String businessId, T value) throws JsonProcessingException {
		try {
			String val = convertToJSONStr(value);
			String key = Constants.BIZ_IG_STATUS + ":" + businessId;
			redisTemplate.opsForValue().set(key, val, 30, TimeUnit.MINUTES);
		} catch (Exception e) {
			log.error("Redis Exception : updateIgStatusCache businessId {} exception {}", businessId, e);
		}
	}

	public <T> void updateTwitterStatusCache(String businessId, T value) throws JsonProcessingException {
		try {
			String val = convertToJSONStr(value);
			String key = Constants.BIZ_TWITTER_STATUS + ":" + businessId;
			redisTemplate.opsForValue().set(key, val, 30, TimeUnit.MINUTES);
		} catch (Exception e) {
			log.error("Redis Exception : updateTwitterStatusCache businessId {} exception {}", businessId, e);
		}
	}

	private String convertToJSONStr(Object data) throws JsonProcessingException {
		if (data instanceof String) {
			return data.toString();
		}
		return OBJECT_MAPPER.writeValueAsString(data);
	}

	public String getIgStatusFromCache(String selectedBusinessId) {
		try {
			String key = Constants.BIZ_IG_STATUS + ":" + selectedBusinessId;
			String value = redisTemplate.opsForValue().get(key);
			return value;
		} catch (Exception e) {
			log.error("Redis Exception : getIgStatusFromCache businessId {} exception {}", selectedBusinessId, e);
		}
		return null;
	}

	public String getTwitterStatusFromCache(String selectedBusinessId) {
		try {
			String key = Constants.BIZ_TWITTER_STATUS+ ":" + selectedBusinessId;
			String value = redisTemplate.opsForValue().get(key);
			return value;
		} catch (Exception e) {
			log.error("Redis Exception : getTwitterStatusFromCache businessId {} exception {}", selectedBusinessId, e);
		}
		return null;
	}

	public String getFbStatusFromCache(String selectedBusinessId) {
		try {
			String key = Constants.BIZ_FB_STATUS + ":" + selectedBusinessId;
			String value = redisTemplate.opsForValue().get(key);
			return value;
		} catch (Exception e) {
			log.error("Redis Exception : getFbStatusFromCache businessId {} exception {}", selectedBusinessId, e);
		}
		return null;
	}

	public void evictPlatformCache(String key) {
		try {
			redisPlatformTemplate.delete(key);
		} catch (Exception e) {
			log.error("Redis Exception : evictPlatformCache key {}", key, e);
		}
	}

	public void evictWebChatCache(List<String> keys) {
		try {
			redisTemplate.delete(keys);
		} catch (Exception e) {
			log.error("Redis Exception : evictWebChatCache keys {} exception {}", keys, e);
		}
	}

	public void evictPlatformCaches(List<String> keys) {
		try {
			redisPlatformTemplate.delete(keys);
		} catch (Exception e) {
			log.error("Redis Exception : evictPlatformCaches keys {}", keys, e);
		}
	}

	public void evictMessengerCustomerCache(Integer customerId) {
		String key = Constants.CUSTOMER_CACHE + "::CID-" + customerId; // msgr_customer::CID-3324626
		try {
			Boolean deleted = redisTemplate.delete(key);
			if (Objects.nonNull(deleted) && deleted) {
				log.info("msgr_customer cache evicted for customer id: {}", customerId);
			}

		} catch (Exception e) {
			log.error("Redis Exception : evictMessengerCustomerCache customerId {} exception {}", customerId, e);
		}

	}

	public void evictInstagramStatusCache(Integer businessId) {
		try {
			String key = Constants.BIZ_IG_STATUS + ":" + businessId;
			Boolean deleted = redisTemplate.delete(key);
			if (Objects.nonNull(deleted) && deleted) {
				log.info("msgr_instagram status cache evicted for businessId : {}", businessId);
			}
		} catch (Exception e) {
			log.error("Redis Exception : evictInstagramStatusCache businessId {} businessId exception {}", businessId,
					e);
		}
	}

	public <T> void setOpsForValueWithExpiry(String redisKey, T value, Long timeout, TimeUnit unit) {
		log.info("Setting value in redis for key {} with timeout {} and timeUnit {}", redisKey, timeout, unit);
		try {
			if (timeout != null && timeout > 0 && unit != null) {
				redisTemplate.opsForValue().set(redisKey, convertToJSONStr(value), timeout, unit);
			} else {
				redisTemplate.opsForValue().set(redisKey, convertToJSONStr(value));
			}
		} catch (Exception e) {
			log.error("Exception while setting value in redis for key {}", redisKey, e);
		}
	}

	public <T> void setOpsForValue(String redisKey, T value) {
		try {
			setOpsForValueWithExpiry(redisKey, value, null, null);
		} catch (Exception e) {
			log.error("Exception while setting value in redis for key {}", redisKey, e);
		}
	}

	public String getKeyValueFromRedis(String redisKey) {
		String value = null;
		try {
			value = redisTemplate.opsForValue().get(redisKey);
		} catch (Exception e) {
			log.error("Exception while getting value from redis for key {}", redisKey, e);
		}
		return value;
	}

	public boolean accureLockOnRedisKey(String redisKey, String value) {
		boolean accureLock = false;
		try {
			return redisTemplate.opsForValue().setIfAbsent(redisKey, value);
		} catch (Exception e) {
			log.error("Exception while preformaing setIfAbsent opeartion redis on key {}", redisKey, e);
		}
		return accureLock;
	}

	public void deleteKey(String redisKey) {
		try {
			redisTemplate.delete(redisKey);
		} catch (Exception e) {
			log.error("Exception while deleting redis key {}", redisKey, e);
		}
	}

	public Long getRedisKeyTTL(String redisKey, TimeUnit timeUnit) {
		Long redisTTLKey = null;
		try {
			redisTTLKey = redisTemplate.getExpire(redisKey, timeUnit);
		} catch (Exception e) {
			log.error("Exception while getting redis TTL key {}", redisKey, e);
		}
		return redisTTLKey == null ? 0L : redisTTLKey;
	}

	public void setOpsForHash(String redisKey, Object hashKey, Object hashValue) {
		try {
			redisTemplate.opsForHash().put(redisKey, hashKey, hashValue);
		} catch (Exception e) {
			log.error("Exception while setting value in redis for key {} and hashKey {}", redisKey,
					hashKey, e);
		}
	}

	public Object getOpsForHash(String redisKey, Object hashKey) {
		Object result = null;
		HashOperations<String, Object, Object> opsForHash = redisTemplate.opsForHash();
		try {
			result = opsForHash.get(redisKey, hashKey);
		} catch (Exception e) {
			log.error("Exception while getting value from redis for key {} and hashKey {}", redisKey, hashKey, e);
		}
		return result;
	}

	public void updateLastProcessedConvesationId(int convesationId) {
		String key = Constants.REDIS_KEY_PREFIX_LAST_CONVERSATION_ID_PROCESSED;
		try {
			redisTemplate.opsForValue().set(key, "" + convesationId, 30, TimeUnit.DAYS);
		} catch (Exception e) {
			log.error("Redis Exception : updateLastProcessedConvesationId {} exception {}", convesationId, e);
		}
	}

	public int getLastProcessedConversationId() {
		String key = Constants.REDIS_KEY_PREFIX_LAST_CONVERSATION_ID_PROCESSED;
		String value = null;
		try {
			value = redisTemplate.opsForValue().get(key);
		} catch (Exception e) {
			log.error("Redis Exception : getLastProcessedConversationId", e);
		}
		if (value == null) {
			return 0;
		} else {
			return Integer.parseInt(value);
		}
	}

	public void updateConversationPagesFailed(String pages) {
		try {
			String key = Constants.REDIS_KEY_PREFIX_CONVERSATION_PAGES_FAILED;
			redisTemplate.opsForValue().set(key, pages, 30, TimeUnit.DAYS);
		} catch (Exception e) {
			log.error("Redis Exception : updateConversationPagesFailed", e);
		}
	}

	public String getConversationPagesFailed() {
		String key = Constants.REDIS_KEY_PREFIX_CONVERSATION_PAGES_FAILED;
		String value = null;
		try {
			value = redisTemplate.opsForValue().get(key);
		} catch (Exception e) {
			log.error("Redis Exception : getConversationPagesFailed", e);
		}
		if (value == null) {
			return "";
		} else {
			return value;
		}
	}

	public <T> void updateGMBStatusCache(String businessId, T value) throws JsonProcessingException {
		String val = convertToJSONStr(value);
		String key = Constants.GMB_STATUS + ":" + businessId;
		try {
			redisTemplate.opsForValue().set(key, val, 1, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.error("Redis Exception : updateGMBStatusCache {} exception {}", businessId, e);
		}
	}

	public <T> void updateAppleStatusCache(String businessId, T value) throws JsonProcessingException {
		String val = convertToJSONStr(value);
		String key = Constants.APPLE_STATUS + ":" + businessId;
		try {
			redisTemplate.opsForValue().set(key, val, 1, TimeUnit.SECONDS);
		} catch (Exception e) {
			log.error("Redis Exception : updateAppleStatusCache businessId {} exception {}", businessId, e);
		}
	}

	public String getCachedGoogleStatus(String businessId) {
		String key = Constants.GMB_STATUS + ":" + businessId;
		try {
			return redisTemplate.opsForValue().get(key);
		} catch (Exception e) {
			log.error("Redis Exception : getCachedGoogleStatus businessId {} exception {}", businessId, e);
		}
		return null;
	}

	public String getCachedAppleStatus(String businessId) {
		String key = Constants.APPLE_STATUS + ":" + businessId;
		try {
			return redisTemplate.opsForValue().get(key);

		} catch (Exception e) {
			log.error("Redis Exception : getCachedAppleStatus businessId {} exception {}", businessId, e);
		}
		return null;
	}

	public Long executeLuaScript(DefaultRedisScript<Long> redisScript, List<String> keys, String args) {
		try {
			return redisTemplate.execute(redisScript, keys, args);
		} catch (Exception e) {
			log.error("Exception in executeLuaScript for key:{} and args:{} : {}", keys, args, e.getMessage());
		}
		return null;
	}

	public <T> T getDataFromSpecificBucketFromRedis(String cacheName, String key, Class<T> docType) {
		String finalKey = cacheName + ":" + key;
		String data = getKeyValueFromRedis(finalKey);
		try {
			if (StringUtils.isNotBlank(data)) {
				return OBJECT_MAPPER.readValue(data, docType);

			}
		} catch (IOException e) {
			log.error("error occurred while getDataFromSpecificBucketFromRedis : {} ", e.getMessage());
		}
		return null;

	}

	public void deletePatternMatchingKeysFromRedis(String pattern) {
		log.info("deletePatternMatchingKeysFromRedis called with pattern : {}", pattern);
		Set<String> keys = redisTemplate.keys(pattern);
		evictWebChatCache(new ArrayList<>(keys));
		log.info("deletePatternMatchingKeysFromRedis successfull for keys : {}", keys);
	}

	public void deleteValueFromHash(String key, String hashKey) {
		HashOperations<String, String, Object> hashOps = redisTemplate.opsForHash();
		hashOps.delete(key, hashKey);
	}

	
	public void removeKeyForSmartInbox(String accountId, String userId) {
		String key = generateKey(Constants.SMART_INBOX_FILTERS_CACHE, ":", accountId, userId);
		log.info("Smart inbox  cache evicted for accountId -{} and userId - {}", accountId, userId);
		redisTemplate.delete(key);
	}
	
	public void deleteKeysForAccount(String cacheName, String accountId) {
		String pattern = generateKey(cacheName, ":", accountId + "*");
	    Set<String> keysToDelete = redisTemplate.keys(pattern);
	    log.info("Evicting - {} cache for accountId -{} with pattern ",cacheName, accountId, pattern);
	    redisTemplate.delete(keysToDelete);
	}
	
	public void deleteKeysForUserId(String cacheName, String accountId, List<Integer> userIds) {
		Set<String> keysToDelete = userIds.stream().map(userId -> generateKey(cacheName, ":", accountId, String.valueOf(userId)))
				.collect(Collectors.toSet());

		log.info("Evicting total - [{}] keys from [{}] for account Id", keysToDelete.size(), cacheName);
		redisTemplate.delete(keysToDelete);

	}

	private String generateKey(String cacheName, String delimiter, String... keys) {

		StringBuilder keyBuilder = new StringBuilder();

		// Append CACHE_NAME
		keyBuilder.append(cacheName).append("::");

		// Append dynamic keys
		for (Object key : keys) {
			keyBuilder.append(key.toString()).append(delimiter);
		}

		// Remove the trailing colon
		keyBuilder.deleteCharAt(keyBuilder.length() - 1);

		return keyBuilder.toString();

	}
	
    public void addMultipleKeyValuePairsInCache(Map<String, String> keysAndValues) {
        log.info("addMultipleKeyValuePairsInCache called with keysAndValues : {}",
                keysAndValues);
        try {
            redisTemplate.opsForValue().multiSet(keysAndValues);
            log.info("addMultipleKeyValuePairsInCache successfully");
        } catch (Exception e) {
            log.error("error : {} occurred in addMultipleKeyValuePairsInCache", e.getMessage());
        }
    }

	/**
	 * implement rate limiting for calls to GPT per customer
	 * @param customerId
	 * @return True if the customer is allowed to make the call, False otherwise
	 * 
	 * If the key does not exist, create the key, set it to "1" with expiration time of 24hrs.
	 * This means that the customer can make the first call and the new count will reset after 24 hours.
	 * If the key already exists, it increments the count.
	 * The method checks whether the count has exceeded the rate limit and returns the result.
	 */
	public boolean isCallingGPTAllowed(String customerId) {
		String key = Constants.GPT_RATE_LIMIT + ":" + customerId;
		Integer limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("gpt_rate_limit_per_customer", 25); // Max calls allowed per day

		ValueOperations<String, String> ops = redisTemplate.opsForValue();

		if (!redisTemplate.hasKey(key)) {
			// If the key doesn't exist, set it with an expiration time of 24hrs
			ops.set(key, "1", 24, TimeUnit.HOURS);
			return true;
		}

		// key found, increase the count
		long count = ops.increment(key, 1);

		// Check if customer has exceeded the limit
		return count <= limit;
	}
	
	public boolean isReplyViaEmailAllowed(String fromEmail) {
	    String key = Constants.REPLY_VIA_EMAIL_KEY + ":" + fromEmail;
	    String blockKey = Constants.REPLY_VIA_EMAIL_KEY + ":BLOCKED:" + fromEmail;

	    Integer limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("reply_via_email_limit", 10); // Max calls allowed per user
	    
	    ValueOperations<String, String> ops = redisTemplate.opsForValue();

	    if (redisTemplate.hasKey(blockKey)) {
	        return false; //User is already blocked
	    }
	    
		if (!redisTemplate.hasKey(key)) {
			ops.set(key, "1", 1, TimeUnit.MINUTES);
			return true;
		}

		//key found, increase the count
		long count = ops.increment(key, 1);

		if (count > limit) {
			// User exceeded the limit, block for 30 mins
	        ops.set(blockKey, "BLOCKED", 30, TimeUnit.MINUTES);
	        return false;
		}
		return true;
	}
}
