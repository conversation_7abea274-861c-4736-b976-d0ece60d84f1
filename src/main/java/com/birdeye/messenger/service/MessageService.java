package com.birdeye.messenger.service;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.dto.ConversationHistoryRequest;
import com.birdeye.messenger.dto.LiveChatFetchMessageRequest;
import com.birdeye.messenger.dto.MessageClientIpResponse;
import com.birdeye.messenger.dto.MessageRequest;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.UnansweredFaqAnsweredRequest;
import com.birdeye.messenger.dto.UnansweredFaqDeleteRequest;
import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.dto.report.GenericDownloadReportDTO;

public interface MessageService {

	MessageResponse getMessageV2(MessageRequest messageRequest, boolean isDelta, Integer userId, Integer accountId, String requestSource);

	MessageResponse getLiveChatMessages(LiveChatFetchMessageRequest liveChatFetchMessageRequest);

	boolean deleteMessagesByMcId(Integer mcId, Integer routingId);

	boolean isSingleReviewConversation(Integer conversationId, Integer accountId, Integer reviewId);

	void updateNewConversationIdInMessageDoc(Integer reviewId, Integer accountId, Integer id, Integer m_c_id);

	boolean deleteReviewByIdAndMcId(Integer reviewId,Integer mcId, Integer routingId);

	boolean deleteMessagesByConversationId(Integer conversationId, Integer routingId);

	void deleteActivitiesByConversationId(Integer conversationId, Integer accountId);
	boolean deleteSurveyResponseById(Integer surveyResponseId, Integer routingId);

	void deleteActivitiesByConversationIds(List<Integer> conversationIds, Integer accountId);

	GenericDownloadReportDTO getRobinUnansweredQuestionsV1(Integer accountId, Integer pageSize, Integer startIndex);

	GenericDownloadReportDTO getRobinUnansweredQuestions(Integer accountId, Integer pageSize, Integer startIndex, String queryFile);

	String getMcIdByReviewId(Integer reviewId, Integer accountId);

	String getAgentLastResponseTime(Integer mcId, Integer accountId);

	MessageResponse getConversationHistoryMessages(ConversationHistoryRequest request, Integer userId,
			Integer accountId) throws Exception;

	MessageClientIpResponse getClientIps(MessageRequest request);

	void deleteMessagesDataFromDBOnCustomerDelete(Integer mcId, Integer cId);

	boolean deleteMessagesByMcIdWithRefresh(Integer mcId, Integer routingId, boolean refresh);

	GenericDownloadReportDTO getRobinUnansweredFaq(DownloadReportFilter downloadFilter, Integer pageSize, Integer startIndex);

	void updateRobinUnansweredFaqDelete(@NotNull @Valid UnansweredFaqDeleteRequest updateRequest);

	void updateRobinUnansweredFaq(UnansweredFaqAnsweredRequest updateRequest);

	void migrateUnansweredQualifiedQuestions();

	void updateReplyViaEmailStatus(String docId, boolean replyViaEmail);

}

