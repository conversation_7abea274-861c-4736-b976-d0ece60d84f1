package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.enums.Source;

public interface RobinService {
	
	void sendRobinReply(MessageDTO messageDTO,String oldLmsgOn) throws Exception;

	RobinAutoReplyConfig getRobinAutoReplyConfigForBusiness(Integer businessId,Integer enterpriseId, Source source);

	void updateChatSession(Integer accountId,Integer mcId,UserDTO userDTO,String channel);

}
