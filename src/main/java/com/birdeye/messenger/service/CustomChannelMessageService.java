package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.CustomChannelMessage;
import com.birdeye.messenger.dto.CustomChannelMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;


public interface CustomChannelMessageService {
    
   
    CustomChannelMessage saveCustomChannelMessage(CustomChannelMessageDTO customChannelMessageDTO, String businessNumber, String messengerContactId, String communicationDirection);
    
  
    CustomChannelMessage findById(Integer id);
    
  
    CustomChannelMessage save(CustomChannelMessage message);

    
    CustomChannelMessage saveCampaignCustomChannelMessage(SendM<PERSON>ageD<PERSON> sendMessageDTO, String businessNumber, String messengerContactId);
}
