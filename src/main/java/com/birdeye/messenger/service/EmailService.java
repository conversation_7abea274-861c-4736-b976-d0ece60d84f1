/**
 *
 */
package com.birdeye.messenger.service;

import java.util.List;

import com.birdeye.messenger.dao.entity.Email;
import com.birdeye.messenger.dto.EmailMessageDTO;
import com.birdeye.messenger.dto.EmailMessageDocument;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;

/**
 * <AUTHOR>
 *
 */
public interface EmailService {

	Email saveEmail(SendMessageDTO sendMessageDTO, String sender, String recipient);

	Email saveEmail(EmailMessageDTO emailMessageDTO, String sender, String recipient);

	List<Email> findByCustomerId(Integer customerId);

	void saveEmail(Email email);

	Email findById(Integer id);

	Email saveCampaignEmail(SendMessageDTO sendMessageDTO, String sender, String recipient);

	Email findByReviewRequestId(Long reviewRequestId);

	void deleteEmailByCustomerId(Integer customerId);

	List<EmailMessageDocument> prepareFormattedEmailMessages(List<MessageDocument> messageDocuments,
			ContactDocument contactDocument, String timeZone);

	void updateEmailMessageBody(String messageBody,Integer messageId);
}
