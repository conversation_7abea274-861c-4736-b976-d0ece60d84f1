package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.TeamConfigForRoundRobin;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.RoundRobinActivateRequest;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.sro.DeleteTeamEvent;


public interface RoundRobinAssignmentService {

	TeamConfigForRoundRobin getTeamConfigForRoundRobin(RoundRobinActivateRequest roundRobinActivateRequest);
	
	void createRoundRobinConfig(RoundRobinActivateRequest roundRobinActivateRequest);

	void updateRoundRobinConfig(RoundRobinActivateRequest roundRobinActivateRequest, TeamConfigForRoundRobin teamConfigForRoundRobin);
	
	void disableRoundRobin(Integer accountId);

	void deleteTeam(DeleteTeamEvent deleteTeamEvent);
	
	void goForRoundRobinAssignment(BusinessDTO businessDto, ContactDocument contactDocument);

}
