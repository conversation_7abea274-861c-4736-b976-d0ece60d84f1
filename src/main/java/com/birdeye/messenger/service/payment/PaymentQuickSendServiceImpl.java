package com.birdeye.messenger.service.payment;

import java.util.Objects;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.AddContactMessengerMessage;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.service.MessengerDashboardService;
import com.birdeye.messenger.service.MessengerEventHandlerService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentQuickSendServiceImpl implements PaymentQuickSendService {

	private final PaymentCardReaderService paymentCardReaderService;
	private final MessengerDashboardService messengerDashboardService;
	private final PaymentManualCardEntryService paymentManualCardEntryService;
	private final MessengerEventHandlerService messengerEventHandlerService;
	private final MessengerContactRepository messengerContactRepository;
	
	@Override
	public MessageResponse handleQuickSendPayment(Integer accountId, AddContactMessengerMessage requestDTO) throws Exception {
		requestDTO.setCustomerId(requestDTO.getSendMessage().getCustomerId());
		requestDTO.getSendMessage().setCustomerId(null);
		if(null == requestDTO.getSendMessage().getCustomerId() &&
				Objects.nonNull(requestDTO.getNewContact())) {
			log.debug("Quick Send - Initiating payments to new contact");
			return messengerDashboardService.addNewContactAndSendSms(accountId, requestDTO);
		}
		
		SendMessageDTO sendMessage =requestDTO.getSendMessage();
		
		Optional<MessengerContact> optMessengerContact = messengerContactRepository.findByCustomerId(requestDTO.getSendMessage().getCustomerId());
		if(optMessengerContact.isPresent()) {
			sendMessage.setToCustomerId(optMessengerContact.get().getId().toString());
			requestDTO.getSendMessage().setMessengerContact(optMessengerContact.get());
		}
		
		if (requestDTO.getSource() == null) {
			requestDTO.setSource(1);
		}
		
		sendMessage.setBusinessIdentifierId(String.valueOf(sendMessage.getFromBusinessId()));
		sendMessage.setUserId(requestDTO.getUserId());
		sendMessage.setSource(requestDTO.getSource());
		
		
		PaymentUiRequest paymentRequest = requestDTO.getSendMessage().getPaymentRequest();
		log.debug("Quick Send - Initiating payments to customer - {} with method - {} ",
				requestDTO.getSendMessage().getCustomerId(), paymentRequest.getMethod());
		
		MessageResponse response;
		if(Objects.nonNull(paymentRequest) && "TERMINAL".equals(paymentRequest.getMethod())){
			response = paymentCardReaderService.handleCardReaderPayment(sendMessage);
		}else if(Objects.nonNull(paymentRequest) && ( "CARD_ENTRY".equals(paymentRequest.getMethod()) || "CARD_ON_FILE".equals(paymentRequest.getMethod()))){
			response = paymentManualCardEntryService.handleManualCardEntryPayment(sendMessage);
		} else {
			response = messengerEventHandlerService.handleEvent(sendMessage);
		}
		response.setMcId(Objects.nonNull(sendMessage.getMessengerContact()) ? sendMessage.getMessengerContact().getId() : response.getMcId());
		response.setCustomerId(Objects.nonNull(sendMessage.getCustomerDTO()) ? sendMessage.getCustomerDTO().getId() : response.getCustomerId());
		
		return response;
	}

}
