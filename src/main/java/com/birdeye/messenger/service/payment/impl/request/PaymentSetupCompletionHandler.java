package com.birdeye.messenger.service.payment.impl.request;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.TimeZone;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.dto.payment.PaymentEvent.PaymentEventName;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentInfo.SubscriptionInfo;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.payment.PaymentEventHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.google.common.base.Preconditions;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PaymentSetupCompletionHandler implements PaymentEventHandler  {

	@Autowired MessengerContactService messengerContactService;
	@Autowired BusinessService businessService;
	@Autowired ConversationActivityService conversationActivityService;
	@Autowired ElasticSearchExternalService elasticSearchService;
	@Autowired KafkaService kafkaService;

	@Override
	public void handle(PaymentEvent paymentEvent) {
		validate(paymentEvent);
		MessengerContact mc = messengerContactService.findById(paymentEvent.getConversationId());
		BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());

		SubscriptionInfo.SubscriptionInfoBuilder subscriptionInfoBuilder = SubscriptionInfo.builder()
				.id(paymentEvent.getId())
				.lastFourDigit(paymentEvent.getSubscriptionEventDetails().getLast4())
				.paymentVia(paymentEvent.getSubscriptionEventDetails().getPaymentVia());

		if(null != paymentEvent.getSubscriptionEventDetails().getDueDate()) {
			DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
			Date dueDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentEvent.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			subscriptionInfoBuilder.dueDate(df.format(dueDate));	
		}


		MessangerBaseFilter filter = new MessangerBaseFilter();
		filter.setAccountId(businessDTO.getAccountId());
		filter.setConversationId(mc.getId());
		filter.setCount(1);

		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
		ContactDocument contactDocument = contactDocuments.get(0);

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
		List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();


		ContactDocument.Payment payment = createContactDocumentPaymentInfo(paymentEvent);
		paymentMetaList.add(payment);
		lastMessageMetadataPOJO.setPayment(paymentMetaList);

		if(contactDocument.getPayments() == null || contactDocument.getPayments().isEmpty()) {
			contactDocument.setPayments(Collections.singletonList(payment));
		} else {
			contactDocument.getPayments().add(payment);   
		}
		contactDocument.setHide(false); // to make contactDocument visible on filters


		lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
		mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

		contactDocument.setL_payment_on(paymentEvent.getTimestamp());
		SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);

		contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));

		mc.setTag(MessageTag.INBOX.getCode());
		contactDocument.setC_tag(MessageTag.INBOX.getCode());


		mc.setIsRead(false);
		mc.setViewedBy(null);
		contactDocument.setViewedBy(new ArrayList<>());
		contactDocument.setC_read(false);


		PaymentInfo paymentInfo = new PaymentInfo();
		paymentInfo.setStatus(PaymentStatus.UPDATE_PAYMENT_METHOD);		
		paymentInfo.setAmount(paymentEvent.getAmount());
		paymentInfo.setInvoiceNumber(paymentEvent.getInvoiceNumber());

		paymentInfo.setMethod(paymentEvent.getPaymentMethod());
		paymentInfo.setItemDesc(paymentEvent.getItemDetail());
		paymentInfo.setMemo(paymentEvent.getMemo());
		paymentInfo.setPaymentType(paymentEvent.getPaymentType());
		paymentInfo.setSubscription(true);
		paymentInfo.setSubscriptionInfo(subscriptionInfoBuilder.build());
		paymentInfo.setCurrency(paymentEvent.getCurrencyCode());


		Date created = new Date(paymentEvent.getTimestamp());
		ActivityDto paymentCompletedActivity = ActivityDto.builder().mcId(mc.getId()).created(created).updated(created)
				.activityType(ActivityType.UPDATE_PAYMENT_METHOD)
				.activityMeantFor(ActivityMeantFor.CUSTOMER)
				.paymentInfo(paymentInfo)
				.type(MessageDocument.Type.CREATE)
				.accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
				.build();

		conversationActivityService.persistActivityInDatabase(paymentCompletedActivity, null);
		conversationActivityService.persistActivityInES(paymentCompletedActivity);	

		messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
		messengerContactService.saveOrUpdateMessengerContact(mc);
	}

	@Override
	public PaymentEventName getHandlerName() {
		return PaymentEvent.PaymentEventName.UPDATE_PAYMENT_METHOD;
	}

	@Override
	public void validate(PaymentEvent paymentEvent) {
		Preconditions.checkArgument(Objects.nonNull(paymentEvent.getAmount()), "Amount cannot be empty");
		Preconditions.checkArgument(Objects.nonNull(paymentEvent.getSubscriptionEventDetails()), "Subscription details cannot be null");
		if(!StringUtils.isEmpty(paymentEvent.getPaymentMethod())) {
			Preconditions.checkArgument(StringUtils.isNotBlank(paymentEvent.getItemDetail()), "Item Details cannot be empty");
		}
	}

	private ContactDocument.Payment createContactDocumentPaymentInfo(PaymentEvent paymentEvent){
		ContactDocument.Payment payment = new ContactDocument.Payment();
		payment.setPaymentId(paymentEvent.getSubscriptionEventDetails().getId());
		payment.setStatus(PaymentStatus.UPDATE_PAYMENT_METHOD);
		payment.setUpdatedAt(paymentEvent.getTimestamp());
		payment.setAmount(paymentEvent.getAmount());
		payment.setMethod(paymentEvent.getPaymentMethod());
		payment.setCurrency(paymentEvent.getCurrencyCode());
		return payment;
	}

}
