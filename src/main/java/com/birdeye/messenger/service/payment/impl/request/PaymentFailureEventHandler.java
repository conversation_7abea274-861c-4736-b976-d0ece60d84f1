package com.birdeye.messenger.service.payment.impl.request;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentInfo.SubscriptionInfo;
import com.birdeye.messenger.dto.payment.PaymentNotificationRequest;
import com.birdeye.messenger.enums.ActivityMeantFor;

import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.enums.PaymentType;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.payment.PaymentEventHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentFailureEventHandler implements PaymentEventHandler {

	private final MessengerContactService messengerContactService;
	private final BusinessService businessService;
	private final ElasticSearchExternalService elasticSearchService;
	private final FirebaseService firebaseService;

	@Autowired
	KafkaService kafkaService;
	@Autowired 
	ConversationActivityService conversationActivityService;

	@Override
	public void handle(PaymentEvent paymentEvent) {
		log.info("Payment Failure Event Handler - {}", paymentEvent);
		
		if(Objects.nonNull(paymentEvent.getPaymentType()) && paymentEvent.getPaymentType().equals("Card on file")){
			log.info("Received payment event for Card on file payment");
			return;
		}
		
		MessengerContact mc = messengerContactService.findById(paymentEvent.getConversationId());
		BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());

		MessangerBaseFilter filter = new MessangerBaseFilter();
		filter.setAccountId(businessDTO.getAccountId());
		filter.setConversationId(mc.getId());
		filter.setCount(1);

		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
		ContactDocument contactDocument = contactDocuments.get(0);
		List<ContactDocument.Payment> payments = contactDocument.getPayments();

		if(Objects.nonNull(payments)) {
			for(ContactDocument.Payment payment : payments) {
				if(payment.getPaymentId().equals(paymentEvent.getId())) {
					payment.setStatus(PaymentStatus.FAILED);
					payment.setUpdatedAt(paymentEvent.getTimestamp());
				}
			}
		}

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
		List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();

		for (ContactDocument.Payment payment : paymentMetaList) {
			if (payment.getPaymentId().equals(paymentEvent.getId())) {
				payment.setStatus(PaymentStatus.FAILED);
				payment.setUpdatedAt(paymentEvent.getTimestamp());
			}
		}

		Map<String, Object> data = new HashMap<>();
		data.put("paymentId", paymentEvent.getId());
		data.put("m_c_id", mc.getId());
		Map<String, Object> scriptData = new HashMap<>();
		Long updateTimeStamp = paymentEvent.getTimestamp();
		String inlineValue = "ctx._source.paymentInfo.status=" + "\"" + PaymentStatus.FAILED + "\"" +
				"; ctx._source.u_time=" + "\"" + updateTimeStamp + "\"" + "; ctx._source.type=" + "\"" + MessageDocument.Type.UPDATE.getType() + "\"" +
				"; ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'";

		if(PaymentType.WEBSITE_PAYMENT.getName().equals(paymentEvent.getPaymentType())) {
			/* In case of payment paid via ACH, update the processing bubble to payment completed */
			if(PaymentType.ACH_PAYMENT.getName().equals(paymentEvent.getPaymentMethod()) || PaymentType.AU_BECS.getName().equals(paymentEvent.getPaymentMethod())
			   || PaymentType.AFFIRM.getName().equals(paymentEvent.getPaymentVia())) {
				inlineValue = inlineValue + "; ctx._source.activityType=\"" + ActivityType.PAYMENT_FAILED + "\"";	
			}

			ContactDocument.Payment payment = createContactDocumentPaymentInfo(paymentEvent);
			paymentMetaList.add(payment);
			lastMessageMetadataPOJO.setPayment(paymentMetaList);
			if(contactDocument.getPayments() == null || contactDocument.getPayments().isEmpty()) {
				contactDocument.setPayments(Collections.singletonList(payment));
			} else {
				contactDocument.getPayments().add(payment);   
			}
			contactDocument.setHide(false); // to make contactDocument visible on filters
		}

		lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
		mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

		contactDocument.setL_payment_on(paymentEvent.getTimestamp());
		SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);

		contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));

		mc.setTag(MessageTag.INBOX.getCode());
		contactDocument.setC_tag(MessageTag.INBOX.getCode());

		scriptData.put("inline", inlineValue);
		ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest())
				.index(Constants.Elastic.MESSAGE_INDEX)
				.queryTemplateFile(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID)
				.routingId(businessDTO.getRoutingId())
				.freeMarkerDataModel(data)
				.scriptParam(scriptData);

		boolean response = elasticSearchService.updateByQuery(builder.build());
		if(!response) {
			log.error("Payment Failed Event failed to update in ES, mcId {}, Reason {}", paymentEvent.getConversationId(), response);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}

		messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
		messengerContactService.saveOrUpdateMessengerContact(mc);

		MessageDocument messageDocument =  createFailedActivityOrGetMessageDocument(paymentEvent,mc,businessDTO, data);

		firebaseService.pushToFireBase(contactDocument, messageDocument, false);

//		publishFailedNotification(paymentEvent, mc, businessDTO);
		log.info("{} event handler ends", PaymentEvent.PaymentEventName.PAYMENT_FAILED);
	}

	@Override
	public PaymentEvent.PaymentEventName getHandlerName() {
		return PaymentEvent.PaymentEventName.PAYMENT_FAILED;
	}


//	private void publishFailedNotification(PaymentEvent paymentEvent, MessengerContact mc, BusinessDTO businessDTO) {
//		PaymentNotificationRequest notificationReq = PaymentNotificationRequest.fromPaymentEvent(paymentEvent, mc.getCustomerId(), businessDTO.getBusinessId());
//
//		if(Objects.nonNull(paymentEvent.getSubscriptionEventDetails())) {
//			log.info("Sending failed payment notification");
//			notificationReq.setEventName(PaymentEvent.PaymentEventName.INVOICE_FAILED);
//			kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_NOTIFICATION, notificationReq);
//		}
//		else if(PaymentType.ACH_PAYMENT.getName().equals(paymentEvent.getPaymentMethod())) {
//			notificationReq.setResendUrl(paymentEvent.getResendUrl());
//			notificationReq.setPaymentMethod(PaymentType.ACH_PAYMENT.getName());
//			kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_NOTIFICATION, notificationReq);
//		}
//	}

	private MessageDocument createFailedActivityOrGetMessageDocument(PaymentEvent paymentEvent, MessengerContact mc, BusinessDTO businessDTO,  Map<String, Object> data) {

		if(Objects.nonNull(paymentEvent.getSubscriptionEventDetails()) && Objects.nonNull(paymentEvent.getSubscriptionEventDetails().getCyclicPayment()) &&  paymentEvent.getSubscriptionEventDetails().getCyclicPayment()) {
			
			SubscriptionInfo.SubscriptionInfoBuilder subscriptionInfoBuilder = SubscriptionInfo.builder()
					.id(paymentEvent.getId())
					.lastFourDigit(paymentEvent.getSubscriptionEventDetails().getLast4())
					.paymentVia(paymentEvent.getSubscriptionEventDetails().getPaymentVia());
			
			if(null != paymentEvent.getSubscriptionEventDetails().getNextRetryDate()) {
				DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
				Date nextRetryDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentEvent.getSubscriptionEventDetails().getNextRetryDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				subscriptionInfoBuilder.nextRetryDate(df.format(nextRetryDate));
			} 


			PaymentInfo paymentInfo = new PaymentInfo();
			paymentInfo.setReceiptUrl(paymentEvent.getReceiptUrl());
			paymentInfo.setAmount(paymentEvent.getAmount());
			paymentInfo.setInvoiceNumber(paymentEvent.getInvoiceNumber());
			paymentInfo.setStatus(PaymentStatus.FAILED);
			paymentInfo.setId(paymentEvent.getId());
			paymentInfo.setTransactionId(paymentEvent.getTransactionId());
			paymentInfo.setMethod(paymentEvent.getPaymentMethod());
			paymentInfo.setSubscription(true);
			paymentInfo.setSubscriptionInfo(subscriptionInfoBuilder.build());
			paymentInfo.setCurrency(paymentEvent.getCurrencyCode());
			
			Date created = new Date(paymentEvent.getTimestamp());
			ActivityDto paymentFailedActivity = ActivityDto.builder().mcId(mc.getId()).created(created).updated(created)
					.activityType(ActivityType.PAYMENT_FAILED)
					.activityMeantFor(ActivityMeantFor.CUSTOMER)
					.paymentInfo(paymentInfo)
					.type(MessageDocument.Type.CREATE)
					.accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
					.build();

			log.info("Going to create failed activity - {}", paymentFailedActivity);

			conversationActivityService.persistActivityInDatabase(paymentFailedActivity, null);
			return conversationActivityService.persistActivityInES(paymentFailedActivity);
		}

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
				.addRoutingId(businessDTO.getRoutingId())
				.addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID, data).build();
		ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

		List<MessageDocument> results = dataFromElastic.getResults();
		Optional<MessageDocument> paymentRequestDocOpt = results.stream().filter(doc -> {
			return doc.getMessageType().equals(MessageDocument.MessageType.CHAT) || doc.getMessageType().equals(MessageDocument.MessageType.ACTIVITY);
		}).findFirst();

		if(!paymentRequestDocOpt.isPresent()) {
			throw new BadRequestException(ErrorCode.ES_NOT_DATA);
		}

		return paymentRequestDocOpt.get();
	}

	private ContactDocument.Payment createContactDocumentPaymentInfo(PaymentEvent paymentEvent){
		ContactDocument.Payment payment = new ContactDocument.Payment();
		payment.setPaymentId(paymentEvent.getId());
		payment.setStatus(PaymentStatus.PAYMENT_FAILED);
		payment.setUpdatedAt(paymentEvent.getTimestamp());
		payment.setAmount(paymentEvent.getAmount());
		payment.setCurrency(paymentEvent.getCurrencyCode());
		payment.setMethod(paymentEvent.getPaymentMethod());

		return payment;
	}

}
