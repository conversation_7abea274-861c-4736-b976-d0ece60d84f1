package com.birdeye.messenger.service.payment.impl.refund;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentNotificationRequest;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.*;
import com.birdeye.messenger.service.payment.PaymentEventHandler;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public abstract class AbstractRefundHandler implements PaymentEventHandler {

    @Autowired
    private MessengerContactService messengerContactService;
    @Autowired
    private BusinessService businessService;
    @Autowired
    private ConversationActivityService conversationActivityService;
    @Autowired
    private FirebaseService firebaseService;
    @Autowired
    private UserService userService;
    @Autowired
    private KafkaService kafkaService;
    @Autowired
    private ElasticSearchExternalService elasticSearchService;

    @Override
    public void handle(PaymentEvent paymentEvent) {

        validate(paymentEvent);

        MessengerContact mc = messengerContactService.findById(paymentEvent.getConversationId());
        BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());

        MessangerBaseFilter filter = new MessangerBaseFilter();
        filter.setAccountId(businessDTO.getAccountId());
        filter.setConversationId(mc.getId());
        filter.setCount(1);

        List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
        ContactDocument contactDocument = contactDocuments.get(0);

        Optional<MessageDocument> refundActivityOpt = getActivityIfAlreadyPresent(paymentEvent, businessDTO, mc);

        ImmutablePair<ContactDocument, MessageDocument> docs = null;
        if(refundActivityOpt.isPresent()) {
            docs = updateActivity(refundActivityOpt.get(), contactDocument, paymentEvent, businessDTO, mc);
        }
        //else if(!getPaymentStatusForMessage().equals(PaymentStatus.FAILED) && !getPaymentStatusForMessage().equals(PaymentStatus.CANCELLED)){
        else if(Objects.nonNull(getActivityType())){
            docs = createActivity(paymentEvent, contactDocument, mc, businessDTO);
        }

        if(Objects.nonNull(docs) && Objects.nonNull(docs.getRight()) && Objects.nonNull(docs.getLeft())) {
            firebaseService.pushToFireBase(docs.getLeft(), docs.getRight(), false);
//            if (PaymentEvent.PaymentEventName.PAYMENT_FULL_REFUND_COMPLETED.equals(paymentEvent.getEventName()) ||
//                    PaymentEvent.PaymentEventName.PAYMENT_PARTIAL_REFUND_COMPLETED.equals(paymentEvent.getEventName())) {
//                PaymentNotificationRequest paymentNotificationReq = PaymentNotificationRequest.fromPaymentEvent(paymentEvent, mc.getCustomerId(), mc.getBusinessId());
//                kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_NOTIFICATION, paymentNotificationReq);
//            }
        }

    }

    @Override
    public void validate(PaymentEvent paymentEvent) {
        try {
            Preconditions.checkArgument(Objects.nonNull(paymentEvent.getRefundAmount()), "Refund Amount cannot be empty");
            Preconditions.checkArgument(Objects.nonNull(paymentEvent.getParentId()), "Original Payment request cannot be empty");
            Preconditions.checkArgument(Objects.nonNull(paymentEvent.getItemDetail()), "Item Detail cannot be empty");
            Preconditions.checkArgument(Objects.nonNull(paymentEvent.getAmount()), "Actual payment amount cannot be empty");
            //Preconditions.checkArgument(StringUtils.isNotBlank(paymentEvent.getInvoiceNumber()), "Invoice Url cannot be empty");
            Preconditions.checkArgument(StringUtils.isNotBlank(paymentEvent.getRefundNote()), "Refund Note cannot be empty");
            Preconditions.checkArgument(Objects.nonNull(paymentEvent.getUserId()), "UserId cannot be empty");
        } catch (IllegalArgumentException e) {
            log.error("Invalid Payment Completion Event payload {} {}", paymentEvent, e);
            throw new BadRequestException("Invalid Payment Completion Event payload");
        }
    }

    private Optional<MessageDocument> getActivityIfAlreadyPresent(PaymentEvent paymentEvent, BusinessDTO businessDTO, MessengerContact mc) {

        Map<String, Object> data = new HashMap<>();
        data.put("paymentId", paymentEvent.getId());
        data.put("m_c_id", mc.getId());

        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(businessDTO.getRoutingId())
                .addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID, data).build();
        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

        List<MessageDocument> results = dataFromElastic.getResults();

        return results.stream().filter(doc -> {
            return paymentEvent.getId().equals(doc.getPaymentInfo().getId());
        }).findFirst();

    }

    private ImmutablePair<ContactDocument, MessageDocument> updateActivity(MessageDocument messageDocument, ContactDocument contactDocument, PaymentEvent paymentEvent, BusinessDTO businessDTO, MessengerContact mc) {

        List<ContactDocument.Payment> payments = contactDocument.getPayments();
        PaymentInfo paymentInfo = messageDocument.getPaymentInfo();
        PaymentStatus paymentStatusForMessage = getPaymentStatusForMessage();

        if(PaymentStatus.CANCELLED.equals(paymentStatusForMessage)) {
            Integer userId = paymentEvent.getUserId();
            if(Objects.nonNull(userId)) {
                UserDTO userDTO = userService.getUserDTO(userId);
                paymentInfo.setReqCancelledByName(userDTO.getName());
                paymentInfo.setReqCancelledAt(paymentEvent.getTimestamp());
                paymentInfo.setReqCancelledById(userId);
                paymentInfo.setOtherReason(paymentEvent.getOtherReason());
                messageDocument.setCreatedBy(new MessageDocument.UserDetail(userId, userDTO.getName()));
            }
        }

        paymentInfo.setStatus(paymentStatusForMessage);
        messageDocument.setU_time(paymentEvent.getTimestamp());
        messageDocument.setCr_time(paymentEvent.getTimestamp());

        for(ContactDocument.Payment payment : payments) {
            if(payment.getPaymentId().equals(paymentEvent.getId())) {
                payment.setStatus(getPaymentStatusForConversation());
                payment.setUpdatedAt(paymentEvent.getTimestamp());
            }
        }
        contactDocument.setL_payment_on(paymentEvent.getTimestamp());

        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();
        for(ContactDocument.Payment payment : paymentMetaList) {
            if(payment.getPaymentId().equals(paymentEvent.getId())) {
                payment.setStatus(getPaymentStatusForConversation());
                payment.setUpdatedAt(paymentEvent.getTimestamp());
            }
        }
        lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));


        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));

        UserDTO userDTO = userService.getUserDTO(paymentEvent.getUserId());
        mc.setIsRead(false);
        mc.setViewedBy(StringUtils.join(Collections.singletonList(userDTO.getId()), ','));

        contactDocument.setViewedBy(Collections.singletonList(userDTO.getId()));
        contactDocument.setC_read(false);

        mc.setTag(MessageTag.INBOX.getCode());
        contactDocument.setC_tag(MessageTag.INBOX.getCode());


        messengerContactService.saveOrUpdateMessengerContact(mc);
        messengerContactService.updateContactOnES(paymentEvent.getConversationId(), contactDocument, businessDTO.getRoutingId());
        messengerContactService.updateMessageOnES(messageDocument, businessDTO.getRoutingId());
        return new ImmutablePair<>(contactDocument, messageDocument);
    }

    private ImmutablePair<ContactDocument, MessageDocument> createActivity(PaymentEvent paymentEvent, ContactDocument contactDocument, MessengerContact mc, BusinessDTO businessDTO) {

        ContactDocument.Payment refund = new ContactDocument.Payment();
        refund.setStatus(getPaymentStatusForConversation());
        refund.setPaymentId(paymentEvent.getId());
        refund.setText(paymentEvent.getRefundNote());
        refund.setAmount(paymentEvent.getRefundAmount());
        refund.setUpdatedAt(paymentEvent.getTimestamp());
        refund.setCreatedAt(paymentEvent.getTimestamp());
        refund.setCurrency(paymentEvent.getCurrencyCode());
        
        List<ContactDocument.Payment> payments = contactDocument.getPayments();
        payments.add(refund);
        contactDocument.setL_payment_on(paymentEvent.getTimestamp());

        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMeta = lastMessageMetadataPOJO.getPayment();
        paymentMeta.add(refund);
        lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));


        UserDTO userDTO = userService.getUserDTO(paymentEvent.getUserId());
        mc.setIsRead(false);
        mc.setViewedBy(StringUtils.join(Collections.singletonList(userDTO.getId()), ','));

        contactDocument.setViewedBy(Collections.singletonList(userDTO.getId()));
        contactDocument.setC_read(false);

        mc.setTag(MessageTag.INBOX.getCode());
        contactDocument.setC_tag(MessageTag.INBOX.getCode());


        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setRefundAmount(paymentEvent.getRefundAmount());
        paymentInfo.setStatus(getPaymentStatusForMessage());
        paymentInfo.setId(paymentEvent.getId());
        paymentInfo.setTransactionId(paymentEvent.getTransactionId());
        paymentInfo.setParentId(paymentEvent.getParentId());
        paymentInfo.setInvoiceNumber(paymentEvent.getInvoiceNumber());
        paymentInfo.setReceiptUrl(paymentEvent.getReceiptUrl());
        paymentInfo.setRefundNote(paymentEvent.getRefundNote());
        paymentInfo.setOtherReason(paymentEvent.getOtherReason());
        paymentInfo.setAmount(paymentEvent.getAmount());

        paymentInfo.setReqCreatedAt(paymentEvent.getTimestamp());
        paymentInfo.setReqCreatedByName(userDTO.getName());
        paymentInfo.setReqCreatedById(userDTO.getId());
        paymentInfo.setCurrency(paymentEvent.getCurrencyCode());

        Date created = new Date(paymentEvent.getTimestamp());

        ActivityDto paymentCompletedActivity = ActivityDto.builder().mcId(mc.getId()).created(created).updated(created)
                .activityType(getActivityType())
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .paymentInfo(paymentInfo)
                .type(MessageDocument.Type.CREATE)
                .actorName(userDTO.getName())
                .actorId(userDTO.getId())
                .accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
                .build();

        messengerContactService.saveOrUpdateMessengerContact(mc);
        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        conversationActivityService.persistActivityInDatabase(paymentCompletedActivity, userDTO);
        MessageDocument messageDocument = conversationActivityService.persistActivityInES(paymentCompletedActivity);

        return new ImmutablePair<>(contactDocument, messageDocument);

    }


    abstract ActivityType getActivityType();

    abstract PaymentStatus getPaymentStatusForMessage();

    abstract PaymentStatus getPaymentStatusForConversation();
}
