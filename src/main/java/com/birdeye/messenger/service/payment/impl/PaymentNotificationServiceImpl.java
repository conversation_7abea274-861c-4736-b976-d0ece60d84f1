package com.birdeye.messenger.service.payment.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.SendConversationRequest;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.dto.payment.PaymentEvent.PaymentEventName;
import com.birdeye.messenger.dto.payment.PaymentNotificationRequest;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.NexusEmailService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.SmsService;
import com.birdeye.messenger.service.payment.PaymentNotificationService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentNotificationServiceImpl implements PaymentNotificationService {

	private final BusinessService businessService;
	private final ContactService contactService;
	private final NexusEmailService nexusEmailService;
	private final KafkaService kafkaService;
	private final SmsService smsService;
	private final UserService userService;

	@Override
	public void processNotification(PaymentNotificationRequest paymentNotificationRequest) {

		log.info("PaymentNotificationRequest - {}", paymentNotificationRequest);
		BusinessDTO businessDTO = businessService.getBusinessDTO(paymentNotificationRequest.getBusinessId());
		CustomerDTO customerDTO = null;


		if(Objects.nonNull(paymentNotificationRequest.getCustomerId())) {
			customerDTO = contactService.findByIdNoCaching(paymentNotificationRequest.getCustomerId());
			if(StringUtils.isNotBlank(paymentNotificationRequest.getCustomerEmailId())){
				customerDTO.setEmailId(paymentNotificationRequest.getCustomerEmailId());
			}
		}

		switch (paymentNotificationRequest.getEventName()) {
		case PAYMENT_COMPLETED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.RECIPIENT_TYPE);
			// Check added for unattributed payment events
			if(Objects.nonNull(customerDTO)) {
				processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
				processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			}

			break;
		case PAYMENT_FULL_REFUND_COMPLETED:
		case PAYMENT_PARTIAL_REFUND_COMPLETED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;
		case PAYMENT_INITIATED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;


		case PAYMENT_PROCESSING:

			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.RECIPIENT_TYPE);
			// Check added for unattributed payment events
			if(Objects.nonNull(customerDTO)) {
				processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
				processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			}
			break;

		case PAYMENT_FAILED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.RECIPIENT_TYPE);

			// Check added for unattributed payment events
			if(Objects.nonNull(customerDTO)) {
				processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
				processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			}
			break;
		case SUBSCRIPTION_CANCELLED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.RECIPIENT_TYPE);
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.CUSTOM_RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;
		case UPCOMING_INVOICE:
			processEmailNotification(paymentNotificationRequest,businessDTO,customerDTO,Constants.CUSTOM_RECIPIENT_TYPE);
			processEmailNotification(paymentNotificationRequest,businessDTO,customerDTO,Constants.RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;
		case UPDATE_PAYMENT_METHOD:
			processEmailNotification(paymentNotificationRequest,businessDTO,customerDTO,Constants.CUSTOM_RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;
		case INVOICE_FAILED:
			processEmailNotification(paymentNotificationRequest, businessDTO, customerDTO, Constants.RECIPIENT_TYPE);
			processEmailNotification(paymentNotificationRequest,businessDTO,customerDTO,Constants.CUSTOM_RECIPIENT_TYPE);
			processTextNotification(paymentNotificationRequest, businessDTO, customerDTO);
			break;
		default:
			throw new BadRequestException(String.format("Payment Event %s not supported for email notifications", paymentNotificationRequest.getEventName()));
		}

	}

	private void processEmailNotification(PaymentNotificationRequest paymentNotificationRequest, BusinessDTO businessDTO, CustomerDTO customerDTO, String recipientType) {
		String emailSubject = getEmailSubject(paymentNotificationRequest, businessDTO, customerDTO, recipientType);
		Map<String, String> emailTokens = getEmailTokens(paymentNotificationRequest, businessDTO, customerDTO, recipientType);
		List<String> emailIds = getRecipientList(recipientType, businessDTO, customerDTO);
		EmailDTO emailDTO = new EmailDTO();
		if (CollectionUtils.isNotEmpty(emailIds)) {
			emailDTO.setBusinessId(String.valueOf(businessDTO.getBusinessId()));
			emailDTO.setFrom(businessDTO.getBirdEyeEmailId());
			emailDTO.setFromName(StringEscapeUtils.unescapeHtml4(getBusinessName(businessDTO, recipientType)));
			emailDTO.setRequestType(Constants.PAYMENT_EMAIL_TEMPLATE);
			emailDTO.setRequestSubType(Constants.PAYMENT_EMAIL_TEMPLATE);
			emailDTO.setRecipientType(recipientType);
			emailDTO.setTextType(EmailDTO.TEXT_TYPE.HTML);
			emailDTO.setReplyTo("<EMAIL>");
			emailDTO.setTo(emailIds);
			emailDTO.setSubject(emailSubject);
			emailDTO.setBusinessNumber(businessDTO.getBusinessNumber());
			if(Objects.nonNull(customerDTO)) {
				emailDTO.setCustomerId(customerDTO.getId());
			}
			emailDTO.setDataObject(emailTokens);
			emailDTO.setExternalUid(paymentNotificationRequest.getId());
			if(recipientType==Constants.CUSTOM_RECIPIENT_TYPE) {
				emailDTO.setIsCustomerEmail(1);
				emailDTO.setEmailCategory(Constants.SERVICE); //payment_alert
			}
		}

		nexusEmailService.sendMailV2(businessDTO.getBusinessId(), emailDTO, Constants.PAYMENT_EMAIL_TEMPLATE,
				Constants.PAYMENT_ALERT + "_" + paymentNotificationRequest.getId(),
				businessDTO.getBusinessNumber(), emailTokens);

	}   

	private List<String> getRecipientList(String recipientType, BusinessDTO businessDTO, CustomerDTO customerDTO) {
		if(Constants.RECIPIENT_TYPE.equals(recipientType)) {
			//return Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");
			List<String> emailIdsForPaymentNotifications = userService.getEmailIdsForPaymentNotifications(businessDTO.getAccountId(), businessDTO.getBusinessId());
			log.info("UserIds from payment notifications {}", emailIdsForPaymentNotifications);
			//emailIdsForPaymentNotifications.add("<EMAIL>");
			return emailIdsForPaymentNotifications;
		}
		else if(Objects.nonNull(customerDTO)){
			String emailId = customerDTO.getEmailId();
			if(StringUtils.isBlank(emailId)) {
				log.info("No customer email Ids found for sending payment notifications");
				return new ArrayList<>();
			}
			if(emailId.contains("@gbmdummy.com") || emailId.contains("@fb.com") || emailId.contains("@ig.com")) return new ArrayList<>();
			else return Collections.singletonList(emailId);
		}
		else {
			return new ArrayList<>();
		}
	}

	private String[] getTitleAndBody(PaymentNotificationRequest paymentNotificationRequest, BusinessDTO businessDTO, CustomerDTO customerDTO, String recipientType) {
		String title;
		String body;
		String businessName = getBusinessName(businessDTO, recipientType);
		String customerName = Objects.nonNull(customerDTO) ? MessengerUtil.buildCustomerName(customerDTO) : "Contact";
		Double refund = paymentNotificationRequest.getRefundAmount();
		SimpleDateFormat sdf = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
		switch (paymentNotificationRequest.getEventName()) {

		case PAYMENT_COMPLETED:
			if(paymentNotificationRequest.getSubscriptionEventDetails() != null) {
				//for Recurring payments notifications
				Date dueDate = null;
				if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails().getDueDate())) {
						dueDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				}
				
				Date endDate = null;
				if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails().getEndDate())) {
						endDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getEndDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				}
				
				String amount = MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$"));
				
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					title = String.format("Thank you for the payment");
					body = String.format("Hi %s, payment of <b>%s</b> using <b>%s ending %s</b> for <b>%s</b> is successful.",
							customerName, amount,
							paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
							paymentNotificationRequest.getSubscriptionEventDetails().getLast4(),
							businessName
								);

					if(Objects.nonNull(dueDate)) {
						body = body.concat(String.format(" We will auto debit <b>%s</b> %s until <b>%s</b>. Your next payment date is <b>%s</b>.",
								amount,
								paymentNotificationRequest.getSubscriptionEventDetails().getIntervalMode(),
								Objects.nonNull(endDate) ? sdf.format(endDate) : "cancelation",
								sdf.format(dueDate)));
					}

				}else {
					title = "Recurring payment was successful";
					body = String.format("%s has succesfully paid recurring payment of <b>%s</b> using <b>%s ending %s</b>.",
							customerName, amount,
							paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
							paymentNotificationRequest.getSubscriptionEventDetails().getLast4()
							);
					
					if(Objects.nonNull(dueDate)) {
						body = body.concat(String.format(" We will auto debit <b>%s</b> %s using customer's payment method until %s. Next payment date is <b>%s</b>.",
								amount, 
								paymentNotificationRequest.getSubscriptionEventDetails().getIntervalMode(),
								Objects.nonNull(endDate) ? sdf.format(endDate) : "cancelation",
								sdf.format(dueDate)));
					}
				}

			}else {
				//For non-recurring payment notifications
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					title = "Payment confirmation";
					body = String.format("Thank you for making a payment to %s.", businessName);
				} else {
					title = "Payment received";  // BIRDEYE-10370 
					if(Objects.nonNull(customerDTO)) {
						body = String.format("%s made a payment.", customerName);
					}else {
						body = String.format("A payment of %s was received.", MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")));
					}
				}
			}
			break;

		case PAYMENT_FULL_REFUND_COMPLETED:
		case PAYMENT_PARTIAL_REFUND_COMPLETED:

			title = String.format("Refund from %s", businessName);
			body = String.format("A refund of %s is on the way.", MessengerUtil.getFormattedAmount(refund, Optional.of("$")));
			break;


		case PAYMENT_INITIATED:
			title = "Payment initiated";
			body = String.format("%s has initiated a payment of %s", businessName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")));
			break;

		case PAYMENT_PROCESSING:
			if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails())) {
				title = "Recurring payment initiated";
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) { 
					body = String.format("Your recurring payment has been initiated to %s. Processing may take up to 5 business days.", businessName);
				} else {
					body = String.format("%s has initiated recurring payment of %s through %s ending %s. Please allow up to 5 business days for processing.", 
							customerName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
							paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
							paymentNotificationRequest.getSubscriptionEventDetails().getLast4());
				}
			} else {
				title = "Payment initiated";
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) { 
					body = String.format("Your payment has been initiated to %s. Processing may take up to 5 business days.", businessName);
				} else {
					body = String.format("%s has initiated a payment of %s through bank transfer. Please allow up to 5 business days for processing.", 
							customerName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")));
				}
			}
			break;

		case PAYMENT_FAILED:	
				title = "Payment failed";
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {

					body = String.format("We were unable to process your last payment of %s to %s.",
							MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
							getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
				} else {
					body = String.format("The last payment from %s has failed.", customerName);
				}
			break;

		case UPCOMING_INVOICE:
			Date dateInBusinessTimezone = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			String formattedDate = sdf.format(dateInBusinessTimezone);
			title = "Recurring payment reminder";
			if(Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
				body = String.format("Hi %s, this is a friendly reminder for your upcoming recurring payment for <b>%s</b> with <b>%s</b>. We will auto debit <b>%s</b> using <b>%s ending %s</b> on <b>%s</b>.", 
						customerName, paymentNotificationRequest.getItemDetail(),
						businessName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
						paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
						paymentNotificationRequest.getSubscriptionEventDetails().getLast4(),
						formattedDate);
			} else {
				body = String.format(
						"We have sent a friendly reminder to %s for an upcoming recurring payment for <b>%s</b> with <b>%s</b>. We will auto debit <b>%s</b> using <b>%s ending %s</b> on <b>%s</b>.",
						customerName, paymentNotificationRequest.getItemDetail(), businessName,
						MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
						paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
						paymentNotificationRequest.getSubscriptionEventDetails().getLast4(), formattedDate);
			}
			break;


		case UPDATE_PAYMENT_METHOD:
			title="Update your payment method";
			
			body =String.format("Hi %s, please use the below button to update the payment method for your recurring payment of <b>%s</b> for <b>%s</b> with <b>%s</b>.", 
					customerName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
					paymentNotificationRequest.getItemDetail(), businessName);
			break;

		case SUBSCRIPTION_CANCELLED:
			Date cancelationDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getCancelledDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			if(recipientType.equals(Constants.CUSTOM_RECIPIENT_TYPE)) {
				title = "Your recurring payment canceled";
				body =String.format("Hi %s, your recurring payment of <b>%s</b> for <b>%s</b> was canceled on <b>%s</b> and you will not be charged again.",
						customerName, MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
						businessName, sdf.format(cancelationDate));
			}else {
				title = "Recurring payment canceled";
				body = String.format("Recurring payment of <b>%s</b> was canceled on <b>%s</b> and %s will not be charged again.",
						MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")),
						sdf.format(cancelationDate), customerName);
			}
			break;
		case INVOICE_FAILED:
			//For recurring payment notifications	
			Date retryDate = null;
			if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails().getNextRetryDate())) {
					retryDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getNextRetryDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			}
			
			String amount = MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$"));
			
			if(Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
				title = "Your payment was unsuccessful";
				
				body = String.format("Hi %s, we are unable to process your recurring payment of <b>%s</b> using <b>%s ending %s</b> for '%s'.",
						customerName, amount,
						paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
						paymentNotificationRequest.getSubscriptionEventDetails().getLast4(),
						paymentNotificationRequest.getItemDetail());

				if(Objects.nonNull(retryDate)) {
					body = body.concat(" You can update the payment method using the Update payment button below.");
				}
			} else {
				title = "Recurring payment was unsuccessful";
				body = String.format("We are unable to process recurring payment of <b>%s</b> using <b>%s ending %s</b> for '%s'.",
						amount,
						paymentNotificationRequest.getSubscriptionEventDetails().getPaymentVia(),
						paymentNotificationRequest.getSubscriptionEventDetails().getLast4(),
						paymentNotificationRequest.getItemDetail());
				if(Objects.nonNull(retryDate)) {
					body = body.concat(String.format(" Message %s to update the payment method.", customerName));
				}
				
			}
			if(Objects.nonNull(retryDate )) {
				body = body.concat(String.format(" We will retry on <b>%s</b>.", sdf.format(retryDate)));
			}
			break;
		default:
			throw new BadRequestException(String.format("Payment Event %s not supported for email notifications", paymentNotificationRequest.getEventName()));
		}
		return new String[]{title, body};
	}

	private String getBusinessName(BusinessDTO businessDTO, String recipientType) {
		if(Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
			return businessDTO.getBusinessName();
		}
		return StringUtils.isNotBlank(businessDTO.getBusinessAlias()) ? businessDTO.getBusinessAlias() : businessDTO.getBusinessName();
	}

	private Map<String, String> getEmailTokens(PaymentNotificationRequest paymentNotificationRequest, BusinessDTO businessDTO, CustomerDTO customerDTO, String recipientType) {
		Map<String, String> tokens = new HashMap<>();
		String[] titleAndBody = getTitleAndBody(paymentNotificationRequest, businessDTO, customerDTO, recipientType);

		DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
		Date dateInBusinessTimezone = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getTimestamp()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
		String formattedDate = df.format(dateInBusinessTimezone);
		if (paymentNotificationRequest.getSubscriptionEventDetails()!= null){
			tokens.put("isSubscription", "true");
			if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails().getDueDate())) {
				Date dueDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentNotificationRequest.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				String formattedDueDate = df.format(dueDate);
				tokens.put("dueDate",formattedDueDate);
			}

			tokens.put("updatePaymentMethod",paymentNotificationRequest.getSubscriptionEventDetails().getUpdatePaymentMethodLink());

			if(paymentNotificationRequest.getEventName().equals(PaymentEvent.PaymentEventName.PAYMENT_COMPLETED)) {
				tokens.put("subscriptionAmountPaid", "true");
			}

			if(Objects.nonNull(paymentNotificationRequest.getSubscriptionEventDetails())) {
				String intervalMode = paymentNotificationRequest.getSubscriptionEventDetails().getIntervalMode();
				if(Objects.nonNull(intervalMode)) {
					String formattedIntervalMode = 
							String.valueOf(Character.toUpperCase(intervalMode.charAt(0))).concat(intervalMode.substring(1));
					tokens.put("subscriptionIntervalMode", formattedIntervalMode);
				}
			}

		}

		String inboxLink = getInboxLInk(businessDTO, paymentNotificationRequest.getConversationId());
		tokens.put("notificationName", getNotificationName(paymentNotificationRequest, recipientType));

		// BIRDEYE-10370 
		String name = null;
		if(Objects.nonNull(customerDTO)) {
			name = StringUtils.isNotBlank(customerDTO.getFirstName()) ? customerDTO.getFirstName() : customerDTO.getLastName();
		}else {
			name = "contact";
		}

		tokens.put("customerName", StringUtils.isNotBlank(name) ? name : "customer");
		tokens.put("title", titleAndBody[0]);
		tokens.put("body", titleAndBody[1]);
		if (paymentNotificationRequest.getReceiptUrl() != null){
			tokens.put("receiptUrl", paymentNotificationRequest.getReceiptUrl());
		}
		tokens.put("invoiceNumber", StringUtils.isNotBlank(paymentNotificationRequest.getInvoiceNumber()) ? paymentNotificationRequest.getInvoiceNumber() : "N/A");
		tokens.put("date", formattedDate);
		tokens.put("itemDesc", StringUtils.isNotBlank(paymentNotificationRequest.getItemDetail()) ? paymentNotificationRequest.getItemDetail() : "N/A");
		tokens.put("itemPrice", MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")));
		tokens.put("amount", MessengerUtil.getFormattedAmount(paymentNotificationRequest.getAmount(), Optional.of("$")));
		if(Objects.nonNull(paymentNotificationRequest.getRefundAmount())) {
			tokens.put("refundAmount", MessengerUtil.getFormattedAmount(paymentNotificationRequest.getRefundAmount(), Optional.of("$")));
		}
		tokens.put("transactionId", paymentNotificationRequest.getTransactionId());
		tokens.put("inboxLink", inboxLink);

		if(Objects.nonNull(paymentNotificationRequest.getResendUrl())) {
			tokens.put("resendUrl", paymentNotificationRequest.getResendUrl());
		}

		// BIRDEYE-10370
		if(Objects.isNull(customerDTO) && Objects.nonNull(paymentNotificationRequest.getMemo())){
			tokens.put("memo", paymentNotificationRequest.getMemo());
		}

		return tokens;
	}

	private String getInboxLInk(BusinessDTO businessDTO, Integer conversationId) {
		String serverBaseURL = businessService.getWebsiteDomain(businessDTO.getBusinessId()) + "/";
		Integer businessId = businessDTO.getBusinessId();
		Integer enterpriseId = businessDTO.getAccountId();
		String inboxLink = "${serverBaseURL}dashboard/messenger/all/${mcId}?businessId=${businessId}&enterpriseId=${enterpriseId}&conversationId=${mcId}&redirectURL=true";
		inboxLink = inboxLink.replace("${serverBaseURL}", serverBaseURL);
		inboxLink = inboxLink.replace("${mcId}", String.valueOf(conversationId));
		inboxLink = inboxLink.replace("${businessId}", String.valueOf(businessId));
		inboxLink = inboxLink.replace("${enterpriseId}", String.valueOf(enterpriseId));
		return inboxLink;
	}

	private String getNotificationName(PaymentNotificationRequest req, String recipientType) {
		PaymentNotificationRequest.PaymentEventName eventName = req.getEventName();
		if(eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_COMPLETED) && Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
			return PaymentNotificationName.PAYMENT_CONFIRMATION_CUSTOMER.name();
		}else if(eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_COMPLETED) && Constants.RECIPIENT_TYPE.equals(recipientType)) {
			return PaymentNotificationName.PAYMENT_CONFIRMATION_BUSINESS.name();
		}else if (eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_FULL_REFUND_COMPLETED) || eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_PARTIAL_REFUND_COMPLETED)) {
			return PaymentNotificationName.PAYMENT_REFUND_CUSTOMER.name();
		}else if (eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_INITIATED)){
			return PaymentNotificationName.PAYMENT_INITIATED.name();
		}else if (eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_PROCESSING)){
			return PaymentNotificationName.PAYMENT_PROCESSING.name();
		}else if(eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_FAILED) && Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)){
			return PaymentNotificationName.PAYMENT_FAILED_CUSTOMER.name();
		}else if(eventName.equals(PaymentEvent.PaymentEventName.PAYMENT_FAILED) && Constants.RECIPIENT_TYPE.equals(recipientType)){
			return PaymentNotificationName.PAYMENT_FAILED_BUSINESS.name();
		}else if(eventName.equals(PaymentEvent.PaymentEventName.UPCOMING_INVOICE) && Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)){
			return PaymentNotificationName.UPCOMING_INVOICE_CUSTOMER.name();
		}else if(eventName.equals(PaymentEvent.PaymentEventName.UPCOMING_INVOICE) && Constants.RECIPIENT_TYPE.equals(recipientType)){
			return PaymentNotificationName.UPCOMING_INVOICE_BUSINESS.name();
		}else if(eventName.equals(PaymentEventName.UPDATE_PAYMENT_METHOD)) {
			return PaymentNotificationName.UPDATE_PAYMENT_METHOD.name();
		}else if(eventName.equals(PaymentEventName.SUBSCRIPTION_CANCELLED) && Constants.RECIPIENT_TYPE.equals(recipientType)){
			return PaymentNotificationName.SUBSCRIPTION_CANCELLED_BUSINESS.name();
		}else if(eventName.equals(PaymentEventName.SUBSCRIPTION_CANCELLED) && Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
			return PaymentNotificationName.SUBSCRIPTION_CANCELLED_CUSTOMER.name();
		}else if(eventName.equals(PaymentEventName.INVOICE_FAILED) && Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
			return PaymentNotificationName.SUBSCRIPTION_INVOICE_FAILED_CUSTOMER.name();
		}else if(eventName.equals(PaymentEventName.INVOICE_FAILED) && Constants.RECIPIENT_TYPE.equals(recipientType)) {
			return PaymentNotificationName.SUBSCRIPTION_INVOICE_FAILED_BUSINESS.name();
		}
		throw new BadRequestException(String.format("Payment Event %s not supported for email notification", req.getEventName()));
	}

	private String getEmailSubject(PaymentNotificationRequest req, BusinessDTO businessDTO, CustomerDTO customerDTO, String recipientType) {

		String subject;
		String businessName = getBusinessName(businessDTO, recipientType);
		String customerName = Objects.nonNull(customerDTO) ? MessengerUtil.buildCustomerName(customerDTO) : "Contact";
		Double refund = req.getRefundAmount();
		SimpleDateFormat sdf = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
		

		switch (req.getEventName()) {

		case PAYMENT_COMPLETED:
			if (Objects.nonNull(req.getSubscriptionEventDetails())) {
				//For reccuring
				Date dateInBusinessTimezone = ControllerUtil.convertToBusinessTimeZone(new Date(req.getTimestamp()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				String formattedDate = sdf.format(dateInBusinessTimezone);
				subject = String.format("Recurring payment of %s was successful on %s",
						MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), formattedDate);
			} else {
				//for non-recurring

				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					subject = String.format("You have successfully paid %s %s", businessName,
							MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
				}
				// BIRDEYE-10370
				else {
					// email subject for business user
					if (Objects.nonNull(customerDTO)) {
						subject = String.format("%s paid you %s", customerName,
								MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
					} else {
						subject = String.format("A payment of %s was received",
								MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
					}
				}
			}
			break;

		case PAYMENT_FULL_REFUND_COMPLETED:
		case PAYMENT_PARTIAL_REFUND_COMPLETED:
			subject = String.format("%s sent you a refund of %s", businessName, MessengerUtil.getFormattedAmount(refund, Optional.of("$")));
			break;


		case PAYMENT_INITIATED:
			subject = String.format("%s has initiated a payment of %s", businessName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
			break;

		case PAYMENT_PROCESSING:
			if(Objects.nonNull(req.getSubscriptionEventDetails())) {
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					subject = String.format("You have initiated recurring payment of %s to %s",  MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), businessName);
				} else {
					subject = String.format("%s initiated recurring payment of %s", customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
				}
			}else {
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					subject = String.format("You have initiated a payment of %s to %s",  MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), businessName);
				} else {
					subject = String.format("%s initiated a payment of %s", customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
				}
			}
			break;

		case PAYMENT_FAILED: 
				if (Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
					subject = String.format("Your payment of %s to %s has failed.",
							MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), businessName);

				} else {
					subject = String.format("Payment of %s from %s has failed.",
							MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), customerName);

				}
			break;
		case SUBSCRIPTION_CANCELLED:
			Date dateInBusinessTimezone = ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getCancelledDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			String formattedDate = sdf.format(dateInBusinessTimezone);
			subject = String.format("Recurring payment of %s was canceled on %s",
					MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),formattedDate);
			break;
		case UPCOMING_INVOICE:
			if(Constants.CUSTOM_RECIPIENT_TYPE.equals(recipientType)) {
				subject = String.format("Reminder for your recurring payment of %s",MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
			} else {
				subject = String.format("Reminder for recurring payment of %s",MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
			}
			break;
		case UPDATE_PAYMENT_METHOD:
			subject= String.format("Update payment method for recurring payment of %s", MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")));
			break;
		case INVOICE_FAILED:
			subject = String.format("Recurring payment of %s was unsuccessful on %s",
					MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					sdf.format(ControllerUtil
					.convertToBusinessTimeZone(new Date(req.getTimestamp()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime()));
			break;
		default:
			throw new BadRequestException(String.format("Payment Event %s not supported for email notifications", req.getEventName()));
		}
		return subject;
	}


	private void processTextNotification(PaymentNotificationRequest paymentNotificationRequest, BusinessDTO businessDTO, CustomerDTO customerDTO) {
		if(Objects.nonNull(customerDTO)) {
			String fromPhone = smsService.getFormattedBusinessNumber(businessDTO.getBusinessId());
			SendConversationRequest request = new SendConversationRequest();
			request.setBody(getTextMsgBody(paymentNotificationRequest, businessDTO, customerDTO));
			request.setExternalUID(Constants.PAYMENT_ALERT + paymentNotificationRequest.getId());
			request.setFromBusinessId(businessDTO.getBusinessId());
			request.setFromPhone(fromPhone);
			request.setToCustomerId(customerDTO.getId());
			request.setToPhone(customerDTO.getPhoneE164());
			request.setType("messenger");
			request.setSubType("payments");
            request.setAccountId(businessDTO.getAccountId());
			kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.COMM_SMS_SEND,businessDTO.getBusinessId(), request);
		}
	}

	private String getTextMsgBody(PaymentNotificationRequest req, BusinessDTO businessDTO, CustomerDTO customerDTO) {
		DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
		String customerName = MessengerUtil.buildCustomerName(customerDTO);
		String body;
		switch (req.getEventName()) {
		case PAYMENT_COMPLETED:
			if(Objects.nonNull(req.getSubscriptionEventDetails())) {
				//Text notification for recurring payments

				Date dueDate = null;
				if(Objects.nonNull(req.getSubscriptionEventDetails().getDueDate())) {
					dueDate = ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				}
				
				Date endDate = null;
				if(Objects.nonNull(req.getSubscriptionEventDetails().getEndDate())) {
					endDate = ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getEndDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				}
				
				String amount = MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$"));
				body = String.format("Hi %s, payment of %s using %s ending %s for %s is successful.",
							customerName, amount,
							req.getSubscriptionEventDetails().getPaymentVia(), req.getSubscriptionEventDetails().getLast4(),
							getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
				
				if(Objects.nonNull(dueDate)) {
					body = body.concat(String.format(" We will auto debit %s %s until %s. Your next payment date is %s.",
							amount,
							req.getSubscriptionEventDetails().getIntervalMode(),
							Objects.nonNull(endDate) ? df.format(endDate) : "cancelation",
							df.format(dueDate)));
				}
//				 Click here to view receipt
				body = body.concat(String.format("\nClick here to View receipt %s", req.getReceiptUrl()));
				
			}else {
				//Text Notifications for non-recurring payments
				body = String.format("Hi %s. Thank you for your payment of %s to %s. We've sent the invoice to your email. You can also use this link to download the invoice %s",
						customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE), req.getReceiptUrl());
			}
			break;
		case PAYMENT_FULL_REFUND_COMPLETED:
		case PAYMENT_PARTIAL_REFUND_COMPLETED:
			body = String.format("Hi %s. We've initiated a refund of %s against your payment of %s. The amount will reflect in your account in 5-10 business days. " +
					"Thank you.\n -Team %s", customerName, MessengerUtil.getFormattedAmount(req.getRefundAmount(), Optional.of("$")), MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
			break;

		case PAYMENT_INITIATED:

			body = String.format("Payment of %s is initiated by %s for %s. ",  MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE),req.getItemDetail());
			if (StringUtils.isNotEmpty(businessDTO.getPhoneNumber())){
				body = body  + String.format("Don’t recognise this charge? Reach us on %s ",businessDTO.getPhoneNumber());
			}
			break;
		case PAYMENT_PROCESSING:
			if(Objects.nonNull(req.getSubscriptionEventDetails())) {
				body = String.format("Hi %s. Your recurring payment has been initiated to %s. Processing may take up to 5 business days.", customerName, getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
			}else {
				body = String.format("Hi %s. You have initiated a payment of %s to %s. Processing may take up to 5 business days.",
					customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")), getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
			}
			break;

			// TODO : RESENT URL
		case PAYMENT_FAILED:
			body = String.format("Hi %s. We were unable to process your last payment of %s to %s.",
					customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE));
			break;
		case UPDATE_PAYMENT_METHOD: 
			body =String.format("Hi %s. Please use the below link to update the payment method for your recurring payment of %s for %s with %s.\n%s", 
					customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					req.getItemDetail(), getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE), req.getSubscriptionEventDetails().getUpdatePaymentMethodLink());
			break;
		case SUBSCRIPTION_CANCELLED:
			Date cancelationDate = ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getCancelledDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			body = String.format("Hi %s. Your recurring payment of %s for %s was canceled on %s and you will not be charged again.",
					customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE), df.format(cancelationDate));
			break;
		case UPCOMING_INVOICE:

			Date dueDate=ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
			body = String.format("Hi %s. This is a friendly reminder for your upcoming recurring payment for %s with %s. We will auto debit %s using %s ending %s on %s.", 
					customerName, req.getItemDetail(),
					getBusinessName(businessDTO, Constants.CUSTOM_RECIPIENT_TYPE), MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					req.getSubscriptionEventDetails().getPaymentVia(), req.getSubscriptionEventDetails().getLast4(),
					df.format(dueDate));
			body = body.concat(String.format("\nClick the link below if you want to update the payment method.\n%s", req.getSubscriptionEventDetails().getUpdatePaymentMethodLink()));
			
			break;
		case INVOICE_FAILED:
			String retrytext = null;
			String updatePaymentMethodLink=null;
			body = String.format("Hi %s. We are unable to process your recurring payment of %s using %s ending %s for %s.",
					customerName, MessengerUtil.getFormattedAmount(req.getAmount(), Optional.of("$")),
					req.getSubscriptionEventDetails().getPaymentVia(),
					req.getSubscriptionEventDetails().getLast4(), req.getItemDetail());
			
			if(Objects.nonNull(req.getSubscriptionEventDetails().getNextRetryDate())){
				Date retryDateInBusinessTimezone = ControllerUtil.convertToBusinessTimeZone(new Date(req.getSubscriptionEventDetails().getNextRetryDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				retrytext = String.format(" You can update the payment method using the link below. We will retry your payment by %s.", df.format(retryDateInBusinessTimezone));
			}
			if(!req.getSubscriptionEventDetails().getPaymentVia().equalsIgnoreCase("account") && Objects.nonNull(req.getSubscriptionEventDetails().getUpdatePaymentMethodLink()) ){
				//To remove the update payment method CTA from unsuccessful payments for ACH
				updatePaymentMethodLink=String.format("%s", req.getSubscriptionEventDetails().getUpdatePaymentMethodLink());
			}
			if(Objects.nonNull(retrytext)) {
				body= body.concat(retrytext);
			}
			if(Objects.nonNull(updatePaymentMethodLink)) {
				body=body.concat(updatePaymentMethodLink);
			}
			break;
		default:
			throw new BadRequestException(String.format("Payment Event %s not supported for email notifications", req.getEventName()));
		}
		return body;
	}

}
