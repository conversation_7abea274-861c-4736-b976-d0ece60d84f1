package com.birdeye.messenger.service.payment.impl.refund;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.PaymentStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PartialRefundCompletedHandler extends AbstractRefundHandler {

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_PARTIAL_REFUND_COMPLETED;
    }

    @Override
    ActivityType getActivityType() {
        return ActivityType.PAYMENT_PARTIAL_REFUND;
    }

    @Override
    PaymentStatus getPaymentStatusForMessage() {
        return PaymentStatus.COMPLETED;
    }

    @Override
    PaymentStatus getPaymentStatusForConversation() {
        return PaymentStatus.PARTIALLY_REFUNDED;
    }
}
