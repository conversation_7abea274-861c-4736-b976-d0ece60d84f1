package com.birdeye.messenger.service.payment;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.*;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkRequest.PaymentRequestType;
import com.birdeye.messenger.enums.*;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.*;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

import jakarta.transaction.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentManualCardEntryServiceImpl implements PaymentManualCardEntryService {

    private final PaymentService paymentService;
    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final UserService userService;
    private final ConversationActivityService conversationActivityService;
    private final FirebaseService firebaseService;
    private final ContactService contactService;
    private final AssignmentService assignmentService;
    private final KafkaService kafkaService;

    @Override
    public MessageResponse handleManualCardEntryPayment(SendMessageDTO sendMessageDTO) {
        log.info("request:{}",sendMessageDTO.toString());
        if(Objects.nonNull(sendMessageDTO.getCustomerId())){
            MessengerContact messengerContact = messengerContactService.findByCustomerId(sendMessageDTO.getCustomerId());
            if(Objects.nonNull(messengerContact)){
                sendMessageDTO.setToCustomerId(messengerContact.getId().toString());
            }
        }

        if(Objects.isNull(sendMessageDTO.getToCustomerId())) {
    		return handleCardEntryPaymentWithNoContactFound(sendMessageDTO);
    	}

        PaymentUiRequest paymentRequest = sendMessageDTO.getPaymentRequest();

        if(Objects.isNull(paymentRequest) || ( !"CARD_ENTRY".equals(paymentRequest.getMethod()) && !"CARD_ON_FILE".equals(paymentRequest.getMethod()) )) {
            throw new BadRequestException("Not a valid request for card entry or card on file payment " + paymentRequest);
        }

        setBasicDetailsInfo(sendMessageDTO);
        BusinessDTO businessDTO = sendMessageDTO.getBusinessDTO();
        MessengerContact mc = sendMessageDTO.getMessengerContact();
        CustomerDTO customerDTO = sendMessageDTO.getCustomerDTO();
        Integer mcId = Integer.parseInt(sendMessageDTO.getToCustomerId());
        Integer businessId = sendMessageDTO.getFromBusinessId();
        Integer userId = sendMessageDTO.getUserId();

        UserDTO userDTO = userService.getUserDTO(userId);

        if(Objects.isNull(mc) || Objects.isNull(businessDTO) || Objects.isNull(userDTO)) {
            throw new BadRequestException(String.format("Either %d mc or %d business or %d user not found", mcId, businessId, userId));
        }
        
        CreatePaymentLinkRequest request = new CreatePaymentLinkRequest(sendMessageDTO,"CARD_ENTRY".equals(paymentRequest.getMethod()) ? CreatePaymentLinkRequest.PaymentRequestType.CARD_ENTRY : CreatePaymentLinkRequest.PaymentRequestType.CARD_ON_FILE);
        CreatePaymentLinkResponse createPaymentLinkResponse = paymentService.createPaymentRequest(request);

        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();
        Date createdAt = new Date();
        ContactDocument.Payment payment = getContactDocumentPayment(createPaymentLinkResponse,paymentRequest,createdAt);
        //payment.setText(); Not required since its a activity
        paymentMetaList.add(payment);
        lastMessageMetadataPOJO.setL_payment_on(createdAt.getTime()); // Doubtful here
        lastMessageMetadataPOJO.setPayment(paymentMetaList);
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(userDTO.getName());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

        MessangerBaseFilter filter = getMessangerBaseFilter(businessDTO, mc);

        List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
        ContactDocument contactDocument = updateContactDocument(userDTO, createdAt, payment, contactDocuments.get(0));
        mc.setTag(MessengerTagEnum.INBOX.getId());

        //assign conversation to the user
        IdentityDTO identityDTO = new IdentityDTO(userDTO.getId(), userDTO.getName(),"U" ,userDTO.getEmailId());
        ConversationAssignmentRequestDTO requestDTO = getConversationAssignmentRequestDTO(mcId, identityDTO);

        PaymentInfo paymentInfo = getPaymentInfo(paymentRequest, createPaymentLinkResponse, userDTO, createdAt);
        ActivityType activityType = request.getPaymentRequestType().equals(PaymentRequestType.CARD_ENTRY) ? ActivityType.MANUAL_CARD_ENTRY_PAYMENT_REQUEST : ActivityType.CARD_ON_FILE_PAYMENT;
        ActivityDto paymentActivity = getActivityDto(businessDTO, mc, userDTO, createdAt, paymentInfo, activityType);

        messengerContactService.saveOrUpdateMessengerContact(mc);
        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        conversationActivityService.persistActivityInDatabase(paymentActivity, userDTO);
        MessageDocument messageDocument = conversationActivityService.persistActivityInES(paymentActivity);
        log.info("id1:{} id2:{}",contactDocument.getCr_asgn_id(),userDTO.getId());
        if (!contactDocument.getCr_asgn_id().equals(userDTO.getId())){
            assignmentService.assignConversation(requestDTO,null,businessDTO.getAccountId());
        }
        firebaseService.pushToFireBase(contactDocument, messageDocument, false);

        SortedSet<MessageResponse.Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        String sendingTimeString = TimeZoneUtil.formatDateForTz(createdAt, Constants.DATE_FORMAT_MESSENGER_UI, businessDTO.getTimeZoneId());
        MessageResponse.Message message = new MessageResponse.Message(messageDocument, createdAt, sendingTimeString);
        allMessages.add(message);
        
        if(request.getPaymentRequestType().equals(PaymentRequestType.CARD_ENTRY)) {
        	//Payment Initiation notification to customer in case of manual card entry.
            PaymentNotificationRequest paymentNotificationReq = PaymentNotificationRequest.
                    fromPaymentEvent(paymentRequest, mc.getCustomerId(), mc.getBusinessId(),PaymentEvent.PaymentEventName.PAYMENT_INITIATED,mcId,customerDTO.getEmailId(),createPaymentLinkResponse,userId);
            kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_NOTIFICATION, paymentNotificationReq);
        }
        
        return new SendResponse(sendMessageDTO, allMessages, false, true);
    }
    
    
    private MessageResponse handleCardEntryPaymentWithNoContactFound(SendMessageDTO sendMessageDTO) {
    	log.info("Request for card entry payment with No contact: {}", sendMessageDTO.toString());
        PaymentUiRequest paymentRequest = sendMessageDTO.getPaymentRequest();
        if(Objects.isNull(paymentRequest) || !"CARD_ENTRY".equals(paymentRequest.getMethod())) {
            throw new BadRequestException("Not a valid request for card entry  payment " + paymentRequest);
        }

        setBasicDetailsInfo(sendMessageDTO);
        CreatePaymentLinkRequest request = new CreatePaymentLinkRequest(sendMessageDTO, CreatePaymentLinkRequest.PaymentRequestType.CARD_ENTRY);
        CreatePaymentLinkResponse createPaymentLinkResponse = paymentService.createPaymentRequest(request);

        MessengerContact mc = sendMessageDTO.getMessengerContact();
        BusinessDTO businessDTO = sendMessageDTO.getBusinessDTO();
        UserDTO userDTO = userService.getUserDTO(sendMessageDTO.getUserId());
        
        if(Objects.isNull(mc) || Objects.isNull(businessDTO) || Objects.isNull(userDTO)) {
            throw new BadRequestException(String.format("Either %d mc or %d business or %d user not found", mc.getId(), sendMessageDTO.getFromBusinessId(), sendMessageDTO.getUserId()));
        }
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();
        Date createdAt = new Date();
        ContactDocument.Payment payment = getContactDocumentPayment(createPaymentLinkResponse,paymentRequest,createdAt);
        //payment.setText(); Not required since its a activity
        paymentMetaList.add(payment);
        lastMessageMetadataPOJO.setL_payment_on(createdAt.getTime()); // Doubtful here
        lastMessageMetadataPOJO.setPayment(paymentMetaList);
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(userDTO.getName());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

        createdAt = new Date(payment.getCreatedAt());

        // TODO: Use builder method to create ContactDocument
        ContactDocument contactDocument = new ContactDocument();
        contactDocument.setE_id(businessDTO.getAccountId());
        contactDocument.setB_id(businessDTO.getBusinessId());
        contactDocument.setM_c_id(mc.getId());
        contactDocument.setPayments(Collections.singletonList(payment));
        contactDocument.setL_payment_on(createdAt.getTime());
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(createdAt));
        contactDocument.setHide(false);
        contactDocument.setC_tag(MessengerTagEnum.INBOX.getId());
        contactDocument.setLastMessageUserId(userDTO.getId());
        contactDocument.setLastMessageUserName(userDTO.getName());
        contactDocument.setCr_date(df.format(createdAt));
        contactDocument.setCr_asgn_id(Constants.Elastic.UNASSIGNED_ID);
        contactDocument.setCr_asgn_name(Constants.Elastic.UNASSIGNED_NAME);
        contactDocument.setTeam_id(Constants.Elastic.UNASSIGNED_ID);
        contactDocument.setTeam_name(Constants.Elastic.UNASSIGNED_NAME);
        contactDocument.setAssignmentType("U");
        
        
        mc.setPaymentReferenceId(createPaymentLinkResponse.getTransactionId());
        mc.setTag(MessengerTagEnum.INBOX.getId());
        
        
        //create activity for card entry
        PaymentInfo paymentInfo = getPaymentInfo(paymentRequest, createPaymentLinkResponse, userDTO, createdAt);
        ActivityDto manualCardEntryPaymentActivity = getActivityDto(businessDTO, mc, userDTO, createdAt, paymentInfo, ActivityType.MANUAL_CARD_ENTRY_PAYMENT_REQUEST);
        
        
        messengerContactService.saveOrUpdateMessengerContact(mc);
        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        conversationActivityService.persistActivityInDatabase(manualCardEntryPaymentActivity, userDTO);
       

        MessageDocument messageDocument = conversationActivityService.persistActivityInES(manualCardEntryPaymentActivity);

        firebaseService.pushToFireBase(contactDocument, messageDocument, false);
        
        SortedSet<MessageResponse.Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        String sendingTimeString = TimeZoneUtil.formatDateForTz(createdAt, Constants.DATE_FORMAT_MESSENGER_UI, businessDTO.getTimeZoneId());
        MessageResponse.Message message = new MessageResponse.Message(messageDocument, createdAt, sendingTimeString);
        allMessages.add(message);
        return new SendResponse(sendMessageDTO, allMessages, false, true);
    }
    
    private void setBasicDetailsInfo(SendMessageDTO sendMessageDTO){
        BusinessDTO businessDTO = businessService.getBusinessDTO(sendMessageDTO.getFromBusinessId());
        sendMessageDTO.setBusinessDTO(businessDTO);
        if(!StringUtils.isEmpty(sendMessageDTO.getToCustomerId())){
            MessengerContact mc =Objects.nonNull(sendMessageDTO.getMessengerContact()) ? 
            		sendMessageDTO.getMessengerContact() : messengerContactService.findById(Integer.parseInt(sendMessageDTO.getToCustomerId()));
            sendMessageDTO.setMessengerContact(mc);
            CustomerDTO customerDTO = contactService.findByIdWithCustomFields(mc.getCustomerId());
            sendMessageDTO.setCustomerDTO(customerDTO);
        }else{
            MessengerContact mc = messengerContactService.createMessengerConversation(sendMessageDTO.getFromBusinessId());

            sendMessageDTO.setMessengerContact(mc);
        }
    }

    private ActivityDto getActivityDto(BusinessDTO businessDTO, MessengerContact mc, UserDTO userDTO, Date createdAt, PaymentInfo paymentInfo, ActivityType activityType) {
        return ActivityDto.builder().mcId(mc.getId()).created(createdAt).updated(createdAt).actorId(userDTO.getId()).actorName(userDTO.getName())
                    .activityType(activityType)
                    .activityMeantFor(ActivityMeantFor.CUSTOMER)
                    .paymentInfo(paymentInfo)
                    .type(MessageDocument.Type.CREATE)
                    .accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
                    .build();
    }
    

    private ConversationAssignmentRequestDTO getConversationAssignmentRequestDTO(Integer mcId, IdentityDTO identityDTO) {
        ConversationAssignmentRequestDTO requestDTO = new ConversationAssignmentRequestDTO();
        requestDTO.setTo(identityDTO);
        requestDTO.setMcId(mcId);
        requestDTO.setDoNotNotify(true);
        return requestDTO;
    }

    private MessangerBaseFilter getMessangerBaseFilter(BusinessDTO businessDTO, MessengerContact mc) {
        MessangerBaseFilter filter = new MessangerBaseFilter();
        filter.setAccountId(businessDTO.getAccountId());
        filter.setConversationId(mc.getId());
        filter.setCount(1);
        return filter;
    }

    private ContactDocument updateContactDocument(UserDTO userDTO, Date createdAt, ContactDocument.Payment payment, ContactDocument contactDocument) {
        if(CollectionUtils.isEmpty(contactDocument.getPayments())) {
        	contactDocument.setPayments(new ArrayList<>());
        }
        contactDocument.getPayments().add(payment);
        contactDocument.setL_payment_on(createdAt.getTime());
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(createdAt));
        contactDocument.setHide(false);
        contactDocument.setC_tag(MessengerTagEnum.INBOX.getId());
        contactDocument.setLastMessageUserId(userDTO.getId());
        contactDocument.setLastMessageUserName(userDTO.getName());
        return contactDocument;
    }

    private PaymentInfo getPaymentInfo(PaymentUiRequest paymentRequest, CreatePaymentLinkResponse createPaymentLinkResponse, UserDTO userDTO, Date createdAt) {
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setId(createPaymentLinkResponse.getPaymentRequestId());
        paymentInfo.setAmount(paymentRequest.getItems().get(0).getAmount());
        if("CARD_ON_FILE".equals(paymentRequest.getMethod())) {
        	paymentInfo.setStatus(getCardOnFilePaymentStatus(createPaymentLinkResponse));
        	paymentInfo.setPaymentVia(createPaymentLinkResponse.getPaymentVia());
        	paymentInfo.setLastFourDigit(createPaymentLinkResponse.getLastFourDigits());
        	paymentInfo.setReceiptUrl(createPaymentLinkResponse.getReceiptUrl());
        }else {
        	paymentInfo.setStatus(PaymentStatus.NOT_PAID);
        }
        paymentInfo.setReqCreatedById(userDTO.getId());
        paymentInfo.setReqCreatedByName(userDTO.getName());
        paymentInfo.setReqCreatedAt(createdAt.getTime());
        paymentInfo.setMethod(paymentRequest.getMethod());
        paymentInfo.setInvoiceNumber(paymentRequest.getInvoiceNumber());
        paymentInfo.setLink(createPaymentLinkResponse.getPaymentLink());
        paymentInfo.setItemDesc(paymentRequest.getItems().get(0).getItemDesc());
        paymentInfo.setTransactionId(createPaymentLinkResponse.getTransactionId());
        paymentInfo.setCurrency(createPaymentLinkResponse.getCurrency());
        return paymentInfo;
    }

    private ContactDocument.Payment getContactDocumentPayment(CreatePaymentLinkResponse createPaymentLinkResponse,PaymentUiRequest paymentRequest,Date createdAt) {
        ContactDocument.Payment payment = new ContactDocument.Payment();
        payment.setPaymentId(createPaymentLinkResponse.getPaymentRequestId());
        if("CARD_ON_FILE".equals(paymentRequest.getMethod())) {
        	payment.setStatus(getCardOnFilePaymentStatus(createPaymentLinkResponse)); 
        }else {
        	payment.setStatus(PaymentStatus.NOT_PAID);
        }
        payment.setCreatedAt(createdAt.getTime());
        payment.setUpdatedAt(createdAt.getTime());
        payment.setAmount(paymentRequest.getItems().get(0).getAmount());
        payment.setMethod(paymentRequest.getMethod());
        payment.setCurrency(createPaymentLinkResponse.getCurrency());
        return  payment;
    }
    
    private PaymentStatus getCardOnFilePaymentStatus(CreatePaymentLinkResponse createPaymentLinkResponse) {
    	if(createPaymentLinkResponse.getStatus().equals("FAILED")  ) {
    		return PaymentStatus.FAILED;
    	}
    	
    	return PaymentStatus.COMPLETED;
    	
    	}
}
