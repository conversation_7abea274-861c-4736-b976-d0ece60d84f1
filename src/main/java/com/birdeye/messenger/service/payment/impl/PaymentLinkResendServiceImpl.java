package com.birdeye.messenger.service.payment.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.TenDlcStatusDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkResponse;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentItem;
import com.birdeye.messenger.dto.payment.PaymentResendRequest;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.payment.PaymentLinkResendService;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentLinkResendServiceImpl implements PaymentLinkResendService {

    private final MessengerEventHandlerService messengerEventHandlerService;
    private final ElasticSearchExternalService elasticSearchService;
    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final NexusService nexusService;

    @Override
    public MessageResponse resendPaymentLink(PaymentResendRequest request) throws Exception {

        MessengerContact mc = messengerContactService.findById(request.getMcId());
        if(Objects.isNull(mc)) throw new BadRequestException(String.format("Contact %d not found", request.getMcId()));

        BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());
        if(Objects.isNull(businessDTO)) throw new BadRequestException(String.format("Business for businessId %d not found", mc.getBusinessId()));

        MessageDocument paymentMsg = getLastSentPaymentRequest(request, mc, businessDTO);

        paymentMsg.getPaymentInfo().setResent(true);
        messengerContactService.updateMessageOnES(paymentMsg, businessDTO.getRoutingId());

        SendMessageDTO sendRequest = createResendRequest(paymentMsg, mc, businessDTO, request);
        return messengerEventHandlerService.handleEvent(sendRequest);

    }

    private SendMessageDTO createResendRequest(MessageDocument doc, MessengerContact mc, BusinessDTO businessDTO, PaymentResendRequest req) {

        String body = MessengerUtil.decryptMessage(doc);
        TenDlcStatusDTO tenDlcStatusDTO = nexusService.checkTenDlcStatus(businessDTO.getEnterpriseNumber());
		if(Objects.nonNull(tenDlcStatusDTO) && tenDlcStatusDTO.isUsAccount()){
			String status = tenDlcStatusDTO.getStatus();
			TenDlcStatusDTO.TollFreeInfoDTO tollFreeInfo = tenDlcStatusDTO.getTollFreeInfo();
			if (status != null && (status.equals("not_started") || status.equals("in_progress") || status.equals("failed"))
					&& tollFreeInfo != null && tollFreeInfo.isAvailable()) {
				body = body.replace(("\n\n").concat(tenDlcStatusDTO.getTollFreeInfo().getBranding()),"");
			}
		}
        SendMessageDTO sendMessageDTO = new SendMessageDTO();

        PaymentUiRequest paymentUiRequest = new PaymentUiRequest();
        PaymentInfo paymentInfo = doc.getPaymentInfo();
        PaymentItem paymentItem = new PaymentItem();
        paymentItem.setAmount(paymentInfo.getAmount());
        paymentItem.setItemDesc(paymentInfo.getItemDesc());
        List<PaymentItem> paymentItemList = new ArrayList<>();
        paymentItemList.add(paymentItem);
        paymentUiRequest.setItems(paymentItemList);
        paymentUiRequest.setResend(true);
        paymentUiRequest.setInvoiceNumber(paymentInfo.getInvoiceNumber());
        paymentUiRequest.setMethod(paymentInfo.getMethod());

        sendMessageDTO.setPaymentRequest(paymentUiRequest);
        sendMessageDTO.setBody(body);
        sendMessageDTO.setPaymentRequestEmailBody(body.replace(paymentInfo.getLink(), "").trim());
        sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
        sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
        sendMessageDTO.setToCustomerId(String.valueOf(mc.getId()));
        sendMessageDTO.setUserId(req.getUserId());
        sendMessageDTO.setSource(req.getSource());

        CreatePaymentLinkResponse createPaymentLinkResponse = new CreatePaymentLinkResponse();
        createPaymentLinkResponse.setLinkCreatedAt(new Date().getTime());
        createPaymentLinkResponse.setPaymentLink(paymentInfo.getLink());
        createPaymentLinkResponse.setPaymentRequestId(paymentInfo.getId());
        createPaymentLinkResponse.setTransactionId(paymentInfo.getTransactionId());
        createPaymentLinkResponse.setCurrency(paymentInfo.getCurrency());
        sendMessageDTO.setCreatePaymentLinkResponse(createPaymentLinkResponse);

        return sendMessageDTO;

        /**
         * {
         *     "body": "Paisa de.",
         *     "fromBusinessId": 100037151,
         *     "businessIdentifierId": 100037151,
         *     "toCustomerId": 205656,
         *     "mediaUrls": [],
         *     "userId": "25266",
         *     "paymentRequest": {
         *         "invoiceNumber": "235ae",
         *         "items": [
         *             {
         *                 "itemDesc": "ps 1 purchase",
         *                 "amount": "26262.34"
         *             }
         *         ]
         *     }
         * }
         */


    }

    private MessageDocument getLastSentPaymentRequest(PaymentResendRequest req, MessengerContact mc, BusinessDTO businessDTO) {


        Map<String, Object> data = new HashMap<>();
        data.put("paymentId", req.getPaymentId());
        data.put("m_c_id", mc.getId());

        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(businessDTO.getRoutingId())
                .addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID, data).build();
        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

        List<MessageDocument> results = dataFromElastic.getResults();

        return results.stream().filter(doc -> doc.getMessageType().equals(MessageDocument.MessageType.CHAT))
                .findFirst()
                .orElseThrow(() -> new BadRequestException(String.format("Payment Request for contact %d not found", mc.getId())));
    }

}
