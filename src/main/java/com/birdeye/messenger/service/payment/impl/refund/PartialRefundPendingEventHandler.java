package com.birdeye.messenger.service.payment.impl.refund;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.PaymentStatus;
import org.springframework.stereotype.Service;

@Service
public class PartialRefundPendingEventHandler extends AbstractRefundHandler {

    @Override
    ActivityType getActivityType() {
        return ActivityType.PAYMENT_PARTIAL_REFUND;
    }

    @Override
    PaymentStatus getPaymentStatusForMessage() {
        return PaymentStatus.PENDING;
    }

    @Override
    PaymentStatus getPaymentStatusForConversation() {
        return PaymentStatus.PARTIAL_REFUND_PENDING;
    }

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_PARTIAL_REFUND_PENDING;
    }
}
