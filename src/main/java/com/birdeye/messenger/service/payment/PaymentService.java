package com.birdeye.messenger.service.payment;

import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.PaymentTemplate;
import com.birdeye.messenger.dto.payment.CardReaderPaymentIntentResponse;
import com.birdeye.messenger.dto.payment.CardReaderPaymentRequest;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkRequest;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkResponse;

public interface PaymentService {

    CreatePaymentLinkResponse createPaymentRequest(CreatePaymentLinkRequest createPaymentLinkRequest);

    CardReaderPaymentIntentResponse createCardReaderPaymentIntent(CardReaderPaymentRequest cardReaderPaymentRequest);

    void decorateWithPaymentLink(MessageDTO messageDTO);

    PaymentTemplate getMobPaymentTemplate(Integer businessId);
}
