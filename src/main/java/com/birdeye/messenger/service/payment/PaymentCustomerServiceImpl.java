package com.birdeye.messenger.service.payment;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.payment.PaymentCustomerContextRequest;
import com.birdeye.messenger.dto.payment.PaymentCustomerContextResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisLockService;
import com.google.common.base.Preconditions;

@Service
public class PaymentCustomerServiceImpl implements PaymentCustomerService {

	@Autowired
	ContactService contactService;

	@Autowired
	RedisLockService redisLockService;

	@Autowired
	MessengerContactService messengerContactService;

	@Autowired
	BusinessService businessService;

	@Override
	public PaymentCustomerContextResponse getOrCreateConversationId(PaymentCustomerContextRequest request) {
		KontactoRequest koncKontactoRequest = new KontactoRequest(request);

		CustomerDTO customerDTO = contactService.getorCreateNewCustomer(koncKontactoRequest, request.getAccountId(), -1);

		String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
		Optional<Lock> lockOpt = Optional.empty();
		lockOpt = redisLockService.tryLock(lockKeyCustomer,2,TimeUnit.SECONDS);
		try {
			if(lockOpt.isPresent()) {
				MessengerContact messengerContact = messengerContactService.findByCustomerId(customerDTO.getId());
				PaymentCustomerContextResponse response = PaymentCustomerContextResponse.builder()
						.customerDTO(customerDTO).build();
				if(null != messengerContact) {
					response.setMessengerContactId(messengerContact.getId());
					return response;
				}

				BusinessDTO businessDTO = businessService.getBusinessLiteDTO(customerDTO.getBusinessId());
				Preconditions.checkArgument(Objects.nonNull(customerDTO) && Objects.nonNull(businessDTO), "customerDTO %s and businessDTO %s must not be null", customerDTO, businessDTO);
				messengerContact = messengerContactService.createContact(customerDTO.getId(), customerDTO.getBusinessId());
				messengerContact.setLastMessage("");
				messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX, null);

				response.setMessengerContactId(messengerContact.getId());
				return response;
			} else {
				throw new RedisLockException(ErrorCode.UNABLE_TO_ACQUIRE_LOCK);
			}
		}finally {
			if(lockOpt.isPresent()) {
				redisLockService.unlock(lockOpt.get());
			}
		}
	}

}
