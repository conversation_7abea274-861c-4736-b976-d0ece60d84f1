package com.birdeye.messenger.service.payment.impl.request;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.PaymentStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class PaymentCompletionSuccessEventHandler extends PaymentCompletionEventHandler {

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_COMPLETED;
    }

    @Override
    protected PaymentStatus getPaymentStatusForMessage(PaymentEvent paymentEvent) {
        if(StringUtils.isNotBlank(paymentEvent.getReceiptUrl())) return PaymentStatus.COMPLETED;
        return PaymentStatus.MARKED_AS_PAID;
    }

    @Override
    protected PaymentStatus getPaymentStatusForConversation(PaymentEvent paymentEvent) {
        if(StringUtils.isNotBlank(paymentEvent.getReceiptUrl())) return PaymentStatus.COMPLETED;
        return PaymentStatus.MARKED_AS_PAID;
    }

    @Override
    boolean sendNotifications() {
        return true;
    }
}
