package com.birdeye.messenger.service.payment.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.PaymentTemplate;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.payment.CRMInvoiceMessage;
import com.birdeye.messenger.dto.payment.PaymentItem;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentAutomationUser;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.MessengerEventHandlerService;
import com.birdeye.messenger.service.SamayService;
import com.birdeye.messenger.service.impl.CommunicationHelperService;
import com.birdeye.messenger.service.payment.CRMPaymentsService;
import com.birdeye.messenger.service.payment.PaymentService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CRMPaymentsServiceImpl implements CRMPaymentsService {

	private final BusinessService businessService;
	private final ContactService contactService;
	private final MessengerContactService messengerContactService;
	private final CommunicationHelperService communicationHelperService;
	private final MessengerEventHandlerService messengerEventHandlerService;
	private final SamayService samayService;
    private final PaymentService paymentService;

	@Override
	public void handleCreateInvoiceEvent(CRMInvoiceMessage inputMessage) throws Exception {
		// 1. Find Business
		BusinessDTO businessDTO = businessService.getBusinessDTO(inputMessage.getBusinessId());
		// 2. Submit to Samay service for delay
		if (inputMessage.getCommunicationPreference()!=null && inputMessage.getCommunicationPreference().getDelayInHours()>0) {
			log.info("CRM handleCreateInvoiceEvent - submit to samay");
			Long timeOut = Long.valueOf(inputMessage.getCommunicationPreference().getDelayInHours());
			inputMessage.getCommunicationPreference().setDelayInHours(0);
			Long expiry = DateUtils.samayExpiryDateCalculation("hours", timeOut);
			samayService.publishToScheduler(inputMessage, expiry,KafkaTopicEnum.PAYMENT_DELAY_SAMAY_EVENT);
			return;
		}
		// 3. Validate Request
		if (validate(inputMessage, businessDTO)) {
			// 4. Create Customer
			KontactoRequest kontactoRequest = createKontactoRequest(inputMessage, businessDTO.getCountryCode());
			CustomerDTO customerDTO = contactService.getorCreateNewCustomer(kontactoRequest, businessDTO.getRoutingId(),
					PaymentAutomationUser.getPaymentAutomationUserByName(inputMessage.getAutomationUser(),businessDTO.getAccountId()).getUserId());
			// 5. Customer history for New contact created via CRM
			//N/A

			// 6. Create MessengerContact
			MessengerContact messengerContact = messengerContactService.getOrCreateContactForExistingCustomer(
					inputMessage.getBusinessId(), customerDTO.getId(), businessDTO.getAccountId());

			// 7. Update MC on ES
			messengerContactService.updateContactOnES(messengerContact, customerDTO, businessDTO, MessageTag.INBOX, null);

			// 8. Prepare Send Request
			SendMessageDTO sendMessageDTO = getSMSDataForCRMEvent(inputMessage, businessDTO, messengerContact);

			// 9. Send
			log.info("CRM handleCreateInvoiceEvent - send sms {}", sendMessageDTO);
			messengerEventHandlerService.handleEvent(sendMessageDTO);
		}
	}

	private boolean validate(CRMInvoiceMessage inputMessage, BusinessDTO businessDTO) {
		if(businessDTO==null) {
			log.error("CRM - BusinessId: {} not found",inputMessage.getBusinessId());
			return false;
		}
		if (inputMessage.getCustomerInfo() == null) {
			log.error("CRM - Customer info cannot be null");
			return false;
		}
		if (inputMessage.getCustomerInfo().getEmail() == null && inputMessage.getCustomerInfo().getPhone() == null) {
			log.error("CRM - Customer phone or email must be present");
			return false;
		}
		if (inputMessage.getInvoiceDetails() == null) {
			log.error("CRM - Invoice details cannot be null");
			return false;
		}
		if (inputMessage.getCommunicationPreference() == null) {
			log.error("CRM - Communication preference cannot be null");
			return false;
		}
		PaymentTemplate template = null;
		String formattedText = null;
        Integer accountId = businessDTO.getAccountId();
		if (inputMessage.getCustomerInfo().getEmail()!=null && inputMessage.getCommunicationPreference().isSendEmail()
				&& inputMessage.getCommunicationPreference().getEmailTemplate() == null) {
            template = paymentService.getMobPaymentTemplate(accountId);
            formattedText = ControllerUtil.replacePaymentTokens(template.getRawDescription(), inputMessage,
                    businessDTO);
			inputMessage.getCommunicationPreference().setEmailTemplate(formattedText);
			
		}
		if (inputMessage.getCustomerInfo().getPhone()!=null && inputMessage.getCommunicationPreference().isSendText()
				&& inputMessage.getCommunicationPreference().getTextTemplate() == null) {
			if (!StringUtils.isEmpty(formattedText))
				inputMessage.getCommunicationPreference().setTextTemplate(formattedText);
			else {
                template = paymentService.getMobPaymentTemplate(accountId);
				formattedText = ControllerUtil.replacePaymentTokens(template.getRawDescription(), inputMessage, businessDTO);
				inputMessage.getCommunicationPreference().setTextTemplate(formattedText);
			}
		}
		return true;
	}

	private KontactoRequest createKontactoRequest(CRMInvoiceMessage inputMessage, String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String name = "";
		name = inputMessage.getCustomerInfo().getFirstName();
		if (StringUtils.isBlank(inputMessage.getCustomerInfo().getFirstName()) && StringUtils.isNotBlank(inputMessage.getCustomerInfo().getLastName())) {
			name =inputMessage.getCustomerInfo().getLastName();
		} else if (StringUtils.isNotBlank(inputMessage.getCustomerInfo().getLastName())) {
			name = name + " " +inputMessage.getCustomerInfo().getLastName();
		}

		kontactoRequest.setName(name);
		kontactoRequest.setEmailId(inputMessage.getCustomerInfo().getEmail());
		kontactoRequest.setPhone(inputMessage.getCustomerInfo().getPhone());
		kontactoRequest.setSource(KontactoRequest.OTHER);
		kontactoRequest.setBusinessId(inputMessage.getBusinessId());

		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);

		return kontactoRequest;
	}

	private SendMessageDTO getSMSDataForCRMEvent(CRMInvoiceMessage inputMessage, BusinessDTO businessDTO,
			MessengerContact messengerContact) {
		SendMessageDTO sendMessageDTO = new SendMessageDTO();
		sendMessageDTO.setFromBusinessId(businessDTO.getBusinessId());
		sendMessageDTO.setToCustomerId(String.valueOf(messengerContact.getId()));
		sendMessageDTO.setCustomerId(messengerContact.getCustomerId());
		sendMessageDTO.setBusinessIdentifierId(String.valueOf(businessDTO.getBusinessId()));
		sendMessageDTO.setMessengerContact(messengerContact);
		// default source is sms
		if (sendMessageDTO.getSource() == null) {
			sendMessageDTO.setSource(1);
		}
		//if both email & text is present - preference is sms
		if (inputMessage.getCustomerInfo().getEmail()!=null && inputMessage.getCommunicationPreference().isSendEmail()
				&& inputMessage.getCommunicationPreference().getEmailTemplate() != null) {
			sendMessageDTO.setSource(111);
			sendMessageDTO.setBody(inputMessage.getCommunicationPreference().getEmailTemplate());
		}
		if (inputMessage.getCustomerInfo().getPhone()!=null && inputMessage.getCommunicationPreference().isSendText()
				&& inputMessage.getCommunicationPreference().getTextTemplate() != null) {
			sendMessageDTO.setSource(1);
			sendMessageDTO.setBody(inputMessage.getCommunicationPreference().getTextTemplate());
		}
		PaymentAutomationUser automationUser=PaymentAutomationUser.getPaymentAutomationUserByName(inputMessage.getAutomationUser(),businessDTO.getAccountId());
		UserDTO	userDTO = communicationHelperService.getUserDTO(automationUser.getUserId());
		sendMessageDTO.setUserId(automationUser.getUserId());		
		sendMessageDTO.setUserDTO(userDTO);

		PaymentUiRequest paymentRequest = createPaymentRequest(inputMessage);
		sendMessageDTO.setPaymentRequest(paymentRequest);

		if(inputMessage.getInvoiceDetails() != null && StringUtils.isNotEmpty(inputMessage.getInvoiceDetails().getMediaUrl())) {	
			MessengerMediaFile  mediaFile =  new MessengerMediaFile(inputMessage.getInvoiceDetails().getMediaUrl());
			List<MessengerMediaFile> mediaFiles = Arrays.asList(mediaFile);		
			sendMessageDTO.setMediaUrls(mediaFiles);
		}
		return sendMessageDTO;
	}

	private PaymentUiRequest createPaymentRequest(CRMInvoiceMessage inputMessage) {
		PaymentUiRequest paymentRequest = new PaymentUiRequest();
		List<PaymentItem> items = new ArrayList<>();

		PaymentItem item = new PaymentItem();
		item.setItemDesc(inputMessage.getInvoiceDetails().getInvoiceItemDesc());
		item.setAmount(inputMessage.getInvoiceDetails().getTotalAmount()*100);
		items.add(item);

		paymentRequest.setItems(items);
		paymentRequest.setInvoiceNumber(inputMessage.getInvoiceDetails().getInvoiceNumber());
		paymentRequest.setMethod("ONLINE");
		paymentRequest.setCrmName(inputMessage.getCrmName());
		paymentRequest.setCrmDocId(inputMessage.getCrmDocId());
		paymentRequest.setCrmCustomerId(inputMessage.getCrmCustomerId());
		paymentRequest.setExtTrackingId(inputMessage.getExtTrackingId());
		paymentRequest.setInvoiceDueDate(inputMessage.getInvoiceDetails().getInvoiceDueDate());
		paymentRequest.setTags(inputMessage.getInvoiceDetails().getTags());
		paymentRequest.setCustomFields(inputMessage.getInvoiceDetails().getCustomFields());
		return paymentRequest;
	}

}
