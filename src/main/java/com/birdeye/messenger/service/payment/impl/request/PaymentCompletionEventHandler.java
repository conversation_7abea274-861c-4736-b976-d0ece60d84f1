package com.birdeye.messenger.service.payment.impl.request;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentNotificationRequest;
import com.birdeye.messenger.dto.payment.PaymentInfo.SubscriptionInfo;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.enums.PaymentType;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.payment.PaymentEventHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.google.common.base.Preconditions;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public abstract class PaymentCompletionEventHandler implements PaymentEventHandler {


	@Autowired private MessengerContactService messengerContactService;
	@Autowired private BusinessService businessService;
	@Autowired private ConversationActivityService conversationActivityService;
	@Autowired private ElasticSearchExternalService elasticSearchService;
	@Autowired private FirebaseService firebaseService;
	@Autowired private KafkaService kafkaService;
	@Autowired private UserService userService;

	@Override
	public void handle(PaymentEvent paymentEvent) {
		
		if(Objects.nonNull(paymentEvent.getPaymentType()) && paymentEvent.getPaymentType().equals("Card on file")){
			log.info("Received payment event for Card on file payment");
			return;
		}
		validate(paymentEvent);

		MessengerContact mc = messengerContactService.findById(paymentEvent.getConversationId());
		BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());

		MessangerBaseFilter filter = new MessangerBaseFilter();
		filter.setAccountId(businessDTO.getAccountId());
		filter.setConversationId(mc.getId());
		filter.setCount(1);

		List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
		ContactDocument contactDocument = contactDocuments.get(0);
		List<ContactDocument.Payment> payments = contactDocument.getPayments();

		if(Objects.nonNull(payments)) {
			for(ContactDocument.Payment payment : payments) {
				if(payment.getPaymentId().equals(paymentEvent.getId())) {
					payment.setStatus(getPaymentStatusForConversation(paymentEvent));
					payment.setUpdatedAt(paymentEvent.getTimestamp());
					payment.setAmount(paymentEvent.getAmount());
				}
			}
		}

		LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
		List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();
		
		for(ContactDocument.Payment payment : paymentMetaList) {
			if(payment.getPaymentId().equals(paymentEvent.getId())) {
				payment.setStatus(getPaymentStatusForConversation(paymentEvent));
				payment.setUpdatedAt(paymentEvent.getTimestamp());
				payment.setAmount(paymentEvent.getAmount());
			}
		}
		
		Map<String, Object> data = new HashMap<>();
		data.put("paymentId", paymentEvent.getId());
		data.put("m_c_id", mc.getId());
		Map<String, Object> scriptData = new HashMap<>();
		Long updateTimeStamp = paymentEvent.getTimestamp();
		String inlineValue = "ctx._source.paymentInfo.status=" + "\"" + getPaymentStatusForMessage(paymentEvent) + "\"" +
				"; ctx._source.u_time=" + "\"" + updateTimeStamp + "\"" + "; ctx._source.type=" + "\"" + MessageDocument.Type.UPDATE.getType() + "\"" +
				"; ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'";

		if(PaymentType.WEBSITE_PAYMENT.getName().equals(paymentEvent.getPaymentType()) ||
				PaymentType.QR_CODE.getName().equals(paymentEvent.getPaymentType())) {
			/* In case of payment paid via ACH, update the processing bubble to payment completed */
			if(PaymentType.ACH_PAYMENT.getName().equals(paymentEvent.getPaymentMethod()) || PaymentType.AU_BECS.getName().equals(paymentEvent.getPaymentMethod())
				|| PaymentType.AFFIRM.getName().equals(paymentEvent.getPaymentVia())) {
				inlineValue = inlineValue + "; ctx._source.activityType=\"" + ActivityType.PAYMENT_COMPLETED + "\"" + 
				"; ctx._source.paymentInfo.receiptUrl=" + "\"" + paymentEvent.getReceiptUrl() + "\"";	
			}
			
			ContactDocument.Payment payment = createContactDocumentPaymentInfo(paymentEvent);
			paymentMetaList.add(payment);
	        lastMessageMetadataPOJO.setPayment(paymentMetaList);
	        if(contactDocument.getPayments() == null || contactDocument.getPayments().isEmpty()) {
	        	contactDocument.setPayments(Collections.singletonList(payment));
	        } else {
	        	contactDocument.getPayments().add(payment);   
	        }
	        contactDocument.setHide(false); // to make contactDocument visible on filters
		}
		
		lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
		mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

		contactDocument.setL_payment_on(paymentEvent.getTimestamp());
		SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		
		contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));

		mc.setTag(MessageTag.INBOX.getCode());
		contactDocument.setC_tag(MessageTag.INBOX.getCode());

		if(getActivityType(paymentEvent).equals(ActivityType.MARKED_AS_PAID)) {
			UserDTO userDTO = userService.getUserDTO(paymentEvent.getUserId());
			mc.setIsRead(false);
			mc.setViewedBy(StringUtils.join(Collections.singletonList(userDTO.getId()), ','));
			contactDocument.setViewedBy(Collections.singletonList(userDTO.getId()));
			contactDocument.setC_read(false);
		} else {
			mc.setIsRead(false);
			mc.setViewedBy(null);
			contactDocument.setViewedBy(new ArrayList<>());
			contactDocument.setC_read(false);
		}

		scriptData.put("inline", inlineValue);
		ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest())
				.index(Constants.Elastic.MESSAGE_INDEX)
				.queryTemplateFile(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID)
				.routingId(businessDTO.getRoutingId())
				.freeMarkerDataModel(data)
				.scriptParam(scriptData);
		
		boolean response = elasticSearchService.updateByQuery(builder.build());
		if(!response) {
			log.error("Payment Completion Event failed to update in ES, mcId {}, Reason {}", paymentEvent.getConversationId(), response);
			throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
		}
		
		/* In case of (Payment Type == Webform  && payment mode == ACH ); 
		 * card activity has already been created from processing event handler we just need to 
		 * update payment status and no need to create new card activity for this
		 */
		if(!(PaymentType.WEBSITE_PAYMENT.getName().equals(paymentEvent.getPaymentType()) &&
				(PaymentType.ACH_PAYMENT.getName().equals(paymentEvent.getPaymentMethod()) || PaymentType.AU_BECS.getName().equals(paymentEvent.getPaymentMethod())) )) {

			PaymentInfo paymentInfo = createPaymentInfo(paymentEvent,businessDTO);
	
			Date created = new Date(paymentEvent.getTimestamp());
			ActivityDto paymentCompletedActivity = ActivityDto.builder().mcId(mc.getId()).created(created).updated(created)
					.activityType(getActivityType(paymentEvent))
					.activityMeantFor(ActivityMeantFor.CUSTOMER)
					.paymentInfo(paymentInfo)
					.type(MessageDocument.Type.CREATE)
					.accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
					.build();

			conversationActivityService.persistActivityInDatabase(paymentCompletedActivity, null);
			conversationActivityService.persistActivityInES(paymentCompletedActivity);
		}
		
		messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
		messengerContactService.saveOrUpdateMessengerContact(mc);

		ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(businessDTO.getRoutingId())
                .addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID, data).build();
        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

        List<MessageDocument> results = dataFromElastic.getResults();
        Optional<MessageDocument> paymentRequestDocOpt = results.stream().filter(doc -> {
            return doc.getMessageType().equals(MessageDocument.MessageType.CHAT) || doc.getMessageType().equals(MessageDocument.MessageType.ACTIVITY);
        }).findFirst();
        
        firebaseService.pushToFireBase(contactDocument, paymentRequestDocOpt.get(), false);

//		if(sendNotifications() && !getActivityType(paymentEvent).equals(ActivityType.MARKED_AS_PAID)) {
//			log.info("Activity type:{}",getActivityType(paymentEvent));
//			PaymentNotificationRequest notificationReq = PaymentNotificationRequest.fromPaymentEvent(paymentEvent, mc.getCustomerId(), businessDTO.getBusinessId());
//			kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_NOTIFICATION, notificationReq);
//		}

	}

	@Override
	public void validate(PaymentEvent paymentEvent) {
		Preconditions.checkArgument(Objects.nonNull(paymentEvent.getAmount()), "Amount cannot be empty");
		// In case of terminal request item details can be null
		if(!StringUtils.isEmpty(paymentEvent.getPaymentMethod()) && !PaymentType.TERMINAL.getName().equals(paymentEvent.getPaymentMethod())) {
			Preconditions.checkArgument(StringUtils.isNotBlank(paymentEvent.getItemDetail()), "Item Details cannot be empty");
		}
	}

	abstract protected PaymentStatus getPaymentStatusForMessage(PaymentEvent paymentEvent);

	abstract protected PaymentStatus getPaymentStatusForConversation(PaymentEvent paymentEvent);

	abstract boolean sendNotifications();


	protected ActivityType getActivityType(PaymentEvent event) {
		if(StringUtils.isNotBlank(event.getReceiptUrl())) {
			if(Objects.nonNull(event.getAmountPending()) && event.getAmountPending() > 0)
				return ActivityType.PARTIALLY_PAID;
			else
				return ActivityType.PAYMENT_COMPLETED;
		}
		return ActivityType.MARKED_AS_PAID;
	}
	
	private ContactDocument.Payment createContactDocumentPaymentInfo(PaymentEvent paymentEvent){
        ContactDocument.Payment payment = new ContactDocument.Payment();
        payment.setPaymentId(paymentEvent.getId());
        payment.setStatus(PaymentStatus.COMPLETED);
        payment.setUpdatedAt(paymentEvent.getTimestamp());
        payment.setAmount(paymentEvent.getAmount());
        payment.setMethod(paymentEvent.getPaymentMethod());
        payment.setCurrency(paymentEvent.getCurrencyCode());
        
        return payment;
    }
	
	private PaymentInfo createPaymentInfo(PaymentEvent paymentEvent, BusinessDTO businessDTO) {
		
		PaymentInfo paymentInfo = new PaymentInfo();
		SubscriptionInfo.SubscriptionInfoBuilder subscriptionInfoBuilder = null;
		if(Objects.nonNull(paymentEvent.getSubscriptionEventDetails())) {
			subscriptionInfoBuilder = SubscriptionInfo.builder()
					.id(paymentEvent.getId())
					.paymentVia(paymentEvent.getSubscriptionEventDetails().getPaymentVia())
					.lastFourDigit(paymentEvent.getSubscriptionEventDetails().getLast4());
			if(null != paymentEvent.getSubscriptionEventDetails().getDueDate()) {
				DateFormat df = new SimpleDateFormat(Constants.DATE_FORMAT_MESSENGER_PAYMENTS);
				Date dueDate = ControllerUtil.convertToBusinessTimeZone(new Date(paymentEvent.getSubscriptionEventDetails().getDueDate()), TimeZone.getTimeZone(businessDTO.getTimeZoneId())).getTime();
				subscriptionInfoBuilder.dueDate(df.format(dueDate));	
			}
			paymentInfo.setSubscription(true);
		}
		
		paymentInfo.setReceiptUrl(paymentEvent.getReceiptUrl());
		paymentInfo.setAmount(paymentEvent.getAmount());
		paymentInfo.setAmountPending(paymentEvent.getAmountPending());
		paymentInfo.setAmountRequested(paymentEvent.getAmountRequested());
		paymentInfo.setTransactionFeePaidByCustomer(paymentEvent.getTransactionFeePaidByCustomer());
		paymentInfo.setPaymentVia(paymentEvent.getPaymentVia());
		paymentInfo.setLastFourDigit(paymentEvent.getLast4());
		paymentInfo.setInvoiceNumber(paymentEvent.getInvoiceNumber());
		paymentInfo.setStatus(getPaymentStatusForMessage(paymentEvent));
		paymentInfo.setId(paymentEvent.getId());
		paymentInfo.setTransactionId(paymentEvent.getTransactionId());
		paymentInfo.setMethod(paymentEvent.getPaymentMethod());
		paymentInfo.setItemDesc(paymentEvent.getItemDetail());
		paymentInfo.setMemo(paymentEvent.getMemo());
		paymentInfo.setPaymentType(paymentEvent.getPaymentType());
		paymentInfo.setSubscriptionInfo(Objects.nonNull(subscriptionInfoBuilder) ? subscriptionInfoBuilder.build() : null);
		paymentInfo.setCurrency(paymentEvent.getCurrencyCode());
		return paymentInfo;
	}
}
