package com.birdeye.messenger.service.payment.impl.refund;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.PaymentStatus;
import org.springframework.stereotype.Service;

@Service
public class FullRefundPendingEventHandler extends AbstractRefundHandler {

    @Override
    ActivityType getActivityType() {
        return ActivityType.PAYMENT_FULL_REFUND;
    }

    @Override
    PaymentStatus getPaymentStatusForMessage() {
        return PaymentStatus.PENDING;
    }

    @Override
    PaymentStatus getPaymentStatusForConversation() {
        return PaymentStatus.REFUND_PENDING;
    }

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_FULL_REFUND_PENDING;
    }
}
