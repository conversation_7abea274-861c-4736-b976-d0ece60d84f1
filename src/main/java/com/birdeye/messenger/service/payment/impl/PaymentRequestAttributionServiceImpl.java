package com.birdeye.messenger.service.payment.impl;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import jakarta.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.FirebaseDto;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.elastic.BulkUpsertPayload;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentAttributionRequest;
import com.birdeye.messenger.dto.payment.PaymentRequestAttributionEvent;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.exception.RedisLockException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.KontactoRequest;
import com.birdeye.messenger.external.service.MessengerMessageService;
import com.birdeye.messenger.service.AddNoteService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.ConversationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.service.payment.PaymentRequestAttributionService;
import com.birdeye.messenger.util.JSONUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PaymentRequestAttributionServiceImpl implements PaymentRequestAttributionService{

	@Autowired
	BusinessService businessService;

	@Autowired
	ContactService contactService;

	@Autowired
	RedisLockService redisLockService;

	@Autowired
	MessengerContactService messengerContactService;

	@Autowired
	MessengerMessageService messengerMessageService;

	@Autowired
	MessageService messageService;

	@Autowired
	KafkaService kafkaService;

	@Autowired
	ElasticSearchExternalService elasticSearchService;

	@Autowired
	ConversationService conversationService;

	@Autowired
	ConversationActivityService conversationActivityService;

	@Autowired
	FirebaseService firebaseService;

	@Autowired
	AddNoteService noteService;

	@Override
	@Transactional
	public Integer linkPayment(PaymentAttributionRequest paymentAttributionRequest, Integer userId, Integer accountId) {

		BusinessDTO businessDTO = businessService.getBusinessLiteDTO(paymentAttributionRequest.getBusinessId());
		Optional<Lock> lockOpt = Optional.empty();

		if (Objects.isNull(businessDTO)){
			log.error("No business found for id:{}",paymentAttributionRequest.getBusinessId());
			throw new NotFoundException(ErrorCode.BUSINESS_NOT_FOUND);
		}

		if (Objects.isNull(paymentAttributionRequest.getMcId())) {
			log.error("mcId cannot be null, request:{}", paymentAttributionRequest);
			throw new BadRequestException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
		}
		CustomerDTO customerDTO = null;
		if(Objects.isNull(paymentAttributionRequest.getCustomerId())){
			customerDTO = createCustomer(paymentAttributionRequest, businessDTO, userId, accountId);
		} else {
			customerDTO = contactService.findById(paymentAttributionRequest.getCustomerId());
		}

		if (Objects.isNull(customerDTO)){
			log.error("Customer not present for mcId : {}", paymentAttributionRequest.getMcId());
			throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND);
		}
		String lockKeyCustomer = Constants.CUSTOMER_ID_PREFIX + customerDTO.getId();
		lockOpt = redisLockService.tryLock(lockKeyCustomer);
		if (lockOpt.isPresent()) {
			try {
				MessengerContact fromMessengerContact = messengerContactService.findById(paymentAttributionRequest.getMcId());

				if(Objects.isNull(fromMessengerContact)) {
					// fromMessengerContact = Create conversation or throw error
					log.error("No MessengerContact found - {}", paymentAttributionRequest.getMcId());
					throw new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST);
				}
				if(fromMessengerContact.getCustomerId() != null) {
					log.error("Customer already present for MessengerContact to be merged. mcId: {}, cId: {}", paymentAttributionRequest.getMcId(), fromMessengerContact.getCustomerId());
					throw new BadRequestException(ErrorCode.CUSTOMER_ALREADY_PRESENT_MESSENGER_CONTACT);
				}

				List<MessengerContact> toMessengerContacts = messengerContactService.findByCustomerIdAndBusinessId(customerDTO.getId(), businessDTO.getBusinessId());
				
				if(CollectionUtils.isEmpty(toMessengerContacts)) { 
					// Updated Conversation
					log.info("Payment attribution - updateConversationWithCustomerDetails - fromMessengerContact: {}, accountId: {}, userId: {}", fromMessengerContact, accountId, userId);
					
					/* If unattributed contact on loc1 and attributed contact on loc2, then unattributed contact will be a new contact on loc1
					 * i.e the contact will be same on enterprise level but diff on loc(business) level */
					if(!customerDTO.getBusinessId().equals(paymentAttributionRequest.getBusinessId())) {
						paymentAttributionRequest.setCustomerName(
								Objects.nonNull(customerDTO.getName()) ? customerDTO.getName(): customerDTO.getDisplayName());
						paymentAttributionRequest.setCustomerEmail(customerDTO.getEmailId());
						paymentAttributionRequest.setCustomerPhone(customerDTO.getPhone());
						paymentAttributionRequest.setCustomerPhone(customerDTO.getCountryCode());
						customerDTO = createCustomer(paymentAttributionRequest, businessDTO, userId, accountId);
					}
					
					return updateConversationWithCustomerDetails(customerDTO, businessDTO, fromMessengerContact, accountId, userId);
				} else {
					// Check from already attributed payment request
					if(fromMessengerContact.getId().equals(toMessengerContacts.get(0).getId())) {
                        log.info("Link Payment:already attributed for request {}", paymentAttributionRequest);
                        return fromMessengerContact.getId();
                    }
					// Merge conversation
					log.info("Payment attribution - mergeConversations - fromMessengerContact: {}, toMessengerContact: {}, accountId: {}, userId: {}", fromMessengerContact, toMessengerContacts, accountId, userId);
					mergeConversations(fromMessengerContact, toMessengerContacts.get(0), accountId, userId, customerDTO);
					return toMessengerContacts.get(0).getId();
				}

			} finally {
				if (lockOpt.isPresent()){
					redisLockService.unlock(lockOpt.get());
				}
			}
		} else {
			//Reject such events
			log.info("[Payment Link Event] Unable to acquire lock key:{}",lockKeyCustomer);
			throw new RedisLockException(ErrorCode.PAYMENT_LINK_LOCK);
		}
	}


	private CustomerDTO createCustomer(PaymentAttributionRequest paymentAttributionRequest, BusinessDTO businessDTO, Integer userId, Integer accountId){
		KontactoRequest kontactoRequest = new KontactoRequest();
		kontactoRequest.setBusinessId(businessDTO.getBusinessId());
		kontactoRequest.setEmailId(paymentAttributionRequest.getCustomerEmail());
		kontactoRequest.setName(paymentAttributionRequest.getCustomerName());
		kontactoRequest.setPhone(paymentAttributionRequest.getCustomerPhone());
		kontactoRequest.setSource(KontactoRequest.DASHBOARD);
		kontactoRequest.setRoutingId(accountId);
		KontactoRequest.LocationInfo locationInfo = new KontactoRequest.LocationInfo();
		locationInfo.setCountryCode(paymentAttributionRequest.getCountryCode());
		kontactoRequest.setLocation(locationInfo);
		return contactService.getorCreateNewCustomer(kontactoRequest,accountId,userId);

	}


	private Integer updateConversationWithCustomerDetails(CustomerDTO customerDTO, BusinessDTO businessDTO, MessengerContact messengerContact, Integer accountId, Integer userId){
		updateMessengerContact(messengerContact,customerDTO);
		Optional<ContactDocument> contactDocumentOptional = messengerContactService.getContactDocument(accountId,messengerContact.getId());
		if (contactDocumentOptional.isPresent()){
			ContactDocument cd = contactDocumentOptional.get();
			if (updateContactDocument(cd, businessDTO, customerDTO,accountId)){
				publishPaymentRequestAttributionEvent(contactDocumentOptional, customerDTO, userId, messengerContact.getId(), new Date().getTime());
				return messengerContact.getId();
			}
		}
		return null;
	}

	private void mergeConversations(MessengerContact fromMessengerContact, MessengerContact toMessengerContact,Integer accountId,Integer userId, CustomerDTO customerDTO){
		updateMessengerMessages(fromMessengerContact,toMessengerContact);
		//Merge with the existing conversation available with customer provided
		//We will be transferring notes to the attributed conversation.
		Optional<ContactDocument> fromContactDocument = messengerContactService.getContactDocument(accountId,fromMessengerContact.getId());
		Optional<ContactDocument> toContactDocument = messengerContactService.getContactDocument(accountId,toMessengerContact.getId());
		Optional<ContactDocument> contactDocumentOptional = mergeContactDocument(fromContactDocument,toContactDocument,toMessengerContact);
		
		updateMessengerContact(toMessengerContact);
		if (contactDocumentOptional.isPresent()){
			messengerContactService.updateContactDocumentOnES(contactDocumentOptional.get(),
					contactDocumentOptional.get().getId().toString(),contactDocumentOptional.get().getE_id());
		}
		//update Message document with the new McId
		updateMessageDocument(fromMessengerContact.getId(),toMessengerContact.getId(),accountId);
		//After successful merging of the unattributed conversation to the attributed one.
		//Delete the unattributed conversation.
		deleteUnattributedConversation(fromMessengerContact,accountId,fromMessengerContact.getBusinessId());
		
		publishPaymentRequestAttributionEvent(contactDocumentOptional, customerDTO, userId, toMessengerContact.getId(),new Date().getTime());
	}

	private void updateMessengerContact(MessengerContact mc, CustomerDTO customerDTO){
		mc.setViewedBy(null);
		mc.setTag(MessengerTagEnum.UNREAD.getId());
		mc.setCustomerId(customerDTO.getId());
		mc.setIsRead(false);
		mc.setLeadSource(customerDTO.getLeadSource());
		mc.setContactState(customerDTO.getContactState());
		mc.setUpdatedAt(new Date());
		mc.setLead(customerDTO.isLead());
		log.debug("Updated messenger contact with customer details - {}", mc);		
		messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(mc);
	}

	private void updateMessengerContact(MessengerContact messengerContact){
		messengerContact.setUpdatedAt(new Date());
		messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(messengerContact);
	}


	private boolean updateContactDocument(ContactDocument cd, BusinessDTO businessDTO, CustomerDTO customerDTO, Integer accountId){
		ContactDocument.Builder builder = new ContactDocument.Builder(cd);
		builder.addCustomerInfo(customerDTO);
		cd = builder.build();
		cd.setViewedBy(new ArrayList<>());
		cd.setC_tag(MessengerTagEnum.UNREAD.getId());
		cd.setB_name(businessDTO.getBusinessName());
		cd.setB_num(businessDTO.getBusinessNumber());
		//this is required as conversation is now attributed.
//		cd.setL_rvr_name("");
		DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
		cd.setUpdatedAt(df.format(new Date()));
		return messengerContactService.upsertContactDocumentOnES(cd,cd.getM_c_id().toString(),accountId);
	}

	private Optional<ContactDocument> mergeContactDocument(Optional<ContactDocument> fromContactDocumentOptional, Optional<ContactDocument> toContactDocumentOptional, MessengerContact toMessengerContact){

		if (fromContactDocumentOptional.isPresent() && toContactDocumentOptional.isPresent()){
			ContactDocument fromContactDocument = fromContactDocumentOptional.get();
			ContactDocument toContactDocument = toContactDocumentOptional.get();
			DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
			toContactDocument.setUpdatedAt(df.format(new Date()));

			if (CollectionUtils.isEmpty(toContactDocument.getPayments())){
				toContactDocument.setPayments(fromContactDocument.getPayments());
				toContactDocument.setL_payment_on(fromContactDocument.getL_payment_on());
			}else {
				List<ContactDocument.Payment> payments = toContactDocument.getPayments();
				payments.addAll(fromContactDocument.getPayments());
				if(fromContactDocument.getL_payment_on()> toContactDocument.getL_payment_on()) {
					toContactDocument.setL_payment_on(fromContactDocument.getL_payment_on());
				}
			}
			if (toContactDocument.getC_tag().equals(MessengerTagEnum.CAMPAIGN.getId())){
				toContactDocument.setC_tag(MessengerTagEnum.UNREAD.getId());
				toMessengerContact.setTag(MessengerTagEnum.UNREAD.getId());
				toContactDocument.setHide(false);
			}
			//this is required as unattributed conversation could have internal notes.
			// so there might be possibility that internal note in unattributed conversation is latest.
			if ((Objects.nonNull(fromContactDocument.getL_msg_on()) && Objects.nonNull(toContactDocument.getL_msg_on())
					&&  fromContactDocument.get_l_msg_on_epoch() > toContactDocument.get_l_msg_on_epoch())
					||
					(Objects.nonNull(fromContactDocument.getL_msg_on()) && Objects.isNull(toContactDocument.getL_msg_on())) ){
				updateLastMessage(fromContactDocument,toContactDocument,toMessengerContact);
			}
			log.info("Updated contact document with latest payment request - {}", toContactDocument);
			return Optional.of(toContactDocument);
		}
		return Optional.empty();
	}

	private void updateLastMessage(ContactDocument fromContactDocument,ContactDocument toContactDocument,MessengerContact toMessengerContact){
		toContactDocument.setL_msg_on(fromContactDocument.getL_msg_on());
		toContactDocument.setL_msg(fromContactDocument.getL_msg());
		toContactDocument.setLastMessageMetaData(fromContactDocument.getLastMessageMetaData());
		toContactDocument.setLastMessageType(fromContactDocument.getLastMessageType());
		toContactDocument.setLastMsgSource(fromContactDocument.getLastMsgSource());
		toContactDocument.setLastMessageUserId(fromContactDocument.getLastMessageUserId());
		toContactDocument.setLastMessageUserName(fromContactDocument.getLastMessageUserName());
		toMessengerContact.setLastMessage(fromContactDocument.getL_msg());
		toMessengerContact.setLastMsgOn(new Date(fromContactDocument.get_l_msg_on_epoch()));
		toMessengerContact.setLastMessageMetaData(JSONUtils.toJSON(fromContactDocument.getLastMessageMetaData()));
	}

	private void updateMessengerMessages(MessengerContact from,MessengerContact to){
		messengerMessageService.updateMessengerMessageMcId(from.getId(),to.getId());
	}

	private void updateMessageDocument(Integer fromMcId,Integer toMcId,Integer accountId){
		List<MessageDocument> messageDocuments = getMessageDocuments(fromMcId,accountId);
		if (CollectionUtils.isNotEmpty(messageDocuments)){
			messageDocuments.stream().forEach(messageDocument -> messageDocument.setC_id(toMcId.toString()));
			BulkUpsertPayload<MessageDocument> bulkUpsertActivities = new BulkUpsertPayload<>(messageDocuments,
					accountId, accountId, Constants.Elastic.MESSAGE_INDEX);
			try {
				elasticSearchService.bulkUpsert(bulkUpsertActivities);
			} catch (Exception e) {
				log.error("Exception while updating bulk messages", e);
				throw new MessengerException(ErrorCode.ERROR_WHILE_BULK_UPDATING_MESSAGES);
			}
		}
	}

	private List<MessageDocument> getMessageDocuments(Integer mcId,Integer accountId){
		log.info("Get message doc from ES mcId:{} , accountId:{}",mcId,accountId);
		MessangerBaseFilter messageDocFilter = new MessangerBaseFilter();
		messageDocFilter.setConversationId(mcId);
		messageDocFilter.setAccountId(accountId);
		messageDocFilter.setCount(10000);
		messageDocFilter.setQueryFile(Constants.Elastic.GET_MESSAGES_V2);
		return messengerContactService.getMessagesFromES(messageDocFilter);
	}

	private void deleteUnattributedConversation(MessengerContact messengerContact,Integer accountId,Integer businessId){
		Map<String, List<Integer>> result=null;
		boolean conversationDeleted =
				conversationService.deleteConversation(messengerContact.getId(), accountId,RefreshPolicy.IMMEDIATE);
		if (conversationDeleted) {
			//2. Delete From MessengerContact from DB
			messengerContactService.deleteMessengerContact(messengerContact.getId());
			//3. Remove data from MessageIndex
			messageService.deleteMessagesByMcId(messengerContact.getId(), accountId);
			//4. Remove from messenger_message
			List<com.birdeye.messenger.dao.entity.MessengerMessage> messengerMessages =
					messengerMessageService.deleteByMCId(messengerContact.getId());
			if (CollectionUtils.isNotEmpty(messengerMessages)) {
				result = messengerMessages.stream()
						.collect(Collectors.groupingBy(com.birdeye.messenger.dao.entity.MessengerMessage::getMessageType,
								Collectors.mapping(com.birdeye.messenger.dao.entity.MessengerMessage::getMessageId, Collectors.toList())));
			}
			//5. DELETE Messenger Notes
			noteService.deleteConversationNotes(result,null);
			//6. DELETE Messenger Activities
			conversationActivityService.deleteConversationActivities(result,null);
			//firebaseService.mirrorOnWeb(accountId,businessId);
			FirebaseDto firebaseDto = new FirebaseDto();
			firebaseDto.setAccountId(accountId);
			firebaseDto.setBusinessId(businessId);
			firebaseDto.setMcId(messengerContact.getId());
			firebaseService.mirrorOnWeb(firebaseDto);
			log.info("Deleted anonymous payment request post merging");
		}
	}
	
	private void publishPaymentRequestAttributionEvent(Optional<ContactDocument> contactDocumentOptional, CustomerDTO customerDTO, Integer userId, Integer mcId, Long updatedAt) {
		
		if (contactDocumentOptional.isPresent()){
			ContactDocument contactDocument = contactDocumentOptional.get();
			
			if( CollectionUtils.isEmpty(contactDocument.getPayments())) {
				log.info("publishPaymentRequestAttributionEvent: contactDocument doesn't have payments - {}", contactDocument);
			}
			
			PaymentRequestAttributionEvent event = PaymentRequestAttributionEvent.builder()
					.customerId(customerDTO.getId())
					.customerEmail(customerDTO.getEmailId())
					.customerName(customerDTO.getDisplayName())
					.customerPhone(customerDTO.getPhone())
					.mcId(mcId)
					.userId(userId)
					.eventType(Constants.LINK)
					.paymentIds(contactDocument.getPayments().stream().map(r -> r.getPaymentId()).collect(Collectors.toList()))
					.updatedAt(new Date().getTime()).build();
			kafkaService.publishToKafkaAsync(KafkaTopicEnum.PAYMENT_ATTRIBUTION_EVENT, event);
			
		} else {
			 log.info("publishPaymentRequestAttributionEvent: contactDocument is null");
		}
	}
}
