package com.birdeye.messenger.service.payment.impl.request;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.es.sro.ESUpdateByQueryRequest;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.payment.PaymentEventHandler;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentLinkExpiredEventHandler implements PaymentEventHandler {

    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final ElasticSearchExternalService elasticSearchService;
    private final FirebaseService firebaseService;
    private final UserService userService;

    @Override
    public void handle(PaymentEvent paymentEvent) {

        MessengerContact mc = messengerContactService.findById(paymentEvent.getConversationId());
        BusinessDTO businessDTO = businessService.getBusinessDTO(mc.getBusinessId());

        MessangerBaseFilter filter = new MessangerBaseFilter();
        filter.setAccountId(businessDTO.getAccountId());
        filter.setConversationId(mc.getId());
        filter.setCount(1);

        List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
        ContactDocument contactDocument = contactDocuments.get(0);
        List<ContactDocument.Payment> payments = contactDocument.getPayments();

        for(ContactDocument.Payment payment : payments) {
            if(payment.getPaymentId().equals(paymentEvent.getId())) {
                payment.setStatus(PaymentStatus.EXPIRED);
                payment.setUpdatedAt(paymentEvent.getTimestamp());
            }
        }

        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();
        for(ContactDocument.Payment payment : paymentMetaList) {
            if(payment.getPaymentId().equals(paymentEvent.getId())) {
                payment.setStatus(PaymentStatus.EXPIRED);
                payment.setUpdatedAt(paymentEvent.getTimestamp());
                if (payment.getMethod() == null || (payment.getMethod() != null && !payment.getMethod().equals("TERMINAL"))){ // in case of terminal we will not marking the conversation as unread
                    contactDocument.setC_read(false);
                    mc.setIsRead(false);
                }
            }
        }
        lastMessageMetadataPOJO.setL_payment_on(paymentEvent.getTimestamp());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));

        contactDocument.setL_payment_on(paymentEvent.getTimestamp());
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(new Date(paymentEvent.getTimestamp())));
        
        mc.setTag(MessageTag.INBOX.getCode());
        contactDocument.setC_tag(MessageTag.INBOX.getCode());



        Map<String, Object> data = new HashMap<>();
        data.put("paymentId", paymentEvent.getId());
        data.put("m_c_id", mc.getId());
        Map<String, Object> scriptData = new HashMap<>();
        Long updateTimeStamp = paymentEvent.getTimestamp();


        String inlineValue = "ctx._source.paymentInfo.status=" + "\"" + PaymentStatus.EXPIRED + "\"" + "; ctx._source.cr_time=" + "\"" + updateTimeStamp + "\""
                 + "; ctx._source.u_time=" + "\"" + updateTimeStamp + "\"" + "; ctx._source.type=" + "\"" + MessageDocument.Type.UPDATE.getType() + "\"" +
                "; ctx._source.lastUpdateDate='"+(new Date()).getTime()+"'";
        scriptData.put("inline", inlineValue);

        ESUpdateByQueryRequest.Builder builder = new ESUpdateByQueryRequest.Builder(new ESUpdateByQueryRequest())
                .index(Constants.Elastic.MESSAGE_INDEX)
                .queryTemplateFile(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID_CANCELLATION)
                .routingId(businessDTO.getRoutingId())
                .freeMarkerDataModel(data)
                .scriptParam(scriptData);


        boolean response = elasticSearchService.updateByQuery(builder.build());
        if(!response) {
            log.error("Payment Expiration Event failed to update in ES, mcId {}, Reason {}", paymentEvent.getConversationId(), response);
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }

        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        messengerContactService.saveOrUpdateMessengerContact(mc);

        ESRequest esRequest = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(businessDTO.getRoutingId())
                .addTemplateAndDataModel(Constants.Elastic.GET_MESSAGE_BY_PAYMENT_ID, data).build();
        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);

        List<MessageDocument> results = dataFromElastic.getResults();
        Optional<MessageDocument> paymentRequestDocOpt = results.stream().filter(doc -> {
            return doc.getMessageType().equals(MessageDocument.MessageType.CHAT) || doc.getMessageType().equals(MessageDocument.MessageType.ACTIVITY);
        }).findFirst();

        if(!paymentRequestDocOpt.isPresent()) {
            throw new BadRequestException("Invalid Request");
        }
        firebaseService.pushToFireBase(contactDocument, paymentRequestDocOpt.get(), false);

    }

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_LINK_EXPIRED;
    }
}
