package com.birdeye.messenger.service.payment.impl;

public enum PaymentNotificationName {
    PAYMENT_REQUEST, 
    PAYMENT_CONFIRMATION_CUSTOMER, 
    PAYMENT_CONFIRMATION_BUSINESS, 
    PAYMENT_REFUND_CUSTOMER,
    <PERSON>YMENT_INITIATED,
    PAYMENT_PROCESSING,
    PAYMENT_FAILED_CUSTOMER, 
    PAYMENT_FAILED_BUSINESS,
    UPCOMING_INVOICE_CUSTOMER,
    UPCOMING_INVOICE_BUSINESS,
    UPDATE_PAYMENT_METHOD,
    SUBSCRIPTION_CANCELLED_BUSINESS,
    SUBSCRIPTION_CANCELLED_CUSTOMER,
    SUBSCRIPTION_INVOICE_FAILED_CUSTOMER,
    SUBSCRIPTION_INVOICE_FAILED_BUSINESS
}
