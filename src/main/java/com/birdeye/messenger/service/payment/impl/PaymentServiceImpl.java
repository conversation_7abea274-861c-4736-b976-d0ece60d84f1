package com.birdeye.messenger.service.payment.impl;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.PaymentTemplate;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.payment.CardReaderPaymentIntentResponse;
import com.birdeye.messenger.dto.payment.CardReaderPaymentRequest;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkRequest;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkRequest.PaymentRequestType;
import com.birdeye.messenger.dto.payment.CreatePaymentLinkResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.Error;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.payment.PaymentService;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final RestTemplate restTemplate;

    @Value("${payment.base.url}")
    private String baseUrl;

    private HttpHeaders getHeaders() {
        HttpHeaders httpHeaders = MessengerUtil.getDefaultPostHeader();
        httpHeaders.set(RequestLoggingFilter.HEADER_ARMOR_REQUEST_ID, MDC.get(RequestLoggingFilter.HEADER_ARMOR_REQUEST_ID));
        httpHeaders.set(Constants.ACC_ID_HEADER, MDC.get(RequestLoggingFilter.ACCOUNT_ID));
        httpHeaders.set(Constants.USER_ID_HEADER, MDC.get(RequestLoggingFilter.USER_ID));
        return httpHeaders;
    }

    @Override
    public CreatePaymentLinkResponse createPaymentRequest(CreatePaymentLinkRequest createPaymentLinkRequest) {
        String url = baseUrl + "/generate-payment-link";
        if(Objects.nonNull(createPaymentLinkRequest.getPaymentRequestType()) && createPaymentLinkRequest.getPaymentRequestType().equals(PaymentRequestType.CARD_ON_FILE)) {
            url = baseUrl + "/card-on-file-payment";
        }
        HttpEntity<Object> httpEntity = new HttpEntity<>(createPaymentLinkRequest, getHeaders());
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        ObjectMapper mapper = new ObjectMapper();
        if(!response.getStatusCode().is2xxSuccessful()) {
            try {
                Error error = mapper.readValue(response.getBody(), Error.class);
                if(error.getCode().equals("1141")){
                    //Raising MessengerException so Http status will be 500
                    throw new MessengerException(ErrorCode.PAYMENT_INVALID_REQUESTED_AMOUNT,error.getMessage());
                }
                throw new BadRequestException(error.getMessage());
            }catch (IOException e) {
                log.error("payment-service REST API error request [ {} ], response [ {} ]", createPaymentLinkRequest, response);
                throw new BadRequestException("Payment Link Request Failed " + response.getBody());
            }
        }
        try{
            return mapper.readValue(response.getBody(), CreatePaymentLinkResponse.class);
        }catch (Exception e) {
            log.error("payment-service object deserialzation exception [ {} ], response [ {} ]", createPaymentLinkRequest, response);
            throw new BadRequestException("Payment Link Request Failed. " + response.getBody());
        }
    }

    @Override
    public void decorateWithPaymentLink(MessageDTO messageDTO) {
        if(messageDTO.getPaymentRequest().isResend()) {
            //TODO: refresh paymentLink - to be implemented on payment-service
        }
        else {
            CreatePaymentLinkRequest request = new CreatePaymentLinkRequest(messageDTO, CreatePaymentLinkRequest.PaymentRequestType.TEXT_TO_PAY);
            CreatePaymentLinkResponse createPaymentLinkResponse = createPaymentRequest(request);
            createPaymentLinkResponse.setLinkCreatedAt(new Date().getTime());
            SendMessageDTO dto = (SendMessageDTO) messageDTO;
            String body = dto.getBody() + "\n"+ createPaymentLinkResponse.getPaymentLink();
            dto.setPaymentRequestEmailBody(dto.getBody()); // body without the payment link
            dto.setBody(body);
            messageDTO.setCreatePaymentLinkResponse(createPaymentLinkResponse);
        }
    }

    @Override
    public CardReaderPaymentIntentResponse createCardReaderPaymentIntent(CardReaderPaymentRequest cardReaderPaymentRequest) {
        String url = baseUrl + "/terminals/create/payment-intent";
        HttpEntity<Object> httpEntity = new HttpEntity<>(cardReaderPaymentRequest, getHeaders());
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        ObjectMapper mapper = new ObjectMapper();
        if(!response.getStatusCode().is2xxSuccessful()) {
            try {
                Error error = mapper.readValue(response.getBody(), Error.class);
                throw new BadRequestException(error.getMessage());
            }catch (IOException e) {
                log.error("payment-service REST API error request [ {} ], response [ {} ]", cardReaderPaymentRequest, response);
                throw new BadRequestException("Payment Intent Request for card reader failed" + response.getBody());
            }
        }

        try{
            return mapper.readValue(response.getBody(), CardReaderPaymentIntentResponse.class);
        }catch (Exception e) {
            log.error("payment-service object deserialzation exception [ {} ], response [ {} ]", cardReaderPaymentRequest, response);
            throw new BadRequestException("Payment Intent Request for card reader failed " + response.getBody());
        }
    }

    @Override
    public PaymentTemplate getMobPaymentTemplate(Integer accountId) {
        log.info("getMobPaymentTemplate called for businessId : {}", accountId);
        ResponseEntity<PaymentTemplate> responseEntity = null;
        HttpEntity<Object> request = new HttpEntity<>(getHeaders());
        String url = baseUrl + "/template/" + accountId + "?isMobileTemplate=true";
        responseEntity = restTemplate.exchange(url, HttpMethod.GET, request, PaymentTemplate.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("Payment template fetched successfully ");
            return responseEntity.getBody();
        }
        log.error("Error fetching payment template");
        throw new BadRequestException("Error fetching payment template for account " + accountId);
    }
}
