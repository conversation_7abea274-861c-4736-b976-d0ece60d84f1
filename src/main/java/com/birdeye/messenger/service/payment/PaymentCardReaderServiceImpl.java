package com.birdeye.messenger.service.payment;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.SortedSet;
import java.util.TreeSet;

import jakarta.transaction.Transactional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.ActivityDto;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.dto.LastMessageMetaData;
import com.birdeye.messenger.dto.MessageResponse;
import com.birdeye.messenger.dto.MessangerBaseFilter;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SendResponse;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.payment.CardReaderPaymentIntentResponse;
import com.birdeye.messenger.dto.payment.CardReaderPaymentRequest;
import com.birdeye.messenger.dto.payment.PaymentInfo;
import com.birdeye.messenger.dto.payment.PaymentUiRequest;
import com.birdeye.messenger.enums.ActivityMeantFor;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessengerTagEnum;
import com.birdeye.messenger.enums.PaymentDeviceType;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.event.ConversationTagChangeEvent;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.ContactService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.FirebaseService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.util.TimeZoneUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentCardReaderServiceImpl implements PaymentCardReaderService {

    private final PaymentService paymentService;
    private final MessengerContactService messengerContactService;
    private final BusinessService businessService;
    private final UserService userService;
    private final ConversationActivityService conversationActivityService;
    private final FirebaseService firebaseService;
    private final ContactService contactService;
    private final KafkaService kafkaService;

    @Override
    @Transactional
    public MessageResponse handleCardReaderPayment(SendMessageDTO sendMessageDTO) {
        log.info("request:{}",sendMessageDTO.toString());
        if(Objects.nonNull(sendMessageDTO.getCustomerId())){
            MessengerContact messengerContact = messengerContactService.findByCustomerId(sendMessageDTO.getCustomerId());
            if(Objects.nonNull(messengerContact)){
                sendMessageDTO.setToCustomerId(messengerContact.getId().toString());
            }
        }

        if(Objects.isNull(sendMessageDTO.getToCustomerId())) {
    		return handleCardReaderPaymentWithNoContactFound(sendMessageDTO);
    	}

        log.info("handleCardReaderPayment Request:{}",sendMessageDTO.toString());
        PaymentUiRequest paymentRequest = sendMessageDTO.getPaymentRequest();
        if(Objects.isNull(paymentRequest) || !"TERMINAL".equals(paymentRequest.getMethod())) {	
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.INVALID_PAYMENT_REQUEST, 
            		ComponentCodeEnum.PAYMENT, HttpStatus.BAD_REQUEST)
            		.message("Not a valid request for card reader payment - {} ", paymentRequest));
        }

        setBasicDetailsInfo(sendMessageDTO);
        CardReaderPaymentIntentResponse cardReaderPaymentIntent = createTerminalPayment(sendMessageDTO);

        MessengerContact mc = sendMessageDTO.getMessengerContact();
        BusinessDTO businessDTO = sendMessageDTO.getBusinessDTO();
        UserDTO userDTO = userService.getUserDTO(sendMessageDTO.getUserId());
        if(Objects.isNull(mc) || Objects.isNull(businessDTO) || Objects.isNull(userDTO)) {
            throw new BadRequestException(String.format("Either %d mc or %d business or %d user not found", mc.getId(), sendMessageDTO.getFromBusinessId(), sendMessageDTO.getUserId()));
        }

        ContactDocument.Payment payment = setLastMessageMetadata(cardReaderPaymentIntent, paymentRequest, mc, userDTO);
        Date createdAt = new Date(payment.getCreatedAt());
        MessangerBaseFilter filter = new MessangerBaseFilter();
        filter.setAccountId(businessDTO.getAccountId());
        filter.setConversationId(mc.getId());
        filter.setCount(1);

        List<ContactDocument> contactDocuments = messengerContactService.getContactFromES(filter);
        ContactDocument contactDocument = contactDocuments.get(0);
        if(CollectionUtils.isEmpty(contactDocument.getPayments())) {
        	contactDocument.setPayments(new ArrayList<>());
        }
        contactDocument.getPayments().add(payment);
        contactDocument.setL_payment_on(createdAt.getTime());
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(createdAt));
        contactDocument.setHide(false);
        contactDocument.setC_tag(MessengerTagEnum.INBOX.getId());
        contactDocument.setLastMessageUserId(userDTO.getId());
        contactDocument.setLastMessageUserName(userDTO.getName());
        mc.setTag(MessengerTagEnum.INBOX.getId());

        ActivityDto cardReaderPaymentActivity = generateTerminalPaymentActivity(cardReaderPaymentIntent, paymentRequest, businessDTO, userDTO, mc.getId(), createdAt, paymentRequest.getDeviceType());

        mc.setPaymentReferenceId(cardReaderPaymentIntent.getTransactionId());
        messengerContactService.saveOrUpdateMessengerContact(mc);
        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        conversationActivityService.persistActivityInDatabase(cardReaderPaymentActivity, userDTO);
        MessageDocument messageDocument = conversationActivityService.persistActivityInES(cardReaderPaymentActivity);
        log.info("id1:{} id2:{}",contactDocument.getCr_asgn_id(),userDTO.getId());
        
        assignmentUpdate(sendMessageDTO, mc, businessDTO);

        firebaseService.pushToFireBase(contactDocument, messageDocument, false);

        SortedSet<MessageResponse.Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        String sendingTimeString = TimeZoneUtil.formatDateForTz(createdAt, Constants.DATE_FORMAT_MESSENGER_UI, businessDTO.getTimeZoneId());
        MessageResponse.Message message = new MessageResponse.Message(messageDocument, createdAt, sendingTimeString);
        allMessages.add(message);
        return new SendResponse(sendMessageDTO, allMessages, false, true);
    }

    private MessageResponse handleCardReaderPaymentWithNoContactFound(SendMessageDTO sendMessageDTO) {
    	log.info("Request for card reader payment with No contact: {}", sendMessageDTO.toString());
        PaymentUiRequest paymentRequest = sendMessageDTO.getPaymentRequest();
        if(Objects.isNull(paymentRequest) || !"TERMINAL".equals(paymentRequest.getMethod())) {
            throw new BadRequestException("Not a valid request for card reader  payment " + paymentRequest);
        }

        setBasicDetailsInfo(sendMessageDTO);
        CardReaderPaymentIntentResponse cardReaderPaymentIntent = createTerminalPayment(sendMessageDTO);

        MessengerContact mc = sendMessageDTO.getMessengerContact();
        BusinessDTO businessDTO = sendMessageDTO.getBusinessDTO();
        UserDTO userDTO = userService.getUserDTO(sendMessageDTO.getUserId());
        
        if(Objects.isNull(mc) || Objects.isNull(businessDTO) || Objects.isNull(userDTO)) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.NOT_FOUND,ComponentCodeEnum.PAYMENT, HttpStatus.BAD_REQUEST)
            		.message("Either of the following will be null| MC - {}, Business - {}, User - {}",
        					mc != null ? mc.getId(): null, sendMessageDTO.getFromBusinessId(), sendMessageDTO.getUserId()));
        }
        ContactDocument.Payment payment = setLastMessageMetadata(cardReaderPaymentIntent, paymentRequest, mc, userDTO);
        Date createdAt = new Date(payment.getCreatedAt());

        ContactDocument contactDocument = new ContactDocument();
        contactDocument.setE_id(businessDTO.getAccountId());
        contactDocument.setB_id(businessDTO.getBusinessId());
        contactDocument.setM_c_id(mc.getId());
        contactDocument.setPayments(Collections.singletonList(payment));
        contactDocument.setL_payment_on(createdAt.getTime());
        SimpleDateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
        contactDocument.setUpdatedAt(df.format(createdAt));
        contactDocument.setHide(false);
        contactDocument.setC_tag(MessengerTagEnum.INBOX.getId());
        contactDocument.setLastMessageUserId(userDTO.getId());
        contactDocument.setLastMessageUserName(userDTO.getName());
        contactDocument.setCr_date(df.format(createdAt));
        contactDocument.setCr_asgn_id(Constants.Elastic.UNASSIGNED_ID);
        contactDocument.setCr_asgn_name(Constants.Elastic.UNASSIGNED_NAME);
        contactDocument.setTeam_id(Constants.Elastic.UNASSIGNED_ID);
        contactDocument.setTeam_name(Constants.Elastic.UNASSIGNED_NAME);
        contactDocument.setAssignmentType("U");
        mc.setTag(MessengerTagEnum.INBOX.getId());

        ActivityDto cardReaderPaymentActivity = generateTerminalPaymentActivity(cardReaderPaymentIntent, paymentRequest, businessDTO, userDTO, mc.getId(), createdAt, paymentRequest.getDeviceType());
        
        mc.setPaymentReferenceId(cardReaderPaymentIntent.getTransactionId());
        messengerContactService.saveOrUpdateMessengerContactWithExistingTransaction(mc);
        messengerContactService.updateContactOnES(mc.getId(), contactDocument, businessDTO.getRoutingId());
        conversationActivityService.persistActivityInDatabase(cardReaderPaymentActivity, userDTO);
        
        MessageDocument messageDocument = conversationActivityService.persistActivityInES(cardReaderPaymentActivity);
        firebaseService.pushToFireBase(contactDocument, messageDocument, false);

        SortedSet<MessageResponse.Message> allMessages = new TreeSet<>(MessageResponse.getMessageComparator());
        String sendingTimeString = TimeZoneUtil.formatDateForTz(createdAt, Constants.DATE_FORMAT_MESSENGER_UI, businessDTO.getTimeZoneId());
        MessageResponse.Message message = new MessageResponse.Message(messageDocument, createdAt, sendingTimeString);
        allMessages.add(message);
        return new SendResponse(sendMessageDTO, allMessages, false, true);
    }

    private void setBasicDetailsInfo(SendMessageDTO sendMessageDTO){
        BusinessDTO businessDTO = businessService.getBusinessDTO(sendMessageDTO.getFromBusinessId());
        sendMessageDTO.setBusinessDTO(businessDTO);
        if(Objects.nonNull(sendMessageDTO.getToCustomerId())){
        	MessengerContact mc = Objects.nonNull(sendMessageDTO.getMessengerContact()) ? sendMessageDTO.getMessengerContact() : messengerContactService.findById(Integer.parseInt(sendMessageDTO.getToCustomerId()));
            sendMessageDTO.setMessengerContact(mc);
            CustomerDTO customerDTO = contactService.findByIdWithCustomFields(mc.getCustomerId());
            sendMessageDTO.setCustomerDTO(customerDTO);
        }else{
            MessengerContact mc = messengerContactService.createMessengerConversation(sendMessageDTO.getFromBusinessId());
            sendMessageDTO.setMessengerContact(mc);
        }
    }

    private CardReaderPaymentIntentResponse createTerminalPayment(SendMessageDTO sendMessageDTO){
        CardReaderPaymentRequest cardReaderPaymentRequest = new CardReaderPaymentRequest(sendMessageDTO);
        return paymentService.createCardReaderPaymentIntent(cardReaderPaymentRequest);
    }

    private ContactDocument.Payment setLastMessageMetadata(CardReaderPaymentIntentResponse cardReaderPaymentIntent, PaymentUiRequest paymentRequest, MessengerContact mc, UserDTO userDTO){
        LastMessageMetaData lastMessageMetadataPOJO = MessengerUtil.getLastMessageMetadataPOJO(mc);
        List<ContactDocument.Payment> paymentMetaList = CollectionUtils.isNotEmpty(lastMessageMetadataPOJO.getPayment()) ? lastMessageMetadataPOJO.getPayment() : new ArrayList<>();

        ContactDocument.Payment payment = new ContactDocument.Payment();
        payment.setPaymentId(cardReaderPaymentIntent.getPaymentRequestId());
        payment.setStatus(PaymentStatus.NOT_PAID);
        Date createdAt = new Date();
        payment.setCreatedAt(createdAt.getTime());
        payment.setUpdatedAt(createdAt.getTime());
        payment.setAmount(paymentRequest.getItems().get(0).getAmount());
        payment.setMethod(paymentRequest.getMethod());
        payment.setCurrency(cardReaderPaymentIntent.getCurrency());
        paymentMetaList.add(payment);

        lastMessageMetadataPOJO.setL_payment_on(createdAt.getTime()); // Doubtful here
        lastMessageMetadataPOJO.setPayment(paymentMetaList);
        lastMessageMetadataPOJO.setLastMessageUserId(userDTO.getId());
        lastMessageMetadataPOJO.setLastMessageUserName(userDTO.getName());
        mc.setLastMessageMetaData(JSONUtils.toJSON(lastMessageMetadataPOJO));
        return payment;
    }

    private ActivityDto generateTerminalPaymentActivity(CardReaderPaymentIntentResponse paymentIntent, PaymentUiRequest paymentRequest, BusinessDTO businessDTO, UserDTO userDTO, Integer mcId, Date paymentCreatedAt, PaymentDeviceType paymentDeviceType){
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setId(paymentIntent.getPaymentRequestId());
        paymentInfo.setTransactionId(paymentIntent.getTransactionId());
        paymentInfo.setAmount(paymentRequest.getItems().get(0).getAmount());
        paymentInfo.setStatus(PaymentStatus.NOT_PAID);
        paymentInfo.setReqCreatedById(userDTO.getId());
        paymentInfo.setReqCreatedByName(userDTO.getName());
        paymentInfo.setReqCreatedAt(paymentCreatedAt.getTime());
        paymentInfo.setMethod(paymentRequest.getMethod());
        paymentInfo.setInvoiceNumber(paymentRequest.getInvoiceNumber());
        paymentInfo.setCardReaderId(paymentIntent.getCardReaderId());
        paymentInfo.setCardReaderLabel(paymentIntent.getCardReaderLabel());
        paymentInfo.setClientSecret(paymentIntent.getClientSecret());
        paymentInfo.setItemDesc(paymentRequest.getItems().get(0).getItemDesc());
        paymentInfo.setDeviceType(paymentDeviceType);
        paymentInfo.setReceiptUrl(paymentIntent.getReceiptURL());
        paymentInfo.setCurrency(paymentIntent.getCurrency());
        
        ActivityDto cardReaderPaymentActivity = ActivityDto.builder().mcId(mcId).created(paymentCreatedAt).updated(paymentCreatedAt).actorId(userDTO.getId()).actorName(userDTO.getName())
                .activityType(ActivityType.TERMINAL_PAYMENT_REQUEST)
                .activityMeantFor(ActivityMeantFor.CUSTOMER)
                .paymentInfo(paymentInfo)
                .type(MessageDocument.Type.CREATE)
                .accountId(businessDTO.getAccountId()).businessId(businessDTO.getBusinessId())
                .build();

        return cardReaderPaymentActivity;
    }
    
    private void assignmentUpdate(SendMessageDTO sendMessageDTO, MessengerContact mc, BusinessDTO businessDTO) {
		/* Assigning conversation to user */
        try {
			String currentAssignmentType = mc.getAssignmentType() == null ? Constants.Assignment.USER_ASSIGNED : mc.getAssignmentType().name();
			IdentityDTO assignee = new IdentityDTO(mc.getCurrentAssignee(), mc.getCurrentAssigneeName(), currentAssignmentType, mc.getCurrentAssigneeEmailId());

			ConversationTagChangeEvent conversationChangeTagEvent = new ConversationTagChangeEvent(businessDTO.getAccountId(),
					mc.getBusinessId(), mc.getTag(), MessengerTagEnum.INBOX.getId(), sendMessageDTO.getUserId(), mc.getId(), assignee, true);

			kafkaService.publishToKafkaAsync(KafkaTopicEnum.TAG_UPDATE, mc.getId(), conversationChangeTagEvent);
		} catch (Exception e) {
			log.error("Some error occured while pushing tag update event from paymentCardReaderServiceImpl : {}", e);
		}
	}
}