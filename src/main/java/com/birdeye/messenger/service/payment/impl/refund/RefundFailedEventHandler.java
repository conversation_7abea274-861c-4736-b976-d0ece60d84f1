package com.birdeye.messenger.service.payment.impl.refund;

import com.birdeye.messenger.dto.payment.PaymentEvent;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.PaymentStatus;
import org.springframework.stereotype.Service;

@Service
public class RefundFailedEventHandler extends AbstractRefundHandler {

    @Override
    ActivityType getActivityType() {
        return null;
    }

    @Override
    PaymentStatus getPaymentStatusForMessage() {
        return PaymentStatus.FAILED;
    }

    @Override
    PaymentStatus getPaymentStatusForConversation() {
        return PaymentStatus.REFUND_FAILED;
    }

    @Override
    public PaymentEvent.PaymentEventName getHandlerName() {
        return PaymentEvent.PaymentEventName.PAYMENT_REFUND_FAILED;
    }
}
