package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.UnlinkReviewResponse;
import com.birdeye.messenger.sro.DeleteReviewEvent;
import com.birdeye.messenger.sro.ReviewAttributionRequest;
import com.birdeye.messenger.sro.ReviewEvent;

public interface ReviewEventHandlerService {

    void handleUpsertEvent(ReviewEvent reviewEvent) throws Exception;

	void handleDeleteEvent(DeleteReviewEvent deleteReviewEvent) throws Exception;

	UnlinkReviewResponse unlinkReview(Integer userId, Integer accountId, Integer reviewId, Integer conversationId, boolean isCustomerDelete, boolean publishEvent);

    Integer linkReview(Integer userId, Integer accountId, ReviewAttributionRequest reviewAttributionRequest,boolean publishLinkEventToReviewService);

    UnlinkReviewResponse unlinkReview(Integer userId, Integer accountId, Integer reviewId, Integer customerId);
}
