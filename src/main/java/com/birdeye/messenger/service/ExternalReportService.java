package com.birdeye.messenger.service;

import java.util.Map;

import com.birdeye.messenger.dto.*;

/**
 * <AUTHOR>
 *
 */
public interface ExternalReportService {

	void getInboxSendReportByLocations(MessengerFilter filters);

	void getSendByChannelReport(MessengerFilter filters);

	void getSendAutoreplyReport(MessengerFilter filters);

	MatrixRoiReport getMatrixRoiReport(MessengerFilter filters);

	BizAppGetAllMessagesResponse getAllMessages(ExportInboxRequest filters);

	BizAppGetAllContactsResponse getAllContacts(ExportInboxRequest filters);

	Map<String, Object> getRoiReportForInbox(ExternalReportFilter filters);

	Map<String, Object> getRoiReportForWebchat(ExternalReportFilter filters);
	
	SmsCommunicationResponse getSmsMessagesCount(SmsCommunicationRequest request);

	Object getReserveWGoogleReport(ExternalReportFilter filter);

}
