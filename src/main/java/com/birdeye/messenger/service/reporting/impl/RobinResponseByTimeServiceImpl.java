package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesByTimeResponse;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesDataPoint;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.RobinResponseByTime;
import com.birdeye.messenger.service.reporting.dto.RobinResponseDataPoint;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class RobinResponseByTimeServiceImpl implements ReportService {


    private final ElasticSearchExternalService elasticSearchService;

    @Override
    public RobinResponseByTime getReport(ReportFilter filter) {
        return buildESRequestForRobinResponseMessages(filter);
    }

    @Override
    public Enum getReportType() {
        return ReportType.ROBIN_RESPONSE_BY_TIME;
    }

    private RobinResponseByTime buildESRequestForRobinResponseMessages(ReportFilter filter) {

        List<Integer> businessIds = filter.getBusinessIds();
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }


        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_ROBIN_RESPONSE_BY_TIME, data)
                .addSize(0)
                .build();

        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);

        if (searchResponse.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch robin response messages report");
        } else {
            RobinResponseByTime response = new RobinResponseByTime();
            response.setTotalCount(searchResponse.getHits().getTotalHits().value);
            response.setGroupByType(filter.getInterval());

            Aggregations aggregations = searchResponse.getAggregations();
            ParsedDateHistogram receive_message = aggregations.get("robin_response");

            if (Objects.nonNull(receive_message)) {
            	List<? extends Histogram.Bucket> buckets = receive_message.getBuckets();
                List<RobinResponseDataPoint> dataPoints = buckets.stream().map(bucket -> {
                    String dateBucket = bucket.getKeyAsString();
                    String[] parts = dateBucket.split("(?=T)");
                    dateBucket = parts[0];
                    LocalDate dateTime = LocalDate.parse(dateBucket);

                    DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    String label = dateTime.format(formatter);
                    int count = (int) bucket.getDocCount();
                    RobinResponseDataPoint dataPoint = new RobinResponseDataPoint();
                    dataPoint.setRobinResponseCount(count);
                    dataPoint.setLabel(label);
                    dataPoint.setShortLabel(label);
                    return dataPoint;
                }).collect(Collectors.toList());
                response.setDataPoints(dataPoints);
            }

            Range last_1_month = aggregations.get("last_1_month");
            if (Objects.nonNull(last_1_month)) {
            	List<? extends Range.Bucket> buckets = last_1_month.getBuckets();
                buckets.stream().findAny().ifPresent(bucket -> response.setThisMonth((int) bucket.getDocCount()));
            }

            if (Objects.nonNull(searchResponse.getHits())) {
                response.setLastOneYear(searchResponse.getHits().getTotalHits().value);
            }
            response.setDataPoints(response.getDataPoints());
            return response;
        }
    }
    @Override
    public Object  getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff =  filters.getEndDateInMillis() - filters.getStartDateInMillis();
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        RobinResponseByTime robinResponseByTime =  getReport(filters);
        List<RobinResponseDataPoint> robinResponseByTimeDataPoints = robinResponseByTime.getDataPoints();
        DateUtils.customizeLabels(robinResponseByTimeDataPoints, startDate, endDate, groupByType, false);
        robinResponseByTime.setDateDiff(days);
        return  robinResponseByTime;
    }
}
