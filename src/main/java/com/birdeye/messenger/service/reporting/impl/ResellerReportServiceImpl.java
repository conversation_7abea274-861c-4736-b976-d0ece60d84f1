package com.birdeye.messenger.service.reporting.impl;


import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import com.birdeye.messenger.dao.entity.BusinessChatEnabledLocation;
import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.repository.BusinessChatEnabledLocationRepository;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.service.ReceptionistService;
import com.birdeye.messenger.util.MessengerUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.Booleans;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.range.DateRangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.range.ParsedDateRange;
import org.elasticsearch.search.aggregations.bucket.range.ParsedDateRange.ParsedBucket;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Percentiles;
import org.elasticsearch.search.aggregations.metrics.PercentilesAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.BusinessReceptionistConfiguration;
import com.birdeye.messenger.dao.repository.BusinessChatWidgetConfigRepository;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.BusinessReceptionistConfigurationService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.reporting.ResellerReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.birdeye.messenger.constant.Constants.MStoMin;


@Service
@Slf4j
@RequiredArgsConstructor
public class ResellerReportServiceImpl implements ResellerReportService {
	
	private final BusinessChatWidgetConfigRepository businessChatWidgetConfigRepository;
	private final BusinessReceptionistConfigurationService receptionistConfigurationService;
	private final ElasticSearchExternalService elasticSearchExternalService;
	private final KafkaService kafkaService;
	private final BusinessService businessService;
	private final ThreadPoolTaskExecutor poolExecutor;
	private final ReceptionistService receptionistService;
	private final BusinessChatEnabledLocationRepository businessChatEnabledLocationRepository;
	@Override
	public void publishResellerSetUpReport(ResellerSetupReportRequest request) {
		ResellerSetupReportResponse resellerSetupReportResponse = new ResellerSetupReportResponse();
		resellerSetupReportResponse.setAccountId(request.getAccountId());
		List<BusinessChatBotDataDto> businessChatbotData = null;
		List<BusinessChatBotDataDto> businessChatbotDataWithDefaultWidget = null;
		if ("Business".equals(request.getType())) {
			businessChatbotData = businessChatWidgetConfigRepository.findByBusinessNumberAndInstalledAndEnabled(request.getAccountNumber());
			if(CollectionUtils.isNotEmpty(businessChatbotData)){
				resellerSetupReportResponse.setIsWebchatInstalled("Yes");
				if(businessChatbotData.get(0).getChatbotEnabled() == 1){
					resellerSetupReportResponse.setIsChatbotEnabled("Yes");
				}
			}
		} else {
			businessChatbotDataWithDefaultWidget = businessChatWidgetConfigRepository.findByBusinessNumberAndInstalledAndEnabled(request.getAccountNumber());
			List<BusinessChatEnabledLocation> businessChatbotDataWithoutDefaultWidget = businessChatEnabledLocationRepository.findAllCustomWidget(request.getAccountNumber());
			log.info("chatbot data with default widget: {}, without: {}",businessChatbotDataWithDefaultWidget.size(),businessChatbotDataWithoutDefaultWidget.size());
			if(Objects.nonNull(businessChatbotDataWithoutDefaultWidget)){
				log.info("checking widget for active location: {}",businessChatbotDataWithoutDefaultWidget);
				for(Long businessNumber: request.getBusinessNumbers()){
					for(BusinessChatEnabledLocation businessChatBotDataDto: businessChatbotDataWithoutDefaultWidget){
						if(businessChatBotDataDto.getBusinessNumber().equals(businessNumber)){
							resellerSetupReportResponse.setIsWebchatInstalled("Yes");
							if(businessChatbotDataWithDefaultWidget.stream().filter(data->data.getId().equals(businessChatBotDataDto.getBusinessChatWidgetId())).collect(Collectors.toList()).get(0).getChatbotEnabled()==1){
								resellerSetupReportResponse.setIsChatbotEnabled("Yes");
								break;
							}
						}
					}
				}
				if(Objects.isNull(resellerSetupReportResponse.getIsWebchatInstalled())||Objects.isNull(resellerSetupReportResponse.getIsChatbotEnabled())){
					log.info("checking for default widget: {}", businessChatbotDataWithDefaultWidget);
					List<Integer> locationWidgetIds = businessChatbotDataWithoutDefaultWidget.stream().map(data->data.getBusinessChatWidgetId()).collect(Collectors.toList());
					if(CollectionUtils.isNotEmpty(businessChatbotDataWithDefaultWidget) ){
						List<BusinessChatBotDataDto> widgetIdsDefault=businessChatbotDataWithDefaultWidget.stream().filter(
								dto->!locationWidgetIds.contains(dto.getId())
						).collect(Collectors.toList());

						if(CollectionUtils.isNotEmpty(widgetIdsDefault)){
							resellerSetupReportResponse.setIsWebchatInstalled("Yes");
							if(widgetIdsDefault.get(0).getChatbotEnabled().equals(1)){
								resellerSetupReportResponse.setIsChatbotEnabled("Yes");
							}
						}
					}
				}
			}
		}
		if(Objects.isNull(resellerSetupReportResponse.getIsWebchatInstalled())){
			resellerSetupReportResponse.setIsWebchatInstalled("No");
		}
		if(Objects.isNull(resellerSetupReportResponse.getIsChatbotEnabled())){
			resellerSetupReportResponse.setIsChatbotEnabled("No");
		}
		BusinessDTO business = new BusinessDTO();
		business.setEnterpriseId(request.getAccountId());
		boolean receptionistEnabled = receptionistService.isReceptionistEnabledCheck(business);
		resellerSetupReportResponse.setIsReceptionistEnabled("No");
		if(Boolean.TRUE.equals(receptionistEnabled)){
			BusinessReceptionistConfiguration config=receptionistConfigurationService.getReceptionistConfiguration(request.getAccountNumber());
			if(Objects.nonNull(config)&&config.getForwardingSetupStatus()!=null
					&&config.getForwardingSetupStatus()==1){
				resellerSetupReportResponse.setIsReceptionistEnabled("Yes");
			}else{

				ESFindByFieldRequest<MessageDocument> esFindRequest=new ESFindByFieldRequest.Builder<MessageDocument>()
						.setIndex(Constants.Elastic.MESSAGE_INDEX).setRoutingId(request.getAccountId())
						.setDocumentType(MessageDocument.class).setMustFields("source",String.valueOf(Source.VOICE_CALL.getSourceId())).setMustFields("e_id",String.valueOf(request.getAccountId())).build();
				esFindRequest.getBoolQuery().must(QueryBuilders.rangeQuery("cr_time").from("now-30d").to("now"));
				if(CollectionUtils.isNotEmpty(request.getBusinessIds())){
					esFindRequest.getBoolQuery().must(QueryBuilders.termsQuery("b_id",request.getBusinessIds()));
				}
				esFindRequest.isValidateRequest();
				List<MessageDocument> messages=elasticSearchExternalService.findDocumentByField(esFindRequest);
				if(CollectionUtils.isNotEmpty(messages)){
					resellerSetupReportResponse.setIsReceptionistEnabled("Yes");
				}
			}
		}
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESELLER_SETUP_REPORT,request.getAccountId(), resellerSetupReportResponse);
	}

	@Override
	public void publishResellerAdoptionReport(ResellerSetupReportRequest request) {
		ResellerAdoptionReportResponse resellerAdoptionReportResponse = new ResellerAdoptionReportResponse();
		resellerAdoptionReportResponse.setAccountId(request.getAccountId());
		if (Integer.valueOf(0).equals(businessService.isMessengerEnabled(request.getAccountId()))) {
			createDefaultResponse(resellerAdoptionReportResponse);
		} else {
			List<Future> futureTasks=new ArrayList<>();
			futureTasks.add(poolExecutor.submit(() -> aggregateMessageCountsByDirection(request, resellerAdoptionReportResponse)));
			futureTasks.add(poolExecutor.submit(() -> countVoiceCalls(request, resellerAdoptionReportResponse)));
			futureTasks.add(poolExecutor.submit(() -> aggregateMessageCountByLocation(request, resellerAdoptionReportResponse)));
			futureTasks.add(poolExecutor.submit(() -> calculateMedianResponseTime(request, resellerAdoptionReportResponse)));

			// Wait for all threads to complete
			for (Future<?> future : futureTasks) {
				try {
					future.get();
				} catch (InterruptedException | ExecutionException e) {
					log.error("Exception occure in getting future request {}", e);
				}
			}
		}
		kafkaService.publishToKafkaAsync(KafkaTopicEnum.RESELLER_ADOPTION_REPORT, request.getAccountId(),
				resellerAdoptionReportResponse);
	}

	private void calculateMedianResponseTime(ResellerSetupReportRequest request,
											 ResellerAdoptionReportResponse resellerAdoptionReportResponse) {
		SearchRequest searchRequest = new SearchRequest(Constants.Elastic.MESSAGE_INDEX);
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
				.must(QueryBuilders.termQuery("e_id", request.getAccountId()))
				.must(QueryBuilders.termQuery("messageType", "CHAT"))
				.must(QueryBuilders.termQuery("firstResponse", true))
				.must(QueryBuilders.existsQuery("relativeResponseTime"))
				.must(QueryBuilders.rangeQuery("cr_time").from("now-30d").to("now"));
		if(CollectionUtils.isNotEmpty(request.getBusinessIds())) {
			boolQueryBuilder.must(QueryBuilders.termsQuery("b_id",  request.getBusinessIds()));
        }
		DateRangeAggregationBuilder dateRangeAgg = AggregationBuilders.dateRange("within_date_range")
				.field("cr_time")
				.addRange("now-30d", "now");
		PercentilesAggregationBuilder percentilesAgg = AggregationBuilders.percentiles("within_date_range_mfr")
				.field("relativeResponseTime");

		dateRangeAgg.subAggregation(percentilesAgg);

		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
				.query(boolQueryBuilder)
				.size(0)
				.aggregation(dateRangeAgg);
		searchRequest.source(sourceBuilder);

		SearchResponse searchResponse = elasticSearchExternalService
				.getSearchResult(searchRequest.source(sourceBuilder));
		Aggregations aggregations = searchResponse.getAggregations();
		ParsedDateRange dateRangeAggregation = aggregations.get("within_date_range");
		ParsedDateRange.ParsedBucket bucket = (ParsedBucket) dateRangeAggregation.getBuckets().get(0);
		Percentiles percentiles = bucket.getAggregations().get("within_date_range_mfr");
		double percentileValue = percentiles.percentile(50);
		if (Double.isNaN(percentileValue)) {
			percentileValue = 0.0;
		}
		Double aDouble = percentileValue;
		Double thisMonthTotal;
		Double formattedTime = null;
		if (aDouble != null && !Double.isNaN(aDouble)) {
			thisMonthTotal = aDouble / MStoMin;
			formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
		}
		resellerAdoptionReportResponse.setMedianResponseTime(String.valueOf(formattedTime));
	}

	private void aggregateMessageCountByLocation(ResellerSetupReportRequest request,
												 ResellerAdoptionReportResponse resellerAdoptionReportResponse) {
		SearchRequest searchRequest = new SearchRequest(Constants.Elastic.MESSAGE_INDEX);
		BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
				.must(QueryBuilders.termQuery("e_id", request.getAccountId()))
				.must(QueryBuilders.termQuery("messageType", "CHAT"))
				.must(QueryBuilders.existsQuery("communicationDirection"))
				.mustNot(QueryBuilders.termQuery("spam",Boolean.TRUE))
				.must(QueryBuilders.rangeQuery("cr_time").from("now-30d").to("now"));
		if(CollectionUtils.isNotEmpty(request.getBusinessIds())) {
			boolQueryBuilder.must(QueryBuilders.termsQuery("b_id",request.getBusinessIds()));
        }
		TermsAggregationBuilder messageCountAgg = AggregationBuilders.terms("message_count")
				.field("b_id")
				.size(10000)
				.subAggregation(
						AggregationBuilders.terms("comm_dir")
								.field("communicationDirection")
								.size(2)
				);
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
				.query(boolQueryBuilder)
				.size(0)
				.aggregation(messageCountAgg);
		SearchResponse searchResponse = elasticSearchExternalService
				.getSearchResult(searchRequest.source(sourceBuilder));

		Terms messageCountTerms = searchResponse.getAggregations().get("message_count");
		List<Integer> businessIds = new ArrayList<>();
		if("Business".equals(request.getType())) {
			businessIds.add(request.getAccountId());
		}else {
			businessIds.addAll(request.getBusinessIds());
		}
		List<Integer> receivingLessThanTenMessage = new ArrayList<>(businessIds);
		List<Integer> replyingLessThanTenMessages = new ArrayList<>(businessIds);

		for (Terms.Bucket messageCountBucket : messageCountTerms.getBuckets()) {
			String bIdKey = messageCountBucket.getKeyAsString();
			Terms commDirTerms = messageCountBucket.getAggregations().get("comm_dir");

			for (Terms.Bucket commDirBucket : commDirTerms.getBuckets()) {
				String commDirKey = commDirBucket.getKeyAsString();
				long docCount = commDirBucket.getDocCount();
				if (docCount >9) {
					if(CommunicationDirection.RECEIVE.name().equals(commDirKey)) {
						receivingLessThanTenMessage.remove(Integer.valueOf(bIdKey));
					}
					if(CommunicationDirection.SEND.name().equals(commDirKey)) {
						replyingLessThanTenMessages.remove(Integer.valueOf(bIdKey));
					}
				}
			}
		}
		if(receivingLessThanTenMessage.isEmpty()) {
			resellerAdoptionReportResponse.setReceivedTenPlusMessages("Yes");
		}else {
			resellerAdoptionReportResponse.setReceivedTenPlusMessages("No");
			resellerAdoptionReportResponse.setReceivedLessThanTenMessagesBusinessIds(receivingLessThanTenMessage);
		}
		if(replyingLessThanTenMessages.isEmpty()) {
			resellerAdoptionReportResponse.setRepliedTenPlusMessages("Yes");
		}else {
			resellerAdoptionReportResponse.setRepliedTenPlusMessages("No");
			resellerAdoptionReportResponse.setRepliedLessThanTenMessagesBusinessIds(replyingLessThanTenMessages);
		}
	}

	private void aggregateMessageCountsByDirection(ResellerSetupReportRequest request,
												   ResellerAdoptionReportResponse adoptionReportResponse) {
		SearchRequest searchRequest = new SearchRequest(Constants.Elastic.MESSAGE_INDEX);
		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
				.must(QueryBuilders.termQuery("e_id", String.valueOf(request.getAccountId())))
				.must(QueryBuilders.existsQuery("communicationDirection"))
				.mustNot(QueryBuilders.termQuery("spam",Boolean.TRUE))
				.must(QueryBuilders.rangeQuery("cr_time").from("now-30d").to("now"));

		if(CollectionUtils.isNotEmpty(request.getBusinessIds())) {
			boolQuery.must(QueryBuilders.termsQuery("b_id",request.getBusinessIds()));
        }
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(boolQuery).size(0)
				.aggregation(AggregationBuilders.terms("messagesCount").field("communicationDirection"));

		SearchResponse searchResponse = elasticSearchExternalService
				.getSearchResult(searchRequest.source(sourceBuilder));

		Terms termsAgg = searchResponse.getAggregations().get("messagesCount");
		for (Terms.Bucket bucket : termsAgg.getBuckets()) {
			String key = bucket.getKeyAsString();
			long docCount = bucket.getDocCount();
			if (CommunicationDirection.RECEIVE.name().equals(key)) {
				adoptionReportResponse.setMessagesReceived(String.valueOf(docCount));
			}
			if (CommunicationDirection.SEND.name().equals(key)) {
				adoptionReportResponse.setMessagesSent(String.valueOf(docCount));
			}
		}
	}
	private void countVoiceCalls(ResellerSetupReportRequest request,
								 ResellerAdoptionReportResponse adoptionReportResponse) {
		SearchRequest searchRequest = new SearchRequest(Constants.Elastic.MESSAGE_INDEX);

		BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
				.must(QueryBuilders.termQuery("e_id", String.valueOf(request.getAccountId())))
				.must(QueryBuilders.termQuery("source", String.valueOf(Source.VOICE_CALL.getSourceId())))
				.mustNot(QueryBuilders.termQuery("spam",Boolean.TRUE))
				.must(QueryBuilders.rangeQuery("cr_time").from("now-30d").to("now"));
		
		if(CollectionUtils.isNotEmpty(request.getBusinessIds())) {
			boolQuery.must(QueryBuilders.termsQuery("b_id",request.getBusinessIds()));
        }
		SearchSourceBuilder sourceBuilder = new SearchSourceBuilder().query(boolQuery).size(0)
				.aggregation(AggregationBuilders.terms("voiceCallCount").field("source"));
		SearchResponse searchResponse = elasticSearchExternalService
				.getSearchResult(searchRequest.source(sourceBuilder));
		Terms termsAgg = searchResponse.getAggregations().get("voiceCallCount");
		adoptionReportResponse.setVoiceCallsReceived("0");
		if(CollectionUtils.isNotEmpty(termsAgg.getBuckets())) {
			Terms.Bucket bucket =termsAgg.getBuckets().get(0);
			adoptionReportResponse.setVoiceCallsReceived(String.valueOf(bucket.getDocCount()));
		}
	}

	private void createDefaultResponse(ResellerAdoptionReportResponse adoptionReportResponse) {
		adoptionReportResponse.setMessagesReceived("NA");
		adoptionReportResponse.setMessagesSent("NA");
		adoptionReportResponse.setMedianResponseTime("NA");
		adoptionReportResponse.setVoiceCallsReceived("NA");
		adoptionReportResponse.setReceivedTenPlusMessages("NA");
		adoptionReportResponse.setRepliedTenPlusMessages("NA");
	}
}