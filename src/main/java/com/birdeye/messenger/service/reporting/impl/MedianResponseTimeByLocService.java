package com.birdeye.messenger.service.reporting.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedTDigestPercentiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.ResponseTimeByLocResponse;
import com.birdeye.messenger.dto.ResponseTimeOverLocationDataPoint;
import com.birdeye.messenger.dto.UserLoginMessage;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.birdeye.messenger.validator.ReportRequestValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MedianResponseTimeByLocService implements ReportService {

    @Autowired
    private ElasticSearchExternalService elasticSearchService;

    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private CommonService commonService;
    
    private static final int MStoMin = 60000;
    
    @Override
    @SuppressWarnings("unchecked")
    public Object getReport(ReportFilter filter) {

        ReportRequestValidator.validateLocationBasedReportFilter(filter);

        // --------------------- input validation ------------------------------------------------
        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION,filter.getSources());
        String order = StringUtils.isBlank(filter.getOrder()) ? "desc" : filter.getOrder();
        String sortBy = StringUtils.isBlank(filter.getSortBy()) ? "count" : filter.getSortBy();
        Integer page = filter.getPage();
        Integer size = filter.getSize();
        Integer accountId = filter.getAccountId();
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();

        // ----------------- get data from ES -------------------------------------------------
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("order", order);
        templateData.put("size", businessIds.size());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);

        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_MEDIAN_RESPONSE_OVER_LOCATION, data)
                .addSize(0)
                .build();

        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        ResponseTimeByLocResponse response = new ResponseTimeByLocResponse();

        if (searchResponse.status().getStatus() != 200) return response;

        UserLoginMessage userLoginMessage = new UserLoginMessage();
        userLoginMessage.setBusinessId(accountId);
        userLoginMessage.setBusinessIds(businessIds);
        Map<Integer, BizLite> businessDataForMessenger = businessService.getBizLite(businessIds, accountId);
        response.setTotalCount((int) searchResponse.getHits().getTotalHits().value);
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedLongTerms byLocation = aggregations.get("med_resp_by_loc");
        if (Objects.nonNull(byLocation)) {
            List<? extends Terms.Bucket> buckets = byLocation.getBuckets();
            List<ResponseTimeOverLocationDataPoint> dataPoints = buckets.stream().map(bucket -> {
                ResponseTimeOverLocationDataPoint dataPoint = new ResponseTimeOverLocationDataPoint();
                ParsedTDigestPercentiles percentile = bucket.getAggregations().get("median_response");
                Double aDouble = percentile.percentile(50d);
                Double thisMonthTotal;
                Double medianResponseTime = null;
                if (aDouble != null && !Double.isNaN(aDouble)) {
                    thisMonthTotal = aDouble / MStoMin;
                    medianResponseTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
                }
                dataPoint.setResponseTime(medianResponseTime);
                String businessId = bucket.getKeyAsString();
                BizLite bizLite = businessDataForMessenger.get(Integer.parseInt(businessId));
                if (Objects.nonNull(bizLite)) {
                    dataPoint.setShortLabel(bizLite.getDisplayName());
                    dataPoint.setLocationId(bizLite.getLocationId());
                    dataPoint.setLabel(bizLite.getDisplayName());
                    dataPoint.setBusinessId(bizLite.getId());
                }
                return dataPoint;
            }).filter(dataPoint -> StringUtils.isNotBlank(dataPoint.getLabel())).collect(Collectors.toList());
            response.setDataPoints(dataPoints);
        }

        List<ResponseTimeOverLocationDataPoint> dataPoints = response.getDataPoints();
        if (CollectionUtils.isNotEmpty(dataPoints)) {
            int mostIndex;
            int leastIndex;
            if (order.equals("asc")) {
                mostIndex = dataPoints.size() - 1;
                leastIndex = 0;
            } else {
                mostIndex = 0;
                leastIndex = dataPoints.size() - 1;
            }

            response.setLabelForFastest(dataPoints.get(leastIndex).getLabel());
            response.setFastest(String.valueOf(dataPoints.get(leastIndex).getResponseTime()));

            response.setLabelForSlowest(dataPoints.get(mostIndex).getLabel());
            response.setSlowest(String.valueOf(dataPoints.get(mostIndex).getResponseTime()));

        }

        sort(dataPoints, order, sortBy);
        if("table".equalsIgnoreCase(filter.getViewType())){
            addEmptyDataForTableView(dataPoints, new ArrayList<>(businessIds), businessDataForMessenger);
        }
        List<ResponseTimeOverLocationDataPoint> paginated = (List<ResponseTimeOverLocationDataPoint>) MessengerUtil.paginateIt(dataPoints, page, size, response);
        response.setDataPoints(paginated);

        ParsedCardinality locCount = aggregations.get("loc_count");
        int distinctLocations = (int) locCount.getValue();
        if (!"table".equalsIgnoreCase(filter.getViewType()) && distinctLocations > 0) {
            response.setTotalPages(((distinctLocations - 1) / filter.getSize()) + 1);
        }

        return response;

    }

    private void addEmptyDataForTableView(List<ResponseTimeOverLocationDataPoint> dataPoints, List<Integer> businessIds, Map<Integer, BizLite> businessData) {
        Set<Integer> idsForWithDataIsPresent = dataPoints.stream().map(dataPoint -> dataPoint.getBusinessId()).collect(Collectors.toSet());
        boolean removed = businessIds.removeAll(idsForWithDataIsPresent);
        if(removed) {
            businessIds.forEach(businessId -> {
                BizLite bizLite = businessData.get(businessId);
                if (Objects.nonNull(bizLite)) {
                    ResponseTimeOverLocationDataPoint dataPoint = new ResponseTimeOverLocationDataPoint();
                    dataPoint.setShortLabel(bizLite.getDisplayName());
                    dataPoint.setResponseTime(null);
                    dataPoint.setLocationId(bizLite.getLocationId());
                    dataPoint.setBusinessId(bizLite.getId());
                    dataPoint.setLabel(bizLite.getDisplayName());
                    dataPoints.add(dataPoint);
                }
            });
        }
    }

    private void sort(List<ResponseTimeOverLocationDataPoint> dataPoints, String order, String sortBy) {
        if ("name".equals(sortBy)) {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparing(ResponseTimeOverLocationDataPoint::getLabel));
            } else {
                dataPoints.sort(Comparator.comparing(ResponseTimeOverLocationDataPoint::getLabel).reversed());
            }
        } else {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparingDouble(ResponseTimeOverLocationDataPoint::getResponseTime));
            } else {
                dataPoints.sort(Comparator.comparingDouble(ResponseTimeOverLocationDataPoint::getResponseTime).reversed());
            }
        }

    }

    @Override
    public Enum getReportType() {
        return ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION;
    }

    @Override
    public Object getReportV2(ReportFilter filter) throws Exception {
        return null;
    }
}
