package com.birdeye.messenger.service.reporting.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RobinResponseByTime {

    private Integer thisMonth;
    private Long lastOneYear;
    private String groupByType;
    private Long totalCount;
    private Long dateDiff;
    private List<RobinResponseDataPoint> dataPoints = Collections.emptyList();

}
