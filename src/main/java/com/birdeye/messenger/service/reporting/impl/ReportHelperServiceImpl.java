package com.birdeye.messenger.service.reporting.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportHelperService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.EarliestMetricDateDTO;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesByTimeResponse;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReportHelperServiceImpl implements ReportHelperService {

    private final ElasticSearchExternalService elasticSearchService;

    @Override
    @SuppressWarnings("unchecked")
    public EarliestMetricDateDTO getEarliestMessageDate(ReportFilter filter) {

        EarliestMetricDateDTO response = new EarliestMetricDateDTO();

        Integer accountId = filter.getAccountId();
        List<Integer> businessIds = filter.getBusinessIds();
        if(Objects.isNull(accountId) || Objects.isNull(businessIds)) {
            return response;
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);

        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addSize(1)
                .addTemplateAndDataModel(Constants.Elastic.GET_OLDEST_MESSAGE, data).build();

        ElasticData<MessageDocument> dataFromElastic = elasticSearchService.getDataFromElastic(esRequest, MessageDocument.class);
        if(dataFromElastic.isSucceeded() && CollectionUtils.isNotEmpty(dataFromElastic.getResults())) {
            List<MessageDocument> messageDocuments = dataFromElastic.getResults();
            MessageDocument doc = messageDocuments.get(0);
            response.setEarliestMetricDate(doc.getCr_date());
        }
        return response;
    }
    @Override
    @Cacheable(cacheNames = Constants.APP_RECEIVE_MSG_COUNT_CACHE, condition="#accountId == #businessId",key="'APP_RCV_MSG_COUNT-'.concat(#accountId)", unless="#result == null")
    public Integer getReceivedMessageCountOverTime(ReportFilter filters, ReportService reportService, Integer accountId, Integer businessId) throws Exception {
		filters.setDashboardInboxReport(true);
        filters.setInterval("all_time");
        ReceivedMessagesByTimeResponse receivedMessagesByTimeResponse = (ReceivedMessagesByTimeResponse)reportService.getReport(filters);
        Integer receivedMessageCount=receivedMessagesByTimeResponse.getTotalCount()!=null?receivedMessagesByTimeResponse.getTotalCount():0;
		return receivedMessageCount;
	}
}
