package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.metrics.ParsedTDigestPercentiles;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.MedianResponseOverTimeDTO;
import com.birdeye.messenger.service.reporting.dto.MedianResponseOverTimeDataPoint;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class MedianResponseOverTimeServiceImpl implements ReportService {

    private static final int MStoMin = 60000;
    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    public Object getReport(ReportFilter filter) throws Exception {

        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.MEDIAN_RESPONSE_OVER_TIME,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }


        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("interval", interval);
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", true);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("thisMonth", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_MEDIAN_RESPONSE_OVER_TIME, data)
                .addSize(0)
                .build();

        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);

        if (searchResponse.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch Received Messages Report");
        } else {
            MedianResponseOverTimeDTO response = new MedianResponseOverTimeDTO();
            response.setTotalCount((int) searchResponse.getHits().getTotalHits().value);
            response.setGroupByType(filter.getInterval());


            List<MedianResponseOverTimeDataPoint> dataPoints = new ArrayList<>();
            Aggregations aggregations = searchResponse.getAggregations();
            ParsedDateHistogram termsAggregations = aggregations.get("response_time");

            List<? extends Histogram.Bucket> buckets = termsAggregations.getBuckets();

            if (CollectionUtils.isEmpty(buckets)) {
                return response;
            }

            buckets.stream().forEach(bucket -> {
            	ParsedTDigestPercentiles percentilesAggregation = bucket.getAggregations().get("median_first_response");
                if (!ObjectUtils.isEmpty(percentilesAggregation)) {
                    Double medianResponseTime = percentilesAggregation.percentile(50d);
                    MedianResponseOverTimeDataPoint medianResponseOverTimeDataPoint = new MedianResponseOverTimeDataPoint();
                    medianResponseOverTimeDataPoint.setResponseTime(medianResponseTime);
                    ZonedDateTime instant = (ZonedDateTime) bucket.getKey();
                    Date date = Date.from(instant.toInstant());
                    String dateBucket = bucket.getKeyAsString();
                    String[] parts = dateBucket.split("(?=T)");
                    dateBucket = parts[0];
                    LocalDate dateTime = LocalDate.parse(dateBucket);
                    DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    String label = dateTime.format(formatter);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    int d = dateTime.getDayOfMonth();
                    int month = dateTime.getMonthValue();
                    int year  = dateTime.getYear();
                    //calendar.get(Calendar.MONTH) is zero indexed so +1 to get exact month
                    if(calendar.get(Calendar.DAY_OF_MONTH) < d || calendar.get(Calendar.MONTH)+1 < month || calendar.get(Calendar.YEAR) < year) {
                        date =  DateUtils.addDays(date, 1);
                    }
                    medianResponseOverTimeDataPoint.setLabel(label);
                    medianResponseOverTimeDataPoint.setShortLabel(label);
                    medianResponseOverTimeDataPoint.setDateTime(date);
                    dataPoints.add(medianResponseOverTimeDataPoint);
                } else {
                    MedianResponseOverTimeDataPoint medianResponseOverTimeDataPoint = new MedianResponseOverTimeDataPoint();
                    medianResponseOverTimeDataPoint.setResponseTime(null);
                    ZonedDateTime instant = (ZonedDateTime) bucket.getKey();
                    Date date = Date.from(instant.toInstant());
                    String dateBucket = bucket.getKeyAsString();
                    String[] parts = dateBucket.split("(?=T)");
                    dateBucket = parts[0];
//                    String part2 = parts[1];
                    LocalDate dateTime = LocalDate.parse(dateBucket);
                    DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    String label = dateTime.format(formatter);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(date);
                    int d = dateTime.getDayOfMonth();
                    int month = dateTime.getMonthValue();
                    int year  = dateTime.getYear();
                    //calendar.get(Calendar.MONTH) is zero indexed so +1 to get exact month
                    if(calendar.get(Calendar.DAY_OF_MONTH) < d || calendar.get(Calendar.MONTH)+1 < month || calendar.get(Calendar.YEAR) < year) {
                        date =  DateUtils.addDays(date, 1);
                    }
                    medianResponseOverTimeDataPoint.setLabel(label);
                    medianResponseOverTimeDataPoint.setShortLabel(label);
                    medianResponseOverTimeDataPoint.setDateTime(date);
                    dataPoints.add(medianResponseOverTimeDataPoint);
                }
            });

            Range last_1_month = aggregations.get("last_1_month");
            if (Objects.nonNull(last_1_month)) {
            	List<? extends Range.Bucket> oneMonthBucket = last_1_month.getBuckets();
                oneMonthBucket.stream().findAny().ifPresent(bucket -> {
                	ParsedTDigestPercentiles percentilesAggregation = bucket.getAggregations().get("last_month_mfr");
                    Double aDouble = percentilesAggregation.percentile(50d);
                    Double formattedTime = null;
                    if (aDouble != null && !Double.isNaN(aDouble)) {
                        Double thisMonthTotal = aDouble / MStoMin;
                        formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
                    }
                    response.setThisMonth(formattedTime);
                });
            }

            Range lastOneYearAggregation = aggregations.get("last_one_year");
            if (Objects.nonNull(lastOneYearAggregation)) {
            	List<? extends Range.Bucket> oneMonthBucket = lastOneYearAggregation.getBuckets();
                oneMonthBucket.stream().findAny().ifPresent(bucket -> {
                	ParsedTDigestPercentiles percentilesAggregation = bucket.getAggregations().get("last_one_year_mrt");
                    Double aDouble = percentilesAggregation.percentile(50d);
                    Double thisMonthTotal;
                    Double formattedTime = null;
                    if (aDouble != null && !Double.isNaN(aDouble)) {
                        thisMonthTotal = aDouble / MStoMin;
                        formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
                    }
                    response.setLastOneYear(formattedTime);
                });
            }
			if (filter.isDashboardInboxReport()) {
				Range withinDateRange = aggregations.get("within_date_range");
				if (Objects.nonNull(withinDateRange)) {
					List<? extends Range.Bucket> oneMonthBucket = withinDateRange.getBuckets();
					oneMonthBucket.stream().findAny().ifPresent(bucket -> {
						ParsedTDigestPercentiles percentilesAggregation = bucket
								.getAggregations().get("within_date_range_mfr");
						Double aDouble = percentilesAggregation.percentile(50d);
						Double thisMonthTotal;
						Double formattedTime = null;
						if (aDouble != null && !Double.isNaN(aDouble)) {
							thisMonthTotal = aDouble / MStoMin;
							formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
						}
						response.setWithinDateRange(formattedTime);
					});
				}
			}
            response.setDataPoints(dataPoints);
            // MS to Minute conversion + Ceil Response Time
            response.getDataPoints().forEach(a -> {
                if (a.getResponseTime() != null && !Double.isNaN(a.getResponseTime())) {
                    Double formattedTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(a.getResponseTime() / MStoMin);
                    a.setResponseTime(formattedTime);
                } else {
                	a.setResponseTime(0d);
                }
            });
            return response;
        }

    }

    @Override
    public Enum getReportType() {
        return ReportType.MEDIAN_RESPONSE_OVER_TIME;
    }

    @Override
    public Object getReportV2(ReportFilter filter) throws Exception {
        Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
        Date startDate ;
        Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filter.getEndDateInMillis() - filter.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filter.getInterval());
        MedianResponseOverTimeDTO response = (MedianResponseOverTimeDTO) getReport(filter);
        List<MedianResponseOverTimeDataPoint> medianResponseOverTimeDataPoints = response.getDataPoints();
        if(CollectionUtils.isNotEmpty(response.getDataPoints())) {
            startDate = response.getDataPoints().get(0).getDateTime();
        }else {
            startDate = new Date(startTS.getTime());
        }

        if (startDate.before(new Date(startTS.getTime()))) {
            startDate = new Date(startTS.getTime());
        }
        com.birdeye.messenger.util.DateUtils.customizeLabels(medianResponseOverTimeDataPoints, startDate, endDate, groupByType, false);
        List<MedianResponseOverTimeDataPoint> notNullResponseTime = new ArrayList<>();
        for (MedianResponseOverTimeDataPoint responseTime : response.getDataPoints()) {
            if(responseTime.getResponseTime() != null) {
                notNullResponseTime.add(responseTime);
            }
        }
        response.setDataPoints(notNullResponseTime);
        response.setDateDiff(days);
        return response;

    }
}
