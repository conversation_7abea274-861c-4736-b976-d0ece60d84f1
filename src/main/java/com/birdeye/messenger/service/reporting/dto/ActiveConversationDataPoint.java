package com.birdeye.messenger.service.reporting.dto;

import com.birdeye.messenger.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActiveConversationDataPoint extends ReportDataPoint {

    private UserDTO user;

    private Integer userId;

    private Integer locationId;

    private Integer activeConversationCount;

    private Integer businessId;

}
