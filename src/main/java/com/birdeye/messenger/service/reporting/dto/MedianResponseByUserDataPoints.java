package com.birdeye.messenger.service.reporting.dto;

import com.birdeye.messenger.dto.UserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MedianResponseByUserDataPoints extends ReportDataPoint {

    private UserDTO user;

    private Integer userId;

    private Double responseTimeMin;
}
