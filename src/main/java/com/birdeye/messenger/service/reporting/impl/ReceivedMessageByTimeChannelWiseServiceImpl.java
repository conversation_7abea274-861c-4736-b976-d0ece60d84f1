package com.birdeye.messenger.service.reporting.impl;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesByTimeChannelWiseDataPoint;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesByTimeChannelWiseResponse;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.TimeZoneUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.stereotype.Service;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.bucket.range.Range;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReceivedMessageByTimeChannelWiseServiceImpl implements ReportService{
    
    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    public ReceivedMessagesByTimeChannelWiseResponse getReport(ReportFilter filter) {
        return buildESRequestForReceiveMessages(filter);
    }

    @Override
    public Enum getReportType() {
        return ReportType.RECEIVED_MESSAGES_CHANNEL_WISE;
    }

    private ReceivedMessagesByTimeChannelWiseResponse buildESRequestForReceiveMessages(ReportFilter filter) {

        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.RECEIVED_MESSAGES_CHANNEL_WISE,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_RECEIVED_MESSAGES_REPORT_BY_TIME_CHANNEL_WISE, data)
                .addSize(0)
                .build();

        SearchResponse searchResult = elasticSearchService.getSearchResult(request);

        if (searchResult.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch Received Messages Report");
        } else {
            ReceivedMessagesByTimeChannelWiseResponse response = new ReceivedMessagesByTimeChannelWiseResponse();
            response.setTotalCount(Math.toIntExact(searchResult.getHits().getTotalHits().value));
            if(filter.isDashboardInboxReport()) {
                return response;
            }
            response.setGroupByType(filter.getInterval());

            Aggregations aggregations = searchResult.getAggregations();
            ParsedDateHistogram receive_message = aggregations.get("receive_message");
            if (Objects.nonNull(receive_message)) {
                List<? extends Histogram.Bucket> buckets = receive_message.getBuckets();
                List<ReceivedMessagesByTimeChannelWiseDataPoint> dataPoints = buckets.stream().map(bucket -> {
                    DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                    String dateKeyAsString = bucket.getKeyAsString();
                    String[] parts = dateKeyAsString.split("(?=T)");
                    dateKeyAsString = parts[0];
//                    String part2 = parts[1];
                    LocalDate date = LocalDate.parse(dateKeyAsString);
//                    LocalDateTime dateTime = LocalDateTime.parse(dateBucket, formatter);
//                    ZonedDateTime time = dateTime.atZone(ZoneId.of(filter.getAccountTimeZoneId()));
//            		LocalDate date = instant.toLocalDate();
                    String label = date.format(formatter);
                    ReceivedMessagesByTimeChannelWiseDataPoint dataPoint = new ReceivedMessagesByTimeChannelWiseDataPoint();
                    if(CollectionUtils.isEmpty(filter.getSources())){
                        insertDefaultChannels(dataPoint);
                    }else if(CollectionUtils.isNotEmpty(sourceIds)){
                        Map<String,Integer> receivedMessageCountMap=new HashMap<>();
                        for(Integer source: sourceIds){
                            receivedMessageCountMap.put(getKey(source),0);
                        }
                        dataPoint.setSubDataPoints(receivedMessageCountMap);
                    }
                    ParsedLongTerms source_aggregation = bucket.getAggregations().get("source_aggregation");
                    if(Objects.nonNull(source_aggregation) && CollectionUtils.isNotEmpty(source_aggregation.getBuckets())) {
                        dataPoint.setTotalCount(bucket.getDocCount());
                        for(Bucket parsed_bucket:source_aggregation.getBuckets()) {
                            int count=(int)(parsed_bucket != null ? parsed_bucket.getDocCount() : 0);
                            dataPoint.getSubDataPoints().putIfAbsent(getKey(((Long) parsed_bucket.getKey()).intValue()), 0);
                            dataPoint.getSubDataPoints().put(getKey(((Long) parsed_bucket.getKey()).intValue()), dataPoint.getSubDataPoints().get(getKey(((Long) parsed_bucket.getKey()).intValue()))+ count);
                        }
                    }
                    dataPoint.setLabel(label);
                    dataPoint.setShortLabel(label);
                    return dataPoint;
                }).collect(Collectors.toList());
                response.setDataPoints(dataPoints);
            }

            Range last_1_month = aggregations.get("last_1_month");
            if (Objects.nonNull(last_1_month)) {
                List<? extends Range.Bucket> buckets = last_1_month.getBuckets();
                buckets.stream().findAny().ifPresent(bucket -> response.setThisMonth((int) bucket.getDocCount()));
            }

            if (Boolean.TRUE.equals(filter.isLastOneYear())) {
                ParsedCardinality messageReceivedInLastOneYear = aggregations.get("last_one_year");
                response.setLastOneYear((Long)(messageReceivedInLastOneYear!=null?messageReceivedInLastOneYear.getValue():0));

            }
            return response;
        }
    }

    private String getKey(int source) {
        if (com.birdeye.messenger.enums.Source.WEB_CHAT.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_RECEIVE.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId().equals(source)
        ) {
            return com.birdeye.messenger.enums.Source.WEB_CHAT.name();
        }
        return com.birdeye.messenger.enums.Source.getValue(source).name();
    }
    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filters.getEndDateInMillis() - filters.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        ReceivedMessagesByTimeChannelWiseResponse receivedMessagesByTimeResponse =  getReport(filters);
        List<ReceivedMessagesByTimeChannelWiseDataPoint> receivedMessagesByTimeResponseDataPoints = receivedMessagesByTimeResponse.getDataPoints();
        DateUtils.customizeLabels(receivedMessagesByTimeResponseDataPoints, startDate, endDate, groupByType, false);
        receivedMessagesByTimeResponse.setDateDiff(days);
        return  receivedMessagesByTimeResponse;
    }
    
    private void insertDefaultChannels(ReceivedMessagesByTimeChannelWiseDataPoint dataPoint){
        Map<String,Integer> receivedMessageCountMap=new HashMap<>();
        receivedMessageCountMap.put(Source.APPLE.name(),0);
        receivedMessageCountMap.put(Source.APPOINTMENT.name(),0);
        receivedMessageCountMap.put(Source.CONTACT_US.name(),0);
        receivedMessageCountMap.put(Source.EMAIL.name(),0);
        receivedMessageCountMap.put(Source.FACEBOOK.name(),0);
        receivedMessageCountMap.put(Source.INSTAGRAM.name(),0);
        receivedMessageCountMap.put(Source.TWITTER.name(),0);
        receivedMessageCountMap.put(Source.GOOGLE.name(),0);
        receivedMessageCountMap.put(Source.SMS.name(),0);
        receivedMessageCountMap.put(Source.VOICE_CALL.name(),0);
        receivedMessageCountMap.put(Source.WEB_CHAT.name(),0);
        dataPoint.setSubDataPoints(receivedMessageCountMap);
    }
    
}
