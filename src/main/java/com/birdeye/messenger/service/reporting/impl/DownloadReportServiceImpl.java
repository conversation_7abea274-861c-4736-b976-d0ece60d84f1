package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.util.*;

import com.birdeye.messenger.dto.report.*;
import com.birdeye.messenger.service.reporting.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ActiveConversationByUserResponse;
import com.birdeye.messenger.dto.ReceivedConversationByLocResponse;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.ResponseTimeByLocResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.MessageService;
import com.birdeye.messenger.service.reporting.DownloadReportService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.util.DateUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class DownloadReportServiceImpl implements DownloadReportService {

    private final List<ReportService> reportServiceList;
    private final MessageService messageService;
    @Override
    public GenericDownloadReportDTO downloadReportByTime(DownloadReportFilter downloadFilter) throws Exception {

        ReportFilter filter = filterConversion(downloadFilter, null, null);

        // TODO - Need parallelism for all the 3 reports
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
        ReceivedMessagesByTimeResponse receivedMessagesByTimeResponse = (ReceivedMessagesByTimeResponse) reportService.getReport(filter);

        Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());


        Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());


        GroupByType groupByType = GroupByType.fromString(filter.getInterval());
        if (receivedMessagesByTimeResponse != null)
            DateUtils.customizeLabels(receivedMessagesByTimeResponse.getDataPoints(), startDate, endDate, groupByType, false);


        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByTimeDownloadReportDTO> allItems = new ArrayList<>();

        Map<String, ByTimeDownloadReportDTO> map = new LinkedHashMap<>();

        // PREPARE Map for Date only
        if (receivedMessagesByTimeResponse != null && receivedMessagesByTimeResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedMessagesByTimeResponse.getDataPoints())) {
            receivedMessagesByTimeResponse.getDataPoints().forEach(a -> map.put(a.getLabel(), new ByTimeDownloadReportDTO(a.getLabel())));
        }

        // SET Received Messages count
        if (receivedMessagesByTimeResponse != null && receivedMessagesByTimeResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedMessagesByTimeResponse.getDataPoints())) {
            receivedMessagesByTimeResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel()))
                    map.get(a.getLabel()).setReceivedMessages(a.getReceivedMessageCount());
            });
        }

        // SET active conv count
        reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_TIME);
        ActiveConversationByTimeResponse activeConversationByTimeResponse = (ActiveConversationByTimeResponse) reportService.getReport(filter);
        if (activeConversationByTimeResponse != null)
            DateUtils.customizeLabels(activeConversationByTimeResponse.getDataPoints(), startDate, endDate, groupByType, false);

        if (activeConversationByTimeResponse != null && activeConversationByTimeResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationByTimeResponse.getDataPoints())) {
            activeConversationByTimeResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel()))
                    map.get(a.getLabel()).setActiveConversations(a.getActiveConversationCount().intValue());
                else {
                    ByTimeDownloadReportDTO byTimeDownloadReportDTO = new ByTimeDownloadReportDTO();
                    byTimeDownloadReportDTO.setActiveConversations(a.getActiveConversationCount());
                    byTimeDownloadReportDTO.setDate(a.getLabel());
                    map.put(a.getLabel(), byTimeDownloadReportDTO);
                }
            });
        }

        // SET MEDIAN RESPONSE OVER TIME
        reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_OVER_TIME);
        MedianResponseOverTimeDTO medianResponseOverTime = (MedianResponseOverTimeDTO) reportService.getReport(filter);
        if (medianResponseOverTime != null)
            DateUtils.customizeLabels(medianResponseOverTime.getDataPoints(), startDate, endDate, groupByType, false);

        if (medianResponseOverTime != null && medianResponseOverTime.getDataPoints() != null && CollectionUtils.isNotEmpty(medianResponseOverTime.getDataPoints())) {
            medianResponseOverTime.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel()))
                    map.get(a.getLabel()).setResponseTime(a.getResponseTime());
                else {
                    ByTimeDownloadReportDTO byTimeDownloadReportDTO = new ByTimeDownloadReportDTO();
                    byTimeDownloadReportDTO.setResponseTime(a.getResponseTime());
                    byTimeDownloadReportDTO.setDate(a.getLabel());
                    map.put(a.getLabel(), byTimeDownloadReportDTO);
                }
            });
        }

        map.forEach((k, v) -> allItems.add(v));

        genericDownloadReportDTO.setData(allItems);
        log.info("Response Data : {}", genericDownloadReportDTO);

        return genericDownloadReportDTO;
    }

    @Override
    public GenericDownloadReportDTO downloadReportOverLocation(DownloadReportFilter downloadFilter, Integer startIndex, Integer pageSize) throws Exception {

        log.info("Download Report Over location Request : {} , Start Index {} , pageSize {}", downloadFilter, startIndex, pageSize);
        ReportFilter filter = filterConversion(downloadFilter, startIndex, pageSize);
        String viewType = downloadFilter.getViewType();

        // TODO - Need parallelism for all the 3 reports
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_CONVERSATION_BY_LOCATION);
        filter.setViewType("table");
        ReceivedConversationByLocResponse receivedConversationByLocResponse = (ReceivedConversationByLocResponse) reportService.getReport(filter);
        filter.setViewType(viewType);
        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByLocationDownloadReportDTO> allItems = new ArrayList<>();

        Map<Integer, ByLocationDownloadReportDTO> map = new LinkedHashMap<>();

        // PREPARE Map for businessId only
        if (receivedConversationByLocResponse != null && receivedConversationByLocResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedConversationByLocResponse.getDataPoints())) {
            receivedConversationByLocResponse.getDataPoints().forEach(a -> map.put(a.getBusinessId(), new ByLocationDownloadReportDTO(a.getLabel(), a.getBusinessId())));
        }

        // SET Received Messages count
        if (receivedConversationByLocResponse != null && receivedConversationByLocResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedConversationByLocResponse.getDataPoints())) {
            receivedConversationByLocResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getBusinessId()))
                    map.get(a.getBusinessId()).setReceivedMessages(a.getReceivedConversationCount());
            });
        }

        // SET active conv count
        reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_LOCATION);
        ActiveConversationByLocResponse activeConversationByLocResponse = (ActiveConversationByLocResponse) reportService.getReport(filter);
        if (activeConversationByLocResponse != null && activeConversationByLocResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationByLocResponse.getDataPoints())) {
            activeConversationByLocResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getBusinessId()))
                    map.get(a.getBusinessId()).setActiveConversations(a.getActiveConversationCount());
                else {
                    ByLocationDownloadReportDTO byLocationDownloadReportDTO = new ByLocationDownloadReportDTO();
                    byLocationDownloadReportDTO.setActiveConversations(a.getActiveConversationCount());
                    byLocationDownloadReportDTO.setLocationName(a.getLabel());
                    byLocationDownloadReportDTO.setBusinessId(a.getBusinessId());
                    map.put(a.getBusinessId(), byLocationDownloadReportDTO);
                }
            });
        }

        // SET Response Time
        reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_LOCATION);
        ResponseTimeByLocResponse responseTimeByLocResponse = (ResponseTimeByLocResponse) reportService.getReport(filter);
        if (responseTimeByLocResponse != null && responseTimeByLocResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(responseTimeByLocResponse.getDataPoints())) {
            responseTimeByLocResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getBusinessId()))
                    map.get(a.getBusinessId()).setResponseTime(a.getResponseTime());
                else {
                    ByLocationDownloadReportDTO byLocationDownloadReportDTO = new ByLocationDownloadReportDTO();
                    byLocationDownloadReportDTO.setResponseTime(a.getResponseTime());
                    byLocationDownloadReportDTO.setLocationName(a.getLabel());
                    byLocationDownloadReportDTO.setBusinessId(a.getBusinessId());
                    map.put(a.getBusinessId(), byLocationDownloadReportDTO);
                }
            });
        }

        map.forEach((k, v) -> allItems.add(v));

        genericDownloadReportDTO.setData(allItems);
        return genericDownloadReportDTO;
    }

    @Override
    public GenericDownloadReportDTO downloadReportByUser(DownloadReportFilter downloadFilter, Integer startIndex, Integer pageSize) throws Exception {
        ReportFilter reportFilter = filterConversion(downloadFilter, startIndex, pageSize);
        Map<String, ByUserDownloadReportDTO> map = new LinkedHashMap<>();
        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByUserDownloadReportDTO> allItems = new ArrayList<>();

        ReportService reportServiceByType = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_USER);
        ActiveConversationByUserResponse activeConversationByUserResponse = (ActiveConversationByUserResponse) reportServiceByType.getReport(reportFilter);


        // PREPARE Map for userId only
        if (activeConversationByUserResponse != null && activeConversationByUserResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationByUserResponse.getDataPoints())) {
            activeConversationByUserResponse.getDataPoints().forEach(a -> map.put(a.getUser().getId().toString(), new ByUserDownloadReportDTO(a.getUser().getId(), a.getUser().getName())));
        }

        // PREPARE Map for User Active Conversation
        if (activeConversationByUserResponse != null && activeConversationByUserResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationByUserResponse.getDataPoints())) {
            activeConversationByUserResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getUser().getId().toString()))
                    map.get(a.getUser().getId().toString()).setActiveConversations(a.getActiveConversationCount());
            });
        }


        reportServiceByType = getReportServiceByType(ReportType.MEDIAN_RESPONSE_TIME_BY_USER);
        MedianResponseTimeByUserResponse medianResponseTimeByUserResponse = (MedianResponseTimeByUserResponse) reportServiceByType.getReport(reportFilter);

        // PREPARE Map for User Response Time
        if (medianResponseTimeByUserResponse != null && medianResponseTimeByUserResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(medianResponseTimeByUserResponse.getDataPoints())) {
            medianResponseTimeByUserResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getUser().getId().toString()))
                    map.get(a.getUser().getId().toString()).setResponseTime(a.getResponseTimeMin());
                else{
                    ByUserDownloadReportDTO byUserDownloadReportDTO = new ByUserDownloadReportDTO();
                    byUserDownloadReportDTO.setResponseTime(a.getResponseTimeMin());
                    byUserDownloadReportDTO.setUserName(a.getUser().getName());
                    byUserDownloadReportDTO.setUserId(a.getUser().getId());
                    map.put(a.getUser().getId().toString(),byUserDownloadReportDTO);
                }
            });
        }

        map.forEach((k, v) -> allItems.add(v));

        genericDownloadReportDTO.setData(allItems);
        return genericDownloadReportDTO;
    }

    public static ReportFilter filterConversion(DownloadReportFilter downloadReportFilter, Integer startIndex, Integer pageSize) {

        ReportFilter reportFilter = new ReportFilter();

        if (startIndex != null && pageSize != null && pageSize != 0) {
            reportFilter.setSize(pageSize);
            reportFilter.setPage(startIndex / pageSize);
        }

        if (downloadReportFilter.getFilter() != null && CollectionUtils.isNotEmpty(downloadReportFilter.getFilter().getUserIds())) {
            reportFilter.setUserIds(downloadReportFilter.getFilter().getUserIds());
        }
        if(CollectionUtils.isNotEmpty(downloadReportFilter.getFilter().getSources())){
            reportFilter.setSources(downloadReportFilter.getFilter().getSources());
        }
        reportFilter.setInterval(downloadReportFilter.getFilter().getInterval());
        reportFilter.setLastOneYear(downloadReportFilter.getFilter().isLastOneYear());
        if(StringUtils.isNotEmpty(downloadReportFilter.getFilter().getStartDate())){
            reportFilter.setStartDate(downloadReportFilter.getFilter().getStartDate());
        }
        if(StringUtils.isNotEmpty(downloadReportFilter.getFilter().getEndDate())){
            reportFilter.setEndDate(downloadReportFilter.getFilter().getEndDate());
        }
        reportFilter.setAccountId(downloadReportFilter.getAccountId());
        reportFilter.setBusinessIds(downloadReportFilter.getBusinessIds());
        reportFilter.setStartTimeEpoch(downloadReportFilter.getFilter().getStartTimeEpoch());
        reportFilter.setEndTimeEpoch(downloadReportFilter.getFilter().getEndTimeEpoch());
        reportFilter.setViewType(downloadReportFilter.getViewType());
        if(StringUtils.isNotEmpty(downloadReportFilter.getAccountTimeZoneId())) {
            reportFilter.setAccountTimeZoneId(downloadReportFilter.getAccountTimeZoneId());
        } else if (StringUtils.isNotEmpty(downloadReportFilter.getTimezoneId())){
            reportFilter.setAccountTimeZoneId(downloadReportFilter.getTimezoneId());
        } else {
            reportFilter.setAccountTimeZoneId(Constants.DEFAULT_REPORT_TIMEZONE_ID);
        }

        reportFilter.setCustomerType(downloadReportFilter.getCustomerType());
        reportFilter.setExperienceScores(downloadReportFilter.getExperienceScores());
        reportFilter.setSurveyScores(downloadReportFilter.getSurveyScores());
        
        log.info("Converted Data {}", reportFilter);
        return reportFilter;
    }

    private ReportService getReportServiceByType(ReportType type) {
        return reportServiceList.stream()
                .filter(reportService -> type.equals(reportService.getReportType()))
                .findFirst().orElseThrow(() -> new MessengerException(ErrorCode.INVALID_REPORT_TYPE));
    }

    @Override
    public GenericDownloadReportDTO downloadRobinUnansweredQuestionsV1(DownloadReportFilter downloadFilter,
                                                                       Integer startIndex, Integer pageSize) {
        if(downloadFilter.getAccountId()==null) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
        }
        return messageService.getRobinUnansweredQuestionsV1(downloadFilter.getAccountId(),pageSize,startIndex);

    }

    @Override
    public GenericDownloadReportDTO downloadRobinUnansweredQuestions(DownloadReportFilter downloadFilter,
                                                                     Integer startIndex, Integer pageSize) {
        if(downloadFilter.getAccountId()==null) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.MISSING_ACCOUNT_ID, HttpStatus.BAD_REQUEST));
        }
        if (downloadFilter.isUnansweredFaq()) {
        	return messageService.getRobinUnansweredFaq(downloadFilter,pageSize,startIndex);
        }
        return messageService.getRobinUnansweredQuestions(downloadFilter.getAccountId(),pageSize,startIndex,Constants.Elastic.GET_ROBIN_UNASWERED_QUESTIONS);

    }

    @Override
    public GenericDownloadReportDTO downloadRobinResponseByTimeReports(DownloadReportFilter downloadFilter) throws Exception {
        ReportFilter filter = filterConversion(downloadFilter, null, null);
        Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        GroupByType groupByType = GroupByType.fromString(filter.getInterval());
        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByTimeRobinDownloadReportDTO> allItems = new ArrayList<>();
        Map<String, ByTimeRobinDownloadReportDTO> map = new LinkedHashMap<>();
        ReportService reportService = getReportServiceByType(ReportType.ROBIN_RESPONSE_BY_TIME);
        RobinResponseByTime RobinresponseOverTime = (RobinResponseByTime) reportService.getReport(filter);
        if (RobinresponseOverTime != null)
            DateUtils.customizeLabels(RobinresponseOverTime.getDataPoints(), startDate, endDate, groupByType, false);

        // PREPARE Map for Date only
        if (RobinresponseOverTime != null && RobinresponseOverTime.getDataPoints() != null && CollectionUtils.isNotEmpty(RobinresponseOverTime.getDataPoints())) {
            RobinresponseOverTime.getDataPoints().forEach(a -> map.put(a.getLabel(), new ByTimeRobinDownloadReportDTO(a.getLabel())));
        }
        // SET Robin Response Messages count
        if (RobinresponseOverTime != null && RobinresponseOverTime.getDataPoints() != null && CollectionUtils.isNotEmpty(RobinresponseOverTime.getDataPoints())) {
            RobinresponseOverTime.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel()))
                    map.get(a.getLabel()).setResponseCount(a.getRobinResponseCount());
            });
        }
        map.forEach((k, v) -> allItems.add(v));
        genericDownloadReportDTO.setData(allItems);
        log.info("Response Data : {}", genericDownloadReportDTO);
        return genericDownloadReportDTO;
    }

    @Override
    public GenericDownloadReportDTO downloadMessageReceivedChannelWiseReportsExcel(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception{
        ReportFilter filter = filterConversion(downloadFilter, startIndex, pageSize);
        Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        GroupByType groupByType = GroupByType.fromString(filter.getInterval());
        String viewType = downloadFilter.getViewType();

        // TODO - Need parallelism for all the 3 reports
        ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_CHANNEL_WISE);
        filter.setViewType("table");
        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByChannelReportDownloadResponseDTO> allItems = new ArrayList<>();
        Map<String,ByChannelReportDownloadResponseDTO> map = new LinkedHashMap<>();
        ReceivedMessagesByTimeChannelWiseResponse receivedMessagesChannelWiseResponse = (ReceivedMessagesByTimeChannelWiseResponse) reportService.getReport(filter);
        filter.setViewType(viewType);

        if (Objects.nonNull(receivedMessagesChannelWiseResponse))
            DateUtils.customizeLabels(receivedMessagesChannelWiseResponse.getDataPoints(), startDate, endDate, groupByType, false);

        // PREPARE Map for Date only
        if (receivedMessagesChannelWiseResponse != null && receivedMessagesChannelWiseResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedMessagesChannelWiseResponse.getDataPoints())) {
            receivedMessagesChannelWiseResponse.getDataPoints().forEach(a -> map.put(a.getLabel(), new ByChannelReportDownloadResponseDTO()));
        }
        if (receivedMessagesChannelWiseResponse != null && receivedMessagesChannelWiseResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(receivedMessagesChannelWiseResponse.getDataPoints())) {
            receivedMessagesChannelWiseResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel())){
                    ByChannelReportDownloadResponseDTO response = new ByChannelReportDownloadResponseDTO();
                    response.setLabel(a.getLabel());
                    a.getSubDataPoints().forEach((field, value) -> setField(response, field, value));
                    map.put(a.getLabel(),response);
                }
            });
        }

        
        map.forEach((k, v) -> allItems.add(v));

        genericDownloadReportDTO.setData(allItems);
        return genericDownloadReportDTO;
    }

    private static void setField(ByChannelReportDownloadResponseDTO dto, String field, Integer value) {
        switch (field) {
            case "APPLE":
                dto.setAppleMessages(value);
                break;
            case "APPOINTMENT":
                dto.setAppointment(value);
                break;
            case "WEB_CHAT":
                dto.setWebchat(value);
                break;
            case "EMAIL":
                dto.setEmail(value);
                break;
            case "FACEBOOK":
                dto.setFacebook(value);
                break;
            case "INSTAGRAM":
                dto.setInstagram(value);
                break;
            case "TWITTER":
                dto.setTwitter(value);
                break;
            case "SMS":
                dto.setText(value);
                break;
            case "VOICE_CALL":
                dto.setVoiceCall(value);
                break;
            case "CONTACT_US":
                dto.setWebforms(value);
                break;
            case "GOOGLE":
                dto.setGoogle(value);
                break;
            default:
                log.info("Unknown channel: {}",value);
                break;
        }
    }

    @Override
    public GenericDownloadReportDTO downloadMessageReceivedChannelWiseReportsPdf(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception{
        GenericDownloadReportDTO excelResponse = downloadMessageReceivedChannelWiseReportsExcel(downloadFilter,startIndex,pageSize);
        return null;
    }

    @Override
    public GenericDownloadReportDTO downloadActiveConversationChannelWiseReportsExcel(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception{
        ReportFilter filter = filterConversion(downloadFilter, startIndex, pageSize);
        Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        GroupByType groupByType = GroupByType.fromString(filter.getInterval());
        String viewType = downloadFilter.getViewType();

        // TODO - Need parallelism for all the 3 reports
        ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_CHANNEL_WISE);
        filter.setViewType("table");
        GenericDownloadReportDTO genericDownloadReportDTO = new GenericDownloadReportDTO();
        List<ByChannelReportDownloadResponseDTO> allItems = new ArrayList<>();
        Map<String,ByChannelReportDownloadResponseDTO> map = new LinkedHashMap<>();
        ActiveConversationChannelWiseResponse activeConversationChannelWiseResponse = (ActiveConversationChannelWiseResponse) reportService.getReport(filter);
        filter.setViewType(viewType);

        if (Objects.nonNull(activeConversationChannelWiseResponse))
            DateUtils.customizeLabels(activeConversationChannelWiseResponse.getDataPoints(), startDate, endDate, groupByType, false);

        // PREPARE Map for Date only
        if (activeConversationChannelWiseResponse != null && activeConversationChannelWiseResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationChannelWiseResponse.getDataPoints())) {
            activeConversationChannelWiseResponse.getDataPoints().forEach(a -> map.put(a.getLabel(), new ByChannelReportDownloadResponseDTO()));
        }
        if (activeConversationChannelWiseResponse != null && activeConversationChannelWiseResponse.getDataPoints() != null && CollectionUtils.isNotEmpty(activeConversationChannelWiseResponse.getDataPoints())) {
            activeConversationChannelWiseResponse.getDataPoints().forEach(a -> {
                if (map.containsKey(a.getLabel())){
                    ByChannelReportDownloadResponseDTO response = new ByChannelReportDownloadResponseDTO();
                    response.setLabel(a.getLabel());
                    a.getSubDataPoints().forEach((field, value) -> setField(response, field, value));
                    map.put(a.getLabel(),response);
                }
            });
        }


        map.forEach((k, v) -> allItems.add(v));

        genericDownloadReportDTO.setData(allItems);
        return genericDownloadReportDTO;
    }
}
