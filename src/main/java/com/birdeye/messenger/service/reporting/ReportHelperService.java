package com.birdeye.messenger.service.reporting;

import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.service.reporting.dto.EarliestMetricDateDTO;

public interface ReportHelperService {

    EarliestMetricDateDTO getEarliestMessageDate(ReportFilter filter);

	Integer getReceivedMessageCountOverTime(ReportFilter filters, ReportService reportService, Integer accountId, Integer businessId) throws Exception;
    
}
