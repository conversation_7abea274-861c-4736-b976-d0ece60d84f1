package com.birdeye.messenger.service.reporting.dto;

import com.birdeye.messenger.dto.PagedResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveConversationByLocResponse extends PagedResponse {

    private String most;
    private String labelForMost;
    private String least;
    private String labelForLeast;
    private Integer totalCount;
    private List<ActiveConversationDataPoint> dataPoints = Collections.emptyList();

}
