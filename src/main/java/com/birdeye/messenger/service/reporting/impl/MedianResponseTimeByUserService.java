package com.birdeye.messenger.service.reporting.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedTDigestPercentiles;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.ext.sro.GetUsersByIdsResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.MedianResponseByUserDataPoints;
import com.birdeye.messenger.service.reporting.dto.MedianResponseTimeByUserResponse;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MedianResponseTimeByUserService implements ReportService {

    @Autowired
    private ElasticSearchExternalService elasticSearchService;

    @Autowired
    private BusinessService businessService;
    
    @Autowired
    private CommonService commonService;
    
    private static final int MStoMin = 60000;

    @Override
    public Object getReport(ReportFilter filter) throws Exception {

        Map<String, Object> templateData = prepareTemplateData(filter);

        ESRequest request = new ESRequest.Builder(new ESRequest()).addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(filter.getAccountId())
                .addTemplateAndDataModel(Constants.Elastic.GET_MEDIAN_RESPONSE_TIME_BY_USER, templateData).addSize(0)
                .build();

        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);

        if (searchResponse.status().getStatus() != 200) {
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }

        MedianResponseTimeByUserResponse response = new MedianResponseTimeByUserResponse();
        List<MedianResponseByUserDataPoints> dataPoints = new ArrayList<>();

        Aggregations aggregations = searchResponse.getAggregations();
        ParsedLongTerms termsAggregations = aggregations.get("med_resp_by_user");

        List<? extends Terms.Bucket> buckets = termsAggregations.getBuckets();

        if (CollectionUtils.isEmpty(buckets)) {
            return response;
        }

        Map<Integer, UserDTO> idToUserMap = new HashMap<>();

        buckets.forEach(bucket -> {
        	ParsedTDigestPercentiles percentilesAggregation = bucket.getAggregations().get("median_response");
            int userId = Integer.parseInt(bucket.getKeyAsString());
            UserDTO user = new UserDTO(userId);
            idToUserMap.put(user.getId(), user);
            if (!ObjectUtils.isEmpty(percentilesAggregation)) {
                // MS to minute conversion
            	Double aDouble = percentilesAggregation.percentile(50d);
                Double thisMonthTotal;
                Double medianResponseTime = null;
                if (aDouble != null && !Double.isNaN(aDouble)) {
                    thisMonthTotal = aDouble / MStoMin;
                    medianResponseTime = MessengerUtil.parseDoubleToOneDecimalAndCeil(thisMonthTotal);
                }
                MedianResponseByUserDataPoints dataPoint = new MedianResponseByUserDataPoints(user, user.getId(), medianResponseTime);
                dataPoints.add(dataPoint);
            }
        });

        List<com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User> usersByIds = businessService.getUsersByIds(new ArrayList<>(idToUserMap.keySet()));

        Map<Integer, UserDTO> userDTOMap = new HashMap<>();

        usersByIds.forEach(fromU -> {
            UserDTO user = idToUserMap.get(fromU.getId());
            user.setEmailId(fromU.getEmailId());
            user.setFirstName(fromU.getFirstName());
            user.setLastName(fromU.getLastName());
            user.setName(fromU.getName());
            userDTOMap.put(user.getId(), user);
        });

        dataPoints.forEach(a -> {
            a.setUser(userDTOMap.get(a.getUserId()));
            String userName = MessengerUtil.buildUserName(a.getUser());
            if (StringUtils.isBlank(userName)) userName = "UNNAMED";
            a.setShortLabel(userName);
            a.setLabel(userName);
        });

        sort(dataPoints, filter.getOrder(), filter.getSortBy());
        if("table".equalsIgnoreCase(filter.getViewType())){
            addEmptyDataForTableView(dataPoints, new ArrayList<>(filter.getUserIds()));
        }
        List<MedianResponseByUserDataPoints> paginated = (List<MedianResponseByUserDataPoints>) MessengerUtil.paginateIt(dataPoints, filter.getPage(), filter.getSize(), response);
        response.setTotalCount(dataPoints.size());
        response.setDataPoints(paginated);

        ParsedCardinality usr_count = aggregations.get("usr_count");
        
        int distinctResponders = (int) usr_count.getValue();
        if (!"table".equalsIgnoreCase(filter.getViewType()) && distinctResponders > 0) {
            response.setTotalPages(((distinctResponders - 1) / filter.getSize()) + 1);
        }
        return response;
    }

    private void addEmptyDataForTableView(List<MedianResponseByUserDataPoints> dataPoints, List<Integer> userIds) {
        Set<Integer> idsForWithDataIsPresent = dataPoints.stream().map(dataPoint -> dataPoint.getUserId()).collect(Collectors.toSet());
        boolean removed = userIds.removeAll(idsForWithDataIsPresent);
        List<com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User> usersByIds = businessService.getUsersByIds(userIds);
        if(removed) {
            usersByIds.forEach(user -> {
                if (Objects.nonNull(user)) {
                    UserDTO userDTO = getUserDTO(user);
                    String userName = MessengerUtil.buildUserName(userDTO);
                    if (StringUtils.isBlank(userName)) userName = "UNNAMED";
                    MedianResponseByUserDataPoints dataPoint = new MedianResponseByUserDataPoints();
                    dataPoint.setShortLabel(userName);
                    dataPoint.setLabel(userName);
                    dataPoint.setResponseTimeMin(null);
                    dataPoint.setUserId(userDTO.getId());
                    dataPoint.setUser(userDTO);
                    dataPoints.add(dataPoint);
                }
            });
        }
    }

    private UserDTO getUserDTO(GetUsersByIdsResponse.User user) {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(user.getId());
        userDTO.setEmailId(user.getEmailId());
        userDTO.setName(user.getName());
        userDTO.setFirstName(user.getFirstName());
        userDTO.setLastName(user.getLastName());
        return userDTO;
    }

    private Map<String, Object> prepareTemplateData(ReportFilter filter) {
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", filter.getAccountId());
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(filter.getBusinessIds()));
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(commonService.getSourceIdsByInboxReportType(ReportType.MEDIAN_RESPONSE_TIME_BY_USER,filter.getSources())));
        templateData.put("startTime", filter.getStartDateInMillis());
        templateData.put("endTime", filter.getEndDateInMillis());
        templateData.put("order", filter.getOrder());
        templateData.put("size", 100000); // set it to a high value since we don't know exact count, also api is not available to return that value.
        if (CollectionUtils.isNotEmpty(filter.getUserIds())) {
            templateData.put("userIds", ControllerUtil.toCommaSeparatedString(filter.getUserIds()));
            templateData.put("size", filter.getUserIds().size());
        }
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        return data;
    }

    private void sort(List<MedianResponseByUserDataPoints> dataPoints, String order, String sortBy) {
        if ("name".equals(sortBy)) {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparing(MedianResponseByUserDataPoints::getLabel));
            } else {
                dataPoints.sort(Comparator.comparing(MedianResponseByUserDataPoints::getLabel).reversed());
            }
        } else {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparingDouble(MedianResponseByUserDataPoints::getResponseTimeMin));
            } else {
                dataPoints.sort(Comparator.comparingDouble(MedianResponseByUserDataPoints::getResponseTimeMin).reversed());
            }
        }
    }

    @Override
    public Enum getReportType() {
        return ReportType.MEDIAN_RESPONSE_TIME_BY_USER;
    }

    @Override
    public Object getReportV2(ReportFilter filter) throws Exception {
        return null;
    }
}
