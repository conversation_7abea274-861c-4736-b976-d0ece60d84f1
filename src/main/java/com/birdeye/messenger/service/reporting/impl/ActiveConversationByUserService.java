package com.birdeye.messenger.service.reporting.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ActiveConversationByUserResponse;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.ext.sro.GetUsersByIdsResponse;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationDataPoint;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActiveConversationByUserService implements ReportService {

    private final ElasticSearchExternalService elasticSearchService;

    private final CommonService commonService;

    @Autowired
    private BusinessService businessService;
    
    
    @Override
    @SuppressWarnings("unchecked")
    public Object getReport(ReportFilter filter) {

        // --------------------- input validation ------------------------------------------------
        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.ACTIVE_CONVERSATION_BY_USER,filter.getSources());
        List<Integer> userIds = filter.getUserIds();
        String order = StringUtils.isBlank(filter.getOrder()) ? "desc" : filter.getOrder();
        String sortBy = StringUtils.isBlank(filter.getSortBy()) ? "count" : filter.getSortBy();
        Integer page = filter.getPage();
        Integer size = filter.getSize();
        Integer accountId = filter.getAccountId();
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(page) || Objects.isNull(size)
                || Objects.isNull(accountId) || Objects.isNull(startDate) || Objects.isNull(endDate) ||
                (!sortBy.equals("name") && !sortBy.equals("count"))) {
            throw new BadRequestException(new ErrorMessageBuilder(ErrorCode.REPORT_VALIDATION_FAILURE, HttpStatus.BAD_REQUEST)
            		.message("Validation Error | Report type - {}, filters", getReportType(),filter.toString()));
        }

        // ----------------- get data from ES -------------------------------------------------
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("order", order);
        templateData.put("size", 100000); // set it to a high value since we don't know exact count, also api is not available to return that value.
        if (CollectionUtils.isNotEmpty(userIds)) {
            templateData.put("userIds", ControllerUtil.toCommaSeparatedString(userIds));
            templateData.put("size", userIds.size());
        }
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_ACTIVE_CONVERSATION_BY_USER, data)
                .addSize(0)
                .build();
        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);

        List<Integer> userIdsList = new ArrayList<>();

        ActiveConversationByUserResponse response = new ActiveConversationByUserResponse();
        if (searchResponse.status().getStatus() != 200) return response;
        Terms by_user = searchResponse.getAggregations().get("by_user");
        if (Objects.nonNull(by_user)) {
        	List<? extends Bucket> buckets = by_user.getBuckets();
            List<ActiveConversationDataPoint> dataPoints = buckets.stream().map(bucket -> {
                ActiveConversationDataPoint dataPoint = new ActiveConversationDataPoint();
                ParsedCardinality convCount = bucket.getAggregations().get("conv_cnt");
                dataPoint.setActiveConversationCount((int) (convCount != null ? convCount.getValue() : 0));
                dataPoint.setUser(new UserDTO(Integer.valueOf(bucket.getKeyAsString())));
                dataPoint.setUserId(Integer.valueOf(bucket.getKeyAsString()));
                userIdsList.add(Integer.valueOf(bucket.getKeyAsString()));
                return dataPoint;
            }).collect(Collectors.toList());
            response.setDataPoints(dataPoints);
        }

        List<com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User> usersByIds = businessService.getUsersByIds(userIdsList);

        Map<Integer, UserDTO> idToUserMap = new HashMap<>();
        usersByIds.forEach(a -> idToUserMap.put(a.getId(), getUserDTO(a)));

        List<ActiveConversationDataPoint> dataPoints = response.getDataPoints();
        dataPoints.forEach(a -> {
            UserDTO userDTO = idToUserMap.get(a.getUser().getId());
            if (Objects.nonNull(userDTO)) {
                String userName = MessengerUtil.buildUserName(userDTO);
                if (StringUtils.isBlank(userName)) userName = "UNNAMED";
                a.setShortLabel(userName);
                a.setLabel(userName);
                a.setUser(userDTO);
            }
        });

        dataPoints = dataPoints.stream().filter(dataPoint -> StringUtils.isNotBlank(dataPoint.getLabel())).collect(Collectors.toList());

        if("table".equalsIgnoreCase(filter.getViewType())){
            addEmptyDataForTableView(dataPoints, new ArrayList<>(userIds));
        }
        response.setTotalCount(dataPoints.size());

        sort(dataPoints, order, sortBy);

        List<ActiveConversationDataPoint> paginated = (List<ActiveConversationDataPoint>) MessengerUtil.paginateIt(dataPoints, page, size, response);
        response.setDataPoints(paginated);
        return response;
    }

    private void addEmptyDataForTableView(List<ActiveConversationDataPoint> dataPoints, List<Integer> userIds) {
        Set<Integer> idsForWithDataIsPresent = dataPoints.stream().map(dataPoint -> dataPoint.getUserId()).collect(Collectors.toSet());
        boolean removed = userIds.removeAll(idsForWithDataIsPresent);
        List<com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User> usersByIds = businessService.getUsersByIds(userIds);
        if(removed) {
            usersByIds.forEach(user -> {
                if (Objects.nonNull(user)) {
                    UserDTO userDTO = getUserDTO(user);
                    String userName = MessengerUtil.buildUserName(userDTO);
                    if (StringUtils.isBlank(userName)) userName = "UNNAMED";
                    ActiveConversationDataPoint dataPoint = new ActiveConversationDataPoint();
                    dataPoint.setShortLabel(userName);
                    dataPoint.setLabel(userName);
                    dataPoint.setActiveConversationCount(0);
                    dataPoint.setUserId(userDTO.getId());
                    dataPoint.setUser(userDTO);
                    dataPoints.add(dataPoint);
                }
            });
        }
    }

    private UserDTO getUserDTO(GetUsersByIdsResponse.User user) {
        UserDTO userDTO = new UserDTO();
        userDTO.setId(user.getId());
        userDTO.setEmailId(user.getEmailId());
        userDTO.setName(user.getName());
        userDTO.setFirstName(user.getFirstName());
        userDTO.setLastName(user.getLastName());
        return userDTO;
    }

    private void sort(List<ActiveConversationDataPoint> dataPoints, String order, String sortBy) {
        if ("name".equals(sortBy)) {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparing(ActiveConversationDataPoint::getLabel));
            } else {
                dataPoints.sort(Comparator.comparing(ActiveConversationDataPoint::getLabel).reversed());
            }
        } else {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparingInt(ActiveConversationDataPoint::getActiveConversationCount));
            } else {
                dataPoints.sort(Comparator.comparingInt(ActiveConversationDataPoint::getActiveConversationCount).reversed());
            }
        }

    }

    @Override
    public Enum getReportType() {
        return ReportType.ACTIVE_CONVERSATION_BY_USER;
    }

    @Override
    public Object getReportV2(ReportFilter filter) throws Exception {
        return null;
    }
}
