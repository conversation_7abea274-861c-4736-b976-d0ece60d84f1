package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.service.reporting.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.reporting.ReportService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardInboxReportOverTime implements ReportService {
	
	private final ThreadPoolTaskExecutor poolExecutor;
    private final List<ReportService> reportServiceList;
    
    @Override
    public Object getReport(ReportFilter filters) throws Exception {
    	DashboardInboxResponseOverTimeDTO  response=new DashboardInboxResponseOverTimeDTO();
		List<Future<?>> futures = new ArrayList<Future<?>>();
		filters.setDashboardInboxReport(true);
		Future<?> activeConversationCountResponse=poolExecutor.submit(() -> getActiveConversationCount(filters,response));
		futures.add(activeConversationCountResponse);
		
		Future<?> receivedMessagesCountResponse=poolExecutor.submit(() -> getReceivedMessagesCount(filters,response));
		futures.add(receivedMessagesCountResponse);
		
		Future<?> medianResponseOverTimeResponse=poolExecutor.submit(() -> getMedianResponseOverTime(filters,response));
		futures.add(medianResponseOverTimeResponse);
		
		// Blocking till all submited task is completed
		for (Future<?> future:futures) {
		   future.get();
		}
		return response;
    }

    private Object getMedianResponseOverTime(ReportFilter filters, DashboardInboxResponseOverTimeDTO response) throws Exception {
    	ReportService reportService = getReportServiceByType(ReportType.MEDIAN_RESPONSE_OVER_TIME);
    	MedianResponseOverTimeDTO medianResponseOverTimeDTO = (MedianResponseOverTimeDTO)reportService.getReport(filters);
        response.setMedianResponseOverTime(medianResponseOverTimeDTO);
        return response;
	}

	private Object getReceivedMessagesCount(ReportFilter filters, DashboardInboxResponseOverTimeDTO response) throws Exception {
    	ReportService reportService = getReportServiceByType(ReportType.RECEIVED_MESSAGES_BY_TIME);
    	ReceivedMessagesByTimeResponse receivedMessagesByTimeResponse = (ReceivedMessagesByTimeResponse)reportService.getReport(filters);
        response.setReceivedMessageCount(receivedMessagesByTimeResponse.getTotalCount());
        return response;
	}

	private Object getActiveConversationCount(ReportFilter filters, DashboardInboxResponseOverTimeDTO response) throws Exception {
    	 ReportService reportService = getReportServiceByType(ReportType.ACTIVE_CONVERSATION_BY_TIME);
    	 ActiveConversationByTimeResponse activeConversationByTimeResponse = (ActiveConversationByTimeResponse)reportService.getReport(filters);
         response.setActiveConversationCount(activeConversationByTimeResponse.getTotalCount().intValue());
         return response;
	}
    private ReportService getReportServiceByType(ReportType type) {
        return reportServiceList.stream()
                .filter(reportService -> type.equals(reportService.getReportType()))
                .findFirst().orElseThrow(() -> new MessengerException(ErrorCode.INVALID_REPORT_TYPE));
    }
	@Override
    public Enum getReportType() {
        return ReportType.DASHBOARD_INBOX_REPORT_OVER_TIME;
    }

	@Override
	public Object getReportV2(ReportFilter filter) throws Exception {
		DashboardInboxResponseOverTimeDTO response = (DashboardInboxResponseOverTimeDTO) getReport(filter);
        MedianResponseOverTimeDTO overTimeResponse = response.getMedianResponseOverTime();
		Timestamp startTS = new Timestamp(filter.getStartDateInMillis());
		Date startDate ;
		Timestamp endTS = new Timestamp(filter.getEndDateInMillis());
		Date endDate = new Date(endTS.getTime());
		long diff = filter.getEndDateInMillis() - filter.getStartDateInMillis() ;
		long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
		GroupByType groupByType = GroupByType.fromString(filter.getInterval());
		if(CollectionUtils.isNotEmpty(overTimeResponse.getDataPoints())) {
			startDate = overTimeResponse.getDataPoints().get(0).getDateTime();
		}else {
			startDate = new Date(startTS.getTime());
		}

		if (startDate.before(new Date(startTS.getTime()))) {
			startDate = new Date(startTS.getTime());
		}
		com.birdeye.messenger.util.DateUtils.customizeLabels(overTimeResponse.getDataPoints(), startDate, endDate, groupByType, false);
		List<MedianResponseOverTimeDataPoint> notNullResponseTime = new ArrayList<>();
		for (MedianResponseOverTimeDataPoint responseTime : response.getMedianResponseOverTime().getDataPoints()) {
			if(responseTime.getResponseTime() != null) {
				notNullResponseTime.add(responseTime);
			}
		}
		overTimeResponse.setDataPoints(notNullResponseTime);
		overTimeResponse.setDateDiff(days);
		return response;
	}
}
