package com.birdeye.messenger.service.reporting.dto;

import java.util.Collections;
import java.util.List;

import com.birdeye.messenger.dto.PagedResponse;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MedianResponseTimeByUserResponse extends PagedResponse {
	
	private Integer totalCount;
	
    private List<MedianResponseByUserDataPoints> dataPoints = Collections.emptyList();
    
}
