package com.birdeye.messenger.service.reporting.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActiveConversationChannelWiseResponse{
    private Integer thisMonth;
    private Long lastOneYear;
    private String groupByType;
    private Long totalCount;
    private Long dateDiff;
    private List<ActiveConversationChannelWiseDataPoint> dataPoints = Collections.emptyList();
}
