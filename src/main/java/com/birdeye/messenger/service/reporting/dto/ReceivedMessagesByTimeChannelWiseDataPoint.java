package com.birdeye.messenger.service.reporting.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReceivedMessagesByTimeChannelWiseDataPoint extends ReportDataPoint{
    private Map<String,Integer> subDataPoints=new HashMap<>();
}
