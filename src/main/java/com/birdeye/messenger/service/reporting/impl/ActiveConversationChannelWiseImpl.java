package com.birdeye.messenger.service.reporting.impl;

import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.service.reporting.ReportService;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.ParsedDateRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationChannelWiseResponse;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationChannelWiseDataPoint;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActiveConversationChannelWiseImpl implements ReportService{

    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;
    @Override
    public Object getReport(ReportFilter filter) {
        AtomicInteger totalCount= new AtomicInteger(0);
        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.ACTIVE_CONVERSATION_CHANNEL_WISE,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            throw new MessengerException(getReportType() + ": invalid input " + filter.toString());
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_ACTIVE_CONVERSATION_CHANNEL_WISE, data)
                .addSize(0)
                .build();
        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        ActiveConversationChannelWiseResponse response = new ActiveConversationChannelWiseResponse();
        if (searchResponse.status().getStatus() != 200) return response;
        response.setTotalCount(searchResponse.getHits().getTotalHits().value);
        response.setGroupByType(filter.getInterval());

        Aggregations aggregations = searchResponse.getAggregations();
        ParsedDateHistogram active_conv = aggregations.get("active_conv");
        if (Objects.nonNull(active_conv)) {
            List<? extends Histogram.Bucket> buckets = active_conv.getBuckets();
            List<ActiveConversationChannelWiseDataPoint> dataPoints = buckets.stream().map(bucket -> {
                String dateBucket = bucket.getKeyAsString();
                String[] parts = dateBucket.split("(?=T)");
                dateBucket = parts[0];
//                    String part2 = parts[1];
                LocalDate dateTime = LocalDate.parse(dateBucket);
                DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                String label = dateTime.format(formatter);
                ActiveConversationChannelWiseDataPoint dataPoint = new ActiveConversationChannelWiseDataPoint();
                if(CollectionUtils.isEmpty(filter.getSources())){
                    insertDefaultChannels(dataPoint);
                }else if(org.apache.commons.collections.CollectionUtils.isNotEmpty(sourceIds)){
                    Map<String,Integer> receivedMessageCountMap=new HashMap<>();
                    for(Integer source: sourceIds){
                        receivedMessageCountMap.put(getKey(source),0);
                    }
                    dataPoint.setSubDataPoints(receivedMessageCountMap);
                }
                ParsedLongTerms source_aggregation = bucket.getAggregations().get("source_aggregation");
                if(Objects.nonNull(source_aggregation) && CollectionUtils.isNotEmpty(source_aggregation.getBuckets())) {
                    Long totalDistinctConversationCount = 0l;
                    for(Bucket parsed_bucket:source_aggregation.getBuckets()) {
                        ParsedCardinality distinct_conv = parsed_bucket.getAggregations().get("distinct_conv");
                        int count=(int)(distinct_conv != null ? distinct_conv.getValue() : 0);
                        totalDistinctConversationCount = totalDistinctConversationCount + count;
                        dataPoint.getSubDataPoints().putIfAbsent(getKey(((Long) parsed_bucket.getKey()).intValue()), 0);
                        dataPoint.getSubDataPoints().put(getKey(((Long) parsed_bucket.getKey()).intValue()),dataPoint.getSubDataPoints().get(getKey(((Long) parsed_bucket.getKey()).intValue()))+ count);
                    }
                    dataPoint.setTotalCount(totalDistinctConversationCount);
                    totalCount.getAndAdd(Math.toIntExact(totalDistinctConversationCount));
                }
                dataPoint.setLabel(label);
                dataPoint.setShortLabel(label);
                return dataPoint;
            }).collect(Collectors.toList());
            response.setDataPoints(dataPoints);
        }

        ParsedDateRange last_1_month = aggregations.get("last_1_month");
        if (Objects.nonNull(last_1_month)) {
            List<? extends Range.Bucket> buckets = last_1_month.getBuckets();
            buckets.stream().findAny().ifPresent(bucket -> {
                ParsedCardinality conv_cnt = bucket.getAggregations().get("conv_cnt");
                response.setThisMonth((int)(conv_cnt!=null?conv_cnt.getValue():0));
            });
        }

        if (Boolean.TRUE.equals(filter.isLastOneYear())) {
            ParsedCardinality activeConvInLastOneYear = aggregations.get("last_one_year");
            response.setLastOneYear((Long)(activeConvInLastOneYear!=null?activeConvInLastOneYear.getValue():0));

        }
        response.setTotalCount((long)totalCount.get());
        return response;

    }

    private String getKey(int source) {
        if (com.birdeye.messenger.enums.Source.WEB_CHAT.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.WEB_CHAT_BIRDEYE_PROFILE.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_RECEIVE.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_BIRDEYE_PROFILE_RECEIVE.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_SEND.getSourceId().equals(source)
                || com.birdeye.messenger.enums.Source.LIVE_CHAT_BIRDEYE_PROFILE_SEND.getSourceId().equals(source)) {
            return com.birdeye.messenger.enums.Source.WEB_CHAT.name();
        }
        return com.birdeye.messenger.enums.Source.getValue(source).name();
    }

    @Override
    public Enum getReportType() {
        return ReportType.ACTIVE_CONVERSATION_CHANNEL_WISE;
    }

    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filters.getEndDateInMillis() - filters.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        ActiveConversationChannelWiseResponse activeConversationByTimeResponse = (ActiveConversationChannelWiseResponse) getReport(filters);
        List<ActiveConversationChannelWiseDataPoint> activeConversationByTimeDataPoints = activeConversationByTimeResponse.getDataPoints();
        DateUtils.customizeLabels(activeConversationByTimeDataPoints, startDate, endDate, groupByType, false);
        activeConversationByTimeResponse.setDateDiff(days);
        return  activeConversationByTimeResponse;
    }

    private void insertDefaultChannels(ActiveConversationChannelWiseDataPoint dataPoint){
        Map<String,Integer> activeConversationCountMap=new HashMap<>();
        activeConversationCountMap.put(Source.APPLE.name(),0);
        activeConversationCountMap.put(Source.APPOINTMENT.name(),0);
        activeConversationCountMap.put(Source.CONTACT_US.name(),0);
        activeConversationCountMap.put(Source.EMAIL.name(),0);
        activeConversationCountMap.put(Source.FACEBOOK.name(),0);
        activeConversationCountMap.put(Source.INSTAGRAM.name(),0);
        activeConversationCountMap.put(Source.TWITTER.name(),0);
        activeConversationCountMap.put(Source.GOOGLE.name(),0);
        activeConversationCountMap.put(Source.SMS.name(),0);
        activeConversationCountMap.put(Source.VOICE_CALL.name(),0);
        activeConversationCountMap.put(Source.WEB_CHAT.name(),0);
        dataPoint.setSubDataPoints(activeConversationCountMap);
    }
}
