package com.birdeye.messenger.service.reporting.dto;

import com.birdeye.messenger.annotations.DynamicExcelCell;
import com.birdeye.messenger.dto.DynamicCellDto;
import com.birdeye.messenger.enums.CellType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.Transient;

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReportDataPoint {

    private String label;

    private String shortLabel;

    @DynamicExcelCell(order=2)
    private DynamicCellDto dynamicLabel;

    private String startDate;

    private String endDate;

    @Transient
    protected long totalCount = 0L;

    public void setLabel(Object label) {
        this.label = (String) label;
        // also prepare dynamic cell label when preparing label
        if(this.dynamicLabel==null){
            this.dynamicLabel = new DynamicCellDto("label", this.label, CellType.TEXT);
        }else{
            this.dynamicLabel.setValue(label);
        }
    }

}
