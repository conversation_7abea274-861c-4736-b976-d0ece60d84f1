package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.ParsedDateRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationByTimeResponse;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationDataPoint;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActiveConversationByTimeService implements ReportService {

    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;
    
    @Override
    public Object getReport(ReportFilter filter) {
        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.ACTIVE_CONVERSATION_BY_TIME,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            throw new MessengerException(getReportType() + ": invalid input " + filter.toString());
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_ACTIVE_CONVERSATION_BY_TIME, data)
                .addSize(0)
                .build();
        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        ActiveConversationByTimeResponse response = new ActiveConversationByTimeResponse();
        if (searchResponse.status().getStatus() != 200) return response;
        response.setTotalCount(searchResponse.getHits().getTotalHits().value);
        response.setGroupByType(filter.getInterval());

        Aggregations aggregations = searchResponse.getAggregations();
        ParsedDateHistogram active_conv = aggregations.get("active_conv");
        if (Objects.nonNull(active_conv)) {
            List<? extends Histogram.Bucket> buckets = active_conv.getBuckets();
            List<ActiveConversationDataPoint> dataPoints = buckets.stream().map(bucket -> {
                String dateBucket = bucket.getKeyAsString();
                String[] parts = dateBucket.split("(?=T)");
                dateBucket = parts[0];
//                    String part2 = parts[1];
                LocalDate dateTime = LocalDate.parse(dateBucket);
                DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
                String label = dateTime.format(formatter);
                ActiveConversationDataPoint dataPoint = new ActiveConversationDataPoint();
                ParsedCardinality distinct_conv = bucket.getAggregations().get("distinct_conv");
                dataPoint.setActiveConversationCount((int) (distinct_conv != null ? distinct_conv.getValue() : 0));
                dataPoint.setLabel(label);
                dataPoint.setShortLabel(label);
                return dataPoint;
            }).collect(Collectors.toList());
            response.setDataPoints(dataPoints);
        }

        ParsedDateRange last_1_month = aggregations.get("last_1_month");
        if (Objects.nonNull(last_1_month)) {
        	List<? extends Range.Bucket> buckets = last_1_month.getBuckets();
            buckets.stream().findAny().ifPresent(bucket -> {
            	ParsedCardinality conv_cnt = bucket.getAggregations().get("conv_cnt");
            	response.setThisMonth((int)(conv_cnt!=null?conv_cnt.getValue():0));
            });
        }

        if (Boolean.TRUE.equals(filter.isLastOneYear())) {
        	ParsedCardinality activeConvInLastOneYear = aggregations.get("last_one_year");
        	response.setLastOneYear((int)(activeConvInLastOneYear!=null?activeConvInLastOneYear.getValue():0));
        	
        }
        List<ActiveConversationDataPoint> dataPoints = response.getDataPoints();
        int zeroCount = 0;
        Long totalCount=0l;
        for(ActiveConversationDataPoint dataPoint : dataPoints) {
        	if(dataPoint.getActiveConversationCount()!=null) {
        		if(dataPoint.getActiveConversationCount().equals(0)) {
                	zeroCount++;
                }else {
                	totalCount+=dataPoint.getActiveConversationCount();
                }	
        	}
            
        }
        response.setTotalCount(totalCount);
        response.setDataPoints(dataPoints);
        return response;

    }

    @Override
    public Enum getReportType() {
        return ReportType.ACTIVE_CONVERSATION_BY_TIME;
    }

    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filters.getEndDateInMillis() - filters.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        ActiveConversationByTimeResponse activeConversationByTimeResponse = (ActiveConversationByTimeResponse) getReport(filters);
        List<ActiveConversationDataPoint> activeConversationByTimeDataPoints = activeConversationByTimeResponse.getDataPoints();
        DateUtils.customizeLabels(activeConversationByTimeDataPoints, startDate, endDate, groupByType, false);
        activeConversationByTimeResponse.setDateDiff(days);
        return  activeConversationByTimeResponse;
    }
}
