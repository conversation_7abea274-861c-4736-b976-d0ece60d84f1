package com.birdeye.messenger.service.reporting.impl;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.birdeye.messenger.enums.GroupByType;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationByTimeResponse;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationDataPoint;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.TimeZoneUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.ParsedDateHistogram;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesByTimeResponse;
import com.birdeye.messenger.service.reporting.dto.ReceivedMessagesDataPoint;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReceivedMessageByTimeServiceImpl implements ReportService {

	private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    public ReceivedMessagesByTimeResponse getReport(ReportFilter filter) {
        return buildESRequestForReceiveMessages(filter);
    }

    @Override
    public Enum getReportType() {
        return ReportType.RECEIVED_MESSAGES_BY_TIME;
    }

    private ReceivedMessagesByTimeResponse buildESRequestForReceiveMessages(ReportFilter filter) {

        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.RECEIVED_MESSAGES_BY_TIME,filter.getSources());
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        String interval = filter.getInterval();
        Integer accountId = filter.getAccountId();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(startDate)
                || Objects.isNull(endDate) || StringUtils.isBlank(interval) || Objects.isNull(accountId)) {
            log.error(getReportType() + ": validation failed for input {}", filter);
            return null;
        }

        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("interval", interval);
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        if (lastOneYear) {
            templateData.put("lastOneYear", lastOneYear);
            Long fromEpocTime = ZonedDateTime.now(ZoneId.of("America/Los_Angeles")).withHour(0)
                    .withSecond(0).withMinute(0).withDayOfMonth(1).toInstant().toEpochMilli();
            templateData.put("fromEpocTime", fromEpocTime);
        }
        templateData.put("isDashboardInboxReport", filter.isDashboardInboxReport());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));

        TimeZoneUtil.addTimeZoneId(filter.getAccountTimeZoneId(), templateData);
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_RECEIVE_MESSAGE_BY_TIME, data)
                .addSize(0)
                .build();

        SearchResponse searchResult = elasticSearchService.getSearchResult(request);

        if (searchResult.status().getStatus() != 200) {
            throw new MessengerException("Failed to fetch Received Messages Report");
        } else {
            ReceivedMessagesByTimeResponse response = new ReceivedMessagesByTimeResponse();
            response.setTotalCount(Math.toIntExact(searchResult.getHits().getTotalHits().value));
            if(filter.isDashboardInboxReport()) {
            	return response;
            }
            response.setGroupByType(filter.getInterval());

            Aggregations aggregations = searchResult.getAggregations();
            ParsedDateHistogram receive_message = aggregations.get("receive_message");
            if (Objects.nonNull(receive_message)) {
            	List<? extends Histogram.Bucket> buckets = receive_message.getBuckets();
            	List<ReceivedMessagesDataPoint> dataPoints = buckets.stream().map(bucket -> {
            		DateTimeFormatter formatter =  DateTimeFormatter.ofPattern("MM/dd/yyyy");
            		String dateKeyAsString = bucket.getKeyAsString();
                    String[] parts = dateKeyAsString.split("(?=T)");
                    dateKeyAsString = parts[0];
//                    String part2 = parts[1];
                    LocalDate date = LocalDate.parse(dateKeyAsString);
//                    LocalDateTime dateTime = LocalDateTime.parse(dateBucket, formatter);
//                    ZonedDateTime time = dateTime.atZone(ZoneId.of(filter.getAccountTimeZoneId()));
//            		LocalDate date = instant.toLocalDate();
            		String label = date.format(formatter);
            		int count = (int) bucket.getDocCount();
            		ReceivedMessagesDataPoint dataPoint = new ReceivedMessagesDataPoint();
            		dataPoint.setReceivedMessageCount(count);
            		dataPoint.setLabel(label);
            		dataPoint.setShortLabel(label);
            		return dataPoint;
            	}).collect(Collectors.toList());
            	response.setDataPoints(dataPoints);
            }
            
            Range last_1_month = aggregations.get("last_1_month");
            if (Objects.nonNull(last_1_month)) {
                List<? extends Range.Bucket> buckets = last_1_month.getBuckets();
                buckets.stream().findAny().ifPresent(bucket -> response.setThisMonth((int) bucket.getDocCount()));
            }

            if (Objects.nonNull(searchResult.getHits())) {
                response.setLastOneYear(searchResult.getHits().getTotalHits().value);
            }
            response.setDataPoints(response.getDataPoints());
            return response;
        }
    }

    @Override
    public Object getReportV2(ReportFilter filters) throws Exception {
        Timestamp startTS = new Timestamp(filters.getStartDateInMillis());
        Date startDate = new Date(startTS.getTime());
        Timestamp endTS = new Timestamp(filters.getEndDateInMillis());
        Date endDate = new Date(endTS.getTime());
        long diff = filters.getEndDateInMillis() - filters.getStartDateInMillis() ;
        long days = TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS);
        GroupByType groupByType = GroupByType.fromString(filters.getInterval());
        ReceivedMessagesByTimeResponse receivedMessagesByTimeResponse =  getReport(filters);
        List<ReceivedMessagesDataPoint> receivedMessagesByTimeResponseDataPoints = receivedMessagesByTimeResponse.getDataPoints();
        DateUtils.customizeLabels(receivedMessagesByTimeResponseDataPoints, startDate, endDate, groupByType, false);
        receivedMessagesByTimeResponse.setDateDiff(days);
        return  receivedMessagesByTimeResponse;
    }
}
