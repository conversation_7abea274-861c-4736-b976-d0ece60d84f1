package com.birdeye.messenger.service.reporting.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms.Bucket;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ReportFilter;
import com.birdeye.messenger.dto.UserLoginMessage;
import com.birdeye.messenger.enums.ReportType;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.reporting.ReportService;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationByLocResponse;
import com.birdeye.messenger.service.reporting.dto.ActiveConversationDataPoint;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class ActiveConversationByLocService implements ReportService {
    private final BusinessService businessService;
    private final ElasticSearchExternalService elasticSearchService;
    private final CommonService commonService;

    @Override
    @SuppressWarnings("unchecked")
    public Object getReport(ReportFilter filter) {

        // --------------------- input validation ------------------------------------------------
        List<Integer> businessIds = filter.getBusinessIds();
        List<Integer> sourceIds = commonService.getSourceIdsByInboxReportType(ReportType.ACTIVE_CONVERSATION_BY_LOCATION,filter.getSources());
        String order = StringUtils.isBlank(filter.getOrder()) ? "desc" : filter.getOrder();
        String sortBy = StringUtils.isBlank(filter.getSortBy()) ? "count": filter.getSortBy();
        Integer page = filter.getPage();
        Integer size = filter.getSize();
        Integer accountId = filter.getAccountId();
        Long startDate = filter.getStartDateInMillis();
        Long endDate = filter.getEndDateInMillis();
        boolean lastOneYear = filter.isLastOneYear();

        if (CollectionUtils.isEmpty(businessIds) || Objects.isNull(page) || Objects.isNull(size)
                || Objects.isNull(accountId) || Objects.isNull(startDate) || Objects.isNull(endDate) ||
                (!sortBy.equals("name") && !sortBy.equals("count"))) {
            log.error(getReportType() + ": validation for input", filter);
            throw new MessengerException(getReportType() + ": invalid input " + filter.toString());
        }

        // ----------------- get data from ES -------------------------------------------------
        Map<String, Object> templateData = new HashMap<>();
        templateData.put("accountId", accountId);
        templateData.put("businessIds", ControllerUtil.toCommaSeparatedString(businessIds));
        templateData.put("sourceIds", ControllerUtil.toCommaSeparatedString(sourceIds));
        templateData.put("startTime", startDate);
        templateData.put("endTime", endDate);
        templateData.put("order", order);
        templateData.put("size", businessIds.size());
        templateData.put("customerType",filter.getCustomerType());
        templateData.put("surveyScores",ControllerUtil.toCommaSeparatedString(filter.getSurveyScores()));
        templateData.put("experienceScores",ControllerUtil.toCommaSeparatedString(filter.getExperienceScores()));
        Map<String, Object> data = new HashMap<>();
        data.put("data", templateData);
        ESRequest request = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.MESSAGE_INDEX)
                .addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_ACTIVE_CONVERSATION_BY_LOC, data)
                .addSize(0)
                .build();
        SearchResponse searchResponse = elasticSearchService.getSearchResult(request);
        ActiveConversationByLocResponse response = new ActiveConversationByLocResponse();
        if (searchResponse.status().getStatus() != 200) return response;
        UserLoginMessage userLoginMessage = new UserLoginMessage();
        userLoginMessage.setBusinessId(accountId);
        userLoginMessage.setBusinessIds(businessIds);
        Map<Integer, BizLite> businessDataForMessenger = businessService.getBizLite(businessIds, accountId);
        response.setTotalCount((int) searchResponse.getHits().getTotalHits().value);
        Terms aggregations = searchResponse.getAggregations().get("by_location");
        if (Objects.nonNull(aggregations)) {
            List<? extends Bucket> buckets = aggregations.getBuckets();
            List<ActiveConversationDataPoint> dataPoints = buckets.stream().map(bucket -> {
                ActiveConversationDataPoint dataPoint = new ActiveConversationDataPoint();
                ParsedCardinality convCount = bucket.getAggregations().get("conv_cnt");
                dataPoint.setActiveConversationCount((int) (convCount != null ? convCount.getValue():0));
                String businessId = bucket.getKeyAsString();
                BizLite bizLite = businessDataForMessenger.get(Integer.parseInt(businessId));
                if (Objects.nonNull(bizLite)) {
                    dataPoint.setShortLabel(bizLite.getDisplayName());
                    dataPoint.setLocationId(bizLite.getLocationId());
                    dataPoint.setBusinessId(bizLite.getId());
                    dataPoint.setLabel(bizLite.getDisplayName());
                }
                return dataPoint;
            }).filter(dataPoint -> StringUtils.isNotBlank(dataPoint.getLabel())).collect(Collectors.toList());
            response.setDataPoints(dataPoints);
        }
        
        if(!CollectionUtils.isEmpty(response.getDataPoints())) {
        	response.setTotalCount((int) response.getDataPoints().stream().map(d -> d.getActiveConversationCount()).reduce(0, Integer::sum));
        }
        List<ActiveConversationDataPoint> dataPoints = response.getDataPoints();

        if (CollectionUtils.isNotEmpty(dataPoints)) {
            int mostIndex;
            int leastIndex;
            if (order.equals("asc")) {
                mostIndex = dataPoints.size() - 1;
                leastIndex = 0;
            } else {
                mostIndex = 0;
                leastIndex = dataPoints.size() - 1;
            }

            response.setLabelForMost(dataPoints.get(mostIndex).getLabel());
            response.setMost(String.valueOf(dataPoints.get(mostIndex).getActiveConversationCount()));

            response.setLabelForLeast(dataPoints.get(leastIndex).getLabel());
            response.setLeast(String.valueOf(dataPoints.get(leastIndex).getActiveConversationCount()));

        }

        if("table".equalsIgnoreCase(filter.getViewType())){
            addEmptyDataForTableView(dataPoints, new ArrayList<>(businessIds), businessDataForMessenger);
        }
        sort(dataPoints, order, sortBy);
        List<ActiveConversationDataPoint> paginated = (List<ActiveConversationDataPoint>) MessengerUtil.paginateIt(dataPoints, page, size, response);
        response.setDataPoints(paginated);
        return response;

    }

    private void addEmptyDataForTableView(List<ActiveConversationDataPoint> dataPoints, List<Integer> businessIds, Map<Integer, BizLite> businessData) {
        Set<Integer> idsForWithDataIsPresent = dataPoints.stream().map(dataPoint -> dataPoint.getBusinessId()).collect(Collectors.toSet());
        boolean removed = businessIds.removeAll(idsForWithDataIsPresent);
        if(removed) {
            businessIds.forEach(businessId -> {
                BizLite bizLite = businessData.get(businessId);
                if (Objects.nonNull(bizLite)) {
                    ActiveConversationDataPoint dataPoint = new ActiveConversationDataPoint();
                    dataPoint.setShortLabel(bizLite.getDisplayName());
                    dataPoint.setActiveConversationCount(0);
                    dataPoint.setLocationId(bizLite.getLocationId());
                    dataPoint.setBusinessId(bizLite.getId());
                    dataPoint.setLabel(bizLite.getDisplayName());
                    dataPoints.add(dataPoint);
                }
            });
        }
    }

    private void sort(List<ActiveConversationDataPoint> dataPoints, String order, String sortBy) {
        if ("name".equals(sortBy)) {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparing(ActiveConversationDataPoint::getLabel));
            } else {
                dataPoints.sort(Comparator.comparing(ActiveConversationDataPoint::getLabel).reversed());
            }
        } else {
            if ("asc".equals(order)) {
                dataPoints.sort(Comparator.comparingInt(ActiveConversationDataPoint::getActiveConversationCount));
            } else {
                dataPoints.sort(Comparator.comparingInt(ActiveConversationDataPoint::getActiveConversationCount).reversed());
            }
        }

    }

    @Override
    public Enum getReportType() {
        return ReportType.ACTIVE_CONVERSATION_BY_LOCATION;
    }

    @Override
    public Object getReportV2(ReportFilter filter) throws Exception {
        return null;
    }
}
