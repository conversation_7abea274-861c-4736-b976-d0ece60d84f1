package com.birdeye.messenger.service.reporting.dto;

import com.birdeye.messenger.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActiveConversationChannelWiseDataPoint extends ReportDataPoint{
    private UserDTO user;

    private Integer userId;

    private Integer locationId;

    private Map<String,Integer> subDataPoints=new HashMap<>();

    private Integer businessId;
}
