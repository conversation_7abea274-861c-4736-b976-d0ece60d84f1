package com.birdeye.messenger.service.reporting;

import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.dto.report.DownloadReportFilter;
import com.birdeye.messenger.dto.report.GenericDownloadReportDTO;

public interface DownloadReportService {


    GenericDownloadReportDTO downloadReportByTime(DownloadReportFilter downloadFilter) throws Exception;

    GenericDownloadReportDTO downloadReportOverLocation(DownloadReportFilter downloadFilter, Integer startIndex, Integer pageSize) throws Exception;

    GenericDownloadReportDTO downloadReportByUser(DownloadReportFilter downloadFilter, Integer startIndex, Integer pageSize) throws Exception;

	GenericDownloadReportDTO downloadRobinUnansweredQuestionsV1(DownloadReportFilter downloadFilter, Integer startIndex,
			Integer pageSize);

	GenericDownloadReportDTO downloadRobinUnansweredQuestions(@NotNull DownloadReportFilter downloadFilter,
			Integer startIndex, Integer pageSize);

	GenericDownloadReportDTO downloadRobinResponseByTimeReports(DownloadReportFilter downloadFilter) throws Exception;

	GenericDownloadReportDTO downloadMessageReceivedChannelWiseReportsExcel(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception;

	GenericDownloadReportDTO downloadMessageReceivedChannelWiseReportsPdf(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception;

	GenericDownloadReportDTO downloadActiveConversationChannelWiseReportsExcel(DownloadReportFilter downloadFilter,Integer startIndex,Integer pageSize) throws Exception;


}
