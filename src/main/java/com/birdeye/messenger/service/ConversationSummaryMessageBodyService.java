/**
 * 
 */
package com.birdeye.messenger.service;

import com.birdeye.messenger.dao.entity.ConversationSummaryMessageBody;
import com.birdeye.messenger.dto.ConversationSummaryMessageBodyRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;

/**
 * <AUTHOR>
 *
 */
public interface ConversationSummaryMessageBodyService {

    String formatMessageBodyForActivityType(ActivityType activityType, MessageDocument messageDocument,
            ConversationSummaryMessageType conversationSummaryMessageType);

    ConversationSummaryMessageBody saveConversationSummaryMessageBody(
            ConversationSummaryMessageBodyRequest conversationSummaryMessageBodyRequest);

    void deleteConversationSummaryMessageBody(ActivityType activityType);

    ConversationSummaryMessageBody getConversationSummaryMessageBodyByMessageType(ActivityType activityType);

}
