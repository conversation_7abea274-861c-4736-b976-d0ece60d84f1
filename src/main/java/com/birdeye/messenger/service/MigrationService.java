package com.birdeye.messenger.service;

import java.util.Date;
import java.util.List;

import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import com.birdeye.messenger.dao.entity.BusinessChatWidgetConfig;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.report.ResponseTimeMigrationDto;
import com.birdeye.messenger.sro.BusinessMoveEvent;
import com.birdeye.messenger.sro.Conversation;
import com.birdeye.messenger.sro.CreateDeafaultCreationRequest;


public interface MigrationService {

    Integer migrate(MessengerFilter messengerFilter);

    void migrateByMessengerContactId(Integer contactId, Boolean migrateSMS);

    void migrateImageUrl(Integer messageId);

    void migrateImageUrl();

	void updateAndMigrateContactDataToES(Integer messengerContactId);

	void migrateContactDataToES();

	void migrateContactDataToES(MessengerFilter messengerFilter);

    void migrateLocationWidgets();

    void moveInboxData(BusinessMoveEvent businessMoveEvent);

	void migrateMedia(ImageUploadToS3Response imageUploadToS3Response);

	void validateRequest(Conversation conversation);

	void migratePodiumData(Conversation conversation);

	void migrateLastRecievedMessageSource(List<Integer> mcids);

    void publishImageUploadRequest(Integer businessId, Integer messageId, String imageUrl, MessageDocument.Channel channel);

    String publishEventForLastReceivedMessageSource(MigrateLastReceivedMessageSourceRequest request);

	String publishEventToMigrateBusinessIdInOldMessageDocs(@NotNull @Valid MigrateBusinessIdInOldMessageDocumentRequest request);

	void migrateBusinessIdInOldMessageDocs(MessengerFilter messengerFilter);

	void migrateWidgetConfiguation(Integer widgetId);

	void migrateLeadSources(Integer mcId);

	void migrateLeadSourcesFromDate(Date start);
    void migrateResponseTime(MessengerContact contact);

    void initiateResponseTimeMigration(ResponseTimeMigrationDto responseTimeMigrationDto);

	void publishEventToMergeDuplicateContacts(Integer businessId, Integer customerId,Integer pageSize);

	void mergeDuplicateContacts(MergeDuplicateContactsEvent event);

	void publishEventToMigrateFbAndGBMId(Integer businessId, Integer mcId, Integer pageSize);

	void updateFbAndGBMId(UpdateFBAndGBMIdEvents event);
	void migrateWidgets();
	void migrateWidgetToMultiLocWidget(BusinessChatWidgetConfig bc);
//	void migrateWidgetToLocation();

	void createDefaultWidget(List<CreateDeafaultCreationRequest> request);

	void businessUpgradeDowngradeMoveEvent(BusinessMigrationBatchDto event);
	
	void migrateCsvDataToES(ImportCsvMessage messengerFilter) throws Exception;
}
