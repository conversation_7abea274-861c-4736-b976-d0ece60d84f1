package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.MigrateHiddenCampaignDto;

import jakarta.transaction.Transactional;
import java.util.List;

public interface CampaignDataArchiveService{

    void migrateCampaignData();

    void migrateHiddenCampaignDocsMessageDocumentsAndDbData(List<Integer> accounts);

    @Transactional
    void archiveMessageData(MigrateHiddenCampaignDto migrateHiddenCampaignDto);
    
    void archiveCampaignDataJob(MigrateHiddenCampaignDto request);
    
    void archiveCampaignDataJobDeleteConversation(MigrateHiddenCampaignDto request);
}
