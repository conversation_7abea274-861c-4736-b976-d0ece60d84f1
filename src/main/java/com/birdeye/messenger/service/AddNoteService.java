package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.SendMessageDTO;

import java.util.List;
import java.util.Map;

public interface AddNoteService {
	public ConversationDTO saveNote(SendMessageDTO sendMessageDTO);

    void deleteMessengerNoteUsingIds(List<Integer> id);

    void deleteConversationNotes(Map<String, List<Integer>> result, Integer customerId);
}
