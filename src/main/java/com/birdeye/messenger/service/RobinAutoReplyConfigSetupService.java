package com.birdeye.messenger.service;

import com.birdeye.messenger.dto.robin.GetRobinReplyConfigResponse;
import com.birdeye.messenger.dto.robin.RobinAutoReplySetupRequest;

import java.util.List;
import java.util.Map;

public interface RobinAutoReplyConfigSetupService {

    void createUpdateRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest);

    void deleteRobinAutoReplyConfiguration(RobinAutoReplySetupRequest robinAutoReplySetupRequest);

    Map<Integer, GetRobinReplyConfigResponse> getAllRobinAutoReplyConfig(RobinAutoReplySetupRequest request);

    List<GetRobinReplyConfigResponse> getEffectiveRobinAutoReplyConfig(RobinAutoReplySetupRequest request);

    void evictRobinAutoReplyConfigCache(Integer businessId);

}
