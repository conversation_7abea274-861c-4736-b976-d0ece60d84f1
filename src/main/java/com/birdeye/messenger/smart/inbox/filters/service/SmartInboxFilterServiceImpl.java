/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.service;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.dto.UpdateTeamUserEvent;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UserEvent;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.SmartInboxFilterException;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.service.RedisLockService;
import com.birdeye.messenger.smart.inbox.filters.dao.SmartInboxFilterDAO;
import com.birdeye.messenger.smart.inbox.filters.document.SmartInboxFilter;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest.ConversationFilter;
import com.birdeye.messenger.smart.inbox.filters.dto.request.DeleteSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.GetSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.response.ConversationFiltersResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.CreateSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.GetSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.SmartInboxOwnershipResponse;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterSharedType;
import com.birdeye.messenger.sro.DeleteTeamEvent;
import com.birdeye.messenger.util.JSONUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class SmartInboxFilterServiceImpl implements SmartInboxFilterService {

    private final RedisLockService redisLockService;

    private final UserService userService;

    private final SmartInboxFilterDAO smartInboxFilterDAO;

    private final RedisHandler redisHandler;

    /*
     * Saves smart inbox filter
     * Obtained lock to prevent concurrency issue with same name filter for that
     * account
     */
    @Override
    public CreateSmartInboxFilterResponse saveSmartInboxFilter(
            CreateOrUpdateSmartInboxFilterRequest createSmartInboxFilterRequest) {
        Integer accountId = createSmartInboxFilterRequest.getAccountId();
        Integer userId = createSmartInboxFilterRequest.getCreatedBy();
        String name = createSmartInboxFilterRequest.getName();
        String requestedSource = createSmartInboxFilterRequest.getRequestedSource();

        Optional<Lock> lockOptional = Optional.empty();
        try {
            String lockKey = "SMART_INBOX_FILTER_LOCK:" + accountId + "-" + name;
            lockOptional = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);
            if (!lockOptional.isPresent()) {
                log.info("unable to acquire lock for lockKey : {}", lockKey);
                throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_FILTER_ACQUIRE_LOCK);
            }
            UserDTO userDTO = userService.getUserDTO(userId);

            checkIfFilterExistsWithSameName(name, accountId, null);

            SmartInboxFilter smartInboxFilter = createSmartInboxFilterRequest.getSmartInboxFilter(userDTO);
            String id = smartInboxFilterDAO.saveSmartInboxFilter(smartInboxFilter);
            log.info("saveSmartInboxFilter successfully id : {}", id);

            smartInboxFilterDAO.evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(
                    smartInboxFilter, accountId,
                    userId);
            /*
             * Hack to support filters for Android device, due to NumberFormatException
             * Will be removed once mobile team accomodates the changes for
             * ES ID support BIRDEYE-123517
             */
            if ("mobile".equals(requestedSource)) {
                id = String.valueOf(id.hashCode());
            }
            return new CreateSmartInboxFilterResponse(id, name, smartInboxFilter.getCreatedAt(),
                    createSmartInboxFilterRequest.getIsDefault(), JSONUtils.fromJSON(
                            JSONUtils.toJSON(smartInboxFilter.getFilters()), ConversationFiltersResponse.class));
        } finally {
            if (lockOptional.isPresent()) {
                redisLockService.unlock(lockOptional.get());
            }
        }
    }

    /*
     * checks if filter exists with same name
     */
    private void checkIfFilterExistsWithSameName(String name, Integer accountId, String id) {
        SmartInboxFilter smartInboxFilterWithSameName = smartInboxFilterDAO
                .getLatestSmartInboxFilterWithNameAndAccountId(name, accountId);
        boolean existingName = Objects.nonNull(smartInboxFilterWithSameName);
        String existingId = existingName ? smartInboxFilterWithSameName.getId() : null;
        if (existingName && StringUtils.isNotBlank(existingId) && !existingId.equals(id)) {
            throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_DUPLICATE_FILTER_NAME);
        }
    }

    /*
     * Delete smart inbox filter by id
     */
    @Override
    public void deleteSmartInboxFilterById(DeleteSmartInboxFilterRequest deleteSmartInboxFilterRequest) {
        String id = deleteSmartInboxFilterRequest.getId();
        Integer accountId = deleteSmartInboxFilterRequest.getAccountId();
        Integer userId = deleteSmartInboxFilterRequest.getUserId();

        SmartInboxFilter smartInboxFilter = validateSmartInboxFilterEditAccess(id, userId, accountId, "DELETE");
        smartInboxFilterDAO.deleteById(id, accountId);

        smartInboxFilterDAO.evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(
                smartInboxFilter, accountId,
                userId);

        log.info("deleteSmartInboxFilterById successfully");
    }

    /*
     * Validates whether user is owner or not of the filter
     */
    private SmartInboxFilter validateSmartInboxFilterEditAccess(String id, Integer userId, Integer accountId,
            String operation) {
        log.info("validateSmartInboxFilterEditAccess called with id : {}, userId : {} and accountId : {}", id, userId,
                accountId);
        SmartInboxFilter smartInboxFilter = smartInboxFilterDAO.findById(id, accountId);
        if (Objects.isNull(smartInboxFilter)) {
            log.info("Smart inbox filter not found with id : {}", id);
            throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_FILTER_INVALID_FILTER_ID);
        }

        Integer createdBy = smartInboxFilter.getCreatedBy().getId();

        if (!userId.equals(createdBy)) {
            log.info("user : {} doesn't have access to perform {} operation on smart inbox filter : {}", userId,
                    operation, id);
            throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_FILTER_UNAUTHORIZED_ACCESS);
        }
        smartInboxFilter.setId(id);
        return smartInboxFilter;
    }

    /*
     * update smart inbox filter by id
     */
    @Override
    public void updateSmartInboxFilterById(CreateOrUpdateSmartInboxFilterRequest updateSmartInboxFilterRequest) {
        String id = updateSmartInboxFilterRequest.getId();
        String name = updateSmartInboxFilterRequest.getName();
        Integer userId = updateSmartInboxFilterRequest.getCreatedBy();
        Integer accountId = updateSmartInboxFilterRequest.getAccountId();
        ConversationFilter filters = updateSmartInboxFilterRequest.getFilters();
        List<Integer> userIds = updateSmartInboxFilterRequest.getUserIds();
        List<Integer> locationIds = updateSmartInboxFilterRequest.getLocationIds();
        List<Integer> teamIds = updateSmartInboxFilterRequest.getTeamIds();
        LocationHierarchy locationHierarchy = updateSmartInboxFilterRequest.getLocationHierarchy();

        if (StringUtils.isBlank(id)) {
            throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_ID_REQUIRED);
        }

        SmartInboxFilter smartInboxFilter = validateSmartInboxFilterEditAccess(id, userId, accountId, "UPDATE");
        Optional<Lock> lockOptional = Optional.empty();
        try {
            String lockKey = "SMART_INBOX_FILTER_LOCK:" + accountId + "-" + name;
            lockOptional = redisLockService.tryLock(lockKey, 1, TimeUnit.SECONDS);
            if (!lockOptional.isPresent()) {
                log.info("unable to acquire lock for lockKey : {}", lockKey);
                throw new SmartInboxFilterException(ErrorCode.SMART_INBOX_FILTER_ACQUIRE_LOCK);
            }

            Boolean migrated = smartInboxFilter.getMigrated();
            SmartInboxFilterSharedType smartInboxFilterSharedType = updateSmartInboxFilterRequest
                    .getSmartInboxFilterSharedType();

            if ((Boolean.TRUE.equals(migrated) && SmartInboxFilterSharedType.ONLY_ME != smartInboxFilterSharedType)
                    || !name.equals(smartInboxFilter.getName())) {
                checkIfFilterExistsWithSameName(name, accountId, id);
                smartInboxFilter.setName(name);
            }

            smartInboxFilterDAO.updateSmartInboxFilter(smartInboxFilter, locationIds, teamIds, userIds,
                    smartInboxFilterSharedType,
                    smartInboxFilter.getCreatedBy(), filters, locationHierarchy);

            // To evict cache for existing users
            smartInboxFilterDAO.evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(smartInboxFilter, accountId,
                    userId);

            // To evict cache for new users
            SmartInboxFilter smartInboxFilterNew = new SmartInboxFilter();
            smartInboxFilterNew.setLocationIds(locationIds);
            smartInboxFilterNew.setTeamIds(teamIds);
            smartInboxFilterNew.setUserIds(userIds);
            smartInboxFilterDAO.evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(smartInboxFilterNew, accountId,
                    userId);
            smartInboxFilterNew = null;

            log.info(
                    "updateSmartInboxFilterById successfully for id : {}", id);
        } finally {
            if (lockOptional.isPresent()) {
                redisLockService.unlock(lockOptional.get());
            }
        }

    }

    @Override
    @Cacheable(cacheNames = Constants.SMART_INBOX_FILTERS_CACHE, key = "#getSmartInboxFilterRequest.accountId +':'+ #getSmartInboxFilterRequest.userId", unless = "#result == null or #result.size() == 0")
    public List<GetSmartInboxFilterResponse> getSmartInboxFiltersForAUserForAnAccount(
            GetSmartInboxFilterRequest getSmartInboxFilterRequest) {
        Integer accountId = getSmartInboxFilterRequest.getAccountId();
        Integer userId = getSmartInboxFilterRequest.getUserId();
        Integer page = getSmartInboxFilterRequest.getPage();
        Integer size = getSmartInboxFilterRequest.getSize();
        String orderBy = getSmartInboxFilterRequest.getOrderBy();
        String sortOrder = getSmartInboxFilterRequest.getSortOrder().getOrderString();

        List<Integer> teamIds = userService.getTeamIdsOfAUserForAnAccount(accountId, userId);
        Map<Integer, List<Integer>> locationIdsMap = userService.getAccessibleLocationsOfAUserForAnAccount(accountId,
                Collections.singletonList(userId));
        List<Integer> locationIds = MapUtils.isNotEmpty(locationIdsMap) ? locationIdsMap.get(userId) : null;

        ElasticData<SmartInboxFilter> elasticDataSmartInboxFilters = smartInboxFilterDAO
                .getSmartInboxFiltersForAUserForAnAccount(teamIds, locationIds, userId, accountId, page, size, orderBy,
                        sortOrder);

        if (Objects.nonNull(elasticDataSmartInboxFilters)) {
            List<GetSmartInboxFilterResponse> smartInboxFilterResponses = elasticDataSmartInboxFilters.getResults()
                    .stream().filter(smartInbox -> {
                        List<Integer> filterLocations = Objects.nonNull(smartInbox.getFilters())
                                ? smartInbox.getFilters().getBusinessIds()
                                : null;
                        return (CollectionUtils.isEmpty(filterLocations)
                                || (CollectionUtils.isNotEmpty(locationIds) && CollectionUtils.isNotEmpty(filterLocations)
                                        && checkIfUserHasAccessToAnyOfTheFiltersLocation(new LinkedList<>(locationIds),
                                                new LinkedList<>(filterLocations))));
                    }).map(GetSmartInboxFilterResponse::new).collect(Collectors.toList());

            log.info("getSmartInboxFiltersForAUserForAnAccount returned smartInboxFilterResponses successfully : {}",
                    smartInboxFilterResponses);

            return smartInboxFilterResponses;
        }
        log.info("getSmartInboxFiltersForAUserForAnAccount returned null");
        return null;
    }

    private boolean checkIfUserHasAccessToAnyOfTheFiltersLocation(List<Integer> userLocations,
            List<Integer> filterLocations) {
        log.info(
                "checkIfUserHasAccessToAnyOfTheFiltersLocation called with userLocations : {} and filterLocations : {}",
                userLocations, filterLocations);
        userLocations.retainAll(filterLocations);
        return userLocations.size() > 0;
    }

    @Override
    public SmartInboxOwnershipResponse checkOwnership(Integer accountId, String userId) {
        List<SmartInboxFilter> smartInboxFilters = smartInboxFilterDAO.findByUserIdAndAccountId(userId, accountId);

        SmartInboxOwnershipResponse response = new SmartInboxOwnershipResponse();
        response.setHasSmartInboxes(CollectionUtils.isNotEmpty(smartInboxFilters));
        response.setSmartInboxCount(smartInboxFilters.size());
        log.info("Has smart inboxes - {}, count - {}", CollectionUtils.isNotEmpty(smartInboxFilters),
                smartInboxFilters.size());

        return response;

    }

    @Override
    public SmartInboxOwnershipResponse transferOwnership(Integer accountId, Integer userId, Integer transferFrom,
            Integer transferTo) {

        CreateOrUpdateSmartInboxFilterRequest request = new CreateOrUpdateSmartInboxFilterRequest();
        request.setCreatedBy(transferFrom);
        request.setTransferTo(transferTo);
        request.setUpdatedBy(userId);
        request.setAccountId(accountId);

        BulkByScrollResponse bulkByScrollResponse = smartInboxFilterDAO.transferOwnership(request);

        SmartInboxOwnershipResponse response = new SmartInboxOwnershipResponse();

        if (null != bulkByScrollResponse) {
            response.setSmartInboxCount(Long.valueOf(bulkByScrollResponse.getUpdated()).intValue());
            response.setHasSmartInboxes(bulkByScrollResponse.getUpdated() > 0);
            response.setTransferredToUserId(transferTo);
        }
        return response;
    }

    @Override
    public void handleUserEventType(UserEvent event) {
        redisHandler.removeKeyForSmartInbox(String.valueOf(event.getAccountId()), String.valueOf(event.getUserId()));

    }

    @Override
    public void handleDeleteTeamEvent(DeleteTeamEvent event) {
        redisHandler.deleteKeysForAccount(Constants.SMART_INBOX_FILTERS_CACHE, String.valueOf(event.getAccountId()));
    }

    @Override
    public void handlerUpdateTeamUserEvent(UpdateTeamUserEvent event) {
        redisHandler.deleteKeysForUserId(Constants.SMART_INBOX_FILTERS_CACHE, String.valueOf(event.getAccountId()),
                event.getUserList());
    }

}