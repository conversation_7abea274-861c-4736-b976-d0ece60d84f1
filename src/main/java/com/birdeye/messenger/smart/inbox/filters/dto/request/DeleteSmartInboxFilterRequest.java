/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.request;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeleteSmartInboxFilterRequest implements Serializable {
    
    private String id;
    
    private Integer accountId;
    
    private Integer userId;

}
