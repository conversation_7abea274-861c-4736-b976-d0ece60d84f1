/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.response;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import com.birdeye.messenger.enums.ConversationView;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversationFiltersResponse implements Serializable {

    private static final long serialVersionUID = -5916597922870958772L;

    List<Integer> businessIds;
    private List<Integer> assignedTo;
    private List<Integer> teamAssignedTo;
    private List<String> leadSource;
    private List<Integer> lastReceivedMessageSource;
    String searchTerm;
    String conversationStatus; // OPEN, CLOSED UNREAD ALL (null)
    Byte importStatus;

    // time filters
    Integer days;
    Integer months;
    Boolean unAssigned;
    Boolean unResponded;
    Integer sortOrder;
    String startDate;
    String endDate;
    Long lastMessageTime;
    String userId;
    Boolean isRead;
    private String timePeriodSelected;
    String readStatus;

    /* type = REVIEW/LEAD/MESSAGE/NULL */
    private ConversationView type;
    private List<String> customerSentiment;
    private ReviewFilter reviewFilter;
    private SurveyFilter surveyFilter;
    private PaymentFilter paymentFilter;
    private AppointmentFilter appointmentFilter;
    private SpamFilter spamFilter;

    private List<Integer> excludedBizIds;
    private Object customLevelData;
    private Boolean newFilters;
    
    public boolean allTime() {
        return !StringUtils.isEmpty(timePeriodSelected) && "ALL".equalsIgnoreCase(timePeriodSelected);
    }

    public boolean reviewsFilter() {
        return getType() == ConversationView.REVIEW;
    }

    public boolean surveysFilter() {
        return getType() == ConversationView.SURVEY;
    }

    public ConversationView getType() {
        return type != null ? type : ConversationView.RECENT;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(Include.NON_NULL)
    public static class ReviewFilter implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer days;
        private Integer months;
        private String startDate;
        private String endDate;
        private Integer groupByDays;
        private String timePeriodSelected;
        private Long startTimestamp;
        private Long endTimestamp;
        private List<Integer> reviewSites;
        private List<Float> ratings;
        private List<Integer> tagIds;
        private List<Integer> reviewFilterOptions;

        public Boolean responded() {
            if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                if (reviewFilterOptions.contains(5) && reviewFilterOptions.contains(6)) {
                    return null;
                } else if (reviewFilterOptions.contains(5)) {
                    return true;
                } else if (reviewFilterOptions.contains(6)) {
                    return false;
                }
            }
            return null;
        }

        public Integer recommended() {
            if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                if (reviewFilterOptions.contains(12) && reviewFilterOptions.contains(14)) {
                    return null;
                } else if (reviewFilterOptions.contains(12)) {
                    return 2;
                } else if (reviewFilterOptions.contains(14)) {
                    return 4;
                }
            }
            return null;
        }

        public Boolean updated() {
            if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                if (reviewFilterOptions.contains(15)) {
                    return true;
                }
            }
            return null;
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(Include.NON_NULL)
    public static class SurveyFilter implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer days;
        private Integer months;
        private String startDate;
        private String endDate;
        private Integer groupByDays;
        private String timePeriodSelected;
        private Long startTimestamp;
        private Long endTimestamp;
        private List<Integer> surveyIds;
        private List<String> surveyScores;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(Include.NON_NULL)
    public static class PaymentFilter implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer days;
        private Integer months;
        private String startDate;
        private String endDate;
        private Integer groupByDays;
        private String timePeriodSelected;
        private Long startTimestamp;
        private Long endTimestamp;
        private List<String> paymentStatus;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(Include.NON_NULL)
    public static class AppointmentFilter implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer days;
        private Integer months;
        private String startDate;
        private String endDate;
        private Integer groupByDays;
        private String timePeriodSelected;

        private Long startTimestamp;
        private Long endTimestamp;
        private List<String> appointmentStatus;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(Include.NON_NULL)
    public static class SpamFilter implements Serializable {
        private static final long serialVersionUID = -7130293021650585689L;
        private Long startTimestamp;
        private Long endTimestamp;
        private String startDate;
        private String endDate;
        private String timePeriodSelected;
    }
}
