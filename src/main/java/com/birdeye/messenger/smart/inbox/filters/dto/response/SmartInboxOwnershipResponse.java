package com.birdeye.messenger.smart.inbox.filters.dto.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;


/**
 * 
 * <AUTHOR>
 *
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SmartInboxOwnershipResponse implements Serializable {
	
	private boolean hasSmartInboxes;	
	private Integer smartInboxCount;
	private Integer transferredToUserId;
	
}
