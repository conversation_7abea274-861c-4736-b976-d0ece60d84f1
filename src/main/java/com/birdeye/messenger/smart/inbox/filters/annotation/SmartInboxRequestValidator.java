/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import static java.lang.annotation.ElementType.*;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

/**
 * <AUTHOR>
 *
 */
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
@Documented
@Constraint(validatedBy = CreateSmartInboxRequestValidator.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface SmartInboxRequestValidator {

    String message() default "Either userIds/teamIds/locationIds should be present. Multiple fields are not allowed.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
