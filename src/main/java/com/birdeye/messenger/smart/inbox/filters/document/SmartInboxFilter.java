/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.document;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest.ConversationFilter;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterSharedType;
import com.birdeye.messenger.smart.inbox.filters.sro.SmartInboxUserDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class SmartInboxFilter implements Serializable {
    
    private String id;

    private Integer accountId;

    private String name;

    private List<Integer> locationIds;

    private List<Integer> teamIds;

    private List<Integer> userIds;

    private ConversationFilter filters;
    
    private LocationHierarchy locationHierarchy;
    
    private SmartInboxUserDTO createdBy;

    private SmartInboxUserDTO updatedBy;

    private Long createdAt;

    private Long updatedAt;
    
    private SmartInboxFilterSharedType sharedType;
    
    private Boolean migrated;
    
    private Integer isDefault;
    
    private String requestedSource;

}
