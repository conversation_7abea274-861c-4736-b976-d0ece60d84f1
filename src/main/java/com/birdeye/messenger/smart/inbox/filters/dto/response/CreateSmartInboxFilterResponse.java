/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateSmartInboxFilterResponse implements Serializable {
    
    private String id;
    
    private String name;
    
    private Long createdAt;
    
    private Integer isDefault;
    
    private ConversationFiltersResponse filters;

}