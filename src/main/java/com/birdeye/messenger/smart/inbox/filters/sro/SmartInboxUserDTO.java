/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.sro;

import java.io.Serializable;

import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class SmartInboxUserDTO implements Serializable {

    private Integer id;

    private String name;
    @SecureString
    private String emailId;
    @SecureString
    private String phoneNo;

    public SmartInboxUserDTO(UserDTO userDTO) {
        this.id = userDTO.getId();
        this.name = userDTO.getName();
        this.emailId = userDTO.getEmailId();
        this.phoneNo = userDTO.getPhone();
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }

}
