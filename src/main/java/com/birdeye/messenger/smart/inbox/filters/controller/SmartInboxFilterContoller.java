/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.controller;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.messenger.RequestLoggingFilter;
import com.birdeye.messenger.enums.SortOrderEnum;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.DeleteSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.GetSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.response.CreateSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.GetSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.SmartInboxOwnershipResponse;
import com.birdeye.messenger.smart.inbox.filters.service.SmartInboxFilterService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/messenger/smart-inbox/filter", produces = { MediaType.APPLICATION_JSON_VALUE })
public class SmartInboxFilterContoller {

    private final SmartInboxFilterService smartInboxFilterService;

    /*
     * API to save smart inbox filter
     */
    @PostMapping(consumes = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<CreateSmartInboxFilterResponse> saveSmartInboxFilter(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,
            @RequestHeader(value = "x-request-source", required = false) String requestedSource,
            @RequestHeader(value = "channel", required = false) String channel,
            @RequestBody @Valid CreateOrUpdateSmartInboxFilterRequest createSmartInboxFilterRequest) {
        createSmartInboxFilterRequest.setAccountId(accountId);
        createSmartInboxFilterRequest.setCreatedBy(userId);
        createSmartInboxFilterRequest.setRequestedSource(requestedSource);
        log.info("saveSmartInboxFilter called with request : {},channel : {}", createSmartInboxFilterRequest, channel);
        CreateSmartInboxFilterResponse createSmartInboxFilterResponse = smartInboxFilterService
                .saveSmartInboxFilter(createSmartInboxFilterRequest);
        return new ResponseEntity<>(createSmartInboxFilterResponse, HttpStatus.OK);
    }

    /*
     * API to delete smart inbox filter by ID
     */
    @DeleteMapping(value = "/{id}")
    public ResponseEntity<Void> deleteSmartInboxFilterById(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,
            @PathVariable(value = "id") String id) {
        DeleteSmartInboxFilterRequest deleteSmartInboxFilterRequest = DeleteSmartInboxFilterRequest.builder().id(id)
                .accountId(accountId).userId(userId).build();
        log.info("deleteSmartInboxFilterById called with request : {}", deleteSmartInboxFilterRequest);
        smartInboxFilterService.deleteSmartInboxFilterById(deleteSmartInboxFilterRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /*
     * API to update smart inbox filter by ID
     */
    @PutMapping(consumes = { MediaType.APPLICATION_JSON_VALUE })
    public ResponseEntity<Void> updateSmartInboxFilter(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,
            @RequestBody @Valid CreateOrUpdateSmartInboxFilterRequest updateSmartInboxFilterRequest) {
        updateSmartInboxFilterRequest.setAccountId(accountId);
        updateSmartInboxFilterRequest.setCreatedBy(userId);
        log.info("updateSmartInboxFilter called with request : {}", updateSmartInboxFilterRequest);
        smartInboxFilterService.updateSmartInboxFilterById(updateSmartInboxFilterRequest);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

    /*
     * API to get smart inbox filters for a user for an account
     */
    @GetMapping
    public ResponseEntity<List<GetSmartInboxFilterResponse>> getSmartInboxFiltersForAUserForAnAccount(
            @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,
            @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,
            @RequestHeader(value = "x-request-source", required = false) String requestedSource,
            @RequestHeader(value = "channel", required = false) String channel,
            @RequestParam(value = "page", defaultValue = "0") Integer page,
            @RequestParam(value = "size", defaultValue = "500") Integer size,
            @RequestParam(value = "orderBy", defaultValue = "createdAt") String orderBy,
            @RequestParam(value = "sortOrder", defaultValue = "DESC") SortOrderEnum sortOrderEnum) {
        GetSmartInboxFilterRequest getSmartInboxFilterRequest = GetSmartInboxFilterRequest.builder()
                .accountId(accountId).userId(userId).page(page).size(size).orderBy(orderBy).sortOrder(sortOrderEnum)
                .requestedSource(requestedSource).build();
        log.info("getSmartInboxFiltersForAUserForAnAccount called with getSmartInboxFilterRequest : {},channel : {}",
                getSmartInboxFilterRequest, channel);
        List<GetSmartInboxFilterResponse> smartInboxFilterResponses = smartInboxFilterService
                .getSmartInboxFiltersForAUserForAnAccount(getSmartInboxFilterRequest);
        /*
         * Hack to support filters for Android device, due to NumberFormatException
         * Will be removed once mobile team accomodates the changes for
         * ES ID support
         */
        updateSmartInboxFiltersIdsForAndroid(getSmartInboxFilterRequest.getRequestedSource(),
                smartInboxFilterResponses);

        return new ResponseEntity<>(smartInboxFilterResponses, HttpStatus.OK);
    }

    @Operation(description = "Check owned smart inboxes before deletion", summary = "Checks if the user owns the smart inboxes before deleting them.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Owned filters checked successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping(value = "/check-ownership/{userId}")
    public ResponseEntity<SmartInboxOwnershipResponse> checkOwnership(
    		@Parameter(description = "Account ID", required = true, example = "12345") @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,

    		@Parameter(description = "User ID to check ownership for smart inboxes", required = true, example = "54321") @PathVariable String userId) {

        return new ResponseEntity<>(smartInboxFilterService.checkOwnership(accountId, userId), HttpStatus.OK);
    }

    @Operation(description = "Transfer ownership", summary = "Transfers ownership of a resource from one user to another.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Request to transfer ownership registered successful"),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PatchMapping(value = "/transfer/ownership")
    public ResponseEntity<SmartInboxOwnershipResponse> transferOwnership(
    		@Parameter(description = "Account ID", required = true, example = "12345") @RequestHeader(value = RequestLoggingFilter.HEADER_ACCOUNT_ID) Integer accountId,

    		@Parameter(description = "User ID", required = true, example = "98765") @RequestHeader(value = RequestLoggingFilter.HEADER_USER_ID) Integer userId,

    		@Parameter(description = "User ID to transfer ownership to", required = true, example = "54321") @RequestParam Integer transferTo,

    		@Parameter(description = "User ID from ownership has to be transfered", required = true, example = "63832") @RequestParam Integer transferFrom) {

        return new ResponseEntity<>(
                smartInboxFilterService.transferOwnership(accountId, userId, transferFrom, transferTo), HttpStatus.OK);
    }

    private void updateSmartInboxFiltersIdsForAndroid(String requestedSource,
            List<GetSmartInboxFilterResponse> smartInboxFilterResponses) {
        if ("mobile".equals(requestedSource) && CollectionUtils.isNotEmpty(smartInboxFilterResponses)) {
            smartInboxFilterResponses.forEach(filter -> {
                filter.setId(String.valueOf(filter.getId().hashCode()));
            });
        }
    }
}
