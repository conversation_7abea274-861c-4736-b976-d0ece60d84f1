/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.annotation;

import java.util.Collection;
import java.util.List;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import org.apache.commons.collections4.CollectionUtils;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;

/**
 * <AUTHOR>
 *
 */
public class CreateSmartInboxRequestValidator
        implements ConstraintValidator<SmartInboxRequestValidator, CreateOrUpdateSmartInboxFilterRequest> {

    @Override
    public boolean isValid(CreateOrUpdateSmartInboxFilterRequest value, ConstraintValidatorContext context) {
        List<Integer> userIds = value.getUserIds();
        List<Integer> locationIds = value.getLocationIds();
        List<Integer> teamIds = value.getTeamIds();

        if (isNotEmpty(userIds)) {
            return !isNotEmpty(locationIds) && !isNotEmpty(teamIds);
        } else if (isNotEmpty(locationIds)) {
            return !isNotEmpty(userIds) && !isNotEmpty(teamIds);
        } else if (isNotEmpty(teamIds)) {
            return !isNotEmpty(userIds) && !isNotEmpty(locationIds);
        }
        return true;
    }

    private <T> boolean isNotEmpty(Collection<T> collection) {
        return CollectionUtils.isNotEmpty(collection);
    }

}
