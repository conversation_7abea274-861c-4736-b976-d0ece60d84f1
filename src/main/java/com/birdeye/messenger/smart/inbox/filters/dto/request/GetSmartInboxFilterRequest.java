/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.request;

import java.io.Serializable;

import com.birdeye.messenger.enums.SortOrderEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetSmartInboxFilterRequest implements Serializable {

    private Integer accountId;

    private Integer userId;

    private Integer page;

    private Integer size;

    private String orderBy;

    private SortOrderEnum sortOrder;
    
    private String requestedSource;

}