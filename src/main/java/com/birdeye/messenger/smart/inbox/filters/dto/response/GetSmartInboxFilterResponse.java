/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.response;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.smart.inbox.filters.document.SmartInboxFilter;
import com.birdeye.messenger.smart.inbox.filters.sro.SmartInboxUserDTO;
import com.birdeye.messenger.util.JSONUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetSmartInboxFilterResponse implements Serializable {

    private String id;

    private String name;

    private ConversationFiltersResponse filters;

    private LocationHierarchy locationHierarchy;

    private SmartInboxUserDTO createdBy;

    private Long createdAt;

    private List<Integer> userIds;

    private List<Integer> locationIds;

    private List<Integer> teamIds;

    private Boolean hasSmartInboxes;

    private Integer smartInboxCount;

    private Integer isDefault;

    public GetSmartInboxFilterResponse(SmartInboxFilter smartInboxFilter) {
        this.id = smartInboxFilter.getId();
        this.name = smartInboxFilter.getName();
        this.filters = JSONUtils.fromJSON(JSONUtils.toJSON(smartInboxFilter.getFilters()),
                ConversationFiltersResponse.class);
        this.createdBy = smartInboxFilter.getCreatedBy();
        this.createdAt = smartInboxFilter.getCreatedAt();
        this.userIds = smartInboxFilter.getUserIds();
        this.locationIds = smartInboxFilter.getLocationIds();
        this.teamIds = smartInboxFilter.getTeamIds();
        this.locationHierarchy = smartInboxFilter.getLocationHierarchy();
        this.isDefault = Objects.nonNull(smartInboxFilter.getIsDefault()) ? smartInboxFilter.getIsDefault() : 0;
    }

}
