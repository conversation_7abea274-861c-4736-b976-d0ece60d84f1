/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dto.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.StringUtils;

import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.smart.inbox.filters.annotation.SmartInboxRequestValidator;
import com.birdeye.messenger.smart.inbox.filters.document.SmartInboxFilter;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterSharedType;
import com.birdeye.messenger.smart.inbox.filters.sro.SmartInboxUserDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@SmartInboxRequestValidator
public class CreateOrUpdateSmartInboxFilterRequest implements Serializable {

    private static final long serialVersionUID = -343885448925053413L;

    String id;

    @NotBlank(message = "Filter name is a mandatory field")
    private String name;

    @NotNull(message = "Filters defination is mandatory")
    private ConversationFilter filters;

    private LocationHierarchy locationHierarchy;

    private Integer accountId;

    private Integer createdBy;

    private Integer updatedBy;

    private List<Integer> userIds;

    private List<Integer> locationIds;

    private List<Integer> teamIds;

    private Integer transferTo;

    private Integer isDefault;

    private String requestedSource;

    public SmartInboxFilter getSmartInboxFilter(UserDTO userDTO) {
        Long timeStamp = new Date().getTime();
        SmartInboxUserDTO smartInboxUserDTO = new SmartInboxUserDTO(userDTO);
        SmartInboxFilterSharedType sharedType = getSmartInboxFilterSharedType();
        ConversationView type = ConversationView.MESSAGE;

        if ("mobile".equals(requestedSource)) {
            filters.setType(type);
        }

        SmartInboxFilter smartInboxFilter = SmartInboxFilter.builder().accountId(accountId)
                .createdAt(timeStamp).createdBy(smartInboxUserDTO).filters(filters).locationHierarchy(locationHierarchy)
                .locationIds(locationIds)
                .updatedAt(timeStamp).teamIds(teamIds).userIds(userIds).updatedBy(smartInboxUserDTO).name(name)
                .sharedType(sharedType).isDefault(isDefault).requestedSource(requestedSource).build();

        return smartInboxFilter;
    }

    @JsonIgnore
    public SmartInboxFilterSharedType getSmartInboxFilterSharedType() {
        SmartInboxFilterSharedType sharedType = CollectionUtils.isNotEmpty(userIds)
                ? SmartInboxFilterSharedType.WITH_USERS
                : CollectionUtils.isNotEmpty(locationIds) ? SmartInboxFilterSharedType.WITH_LOCATIONS
                        : CollectionUtils.isNotEmpty(teamIds) ? SmartInboxFilterSharedType.WITH_TEAMS
                                : SmartInboxFilterSharedType.ONLY_ME;
        return sharedType;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ConversationFilter implements Serializable {

        private static final long serialVersionUID = -5916597922870958772L;

        List<Integer> businessIds;
        private List<Integer> assignedTo;
        private List<Integer> teamAssignedTo;
        private List<String> leadSource;
        private List<Integer> lastReceivedMessageSource;
        String searchTerm;
        String conversationStatus; // OPEN, CLOSED UNREAD ALL (null)
        Byte importStatus;

        // time filters
        Integer days;
        Integer months;
        Boolean unAssigned;
        Boolean unResponded;
        Integer sortOrder;
        String startDate;
        String endDate;
        Long lastMessageTime;
        String userId;
        Boolean isRead;
        private String timePeriodSelected;
        String readStatus;

        /* type = REVIEW/LEAD/MESSAGE/NULL */
        private ConversationView type;
        private List<String> customerSentiment;
        private ReviewFilter reviewFilter;
        private SurveyFilter surveyFilter;
        private PaymentFilter paymentFilter;
        private AppointmentFilter appointmentFilter;
        private SpamFilter spamFilter;
        
        private List<Integer> excludedBizIds;
        private Object customLevelData;
        private Boolean newFilters;

        public boolean allTime() {
            return !StringUtils.isEmpty(timePeriodSelected) && "ALL".equalsIgnoreCase(timePeriodSelected);
        }

        public boolean reviewsFilter() {
            return getType() == ConversationView.REVIEW;
        }

        public boolean surveysFilter() {
            return getType() == ConversationView.SURVEY;
        }

        public ConversationView getType() {
            return type != null ? type : ConversationView.RECENT;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class ReviewFilter implements Serializable {
            private static final long serialVersionUID = 1L;

            private Integer days;
            private Integer months;
            private String startDate;
            private String endDate;
            private Integer groupByDays;
            private String timePeriodSelected;
            private Long startTimestamp;
            private Long endTimestamp;
            private List<Integer> reviewSites;
            private List<Float> ratings;
            private List<Integer> tagIds;
            private List<Integer> reviewFilterOptions;

            public Boolean responded() {
                if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                    if (reviewFilterOptions.contains(5) && reviewFilterOptions.contains(6)) {
                        return null;
                    } else if (reviewFilterOptions.contains(5)) {
                        return true;
                    } else if (reviewFilterOptions.contains(6)) {
                        return false;
                    }
                }
                return null;
            }

            public Integer recommended() {
                if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                    if (reviewFilterOptions.contains(12) && reviewFilterOptions.contains(14)) {
                        return null;
                    } else if (reviewFilterOptions.contains(12)) {
                        return 2;
                    } else if (reviewFilterOptions.contains(14)) {
                        return 4;
                    }
                }
                return null;
            }

            public Boolean updated() {
                if (CollectionUtils.isNotEmpty(reviewFilterOptions)) {
                    if (reviewFilterOptions.contains(15)) {
                        return true;
                    }
                }
                return null;
            }
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class SurveyFilter implements Serializable {
            private static final long serialVersionUID = 1L;
            private Integer days;
            private Integer months;
            private String startDate;
            private String endDate;
            private Integer groupByDays;
            private String timePeriodSelected;
            private Long startTimestamp;
            private Long endTimestamp;
            private List<Integer> surveyIds;
            private List<String> surveyScores;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class PaymentFilter implements Serializable {
            private static final long serialVersionUID = 1L;
            private Integer days;
            private Integer months;
            private String startDate;
            private String endDate;
            private Integer groupByDays;
            private String timePeriodSelected;
            private Long startTimestamp;
            private Long endTimestamp;
            private List<String> paymentStatus;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class AppointmentFilter implements Serializable {
            private static final long serialVersionUID = 1L;

            private Integer days;
            private Integer months;
            private String startDate;
            private String endDate;
            private Integer groupByDays;
            private String timePeriodSelected;

            private Long startTimestamp;
            private Long endTimestamp;
            private List<String> appointmentStatus;
        }

        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class SpamFilter implements Serializable {
            private static final long serialVersionUID = -7130293021650585689L;
            private Long startTimestamp;
            private Long endTimestamp;
            private String startDate;
            private String endDate;
            private String timePeriodSelected;
        }
    }

}
