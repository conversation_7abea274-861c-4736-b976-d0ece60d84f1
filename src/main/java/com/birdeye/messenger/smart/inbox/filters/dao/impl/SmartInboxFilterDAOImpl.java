/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest.RefreshPolicy;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ESRequest;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.UsersResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.es.sro.ESDeleteByIdRequest;
import com.birdeye.messenger.es.sro.ESFindByFieldRequest;
import com.birdeye.messenger.es.sro.ESFindByIdRequest;
import com.birdeye.messenger.es.sro.ESInsertRequest;
import com.birdeye.messenger.es.sro.ESQueryBuilderRequest;
import com.birdeye.messenger.es.sro.ESUpsertRequest;
import com.birdeye.messenger.es.sro.ElasticUpdateQueryRequest;
import com.birdeye.messenger.es.sro.ElasticUpdateQueryRequest.SmartInboxOwnerBuilder;
import com.birdeye.messenger.exception.SmartInboxFilterException;
import com.birdeye.messenger.external.service.BusinessService;
import com.birdeye.messenger.external.service.UserService;
import com.birdeye.messenger.service.ElasticSearchExternalService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.smart.inbox.filters.dao.SmartInboxFilterDAO;
import com.birdeye.messenger.smart.inbox.filters.document.SmartInboxFilter;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest.ConversationFilter;
import com.birdeye.messenger.smart.inbox.filters.enums.ESQueryType;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterSharedType;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterUpdatedType;
import com.birdeye.messenger.smart.inbox.filters.sro.SmartInboxUserDTO;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@RequiredArgsConstructor
@Slf4j
@Repository
public class SmartInboxFilterDAOImpl implements SmartInboxFilterDAO {

    private final ElasticSearchExternalService elasticSearchExternalService;

    private final String indexName = Constants.Elastic.SMART_INBOX_FILTER_INDEX;

    private final RedisHandler redisHandler;

    private final BusinessService businessService;
    
    private final UserService userService;

    @Override
    public SmartInboxFilter getLatestSmartInboxFilterWithNameAndAccountId(String name, Integer accountId) {
        log.info("getLatestSmartInboxFilterWithNameAndAccountId called with name : {} and accountId : {}", name,
                accountId);
        Map<String, Object> fields = new LinkedHashMap(2) {
            {
                put("accountId", accountId);
                put("name.keyword", name);
            }
        };
        BoolQueryBuilder boolQueryBuilderNameAndAccount = createElasticQueryWithMultipleFieldsSearch(fields,
                ESQueryType.MUST, null);
        ESQueryBuilderRequest<SmartInboxFilter> esQueryBuilderRequest = ESQueryBuilderRequest
                .buildESQueryBuilderRequest(0, 1,
                        SmartInboxFilter.class, accountId, indexName, boolQueryBuilderNameAndAccount, null, null,
                        null);
        esQueryBuilderRequest.setInclude_Id(true);
        List<SmartInboxFilter> smartInboxFilters = elasticSearchExternalService
                .getResultUsingQueryBuilder(esQueryBuilderRequest);
        SmartInboxFilter smartInboxFilter = null;
        if (CollectionUtils.isNotEmpty(smartInboxFilters)) {
            smartInboxFilter = smartInboxFilters.get(0);
        }
        log.info("getLatestSmartInboxFilterWithNameAndAccountId called succefully : {}", smartInboxFilter);
        return smartInboxFilter;
    }

    @Override
    public String saveSmartInboxFilter(SmartInboxFilter smartInboxFilter) {
        log.info("saveSmartInboxFilter called with request : {}", smartInboxFilter);
        ESInsertRequest<SmartInboxFilter> esInsertRequest = ESInsertRequest.buildESInsertRequest(smartInboxFilter,
                indexName,
                smartInboxFilter.getAccountId(), RefreshPolicy.IMMEDIATE, null);
        IndexResponse indexResponse = elasticSearchExternalService.insertESDocument(esInsertRequest);
        log.debug("saveSmartInboxFilter successfully : {}", indexResponse);
        return indexResponse.getId();
    }

    public BoolQueryBuilder createElasticQueryWithMultipleFieldsSearch(Map<String, Object> fields,
            ESQueryType queryType, BoolQueryBuilder boolQueryBuilder) {
        log.info(
                "createElasticQueryWithMultipleFieldsSearch called with fields : {},queryType : {},boolQueryBuilder : {}",
                fields, queryType, boolQueryBuilder);
        if (Objects.isNull(boolQueryBuilder)) {
            boolQueryBuilder = new BoolQueryBuilder();
        }
        if (ESQueryType.MUST == queryType) {
            for (Map.Entry<String, Object> entry : fields.entrySet()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
            }
            return boolQueryBuilder;
        } else if (ESQueryType.MUST_NOT == queryType) {
            for (Map.Entry<String, Object> entry : fields.entrySet()) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
            }
            return boolQueryBuilder;
        } else if (ESQueryType.SHOULD == queryType) {
            for (Map.Entry<String, Object> entry : fields.entrySet()) {
                boolQueryBuilder.should(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
            }
            return boolQueryBuilder;
        } else if (ESQueryType.SHOULD_NOT == queryType) {
            BoolQueryBuilder boolQueryBuilderShouldNot = new BoolQueryBuilder();
            for (Map.Entry<String, Object> entry : fields.entrySet()) {

                boolQueryBuilderShouldNot.mustNot(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
            }
            boolQueryBuilder.should(boolQueryBuilderShouldNot);
            return boolQueryBuilder;
        } else if (ESQueryType.FILTER == queryType) {
            for (Map.Entry<String, Object> entry : fields.entrySet()) {
                boolQueryBuilder.filter(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
            }
            return boolQueryBuilder;
        }
        return null;
    }

    @Override
    public SmartInboxFilter findById(String id, Integer accountId) {
        log.info("smart inbox filter findById called with id : {}", id);
        ESFindByIdRequest<SmartInboxFilter> findSmartInboxFilterByIdRequest = ESFindByIdRequest
                .buildESFindByIdRequest(id, indexName, accountId, SmartInboxFilter.class, null, null);
        SmartInboxFilter smartInboxFilter = elasticSearchExternalService
                .findDocumentById(findSmartInboxFilterByIdRequest);
        log.info("smart inbox filter findById successfully : {}", JSONUtils.toJSON(smartInboxFilter));
        return smartInboxFilter;
    }

    @Override
    public void deleteById(String id, Integer accountId) {
        log.info("smart inbox filter deleteById called with id : {} and accountId : {}", id, accountId);
        ESDeleteByIdRequest esDeleteByIdRequest = ESDeleteByIdRequest.buildESDeleteByIdRequest(id, indexName, accountId,
                RefreshPolicy.IMMEDIATE);
        id = elasticSearchExternalService.deleteDocumentById(esDeleteByIdRequest);
        if (StringUtils.isBlank(id)) {
            log.error("Error occurred while deleting smart inbox filter");
            throw new SmartInboxFilterException(ErrorCode.ES_DELETE_ERROR);
        }
        log.info("smart inbox filter deleteById successful for id : {} and accountId : {}", id, accountId);
    }

    @Override
    public void updateSmartInboxFilter(SmartInboxFilter smartInboxFilter, List<Integer> locationIds,
            List<Integer> teamIds, List<Integer> userIds,
            SmartInboxFilterSharedType smartInboxFilterSharedType, SmartInboxUserDTO updatedBy,
            ConversationFilter filters, LocationHierarchy locationHierarchy) {
        String smartInboxFilterJson = JSONUtils.toJSON(smartInboxFilter);
        log.info(
                "updateSmartInboxFilter called with smartInboxFilter : {}, locationIds : {},teamIds : {},userIds : {} smartInboxFilterSharedType : {}, updatedBy : {} , filters : {} and locationHierarchy : {}",
                smartInboxFilterJson, locationIds, teamIds, userIds, smartInboxFilterSharedType,
                JSONUtils.toJSON(updatedBy), JSONUtils.toJSON(filters), JSONUtils.toJSON(locationHierarchy));

        Map<String, Object> smartInboxFilterMap = JSONUtils.fromJSON(smartInboxFilterJson, Map.class);

        smartInboxFilterMap.put("locationIds", locationIds);
        smartInboxFilterMap.put("teamIds", teamIds);
        smartInboxFilterMap.put("userIds", userIds);
        smartInboxFilterMap.put("sharedType", smartInboxFilterSharedType);
        smartInboxFilterMap.put("filters", filters);
        smartInboxFilterMap.put("locationHierarchy", locationHierarchy);
        smartInboxFilterMap.put("updatedBy", updatedBy);
        smartInboxFilterMap.put("updatedAt", new Date().getTime());
        smartInboxFilterMap.remove("id");
        log.info("modifySmartInboxFilter successfully : {}", JSONUtils.toJSON(smartInboxFilterMap));

        String id = smartInboxFilter.getId();
        Integer accountId = smartInboxFilter.getAccountId();

        ESUpsertRequest<Map<String, Object>> esUpsertRequest = ESUpsertRequest.buildESUpdateRequest(id, accountId,
                indexName, smartInboxFilterMap, null, null, false, RefreshPolicy.IMMEDIATE, 4, true);

        smartInboxFilterMap = elasticSearchExternalService.upsertESDocument(esUpsertRequest);

        if (Objects.isNull(smartInboxFilter)) {
            log.error("Error occurred while updating smart inbox filter");
            throw new SmartInboxFilterException(ErrorCode.ES_UPSERT_UPDATE_ERROR);
        }
        log.info("updateSmartInboxFilter successfully : {}", JSONUtils.toJSON(smartInboxFilterMap));
    }

    @Override
    public ElasticData<SmartInboxFilter> getSmartInboxFiltersForAUserForAnAccount(List<Integer> teamIds,
            List<Integer> locationIds, Integer userId, Integer accountId, Integer page, Integer size, String orderBy,
            String sortOrder) {
        log.info(
                "getSmartInboxFiltersForAUserForAnAccount called with teamIds : {},locationIds : {},userId : {},accountId : {},page : {},size : {},orderBy : {},sortOrder : {}",
                teamIds, locationIds, userId, accountId, page, size, orderBy, sortOrder);
        Integer from = page * size;
        Map<String, Object> data = new HashMap<>(8);
        data.put("from", from);
        data.put("size", size);
        data.put("accountId", accountId);
        data.put("userId", userId);
        data.put("locationIds", ControllerUtil.toCommaSeparatedString(locationIds));
        data.put("teamIds", ControllerUtil.toCommaSeparatedString(teamIds));
        data.put("orderBy", orderBy);
        data.put("sortOrder", sortOrder);
        ESRequest esRequest = new ESRequest.Builder(new ESRequest())
                .addIndex(Constants.Elastic.SMART_INBOX_FILTER_INDEX).addRoutingId(accountId)
                .addTemplateAndDataModel(Constants.Elastic.GET_SMART_INBOX_FILTERS_OF_A_USER_FOR_AN_ACCOUNT,
                        Collections.singletonMap("data", data))
                .build();
        SearchResponse searchResponse = elasticSearchExternalService.getSearchResult(esRequest);
        ElasticData<SmartInboxFilter> elasticData = null;
        if (Objects.nonNull(searchResponse) && Objects.nonNull(searchResponse.getHits())) {
            SearchHits searchHits = searchResponse.getHits();
            List<SmartInboxFilter> smartInboxFilters = Arrays.stream(searchHits.getHits())
                    .map(this::getSmartInboxFilterFromESSearchHit).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            TotalHits total = searchHits.getTotalHits();
            elasticData = new ElasticData<>();
            elasticData.setTotal(Objects.nonNull(total) ? total.value : 0);
            elasticData.setResults(smartInboxFilters);
            elasticData.setSucceeded(true);
            log.info("getSmartInboxFiltersForAUserForAnAccount successfully : {}", JSONUtils.toJSON(smartInboxFilters));
            return elasticData;
        }
        log.info("error occurred in getSmartInboxFiltersForAUserForAnAccount");
        return elasticData;
    }

    private SmartInboxFilter getSmartInboxFilterFromESSearchHit(SearchHit searchHit) {
        Map<String, Object> documentMap = searchHit.getSourceAsMap();
        documentMap.put("id", searchHit.getId());
        return JSONUtils.fromJSON(JSONUtils.toJSON(documentMap), SmartInboxFilter.class);
    }

    @Override
    @Async
    public void evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(SmartInboxFilter smartInboxFilter,
            Integer accountId, Integer userId) {
        log.info(
                "evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters called with smartInboxFilter : {}, accountId : {} and userId : {}",
                smartInboxFilter, accountId, userId);
        List<Integer> userIds = smartInboxFilter.getUserIds();
        List<Integer> locationIds = smartInboxFilter.getLocationIds();
        List<Integer> teamIds = smartInboxFilter.getTeamIds();

        SmartInboxFilterUpdatedType smartInboxFilterUpdatedType = CollectionUtils.isNotEmpty(teamIds)
                ? SmartInboxFilterUpdatedType.TEAMS
                : CollectionUtils.isNotEmpty(locationIds) ? SmartInboxFilterUpdatedType.LOCATIONS
                        : SmartInboxFilterUpdatedType.USERS;

        if (Objects.isNull(userIds)) {
            userIds = Collections.singletonList(userId);
        }

        List<Integer> ids = getUpdatedIds(smartInboxFilterUpdatedType, userIds, teamIds,
                locationIds);

        if (SmartInboxFilterUpdatedType.USERS == smartInboxFilterUpdatedType && !ids.contains(userId)) {
            ids.add(userId);
        }

        switch (smartInboxFilterUpdatedType) {
            case FILTER:
            case USERS:
                buildSmartInboxFilterKeysFromIdsAndEvictCache(ids, accountId);
                break;
            case TEAMS:
                List<Integer> newUserIds = new ArrayList<>();
                ids.stream().forEach(id -> {
                    List<Integer> teamUsers = businessService.getAllUsersOfTeams(id, accountId);
                    if (CollectionUtils.isNotEmpty(teamUsers)) {
                        newUserIds.addAll(teamUsers);
                    }
                });
                buildSmartInboxFilterKeysFromIdsAndEvictCache(newUserIds, accountId);
                break;
            case LOCATIONS:
                redisHandler.deletePatternMatchingKeysFromRedis(
                        Constants.SMART_INBOX_FILTERS_CACHE + "::" + accountId + "*");
                break;
            default:
                log.info(
                        "evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters called without any matching smartInboxFilterUpdatedType : {}",
                        smartInboxFilterUpdatedType);
                break;
        }

    }

    private void buildSmartInboxFilterKeysFromIdsAndEvictCache(List<Integer> ids, Integer accountId) {
        List<String> keys = ids.stream().map(id -> Constants.SMART_INBOX_FILTERS_CACHE + "::" + accountId + ':' + id)
                .collect(Collectors.toList());
        redisHandler.evictWebChatCache(keys);
        log.info("buildSmartInboxFilterKeysFromIdsAndEvictCache successful for keys : {}", keys);
    }

    /*
     * gets the new sharing ids from request
     */
    public List<Integer> getUpdatedIds(SmartInboxFilterUpdatedType smartInboxFilterUpdatedType, List<Integer> userIds,
            List<Integer> teamIds, List<Integer> locationIds) {
        switch (smartInboxFilterUpdatedType) {
            case TEAMS:
                return teamIds;
            case USERS:
                return userIds;
            case LOCATIONS:
                return locationIds;
            default:
                return null;
        }
    }
	public List<SmartInboxFilter> findByUserIdAndAccountId(String userId, Integer accountId) {
		
		ESFindByFieldRequest<SmartInboxFilter> esFindRequest = new ESFindByFieldRequest.Builder<SmartInboxFilter>()
				.setIndex(indexName)
				.setRoutingId(accountId)
				.setDocumentType(SmartInboxFilter.class)
				.setMustNestedPath( "createdBy", "createdBy.id", userId)
				.setMustFields("accountId", String.valueOf(accountId))	
				.setMustNotFields("sharedType", "ONLY_ME")
				.build();
		
		esFindRequest.isValidateRequest();
		
		return elasticSearchExternalService.findDocumentByField(esFindRequest);
	}

	@Override
	public BulkByScrollResponse transferOwnership(CreateOrUpdateSmartInboxFilterRequest createOrUpdateSmartInboxFilterRequest) {
	
		UsersResponse userResponse =   userService.getUsers(Arrays.asList(createOrUpdateSmartInboxFilterRequest.getTransferTo(),
				createOrUpdateSmartInboxFilterRequest.getUpdatedBy()), null);
		
		
		if(null == userResponse || CollectionUtils.isEmpty(userResponse.getUsers())) {
			log.error("No users information returned from core service");
			return null;
		}
		
		UserDTO transferToUser = userResponse.getUsers().stream()
                .filter(userDTO -> userDTO.getId().equals(createOrUpdateSmartInboxFilterRequest.getTransferTo()))
                .findFirst().orElse(null);
		
		UserDTO updateByUser = userResponse.getUsers().stream()
                .filter(userDTO -> userDTO.getId().equals(createOrUpdateSmartInboxFilterRequest.getUpdatedBy()))
                .findFirst().orElse(null);
		
		
		if(null == transferToUser || null == updateByUser ) {
			log.error("Unable to retrieve context from user ids - [{}, {}]", createOrUpdateSmartInboxFilterRequest.getTransferTo(),
					createOrUpdateSmartInboxFilterRequest.getUpdatedBy());
			return null;
		}
		
		String accountId = String.valueOf( createOrUpdateSmartInboxFilterRequest.getAccountId() );
		String userId = String.valueOf(createOrUpdateSmartInboxFilterRequest.getCreatedBy());
		
		String script = "ctx._source.createdBy.id = " + transferToUser.getId()  +
                  "; ctx._source.createdBy.name = '" + transferToUser.getName() + "'" +
                  "; ctx._source.createdBy.emailId = '" + transferToUser.getEmailId()  + "'" +
                  "; ctx._source.updatedBy.id = " + updateByUser.getId() +
                  "; ctx._source.updatedBy.name = '" + updateByUser.getName()  + "'" +
                  "; ctx._source.updatedBy.emailId = '" + updateByUser.getEmailId()  + "'" ;
						
		
		ESDeleteByIdRequest esDeleteByIdRequest = ESDeleteByIdRequest.builder()
				.index(indexName)
				.routingId(accountId)
				.query(QueryBuilders.boolQuery()
						.must(QueryBuilders.termQuery("sharedType", "ONLY_ME"))
						.must(QueryBuilders.nestedQuery(
								"createdBy",
				                QueryBuilders.termQuery("createdBy.id", userId),
				                org.apache.lucene.search.join.ScoreMode.None)
				         )
				)		
				.build();
		
		
		elasticSearchExternalService.deleteDocumentByQueryAsync(esDeleteByIdRequest);
		
		
		SmartInboxOwnerBuilder smartInboxOwnerBuilder = new ElasticUpdateQueryRequest.SmartInboxOwnerBuilder(indexName);
		smartInboxOwnerBuilder.setAccountId(createOrUpdateSmartInboxFilterRequest.getAccountId())		
		.setMustNestedPath( "createdBy", "createdBy.id", userId )
		.setMustFields("accountId", accountId)	
		.setMustNotFields("sharedType", "ONLY_ME")
		
		.setScriptValue(script);
		
		buildSmartInboxFilterKeysFromIdsAndEvictCache(Arrays.asList(createOrUpdateSmartInboxFilterRequest.getCreatedBy(), createOrUpdateSmartInboxFilterRequest.getTransferTo()), 
				createOrUpdateSmartInboxFilterRequest.getAccountId());
		return elasticSearchExternalService.updateSmartInboxOwner(smartInboxOwnerBuilder);
	}
	
	

}
