/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.dao;

import java.util.List;

import org.elasticsearch.index.reindex.BulkByScrollResponse;

import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.LocationHierarchy;
import com.birdeye.messenger.smart.inbox.filters.document.SmartInboxFilter;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest.ConversationFilter;
import com.birdeye.messenger.smart.inbox.filters.enums.SmartInboxFilterSharedType;
import com.birdeye.messenger.smart.inbox.filters.sro.SmartInboxUserDTO;

/**
 * <AUTHOR>
 *
 */
public interface SmartInboxFilterDAO {

    SmartInboxFilter getLatestSmartInboxFilterWithNameAndAccountId(String name, Integer accountId);

    String saveSmartInboxFilter(SmartInboxFilter smartInboxFilter);

    SmartInboxFilter findById(String id, Integer accountId);

    void deleteById(String id, Integer accountId);

    void updateSmartInboxFilter(SmartInboxFilter smartInboxFilter, List<Integer> locationIds, List<Integer> teamIds,
            List<Integer> userIds,
            SmartInboxFilterSharedType smartInboxFilterSharedType, SmartInboxUserDTO updatedBy,
            ConversationFilter filters, LocationHierarchy locationHierarchy);

    ElasticData<SmartInboxFilter> getSmartInboxFiltersForAUserForAnAccount(List<Integer> teamIds,
            List<Integer> locationIds, Integer userId, Integer accountId, Integer page, Integer size, String orderBy,
            String sortOrder);
    
    List<SmartInboxFilter> findByUserIdAndAccountId(String userId, Integer accountId);

    BulkByScrollResponse transferOwnership(CreateOrUpdateSmartInboxFilterRequest createOrUpdateSmartInboxFilterRequest);

	void evictUsersCacheOnSaveUpdateAndDeleteSmartInboxFilters(SmartInboxFilter smartInboxFilter, Integer accountId,
			Integer userId);
}
