/**
 * 
 */
package com.birdeye.messenger.smart.inbox.filters.service;

import java.util.List;

import com.birdeye.messenger.dto.UpdateTeamUserEvent;
import com.birdeye.messenger.dto.UserEvent;
import com.birdeye.messenger.smart.inbox.filters.dto.request.CreateOrUpdateSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.DeleteSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.request.GetSmartInboxFilterRequest;
import com.birdeye.messenger.smart.inbox.filters.dto.response.CreateSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.GetSmartInboxFilterResponse;
import com.birdeye.messenger.smart.inbox.filters.dto.response.SmartInboxOwnershipResponse;
import com.birdeye.messenger.sro.DeleteTeamEvent;

/**
 * <AUTHOR>
 *
 */
public interface SmartInboxFilterService {

    CreateSmartInboxFilterResponse saveSmartInboxFilter(CreateOrUpdateSmartInboxFilterRequest createSmartInboxFilterRequest);

    void deleteSmartInboxFilterById(DeleteSmartInboxFilterRequest deleteSmartInboxFilterRequest);

    void updateSmartInboxFilterById(CreateOrUpdateSmartInboxFilterRequest updateSmartInboxFilterRequest);
    
    List<GetSmartInboxFilterResponse> getSmartInboxFiltersForAUserForAnAccount(GetSmartInboxFilterRequest getSmartInboxFilterRequest);
  
    SmartInboxOwnershipResponse checkOwnership(Integer accountId, String userId);
    
    SmartInboxOwnershipResponse transferOwnership(Integer accountId,Integer userId, Integer transferFrom, Integer transferTo);
    
    void handleUserEventType(UserEvent event);
    
    void handleDeleteTeamEvent(DeleteTeamEvent event);
    
    void handlerUpdateTeamUserEvent(UpdateTeamUserEvent event);
}
