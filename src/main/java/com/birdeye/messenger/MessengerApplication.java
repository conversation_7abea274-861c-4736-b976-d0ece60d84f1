package com.birdeye.messenger;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;

/**
 * 
 * Messenger Application
 * 
 * <AUTHOR>
 *
 */
@EnableConfigurationProperties
@SpringBootApplication(exclude = {KafkaAutoConfiguration.class})
@EnableAspectJAutoProxy
@OpenAPIDefinition
@EnableCaching
@EnableScheduling
public class MessengerApplication extends SpringBootServletInitializer {
	/*
	 * Note that a WebApplicationInitializer is only needed if you are building
	 * a war file and deploying it. If you prefer to run an embedded web server
	 * then you won't need this at all.
	 */
	public static void main(String[] args) {
		SpringApplication.run(MessengerApplication.class, args);
	}
}
