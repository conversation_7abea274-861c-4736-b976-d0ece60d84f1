package com.birdeye.messenger.aspect;


import java.lang.reflect.Method;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import com.birdeye.messenger.annotations.AuditRestCall;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;


/**
 * 
 * <AUTHOR>
 *
 */

@Aspect
@Component
@Slf4j
public class AuditRestCallAspect {
	
	
    private static final String EXTERNAL_IO_CALL = "api_call" ;

    private static final String EXECUTION_TIME = "response_time" ;
    

	@Pointcut("execution(* org.springframework.web.client.RestTemplate.*(..))")
	public void auditRestCallMethodPointcut() {}


	@Around("auditRestCallMethodPointcut()")
	public Object auditRestCall(ProceedingJoinPoint joinPoint)  throws Throwable  {

		if(!ifAnnotated()) {
			return joinPoint.proceed();
		}
		Object[] args = joinPoint.getArgs();

		long startTime = System.currentTimeMillis();
		Object result = null;
		try {
			result = joinPoint.proceed();
		}finally {
			long executionTime = System.currentTimeMillis() - startTime ;

			
			 LogstashMarker marker = Markers.append(EXTERNAL_IO_CALL, args[0])
					 .and(Markers.append(EXECUTION_TIME, executionTime));
		       
			if (executionTime >= 100 ) {
	    		log.info(marker, "Response time for {} is {}" , args[0] , executionTime );
	    	}
		}
		
		
		return result;
	}


	private boolean ifAnnotated() {
		StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
		for (StackTraceElement stackTraceElement : stackTrace) {
			try {
				Class<?> clazz = Class.forName(stackTraceElement.getClassName());
				Method method = clazz.getMethod(stackTraceElement.getMethodName());
				if (method.isAnnotationPresent(AuditRestCall.class)) {
					return true;
				}
			} catch (ClassNotFoundException | NoSuchMethodException ignored) {}
		}
		return false;
	}
}
