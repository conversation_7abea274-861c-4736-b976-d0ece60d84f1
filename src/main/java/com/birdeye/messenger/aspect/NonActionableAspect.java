package com.birdeye.messenger.aspect;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.HandlerMapping;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.NonActionableException;

import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;


/**
 * 
 * <AUTHOR>
 *
 */

@Aspect
@Component
@Slf4j
public class NonActionableAspect {

	
  @Pointcut("@annotation(com.birdeye.messenger.annotations.NonActionableError)")
  public void nonActionableAuditPointcut() {}
  
  
  @After("nonActionableAuditPointcut()")
  public void logEnrichment(JoinPoint joinPoint)  throws Throwable  {
	Object[] args = joinPoint.getArgs();
	
	final NonActionableException ex = (NonActionableException) args[0];
	final ServletWebRequest servletWebRequest = (ServletWebRequest) args[1];

	ErrorMessageBuilder errorMessageBuilder =  ex.getErrorMessageBuilder();;
	
	if(errorMessageBuilder == null) {
		errorMessageBuilder = new ErrorMessageBuilder(ex.getErrorCode() == null ? ErrorCode.BLANK : ex.getErrorCode(), 
				ComponentCodeEnum.ERR)
				.message("NonActionableError Occurred - [{}] ", ex.getClass().getSimpleName())
				.message(ex.getMessage());
	}
	
	if(errorMessageBuilder.getHttpStatus() == HttpStatus.OK) {
		log.warn(getMarkers(servletWebRequest, errorMessageBuilder, ex.getStackTrace()[0].getClassName()), errorMessageBuilder.build());
	}
	else {
		log.error(getMarkers(servletWebRequest, errorMessageBuilder, ex.getStackTrace()[0].getClassName()), errorMessageBuilder.build(), ex);
	}
	
  }
  
  
  private LogstashMarker getMarkers(final ServletWebRequest servletWebRequest, final ErrorMessageBuilder errorMessageBuilder, 
		  final String baseClassName) {
	  
	  LogstashMarker marker = Markers.append( Constants.EXCEPTION_URI, (String) servletWebRequest.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE, 
			  WebRequest.SCOPE_REQUEST));
	  marker
	  .and(Markers.append(Constants.EXCEPTION_HTTP_METHOD, servletWebRequest.getHttpMethod()))
	  .and(Markers.append("error_code", String.valueOf(errorMessageBuilder.getErrorCodeGenerator().getHttpStatusCode())))
	  .and(Markers.append("module", errorMessageBuilder.getErrorCodeGenerator().getComponentCode().name()))
	  .and( Markers.append("base_class", baseClassName ) );
	  
	  return marker;
  }
  
}
