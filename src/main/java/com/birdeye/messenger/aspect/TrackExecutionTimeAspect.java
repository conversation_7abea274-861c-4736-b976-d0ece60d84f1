/**
 * 
 */
package com.birdeye.messenger.aspect;

import java.lang.reflect.Method;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import com.birdeye.messenger.annotations.TrackExecutionTime;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.util.LogUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Aspect
@Component
@Slf4j
public class TrackExecutionTimeAspect {


    @Around("@annotation(com.birdeye.messenger.annotations.TrackExecutionTime)")
    public Object executionTime(ProceedingJoinPoint point) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Class<?> declaringClass = method.getDeclaringClass();
        TrackExecutionTime trackExecutionTime = method.getAnnotation(TrackExecutionTime.class);
        String methodName = method.getName();
        List<String> parameters = Arrays.stream(method.getParameters())
                .map(parameter -> parameter.getType().getSimpleName() + "-" + parameter.getName())
                .collect(Collectors.toList());
        String className = declaringClass.getSimpleName();
        double maximumTimeThreshold = trackExecutionTime.maximumTimeThreshold();
        String apiCall = trackExecutionTime.apiCall();
        Object object = null;
        try {
            Instant start = Instant.now();
            object = point.proceed();
            String esIndex = MDC.get(Constants.ES_INDEX);
            Instant finish = Instant.now();
            long time = Duration.between(start, finish).toMillis();
            if (time > maximumTimeThreshold) {
                LogUtil.logESExecutionTime(apiCall, esIndex, time, className, methodName, parameters);
            }
            MDC.remove(esIndex);
        } catch (Throwable e) {
            log.info("error occurred in executionTime : {}", e.getMessage());
        }
        return object;
    }

}
