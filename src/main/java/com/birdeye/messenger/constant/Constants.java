package com.birdeye.messenger.constant;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.birdeye.messenger.dto.apple.chat.ListPickerInteractiveData.ImageData;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.IntentType;

public interface Constants {

	String FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	String DATE_FORMAT_MESSENGER_UI = "MMM dd, yyyy hh:mm aa zzz"; // Mar 11, 2020 12:50 AM PDT
	String DATE_FORMAT_MESSENGER_PAYMENTS = "MMM dd, yyyy"; // Mar 11, 2020
	String WEBCHAT_ALERT = "webchat_alert";
	String WEBCHAT_ALERT_V1 = "webchat_alert_v1";
	String MESSENGER_ALERT = "messenger_alert";
	String PAYMENT_ALERT = "payment_alert";
	Integer FACEBOOK_DUMMY_USER = -8;
	Integer INSTAGRAM_DUMMY_USER = -18;
	Integer INBOX_DEFENDER_USER = -78;

	Integer Twitter_DUMMY_USER = -22;

	String INBOX_DEFENDER_USERNAME = "InboxDefender";
	// This will be used as default user who marked block all existing blocked
	// contacts before spam detection release
	Integer BUSINESS_BLOCKED_BY_DUMMY_USER = -78787;
	// Redis Key for maintaining change events on account level.
	String REDIS_KEY_MESSENGER_TRIGGER_TIME = "MsngrTriggerTime";
	String SPAM_MESSAGE_NOTIFICATION = "SpamMsgNotification:";
	String BIZ_FB_STATUS = "BIZ_FB_STATUS";
	String BIZ_IG_STATUS = "BIZ_IG_STATUS";
	String BIZ_TWITTER_STATUS= "BIZ_TWITTER_STATUS";
	public static final String BUSINESS_CACHE = "msgr_business";
	public static final String SOCIAL_CACHE = "msgr_social";
	public static final String APP_RECEIVE_MSG_COUNT_CACHE = "app_receive_msg_count";
	public static final String BUSINESS_HOURS_CACHE = "business_hours";
	public static final String APPLE_TYPING_START_EVENT = "typing_start";
	public static final String APPLE_TYPING_END_EVENT = "typing_end";
	// Due to shared redis, having messenger prefix in cache name.
	public static final String BUSINESS_SMS_CACHE = "msgr_business_sms";
	public static final String COMPLETED = "COMPLETED";
	public static final String CUSTOMER_CACHE = "msgr_customer";
	public static final String MESSENGER_SERVICE = "Messenger-Service";
	public static final String DELETE_OPERATION = "delete";
	public static final String ENABLE_OPERATION = "enable";
	public static final String DISABLE_OPERATION = "disable";
	public static final String DEFAULT_CONFIG_CACHE = "default_config_cache";
	public static final String WIDGET_CONFIG_CACHE = "widget_config_cache";
	public static final String BUSINESS_LITE_CACHE = "business_lite_cache";
	public static final String CONTACT_UPGRADE_ACCOUNTS_CACHE = "contact_upgrade_accounts_cache";
	public static final String DEFAULT_WIDGET_CACHE = "default_widget_cache";
	public static final Integer DEFAULT_CONFIG_ID = 1;
	public static final String DEFAULT_WIDGET_NAME = "Default Widget";
	public static final String SENT = "sent";
	public static final String UNASSIGNED = "unAssigned";
	public static final String ASSINGNED_TO_ME = "assignedToMe";
	public static final String ASSINGNED_TO_OTHERS = "assignedToOthers";
	public static final String USER_ID_HEADER = "user-id";
	public static final String ACC_ID_HEADER = "account-id";
	public static final String DEFAULT_URL = "https://birdeye.com";
	public static final String SECURE_PROTOCOL = "https://";
	public static final String NON_SECURE_PROTOCOL = "http://";
	public static final int LIVECHAT_TIMEOUT_IN_SEC = 240;
	public static final Integer NOTIFICATION_BODY_MAX_LENGTH = 150;
	public static final String TEAM_CACHE = "team";
	public static final String DEFAULT_RECEPTIONIST_CONFIG_CACHE = "default_receptionist_config_cache";
	public static final String REDIS_KEY_PREFIX_LAST_CONVERSATION_ID_PROCESSED = "lst_conv_id_prcsd";
	public static final String REDIS_KEY_PREFIX_CONVERSATION_PAGES_FAILED = "conv_pgs_failed";
	public static final String UPGRADE = "UPGRADE";
	public static final String DOWNGRADE = "DOWNGRADE";
	public static final String MOVE = "MOVE";
	public static final String FAILED = "failed";

	public static final String INBOX_MISSED_CALL_CACHE = "inbox_missed_call_cache";
	public static final String NON_INBOX_MISSED_CALL_CACHE = "noninbox_missed_call_cache";
	public static final String REVIEW = "REVIEW";
	public static final String LEAD = "LEAD";
	public static final String LINK = "LINK";
	public static final String UNLINK = "UNLINK";
	public static final String EVENT_ID = "eventId";
	public static final String SURVEY_RESPONSE = "SURVEY_RESPONSE";
	public static final String INBOX_UNRESPONDED_JOB = "INBOX_UNRESPONDED_JOB";
	public static final String INBOX_UNRESPONDED_MESSAGES_NOTIFICATION_JOB = "INBOX_UNRESPONDED_MESSAGES_NOTIFICATION_JOB";
	public static final String UNRESPONDED_NOTIFICATION_INTERVAL_KEY = "UNRESPONDED_NOTIFICATION_INTERVAL";
	public static final String DEFAULT_REPORT_TIMEZONE_ID = "PST8PDT";
	String ASSIGNMENT_ALERT_V1 = "assigned_to_me_alert";
	public static final String BIZ_LITE_CACHE = "biz_lite_cache";
	public static final String BUSINESS_OPTIONS = "business_options";

	public static final String EXCEPTION_URI = "exception_uri";
	public static final String EXCEPTION_HTTP_METHOD = "exception_http_method";
	public static final int EMAIL_ID_MAX_LENGTH = 100;
	public static final int EMAIL_ID_MIN_LENGTH = 7;
	public static final String BUSINESS_FEATURES = "business-features";
	interface Elastic {
		String CONTACT_INDEX = "inbox_conversation";
		String MESSAGE_INDEX = "inbox_conversation_detail";
		String ARCHIVE_CONTACT_INDEX = "archive_inbox_conversation";
		String ARCHIVE_CONTACT_INDEX_TERTIARY = "archive_inbox_conversation_tertiary";
		String SMART_INBOX_FILTER_INDEX = "smart_inbox_filters";
		String GET_MESSAGES = "GET_MESSAGES.ftl";
		String GET_MESSAGES_BY_MCID_AND_REVIEWID = "GET_MESSAGES_BY_MCID_AND_REVIEWID.ftl";
		String GET_MESSAGES_BY_MCIDS = "GET_MESSAGES_BY_MCIDS.ftl";
		String GET_MESSAGES_V1 = "GET_MESSAGES_V1.ftl";
		String GET_MESSAGES_EXCLUDING_REVIEW = "GET_MESSAGES_EXCLUDING_REVIEW.ftl";
		String GET_MESSAGES_V2 = "GET_MESSAGES_V2.ftl";
		String GET_MESSAGES_V3 = "GET_MESSAGES_V3.ftl";
		String GET_MESSAGES_V4 = "GET_MESSAGES_V4.ftl";
		String GET_MESSAGES_BY_ID = "GET_MESSAGES_BY_ID.ftl";
		String GET_MESSAGES_FOR_DELTA = "GET_MESSAGES_FOR_DELTA.ftl";
		String GET_REFERRAL_ACTIVITIES_BY_REFERRER_AND_LEAD_ID = "GET_REFERRAL_ACTIVITIES_BY_REFERRER_AND_LEAD_ID.ftl";
		String GET_MESSAGE_FOR_CHAT_TRANSCRIPT = "GET_MESSAGE_FOR_CHAT_TRANSCRIPT.ftl";
		String GET_MESSAGE_FOR_WEBHOOK_EVENT = "GET_MESSAGE_FOR_WEBHOOK_EVENT.ftl";
		String GET_MESSAGES_IN_HTML_FOR_WEBHOOK = "GET_MESSAGES_IN_HTML_FOR_WEBHOOK.ftl";
		String GET_CONTACT = "GET_CONTACT.ftl";
		String GET_CONTACT_FOR_MIGRATION = "GET_CONTACT_FOR_MIGRATION.ftl";
		String GET_LAST_RECIEVED_MESSAGE_SOURCE = "GET_LAST_RECIEVED_MESSAGE_SOURCE.ftl";
		String GET_CONTACT_V1 = "GET_CONTACT_V1.ftl";
		Integer FETCH_SIZE = 10000;
		Integer ROBIN_UNANSWERED_QUESTION_COUNT_LIMIT = 500;
		String COUNT_LIMIT = "5000";
		String GET_CONVERSATION_COUNT = "GET_CONVERSATION_COUNT.ftl";
		String GET_TEAMS_CONVERSATIONS_COUNT = "GET_TEAMS_CONVERSATIONS_COUNT.ftl";
		String GET_CONVERSATIONS = "GET_CONVERSATIONS.ftl";
		String GET_CONVERSATION_V2 = "GET_CONVERSATION_V2.ftl";
		String MESSENGER_NOTIFICATION = "MESSENGER_NOTIFICATION.ftl";
		String MESSENGER_CONVERSATION = "MESSENGER_CONVERSATION.ftl";
		String MESSENGER_CONVERSATION_2 = "MESSENGER_CONVERSATION_2.ftl";
		String DELETE_CONTACT_BY_MCIDS = "DELETE_CONTACT_BY_MCIDS.ftl";
		String DELETE_MESSAGE_BY_MCID = "DELETE_MESSAGE_BY_MCID.ftl";
		String DELETE_ACTIVITIES_BY_MCID = "DELETE_ACTIVITIES_BY_MCID.ftl";
		String DELETE_ACTIVITIES_BY_MCIDS = "DELETE_ACTIVITIES_BY_MCIDS.ftl";
		String SEARCH_CONVERSATIONS = "SEARCH_CONVERSATIONS.ftl";
		String SEARCH_CONVERSATIONS_V2 = "SEARCH_CONVERSATIONS_V2.ftl";
		String AGGREGATIONS_FIELD = "aggregations";
		String GROUP_BY_UNREAD = "group_by_unread";
		String BUCKETS_FIELD = "buckets";
		String KEY_FIELD = "key";
		String DOC_COUNT_FIELD = "doc_count";
		String MESSENGER_CONVERSATION_SEARCH = "MESSENGER_CONTACT_SEARCH.ftl";
		String GET_CONTACT_FOR_MIGRATION_V1 = "GET_CONTACT_FOR_MIGRATION_V1.ftl";
		String GET_MESSAGE_FOR_BID_MIGRATION = "GET_MESSAGE_FOR_BID_MIGRATION.ftl";
		String GET_MESSAGES_BY_ACCOUNT_ID = "GET_MESSAGES_BY_ACCOUNT_ID.ftl";
		String MESSAGE_BY_ID = "MESSAGE_BY_ID.ftl";
		String MESSAGE_BY_ID_V2 = "MESSAGE_BY_ID_V2.ftl";
		String GET_CONVERSATIONS_V3 = "GET_CONVERSATIONS_V3.ftl";
		// String GET_CONVERSATIONS_V3 = "GET_CONVERSATIONS_V3_DEV_ENVIRONMENT.ftl";
		String GET_OPEN_CONVERSATIONS_COUNT = "GET_OPEN_CONVERSATIONS_COUNT.ftl";
		String GET_ASSIGNED_CONVERSATIONS_COUNT = "GET_ASSIGNED_CONVERSATIONS_COUNT.ftl";
		String GET_CUSTOMER_ACTIVITY = "GET_CUSTOMER_ACTIVITY.ftl";
		Integer UNASSIGNED_ID = -100000;
		String UNASSIGNED_NAME = "";
		String GET_CONVERSATIONS_BY_TEAM_ID = "GET_CONVERSATIONS_BY_TEAM_ID.ftl";
		String GET_CONVERSATIONS_BY_ID = "GET_CONVERSATIONS_BY_ID.ftl";
		String GET_MESSAGES_FOR_EXPORT = "GET_MESSAGES_FOR_EXPORT.ftl";
		String GET_MESSAGES_FOR_EXPORT_NLP = "GET_MESSAGES_FOR_EXPORT_NLP.ftl";
		String GET_MESSAGES_BY_CONTACTS_EXPORT = "GET_MESSAGES_FOR_CONTACTS_EXPORT.ftl";
		String GET_CONVERSATIONS_EXPORT = "GET_CONVERSATIONS_EXPORT.ftl";
		String GET_CONVERSATION_BY_CUSTOMER_EXPORT = "GET_CONVERSATION_BY_CUSTOMER_EXPORT.ftl";
		String DELETE_CONTACT_BY_QUERY_BY_BUSINESS_ID = "DELETE_CONTACT_BY_BUSINESS_ID.ftl";
		String DELETE_CONTACT_BY_QUERY_BY_MC_ID = "DELETE_CONTACT_BY_MC_ID.ftl";
		String DELETE_MESSAGES_BY_C_ID = "DELETE_MESSAGES_BY_C_ID.ftl";
		String DELETE_MESSAGES_BY_MC_ID = "DELETE_MESSAGES_BY_MC_ID.ftl";
		String DELETE_CUSTOMER_ACTIVITY_BY_CUSTOMER_ID = "DELETE_CUSTOMER_ACTIVITY_BY_CUSTOMER_ID.ftl";
		String GET_CONTACT_BY_CUSTOMER_IDS = "GET_CONTACT_BY_CUSTOMER_IDS.ftl";
		String GET_CONVERSATION_BY_REVIEW_ID = "GET_CONVERSATION_BY_REVIEW_ID.ftl";
		String DELETE_REVIEW_BY_ID = "DELETE_REVIEW_BY_ID.ftl";
		String DELETE_SURVEY_RESPONSE_BY_ID = "DELETE_SURVEY_RESPONSE_BY_ID.ftl";
		String GET_CONVERSATIONS_BY_SURVEY_ID = "GET_CONVERSATIONS_BY_SURVEY_ID.ftl";
		String GET_MESSAGES_BY_SURVEY_ID = "GET_MESSAGES_BY_SURVEY_ID.ftl";
		String GET_RECEIVE_MESSAGE_BY_TIME = "GET_RECEIVED_MESSAGES_REPORT_BY_TIME.ftl";
		String GET_ROBIN_RESPONSE_BY_TIME = "GET_ROBIN_RESPONSE_BY_TIME.ftl";
		String GET_ACTIVE_CONVERSATION_BY_TIME = "GET_ACTIVE_CONVERSATION_BY_TIME.ftl";
		String GET_OLDEST_MESSAGE = "GET_OLDEST_MESSAGE.ftl";
		String GET_ACTIVE_CONVERSATION_BY_LOC = "GET_ACTIVE_CONVERSATION_BY_LOC.ftl";
		String GET_RECEIVED_CONVERSATION_BY_LOC = "GET_RECEIVED_CONVERSATION_BY_LOC.ftl";
		String GET_ACTIVE_CONVERSATION_BY_USER = "GET_ACTIVE_CONVERSATION_BY_USER.ftl";
		String GET_MEDIAN_RESPONSE_TIME_BY_USER = "GET_MEDIAN_RESPONSE_TIME_BY_USER.ftl";
		String GET_MEDIAN_RESPONSE_OVER_TIME = "GET_MEDIAN_RESPONSE_OVER_TIME.ftl";
		String GET_MESSAGES_FOR_RES_TIME_MIGRATION = "GET_MESSAGES_FOR_RES_TIME_MIGRATION.ftl";
		String GET_MEDIAN_RESPONSE_OVER_LOCATION = "GET_MEDIAN_RESPONSE_OVER_LOCATION.ftl";
		String GET_CONVERSATIONS_READ_UNREAD_STATUS = "GET_CONVERSATIONS_READ_UNREAD_STATUS.ftl";
		String GET_INBOX_SEND_MESSAGE_REPORT = "GET_INBOX_SEND_MESSAGE_REPORT.ftl";
		String BIZAPP_GET_SEND_BY_CHANNEL = "BIZAPP_GET_SEND_BY_CHANNEL.ftl";
		String BIZAPP_SEND_AUTOREPLY = "BIZAPP_SEND_AUTOREPLY.ftl";
		String GET_ROBIN_UNASWERED_QUESTIONS = "GET_ROBIN_UNASWERED_QUESTIONS.ftl";
		String GET_MESSAGE_BY_PAYMENT_ID = "GET_MESSAGE_BY_PAYMENT_ID.ftl";
		String GET_MESSAGE_BY_PAYMENT_ID_CANCELLATION = "GET_MESSAGE_BY_PAYMENT_ID_CANCEL.ftl";
		String GET_MATRIX_ROI_REPORT = "GET_MATRIX_ROI_REPORT.ftl";
		String BIZAPP_GET_ALL_MESSAGES = "BIZAPP_GET_ALL_MESSAGES.ftl";
		String BIZAPP_GET_ALL_CONTACTS = "BIZAPP_GET_ALL_CONTACTS.ftl";
		String GET_MCID_BY_REVIEWID = "GET_MCID_BY_REVIEWID.ftl";
		String GET_AGENT_LAST_RESPONSE_TIME = "GET_AGENT_LAST_RESPONSE_TIME.ftl";
		String GET_CONVERSATION_COUNT_FOR_FREEMIUM_ACCOUNT = "GET_CONVERSATION_COUNT_FOR_FREEMIUM_ACCOUNT.ftl";
		String GET_CONVERSATION_CAMPAIGN_CLOSE_AUTOMATION = "GET_CONVERSATION_CAMPAIGN_CLOSE_AUTOMATION.ftl";
		String GET_MESSAGES_FOR_CONVERSATION_HISTORY = "GET_MESSAGES_FOR_CONVERSATION_HISTORY.ftl";
		String GET_CONTACT_CLIENT_IP = "GET_CONTACT_CLIENT_IP.ftl";
		String GET_ACTIVE_CONVERSATION_ROI = "GET_ACTIVE_CONVERSATION_ROI.ftl";
		String GET_MEDIAN_RESPONSE_TIME_ROI = "GET_MEDIAN_RESPONSE_TIME_ROI.ftl";
		String GET_WEBCHAT_CONVERSATIONS_ROI = "GET_WEBCHAT_CONVERSATIONS_ROI.ftl";
		String UPDATE_BY_INACTIVE_APPOINTMENT = "UPDATE_BY_INACTIVE_APPOINTMENT.ftl";
		String GET_ASSIGNMENT_COUNT_LAST_30_DAYS = "GET_ASSIGNMENT_COUNT_LAST_30_DAYS.ftl";
		String UPDATE_SPECIALIST_IMAGE_BY_ID = "UPDATE_SPECIALIST_IMAGE_BY_ID.ftl";
		String PULSE_SURVEY_STAT_INDEX = "pulse-survey-stat";
		// String PULSE_SURVEY_STAT_TYPE = "data";
		String GET_PULSE_SURVEY_STAT = "GET_PULSE_SURVEY_STAT.ftl";

		String GET_SECURE_MESSAGES = "GET_SECURE_MESSAGES.ftl";
		String GET_MESSAGES_FOR_UPDATING_CUSTOMER_FILTERS = "GET_MESSAGES_FOR_UPDATING_CUSTOMER_FILTERS.ftl";

		String GET_SMART_INBOX_FILTERS_OF_A_USER_FOR_AN_ACCOUNT = "GET_SMART_INBOX_FILTERS_OF_A_USER_FOR_AN_ACCOUNT.ftl";
		String GET_SMS_SEND_MESSAGES_FOR_ACCOUNT_IN_TIME_FRAME = "GET_SMS_SEND_MESSAGES_FOR_ACCOUNT_IN_TIME_FRAME.ftl";
		String GET_MESSAGES_RESERVE_W_GOOGLE = "GET_MESSAGES_RESERVE_W_GOOGLE.ftl";
		String GET_LAST_RESPONSE_MSG_RESERVE_W_GOOGLE = "GET_LAST_RESPONSE_MSG_RESERVE_W_GOOGLE.ftl";
		String GET_HIDDEN_CAMPAIGN_CONVERSATION_ACCOUNT = "GET_HIDDEN_CAMPAIGN_CONVERSATION_ACCOUNT.ftl";

		String GET_HIDDEN_CAMPAIGN_CONVERSATIONS = "GET_HIDDEN_CAMPAIGN_CONVERSATIONS.ftl";

		String DELETE_CAMPAIGN_HIDDEN_DATA = "DELETE_CAMPAIGN_HIDDEN_DATA.ftl";
		String GPT_POWERED_ROBIN_MESSAGE_HISTORY = "GPT_POWERED_ROBIN_MESSAGE_HISTORY.ftl";

		String GET_MESSAGES_WITHOUT_AUTO_REPLY = "GET_MESSAGES_WITHOUT_AUTO_REPLY.ftl";

		String GET_HIDDEN_CAMPAIGN_CONVERSATION_BY_MCIDS = "GET_HIDDEN_CAMPAIGN_CONVERSATION_BY_MCIDS.ftl";

		String GET_CONVERSATION_BY_PHONE_NUMBER = "GET_CONVERSATION_BY_PHONE_NUMBER.ftl";

		String GET_RECEIVED_MESSAGES_REPORT_BY_TIME_CHANNEL_WISE = "GET_RECEIVED_MESSAGES_REPORT_BY_TIME_CHANNEL_WISE.ftl";

		String GET_ACTIVE_CONVERSATION_CHANNEL_WISE = "GET_ACTIVE_CONVERSATION_CHANNEL_WISE.ftl";
		String MESSAGE_VOLUME_PROFILE_REPORT= "MESSAGE_VOLUME_PROFILE_REPORT.ftl";
		String GET_DISTINCT_BID_AGG_FOR_SURVEY_ID = "GET_DISTINCT_BID_AGG_FOR_SURVEY_ID.ftl";
		String GET_CONVERSATIONS_BY_BID_AND_SURVEY_ID = "GET_CONVERSATIONS_BY_BID_AND_SURVEY_ID.ftl";
		String GET_MESSAGES_BY_BID_AND_SURVEY_ID = "GET_MESSAGES_BY_BID_AND_SURVEY_ID.ftl";
		String GET_ROBIN_UNANSWERED_FAQ = "GET_ROBIN_UNANSWERED_FAQ.ftl";
		String GET_MESSAGES_BY_ID_FAQ_UPDATE = "GET_MESSAGES_BY_ID_FAQ_UPDATE.ftl";
		String GET_DEFAULT_FALLBACK_MSGS_FOR_MIGRATION =  "GET_DEFAULT_FALLBACK_MSGS_FOR_MIGRATION.ftl";
		String GET_MSGS_BY_DOC_ID = "GET_MSGS_BY_DOC_ID.ftl";
		String BULK_DEL_ROBIN_UNANSWERED_FAQ = "BULK_DEL_ROBIN_UNANSWERED_FAQ.ftl";
		String GET_CONVERSATIONS_V4 = "GET_CONVERSATIONS_V4.ftl";
		String GET_CONVERSATIONS_POLLING = "GET_CONVERSATIONS_POLLING.ftl";
		String GET_CONVERSATIONS_POLLING_WITHOUT_SCRIPT = "GET_CONVERSATIONS_POLLING_WITHOUT_SCRIPT.ftl";
		String GET_CONVERSATIONS_V5 = "GET_CONVERSATIONS_V5.ftl";
		String INBOX_ROI_REPORT = "INBOX_ROI_REPORT.ftl";


	}

	interface Assignment {
		public static String USER_ASSIGNED = "U";
		public static String TEAM_ASSIGNED = "T";
		public static Integer UNASSIGNED_ID = -100000;
	}

	public static final Integer INBOX_USER_ID = -77;
	public static final String DUMMY_FB_PAGE = "Fb_";

	interface BusinessService {
		String SCHEDULE_INSTANT = "Instant";
	}

	public static final String LOCATION_CACHE = "webchat_location";
	public static final String WEBCHAT_WEBSITE_CACHE = "webchat_website";
	public static final String WEBCHAT_MICROSITE_CACHE = "webchat_microsite";
	public static final String WEBCHAT_TEAM_CACHE = "webchat_team";
	public static final String WEBCHAT_CUSTOM_FIELDS = "webchat_custom_fields";
	public static final String CUSTOM_FIELD_TYPE = "text";
	public static final String WEBSITE_DOMAIN_CACHE = "website_domain";

	interface CrawlerEvent {
		String CRON = "cron";
		String API = "api";
	}

	interface ActivityTypes {
		String CLOSED_BY = "closed by";
		String NOTE_ADDED = "added a note";
		String ASSIGNED = "assigned this conversation to";
		String CONVERSATION_OPEN = "Conversation opened by";
		String UNASSIGNED = "marked this conversation as \"Unassigned\"";
	}

	public static final String THEMSELF_FIELD = "themself";
	public static final String EMAIL_VIA_INBOX = "email-via-inbox";
	public static final String PAYMENT_EMAIL_TEMPLATE = "payment_alert";
	public static final String SUBSCRIPTION_CANCELLED = "subscription_cancelled";
	public static final String CUSTOM_RECIPIENT_TYPE = "custom";
	public static final String RECIPIENT_TYPE = "business_user";
	public static final String UNSUBSCRIBE_CUSTOMER_URL = "/customer/communication/bulk/";

	public static final String PULSE_SURVEY_CONTEXT_CACHE = "PULSE_SURVEY_CONTEXT";

	interface MESSAGING {
		String REVIEW_WITHOUT_COMMENT = "The user didn't write a review, and has just left a rating.";
	}

	String GRAPH_TABLE_LABEL_SEPARATOR = " - ";
	public static final String FORMAT_MMM_DD = "MMM dd";
	public static final String FORMAT_MMM_DD_YYYY = "MMM dd YYYY";
	public static final String FORMAT_DD_YYYY = "dd yyyy";
	public static final String FORMAT_MM_DD_YYYY = "MM/dd/yyyy";

	String BUSINESS_NUMBER = "businessNumber";
	String BUSINESS_ID = "businessId";
	String WEBCHAT_INSTALLATION_STATUS = "webchat-installation-status";
	String CUSTOMER_ID_PREFIX = "c_id : ";
	String REVIEWER_ID_PREFIX = "rvr_id : ";
	String GMB_STATUS = "GMB_STATUS";

	String CHAT_TRANSFER_ALERT = "chat_transfer_alert";
	String DATE_FORMAT_CHAT_TRANSFER_EMAIL = "MMM d 'at' h:mm a"; // May 3 at 10:21 AM
	String ANNONYMOUS_GOOGLE_USER = "Anonymous Google User";
	String ANONYMOUS_USER= "Anonymous User";

	String ANONYMOUS_FACEBOOK_USER_FIRST_NAME = "Anonymous";
	String ANONYMOUS_FACEBOOK_USER_LAST_NAME = "User";

	Long WIDGET_CREATION_LIMIT = 10000l;

	public static final String JDBC_URL = "***********************************************************************";

	public static final String HOST_NAME = "host";

	public static final String DB_PORT = "port";
	public static final String DB_USERNAME = "username";

	public static final String DB_PASSWORD = "password";

	public static final String DB_NAME = "dbname";

	public static final String MESSENGER_MASTER_JDBC_URL = "************************************************************";
	public static final String MESSENGER_MASTER_HOST_NAME = "messengerMasterHost";
	public static final String MESSENGER_MASTER_DB_PORT = "messengerMasterPort";
	public static final String MESSENGER_MASTER_DB_USERNAME = "messengerMasterUsername";
	public static final String MESSENGER_MASTER_DB_PASSWORD = "messengerMasterPassword";
	public static final String MESSENGER_MASTER_DB_NAME = "messengerMasterDBName";
	public static final String MESSENGER_MASTER_DB = "messengerMasterDB";

	public static final String BAZAARIFY_MASTER_JDBC_URL = "************************************************************";
	public static final String BAZAARIFY_MASTER_HOST_NAME = "bazaarifyMasterHost";
	public static final String BAZAARIFY_MASTER_DB_PORT = "bazaarifyMasterPort";
	public static final String BAZAARIFY_MASTER_DB_USERNAME = "bazaarifyMasterUsername";
	public static final String BAZAARIFY_MASTER_DB_PASSWORD = "bazaarifyMasterPassword";
	public static final String BAZAARIFY_MASTER_DB_NAME = "bazaarifyMasterDBName";
	public static final String BAZAARIFY_MASTER_DB = "bazaarifyMasterDB";

	String CAMPAIGN_CUSTOMER_ID = "campaign_c_id : ";

	public static final int MStoMin = 60000;

	public static final Integer trajectorBusinessId = 749470;
	// Apple constants
	public static final String APPLE_STATUS = "APPLE_STATUS";
	public static final String ANNONYMOUS_APPLE_USER = "Apple message user";
	String APPLE_MAPS_BASE_URL = "https://maps.apple.com/";
	String RICHLINK_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/default-image.png";
	String MESSENGER_LOCATION_CACHE = "msgr_dashboard_location";
	public static final Integer CUSTOM_FIELD_SIZE = 1000;
	public static final Integer MOBILE_FCM_NOTIFICATIONS_BATCH_SIZE = 100;
	public static final String ROUND_ROBIN_ACCOUNTS_CACHE = "round_robin_accounts_config";
	public static final String DEFAULT_TIMEZONE_ID = "America/Los_Angeles";
	Integer GET_BUSINESS_CHAT_EVENT_LIMIT = 1000;
	String MESSENGER_CONTACTS_SECURE_MESSAGING_LINKS_CACHE = "mc_secure_links";
	String SECURE_MESSAGING_LINK_ENCRYPTION_KEY = "SECURE-CHAT";
	String SECURE_LINK_DEFAULT_BODY = "Hi %s, you received a secure message from %s. To view the message, click on the below link\n%s";
	DateFormat SERVER_TIME_FORMATTER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	String SECURE_MESSAGING_OTP_CACHE = "secure_chat_otp";
	String SECURE_MESSAGING_OTP_LOCK_PREFIX = "secure_message_otp : ";
	Integer SECURE_MESSAGING_OTP_EXPIRY_SEC = 30;
	String SECURE_MESSAGING_OTP_MESSAGE_BODY = "%s is your OTP for verification of phone number with %s. It is valid for %s seconds.";
	String SECURE_MESSAGING_SESSION_TOKENS_CACHE = "sm_tokens";
	String BUSINESS_HOURS_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/clock.png";
	String SPOKEN_LANGUAGE_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/pin-address.png";
	String PAYMENT_OPTIONS_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/dollar.png";
	String CONTACT_DETAILS_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/link.png";
	String LOCATION_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/location.png";
	String WEBSITE_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/web-link.png";
	String TYPES_OF_SERVICES_INTENT_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/services.png";
	String SUGGESTION_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/info.png";
	String LIST_PICKER_DEFAULT_IMAGE_URL = "https://d3cnqzq0ivprch.cloudfront.net/prod/css/images/email/list-picker.png";
	String LIST_PICKER_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAE2SURBVHgB7ZYxDkRQEIbHZmsnUOmolCqdTucELsAFqHQcwglcgE7FBZzACVxgd+clXuxWs+xks5v5EskbeeHLi/mNcXsAP8QFfgwR5kaEuRFhbkSYGxHmRoS5uVI2lWUJTdOotWmaUNc1hGGoarzf9z0cxbIsqKqKvN+gjJee58G6rrpOkgSKolDrIAhgWRY4wzAMSpwCSbjruqdTzLJMvwBl27aFo+Bz4jgm7zdkgGdGhLkhxdprdOV5Dq7rqvU8zzryjuD7/uebzrbtpxpfgFmMRFGkpM/wTqyRThhjbB9dmMMbaZqe/nFQZRGJNW5EmBtS043jqJsOpzVsuv0sMU0THMVxHB2RFP4z1jDc8ZT39QbOxfvR813YYm2befGTwOtbSA5zI8LciDA3IsyNCHMjwtzcAdkQhcVa7VqyAAAAAElFTkSuQmCC";
	String BUSINESS_HOURS_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJgSURBVHgB7ZgxkgFBFIbfbm3sAiRCEiGJUCQUCSWEIjIJDoBI5gJcgAPgADgADsAFev1d9VTvLON1z8xuqeqvqoua6un5+82b9/6ZD3WD3ohPejO84KTxgpPGC04aLzhpvigmrtcr7fd7OhwO+j9GKpWifD5PuVyOMpkMxUFkwafTiWazGS0WCy3yGaVSiWq1mh5R+IjiJcbjsR4miCoG4EibIOLT6dQ94sqBy+Wi6vW6ymazehQKBTUajdR6vf4193g8qvl8rsrl8o/5u91OueAkuFqt3i/e6XT0BkwgBiKD4BjERhFtLbjf79/FIqqP4OgjukFwjKMN0Y/mhGFV1pbLpX7AQKPRoHa7TbYgd5HDyHPkd7fbtTrfSvBkMrlftNfrkSt48LBhsNls9JAiFowaiwFcIhsEgrmarFYr8XliwaizABeJWkuD6/DaEsSC0cEAbmdcoJkA5DIakASxYF4QbTYuzLWkgq1bM+cdwK0MdjoQ1qKfcT6fRfOsBZtiwm4lKom5uVek02nRPLFg5C7EmQLxpHN5csFcS7o5cQ5zBLbbLcUFlzO2oRLEgiuViv5FGtgU+jBYMK8tQSwYJYjL0HA4pKigxXNKWNV1G+MB+8jGZzAYKFdgeNi1tVotq3Ot3Vqz2Xzp1sKI6tasBcP7mmYckQ764WfgDnFkMW7uT9niZODNKGHgP8z5o2hhMxAafEN5ZPAlOL/ToVqgy7E/ZswSFazbgP2wqyeJ9BLKoiD8leNiD/yvb80mEM7fJTiqiDYiWiwWY3N5sQn+K/y3taTxgpPGC04aLzhpvgGF4xHqCRnlkQAAAABJRU5ErkJggg==";
	String SUGGESTION_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKHSURBVHgB7ZixsjExGIZz/vlrN0CjpFHSKGmUKjqNkoZSgwtApdOpuABcAC4AF4AL4Aby/29mPpOzJ1lJdnPOnJl9ZjLZNbHeffMl3xcf/D/sF/GH/TISwb5JBPsmEeybRLBv/rIYeD6f7Hw+s8vlIq7RUqkUy+fzLJfLsUwmw+IikuDb7cYWiwVbr9dCpI5SqcTq9bpoUflwrSWm06loMnAVDZDTMnB8Pp9Hc5xb8ng8eKPR4NlsVrRCocAnkwnf7/dfxl6vV75arXi5XP40/nQ6cVesBddqtdeP9/t98QImQDjERhVtJXg4HL7EwlUVm81GiFMBx8ltiMa9N8EQQmIhXMV2u337QnCWnEZo2WK8D89mM9FjwQwGA+UY7BqqaxksvFarJa4Ph4NoVpi8FVwh53TTTcDZXq8XOt2Ie3JZN1s6jART7OJH4sL1mUaJAxmMplMHQgAJhECSCNtvkUyQdLBX47ume7ORYIpHpFkdEBtMJN1uVztefpaNYKvih7KYCiyk5XLplH7v97vxWCvBYfUCXgbT7JJ20+m08VgjwRS7uq3KBflZYTMXxEgwOXA8Hllc7HY70VMZaoqR4EqlInqEhPVGr4EE07NNMRKM2EQD4/FYOQZbVLPZfG1t6HGPz1VjKSRsF6nxout0OqLHyUIlmtwnIejlewL3tP1Vq9WXEcbYZJl2u/22uAnjW6s1gBpALsZHo5FxPYwCn+oHNFR2LlgX8LJLaLhGQaRyCy8DocETyrsCKgynMx3iFXEYXFDyFoVYDcYvkgrOdDbbWBDnQyiJgnC56FFBNfCPnpplIJz+lyBX4TYcLRaLkRwNEovg7yT5b803iWDfJIJ9kwj2zT8leV3NbdEfxQAAAABJRU5ErkJggg==";
	String SPOKEN_LANGUAGE_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH4SURBVHgB7Zg/0gExGIdf33wtF6DRapRUOi6gotOpqOhULoALUKm4gFKDC7gADoAD5PPLTHaWj91sktfMzuSZyWzsZtezb978ISMeUIr4oZThhbnxwtx4YW68MDdeWIf9fk/dbpfa7Tadz+dE92a+uZdYr9eyQFhRrVZpuVxqP+OXmLndbjSfz2VB3RY24U+iiGiz2aTBYCA/o54E58LISUii65VoLpeTYp1OhwqFAg2HQ3ke9aTCJByx2+1Eq9USxWIxKOVyWUwmE3G9XoN2p9MpuL5arURSrCOMATSbzZ4GEiKKaKKgHgZtjaNLFinxacRDsl6vv70H6fKIqqyjnQmJhKMGUq/Xk8co8IIAUf/0UnFoC+PLxuPxkyi6FCVONPwMAFmkhAnaC0etVntalRqNBmWz2bdtL5fLv9SArJrKttutsbBxDm82m8jreJmw8HQ6lUf0iKks0BbG8qmzWh0OB9kT9/s9OIfoqt7p9/tkg7YwojIajWLbodshl8/ng3N4UWCTuwrnu7VXIUx7x+NR1k2nsjDs28vFYiGPpVJJezaJglUYqaEGp4voAlZh22X4HWzbS8zFatm2nRnCsEVYybqMLmAfdC6jC1iFEd1KpUIucS6M7scUhh0Zomu7ULzy1V/NLvB/pHDjhbnxwtx4YW5SJ/wH2j9rTrKI/joAAAAASUVORK5CYII=";
	String PAYMENT_OPTIONS_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALQSURBVHgB7ZgxsilBFIb7vnqxDZAISchIyEiEIjIJoQSZBAsgI2EDLACZBAvAArAANtDP31VH9Z3bbbpnzH2lar6qrntVj56//zl9zhlf/AH7IP6wDyMUHDSh4KAJBQfNxwn+y97E/X5nx+ORnU4n8T9GJBJhyWSSJRIJFovF2DvwLfhyubDZbMYWi4UQqSObzbJyuSyGH778lObRaCSGDFzFAOS0DBwfj8feHeceuN1uvFKp8Hg8LkYqleLD4ZBvt9sf157PZz6fz3kul/t2/eFw4F7wJLhUKj1v3m63xQZMgHCI9SPaWnCv13uKhau2wHFyG6Lx2QYrwcvl8ikWwnWsViter9e183CWnEZo2WAlmEIBDumYTqdCBARhUzoH8XRo86rY9y0YrtANEIsqaEMQ6xariHu65tXTcmJc6ZBnKW2pcinmUTiazSbrdrsifW02GzE3GAx+XC+vQ2ubYCwYFQxAiAoUEOc8ci02UCgUlN9BMQHI1fR9N4wrHS2IMqsik8mIv/1+/1vhqNVq2jXltbC+STGxbn5IjBO4hQoGEBoY6XSadTodI/eu1yszwVrwq36hWCyKuEUMwy2EwuOACtFuRKNRZoKxYIpNnVvr9frZV+ApQPBkMhHO73Y75XfktXRPzolxDJMD+/1eOQ9R6NoolgEdJp0YbJLE6g6zE2OH6aRDhMoxHC64Wq1WhdOI4Xw+LwTrDh4J1mURJdwC6tBQIFSgqqEIYB5FodVqaasYKmKglQ5gYbrJI31pr0MlfNUjYGNU5RqNBrfBultDU2PSrelazl/t1kiI3IzDadN+GE+InMVAV2eLpwZedom6N4SByi1sBkKdbyi6BsoNz+90yBbIBkhlMnKKQoZw5m1kElRE0zTmxNdLKImCcLeOCwKR3v7rW7MMhNPvEuQqVTwUE6+OOnmb4N8i/G0taELBQRMKDppQcNB8nOB/AyKKQWlAe/YAAAAASUVORK5CYII=";
	String CONTACT_DETAILS_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKESURBVHgB7ZgxsgFBEIbnvXqxE0iEJEISoUgoIhMRkSCTcAFEIjIRB8ABcAAugAPgAPv8U9VbY2uXnjWjaqv2q5qaVTv4d6an/579cR6ICPErIkYs2DaxYNvEgm0TOcF/4kvcbjfZQDKZFGGxLni324nxeCx7AoLz+bxoNpva4n9sWvNgMBCz2SzwPsROJhORyWQEF2uCu92uWCwW8hqzWS6XRS6XE+fzWSyXS9lAIpEQ8/mcL9qxQKfTcVKplGzD4dB3zOl0cgqFghyD/nq9sn7buGCOWGK73bpjp9Opw8GoYB2xRKVSkePRczCWh0ejkRuzrVZLNg7FYlH2iG0ORgRjA0EwILHH41FsNhthHMcAtHna7bb8fDgcnGw2626o9Xod+N16vf7dkMBM0nLWajXZ47O61A9RvrONe4+HkddIe1ZnGJsKqQmzRxvNi5q6MONq6vKmNS6hZhjxiqbWB37AyWAKMAeMI9fDzFarVdnTGC7agkksuFwuIp1Ou/fUekEVTeFBYdFoNJ7E6tQTWoJVscgEEAJLpT9EkeMHrBlQrMOiw4iVcGPnkWMDTWG1Wrn3YB5em4WLURzTbyGGw8AW7E1dXvr9visaYyEc6Q2QA3JT1ytYIYGlpOUMSj+9Xs91NxqLcME1OSA7db2C81RqkfKuqsJSI0ToOkzqeoXxEwc2EZqauqjmNQErJNSdvN/v3473E/vJOe4J7lJQGfiu2Pa6G208U7AFq3FcKpV805JtsUDrTKcaB5YYxQ6c7n6/S5dDmQkL1j6naaB9CFVF+xHmJKxDqFMzNhNEozZQX44gz2LWMcO2+PiYTyHwLay+SLFB/PbSNrFg28SCbRM5wf9yxxl3u0Q/UwAAAABJRU5ErkJggg==";
	String LOCATION_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJESURBVHgB7ZixkgFBEIb7ri7lBUikEiEJmYhMRCaRymQimQiRTCYiE5FJ8AI8AC+AB9i7f6p6aotdN2Nmrmrr5quaWtas+aenp7tnP4IfKEF8UsLwgl3jBbvGC3ZN4gR/kSVutxstl0s6Ho+UTqcpn89To9Eg23zYyHSXy4VarZa4hslmszSfz8XVFsYuwWKr1Sptt1vq9/tPv1klMGSxWAS5XC64Xq/yXq1WE/e4rdfrwBbGFt7v9+IK3wWw6qNrcB8bWNt0WPpSqSSEYwO6wtjCiAYMLBklNtzHFOMoAYH1ev3JDRhECGxGWxhbGDF3Op1Ghi4Oa1YJLHE+n4PRaCQjAz6HI4ctrKVmWLNYLMrvyHKwvm188eMaL9g1/6MeRkbbbDZ0Op1EKkYmi4u3lUpFJBf0QRRB9DApN7UEQ+hkMtEuZiAYz6CNx2NRcwyHw7eEKwvGQGgA8RX1LwaG1XjgqLiLtIxVwGpAME4luMLy3W5XNC1UsgvXvGidTic2g+E+94sDGTFcL+92u0AHJcE8QLlc/rUv+jSbzZd9MLFCoSANoIOSS3DJqJJqVTZV+H/u9zvpoBTW4K8Avsh+HAd88tVpGZPv9XrSCOH6QwWlevix5oUF2+22GEy1OMdkEQpns5kUi2dXqxXpoFzAY5DBYCB2eRh+B4FrKpV6eg7RARN9PIlgJTBp3YpO+8SBwWGlw+EgD56qcBh8RyhjdESC1TjG4nPUixS0TCYjV8EUK29+/hJfrbnGC3aNF+yaxAn+Boe/elNgkv9RAAAAAElFTkSuQmCC";
	String WEBSITE_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMwSURBVHgB7Zg9TiNBEIWb1cZcABJCSCCDBDJIICOCjARCEiAjgZAEMiLIiOAAwAFsH8D2AWwfwPYBZvlKelZpFtvdYw8S0jypNT01/fNcU1X9xgvZF8Ivwp/wy1ARLhsV4bJRES4bFeGy8TfMCYPBILRardBut61PW1xcDGtra2F1dTUsLy+HeWBmwt1uNzw/P4e3tzcjOQ5bW1vh8PDQ2ixYmEVLPDw8WAN4cxxh/wyPPz4+FvZ4oRhm8+PjYyMLmfPzc2sitLu7a31vu7u7M5KEzcHBgV0LISuA/f39bGVlxa6dTsfa9va2tX6/n11cXNhzcHNzY/3X11e7v7+/t/v19fWs2WxmqUj28O3t7cg7eq2NRsNiGY/icQ/ZmOfn6C0xLwVJSffx8WEJxqabm5uWaNhEkmu9Xg/D4dDuIUPVICSwb2xsGFE//+rqKry8vERzSEo6xR7xSLZDCM99fn5GzYfoycnJqFKwHmtAmCoShdjYId6IPeLUg9jE/vT0lL2/v9tVMU6f58wlZrF78JxxxHksogkredjkO7tPIJ90wtnZmZH2IEGx5e2TEJ10xCIg9jx6vZ5didNJWFpasvj1SUbMEyZ5+yREJ50WZHESyNvZ1Nv0I7xNiUgO5En7daYhOul2dnaSS1AKlMjTkKwlWNiD0w6vX19fj2yUK7zrx1L+qCaM87VaYwmZGEQTJkbxMDHsXx0kIOO9o1DwNh02HNt+PoRB/sAZh+ikkwfyNVf2aeGipPVklQ+SoXMlLEGTJywCEII0BHzS0VQF8qS0ltaOQXRIcBLRRIK+L0dfdfa/OWiF/I/TXCBpmqKRk45mNoMEG7MJusJrYCWUEgzdi7fp+xLHfLzK/L29PRsXjSwRp6endorRjo6OslqtNpKM9L876SQ9kaEaK4mJLQXJ8lJCHEh5IWjwLMor/9XBa5f0ZKyvBn6t0jwMJNglhhA4EjKXl5fmeYl2jcH7suNZCfpUFP6mw5N4jzgEeGpcafPP6BOzsWUsj5k+QgFEIK4DYBwg6LVwUcxMWIC4/peQN6XGOB2LejSPuRH+KVT/rZWNinDZqAiXjYpw2fgHF6B2sksq2ioAAAAASUVORK5CYII=";
	String TYPES_OF_SERVICES_INTENT_DEFAULT_IMAGE_BASE_64 = "iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJWSURBVHgB7ZcxkgFBFIbfbm3sBBIyEiGJUERGIpQgIuEELsAFyERcgEzCBXAALsAFev1d9ababE9Vz8zrrVI1X5Wlpnv4983fr//+Ui/og/imDyMT7JtMsG8ywb7JBD+fT7pcLnQ6ncgLSojH46Hm87mqVCqqUCjoV71eV5vNRknyhT+Uku12S7PZTFfXRj6fp/F4TO12m1KjUnA8HlW32w0qiuqiyqj27XbTn3kMr8FgoK+nIZFg/KgplMWez2fr3Mlk8jZ3Op0mFh7LEnjki8WCVqtVcK1cLutFxnQ6HRqNRtoGJvf7nYbDYTA3l8tRr9fTVvFiCVTPXFCoMCxhqzgWG+xgA4sQ4+bcONV2FtxsNt/EuoqJ6hLm9/X7fSUu2PQgi+EKuwhnf+Mec4zHXXH2cLFY1O+NRoP2+72TZ+F3tDyb3+HharWqvwv3Hg4HEvUwVwN+i7PyUdHw08G9aH08FqfCiQQzu93uz6NfLpd6zLbzmQvV/Gf+TTBj82x4i7b5PYlgkfCDLXe9Xgc9Ff5Fz4ZPcQ3+rNVqJMEPCcF5AYuy1WrpaxAK0ZKIx0tToLRYkAV438QWjAUlxfV61e9xrOMsmFc5Ehd2sDRw6kPoB6VSyf1m1/6H/usSbDCP59jAxmL2aIQgL2mNCe9e4R+MEhwOPfgO3hXjkPjEEZUlwoKR0qKOUUlIdQi1JTKkt5dV9GecKMzTCXZEbC7hZBcHsVMzhEd1ECxYRFCR7VkJYgtBUSE/KSIVNkHLQihHb0WukEZcsG+yLOGbTLBvMsG++TjBv3gAHDk3r9C8AAAAAElFTkSuQmCC";
	List<ImageData> DEFAULT_IMAGES_DATA = new ArrayList<>(Arrays.asList(
			new ImageData(LIST_PICKER_DEFAULT_IMAGE_BASE_64, "1"),
			new ImageData(SUGGESTION_DEFAULT_IMAGE_BASE_64, "2")));
	String FB_USER_ID_PREFIX = "fb_user_id : ";
	String INSTA_USER_ID_PREFIX = "insta_user_id : ";
	String MESSAGE_ID_PREFIX = "message_id :";
	String SECURE_MESSAGING_PHONE_MASKING_CHARACTER = "•";
	Integer MASKING_LENGTH = 4;
	Integer SECURE_MESSAGING_SLUG_LENGTH = 4;
	public static final String CHAT_TRANSCRIPT_ACCOUNTS_CACHE = "chat_transcript_accounts_config";
	String ES_INDEX = "es_index";

	public static final String ROBIN_AUTOREPLY_CONFIGURATION = "robin_autoreply_config:";

	public static final String TEN_DLC_STATUS = "ten_dlc_status";

	Integer UPDATE_ROBIN_CONFIGURATION_LIMIT = 1000;

	// BIRDEYEV2-5104
	List<Integer> EXCLUDED_SOURCE_IN_UPDATE_FILTERS = Arrays.asList(0, 5, 101);
	List<String> EXCLUDED_MESSAGE_TYPE_IN_UPDATE_FILTERS = Arrays.asList(MessageType.INTERNAL_NOTES.name(),
			MessageType.EVENTS.name(), MessageType.ACTIVITY.name(), MessageType.REVIEW.name(),
			MessageType.SURVEY_RESPONSE.name());
	String SMART_INBOX_FILTERS_CACHE = "smart_inbox_filters";

	List<ActivityType> EXCLUDED_CONVERSATION_SUMMARY_ACTIVITY_TYPES = Arrays.asList(ActivityType.UNASSIGNED,
			ActivityType.ASSIGNED, ActivityType.REASSIGNED, ActivityType.CLOSED, ActivityType.REOPENED,
			ActivityType.UNREAD, ActivityType.AUTO_ASSIGN, ActivityType.AUTO_UNASSIGN, ActivityType.SMS_SUBSCRIBE,
			ActivityType.SMS_UNSUBSCRIBE, ActivityType.LIVECHAT_START, ActivityType.LIVECHAT_END,
			ActivityType.LIVECHAT_TIMEOUT, ActivityType.LIVECHAT_RESTART, ActivityType.TEAM_ASSIGN,
			ActivityType.WEBCHAT_START, ActivityType.LIVECHAT_START_WITH_TEAM, ActivityType.WEBCHAT_START_WITH_TEAM,
			ActivityType.ADD_NOTE, ActivityType.VIDEO_CONV_START, ActivityType.VIDEO_CONV_END,
			ActivityType.IMPORT_MESSAGES_INPROGRESS, ActivityType.IMPORT_MESSAGES_COMPLETED, ActivityType.EMAIL_START,
			ActivityType.REFERRER_LEAD_GEN, ActivityType.REFERRAL_LEAD_GEN, ActivityType.THANK_YOU_NOTE_SENT_REFERRER,
			ActivityType.CHAT_LOCATION_TRANSFER, ActivityType.CONTACT_BLOCKED, ActivityType.CONTACT_UNBLOCKED,
			ActivityType.UPDATE_PAYMENT_METHOD, ActivityType.APPLE_OPT_OUT, ActivityType.MISSED_CALL,
			ActivityType.SECURE_CHAT_INITIATED, ActivityType.SECURE_CHAT_STARTED, ActivityType.SECURE_CHAT_ENDED,
			ActivityType.GMB_AUTO_LOCATION_SELECT);

	Set<ActivityType> APPOINTMENT_ACTIVITY_TYPES = new HashSet<>(Arrays.asList(ActivityType.APPOINTMENT_COMPLETED,
			ActivityType.BOOKED,
			ActivityType.CANCELED, ActivityType.NOSHOW, ActivityType.REMINDER_SENT,
			ActivityType.REMINDER_SENT_AUTOMATION, ActivityType.RESCHEDULED, ActivityType.FAILED,
			ActivityType.CONFIRMED));

	Set<ActivityType> PAYMENT_ACTIVITY_TYPES = new HashSet<>(Arrays.asList(ActivityType.PARTIALLY_PAID,
			ActivityType.CARD_ON_FILE_PAYMENT,
			ActivityType.MANUAL_CARD_ENTRY_PAYMENT_REQUEST, ActivityType.PAYMENT_COMPLETED,
			ActivityType.PAYMENT_FAILED, ActivityType.PAYMENT_FULL_REFUND, ActivityType.PAYMENT_PARTIAL_REFUND,
			ActivityType.PAYMENT_PROCESSING, ActivityType.MARKED_AS_PAID, ActivityType.TERMINAL_PAYMENT_REQUEST));

	Set<ActivityType> PAYMENT_APPOINTMENT_RECEIVE_DIRECTION_ACTIVITY_TYPES = new HashSet<>(
			Arrays.asList(ActivityType.PARTIALLY_PAID,
					ActivityType.PAYMENT_COMPLETED, ActivityType.MARKED_AS_PAID, ActivityType.BOOKED));

	String CONVERSATION_SUMMARY_MESSAGE_BODY_CACHE = "conversation_summary_message_body";

	String AGENT = "Agent: ";

	String CUSTOMER = "Customer: ";

	String INTERNAL_NOTE_MESSAGE_BODY = "Posted an internal note in the chat\n";

	String CONVERSATION_SUMMARY_DATE_FORMAT = "dd MMM yyy hh:mm a";

	Set<String> INTENT_TYPE_STRINGS = Stream
			.of(IntentType.INTENT_TYPES_STRINGS, Arrays.asList("NO_REPLY", "DEFAULT_FALLBACK", "FAQ", "WELCOME"))
			.flatMap(Collection::stream).collect(Collectors.toSet());

	String APPLE_LOCATION_TEAM_MAPPING_CACHE = "apple_location_team_mapping";

	public static final String GPT_RATE_LIMIT = "GPT_RATE_LIMIT";

	public static final String BUSINESS_HOURS_OLD = "[Business Hours]";
	public static final String BUSINESS_HOURS_NEW = "[Business hours]";
	public static final String BUSINESS_TEXTING_NUMBER_OLD = "[Business Texting Number]";
	public static final String BUSINESS_TEXTING_NUMBER_NEW = "[Business texting number]";
	public static final String BUSINESS_SERVICES = "[Business services]";
	public static final String BUSINESS_SERVICES_CAP = "[Business Services]";
	public static final String BUSINESS_LANGUAGES = "[Business languages]";
	public static final String BUSINESS_LANGUAGES_CAP = "[Business Languages]";
	public static final String BUSINESS_PAYMENT_OPTIONS = "[Business payment options]";
	public static final String BUSINESS_PAYMENT_OPTIONS_CAP = "[Business Payment Options]";

	public static final String APPOINTMENT_LINK = "[Appointment link]";

	String REACT = "react";
	String UNREACT = "unreact";
	String STORY_MENTION = "story_mention";
	String STORY_REPLY = "story_reply";
	String GET_INSTAGRAM_MESSAGE_ID_CACHE = "instagram_message_id";
	String THIS_MESSAGE_WAS_DELETED = "This message was deleted.";
	String YOU_DELETED_THIS_MESSAGE = "You deleted this message.";

	String Twitter_USER_ID_PREFIX = "twitter_user_id : ";
	String WHATSAPP_USER_ID_PREFIX = "WA_user_id : ";

	public static final String WEBCHAT_KEY_PREFIX = "Web-chat";

	List<String> SUPPORTED_VIDEO_FORMAT = Arrays.asList("mpeg","mp4","3gpp","mov", "ogg", "avi", "webm","3gp");

	public static final String TIME_ZONE_ID = "time-zone-id";
	public static final String BUSINESS_PROFILE_CACHE = "msgr_business_profile_cache";
	public static final String BUSINESS_CUSTOM_TOKEN_CACHE = "msgr_business_custom_token_cache";

	// Business features constants
	public static final String CONTACTS_TEXT_CATEGORIES_ENABLED = "CONTACTS_TEXT_CATEGORIES_ENABLED";

	public static final String BUSINESS_DETAILS_CACHE = "msgr_business_details_cache";

	public static final String ANONYMOUS_CUSTOMER_DOMAIN = "@anonymous-webchat.com";

	public static final String WEBCHAT_GREETING_MESSAGE = "Hello! Greetings from [Business Name]. How can I assist you today?";

	public static final String APPOINTMENT_CONFIRMATION_CONTEXT = "appointment_confirmation_context:";

	public static final String 	WEBCHAT_APOLOGY_MESSAGE = "Apologies, we do not have the answer for this question, feel free to ask other questions.";

	public static final String PROPERTY_ERROR_CODES_TO_SKIP_UNSUBSCRIBE  ="error_codes_to_skip_unsubscribe";

	public static final String DEFAULT_SKIP_TWILIO_ERROR_CODES ="30005";

	public static final int CONTACT_NAME_MAX_LENGTH =50;

	public static final String PROPERTY_DELAYED_EVENT_MAX_RETRIES  ="delayed_event_max_retries";

	public static final String DEFAULT_DELAYED_EVENT_MAX_RETRIES ="3";


	public static final String PROPERTY_MESSAGE_BODY_MAX_LENGTH  ="message_body_max_length";

	public static final int DEFAULT_MESSAGE_BODY_MAX_LENGTH = 1500;

	public static final String MARKETING = "marketing";
	public static final String FEEDBACK = "feedback";
	public static final String SERVICE = "service";

	public static final String REPLY_VIA_EMAIL_KEY = "REPLY_VIA_EMAIL";

	public static final int MEDIA_CAPTION_MAX_LENGTH = 1024;

	public static final String WABA_ID_PREFIX = "waba_id : ";
	
	public static final String WA_UNSUBSCRIBE_ERROR_CODE  ="wa_unsubscribe_code";
	
	public static final String DEFAULT_WA_UNSUBSCRIBE_ERROR_CODE ="131050";
	
	public static final String WA_NUM_NOT_AVAILABLE_ERROR_CODE  ="wa_num_not_available_code";
	
	public static final String DEFAULT_WA_NUM_NOT_AVAILABLE_ERROR_CODE ="131026";


	public static final String KEYWORD_NOMKT = "NOMKT";
	public static final String KEYWORD_NOFB = "NOFB";
	public static final String KEYWORD_NOSERV = "NOSERV";
	public static final String KEYWORD_JOINMKT = "JOINMKT";
	public static final String KEYWORD_JOINFB = "JOINFB";
	public static final String KEYWORD_JOINSERV = "JOINSERV";

	 // Property keys for configurable templates
	public static final String UNSUB_TEMPLATE_KEY = "sms_unsub_auto_reply_template";
	public static final String RESUB_TEMPLATE_KEY = "sms_resub_auto_reply_template";

    // Default templates
	public static final String DEFAULT_UNSUB_TEMPLATE = "You have unsubscribed from %s messages. Reply %s to resubscribe.";
	public static final String DEFAULT_RESUB_TEMPLATE = "You have been resubscribed to %s messages. Reply %s to unsubscribe.";

	public static final String SHOW_ALL_VOICE_CALLS_ACCOUNTS = "show_all_voice_calls_accounts";

	
	public static final String WEBCHAT_DEFAULT_MOBILE_VIEW="fullView";
	
	public static final String FIRST_OUTBOUND_MESSAGE_CACHE = "first_outbound_message";
	
	public static final String STOP_UNSUBSCRIBE_TEXT ="stop_unsubscribe_text";
	
	public static final String STOP_UNSUBSCRIBE_TEXT_DEFAULT ="Txt STOP to unsub.";
	
	public static final int UNSUBSCRIBE_CACHE_TTL_DAYS = 5;

}
