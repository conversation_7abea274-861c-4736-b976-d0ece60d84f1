package com.birdeye.messenger.constant;

import java.util.HashMap;
import java.util.Map;

public class MessengerConstants {
	public static final String FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public static final String TWILIO_ACCOUNT_SID = "**********************************";
	public static final String TWILIO_AUTH_TOKEN = "e59b877c534833ec50650737f73ee240";
	public static final String SMS = "sms";
	public static final String FACEBOOK = "facebook";

	// User constants
	public static final Integer AUTO_CAMPAIGN_USER = 0;
	public static final Integer AUTO_REPLY_USER = -1;
	public static final Integer VOICECALL_USER = -2;
	public static final Integer ROBIN_REPLY_USER = -10;
	public static final Integer FACEBOOK_PAGE_USER = -8;
	public static final Integer AUTO_REPLY_LIVE_CHAT_USER = -11;
	
	public static final String NO_TRANSCRIPTION_MESSAGE = "Voicemail transcription is not available";
	
	public static final String MISSEDCALL_CONVERSATION_MESSAGE = " called but did not leave a message";
	public static final String TMP_FOLDER = "/tmp/";

	public static final String CAMAPIGN_EMAIL = "/execute/messenger/email/execute";
	public static final String FEEDBACK_MESSAGE_YES = "That helped!";
	public static final String FEEDBACK_MESSAGE_NO = "Talk to an agent";
	public static final String OKAY_MESSAGE ="Give us a moment while we look for someone available to help you.";
	public static final String FEEDBACK_RESPONSE_YES = "Great! Let me know if you have any more questions.";
	public static final String FEEDBACK_MESSAGE = "Was your question answered?";

	public static final String CONTACT_US_LEAD_DEFAULT_MSG="You have a new lead";
	public static final String TALK_TO_EXECUTIVE = "I want to talk to someone";
	public static final String APPLE_ROBIN_OFFLINE_MESSAGE = "We're closed right now but we got your message. We will reach out to you when we are back.";
	public static final String APPLE_ROBIN_ONLINE_MESSAGE = "Give us a moment while we look for someone available to help you.";
	public static final String APPLE_ROBIN_DEFAULT_OFFLINE_MESSAGE = "I am unable to understand your query. We're closed right now but we got your message. We will reach out to you on your when we are back.";
	public static final String APPLE_ROBIN_WELCOME_MESSAGE = "Hi, I am Robin, a virtual assistant to help you with your query.";
    public static final String APPLE_ROBIN_SUGGESTION_HEADER = "What are you looking for, click here to choose an option";
    public static final String APPLE_ROBIN_SUGGESTION_SUB_HEADER = "I found some topics that may be helpful";
	public static final String APPLE_LIST_PIKER = "LIST";
	public static final String APPLE_QUICK_REPLY="QUICK";
	public static final String DEFAULT_SUGGESTION_RESPONSE_BODY = "SUGGESTIONS SENT BY ROBIN";
    public static final Integer MAX_LIMIT_TOP_FAQS = 6;
	public static final String AUTO_REPLY_INSIDE_BUSINESS_HOURS = "Thank you for contacting us. Someone from our team will reach out shortly.";
	public static final String AUTO_REPLY_OUTSIDE_BUSINESS_HOURS = "Thank you for contacting us! We are currently out of business hours, but we’ll get back to you as soon as we can.";

    public static final Map<Integer, String> DEFAULT_USER_ID_AND_NAMES_MAP = new HashMap() {
        {
            put(AUTO_CAMPAIGN_USER, "Auto campaign: ");
            put(AUTO_REPLY_USER, "Auto reply: ");
            put(VOICECALL_USER, "Voice call: ");
            put(ROBIN_REPLY_USER, "Robin: ");
            put(FACEBOOK_PAGE_USER, "Facebook page: ");
            put(AUTO_REPLY_LIVE_CHAT_USER, "Auto reply live chat: ");

        }
    };
    
	public static final String FEEDBACK_MESSAGE_YES_FRENCH = "Merci pour l’aide";
	public static final String FEEDBACK_MESSAGE_NO_FRENCH = "Finaliser avec un agent";
}
