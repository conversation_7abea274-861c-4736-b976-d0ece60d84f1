package com.birdeye.messenger.exception;



import lombok.AllArgsConstructor;


/**
 * 
 * Format : [AppName-Component-StatusCode]  => E.g. INBOX-007-2146
 *  - AppName: the name of a system or application e.g INBOX
 *  - Component short name or code: code of modules present within the system, such as PAYMENT, CHAT, REVIEW. Helps in quick identification
 *  - Status code: error/HTTP status codes.
 *  
 *  
 * <AUTHOR>
 *
 */


@AllArgsConstructor
public class ErrorCodeGenerator {
	private final String appName;
	private final ComponentCodeEnum componentCode;
	private final Integer httpStatusCode;

	public String get() {
		return String.format("%s-%s-%s", appName, componentCode.getCode(), httpStatusCode != null ? httpStatusCode : "XXX");
	}
		
	public static ComponentCode builder() {
		return component -> status -> new ErrorCodeGenerator("INBOX", component, status);     
	}
	
	public interface ComponentCode {
		ErrorCodeGenerator.HttpStatusCode component(ComponentCodeEnum componentCode);
	}
	
	
	public interface HttpStatusCode {
		ErrorCodeGenerator code(Integer httpStatusCode);
	}


	@Override
	public String toString() {
		return get();
	}

	public ComponentCodeEnum getComponentCode() {
		return componentCode;
	}

	public Integer getHttpStatusCode() {
		return httpStatusCode;
	}	
}
