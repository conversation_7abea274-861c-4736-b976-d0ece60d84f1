package com.birdeye.messenger.exception;

import java.io.Serializable;

import com.birdeye.messenger.enums.ErrorCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Error implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String code;
	private String type;
	private String message;
	
	public Error(String code, String message,String type) {
		this.code = code;
		this.message = message;
		this.type = type;
	}
	
	public Error(ErrorCode errorCode,String type) {
		this.code = errorCode.getCode().toString();
		this.message = errorCode.getErrorMessage();
		this.type = type;
	}

}
