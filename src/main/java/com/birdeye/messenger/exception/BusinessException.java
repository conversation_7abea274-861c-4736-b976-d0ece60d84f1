package com.birdeye.messenger.exception;


import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;
/* Exception is triggered when an API call to the core service encounters an error or is unsuccessful */
@Getter
public class BusinessException extends NonActionableException {

    private static final long serialVersionUID = 1L;

    private ErrorCode errorCode;
    
    protected HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;

    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public BusinessException(ErrorCode errorCode, Throwable e) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(ErrorCode errorCode, String message, Throwable e) {
        super(message, e);
        this.errorCode = errorCode;
    }

}
