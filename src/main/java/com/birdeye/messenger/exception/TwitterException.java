package com.birdeye.messenger.exception;

import com.birdeye.messenger.enums.ErrorCode;
import org.springframework.http.HttpStatus;

/*Exception thrown when Twitter messaging API return error while sending or pulling data from Twitter */
public class TwitterException extends NonActionableException{
    private static final long serialVersionUID = 1L;

    public TwitterException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public TwitterException(ErrorCode errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
    }

    public TwitterException(Throwable e, ErrorCode errorCode) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }

    public TwitterException(ErrorCode errorCode, HttpStatus httpStatus) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        super.status = httpStatus;
    }

    public TwitterException(ErrorMessageBuilder errorMessageBuilder) {
        super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
        this.errorMessageBuilder = errorMessageBuilder;
        if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
            this.status = errorMessageBuilder.getHttpStatus();
        }
    }
}
