/**
 * 
 */
package com.birdeye.messenger.exception;

import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
public class SecureMessagingException extends NonActionableException {

    public SecureMessagingException() {
        super("Error occurred in secure messaging");
    }

    public SecureMessagingException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.status = HttpStatus.BAD_REQUEST;
    }

    public SecureMessagingException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        this.status = HttpStatus.BAD_REQUEST;
    }

    public SecureMessagingException(String message) {
        super(message);
    }
   
    
    public SecureMessagingException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;
	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }
	}
	

}
