package com.birdeye.messenger.exception;


import com.birdeye.messenger.enums.ErrorCode;
/*Exception thrown when FB messaging API return error while sending or pulling data from FB */
public class FacebookException extends NonActionableException{

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public FacebookException (ErrorCode errorCode) {
    	super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }
    
    public FacebookException(ErrorCode errorCode, String errorMessage) {
    	super(errorMessage);
        this.errorCode = errorCode;
    }
    
    public FacebookException(Throwable e, ErrorCode errorCode) {
    	super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }
    
	
    public FacebookException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;
	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }
	}
}
