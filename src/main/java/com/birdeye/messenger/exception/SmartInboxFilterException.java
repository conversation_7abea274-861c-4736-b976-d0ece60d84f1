/**
 * 
 */
package com.birdeye.messenger.exception;

import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

/**
 * <AUTHOR>
 *
 */
public class SmartInboxFilterException extends NonActionableException {

    public SmartInboxFilterException() {
        super("Error occurred in Smart Inbox Filter");
    }

    public SmartInboxFilterException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.status = HttpStatus.BAD_REQUEST;
    }

    public SmartInboxFilterException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        this.status = HttpStatus.BAD_REQUEST;
    }

    public SmartInboxFilterException(String message) {
        super(message);
    }

    public SmartInboxFilterException(ErrorMessageBuilder errorMessageBuilder) {
        super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString() : null);
        this.errorMessageBuilder = errorMessageBuilder;
        if (errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
            this.status = errorMessageBuilder.getHttpStatus();
        }
    }

}
