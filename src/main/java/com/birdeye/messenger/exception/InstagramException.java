package com.birdeye.messenger.exception;

import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;
/*Exception thrown when IG messaging API return error while sending or pulling data from IG */
public class InstagramException extends NonActionableException{

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public InstagramException(ErrorCode errorCode) {
    	super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }
    
    public InstagramException(ErrorCode errorCode, String errorMessage) {
    	super(errorMessage);
        this.errorCode = errorCode;
    }
    
    public InstagramException(Throwable e, ErrorCode errorCode) {
    	super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }
    
	public InstagramException(ErrorCode errorCode, HttpStatus httpStatus) {
		super(errorCode.getErrorMessage());
	    this.errorCode = errorCode;
	    super.status = httpStatus;
	}
	
	public InstagramException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;
	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }
	}
}
