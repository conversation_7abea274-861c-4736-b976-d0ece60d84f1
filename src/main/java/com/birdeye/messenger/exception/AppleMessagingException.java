package com.birdeye.messenger.exception;

import com.birdeye.messenger.enums.ErrorCode;

/*Exception thrown when Apple messaging API return error while sending or pulling data from Apple */
public class AppleMessagingException extends NonActionableException {

    public AppleMessagingException(String message, Throwable e) {
        super(message, e);
    }

    public AppleMessagingException(String message) {
        super(message);
    }

    public AppleMessagingException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public AppleMessagingException(ErrorCode errorCode, Throwable e) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }

    public AppleMessagingException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public AppleMessagingException(ErrorMessageBuilder errorMessageBuilder) {
        super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString() : null);
        this.errorMessageBuilder = errorMessageBuilder;
        if (errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
            this.status = errorMessageBuilder.getHttpStatus();
        }
    }

}
