package com.birdeye.messenger.exception;


import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;
/*Exception thrown when duplicate entry for an entity found in data store*/
@Getter
public class DuplicateEntryException extends RuntimeException {

    private static final long serialVersionUID = 1L;
    private final ErrorCode errorCode;

    public DuplicateEntryException(ErrorCode errorCode, Throwable e) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }

    public DuplicateEntryException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public DuplicateEntryException(ErrorCode errorCode, String errorMessage) {
    	  super(errorMessage);
          this.errorCode = errorCode;
    }
}
