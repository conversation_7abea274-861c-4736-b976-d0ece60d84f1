package com.birdeye.messenger.exception;


import com.birdeye.messenger.enums.ErrorCode;
/*Exception thrown when an HTTP 4xx is received.*/
public class BadRequestException extends NonActionableException {

	private static final long serialVersionUID = 1L;

	public BadRequestException(String message) {
		super(message);
	}

	public BadRequestException(ErrorCode errorCode, String message) {
		super(message);
		this.errorCode = errorCode;
	}

	public BadRequestException(ErrorCode errorCode, Throwable e) {
		super(errorCode.getErrorMessage(), e);
		this.errorCode = errorCode;
	}

	public BadRequestException(ErrorCode errorCode) {
		super(errorCode.getErrorMessage());
		this.errorCode = errorCode;
	}

	public BadRequestException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;
	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }
	}
}
