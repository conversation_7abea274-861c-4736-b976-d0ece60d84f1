package com.birdeye.messenger.exception;


import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;

/*Exception thrown when client is not authorized to access the resource.*/
@Getter
public class NotAuthorizedException extends NonActionableException {

    private static final long serialVersionUID = 1L;

    public NotAuthorizedException(ErrorCode errorCode, Throwable e) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
        this.status=HttpStatus.FORBIDDEN;
    }
    public NotAuthorizedException(String message) {
        super(message);
        this.status=HttpStatus.FORBIDDEN;
    }
    public NotAuthorizedException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        this.status=HttpStatus.FORBIDDEN;
    }

    public NotAuthorizedException(ErrorCode errorCode, String errorMessage) {
    	  super(errorMessage);
          this.errorCode = errorCode;
          this.status=HttpStatus.FORBIDDEN;
    }
}
