package com.birdeye.messenger.exception;


import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;
/*Exception thrown when entity you are trying to get is not found */
@Getter
public class NotFoundException extends NonActionableException{
	
    private static final long serialVersionUID = 1L;
    
     public NotFoundException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        this.status=HttpStatus.NOT_FOUND;
    }

	public NotFoundException(ErrorCode errorCode, String errorMessage) {
		super(errorMessage);
		this.errorCode = errorCode;
		this.status = HttpStatus.NOT_FOUND;
	}
	public NotFoundException(String errorMessage) {
		super(errorMessage);
		this.status = HttpStatus.NOT_FOUND;
	}
	
	public NotFoundException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;

	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }

	}
}