package com.birdeye.messenger.exception;


import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;

/**
 * Exception thrown when bad data found in data store
 */
@Getter
public class BadDataException extends RuntimeException{
	private ErrorCode errorCode;
    private static final long serialVersionUID = 1L;
    
	public BadDataException(String errorMessage) {
		super(errorMessage);
	}
	public BadDataException(ErrorCode errorCode) {
		super(errorCode.getErrorMessage());
		this.errorCode = errorCode;
	}
}