package com.birdeye.messenger.exception;

import org.springframework.http.HttpStatus;

/*Exception thrown when GBM messaging API return error while sending or pulling data from Google */
public class GoogleMessagingException extends NonActionableException {
	protected HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
    public GoogleMessagingException(String message, Throwable e) {
        super(message, e);
    }

    public GoogleMessagingException(String message) {
        super(message);
    }


}
