package com.birdeye.messenger.exception;

import com.birdeye.messenger.enums.ErrorCode;
import lombok.Getter;
import lombok.Setter;

/* Exception thrown when something unusual happen within the system for eg: unable to read/write data from ES/DB etc*/

@Setter
@Getter
public class MessengerException extends RuntimeException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6836458557432389532L;
	private ErrorCode errorCode;

	public MessengerException(ErrorCode errorCode) {
		super(errorCode.getErrorMessage());
		this.errorCode = errorCode;
	}

	public MessengerException(ErrorCode errorCode, Throwable e) {
		super(errorCode.getErrorMessage(), e);
		this.errorCode = errorCode;
	}

	public MessengerException(String message, Throwable e) {
		super(message, e);
		this.errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
	}

	public MessengerException(String message) {
		super(message);
		this.errorCode = ErrorCode.INTERNAL_SERVER_ERROR;
	}

	public MessengerException(ErrorCode errorCode, String message) {
		super(message);
		this.errorCode = errorCode;
	}

	public MessengerException(ErrorCode errorCode, String message, Throwable e) {
		super(message, e);
		this.errorCode = errorCode;
	}

}
