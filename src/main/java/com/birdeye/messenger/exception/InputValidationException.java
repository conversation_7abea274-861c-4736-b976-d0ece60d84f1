package com.birdeye.messenger.exception;




import com.birdeye.messenger.enums.ErrorCode;
/* Exception thrown when input request validation failed.*/
public class InputValidationException extends NonActionableException {

    private static final long serialVersionUID = 1L;

    public InputValidationException(ErrorCode errorCode, Throwable e) {
        super(errorCode.getErrorMessage(), e);
        this.errorCode = errorCode;
    }

    public InputValidationException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
    }

    public InputValidationException(ErrorCode errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
    }

	public InputValidationException(String message) {
		super(message);
		this.errorCode = null;
	}
	
	public InputValidationException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
	    this.errorMessageBuilder = errorMessageBuilder;
	    
	    if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
	    	this.status = errorMessageBuilder.getHttpStatus();
	    }
	}
}
