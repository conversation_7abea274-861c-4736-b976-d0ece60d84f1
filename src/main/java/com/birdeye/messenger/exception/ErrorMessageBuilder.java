package com.birdeye.messenger.exception;

import org.slf4j.helpers.MessageFormatter;
import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;


/**
 * Nomenclature for standard error logging
 * " ErrorCode {@see ErrorCodeGenerator} = Message (Short Desc) : because/reason
 *  
 * <AUTHOR>
 *
 */
public class ErrorMessageBuilder {
	
	private final ErrorCodeGenerator errorCodeGenerator;
	private final String description;
	private final String shortDescription;
	private final StringBuilder messageBuilder = new StringBuilder();
	private final ErrorCode errorCode;
	private HttpStatus httpStatus;
	
	public ErrorMessageBuilder(final ErrorCode errorCode, final ComponentCodeEnum componentCode) {
		this(errorCode, componentCode, errorCode.getCode());
	}
	
	
	public ErrorMessageBuilder(final ErrorCode errorCode, final HttpStatus status) { 
		this(errorCode, ComponentCodeEnum.ERR, status);
	}
	
	public ErrorMessageBuilder(final ErrorCode errorCode, final ComponentCodeEnum componentCode, final HttpStatus status) {
		this(errorCode, componentCode,status.value());
		this.httpStatus = status;
	}
	
	public HttpStatus getHttpStatus() {
		return httpStatus;
	}
	
	public ErrorCodeGenerator getErrorCodeGenerator() {
		return errorCodeGenerator;
	}

	public ErrorMessageBuilder(final ErrorCode errorCode, final ComponentCodeEnum componentCode, int code) {
		
		this.errorCodeGenerator = ErrorCodeGenerator.builder().component(componentCode).code(code);
		this.errorCode =  errorCode;
		this.description = (errorCode != null) ? errorCode.getErrorMessage() : null;
		this.shortDescription = (errorCode != null) ? errorCode.name() : null;
		this.messageBuilder
		.append(errorCodeGenerator)
		.append(" = ")
		.append(description).append("( ")
		.append(shortDescription)
		.append(" ) : ");
	}
	

	public ErrorMessageBuilder message(final String message, final Object... arguments) {
		messageBuilder.append(addParameters(message, arguments));
		return this;
	}

	
	public ErrorCode getErrorCode() {
		return errorCode;
	}


	public final StringBuilder getMessageBuilder() {
		return messageBuilder;
	}


	public final String build() {
		return messageBuilder.toString();
    }


	private String addParameters(final String message, final Object[] arguments) {
		return MessageFormatter.arrayFormat(message, arguments).getMessage();
	}


	@Override
	public String toString() {
		return build();
	}
	
	
	

}
