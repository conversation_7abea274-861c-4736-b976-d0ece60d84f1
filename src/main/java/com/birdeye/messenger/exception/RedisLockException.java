package com.birdeye.messenger.exception;


import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;
/*Exception thrown when unable to acquire/release Redis lock */
@Getter
public class RedisLockException extends NonActionableException{
	
    private static final long serialVersionUID = 1L;
    
     public RedisLockException(ErrorCode errorCode) {
        super(errorCode.getErrorMessage());
        this.errorCode = errorCode;
        this.status=HttpStatus.LOCKED;
    }

	public RedisLockException(ErrorCode errorCode, String errorMessage) {
		super(errorMessage);
		this.errorCode = errorCode;
		this.status=HttpStatus.LOCKED;
	}
	
	public RedisLockException(String errorMessage) {
		super(errorMessage);
		this.status=HttpStatus.LOCKED;
	}
	
    public RedisLockException(ErrorCode errorCode, HttpStatus httpStatus) {
		super(errorCode.getErrorMessage());
	    this.errorCode = errorCode;
	    super.status = httpStatus;
	}
}