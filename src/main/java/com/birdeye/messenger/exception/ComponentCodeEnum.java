package com.birdeye.messenger.exception;

import lombok.Getter;

@Getter
public enum ComponentCodeEnum {

	CUSTOMER("001"),
	BUSINESS("002"),
	WEBCHAT("003"),
	EMAIL("004"),
	SMS("005"),
	REPORT("006"),
	TEST("007"),
	FB("008"),
	IG("009"),
	PAYMENT("010"),
	REVIEW("011"),
	GOOGLE("012"),
	FILTERS("013"),
	LIVECHAT("014"),
	AUDIT("998"),
	ERR("999"),
	TWITTER("015"),
	WHATSAPP("020");

	
	private String code;
	
	ComponentCodeEnum(String code) {
		this.code = code;
	}
}
