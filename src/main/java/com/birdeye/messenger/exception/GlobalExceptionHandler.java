package com.birdeye.messenger.exception;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.birdeye.messenger.annotations.NonActionableError;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.ErrorDTO;
import com.birdeye.messenger.enums.ErrorCode;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;

@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {


    @Autowired
    private HttpServletRequest request;
	
	// ---- Custom Exception Class ------

	@ExceptionHandler(MessengerException.class)
	public ResponseEntity<ErrorDTO> handleMessengerException(MessengerException ex) {
		log.error("Exception Occurred", ex);
		return new ResponseEntity<>(new ErrorDTO(ex.getErrorCode().getCode(), ex.getMessage()), HttpStatus.INTERNAL_SERVER_ERROR);
	}


	@ExceptionHandler(BadDataException.class)
	public ResponseEntity<ErrorDTO> handleBadDataException(BadDataException ex){
		log.error("Exception Occurred", ex);
		return new ResponseEntity<>(new ErrorDTO(ex.getErrorCode().getCode(), ex.getMessage()),HttpStatus.INTERNAL_SERVER_ERROR);
	}

	@ExceptionHandler(HttpClientErrorException.class)
	public ResponseEntity<ErrorDTO> handleClientErrorException(HttpClientErrorException ex) {
		log.warn("Error response from service status {} and body {}  ", ex.getStatusCode(),
				ex.getResponseBodyAsString());
		return new ResponseEntity<>(new ErrorDTO(ex.getStatusCode().value(), ex.getResponseBodyAsString()),
				HttpStatus.BAD_REQUEST);
	}

	@ExceptionHandler(GoogleMessagingException.class)
	public ResponseEntity<Error> handleGoogleMessagingException(GoogleMessagingException ex) {
		log.error("Google Messaging Exception Occurred", ex);
		return new ResponseEntity<>(new Error("400", ex.getMessage(), GoogleMessagingException.class.getTypeName()),
				HttpStatus.BAD_REQUEST);
	}



	@ExceptionHandler(NonActionableException.class)
	@NonActionableError
	public ResponseEntity<Object> handleNonActionableException(final NonActionableException ex, final WebRequest request){
		final ServletWebRequest servletWebRequest = (ServletWebRequest) request;
		final ErrorCode errorCode =  ex.getErrorMessageBuilder() != null ? ex.getErrorMessageBuilder().getErrorCode() : ex.getErrorCode();
		
		return buildResponseEntity(null, ex.getStatus(), servletWebRequest, errorCode, null);
	}





	// ---- HTTP Status == 400 (Handler) ------

	protected ResponseEntity<Object> handleMethodArgumentNotValid(final MethodArgumentNotValidException ex,
			final HttpHeaders headers, final HttpStatus status, final WebRequest request) {

		ServletWebRequest servletWebRequest = (ServletWebRequest) request;

		final List<String> errors = new ArrayList<String>();
		for (final FieldError error : ex.getBindingResult().getFieldErrors()) {
			errors.add(error.getField() + ": " + error.getDefaultMessage());
		}
		for (final ObjectError error : ex.getBindingResult().getGlobalErrors()) {
			errors.add(error.getObjectName() + ": " + error.getDefaultMessage());
		}

		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {}, Uri - {}, Header - {} | Errors - [{}]", ex.getClass().getSimpleName(),
						servletWebRequest.getRequest() != null ? servletWebRequest.getRequest().getServletPath(): "",
								headers, String.join(",", errors));
				
		log.warn(errorMessageBuilder.build(), ex);

		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.OK, servletWebRequest, errorMessageBuilder.getErrorCode(), errors);
	}
	
	

	protected ResponseEntity<Object> handleTypeMismatch(final TypeMismatchException ex, final HttpHeaders headers,
			final HttpStatus status, final WebRequest request) {

		ServletWebRequest servletWebRequest = (ServletWebRequest) request;
		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {}, Uri - {}, Header - {}", ex.getClass().getSimpleName(),
						servletWebRequest.getRequest() != null ? servletWebRequest.getRequest().getServletPath(): "",
								headers);
		log.warn(errorMessageBuilder.build(), ex);
		final String error = ex.getValue() + " value for " + ex.getPropertyName() + " should be of type " + ex.getRequiredType();
	
		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.OK, servletWebRequest, errorMessageBuilder.getErrorCode(), Collections.singletonList(error));
	}



	@ExceptionHandler({ MethodArgumentTypeMismatchException.class })
	public ResponseEntity<Object> handleMethodArgumentTypeMismatch(final MethodArgumentTypeMismatchException ex, final WebRequest request) {

		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {} occured", ex.getClass().getSimpleName());
		log.warn(errorMessageBuilder.build());

		final String error = ex.getName() + " should be of type " + ex.getRequiredType().getName(); 
		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.OK, (ServletWebRequest) request, 
				errorMessageBuilder.getErrorCode(), Collections.singletonList(error));
	}

	@ExceptionHandler({ ConstraintViolationException.class })
	public ResponseEntity<Object> handleConstraintViolation(final ConstraintViolationException ex, final WebRequest request) {

		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {} occured", ex.getClass().getSimpleName());
		log.warn(errorMessageBuilder.build(), ex);

		final List<String> errors = new ArrayList<String>();
		for (final ConstraintViolation<?> violation : ex.getConstraintViolations()) {
			errors.add(violation.getRootBeanClass().getName() + " " + violation.getPropertyPath() + ": " + violation.getMessage());
		}

		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.OK, (ServletWebRequest) request, 
				errorMessageBuilder.getErrorCode(), errors);
	}
	
	@ExceptionHandler({IllegalArgumentException.class})
	public ResponseEntity<Object> handleIllegalArgumentEx(final IllegalArgumentException ex, final WebRequest request) {
		ServletWebRequest servletWebRequest = (ServletWebRequest) request;
		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {} occured", ex.getClass().getSimpleName());
		log.warn(errorMessageBuilder.build(), ex);
		
		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.OK, servletWebRequest, errorMessageBuilder.getErrorCode(), null);	
	}



	// ---- HTTP Status == 404 (Handler) ------

	// ---- HTTP Status == 405 (Handler) ------

	protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(final HttpRequestMethodNotSupportedException ex,
			final HttpHeaders headers, final HttpStatus status, final WebRequest request) {

		ServletWebRequest servletWebRequest = (ServletWebRequest) request;
		
		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.BAD_REQUEST, ComponentCodeEnum.ERR)
				.message("Exception - {}, Uri - {}, Header - {}", ex.getClass().getSimpleName(),
						servletWebRequest.getRequest() != null ? servletWebRequest.getRequest().getServletPath(): "",
								headers);
				
		log.warn(errorMessageBuilder.build());


		final StringBuilder builder = new StringBuilder();
		builder.append(ex.getMethod());
		builder.append(" method is not supported for this request. Supported methods are:  ");
		ex.getSupportedHttpMethods().forEach(t -> builder.append(t + " "));

		return buildResponseEntity(ex.getLocalizedMessage(), status, servletWebRequest, 
				errorMessageBuilder.getErrorCode(), Collections.singletonList(builder.toString()));
	}


	// ---- HTTP Status == 500 (Handler) ------

	@ExceptionHandler(HttpServerErrorException.class)
	public ResponseEntity<Object> handleServerErrorException(HttpServerErrorException ex) {

		ErrorMessageBuilder errorMessageBuilder = new ErrorMessageBuilder(ErrorCode.INTERNAL_SERVER_ERROR, ComponentCodeEnum.ERR)
				.message("Error response from service status {} and body {}  ", ex.getStatusCode(),
						ex.getResponseBodyAsString());
		
		log.warn(errorMessageBuilder.build());
		return buildResponseEntity(ex.getLocalizedMessage(), HttpStatus.INTERNAL_SERVER_ERROR, null, 
				errorMessageBuilder.getErrorCode(), null);
	}

	@ExceptionHandler(Exception.class)
    public ResponseEntity<Error> handleException(Exception ex) {
        log.error(getMarkers(), "Exception Occurred", ex);
        return new ResponseEntity<>(
                new Error("500", ex.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase()),
                HttpStatus.INTERNAL_SERVER_ERROR);
    }


	private LogstashMarker getMarkers() {
		LogstashMarker marker = Markers.append( Constants.EXCEPTION_URI, (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE));
		marker.and(Markers.append(Constants.EXCEPTION_HTTP_METHOD, request.getMethod()));
		  
		return marker;
	}

	private ResponseEntity<Object> buildResponseEntity(String errorMessage, HttpStatus httpStatus, ServletWebRequest webRequest,
			ErrorCode errorCode, List<String> errors) {

		if(null == httpStatus) {
			httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
		}
		
		if(null == errorCode) {
			errorCode = ErrorCode.BLANK;
		}
		ErrorDTO.Builder errorbuilder = new ErrorDTO.Builder()
				.withHttpStatus(httpStatus)
				.withTimestamp()
				.withMessage(errorCode.getErrorMessage())
				.withErrorCode(errorCode.getCode());


		if (webRequest != null && webRequest.getRequest() != null) {
			errorbuilder.withUri(webRequest.getRequest().getServletPath());
		}

		errorbuilder.withErrorMessage(errorMessage);

		if (errors != null && !errors.isEmpty()) {
			errorbuilder.withErrorList(errors);
		}
		errorbuilder.build();
		return new ResponseEntity<>(errorbuilder.build(), httpStatus);
	}

	@ExceptionHandler({DataIntegrityViolationException.class})
	public ResponseEntity<Object> handleDataIntegrityViolation(DataIntegrityViolationException ex) {
		return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
				.body(Map.of(
						"code", 500,
						"message", "Internal server error"));
	}

	@ExceptionHandler({SQLException.class})
	public ResponseEntity<Object> handleSQLExceptionViolation(SQLException ex) {
		return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
				.body(Map.of("code", 500,
						"message", "Internal server error"));
	}

}
