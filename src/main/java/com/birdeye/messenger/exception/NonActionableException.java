package com.birdeye.messenger.exception;

import org.springframework.http.HttpStatus;

import com.birdeye.messenger.enums.ErrorCode;

import lombok.Getter;
/*NonActionable exception is something where we don’t need to take action for eg Not found exception, Input validation exception, Bad request exception etc.*/
@Getter
public class NonActionableException extends RuntimeException {
	private static final long serialVersionUID = 1L;
	protected ErrorCode errorCode;
	protected ErrorMessageBuilder errorMessageBuilder;

	protected HttpStatus status = HttpStatus.OK;

	public NonActionableException(String errorMessage, Throwable e) {
		super(errorMessage, e);
	}

	public NonActionableException(String errorMessage) {
		super(errorMessage);
	}
}