package com.birdeye.messenger.exception;

import com.birdeye.messenger.enums.ErrorCode;

/**
 * <AUTHOR>
 *
 */
public class WhatsappException extends NonActionableException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public WhatsappException (ErrorCode errorCode) {
		super(errorCode.getErrorMessage());
		this.errorCode = errorCode;
	}

	public WhatsappException(ErrorCode errorCode, String errorMessage) {
		super(errorMessage);
		this.errorCode = errorCode;
	}

	public WhatsappException(Throwable e, ErrorCode errorCode) {
		super(errorCode.getErrorMessage(), e);
		this.errorCode = errorCode;
	}


	public WhatsappException(ErrorMessageBuilder errorMessageBuilder) {
		super(errorMessageBuilder != null ? errorMessageBuilder.getMessageBuilder().toString(): null);
		this.errorMessageBuilder = errorMessageBuilder;
		if(errorMessageBuilder != null && errorMessageBuilder.getHttpStatus() != null) {
			this.status = errorMessageBuilder.getHttpStatus();
		}
	}
}
