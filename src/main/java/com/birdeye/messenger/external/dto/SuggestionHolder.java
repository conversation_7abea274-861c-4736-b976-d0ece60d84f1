package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.util.List;

import com.birdeye.messenger.dto.elastic.MessageDocument.UserDetail;

import lombok.Data;

@Data
public class SuggestionHolder implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	String queryText;
	String subHeader;
	List<Suggestion> suggestions;
	UserDetail createdBy;
	String sentOn;
}