package com.birdeye.messenger.external.dto;

import java.io.Serializable;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
public class ChatbotConfigDTO implements Serializable{

	private static final long serialVersionUID = 7188412684467812030L;
	
	private Integer businessId;
	private Integer accountId;
	private Boolean status;

}
