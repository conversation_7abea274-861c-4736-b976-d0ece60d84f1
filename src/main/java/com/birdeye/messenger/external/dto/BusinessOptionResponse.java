package com.birdeye.messenger.external.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class BusinessOptionResponse implements Serializable {

	private static final long serialVersionUID = -6805025001305483062L;
	
	private Integer smsOpted;
	private Integer enableMessenger;
	private Integer enableChatWidget;
	private Integer	liveChat;
	private Integer chatbot;
	private Integer enableReceptionist;
	private String inboxEmailReplyDomain;
	private Integer isSuspectSupportOn;
	private Integer isGoogleMessageEnabled;
    private Integer isAIFaqEnabled;
}
