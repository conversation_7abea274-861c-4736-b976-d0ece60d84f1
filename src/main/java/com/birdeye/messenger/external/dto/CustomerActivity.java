package com.birdeye.messenger.external.dto;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.elastic.Identifiable;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import lombok.Setter;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class CustomerActivity implements Serializable {

    private Integer b_id;
    private Integer e_id;
    private Long b_num;
    private String b_name;
    private String b_alias;
    private Integer c_id;
    private String c_name;
    @SecureString
    private String c_phone;
    @SecureString
    private String c_email;
    private List<Activity> logs = new ArrayList<>();



    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    public static class Activity implements Identifiable, Serializable {

        private String c_id;
        private Integer e_id;
        private String date;
        private int u_id;
        private Long a_id;
        private String u_name;
        private String a_type;
        private String type = "create";
        private String a_type_name;
        private String a_desc;

        public Activity(){
        }

        public Activity(Integer customerId, String eventType, String eventDesc, String eventName, Long activityId, String type, Integer enterpriseId, Date date) {
            DateFormat df = new SimpleDateFormat(Constants.FORMAT_YYYY_MM_DD_HH_MM_SS);
            this.c_id = String.valueOf(customerId);
            this.e_id = enterpriseId;
            this.a_type = eventType;
            this.a_type_name = eventName;
            this.a_desc = eventDesc;
            this.a_id = activityId;
            if(Objects.nonNull(date)) {
                this.date = df.format(date);
            }
            else {
                this.date = df.format(new Date());
            }
            this.type = type;
        }

        @Override
        @JsonIgnore
        public Object getId() {
            return a_id+"_"+a_type;
        }
    }

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }
}
