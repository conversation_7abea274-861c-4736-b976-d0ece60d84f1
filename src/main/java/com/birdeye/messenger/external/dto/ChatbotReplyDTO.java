package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Encapsulate the chatbot reply to be viewed by end-user.
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ToString
public class ChatbotReplyDTO implements Serializable {

	private static final long serialVersionUID = 6605594173663812861L;
	
	private String type;
	private String intentType;
	private Map<String, Object> data;

}
