package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.util.Objects;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.enums.IntentType;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class Suggestion implements Serializable{

	private static final long serialVersionUID = -4542986035676593999L;
		Type type;
		String key;
		String value;
		String answer;
		boolean secure;
		boolean partial;
		String imageUrl=Constants.SUGGESTION_DEFAULT_IMAGE_URL;
		
		public static enum Type{
			INTENT,FAQ,DEFAULT,FEEDBACK
		}

	public Suggestion(Type type, String key, String value) {
		this.type = type;
		this.key = key;
		this.value = value;
        if (Type.INTENT.equals(type)) {
            IntentType intentType = IntentType.getIntentTypeFromQueryText(value.toLowerCase());
            if (Objects.nonNull(intentType)) {
                this.imageUrl = intentType.getImageUrl();
            }

        }
	}

	public Suggestion() {
		super();
	}
}
	