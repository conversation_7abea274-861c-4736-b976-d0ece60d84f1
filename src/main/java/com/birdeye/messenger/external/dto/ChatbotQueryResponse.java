package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.*;

/**
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatbotQueryResponse implements Serializable {

	private static final long serialVersionUID = -4357431476332485365L;
	
	private String intentType;
	private Map<String, Object> data;
	public SuggestionHolder suggestionHolder;
	
	public boolean isDefault() {
		if("DEFAULT_FALLBACK".equals(getIntentType())) {
			return true;
		}
		return false;
	}
}
