package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown=true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class BusinessReceptionistResponse implements Serializable {

	private static final long serialVersionUID = 4271261332744620143L;

	private Integer totalCount;
	private Integer size;
	private List<BusinessPhoneData> data = new ArrayList<>();

	@Data
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
	public static class BusinessPhoneData implements Serializable{
		private static final long serialVersionUID = -7207410494559192368L;

		private Integer id;
		private String name;
		@SecureString
		private String landlineNumber;
		@SecureString
		private String smsPhoneNumber;
		@SecureString
		private String receptionistNumber;
		private Boolean hostingStatus;

		@Override
		public String toString() {
			return SecureStringUtil.buildSecureToString(this);
		}
	}

}
