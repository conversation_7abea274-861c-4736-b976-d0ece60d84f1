package com.birdeye.messenger.external.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SurveyResponse {

	Integer surveyId;
	String name;
	Integer businessId;
	String surveyType;
	String thankYouMessage;
	
}