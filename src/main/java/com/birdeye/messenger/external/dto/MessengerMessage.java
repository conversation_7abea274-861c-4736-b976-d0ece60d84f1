package com.birdeye.messenger.external.dto;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;

import com.birdeye.messenger.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * This is added to external package as this will be replaced with other simplified POJO class
 */
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Slf4j
@Data
public class MessengerMessage implements Serializable {

    private Integer id;
    private String name;
    @SecureString
    private String phone;
    @SecureString
    private String emailId;
    private Integer businessId;
    private Map<String, List<Message>> messages = new LinkedHashMap<>();
    private boolean hasMore = false;
    private String imageUrl;
    private String fbIntegrationStatus; // facebook integration status
    private Boolean isLastFacebookMessage = false;
    private Boolean isUserUnreachable = false;
    private Integer lastReceivedMsgSource;
    private Integer mcid;
    private static DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    @Data
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class Message {
        private String msgId;
        private String sentOn;
        private Date sentAt;
        private Long sentAtUTC;
        private Integer senderId;
        private String sentBy;
        private String messageType;
        private String status;
        private String body;
        private String extension;
        private String mediaUrl;
        private String name;
        private String contentSize;
        private String contentType;
        private Integer is_encrypt;
        private Integer source = 0;
		private List<MessengerMessage.MediaFile> mediaFiles;
        
        public Message() {}
        public Message(UserDTO userDTO, ConversationDTO conversationDTO, String type, MessengerMediaFile file, String timeZoneId) {
			this.msgId = String.valueOf(conversationDTO.getId());
//            timezone = StringUtils.isNotEmpty(timezone) ? TimeZoneUtil.getTimeZoneID(timezone) : "PST";
            timeZoneId = StringUtils.isNotEmpty(timeZoneId) ? timeZoneId : "PST";
            TimeZone tz = TimeZone.getTimeZone(timeZoneId);
            Date utcDate = conversationDTO.getSentOn() != null ? conversationDTO.getSentOn() : conversationDTO.getCreateDate();
            this.sentAt = ControllerUtil.convertToBusinessTimeZone(utcDate, tz).getTime();
            this.sentAtUTC = utcDate.getTime();
            this.sentOn = new SimpleDateFormat("MMM dd, yyyy hh:mm a")
                    .format(this.sentAt) + " " + tz.getDisplayName(tz.inDaylightTime(this.sentAt), TimeZone.SHORT, Locale.US);
            if (userDTO != null) {
                this.senderId = userDTO.getId();
                this.sentBy = userDTO.getName();
            }
            this.messageType = type;
            this.status = "success";
			this.msgId = String.valueOf(conversationDTO.getId());
            this.source = conversationDTO.getSource();
            if(StringUtils.containsIgnoreCase(this.messageType, "SEND") && StringUtils.isEmpty(this.sentBy)) {
                this.sentBy = setSender(this.source);
            }
            if (StringUtils.isNotBlank(conversationDTO.getBody()) && conversationDTO.getEncrypted() != null && conversationDTO.getEncrypted() == 1) {
                try {
                    this.body = EncryptionUtil.decrypt(conversationDTO.getBody(), StringUtils.join(conversationDTO.getSender(), conversationDTO.getRecipient()), StringUtils.join(conversationDTO.getRecipient(), conversationDTO.getSender()), false);
                } catch (Exception e) {
                }
            } else {
                this.body = conversationDTO.getBody();
            }
            this.is_encrypt = conversationDTO.getEncrypted();
            if (file != null) {
                this.mediaUrl = file.getUrl();
                this.contentType = file.getContentType();
                this.contentSize = file.getContentSize();
                this.extension = FilenameUtils.getExtension(file.getUrl());
                this.name = file.getName();
            }
            
        }
        
        public Message(MessageDocument log, Date sentAt, String timezone) {
            this.msgId = log.getM_id();
            this.sentAt = sentAt;
            this.sentOn = new SimpleDateFormat("MMM dd, yyyy hh:mm a")
                    .format(this.sentAt) + " " + timezone;
            this.senderId = log.getU_id();
            this.sentBy = log.getU_name();
            this.messageType = log.getMsg_type();
			if ("FACEBOOK_RECEIVE".equalsIgnoreCase(log.getMsg_type())) {
				this.messageType = "SMS_RECEIVE";
			}
			if ("FACEBOOK_SEND".equalsIgnoreCase(log.getMsg_type())) {
				this.messageType = "SMS_SEND";
			}
			this.source = log.getSource();
            if(StringUtils.containsIgnoreCase(this.messageType, "SEND") && StringUtils.isEmpty(this.sentBy)) {
                this.sentBy = setSender(this.source);
            }
            this.status = log.getMsg_status();
            if (StringUtils.isNotBlank(log.getMsg_body()) && log.getIs_encrypt() != null && log.getIs_encrypt() == 1) {
                try {
                	//changed decyrption key for FB as sender is saved as recipient and recipient as sender in ES for FB
                    if(log.getSource()!=null && log.getSource()==110) {
                        this.body = EncryptionUtil.decrypt(log.getMsg_body(), StringUtils.join(log.getTo(), log.getFrom()), StringUtils.join(log.getFrom(), log.getTo()), false);
                        if(StringUtils.isEmpty(body)) {
                        	 this.body = EncryptionUtil.decrypt(log.getMsg_body(), StringUtils.join(log.getFrom(), log.getTo()), StringUtils.join(log.getTo(), log.getFrom()), false);
                        }
                    }
                     else
                        this.body = EncryptionUtil.decrypt(log.getMsg_body(), StringUtils.join(log.getFrom(), log.getTo()), StringUtils.join(log.getTo(), log.getFrom()), false);
                    this.body = MessengerUtil.decode(this.body, "UTF-8");
                } catch (Exception e) {
                    this.body = MessengerUtil.decode(this.body, "UTF-8");
                }
            } else {
                this.body = MessengerUtil.decode(log.getMsg_body(), "UTF-8");
            }
            this.extension = log.getA_ext();
            this.mediaUrl = MessengerUtil.decode(log.getA_url(),"UTF-8");
            this.contentSize = log.getA_size();
            this.contentType = log.getA_contype();
            this.is_encrypt = log.getIs_encrypt();
            if(StringUtils.isNotBlank(log.getA_name())) this.name = log.getA_name();
        }

        private String setSender(Integer source) {
            if (source != null) {
                switch (source) {
                    case 0:
                        return "auto campaign";
                    case 1:
                        return "auto reply";
                    case 2:
                        return "webchat";
                    default:
                        return "auto reply";
                }
            }
            return "auto reply";
        }

		public Message(UserDTO userDTO, ConversationDTO conversationDTO, String type, List<MessengerMediaFile> files,
				String timeZoneId) {
			this.msgId = String.valueOf(conversationDTO.getId());
//			timezone = StringUtils.isNotEmpty(timezone) ? TimeZoneUtil.getTimeZoneID(timezone) : "PST";
            timeZoneId = StringUtils.isNotEmpty(timeZoneId) ? timeZoneId : "PST";
            TimeZone tz = TimeZone.getTimeZone(timeZoneId);
			Date utcDate = conversationDTO.getSentOn() != null ? conversationDTO.getSentOn()
					: conversationDTO.getCreateDate();
			this.sentAt = ControllerUtil.convertToBusinessTimeZone(utcDate, tz).getTime();
			this.sentAtUTC = utcDate.getTime();
			this.sentOn = new SimpleDateFormat("MMM dd, yyyy hh:mm a").format(this.sentAt) + " "
					+ tz.getDisplayName(tz.inDaylightTime(this.sentAt), TimeZone.SHORT, Locale.US);
			if (userDTO != null) {
				this.senderId = userDTO.getId();
				this.sentBy = userDTO.getName();
			}
			this.messageType = type;
			this.status = "success";
			this.msgId = String.valueOf(conversationDTO.getId());
			this.source = conversationDTO.getSource();
			if (StringUtils.containsIgnoreCase(this.messageType, "SEND") && StringUtils.isEmpty(this.sentBy)) {
				this.sentBy = setSender(this.source);
			}
			if (StringUtils.isNotBlank(conversationDTO.getBody()) && conversationDTO.getEncrypted() != null
					&& conversationDTO.getEncrypted() == 1) {
				try {
					this.body = EncryptionUtil.decrypt(conversationDTO.getBody(),
							StringUtils.join(conversationDTO.getSender(), conversationDTO.getRecipient()),
							StringUtils.join(conversationDTO.getRecipient(), conversationDTO.getSender()), false);
				} catch (Exception e) {
				}
			} else {
				this.body = conversationDTO.getBody();
			}
			this.is_encrypt = conversationDTO.getEncrypted();
			if (CollectionUtils.isNotEmpty(files)) {
				log.info("new called ", files.size());
				List<MessengerMessage.MediaFile> media = new ArrayList<MessengerMessage.MediaFile>();
				for (MessengerMediaFile mMFile : files) {
					media.add(new MessengerMessage.MediaFile(FilenameUtils.getExtension(mMFile.getUrl()),
							mMFile.getUrl(), mMFile.getContentSize(), mMFile.getName(), mMFile.getContentType()));
				}
				this.mediaFiles = media;
			}
		}

    }
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class UserDetail {
        Integer userId;
        String userName;
    }
    public MessengerMessage(){}
    
    public MessengerMessage(List<MessageDocument> messageDocuments, ContactDocument contactDocument, String timeZone) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.id = contactDocument.getC_id();
        this.name = contactDocument.getC_name();
        this.phone = contactDocument.getC_phone();
        this.emailId = contactDocument.getC_email();
        this.businessId = contactDocument.getB_id();
        this.imageUrl = contactDocument.getImageUrl();
        this.hasMore = false;
        Map<String, List<Message>> msgByDay = new TreeMap<>();
        timeZone = StringUtils.isNotEmpty(timeZone) ? TimeZoneUtil.getTimeZoneID(timeZone) : "PST";
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        for(MessageDocument message : messageDocuments) {
            if (message.getCr_date() == null) {
                continue;
            }
            Date sentOnUTC = null;
            try {
                sentOnUTC = df.parse(message.getCr_date());
                Date sentOn = MessengerUtil.convertToBusinessTimeZone(sentOnUTC, tz).getTime();
                String sendingTime = DateUtils.isSameDay(sentOn, MessengerUtil.convertToBusinessTimeZone(new Date(), tz).getTime()) ? new SimpleDateFormat("yyyy-MM-dd HH:mm").format(sentOn) : new SimpleDateFormat("yyyy-MM-dd").format(sentOn);
                updateMessageList(msgByDay, new Message(message, sentOn, tz.getDisplayName(tz.inDaylightTime(sentOn), TimeZone.SHORT, Locale.US)), sendingTime);
            } catch (ParseException e) {
                log.error("[MessengerMessage] Error parsing Date ", e);
            }
        }

        if (!msgByDay.isEmpty()) {
            for (String day : msgByDay.keySet()) {
                List<Message> msgs = sortMessage(msgByDay.get(day));
                if (CollectionUtils.isNotEmpty(msgs)) {
                    messages.put(msgs.get(0).getSentOn(), msgs);
                }
            }
        }

    }


    private void updateMessageList(Map<String, List<Message>> msgByDay, Message msg, String sendingTime) {
        if (msgByDay.containsKey(sendingTime)) {
            List<Message> list = msgByDay.get(sendingTime);
            list.add(msg);
            msgByDay.put(sendingTime, list);
        } else {
            List<Message> list = new ArrayList();
            list.add(msg);
            msgByDay.put(sendingTime, list);
        }
    }

    private List<Message> sortMessage(List<Message> messages) {
	    	Collections.sort(messages, new Comparator<Message>() {
	    		@Override
	    		public int compare(Message o1, Message o2) {
	    			if(o1.getSentAt().compareTo(o2.getSentAt()) == 0) {
	    				return o1.getMsgId().compareTo(o2.getMsgId());
	    			} else {
	    				return o1.getSentAt().compareTo(o2.getSentAt());
	    			}
	    		}
	    	});
	    	return messages;
    }
    
    public MessengerMessage(SendMessageDTO sendMessageDTO, List<MessengerMessage.Message> msgs,Boolean isLastMessage, Boolean isUserUnreachable) {
        this.id =Integer.valueOf(sendMessageDTO.getToCustomerId());
        this.phone = sendMessageDTO.getToPhone();        
        if(CollectionUtils.isNotEmpty(msgs))
        	messages.put(msgs.get(msgs.size() - 1).getSentOn(), msgs);
        this.isLastFacebookMessage= isLastMessage;
        this.isUserUnreachable=isUserUnreachable;
    }
    
    public MessengerMessage(ContactDocument contactDocument,List<MessageDocument> messageDocuments, boolean hasMore, String timeZoneId) {
    	this.id = contactDocument.getC_id(); // return as received
        this.name = contactDocument.getC_name(); //remove
        this.phone = contactDocument.getC_phone(); // remove
        this.emailId = contactDocument.getC_email(); // remove
        this.businessId = contactDocument.getB_id(); // return as received
        this.hasMore = hasMore;
        this.imageUrl = contactDocument.getImageUrl(); // remove
        Map<String, List<Message>> msgByDay = new TreeMap<>();
//        timezone = StringUtils.isNotEmpty(timezone) ? TimeZoneUtil.getTimeZoneID(timezone) : "PST";
        timeZoneId = StringUtils.isNotEmpty(timeZoneId) ? timeZoneId : "PST";
        TimeZone tz = TimeZone.getTimeZone(timeZoneId);
        
        for (MessageDocument messageDocument:messageDocuments) {
            if (messageDocument.getCr_date() == null) {
                continue;
            }
            Date sentOnUTC=null;
            try {
            	sentOnUTC = df.parse(messageDocument.getCr_date());
            } catch (Exception e) {
            	log.error("Exception in parsing Date With exception :", e);
			}
            Date sentOn = ControllerUtil.convertToBusinessTimeZone(sentOnUTC, tz).getTime();
            String sendingTime = DateUtils.isSameDay(sentOn,ControllerUtil.convertToBusinessTimeZone(new Date(), tz).getTime()) ?
                    new SimpleDateFormat("yyyy-MM-dd HH:mm").format(sentOn) : new SimpleDateFormat("yyyy-MM-dd").format(sentOn) ;
            updateMessageList(msgByDay, new Message(messageDocument, sentOn, tz.getDisplayName(tz.inDaylightTime(sentOn), TimeZone.SHORT, Locale.US)), sendingTime);
        }
        if (!msgByDay.isEmpty()) {
            for (String day : msgByDay.keySet()) {
                List<Message> msgs = sortMessage(msgByDay.get(day));
                if (CollectionUtils.isNotEmpty(msgs)) {
                    messages.put(msgs.get(0).getSentOn(), msgs);
                }
            }
        }
    }

	@Data
	@JsonInclude(JsonInclude.Include.NON_NULL)
	public static class MediaFile {
		private String a_ext;
		private String a_url;
		private String a_size;
		private String a_name;
		private String a_contype;

		public MediaFile() {
		}

		public MediaFile(String ext, String url, String contentLength, String name, String contentType) {
			this.a_contype = contentType;
			this.a_ext = ext;
			this.a_size = contentLength;
			this.a_name = name;
			this.a_url = url;
		}
	}

    @Override
    public String toString() {
        return SecureStringUtil.buildSecureToString(this);
    }

}
