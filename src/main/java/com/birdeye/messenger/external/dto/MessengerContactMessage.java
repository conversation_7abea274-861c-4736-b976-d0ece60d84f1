package com.birdeye.messenger.external.dto;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.CommunicationPreferencesDto;
import com.birdeye.messenger.dto.EmailPreferencesDto;
import com.birdeye.messenger.dto.SmsPreferencesDto;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.ContactDocument.ContentType;
import com.birdeye.messenger.dto.elastic.ContactDocument.RecentContent;
import com.birdeye.messenger.dto.elastic.ContactDocument.Review;
import com.birdeye.messenger.enums.AppointmentActionEventType;
import com.birdeye.messenger.enums.AppointmentSource;
import com.birdeye.messenger.enums.ConversationView;
import com.birdeye.messenger.enums.LeadSource;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.Month;
import com.birdeye.messenger.enums.PaymentStatus;
import com.birdeye.messenger.enums.ReferralSource;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.sro.VideoContext;
import com.birdeye.messenger.util.DateUtils;
import com.birdeye.messenger.util.EncryptionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Slf4j
public class MessengerContactMessage {

    private Long lastMsgUTCTime;
    private Contact customer;
    private Integer tag;
    private Integer businessId;
    private String businessName;
    private String name;
    private String timeElapsed;
    private String recentMessage;
	private Long appointmentStartTime;
    private List<Integer> viewedByUsers;
    private Boolean read;
    private Boolean open;
    private boolean unassigned;
    private String recentMessageType;
    private VideoContext recentVideoConversationContext;

	private boolean referrer;
	private boolean lead;
	private ReferralSource referralSource;
	private LeadSource leadSource;
	private transient ContactDocument.ContentType recentContentType;
	private transient String recentContentStatus;
	private Integer reviewId;
	private String paymentId;
    private Boolean secureMessagingInitiated;
	private Long lastIncomingMessageTime;

    @Data
    public static class Contact {

        private Integer id;
        private String name;
        private String firstName;
        private String lastName;
		@SecureString
        private String emailId;
		@SecureString
        private String phone;
        private boolean appleOptOut;
        private String imageUrl;
        private Integer businessId;
        private Integer cId;
        private String assignedToName;
        private Integer assignedTo;
        private String teamAssignedToName;
        private Integer teamAssignedTo;
        private String assignmentType;
        private String facebookId;
		private Integer templateId;
		//customer sentiment fields
		private Integer sentimentScore;
		private String sentiment;
		private Boolean ongoingPulseSurvey = Boolean.FALSE;
		private String googleConvId;
		private Boolean blocked;
		private String instagramConvId;
		private String appleConvId;
		private String twitterConvId;
		private String whatsappConvId;
		private String customChannel;
		private List<String> capabilityList;
		
		private CommunicationPreferencesDto commPreferences;
		
		
		public Contact(ContactDocument log) {
			this.id = log.getM_c_id();
			this.emailId = log.getC_email();
			this.assignedToName = log.getCr_asgn_name();
			this.assignedTo = log.getCr_asgn_id();
			this.teamAssignedToName = log.getTeam_name();
			this.teamAssignedTo = log.getTeam_id();
			this.assignmentType = StringUtils.isNotBlank(log.getAssignmentType()) ? log.getAssignmentType() : "U";
			if (StringUtils.isNotBlank(log.getC_name())) {
				String[] names = log.getC_name().split(" ");
				this.firstName = names[0];
				this.lastName = names.length > 1 ? names[1] : "";
			}
			this.phone = log.getC_phone();
			// Communication preferences
			if (Objects.nonNull(log.getCommPreferences())) {
				CommunicationPreferencesDto commPrefs = log.getCommPreferences();

				// Fallback: If smsPreferences is null, build from smsOptin/smsUnsubType
				if (commPrefs.getSmsPreferences() == null) {
					SmsPreferencesDto smsPrefs = new SmsPreferencesDto();
					Boolean optin = commPrefs.getSmsOptin();
					smsPrefs.setMarketingOptin(optin);
					smsPrefs.setFeedbackOptin(optin);
					smsPrefs.setServiceOptin(optin);
					smsPrefs.setMarketingOptoutType(commPrefs.getSmsUnsubType());
					smsPrefs.setFeedbackOptoutType(commPrefs.getSmsUnsubType());
					smsPrefs.setServiceOptoutType(commPrefs.getSmsUnsubType());
					commPrefs.setSmsPreferences(smsPrefs);
				} else {
					SmsPreferencesDto updatedPref = commPrefs.getSmsPreferences();

					// Setting the old field again because new contacts might have have new fields
					// only
					commPrefs.setSmsOptin(updatedPref.getServiceOptin());
					commPrefs.setSmsUnsubType(updatedPref.getServiceOptoutType());
				}
				if (log.getCommPreferences().getWhatsappOptin() == null){
					commPrefs.setWhatsappOptin(true);
					commPrefs.setWhatsappUnsubType("");
				}

				this.commPreferences = commPrefs;
			}
			else {
				// Fallback -- Ideally commPreferences should be set for all the contact documents after migration.
				CommunicationPreferencesDto pref = new CommunicationPreferencesDto();

				SmsPreferencesDto smsPreferencesDto = new SmsPreferencesDto();

				smsPreferencesDto.setMarketingOptin((log.getSms_on() == null || log.getSms_on() == 1));
				smsPreferencesDto.setFeedbackOptin((log.getSms_on() == null || log.getSms_on() == 1));
				smsPreferencesDto.setServiceOptin((log.getSms_on() == null || log.getSms_on() == 1));
				smsPreferencesDto.setMarketingOptoutType(log.getUnsubs_type());
				smsPreferencesDto.setFeedbackOptoutType(log.getUnsubs_type());
				smsPreferencesDto.setServiceOptoutType(log.getUnsubs_type());

				//Fallback for mobile
				pref.setSmsOptin(smsPreferencesDto.getServiceOptin());
				pref.setSmsUnsubType(smsPreferencesDto.getServiceOptoutType());

				EmailPreferencesDto emailPref = new EmailPreferencesDto();
				if (StringUtils.isNotBlank(log.getEmailUnsubcribedReason())) {
					emailPref.setFeedbackOptin(false);
					emailPref.setFeedbackOptoutType(log.getEmailUnsubcribedReason());
					emailPref.setMarketingOptin(false);
					emailPref.setMarketingOptoutType(log.getEmailUnsubcribedReason());
					emailPref.setServiceOptin(false);
					emailPref.setServiceOptoutType(log.getEmailUnsubcribedReason());
				} else {
					//BIRD-108375
					emailPref.setFeedbackOptin(true);
					emailPref.setMarketingOptin(true);
					emailPref.setServiceOptin(true);
				}
				pref.setWhatsappOptin(true);
				pref.setWhatsappUnsubType("");
				pref.setSmsPreferences(smsPreferencesDto);
				pref.setEmailPreferences(emailPref);
				this.commPreferences = pref;
			}
			this.appleOptOut = Objects.nonNull(log.getAppleContextDocument())
					&& BooleanUtils.isTrue(log.getAppleContextDocument().getAppleOptOut());
			this.setName(log);
			this.imageUrl = log.getImageUrl();
			this.businessId = log.getB_id();
			this.cId = log.getC_id();
			this.templateId = log.getTemplateId();
			this.sentimentScore = log.getSentimentScore();
			this.sentiment = log.getSentiment();
			if (Objects.nonNull(log.getOngoingPulseSurvey()))
				this.ongoingPulseSurvey = log.getOngoingPulseSurvey();
			this.blocked = Objects.nonNull(log.getBlocked()) && log.getBlocked();
			this.capabilityList = Objects.nonNull(log.getAppleContextDocument())
					? log.getAppleContextDocument().getCapabilityList()
							: null;
			this.customChannel = log.getCustomChannel();
		}
        
        private void setName(ContactDocument log) {
            if (StringUtils.isNotBlank(log.getC_name())) {
                this.name = log.getC_name();
            }
            if (StringUtils.isEmpty(this.name) && StringUtils.isNotBlank(log.getC_phone())) {
                this.name = log.getC_phone();
            }
			if (StringUtils.isEmpty(this.name) && StringUtils.isNotBlank(log.getC_email())
					&& StringUtils.contains(log.getC_email(), "@")) {
				String email = log.getC_email().substring(0, log.getC_email().indexOf("@")).toLowerCase();
				if (email.contains(".")) {
					email = email.replaceAll("\\.", " ");
				}
				if (email.contains("_")) {
					email = email.replaceAll("\\_", " ");
				}
				this.name = email;
			}
        }
        
        public void setName(String name) {
        	this.name = name;
        }
        
		public Contact() {
			
		}

		@Override
		public String toString() {
			return SecureStringUtil.buildSecureToString(this);
		}
	}
   

    public MessengerContactMessage(ContactDocument conversation,Integer source,String facebookId, ConversationView filterType, boolean reviewsAccess,boolean surveysAccess,String googleConvId,String instagramConvId,String appleConvId,String twitterConvId, String whatsappConvId) {
        this.tag = conversation.getC_tag();
        this.customer = new Contact(conversation);
        this.customer.setFacebookId(facebookId);
        this.customer.setGoogleConvId(googleConvId);
        this.customer.setInstagramConvId(instagramConvId);
        this.customer.setAppleConvId(appleConvId);
		this.customer.setTwitterConvId(twitterConvId);
		this.customer.setWhatsappConvId(whatsappConvId);
        this.name = customer.name;
        this.unassigned = conversation.unassigned();
        this.businessId = conversation.getB_id();
        this.recentMessageType = translateMessageType(conversation.getLastMessageType(),conversation.getL_msg());
        Optional<RecentContent> recentContentOpt = conversation.getRecentContentByType(filterType,reviewsAccess,surveysAccess);
        this.recentContentType = recentContentOpt.isPresent()?recentContentOpt.get().getType():null;
        this.recentContentStatus=recentContentOpt.isPresent()?recentContentOpt.get().getStatus():null;
		if (recentContentOpt.isPresent() && recentContentOpt.get().getType() == ContentType.MESSAGE && StringUtils.isNotBlank(recentContentOpt.get().getText()) && conversation.getIs_encrypted() != null && conversation.getIs_encrypted() == 1) {
			try {
				this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
						StringUtils.join(conversation.getB_num(), conversation.getC_phone()),
						StringUtils.join(conversation.getC_phone(), conversation.getB_num()), false);
				if (StringUtils.isEmpty(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.FACEBOOK.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(facebookId)) {
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), facebookId), StringUtils.join(facebookId, conversation.getB_num()),
							false);
				} else if (StringUtils.isBlank(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.GOOGLE.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(googleConvId)) {
					log.error("PIY_GBM: recent message [{}] googleConvId [{}], bNum [{}]", this.recentMessage, googleConvId, conversation.getB_num());
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), googleConvId),
							StringUtils.join(googleConvId, conversation.getB_num()), true);
				} else if (StringUtils.isBlank(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.INSTAGRAM.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(instagramConvId)) {
					log.error("recent message [{}] instagramConvId [{}], bNum [{}]", this.recentMessage, instagramConvId, conversation.getB_num());
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), instagramConvId),
							StringUtils.join(instagramConvId, conversation.getB_num()), true);
				}else if (StringUtils.isBlank(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.APPLE.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(appleConvId)) {
					log.error("recent message [{}] appleConvId [{}], bNum [{}]", this.recentMessage, appleConvId, conversation.getB_num());
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), appleConvId),
							StringUtils.join(appleConvId, conversation.getB_num()), true);
				}else if (StringUtils.isBlank(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.TWITTER.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(twitterConvId)) {
					log.error("recent message [{}] twitterConvId [{}], bNum [{}]", this.recentMessage, twitterConvId, conversation.getB_num());
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), twitterConvId),
							StringUtils.join(twitterConvId, conversation.getB_num()), true);
				}else if (StringUtils.isBlank(this.recentMessage) && Objects.nonNull(conversation.getLastMessageMetaData()) && Source.WHATSAPP.getSourceId().equals(conversation.getLastMessageMetaData().getLastMessageSource()) && StringUtils.isNotBlank(whatsappConvId)) {
					log.error("recent message [{}] whatsappConvId [{}], bNum [{}]", this.recentMessage, whatsappConvId, conversation.getB_num());
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), whatsappConvId),
							StringUtils.join(whatsappConvId, conversation.getB_num()), true);
				}
				else if (StringUtils.isEmpty(this.recentMessage)) {
					this.recentMessage = EncryptionUtil.decrypt(recentContentOpt.get().getText(),
							StringUtils.join(conversation.getB_num(), conversation.getM_c_id()),
							StringUtils.join(conversation.getM_c_id(), conversation.getB_num()), false);
				}
			} catch (Exception e) {
			}
		} else {
			this.recentMessage = recentContentOpt.isPresent() ? recentContentOpt.get().getText() : "";
		}
        //this.recentMessage = ControllerUtil.decode(this.recentMessage, "UTF-8");
        this.businessName = conversation.getB_alias() == null ? conversation.getB_name() : conversation.getB_alias();
		if (recentContentOpt.isPresent() && recentContentOpt.get().getEpoch() != null) {
			this.timeElapsed = getTimeElapsed(new Date(recentContentOpt.get().getEpoch()));
			this.lastMsgUTCTime = recentContentOpt.get().getEpoch();
			this.lastIncomingMessageTime = recentContentOpt.get().getEpoch();
		}
		if(Objects.nonNull(conversation.getLastIncomingMessageTime()) && filterType == ConversationView.MESSAGE){
			this.lastIncomingMessageTime=conversation.getLastIncomingMessageTime();
		}
	    this.recentVideoConversationContext = new VideoContext(conversation.getRecentVideoConversationContext());
	    this.viewedByUsers = conversation.getViewedBy();
	    this.referrer = Boolean.TRUE.equals(conversation.getReferrer());
	    this.lead = Boolean.TRUE.equals(conversation.getLead());
	    this.leadSource = conversation.getLeadOrigin();
	    this.referralSource = conversation.getReferralSource();
	    
	    if((customer.getCId()==null || customer.getCId()==0) && StringUtils.isNotBlank(conversation.getL_rvr_name())) {
	    	customer.setName(conversation.getL_rvr_name());
	    }
	    if(CollectionUtils.isNotEmpty(conversation.getReviews())) {
	    	Optional<Review> reviewOpt=conversation.getReviews().stream().findFirst();
	    	if(reviewOpt.isPresent()) {
	    		this.reviewId=reviewOpt.get().getId();
	    	}
	    }
		if(CollectionUtils.isNotEmpty(conversation.getPayments())){
			Optional<ContactDocument.Payment> paymentOpt = conversation.getPayments().stream().findFirst();
			paymentOpt.ifPresent(payment -> this.paymentId = payment.getPaymentId());
		}
    }

	private String translateMessageType(String lastMessageType, String l_msg) {
		if(StringUtils.isEmpty(l_msg)) {
			return "U";
		}
    	if("SEND".equalsIgnoreCase(lastMessageType)) {
    		return "S";
    	} else if("RECEIVE".equalsIgnoreCase(lastMessageType)) {
    		return "R";
    	} else if ("INTERNAL_NOTE".equalsIgnoreCase(lastMessageType)) {
    		return "I";
    	} else {
    		return "U";
    	}
	}

	public MessengerContactMessage(ContactDocument log, Integer source,String facebookId, Integer loggedInUserId, ConversationView filterType,boolean reviewsAccess,boolean surveysAccess,String googleConvId,String instagramConvId,String appleConvId,String twitterConvId, String whatsappConvId) {
        this(log, source, facebookId, filterType, reviewsAccess,surveysAccess,googleConvId, instagramConvId,appleConvId,twitterConvId,whatsappConvId);
		this.recentMessage = buildLastMessageString(log, this.recentMessage, loggedInUserId);
		this.read = buildReadStatus(log, loggedInUserId);
		this.open = buildOpenStatus(log, loggedInUserId);
	}

    private Boolean buildOpenStatus(ContactDocument log, Integer loggedInUserId) {
		Boolean isOpen = true;
		if(MessageTag.ARCHIVED.getCode() == log.getC_tag()) {
			isOpen = false;
		}
		return isOpen;
	}

	private Boolean buildReadStatus(ContactDocument log, Integer loggedInUserId) {
		Boolean isRead = false;
		if (MessageTag.CAMPAIGN.getCode() == log.getC_tag()) {
			//Mark Campaign conversations (tag = 5) as always read
			isRead = true;
		}else {
			List<Integer> viewedByUsers = log.getViewedBy();
			if ((null != log.getC_read() && log.getC_read()) || (CollectionUtils.isNotEmpty(viewedByUsers)
					&& viewedByUsers.contains(loggedInUserId))) {
				isRead = true;
			}
		}
		return isRead;
	}
    
    private String getTimeElapsed(Date lastActivityDate) {
        Calendar currDate = Calendar.getInstance(TimeZone.getTimeZone("PST"));
        Calendar msgTime = Calendar.getInstance(TimeZone.getTimeZone("PST"));
        msgTime.setTime(lastActivityDate);

        if (currDate.get(Calendar.YEAR) != msgTime.get(Calendar.YEAR)) {
            return new StringBuilder().append(msgTime.get(Calendar.MONTH) + 1).append("/").append(msgTime.get(Calendar.DATE)).append("/").append(msgTime.get(Calendar.YEAR) % 100).toString();
        } else if (currDate.get(Calendar.MONTH) != msgTime.get(Calendar.MONTH)) {
            return new StringBuilder().append(Month.getMonthById(msgTime.get(Calendar.MONTH)).getValue()).append(" ").append(msgTime.get(Calendar.DATE)).toString();
        }
        Long diffMilliSeconds = (new Date()).getTime() - lastActivityDate.getTime();

        if (TimeUnit.MILLISECONDS.toDays(diffMilliSeconds) > 0) {
            return new StringBuilder().append(TimeUnit.MILLISECONDS.toDays(diffMilliSeconds)).append("d").toString();
        } else if (TimeUnit.MILLISECONDS.toHours(diffMilliSeconds) > 0) {
            return new StringBuilder().append(TimeUnit.MILLISECONDS.toHours(diffMilliSeconds)).append("h").toString();
        } else if (TimeUnit.MILLISECONDS.toMinutes(diffMilliSeconds) > 0) {
            return new StringBuilder().append(TimeUnit.MILLISECONDS.toMinutes(diffMilliSeconds)).append("m").toString();
        } else {
            return "now";
        }
    }

    private String buildLastMessageString(ContactDocument document, String lastMessage, Integer loggedInUserId) {
		if(Objects.nonNull(document) && Objects.nonNull(document.getLastMessageMetaData()) && BooleanUtils.isTrue(document.getLastMessageMetaData().getLastMessageDeleted())){
			if(loggedInUserId.equals(document.getLastMessageMetaData().getLastMessageDeletedBy())){
				return "You deleted this message.";
			}else{
				return "This message was deleted.";
			}
		}
    	if(recentContentType!=null && (recentContentType == ContactDocument.ContentType.REVIEW || recentContentType==ContactDocument.ContentType.SURVEY_RESPONSE)) {
    		// recent message already set with appropriate value. No need for modification 
    		return recentMessage;
    	}
    	if(recentContentType==ContactDocument.ContentType.PAYMENT) {
    		if(PaymentStatus.NOT_PAID.name().equals(recentContentStatus) || PaymentStatus.INCOMPLETE.name().equals(recentContentStatus)) {
    			lastMessage = ": " + lastMessage;
    			recentMessage=appendUserNameInLastMessage(document, lastMessage, loggedInUserId);
    		}
    		return recentMessage;
    	}
		if (recentContentType != null && recentContentType == ContactDocument.ContentType.APPOINTMENT) {
			Optional<ContactDocument.Appointment> appointmentOptional = document.getAppointments().stream().filter(a -> a.getUpdatedAt().equals(document.getL_appointment_activity_on())).findFirst();
			if(appointmentOptional.isPresent()) {
				ContactDocument.Appointment appointment = appointmentOptional.get();

				this.appointmentStartTime = appointment.getStartTime();
				if (AppointmentSource.MICROSITE.getValue().equals(appointment.getSource())) {
					return "Appointment: " + appointment.getCustomerComment();
//				}else if (AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue().equals(appointment.getAction())) {
//					return "";
//				}
				} else if (AppointmentActionEventType.REMINDER_SENT.getValue().equals(appointment.getAction()) || AppointmentActionEventType.REMINDER_SENT_AUTOMATION.getValue().equals(appointment.getAction())) {
					return "Appointment reminder sent for " + DateUtils.convertDate(new Date(appointment.getStartTime()));
				}else {
					if (appointment.getUserId() != null) {
						lastMessage = ": " + lastMessage;
						recentMessage = appendUserNameInLastMessageOfAppointment(document, lastMessage, loggedInUserId, appointment);
					}
					return recentMessage;
				}
			}
		}
        if(StringUtils.isNotBlank(lastMessage) && StringUtils.isNotBlank(document.getLastMessageType())) {
            String lastMessageType = document.getLastMessageType();
            if (!"RECEIVE".equalsIgnoreCase(lastMessageType)) {
                lastMessage = ": " + lastMessage;
                if("INTERNAL_NOTE".equals(lastMessageType)) {
                    lastMessage = " added an internal note" + lastMessage;
                }
                
                //TODO: We should make the changes basis on lastMessageSource.
				if (document.getLastMessageUserId() != null) {
					if (document.getLastMessageUserId().equals(MessengerConstants.AUTO_CAMPAIGN_USER)) {
						return "Auto campaign" + lastMessage;
					} else if ((document.getLastMessageUserId().equals(MessengerConstants.AUTO_REPLY_USER)) || (document.getLastMessageUserId().equals(MessengerConstants.AUTO_REPLY_LIVE_CHAT_USER)) ) {
						return "Auto reply" + lastMessage;
					} else if (document.getLastMessageUserId().equals(MessengerConstants.VOICECALL_USER)) {
						return "Voicemail" + lastMessage;
					}
				}
				
            	return appendUserNameInLastMessage(document, lastMessage, loggedInUserId);
            } else if ("RECEIVE".equalsIgnoreCase(lastMessageType) && document.getLastMessageMetaData() != null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource()!= null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource() == Source.VOICE_CALL.getSourceId()) {
            		//BIRDEYE-66320 :  Added for showing Voicemail: before voicemail received messages
            		return "Voicemail: " + lastMessage;
            } else if ("RECEIVE".equalsIgnoreCase(lastMessageType) && document.getLastMessageMetaData() != null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource()!= null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource() == Source.CONTACT_US.getSourceId()) {
            		//BIRDEYE-66320 :  Added for showing Voicemail: before voicemail received messages
            		return "Webform: " + lastMessage;
            } else if ("RECEIVE".equalsIgnoreCase(lastMessageType) && document.getLastMessageMetaData() != null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource()!= null 
            		&& document.getLastMessageMetaData().getLastReceivedMessageSource() == Source.APPOINTMENT.getSourceId()) {
            		//BIRDEYE-66320 :  Added for showing Voicemail: before voicemail received messages
            		return "Appointment: " + lastMessage;
            }
            return lastMessage;
        }
        else return lastMessage;
    }

	private String appendUserNameInLastMessage(ContactDocument document, String lastMessage, Integer loggedInUserId) {
		// User information for birdeye users.
		if (StringUtils.isNotBlank(document.getLastMessageUserName())) {
		    if (loggedInUserId.equals(document.getLastMessageUserId())) {
		        return "You" + lastMessage;
		    }
		    return document.getLastMessageUserName() + lastMessage;
		} else {
		    return "User" + lastMessage;
		}
	}

	private String appendUserNameInLastMessageOfAppointment(ContactDocument document, String lastMessage, Integer loggedInUserId, ContactDocument.Appointment appointment) {
		// User information for birdeye users.
		String lastUserNameAppointment = appointment.getUserName();
		Integer lastUserId = Integer.valueOf(appointment.getUserId().toString());
		if (StringUtils.isNotBlank(lastUserNameAppointment)) {
			if (loggedInUserId.equals(lastUserId)) {
				return "You" + lastMessage;
			}
			return lastUserNameAppointment + lastMessage;
		} else {
			return "User" + lastMessage;
		}
	}
}
