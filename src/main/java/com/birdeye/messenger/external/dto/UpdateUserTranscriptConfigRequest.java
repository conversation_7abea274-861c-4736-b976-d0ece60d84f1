package com.birdeye.messenger.external.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateUserTranscriptConfigRequest {
    private Integer accountId;
    private List<UserTranscriptConfig> userTranscriptConfigs;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserTranscriptConfig {
        private Integer userId;
        private boolean enableTranscript;
        private String transcriptType;
    }
}
