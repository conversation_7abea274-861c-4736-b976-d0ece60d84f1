package com.birdeye.messenger.external.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class ConversationResponse {

    private List<MessengerContactMessage> messengerContactMessages;
    private Long unreadBizCount; // not required for V2
    private Long count;
    private Map<Integer, String> businessFbIntegrationStatusMap;

}
