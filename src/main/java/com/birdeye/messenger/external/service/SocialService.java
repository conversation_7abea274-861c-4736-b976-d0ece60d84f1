package com.birdeye.messenger.external.service;

import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;

import com.birdeye.messenger.dto.GoogleSendSocialRequest;
import com.birdeye.messenger.dto.LikeUnlikeEventSocial;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SendFacebookMessage;
import com.birdeye.messenger.dto.SendInstagramMessage;
import com.birdeye.messenger.dto.SendTwitterMessage;
import com.birdeye.messenger.dto.SendWAMessage;
import com.birdeye.messenger.dto.SendWAMessageResponse;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatus;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatusRequest;
import com.birdeye.messenger.dto.facebook.GetUserDetailsMessage;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.dto.instagram.GetInstagramDetailsMessage;
import com.birdeye.messenger.dto.instagram.InstagramUserDetailsMessage;
import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateDto;
import com.birdeye.messenger.dto.whatsapp.WAMediaResponse;

public interface SocialService {

	ResponseEntity<String> sendFacebookMessage(SendFacebookMessage message);

    ResponseEntity<String> sendGoogleMessage(GoogleSendSocialRequest socialRequest);

    UserDetailsMessage getFacebookUserInfo(GetUserDetailsMessage message);

    Map<?, ?> getFacebookIntegrationStatusFromSocial(Integer businessId);

    Map<?,?> getGoogleIntegrationStatusFromSocial(Integer businessId);

    Integer getBusinessIdMappedWithGooglePlaceId(String googlePlaceId);
    
    InstagramUserDetailsMessage getInstagramUserInfo(GetInstagramDetailsMessage message);
    
    ResponseEntity<String> sendInstagramMessage(SendInstagramMessage message);

    Map<?,?>  getInstagramIntegrationStatusFromSocial(Integer businessId);
    
    String getInstagramPageIdByBusinessId(Integer businessId);

    Map<?,?> getBusinessIdByInstagramPageId(String businessInstagramId);

    String getFacebookPageIdByBusinessId(Integer businessId);

    Integer getBusinessIdByFacebookPageId(String facebookPageId);


    String getTwitterPageIdByBusinessId(Integer businessId);

    ResponseEntity<String> sendTwitterMessage(SendTwitterMessage message);

    SocialChannelIntegrationStatus getAllSocialChannelIntegrationStatus(SocialChannelIntegrationStatusRequest request);
    
    void sendMessageLikeUnlikeEvent(LikeUnlikeEventSocial request);

    void pushReceiveEventToSocial(MessageDTO messageDTO);

    public Map<String,String> getBusinessIdAndBirdeyeCdnAttachmentUrl(Map<String,Object> request,String businessTwitterId);

	public String getWabaNameByWabaId(String wabaId, Integer businessId);

	public List<ExternalWhatsAppTemplateDto> getAllTemplatesByWabaId(String wabaId);

	WAMediaResponse getWAMediaById(String mediaId, String phoneNumberId);

	SendWAMessageResponse sendWhatsappMessage(SendWAMessage sendWAMessage);

	String getWAPhoneNumberIdByBusinessId(Integer businessId);
}
