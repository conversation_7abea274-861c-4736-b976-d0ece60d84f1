package com.birdeye.messenger.external.service;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.json.JSONObject;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.external.dto.SurveyResponse;
import com.birdeye.messenger.util.ControllerUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class SurveyServiceImpl implements SurveyService {

	@Value("${survey.service.url}")
	private String surveyServiceUrl;

	@Autowired
	private RestTemplate restTemplate;

	private final String X_Bazaarify_Session_Token = "X-Bazaarify-Session-Token";
	private final String ALLOW_PUBLIC_ACCESS= "ALLOW_PUBLIC_ACCESS";

	@Override
	public SurveyResponse getSurveyById(Integer surveyId) {
		String url = this.surveyServiceUrl + GET_SURVEY_LITE + surveyId;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(X_Bazaarify_Session_Token,ALLOW_PUBLIC_ACCESS);
		log.info("Calling Survey with url {}", url);
		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>("{}", headers), String.class);
		if (!response.getStatusCode().is2xxSuccessful())
			throw new MessengerException(ErrorCode.HTTP_CONNECTION_ERROR,
					"Error in response from Survey api getSurveyById for surveyId" + surveyId);
		return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(response.getBody()),SurveyResponse.class);
	}

	@Override
	public Optional<String> getNameBySurveyId(Integer surveyId, Integer businessId) {
		Optional<String> optionalName = Optional.empty();

		String url = this.surveyServiceUrl + GET_SURVEY_LITE + surveyId;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(X_Bazaarify_Session_Token,ALLOW_PUBLIC_ACCESS);
		// Added business Id in header to facilitate survey name token replacement at Survey end
		headers.add("businessId", String.valueOf(businessId));
		try {
			HttpEntity<String> request = new HttpEntity<>("{}",headers);
			ResponseEntity<String> response = restTemplate.exchange(url,HttpMethod.POST,
					request, String.class);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("[SurveyServiceImpl] getNameBySurveyId failed for surveyId {} with status {} ", surveyId,
						response.getStatusCode());
			}
			JSONObject jsonObject = new JSONObject(response.getBody());
			if (jsonObject.has("name")){
				String name = jsonObject.getString("name");
				optionalName = Optional.ofNullable(name);
			}
		}
		catch (Exception e) {
			log.info("[SurveyServiceImpl] getNameBySurveyId for SurveyId:{} failed due to:{}", surveyId, e.getMessage());
		}
		return optionalName;
	}

	@Override
	public Map<Integer, String> getTokensReplacedSurveyNames(Integer surveyId, Set<Integer> businessIds) {
		String url = this.surveyServiceUrl + GET_TOKEN_REPLACED_SURVEY_NAMES;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		log.info("Calling Survey with url {}", url);
		Map<String, Object> request = new HashMap<>();
		request.put("surveyId", surveyId);
		request.put("businessIds", businessIds);
		ResponseEntity<Map<Integer,String>> response = restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(request, headers),
				new ParameterizedTypeReference<Map<Integer,String>>(){});
		if (!response.getStatusCode().is2xxSuccessful())
			throw new MessengerException(ErrorCode.HTTP_CONNECTION_ERROR,
					"Error in response from Survey api getTokensReplacedSurveyNames for surveyId" + surveyId);
		return response.getBody();
	}

	public static  class FilterCriteria	{

	}
}
