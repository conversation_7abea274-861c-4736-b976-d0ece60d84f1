package com.birdeye.messenger.external.service;

import java.util.Collections;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.EmailCampaignRequest;
import com.birdeye.messenger.external.dto.ReviewRequestResponse;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CampaignServiceImpl implements CampaignService {

	@Value("${campaign.service.url}")
	private String campaignUrl;
	
	@Autowired
	private RestTemplate restTemplate;
	
	@Override
	public ReviewRequestResponse createReviewRequestAndBitlyUrl(CampaignReviewRequest campaignReviewRequest) {
		String url = this.campaignUrl + RR_CREATE;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		
		HttpEntity<CampaignReviewRequest> campaignRequest = new HttpEntity<CampaignReviewRequest>(campaignReviewRequest, headers);
		
		ReviewRequestResponse response = restTemplate.postForObject(url, campaignRequest, ReviewRequestResponse.class, headers);
		
		return response;
	}

	@Override
	public void createEmailCampaignRequest(EmailCampaignRequest campaignRequest) {
		String url = this.campaignUrl + MessengerConstants.CAMAPIGN_EMAIL;
		log.debug("Sending campaign event call for campaignUrl {} and  campaignRequest {} ", url, campaignRequest);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<EmailCampaignRequest> emailCampaignRequest = new HttpEntity<EmailCampaignRequest>(campaignRequest,
				headers);
		try {
		restTemplate.postForLocation(url, emailCampaignRequest);
		} catch (Exception e) {
			log.error("Exception while calling campaign service to send campaign event from email inbox", e);
		}
	}
	
}
