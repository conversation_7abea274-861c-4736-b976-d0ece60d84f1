package com.birdeye.messenger.external.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.FacebookPageInfoDto;
import com.birdeye.messenger.dto.GoogleSendSocialRequest;
import com.birdeye.messenger.dto.LikeUnlikeEventSocial;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.ReceiveEventForSocialDto;
import com.birdeye.messenger.dto.SendFacebookMessage;
import com.birdeye.messenger.dto.SendInstagramMessage;
import com.birdeye.messenger.dto.SendTwitterMessage;
import com.birdeye.messenger.dto.SendWAMessage;
import com.birdeye.messenger.dto.SendWAMessageResponse;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatus;
import com.birdeye.messenger.dto.SocialChannelIntegrationStatusRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.facebook.GetUserDetailsMessage;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.dto.instagram.GetInstagramDetailsMessage;
import com.birdeye.messenger.dto.instagram.InstagramUserDetailsMessage;
import com.birdeye.messenger.dto.whatsapp.ExternalWhatsAppTemplateDto;
import com.birdeye.messenger.dto.whatsapp.WAMediaResponse;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MediaUtils;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class SocialServiceImpl implements SocialService {

    private final RestTemplate restTemplate;
   
    @Value("${social.messenger.url}")
    private String socialUrl;

    @Value("${social.base.url}")
    private String socialBaseUrl;

    @Value("${social.post.url}")
    private String socialPostUrl;

    @Autowired
    protected KafkaService kafkaService;
    

    @Override
    public ResponseEntity<String> sendFacebookMessage(SendFacebookMessage message) {
        String url = this.socialUrl + "/facebook/send";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SendFacebookMessage> httpEntity = new HttpEntity<>(message, headers);
        return restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
    }
    
    @Override
    public ResponseEntity<String> sendInstagramMessage(SendInstagramMessage message) {
        String url = this.socialBaseUrl + "/instagram/message/send";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SendInstagramMessage> httpEntity = new HttpEntity<>(message, headers);
        return restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
    }

    @Override
    public ResponseEntity<String> sendGoogleMessage(GoogleSendSocialRequest socialRequest) {
        String url = this.socialUrl + "/google/send";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<GoogleSendSocialRequest> httpEntity = new HttpEntity<>(socialRequest, headers);
        ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        if (!exchange.getStatusCode().is2xxSuccessful()) {
            log.info("Google Message Send Exception on Social Service {}",exchange.getStatusCode());
            throw new MessengerException("Google Message Exception");
        }
        return exchange;
    }

    @Override
    public UserDetailsMessage getFacebookUserInfo(GetUserDetailsMessage message) {
        String url = this.socialUrl + "/facebook/userdetails";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<GetUserDetailsMessage> httpEntity = new HttpEntity<>(message, headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getFacebookUserInfo", startTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("Error - [{}] from social api - {} request payload - {} creating anonymous user",ErrorCode.HTTP_CONNECTION_ERROR, url,message);
            UserDetailsMessage userDetailsMessage = new UserDetailsMessage(Constants.ANONYMOUS_FACEBOOK_USER_FIRST_NAME,Constants.ANONYMOUS_FACEBOOK_USER_LAST_NAME,null,null);
            return userDetailsMessage;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), UserDetailsMessage.class);
    }

    @Override
    public Map<?, ?> getFacebookIntegrationStatusFromSocial(Integer businessId) {
        String url = this.socialPostUrl + "/facebook/status/" + businessId;
        return restTemplate.getForObject(url, Map.class);
    }

    @Override
    public Map<?, ?> getGoogleIntegrationStatusFromSocial(Integer businessId) {
        String url = this.socialBaseUrl + "/social/gmb/status/" + businessId;
        return restTemplate.getForObject(url, Map.class);
    }

    @Override
    public Integer getBusinessIdMappedWithGooglePlaceId(String googlePlaceId) {
        String url = this.socialBaseUrl + "/social/gmb/gmb-location?placeId=" + googlePlaceId;
        return restTemplate.getForObject(url, Integer.class);
    }
    
    @Override
    public InstagramUserDetailsMessage getInstagramUserInfo(GetInstagramDetailsMessage message) {
        String url = this.socialBaseUrl + "/instagram/message/user";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<GetInstagramDetailsMessage> httpEntity = new HttpEntity<>(message, headers);
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        if (!res.getStatusCode().is2xxSuccessful()) {
        	log.info(new ErrorMessageBuilder(ErrorCode.HTTP_CONNECTION_ERROR, ComponentCodeEnum.IG)
        			.message("Error from social api - {} creating instagram anonymous User  for userId - {}", res.getBody(), message.getUserId()).build());
            InstagramUserDetailsMessage instagramUserDetailsMessage = new InstagramUserDetailsMessage();
            instagramUserDetailsMessage.setId(null);
            instagramUserDetailsMessage.setName(Constants.ANONYMOUS_USER);
            instagramUserDetailsMessage.setProfile_pic(null);
            return instagramUserDetailsMessage;
        }         
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), InstagramUserDetailsMessage.class);
    }

    @Override
    public Map<?, ?> getInstagramIntegrationStatusFromSocial(Integer businessId) {
        String url = this.socialBaseUrl + "/social/instagram/status/" + businessId;
        return restTemplate.getForObject(url, Map.class);
    }

	@Override
	public String getInstagramPageIdByBusinessId(Integer businessId) {
		String pageId = null;
		String url = this.socialBaseUrl + "/social/instagram/businessid/" + businessId;
		pageId = restTemplate.getForObject(url, String.class);
		if (StringUtils.isBlank(pageId)) {
			throw new NotFoundException(new ErrorMessageBuilder(ErrorCode.INSTAGRAM_PAGE_NOT_FOUND,
					ComponentCodeEnum.IG, HttpStatus.NOT_FOUND).message("No instagram page found for business - {}", businessId));
					
		}

		return pageId;
	}

	@Override
	public Map<?,?> getBusinessIdByInstagramPageId(String businessInstagramId) {
		String url = this.socialBaseUrl + "/social/instagram/" + businessInstagramId+"?fields=businessId";
	        return restTemplate.getForObject(url, Map.class);
	   }

    @Override
    public String getFacebookPageIdByBusinessId(Integer businessId) {
        log.info("calling social service to fetch business FacebookPageId By BusinessId {}",businessId);
        String url = this.socialBaseUrl +  "/social/page/filters?channel=FACEBOOK&businessId="+businessId.toString();
        FacebookPageInfoDto facebookPageInfoRequest = getFacebookPageInfoRequest(url);
        if (facebookPageInfoRequest == null) {
            return null;
        }
        return facebookPageInfoRequest.getPageId();
    }

    @Override
    public Integer getBusinessIdByFacebookPageId(String facebookPageId) {
        log.info("calling social service to fetch businessId for facebookPageId : {}",facebookPageId);
        String url = this.socialBaseUrl +  "/social/page/filters?channel=FACEBOOK&socialId="+facebookPageId;
        FacebookPageInfoDto facebookPageInfoRequest = getFacebookPageInfoRequest(url);
        if (facebookPageInfoRequest == null) {
            return null;
        }
        return facebookPageInfoRequest.getBusinessId();
    }

    public FacebookPageInfoDto getFacebookPageInfoRequest(String url){
        log.info("calling social service to fetch business facebook page data: social service url {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<?> entity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<FacebookPageInfoDto> responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, FacebookPageInfoDto.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getFacebookPageInfoRequest", startTime, endTime);
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
            log.info("error in fetching business facebook page data from social service");
            return null;
        }
        return responseEntity.getBody();
    }
    @Override
    public String getTwitterPageIdByBusinessId(Integer businessId) {
        String pageId = null;
        String url = this.socialBaseUrl + "/social/twitter/businessid/" + businessId;
        pageId = restTemplate.getForObject(url, String.class);
        if (StringUtils.isBlank(pageId)) {
            throw new NotFoundException(new ErrorMessageBuilder(ErrorCode.TWITTER_PAGE_NOT_FOUND,
                    ComponentCodeEnum.IG, HttpStatus.NOT_FOUND).message("No twitter page found for business - {}", businessId));

        }
        return pageId;
    }

    @Override
    public ResponseEntity<String> sendTwitterMessage(SendTwitterMessage message) {
        String url = this.socialBaseUrl + "/twitter/messenger/send";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SendTwitterMessage> httpEntity = new HttpEntity<>(message, headers);
        return restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
    }

    @Override
    @Cacheable(cacheNames = Constants.SOCIAL_CACHE, key="'IntegrationStatus-'.concat(#request.getBusinessId())", unless="#result == null")
    public SocialChannelIntegrationStatus getAllSocialChannelIntegrationStatus(SocialChannelIntegrationStatusRequest request){
        String url = this.socialBaseUrl + "/social/account/all/status";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<SocialChannelIntegrationStatusRequest> httpEntity = new HttpEntity<>(request, headers);
        ResponseEntity<SocialChannelIntegrationStatus> response = restTemplate.exchange(url, HttpMethod.POST, httpEntity, SocialChannelIntegrationStatus.class);
        if (!response.getStatusCode().is2xxSuccessful()) {
            log.info("Error in response from social service to getAllSocialChannelIntegrationStatus {}",response.getStatusCode());
            throw new MessengerException(ErrorCode.HTTP_CONNECTION_ERROR,"Error response from social service to getAllSocialChannelIntegrationStatus");
        }
        return response.getBody();
    }
    public void sendMessageLikeUnlikeEvent(LikeUnlikeEventSocial request){
        log.info("Send message like/unlike event for social: {}",request);
        String url=this.socialBaseUrl+"/social/v2/engage/message/like";
        HttpHeaders headers=new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<LikeUnlikeEventSocial> httpEntity=new HttpEntity<>(request,headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> responseEntity = restTemplate.exchange(url,HttpMethod.POST,httpEntity,String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("sendMessageLikeUnlikeEvent", startTime, endTime);
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
            log.info("error in sending like/unlike request to social");
            throw new MessengerException("Social Like/Unlike Event Failure");
        }
    }
        
    @Async
    @Override
    public void pushReceiveEventToSocial(MessageDTO messageDTO){
    	log.info("pushReceiveEventToSocial - source: {} and msgType: {}",messageDTO.getSource(),messageDTO.getMsgTypeForResTimeCalc());
    	try{
    		if(!(Source.INSTAGRAM.getSourceId().equals(messageDTO.getSource()) || Source.TWITTER.getSourceId().equals(messageDTO.getSource()) || Source.FACEBOOK.getSourceId().equals(messageDTO.getSource()) || Source.GOOGLE.getSourceId().equals(messageDTO.getSource()))){
    			log.info("Not a valid source to push event to social");
    			return;
    		}
    		MessageDocument messageDocument = messageDTO.getMessageDocument();
    		ReceiveEventForSocialDto event = new ReceiveEventForSocialDto();
    		event.setMessageId(messageDocument.getId().toString());
    		
    		if (("S").equals(messageDTO.getMsgTypeForResTimeCalc()) || messageDTO.isBOT()) {
    			if (messageDocument.getU_id()<=0 && messageDocument.getU_id() != MessengerConstants.ROBIN_REPLY_USER) {
    				log.info("Not a valid user event to push to social");
        			return;
    			}
    		}
    		if (("R").equals(messageDTO.getMsgTypeForResTimeCalc())) {
    			event.setPageId(messageDocument.getFrom());
        		event.setAuthorId(messageDocument.getTo());
    		}
    		if (("S").equals(messageDTO.getMsgTypeForResTimeCalc()) || messageDTO.isBOT()) {
    			event.setPageId(messageDocument.getTo());
        		event.setAuthorId(messageDocument.getFrom());
    		}
    		
    		event.setChannel(messageDocument.getChannel().name());
    		event.setMsgBody(MessengerUtil.decryptMessage(messageDocument));
    		event.setAuthorName(messageDTO.getCustomerDTO().getDisplayName());
    		event.setAuthorProfileImage(messageDTO.getContactDocument().getImageUrl());
    		event.setCreatedAt(messageDTO.getMessageDocumentDTO().getCr_date());
    		event.setAttachments(messageDTO.getMessageDocument().getMediaFiles());
    		event.setBusinessId(messageDTO.getBusinessDTO().getBusinessId());
    		event.setCustomerId(messageDTO.getCustomerDTO().getId());
    		event.setEnterpriseId(messageDTO.getBusinessDTO().getAccountId());
    		event.setMessengerContactId(messageDTO.getMessengerContact().getId());
    		if(StringUtils.isNotBlank(messageDTO.getStoryMentionUrl())){
    			event.setType(Constants.STORY_MENTION);
    			Map<String,Object> mediaDetails =MediaUtils.getContentHeaders(messageDTO.getStoryMentionUrl());
    			MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile(mediaDetails.get("content-type").toString(),messageDTO.getStoryMentionUrl(),mediaDetails.get("content-length").toString(),
    					null,mediaDetails.get("content-type").toString());
    			event.setAttachments(Collections.singletonList(mediaFile));
    		}else if(StringUtils.isNotBlank(messageDTO.getStoryReplyUrl())){
    			event.setType(Constants.STORY_REPLY);
    			Map<String,Object> mediaDetails =MediaUtils.getContentHeaders(messageDTO.getStoryReplyUrl());
    			MessageDocument.MediaFile mediaFile = new MessageDocument.MediaFile(mediaDetails.get("content-type").toString(),messageDTO.getStoryMentionUrl(),mediaDetails.get("content-length").toString(),
    					null,mediaDetails.get("content-type").toString());
    			event.setAttachments(Collections.singletonList(mediaFile));
    		}else{
    			event.setType("message");
    		}
    		event.setCommunicationDirection(messageDocument.getCommunicationDirection().name());
    		event.setBirdeyeUserId(messageDocument.getU_id());
    		
    		log.info("pushReceiveEventToSocial : {}", event);
    		kafkaService.publishToKafkaAsync(KafkaTopicEnum.SOCIAL_RECEIVE_MESSAGE_EVENT,
    				messageDTO.getBusinessDTO().getAccountId(), event);
    	}catch(Exception e){
    		log.info("Exception while pushing social receive message event for m_id: {} and mcId: {} ",messageDTO.getMessageDocument().getId(),messageDTO.getMessengerContact().getId(),e);
    	}
    }

    @Override
    public Map<String,String> getBusinessIdAndBirdeyeCdnAttachmentUrl(Map<String,Object> request,String businessTwitterId){
        String url = this.socialBaseUrl + "/social/twitter/" + businessTwitterId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String,Object>> httpEntity = new HttpEntity<>(request, headers);
        ResponseEntity<Map> responseEntity = restTemplate.exchange(url,HttpMethod.POST,httpEntity,Map.class);
        return responseEntity.getBody();
    }
    
    /**
     * Fetches the WABA name from the social service using either the WABA ID or business ID.
     * currently not used in code.
     */
    @Override
	public String getWabaNameByWabaId(String wabaId, Integer businessId) {
		String url = this.socialBaseUrl + "/social/whatsapp/waba/details";
		if (StringUtils.isNotBlank(wabaId)) {
			url += "?wabaId=" + wabaId;
		} else if (businessId != null) {
			url += "?businessId=" + businessId;
		} else {
			throw new MessengerException("wabaId and businessId can't be null");
		}

		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<?> entity = new HttpEntity<>(headers);
			ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, 
					new ParameterizedTypeReference<Map<String, Object>>() {});
			if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
				return responseEntity.getBody().get("wabaName").toString();
			} else {
//				log.error("social service get waba name request failed");
//				throw new MessengerException("Rest call failed");
				return "Birdeye WABA";
			}
		} catch (Exception e) {
			log.error("error in fetching waba name from social service");
			throw new MessengerException("Social WABA Name Fetch Failure");
		}
	}
    
    @Override
	public List<ExternalWhatsAppTemplateDto> getAllTemplatesByWabaId(String wabaId) {
		String url = this.socialBaseUrl + "/social/whatsapp/template/data/" + wabaId;

		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<?> entity = new HttpEntity<>(headers);
			log.info("calling social service to get all templates for wabaid: {}", wabaId);
			ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, 
					new ParameterizedTypeReference<Map<String, Object>>() {});

			if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
				return JSONUtils.collectionFromJSON(JSONUtils.toJSON(responseEntity.getBody().get("data")), ExternalWhatsAppTemplateDto.class);
			} else {
				log.error("social service get all templates request failed");
				throw new MessengerException("Rest call failed");
			}
		} catch (Exception e) {
			log.error("error in fetching all templates by wabaId from social service");
			throw new MessengerException("Social all templated fetch request failed");
		}
	}

	@Override
	public WAMediaResponse getWAMediaById(String mediaId, String phoneNumberId) {
		String url = String.format("%s/social/whatsapp/media/download?mediaId=%s&phoneNumberId=%s", 
				this.socialBaseUrl, mediaId, phoneNumberId);

		try {
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);

			HttpEntity<?> entity = new HttpEntity<>(headers);

			ResponseEntity<WAMediaResponse> responseEntity = restTemplate.exchange(url, HttpMethod.GET, entity, WAMediaResponse.class);

			if (responseEntity.getStatusCode().is2xxSuccessful()) {
				return responseEntity.getBody();
			} else {
				log.error("Media download request failed with status: {}", responseEntity.getStatusCode());
				throw new MessengerException("Media download rest call failed");
			}
		} catch (Exception e) {
			log.error("Error downloading media from social service for mediaId: {}, phoneNumberId: {}", mediaId, phoneNumberId, e);
			throw new MessengerException("Media download request failed");
		}
	}

	@Override
	public SendWAMessageResponse sendWhatsappMessage(SendWAMessage sendWAMessage) {
		String url = this.socialBaseUrl + "/social/whatsapp/send/message";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<SendWAMessage> httpEntity = new HttpEntity<>(sendWAMessage, headers);

		ResponseEntity<SendWAMessageResponse> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, SendWAMessageResponse.class);
		if (responseEntity.getStatusCode().is2xxSuccessful()) {
			return responseEntity.getBody();
		} else {
			log.error("Send WA Msg failed with status: {}", responseEntity.getStatusCode());
			throw new MessengerException("Send WA Msg rest call failed");
		}
	}

	@Override
	public String getWAPhoneNumberIdByBusinessId(Integer businessId) {
		String phoneNumberId = null;
		String url = this.socialBaseUrl + "/social/whatsapp/businessid/" + businessId;
		phoneNumberId = restTemplate.getForObject(url, String.class);
		if (StringUtils.isBlank(phoneNumberId)) {
			throw new NotFoundException(new ErrorMessageBuilder(ErrorCode.WAPHNO_NOT_FOUND,
					ComponentCodeEnum.WHATSAPP, HttpStatus.NOT_FOUND).message("No WA phno found for business - {}", businessId));

		}
		return phoneNumberId;
	}

}
