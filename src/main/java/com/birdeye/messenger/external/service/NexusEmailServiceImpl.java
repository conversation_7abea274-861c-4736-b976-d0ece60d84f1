package com.birdeye.messenger.external.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.EmailDTO;
import com.birdeye.messenger.dto.NexusEmailDTO;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.service.KafkaService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class NexusEmailServiceImpl implements NexusEmailService {

    @Value("${nexus.url}")
    private String nexusBaseUrl;

    private String NEXUS_EMAIL_ENDPOINT = "/email/send";

    private final RestTemplate restTemplate;
    
    private final KafkaService kafkaService;

	@Override
	public <T> void sendMail(Integer businessId, EmailDTO emailDTO, String requestType, String externalUid, Long businessNumber, T dataObject) {
        //TODO: Add code to check whether sendMailViaNexus is enabled or not. If not send through sendGrid API. Check platform code to help
        emailDTO.setBusinessId(String.valueOf(businessId));
        NexusEmailDTO<T> nexusEmailMessage = getNexusEmailInputMessage(businessId, businessNumber, emailDTO, requestType, StringUtils.isNotBlank(emailDTO.getRequestSubType()) ? emailDTO.getRequestSubType() : requestType, externalUid, dataObject);
        callNexusAPI(nexusEmailMessage);
        // TODO: push to kafka instead of directly calling the API. Call API on catch block on fallback.
    }

    private <T> void callNexusAPI(NexusEmailDTO<T> nexusEmailMessage) {
        try {
            StringBuilder url = new StringBuilder(nexusBaseUrl);
            url.append(NEXUS_EMAIL_ENDPOINT);
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<NexusEmailDTO> requestBody = new HttpEntity<>(nexusEmailMessage, headers);
            ResponseEntity<Object> response = restTemplate.exchange(url.toString(), HttpMethod.POST, requestBody, Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to send email for {}", nexusEmailMessage);
            }
        } catch (Exception e) {
            log.error("NEXUS API call failed for email data {} with error {}", nexusEmailMessage, e);
        }
    }
    @Override
	public <T> void sendMailV2(Integer businessId, EmailDTO emailDTO, String requestType, String externalUid,
			Long businessNumber, T dataObject) {
		emailDTO.setBusinessId(String.valueOf(businessId));
		NexusEmailDTO<T> nexusEmailMessage = getNexusEmailInputMessage(businessId, businessNumber, emailDTO,
				requestType,
				StringUtils.isNotBlank(emailDTO.getRequestSubType()) ? emailDTO.getRequestSubType() : requestType,
				externalUid, dataObject);
		try {
			// BIRDEYE-63481 : Split list of 'to' and send mail one by one
			List<String> allEmailIds = nexusEmailMessage.getEmailGenericData().getTo();
			for (String emailId : allEmailIds) {
				nexusEmailMessage.getEmailGenericData().clearAndAddTo(emailId);
				kafkaService.publishToNexusKafkaAsync(KafkaTopicEnum.NEXUS_EMAIL_SEND, null, nexusEmailMessage);

			}
		} catch (Exception e) {
			log.error("error while pushing email data {} to kafka : {}",
					new Object[] { nexusEmailMessage, e.getMessage() });
			throw e;
		}
	}
    private <T> NexusEmailDTO<T> getNexusEmailInputMessage(Integer businessId, Long businessNumber, EmailDTO emailMessage, String type, String subType, String externalUid, T dataObject) {
        NexusEmailDTO<T> nexusEmailMessage = new NexusEmailDTO<>();
        nexusEmailMessage.setEmailBody(emailMessage.getBody());
        nexusEmailMessage.setEmailGenericData(getEmailGenericData(emailMessage));
        nexusEmailMessage.setEmailMetaData(getEmailMetaData(businessId, businessNumber, emailMessage, type, subType, externalUid));
        nexusEmailMessage.setDataObject(dataObject);
        return nexusEmailMessage;
    }

    private NexusEmailDTO.NexusEmailMetaData getEmailMetaData(Integer businessId, Long businessNumber, EmailDTO emailMessage, String type, String subType, String externalUid) {
        NexusEmailDTO.NexusEmailMetaData emailMetaData = new NexusEmailDTO.NexusEmailMetaData();
        emailMetaData.setBusinessNumber(businessNumber);
        emailMetaData.setBusinessId(businessId);
        emailMetaData.setExternalUid(externalUid);
        emailMetaData.setRequestId(emailMessage.getRequestId());
        emailMetaData.setEmailType(type);
        emailMetaData.setEmailSubType(subType);
        emailMetaData.setApplyBranding(true);
		emailMetaData.setCustomerId(emailMessage.getCustomerId());
		emailMetaData.setRecipientType(emailMessage.getRecipientType());
		emailMetaData.setCustomTemplate(emailMessage.isCustomTemplate());
		emailMetaData.setEmailCategory(emailMessage.getEmailCategory());
		emailMetaData.setIsCustomerEmail(emailMessage.getIsCustomerEmail());
        return emailMetaData;

    }

    private NexusEmailDTO.NexusEmailGenericData getEmailGenericData(EmailDTO emailMessage) {
        NexusEmailDTO.NexusEmailGenericData emailGenericData = new NexusEmailDTO.NexusEmailGenericData();
        emailGenericData.setFrom(emailMessage.getFrom());
        emailGenericData.setFromName(emailMessage.getFromName());
        emailGenericData.setReplyTo(emailMessage.getReplyTo());
        emailGenericData.setSubject(emailMessage.getSubject());
        emailGenericData.setTextType(emailMessage.getTextType());
        emailGenericData.setTo(emailMessage.getTo());
        emailGenericData.setAttachments(emailMessage.getAttachments());
        emailGenericData.setBcc(emailMessage.getBcc());
		emailGenericData.setS3AttachementUrls(emailMessage.getS3AttachementUrls());
		emailGenericData.setFileAttachementData(getEmailAttachementData(emailMessage));
        return emailGenericData;
    }

	private List<NexusEmailDTO.EmailFileAttachementData> getEmailAttachementData(EmailDTO emailDTO) {
		List<NexusEmailDTO.EmailFileAttachementData> fileAttchments = new ArrayList<NexusEmailDTO.EmailFileAttachementData>();
		if (CollectionUtils.isNotEmpty(emailDTO.getFileAttachementData())) {
			for (NexusEmailDTO.EmailFileAttachementData attachment : emailDTO.getFileAttachementData()) {
				NexusEmailDTO.EmailFileAttachementData emailFileAttchment = new NexusEmailDTO.EmailFileAttachementData();
				emailFileAttchment.setContent(attachment.getContent());
				emailFileAttchment.setFileName(attachment.getFileName());
				emailFileAttchment.setType(attachment.getType());
				emailFileAttchment.setUrl(attachment.getUrl());
				fileAttchments.add(emailFileAttchment);
			}
		}
		return fileAttchments;
	}
}
