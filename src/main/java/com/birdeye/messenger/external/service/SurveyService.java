package com.birdeye.messenger.external.service;

import com.birdeye.messenger.external.dto.SurveyResponse;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public interface SurveyService {

	String GET_SURVEY_LITE = "/survey/get/";

	String GET_TOKEN_REPLACED_SURVEY_NAMES = "/survey/get-token-replaced-survey-names";

	SurveyResponse getSurveyById(Integer surveyId);

	Optional<String> getNameBySurveyId(Integer surveyId, Integer businessId);

	Map<Integer, String> getTokensReplacedSurveyNames(Integer surveyId, Set<Integer> businessIds);
}
