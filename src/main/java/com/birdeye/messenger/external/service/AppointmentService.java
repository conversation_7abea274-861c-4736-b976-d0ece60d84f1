package com.birdeye.messenger.external.service;

import com.birdeye.messenger.dto.AppointmentApiResponseDto;
import com.birdeye.messenger.dto.AppointmentConfirmationRequestDto;
import com.birdeye.messenger.dto.AppointmentConfirmationResponseDto;

/**
 * <AUTHOR>
 *
 */
public interface AppointmentService {

	AppointmentApiResponseDto getAppointmentDetails(Long appointmentId, Integer accountId);

    AppointmentConfirmationResponseDto confirmAppointment(String altAppointmentId, String pmsAppointmentId, Integer businessId);

}
