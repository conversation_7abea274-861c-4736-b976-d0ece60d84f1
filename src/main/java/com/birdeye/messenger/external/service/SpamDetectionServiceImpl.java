package com.birdeye.messenger.external.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.entity.OutboundSpamMessageAudit;
import com.birdeye.messenger.dao.repository.OutboundSpamMessageAuditRepository;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.CheckSpamRequestDto;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.LiveChatMessageDTO;
import com.birdeye.messenger.dto.MarkBlockAndSpamRequestDto;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.OutboundSpamNotificationDTO;
import com.birdeye.messenger.dto.PhishingDetectionRequest;
import com.birdeye.messenger.dto.SMSMessageDTO;
import com.birdeye.messenger.dto.SendMessageDTO;
import com.birdeye.messenger.dto.SpamDetectionRequestDto;
import com.birdeye.messenger.dto.SpamDetectionResponseDto;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.WebchatMessageDTO;
import com.birdeye.messenger.dto.elastic.ContactDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.facebook.Entry;
import com.birdeye.messenger.dto.facebook.FacebookMessageRequest;
import com.birdeye.messenger.dto.facebook.Message;
import com.birdeye.messenger.dto.googleBusinessMessaging.GoogleUserMessage;
import com.birdeye.messenger.dto.instagram.InstagramMessageRequest;
import com.birdeye.messenger.dto.twitter.MessageCreate;
import com.birdeye.messenger.dto.twitter.TwitterMessageRequest;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.enums.KafkaTopicEnum;
import com.birdeye.messenger.enums.MessageTag;
import com.birdeye.messenger.enums.MessengerEvent;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.service.ConversationActivityService;
import com.birdeye.messenger.service.KafkaService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.NexusService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class SpamDetectionServiceImpl implements SpamDetectionService   {

	@Value("${spam.detection.service.url}")
	private String spamDetectionServiceUrl;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private ContactService contactService;

	@Autowired
	private ConversationActivityService conversationActivityService;

	@Autowired
	private MessengerContactService messengerContactService;
	
	@Autowired
	private NLPService nlpService;
	
	@Autowired
	private OutboundSpamMessageAuditRepository outboundSpamMessageAuditRepository;
	
	@Autowired
	private RedisHandler redisHandler;
	
	@Autowired
	private KafkaService kafkaService;
	
	@Autowired
	ChatbotService chatbotService;
	
	private final BusinessService businessService;

	@Override
	public void spamDetectionAllChannels(MessageDTO messageDTO, MessengerContact messengerContact,
			CustomerDTO customerDTO, MessageTag messageTag) {
		log.info("Enter spamDetectionAllChannels : {}", messengerContact.getId());
		if(!isBlockAndSpamMarked(messengerContact) 
				&& Constants.BUSINESS_BLOCKED_BY_DUMMY_USER.equals(messengerContact.getSpamMarkedBy())) {
			//If marked spam by business or inbox defender - do not mark spam
			checkSpamAndUpdate(messengerContact,messageDTO, messageTag);
		}
	}

	private boolean isBlockAndSpamMarked(MessengerContact messengerContact) {
		return (messengerContact.getBlocked() && messengerContact.getSpam());
	}

	private void checkSpamAndUpdate(MessengerContact messengerContact,MessageDTO messageDTO, MessageTag messageTag) {
		try {
			log.info("Enter checkSpamAndUpdate : {}", messengerContact.getId());
			Boolean spamStatus = Boolean.FALSE;
			BusinessDTO businessDTO = messageDTO.getBusinessDTO();
			String spamDetectionEnabledAccounts = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("spam_detection_enabled_accounts", "261968");
			List<String> spamDetectionEnabledAccountsList = ControllerUtil.getTokensListFromString(spamDetectionEnabledAccounts);
			
			String spamDetectionSources = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("spam_detection_enabled_sources", "1,110,100,12,13,18,14");
			List<String> spamDetectionSourceList = ControllerUtil.getTokensListFromString(spamDetectionSources);
			
			Integer source = messageDTO.getSource();
			
			if (spamDetectionSourceList.contains(String.valueOf(source)) && (spamDetectionEnabledAccountsList.contains(String.valueOf(businessDTO.getAccountId())) 
					|| spamDetectionEnabledAccounts.contains("GLOBAL"))) {
				//NLP spam detection for all channels
				spamStatus = checkSpamThroughSpamDetection(messageDTO,spamStatus);
			} else if (messageDTO.getSource().equals(Source.SMS.getSourceId())){
				//Twilio only for SMS
				CheckSpamRequestDto checkSpamRequestDto = new CheckSpamRequestDto(messageDTO.getCustomerDTO().getPhoneE164(),messageDTO.getBusinessDTO().getCountryCode(),messageDTO.getBusinessDTO().getBusinessId(),true,messageDTO.getBusinessDTO().getAccountId());
				spamStatus = nexusService.checkSpamFromTwilio(checkSpamRequestDto);
			}
			if(!Objects.isNull(spamStatus)){
				updateSpamAndBlock(messengerContact,spamStatus,messageDTO, messageTag);
			}
		} catch (Exception e) {
			// handle exception
			log.error("Exception while checking spam for a contact",e);
			return ;
		}
	}

	private Boolean checkSpamThroughSpamDetection(MessageDTO messageDTO,Boolean spamStatus){
		try{
			SpamDetectionRequestDto spamDetectionRequestDto= null;
			Integer source = messageDTO.getSource();
			switch (source) {
			case 1:
				SMSMessageDTO smsMessageDTO = ((SMSMessageDTO)messageDTO);
				if(Objects.nonNull(smsMessageDTO.getSmsDTO()) && Objects.nonNull(smsMessageDTO.getSmsDTO().getSmsId()) && StringUtils.isNotBlank(smsMessageDTO.getBody())){
					spamDetectionRequestDto=new SpamDetectionRequestDto(smsMessageDTO.getBody(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				}
				break;
			case 110:
				FacebookMessageRequest facebookMessageRequest = (FacebookMessageRequest) messageDTO;
				Entry fbEntry = facebookMessageRequest.getEntry().get(0);
				Message fbMsg = fbEntry.getMessaging().get(0).getMessage();
				spamDetectionRequestDto=new SpamDetectionRequestDto(fbMsg.getText(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 12:
				GoogleUserMessage googleEvent = (GoogleUserMessage) messageDTO;
				GoogleUserMessage.Message googleMessage = googleEvent.getMessage();
				spamDetectionRequestDto=new SpamDetectionRequestDto(googleMessage.getText(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 13:
				InstagramMessageRequest instagramMessageRequest = (InstagramMessageRequest) messageDTO;
				com.birdeye.messenger.dto.instagram.Entry instaEntry = instagramMessageRequest.getEntry().get(0);
				com.birdeye.messenger.dto.instagram.Message instaMsg = instaEntry.getMessaging().get(0).getMessage();
				spamDetectionRequestDto=new SpamDetectionRequestDto(instaMsg.getText(), messageDTO.getBusinessDTO().getBusinessId().toString(),Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 18:
				TwitterMessageRequest twitterMessageRequest = (TwitterMessageRequest) messageDTO;
				MessageCreate twitterMsg = twitterMessageRequest.getDirect_message_events().get(0).getMessage_create();
				spamDetectionRequestDto=new SpamDetectionRequestDto(twitterMsg.getMessage_data().getText(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 14:
				SMSMessageDTO contactSmsMessageDTO = ((SMSMessageDTO)messageDTO);
				spamDetectionRequestDto=new SpamDetectionRequestDto(contactSmsMessageDTO.getBody(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 100:
				SMSMessageDTO voiceCallSmsMessageDTO = ((SMSMessageDTO)messageDTO);
				spamDetectionRequestDto=new SpamDetectionRequestDto(voiceCallSmsMessageDTO.getBody(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 9:
			case 10:
				LiveChatMessageDTO liveChatMessageDTO = ((LiveChatMessageDTO)messageDTO);
				spamDetectionRequestDto=new SpamDetectionRequestDto(liveChatMessageDTO.getMessage(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			case 2:
			case 6:
				WebchatMessageDTO webchatMessageDTO = ((WebchatMessageDTO)messageDTO);
				spamDetectionRequestDto=new SpamDetectionRequestDto(webchatMessageDTO.getMessage(),messageDTO.getBusinessDTO().getBusinessId().toString(), Source.getValue(source).name(), messageDTO.getMessengerContact().getId().toString());
				break;
			default:
			log.info("Other channel for spam");
			break;
			}
			Integer spamDetectionTextSizeLimit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("spam_detection_text_size_limit", 30);
			if (ObjectUtils.isNotEmpty(spamDetectionRequestDto) && StringUtils.isNotBlank(spamDetectionRequestDto.getText()) && spamDetectionRequestDto.getText().length() >= spamDetectionTextSizeLimit) { //SPAM check greater than = 30 chars
				return checkSpamThroughNlp(spamDetectionRequestDto);
			} else {
				return null;
			}
		}catch(Exception e){
			log.error("Exception occur while pushing spam detection audit",e);
		}
		return false;
	}

	private void updateSpamAndBlock(MessengerContact messengerContact,Boolean spamStatus,MessageDTO messageDTO, MessageTag messageTag) {
		try {
			if (Objects.nonNull(messengerContact) && Objects.isNull(messageDTO.getMessengerContact())) {
				messageDTO.setMessengerContact(messengerContact);
			}
			if(!spamStatus.equals(messengerContact.getSpam())) {
				MarkBlockAndSpamRequestDto markBlockAndSpamRequestDto = new MarkBlockAndSpamRequestDto(
						messageDTO.getBusinessDTO().getAccountId(), spamStatus, messengerContact.getCustomerId(),
						null, "inbox", Constants.INBOX_DEFENDER_USER);
				if (!contactService.markBlockAndSpam(markBlockAndSpamRequestDto))
					return;
				boolean check = false;
				if(messengerContact.getSpam().equals(null)){
					check = true;
				}
				messengerContact.setSpam(spamStatus);
				messengerContact.setBlocked(spamStatus);
				messengerContact.setSpamMarkedBy(Constants.INBOX_DEFENDER_USER);
				messengerContact.setRtmPauseTagging(spamStatus);
				messageDTO.getCustomerDTO().setBlocked(spamStatus);
				messageDTO.getCustomerDTO().setUserId(Constants.INBOX_DEFENDER_USER);
				createActivity(messageDTO, spamStatus, check, messageTag);
			} else {
				messengerContact.setSpam(spamStatus);
				messengerContact.setBlocked(spamStatus);
				messengerContact.setSpamMarkedBy(Constants.INBOX_DEFENDER_USER);
				messageDTO.setMessengerContact(messengerContact);
			}
			messengerContactService.saveOrUpdateMessengerContact(messengerContact);
		}catch (Exception e) {
			log.error("Exception while updating a contact spam blocked",e);
			return ;
		}

	}

	private void createActivity(MessageDTO messageDTO, Boolean spamStatus, boolean check, MessageTag messageTagInput){
		MessageTag messageTag = null;
		MessengerContact messengerContact = messageDTO.getMessengerContact();
		if(messageDTO.isUpdateTag()) {
			messageTag = messageTagInput;
		}
		CustomerDTO customerDTO = messageDTO.getCustomerDTO();
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();
		UserDTO userDTO =messageDTO.getUserDTO();
		ContactDocument contactDocument = messengerContactService.contactDocumentBuilder(messengerContact, customerDTO, businessDTO, messageTag,
				userDTO);
		Integer source = messageDTO.getSource();
		Date created = new Date();
		switch (source) {
		case 1:
		case 14:
			SMSMessageDTO dto = ((SMSMessageDTO)messageDTO);
			created = new Date(dto.getSmsDTO().getSentOn() != null && dto.getSmsDTO().getSentOn().after(dto.getSmsDTO().getCreateDate()) ? dto.getSmsDTO().getSentOn().getTime() - 1000 : dto.getSmsDTO().getCreateDate().getTime() - 1000);
			break;
		case 110:
		case 12:
		case 13:
		case 18:
		case 2:
		case 6:
		case 9:
		case 10:
		case 100:
			created = new Date(new Date().getTime()-1000);
			break;
		default:
			log.info("Other channel for spam");
			break;
		}
		if (spamStatus) {
			conversationActivityService.createBlockUnblockActivitySpamPath(ActivityType.CONTACT_BLOCKED, customerDTO, contactDocument, businessDTO,created);
		} else if(!check) {
			conversationActivityService.createBlockUnblockActivitySpamPath(ActivityType.CONTACT_UNBLOCKED, customerDTO, contactDocument, businessDTO,created);
		}
	}

	@Override
	public Boolean checkSpamThroughNlp(SpamDetectionRequestDto dto){
		log.info("Check Spam using text for c_id : {}", dto.getCid());
		String url = this.spamDetectionServiceUrl + "/v1/detect_conversation_spam/";
		HttpHeaders headers = new HttpHeaders();
		headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> entity = new HttpEntity<>(dto,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<SpamDetectionResponseDto> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity,SpamDetectionResponseDto.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("checkSpamFromNLP", startTime, endTime);
		if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
			log.info("Error in response from spam detection service to check spam from text");
			return null;
		}
		log.info("Response from spam detection service : {}",responseEntity.getBody());
		SpamDetectionResponseDto.Result result = responseEntity.getBody().getResults().getResult();
		String spamDetectionRatings = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("spam_detection_ratings",
				"likely,highly likely");
		List<String> spamDetectionRatingList = ControllerUtil.getTokensListFromString(spamDetectionRatings);
		if (Objects.nonNull(result) && spamDetectionRatingList.contains(result.getRisk_rating())) {
			return true;
		}
		return false;
	}

	/*
	 * This  method checkAndAuditSpamForOutboundMessage 
	 * checks for spam in an outbound message and performs related actions such as
	 * creating and saving a spam audit entry and pushing spam event notification to kafka. 
	 */
	@Override
	@Async
	public void checkAndAuditSpamForOutboundMessage(MessageDTO messageDTO) {
		UserDTO userDTO=messageDTO.getUserDTO();
		if (MessengerEvent.SMS_RECEIVE.equals(messageDTO.getEvent()) || messageDTO.getUserDTO() == null || messageDTO.getUserDTO().getId()==null ||  Constants.INSTAGRAM_DUMMY_USER.equals(userDTO.getId()) || Constants.FACEBOOK_DUMMY_USER.equals(userDTO.getId()) || Constants.Twitter_DUMMY_USER.equals(userDTO.getId())) {
			return;
		}
		SendMessageDTO sendMessageDTO;
		try {
			sendMessageDTO = (SendMessageDTO) messageDTO;
		} catch (Exception e) {
			log.error("[checkAndAuditSpamForOutboundMessage] Error in  casting messageDTO to sendMessageDTO object");
			return;
		}

		MessageDocument messageDocument = messageDTO.getMessageDocument();
		BusinessDTO businessDTO = messageDTO.getBusinessDTO();

		//Exclude account from checking outbound spam
		if (messageDocument == null || businessDTO == null || StringUtils.isBlank(messageDocument.getM_id()) ||
				StringUtils.isBlank(sendMessageDTO.getBody()) || sendMessageDTO.getConversationDTO() == null ||
				sendMessageDTO.getConversationDTO().getSource() == null ||
				!CommunicationDirection.SEND.equals(sendMessageDTO.getConversationDTO().getCommunicationDirection())) {
			return;
		}

		BusinessDTO business = businessService.getBusinessLiteDTO(businessDTO.getAccountId());
		if (business == null) {
			throw new MessengerException(ErrorCode.BUSINESS_NOT_FOUND);
		}
		String accountRemovedFromOutboundSpamFiltering = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("accounts_removed_from_outbound_spam_filtering", "261968");
		Set<Integer> spamDetectionEnabledAccountsList = ControllerUtil.getTokensListIntegerFromString(accountRemovedFromOutboundSpamFiltering);
		
		if (spamDetectionEnabledAccountsList.contains(business.getAccountId())){
			log.info("account removed from outbound spam filtering : {}", business.getAccountId());
			return;
		}
		List<Map<String, String>> chatHistory = chatbotService.getHistoryMessageForAI(businessDTO.getBusinessId(), businessDTO.getAccountId(), sendMessageDTO.getCustomerId(), Integer.valueOf(sendMessageDTO.getToCustomerId()));
		PhishingDetectionRequest phishingDetectionReq = new PhishingDetectionRequest(businessDTO.getAccountId(), userDTO.getId(),
				business.getBusinessName(), business.getWebsiteUrl(), sendMessageDTO.getBody(), chatHistory);
		Map<String, String> spamOrPhishingResponse = nlpService.detectPhishingContentForOuboundMessage(phishingDetectionReq);

		boolean spam = spamOrPhishingResponse.containsKey("spam") ? Boolean.parseBoolean(spamOrPhishingResponse.get("spam")) : false;
		boolean phishing = spamOrPhishingResponse.containsKey("phishingResponse") ? Boolean.parseBoolean(spamOrPhishingResponse.get("phishingResponse")) : false;
		if (spam || phishing) {
			log.info("Outbound message classified as spam:{} or phishing_content:{}", spam, phishing);
			OutboundSpamMessageAudit outboundSpamMessageAudit = createAndSaveSpamAudit(messageDocument, sendMessageDTO, businessDTO, messageDTO.getUserId(), spam, phishing);
			handleSpamMessageNotification(businessDTO, outboundSpamMessageAudit);
		}
	}

	private OutboundSpamMessageAudit createAndSaveSpamAudit(MessageDocument messageDocument, SendMessageDTO sendMessageDTO, BusinessDTO businessDTO, Integer userId, boolean spam, boolean phishing) {
		OutboundSpamMessageAudit outboundSpamMessageAudit = new OutboundSpamMessageAudit();
		outboundSpamMessageAudit.setMessageId(messageDocument.getId().toString());
		outboundSpamMessageAudit.setMcId(Integer.valueOf(sendMessageDTO.getToCustomerId()));
		outboundSpamMessageAudit.setChannel(Source.getValue(sendMessageDTO.getConversationDTO().getSource()).name());
		outboundSpamMessageAudit.setAccountId(businessDTO.getAccountId());
		outboundSpamMessageAudit.setBusinessId(businessDTO.getBusinessId());
		outboundSpamMessageAudit.setUserId(userId);
		outboundSpamMessageAudit.setSpamContent(spam);
		outboundSpamMessageAudit.setPhishingContent(phishing);
		outboundSpamMessageAudit.setMessageBody(sendMessageDTO.getBody());
		outboundSpamMessageAuditRepository.save(outboundSpamMessageAudit);
		return outboundSpamMessageAudit;
	}

	//Sending Glip notifications for suspicious activity by pushing events to Kafka. This process occurs once every 24 hours per account.
	private void handleSpamMessageNotification(BusinessDTO businessDTO, OutboundSpamMessageAudit outboundSpamMessageAudit) {
	    String redisKey = Constants.SPAM_MESSAGE_NOTIFICATION + businessDTO.getAccountId();
	    String value = redisHandler.getKeyValueFromRedis(redisKey);

	    if (value == null) {
	        Integer spamMessageCountThreshold = Integer.valueOf(CacheManager.getInstance()
	                .getCache(SystemPropertiesCache.class)
	                .getIntegerProperty("spam_message_count_threshold", 50));

	        List<OutboundSpamMessageAudit> outboundSpamMessageAudits = outboundSpamMessageAuditRepository.findAllByAccountId(businessDTO.getAccountId());

	        if (CollectionUtils.isNotEmpty(outboundSpamMessageAudits) && outboundSpamMessageAudits.size() >= spamMessageCountThreshold) {
	            redisHandler.setOpsForValueWithExpiry(redisKey, businessDTO.getAccountId(), 24L, TimeUnit.HOURS);
	            Map<Integer, List<OutboundSpamMessageAudit>> groupedByUserId = groupByUserIdAndSortByCreatedAt(outboundSpamMessageAudits);

	            if (MapUtils.isNotEmpty(groupedByUserId)) {
	                OutboundSpamNotificationDTO event = createOutboundSpamNotificationDTO(businessDTO, groupedByUserId,outboundSpamMessageAudits);
	                if (StringUtils.isNotEmpty(outboundSpamMessageAudit.getMessageBody()))
	                	event.setMsgBody(outboundSpamMessageAudit.getMessageBody());
	                log.info("Pushing outbound spam notification event to kafka, payload: {}",event);
	                kafkaService.publishToKafkaAsync(KafkaTopicEnum.OUBOUND_SPAM_MESSAGE_NOTIFICATION, businessDTO.getAccountId(), event);
	            }
	        }
	    }else {
	    	log.info("The message id: {} is flagged as spam, but a notification will not be sent out since we have already dispatched a notification within the past 24 hours for this accountId {} ,mcId: {}", outboundSpamMessageAudit.getMessageId(),outboundSpamMessageAudit.getAccountId(),outboundSpamMessageAudit.getMcId());
	    }
	}

	private Map<Integer, List<OutboundSpamMessageAudit>> groupByUserIdAndSortByCreatedAt(List<OutboundSpamMessageAudit> outboundSpamMessageAudits) {
	    return outboundSpamMessageAudits.stream()
	            .collect(Collectors.groupingBy(
	                    OutboundSpamMessageAudit::getUserId,
	                    TreeMap::new, // Using TreeMap to maintain sorting order by key
	                    Collectors.toList()
	            ));
	}

	private OutboundSpamNotificationDTO createOutboundSpamNotificationDTO(BusinessDTO businessDTO, Map<Integer, List<OutboundSpamMessageAudit>> groupedByUserId, List<OutboundSpamMessageAudit> outboundSpamMessageAudits) {
	    OutboundSpamNotificationDTO event = new OutboundSpamNotificationDTO();
	    event.setAccountId(businessDTO.getAccountId());
	    String userIds=groupedByUserId.keySet().toString();
	    event.setSpamStartTime(outboundSpamMessageAudits.get(0).getCreatedAt().toString());
	    event.setSpamEndTime(outboundSpamMessageAudits.get(outboundSpamMessageAudits.size()-1).getCreatedAt().toString());
	    event.setUserIds(userIds);
	    groupedByUserId.forEach((userId, audits) -> {
	        Map<String, Long> spamCountByChannel = audits.stream()
	                .collect(Collectors.groupingBy(OutboundSpamMessageAudit::getChannel, Collectors.counting()));
	        event.addSpamUserDetail(userId, spamCountByChannel, audits.get(0).getCreatedAt(), audits.get(audits.size() - 1).getCreatedAt());
	    });
	    event.setSpamUserDetails(event.getSpamUserList().toString());
	    return event;
	}


}
