package com.birdeye.messenger.external.service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.leadgenagent.GetPromptBasedReplyRequestDTO;
import com.birdeye.messenger.dto.leadgenagent.GetPromptBasedReplyResponseDTO;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.CommunicationDirection;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.dto.elastic.MessageDocument.RobinResponseType;
import com.birdeye.messenger.enums.ActivityType;
import com.birdeye.messenger.enums.ConversationSummaryMessageType;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.service.ChatbotService.QueryChannel;
import com.birdeye.messenger.service.CommonService;
import com.birdeye.messenger.service.ConversationSummaryMessageBodyService;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.JSONUtils;
import com.birdeye.messenger.util.LogUtil;
import com.birdeye.messenger.util.MessengerUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class NLPServiceImpl implements NLPService {

	@Value("${nlp.service.url}")
	private String nlpUrl;

	@Value("${conversational.ai.service.url}")
	private String conversationalAIUrl;
	
	@Value("${spam.detection.service.url}")
	private String spamDetectionServiceUrl;
	
	@Value("${gen.ai.service.url}")
	private String genAIServiceUrl;
	
	@Autowired
	private RestTemplate restTemplate;
	
	@Autowired
	private CommonService commonService;

	@Autowired
	private ConversationSummaryMessageBodyService conversationSummaryMessageBodyService;

	@Autowired
	@Qualifier("customTimeoutRestTemplate")
	private RestTemplate customTimeoutRestTemplate;

	@Override
	public boolean isTextSpamOrProfane(String text) {
		return isTextSpam(text) || isTextProfane(text);
	}

	/**
	 * @param text
	 *            - Comment to be checked
	 * @return boolean - NLP result indicating possible profanity.
	 */
	@Override
	public boolean isTextProfane(String text) {
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		// TODO: Need to check if all params are required
		try {
			NlpProcessInputMessage request = new NlpProcessInputMessage(URLEncoder.encode(text, "UTF-8"), 1L, 5.0,
					"review");
			HttpEntity<NlpProcessInputMessage> requestBody = new HttpEntity<>(request, headers);
			long startTime = System.currentTimeMillis();
			String nlpResponse = restTemplate.postForObject(nlpUrl, requestBody, String.class);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("isTextProfane", startTime, endTime);
			String profane = JSONUtils.nodeValue(nlpResponse, "/profane");
			return Boolean.parseBoolean(profane);
		} catch (Exception e) {
			log.error("Error in calling NLP Service with text {}", text, e);
			// In case of errors, returning false to let webchat message be
			// stored.
			return false;
		}
	}

	@Data
	@AllArgsConstructor
	public class NlpProcessInputMessage {
		private String comment;
		private Long reviewId;
		private Double rating;
		private String type;
	}

	// Referenced from platform code.. can be simplified further.
	@Override
	public boolean isTextSpam(String comments) {
		boolean spamFlag = true;
		if (StringUtils.isBlank(comments)) {
			return spamFlag = false;
		}
		List<String> htmlLinks = isCommentContainingLinkRegex(comments);
		log.info("HTML links in text {}", htmlLinks);
		spamFlag = !htmlLinks.isEmpty();
		return spamFlag;
	}

	private List<String> isCommentContainingLinkRegex(String comment) {
		List<String> url = new ArrayList<>();

		// Below expression handles all URLs starting protocol, www or without
		// either of them , it limits TLDs but wont work with email ids
		// Pattern pattern =
		// Pattern.compile("(http(s)?://)?(?:[\\w-]+\\.)*([\\w-]{1,63})(?:\\.(com|net|in|org|co\\.in|us|gov))(\\/)?");
		// Below expression necessitates usage of http or www but removes TLD
		// dependency and will work with mail Id
		// Remove all mail ids
		String commentWithoutMailIds = comment.replaceAll("([a-zA-Z0-9._-]+@([a-zA-Z0-9_-]+\\.)+[a-zA-Z0-9_-]+)", " ");
		Pattern pattern = Pattern.compile("(http(s)?:\\/\\/|www.)?([a-zA-Z0-9\\-\\.]+)(?:\\.(?:\\w{3}|\\w{2}))");
		Matcher matcher = pattern.matcher(commentWithoutMailIds);
		while (matcher.find()) {
			int start = matcher.start(0);
			int end = matcher.end(0);
			String val = commentWithoutMailIds.substring(start, end);
			if (StringUtils.isNotBlank(val)) {
				url.add(val);
			}
			log.info("matcher.group(0) {0}", val);
		}

		String urlShortenMethod = new String("bit.ly"); // "goo.gl"

		if (comment.contains(urlShortenMethod)) {
			log.info("Inside {}", urlShortenMethod);
			int startIndexOfURL = comment.indexOf(urlShortenMethod);
			String commentsTemp = comment.substring(startIndexOfURL);
			String urlTemp = extractHtmlValue(commentsTemp);
			url.add(urlTemp);
			log.info("value from extractHtmlValue{0} ", url);
			// String shortenUrl = BitlyManipulationUtil.fetchLongURL(urlTemp);
			// if (StringUtils.isNotBlank(shortenUrl)) {
			// url.add(shortenUrl);
			// }
			log.info("value of shortenUrl: {} ", url);
		}
		return url;
	}

	private String extractHtmlValue(String commentsTemp) {
		String htmlLink = null;
		int smallestIndex = **********;
		int[] arrayOfIndexes = new int[5];
		if (commentsTemp.indexOf(",") > 0) {
			log.info("comma {0}", commentsTemp.indexOf(","));
			arrayOfIndexes[0] = commentsTemp.indexOf(",");
		}
		if (commentsTemp.indexOf(" ") > 0) {
			log.info("space {0}", commentsTemp.indexOf(" "));
			arrayOfIndexes[1] = commentsTemp.indexOf(" ");
		}
		if (commentsTemp.indexOf(". ") > 0) {
			log.info("prd spc {0}", commentsTemp.indexOf(". "));
			arrayOfIndexes[2] = commentsTemp.indexOf(". ");
		}
		if (commentsTemp.indexOf("\n") > 0) {
			log.info("new line {0}", commentsTemp.indexOf("\n"));
			arrayOfIndexes[3] = commentsTemp.indexOf("\n");
		}
		if (commentsTemp.indexOf(".\n") > 0) {
			log.info("prd newline {0}", commentsTemp.indexOf(".\n"));
			arrayOfIndexes[4] = commentsTemp.indexOf(".\n");
		}

		for (int i = 0; i < 5; i++) {
			if (smallestIndex > arrayOfIndexes[i] && arrayOfIndexes[i] > 0) {
				smallestIndex = arrayOfIndexes[i];
			}
		}
		log.info("smallestIndex  {0}", smallestIndex);
		if (smallestIndex == **********) {
			log.info("charExists == false");
			htmlLink = commentsTemp;
		} else {
			log.info("charExists == true {0}", smallestIndex);
			htmlLink = commentsTemp.substring(0, smallestIndex);
			log.info("substringed value of comments from bit.ly type{0} ", htmlLink);
		}
		return htmlLink;
	}

	@Override
	public Optional<ChatbotQueryResponse> getGPTPoweredRobinReply(Integer accountId, BusinessDTO businessDto, String queryText,
			QueryChannel queryChannel, Integer customerId, List<Map<String, String>> chatHistory,
			GPTResponseFeedbackAudit audit, Boolean isReceivedDuringBusinessHour, BusinessTimingDTO businessTiming, BusinessProfileData businessProfileData, LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto) {
		GPTReplyRequest request = new GPTReplyRequest(String.valueOf(accountId), "", String.valueOf(businessDto.getBusinessId()), String.valueOf(customerId),
				businessDto.getBusinessName(), queryChannel.name(), chatHistory, isReceivedDuringBusinessHour,
				businessTiming.getTimeZoneId(), businessTiming.getTextData(),livechatAnonymousConversationDataDto.getPrechatEnable() ,Objects.nonNull(businessProfileData) && Objects.nonNull(businessProfileData.getAdditionalData()) && StringUtils.isNotBlank(businessProfileData.getAdditionalData().getAppointmentLink()) ? businessProfileData.getAdditionalData().getAppointmentLink() : "",
				businessDto.getBusinessName(),livechatAnonymousConversationDataDto.getContactDetails() ,livechatAnonymousConversationDataDto.getUiContactCheckFlag(),businessDto.getCountryCode());
	
		GPTReplyResponse gptResponse = null;
		String url = conversationalAIUrl + "/v1/robin-response/";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		ChatbotQueryResponse cqResponse = new ChatbotQueryResponse();
		try {
			HttpEntity<GPTReplyRequest> httpEntity = new HttpEntity<>(request,headers);
			long startTime = System.currentTimeMillis();
			ResponseEntity<String> response = customTimeoutRestTemplate.exchange(url, HttpMethod.POST,httpEntity, String.class, headers);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("getGPTPoweredRobinReply", startTime, endTime);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("[NLPServiceImpl] getQueryResponse failed for queryText {} with status {} ", queryText,
						response.getStatusCode());
				if (Objects.nonNull(gptResponse) && !"506".equals(gptResponse.getCode())) {
					handleErrorResponse(cqResponse, gptResponse);
					return Optional.ofNullable(cqResponse);
				}
			}

			gptResponse = ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(response.getBody()),GPTReplyResponse.class);
			
			if (Objects.nonNull(gptResponse)) {
				Map<String, Object> data = new HashMap<>();
				data.put("support", gptResponse.isSupport());
				data.put("response", gptResponse.getServiceRep());
				data.put("code", null!=gptResponse.getCode() ? gptResponse.getCode():"");
				data.put("message", gptResponse.getMessage());
				data.put("eventId", audit.getId());
				data.put("secureFaq", gptResponse.isSecureToken());
				data.put("answerFlag", gptResponse.isAnswerFlag());
				data.put("citations",gptResponse.getCitations());
				data.put("faqFlag",gptResponse.isFaqFlag());
				data.put("fileFlag",gptResponse.isFileFlag());
				data.put("phoneNumber",gptResponse.getPhoneNumber());
				data.put("email",gptResponse.getEmail());
				data.put("countryCode",gptResponse.getCountryCode());
				audit.setResponse(gptResponse.getServiceRep());
				audit.setSupport(gptResponse.isSupport() ? 1 : 0);
				audit.setResponseCode(gptResponse.getCode());
				audit.setErrorMessage(gptResponse.getMessage());
				cqResponse.setData(data);
				cqResponse.setIntentType("GPT");
			}
			audit.setGptResponseTime(endTime - startTime);
			audit.setAiPayload(JSONUtils.toJSON(request));
		} catch (Exception e) {
			log.info("[ChatbotServiceImpl] getQueryResponse for queryText:{} failed due to:{}", queryText, e.getMessage());
		}
		return Optional.ofNullable(cqResponse);
	}
	
	@Override
	public Map<String, String> buildConversationListFromMessageDocument(
            MessageDocument messageDocument, Map<String, String> robinResponsesPlainTextMap) {
        Map<String, String> messageMap = new HashMap<>();
        if (Objects.nonNull(messageDocument.getMsg_body())) {
            MessageType messageType = messageDocument.getMessageType();
            ActivityType activityType = messageDocument.getActivityType();
            ConversationSummaryMessageType conversationSummaryMessageType = commonService.getConversationSummaryMessageType(
                    messageType,
                    activityType);
            ChatbotQueryResponse chatbotQueryResponse = null;
            if (ConversationSummaryMessageType.CHAT == conversationSummaryMessageType
                    && MessageType.RICH_CONTENT_CHAT == messageType) {
            	ObjectMapper mapper = new ObjectMapper();
    			try {
    				chatbotQueryResponse = mapper.readValue(messageDocument.getMsg_body(), ChatbotQueryResponse.class);
    			} catch (Exception e) {
    				log.info("unable to readValue from json: {}", messageDocument.getMsg_body());
    			}
            }
            
            if (ConversationSummaryMessageType.APPOINTMENT == conversationSummaryMessageType
                    || ConversationSummaryMessageType.PAYMENT == conversationSummaryMessageType) {
                String formattedBody = conversationSummaryMessageBodyService.formatMessageBodyForActivityType(
                        activityType,
                        messageDocument, conversationSummaryMessageType);
                messageDocument.setMsg_body(formattedBody);
            } else if (ConversationSummaryMessageType.CHAT == conversationSummaryMessageType
                    && MessageType.RICH_CONTENT_CHAT == messageType
                    && RobinResponseType.GPT.name().equals(chatbotQueryResponse.getIntentType())) {
            	String robinBody = "";
            	if (MapUtils.isNotEmpty(chatbotQueryResponse.getData())) {
            		robinBody = (String) chatbotQueryResponse.getData().get("response");
            		messageDocument.setMsg_body(robinBody);
            	}
            } else if (ConversationSummaryMessageType.CHAT == conversationSummaryMessageType
                    && MessageType.RICH_CONTENT_CHAT == messageType
                    && MapUtils.isNotEmpty(robinResponsesPlainTextMap)) {
            	String id = messageDocument.getM_id()
            			+ MessengerUtil.getMessageTypeSuffix(messageDocument.getMessageType(),
            					messageDocument.getSource());
            	String robinBody = robinResponsesPlainTextMap.get(id);
            	messageDocument.setMsg_body(robinBody);
            }
            CommunicationDirection communicationDirection = messageDocument.getCommunicationDirection();
            if (CommunicationDirection.RECEIVE == communicationDirection) {
            	messageMap.put("user", messageDocument.getMsg_body());
            } else if (CommunicationDirection.SEND == communicationDirection) {
            	messageMap.put("bot", messageDocument.getMsg_body());
            }
        }
        return messageMap;
    }
	
	private void handleErrorResponse(ChatbotQueryResponse cqResponse, GPTReplyResponse gptResponse) {
		Map<String, Object> data = new HashMap<>();
		data.put("code", "500");
		data.put("message", "Error Response from Conversational AI Service");
		cqResponse.setData(data);
		cqResponse.setIntentType("GPT");
	}

	@Override
	public boolean checkSpamForOuboundMessage(CheckSpamRequest checkSpamRequest) {

		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		try {
			HttpEntity<Object> requestBody = new HttpEntity<>(checkSpamRequest, headers);
			long startTime = System.currentTimeMillis();
			String url = this.spamDetectionServiceUrl + "/v1/detect_outbound_conversation_spam/";
			String nlpResponse = restTemplate.postForObject(url, requestBody, String.class);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("checkSpamForOuboundMessage", startTime, endTime);
			String profane = JSONUtils.nodeValue(nlpResponse, "/spam");
			return Boolean.parseBoolean(profane);
		} catch (Exception e) {
			log.error("Error in calling NLP Service with request: {}", checkSpamRequest, e);
			return false;
		}
	
	}

	@Override
	public Map<String, String> detectPhishingContentForOuboundMessage(PhishingDetectionRequest phishingDetectionReq) {
		HttpHeaders headers = new HttpHeaders();
		Map<String, String> response = new HashMap<>();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		try {
			HttpEntity<Object> requestBody = new HttpEntity<>(phishingDetectionReq, headers);
			long startTime = System.currentTimeMillis();
			String url = this.genAIServiceUrl + "/v1/common/phishing-detection-for-conversation/";
			String phishingResponse = restTemplate.postForObject(url, requestBody, String.class);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("detectPhishingContentForOuboundMessage", startTime, endTime);
			String spam = JSONUtils.nodeValue(phishingResponse, "/spam");
			String phishingContent = JSONUtils.nodeValue(phishingResponse, "/phishingContent");
			response.put("phishingResponse", phishingContent);
			response.put("spam", spam);
			return response;
		} catch (Exception e) {
			log.error("Error in calling Gen AI Service with request: {}", phishingDetectionReq, e);
			return null;
		}
	}

	@Override
	public String getAIReplyUsingPrompt(GetPromptBasedReplyRequestDTO request) {
		log.info("getAIReplyUsingPrompt request: {}", request);
		String url = genAIServiceUrl + "/v1/inbox/ai-prompt-responder/";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		try {
			HttpEntity<GetPromptBasedReplyRequestDTO> httpEntity = new HttpEntity<>(request, headers);
			ResponseEntity<GetPromptBasedReplyResponseDTO> response = customTimeoutRestTemplate.exchange(url, HttpMethod.POST, httpEntity, GetPromptBasedReplyResponseDTO.class, headers);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("[NLPServiceImpl] getAIReplyUsingPrompt failed for request {} with status {} ", request,
						response.getStatusCode());
			}
			return response.getBody().getResponse();
		}catch (Exception e) {
			log.error("[NLPServiceImpl] getAIReplyUsingPrompt failed for request {} due to: {}", request, e.getMessage());
		}
		return null;
	}
}
