package com.birdeye.messenger.external.service;

import com.birdeye.messenger.dto.*;

import java.util.List;

public interface ContactService {

	String HEADER_ENT_ID = "x-enterprise-id";

	String HEADER_USER_ID = "x-user-id";

	String UPDATE_CUSTOMER_STATUS = "/customer/unsubscribe/update/status";
	
	String LOC_CUSTOMER_CREATE = "/customer/create/mapping";
	
	String LOC_CUSTOMER_BYID = "/customer/get/";

	String CUSTOMER_MAP = "/customer/map/";
	String CUSTOM_FIELD = "/customField/";
	String LOOKUP_CUSTOMER_TFN = "/customer/get-by-phone/across-accounts";
	String FIND_ALL_CUSTOMER = "/customer/get/emailphone/businessid";
	String FIND_ALL_CONTACT_UPGRADE_ENABLED_ACCOUNTS = "/feature/contactsupgrade/accountlist";
	
	String UPDATE_CUSTOMER_SUBSCRIPTION_STATUS = "/customer/update/subscription-status";
	
	String MERGE_CUSTOMER = "/customer/messenger/merge";

	CustomerDTO getorCreateNewCustomer(KontactoRequest request, Integer accountId, Integer userId);

	CustomerDTO findById(Integer customerId);
	
	public void onCustomerDataChange(Integer customerId);

	Integer findCustomerIdByEcIdAndBusinessId(Integer ecid, Integer businessId);

	CustomerDTO mergeCustomer(CustomerDTO customerDTO);

	KontactoRequest createContactoRequest(Integer messengerContactId);

	CustomerDTO findByIdNoCaching(Integer customerId);

	void updateCustomerStatus(Integer customerId);

	public Boolean unBlockEmailCommunication(Integer ecid);

    List<CustomerLite> getByIds(List<Integer> cIds);
	List<CustomFieldDto> getCustomFields(Long accountId);

	Boolean markBlockAndSpam(MarkBlockAndSpamRequestDto markBlockAndSpamRequestDto);

	CustomerDTO findByIdWithCustomFields(Integer customerId);

	CustomerDTO lookupCustomerForTfn(SMSMessageDTO smsMessageDTO);

	List<CustomerDTO> findAllCustomer(String fromNumber, String email, Integer businessId, Integer accountId);

	List<Integer> getAllContactUpgradeEnabledAccounts();
	
	boolean updateCustomerSubscriptionStatus(Integer customerId, Integer accountId, Boolean smsOptOut, String emailCategory, String smsKeyword, Boolean whatsappOptout);

	public List<MergeCustomerResponse> anonymousContactMerge(MergeCustomerRequest mergeCustomerRequest);

}
