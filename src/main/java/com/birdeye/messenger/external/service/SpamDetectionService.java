package com.birdeye.messenger.external.service;


import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.CustomerDTO;
import com.birdeye.messenger.dto.MessageDTO;
import com.birdeye.messenger.dto.SpamDetectionRequestDto;
import com.birdeye.messenger.enums.MessageTag;

public interface SpamDetectionService {

	void spamDetectionAllChannels(MessageDTO messageDTO, MessengerContact messengerContact, CustomerDTO customerDTO, MessageTag messageTag);

	public Boolean checkSpamThroughNlp(SpamDetectionRequestDto dto);

	void checkAndAuditSpamForOutboundMessage(MessageDTO messageDTO);

}
