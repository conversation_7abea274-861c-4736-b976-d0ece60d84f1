package com.birdeye.messenger.external.service;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class CampaignReviewRequest {

	Integer customerId;
	String source;
	Integer templateId;
	String smsBody;
	Integer surveyId;
	String surveyType;
	String employeeEmailId;
	Boolean isGlobalTemplate;
	Integer userId;

	public CampaignReviewRequest(Integer customerId, String source, Integer templateId, String smsBody,Integer surveyId,
			String employeeEmailId,Boolean isGlobalTemplate,Integer userId) {
		this.customerId = customerId;
		this.source = source;
		this.templateId = templateId;
		this.smsBody = smsBody;
		this.surveyId=surveyId;
		this.employeeEmailId = employeeEmailId;
		this.isGlobalTemplate = isGlobalTemplate;
		this.userId = userId;
	}
}
