package com.birdeye.messenger.external.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;

import com.birdeye.messenger.dao.entity.AppleMessage;
import com.birdeye.messenger.dao.entity.ConversationActivity;
import com.birdeye.messenger.dao.entity.GoogleMessage;
import com.birdeye.messenger.dao.entity.MessengerMediaFile;
import com.birdeye.messenger.dao.entity.MessengerMessage;
import com.birdeye.messenger.dao.entity.secure.messaging.SecureMessage;
import com.birdeye.messenger.dto.ConversationDTO;
import com.birdeye.messenger.dto.CustomChannelDTO;
import com.birdeye.messenger.dto.LiveChatMessageObject;
import com.birdeye.messenger.dto.SmsDTO;
import com.birdeye.messenger.dto.UserDTO;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.sro.ReviewEvent;
import com.birdeye.messenger.sro.SurveyEvent;

public interface MessengerMessageService {

	void saveMessengerMessage(ConversationDTO conversationDTO, UserDTO userDTO);

	void saveMessengerMessage(SmsDTO smsDTO, UserDTO userDTO);

	void saveMessengerActivity(ConversationActivity activity, Integer conversationId, UserDTO userDTO);

	void saveMessengerMessage(MessageDocument document);

	List<MessengerMessage> deleteByMCId(Integer mcId);
	
	List<MessengerMessage> findByMessengerContactId(Integer mcId);
	
	List<MessengerMessage> deleteByMessengerContactIdAndReviewId(Integer mcId,Integer reviewId);

//	void saveMessengerMessageReviewDetails(ReviewEvent reviewEvent,Integer mcId);

	MessengerMessage findByAccountIdAndReviewId(Integer accountId,Integer reviewId);
	
	MessengerMessage findByMessengerContactIdAndReviewId(Integer mcId,Integer reviewId);

	void  updateMessengerMessage(MessengerMessage messengerMessage);
	void saveMessengerMessageReviewDetails(ReviewEvent event, Integer mcId);
	void updateMessengerMessage(Integer fromMcId,Integer toMcid);
	void deleteAllExceptReviewByMCId(Integer conversationId);
	void saveMessengerMessageSurveyDetails(SurveyEvent.After event, Integer mcId, Integer accountId);
	MessengerMessage findByAccountIdAndSurveyResponseId(Integer accountId,Integer surveyResponseId);
	void deleteBySurveyResponseId(Integer surveyResponseId);

	Map.Entry<MessengerMessage, GoogleMessage> saveGoogleMessage(MessengerMessage msg, GoogleMessage gMsg, Optional<MessengerMediaFile> mediaFile);
	void saveMessengerMessage(LiveChatMessageObject liveChatMessageObject, UserDTO userDTO);

	Entry<MessengerMessage, AppleMessage> saveAppleMessage(MessengerMessage msg, AppleMessage aMsg);
	void updateMessengerMessageMcId(Integer fromMcId, Integer toMcId);

    Entry<MessengerMessage, SecureMessage> saveSecureMessage(MessengerMessage msg, SecureMessage secureMessage);
    
    MessengerMessage findByReviewId(Integer reviewId);
    
    void updateMessengerMessagesInBusinessUpgradeOrDowngrade(Integer targetAccountId, Integer sourceAccountId,
            List<Integer> mcIds);
	
	void updateMessengerMessagesForConversation(Integer fromMcId,Integer toMcid);
	
	void saveMessengerMessage(CustomChannelDTO customChannelDTO, UserDTO userDTO);
	

}
