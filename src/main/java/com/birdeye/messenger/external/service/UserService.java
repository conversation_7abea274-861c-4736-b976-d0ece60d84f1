package com.birdeye.messenger.external.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.Source;
import com.birdeye.messenger.external.dto.UserMessengerNotificationSetting;

public interface UserService {

    Map<String, List<String>> getBusinessUserWebChatTime(Integer businessId);

	UserDTO getUserDTO(Integer userId);

	UserMessengerNotificationSetting getUserNotificationSettings(Integer businessId, Integer userId);

	HashMap<String, ArrayList<String>> getEmailSendTimeForNonInboxUsers(Integer businessId);
	
	Map<Integer, List<Integer>> getValidUserDTOs(Integer userId, Integer accountId, Set<Integer> businessId);

	Map<Integer, TeamAssigneeDto> getValidUserDTOs(Integer userId, Integer accountId, Integer businessId);


	List<String> getEmailIdsForPaymentNotifications(Integer accountId, Integer businessId);
	
    Map<Integer, List<Integer>> getAccessibleLocationsOfAUserForAnAccount(Integer accountId, List<Integer> userIds);

    List<Integer> getTeamIdsOfAUserForAnAccount(Integer accountId, Integer userId);
    
    
    UsersResponse getUsers(List<Integer> userIds, List<String> emailIds);

    UserDTO getUserAndBusinessAssociation(Integer userId, Integer businessId);

	public Map<String, Map<String,Map<String,Map<String,String>>>> getInboxUserEmailAlertSettings(Integer businessId, String type, Integer source);

}
