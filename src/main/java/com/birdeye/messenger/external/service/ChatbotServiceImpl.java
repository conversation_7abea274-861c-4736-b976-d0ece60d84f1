package com.birdeye.messenger.external.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.cache.CacheManager;
import com.birdeye.messenger.cache.SystemPropertiesCache;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.ElasticData;
import com.birdeye.messenger.dto.FeedbackRequest;
import com.birdeye.messenger.dto.LivechatAnonymousConversationDataDto;
import com.birdeye.messenger.dto.MessengerFilter;
import com.birdeye.messenger.dto.RobinPlainTextRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.elastic.MessageDocument.MessageType;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.dto.Suggestion;
import com.birdeye.messenger.service.GPTResponseFeedbackService;
import com.birdeye.messenger.service.MessengerContactService;
import com.birdeye.messenger.service.RedisHandler;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.MessengerUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 *
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ChatbotServiceImpl implements ChatbotService {

	@Value("${chatbot.service.url}")
	private String chatbotUrl;

	private static String DEFAULT_FALLBACK_INTENT = "DEFAULT_FALLBACK";

	private final String queryUrl = "/chatbot/query/%s/channel";
	private final String suggestionUrl = "/chatbot/query/suggestion";
	private final String feedbackUrl = "/chatbot/feedback/";
	private final String auditUrl = "/chatbot/audit/query";
    private final String plainTextUrl = "/chatbot/query/response/plain-text";

	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private MessengerContactService messengerContactService;

	@Autowired
	private NLPService nlpService;

	@Autowired
	private GPTResponseFeedbackService gptResponseFeedbackService;
	private final RedisHandler redisHandler;

	@Override
	public Optional<ChatbotQueryResponse> getQueryResponse(Integer businessId, Integer accountId, String queryText,
			QueryChannel queryChannel, Integer customerId, Integer mcId, Boolean isReceivedDuringBusinessHour,LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto, Integer widgetId) {
		Optional<ChatbotQueryResponse> chatbotQueryResponse = Optional.empty();
		Optional<ChatbotQueryResponse> gptResponse = Optional.empty();
		String gptReplyEnabledWidgets = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("widget_id_with_gpt_reply_enabled", "261968:1");
		List<String> gptReplyEnabledWidgetsList=ControllerUtil.getTokensListFromString(gptReplyEnabledWidgets);
		try {
			boolean aiCallAllowed = isAiChatBotEnabledForTheAccount(accountId);
			List<Integer> accountsWithCustomWidgetConfigured = null;
			if (widgetId != null){
				String widgetAccountId = accountId + ":" + widgetId;
				if (CollectionUtils.isNotEmpty(gptReplyEnabledWidgetsList)) {
					accountsWithCustomWidgetConfigured = gptReplyEnabledWidgetsList.stream()
				            .map(widget -> Integer.valueOf(widget.split(":")[0]))
				            .collect(Collectors.toList());
					if (CollectionUtils.isNotEmpty(accountsWithCustomWidgetConfigured) && accountsWithCustomWidgetConfigured.contains(accountId)) {
						if (gptReplyEnabledWidgetsList.contains(widgetAccountId)) {
							aiCallAllowed = true;
			            } else {
			            	aiCallAllowed = false;
			            }
						log.info("GPT reply widget level config match for widget-{} -> {}", widgetAccountId, aiCallAllowed);
					}
				}
			}
			if (aiCallAllowed){
				log.info("[GPTRobinReply] getQueryResponse for queryText:{}", queryText);
				if (redisHandler.isCallingGPTAllowed(String.valueOf(customerId))) {//check for rate limit per customer
					log.info("GPT api call allowed for accountID : {}, widgetId : {}", accountId, widgetId);
					GPTResponseFeedbackAudit audit = gptResponseFeedbackService.audit(accountId, businessId, mcId, queryChannel.name(), queryText);
					gptResponse = tryForGPTRobinReply(businessId, accountId, queryText, queryChannel, customerId, mcId, audit, isReceivedDuringBusinessHour,livechatAnonymousConversationDataDto);
					audit.setUpdatedAt(new Date());
					gptResponseFeedbackService.save(audit);
				} else {
					log.info("GPT api call limit exceeded");
				}
			}
			boolean gptToReply = gptResponse.isPresent();
			if (gptToReply) {
				boolean support = (boolean) gptResponse.get().getData().get("support");
				String code = (String) gptResponse.get().getData().get("code");
				String responseFromGpt = (String) gptResponse.get().getData().get("response");
				if (support) {
					log.info("[GPTRobinReply] support true b_id:{}, query:{}",businessId, queryText);
					gptResponse.get().setIntentType("DEFAULT_FALLBACK");
					return gptResponse;
				}
				if ("500".equals(code) || "506".equals(code)) {
					log.info("[GPTRobinReply] unsupported code:{}, b_id:{}, query:{}",code,businessId, queryText);
				}
				if (!support && Objects.nonNull(responseFromGpt)) {
					return gptResponse;
				}
			}
		} catch (Exception ex) {
			log.error("Error in GPTRobinReply : {}",ex.getMessage());
		}
		log.info("[ChatbotServiceImpl] getQueryResponse for queryText:{}", queryText);

		String url = this.chatbotUrl + String.format(queryUrl, queryChannel);
		url = url+"?customerId="+customerId;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HDR_BUSINESS_ID, businessId.toString());
		headers.add(HDR_ACCT_ID, accountId.toString());

		try {
			HttpEntity<String> chatbotQueryRequest = new HttpEntity<String>(queryText,headers);
			ResponseEntity<ChatbotQueryResponse> response = restTemplate.postForEntity(url, chatbotQueryRequest,
					ChatbotQueryResponse.class, headers);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("[ChatbotServiceImpl] getQueryResponse failed for queryText {} with status {} ", queryText,
						response.getStatusCode());
			}

			// Return empty optional if response data is empty OR a default fallback.
			if (Objects.isNull(response) || Objects.isNull(response.getBody())
					|| MapUtils.isEmpty(response.getBody().getData())
					|| (DEFAULT_FALLBACK_INTENT.equalsIgnoreCase(response.getBody().getIntentType())
							&& queryChannel != QueryChannel.LIVECHAT
							&& queryChannel != QueryChannel.LIVECHAT_BIRDEYE_PROFILE)) {
				log.info("Returning an empty optional for an empty response from chatbot for businessId:{} and queryText:{}", businessId, queryText);
				return Optional.empty();
			}
			chatbotQueryResponse = Optional.ofNullable(response.getBody());
		}
		catch (Exception e) {
			log.info("[ChatbotServiceImpl] getQueryResponse for queryText:{} failed due to:{}", queryText, e.getMessage());
		}
		return chatbotQueryResponse;
	}

	private Optional<ChatbotQueryResponse> tryForGPTRobinReply(Integer businessId, Integer accountId, String queryText, QueryChannel queryChannel,
			Integer customerId, Integer mcId, GPTResponseFeedbackAudit audit, Boolean isReceivedDuringBusinessHour,LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto) {
		BusinessDTO businessDto = businessService.getBusinessDTO(businessId);
		List<Map<String, String>> chatHistory = getHistoryMessageForAI(businessId, accountId, customerId, mcId);
		BusinessTimingDTO businessTiming = businessService.getBusinessTimings(businessId, true);
		BusinessProfileData businessProfileData = businessService.getBusinessProfileData(businessId);
		return nlpService.getGPTPoweredRobinReply(accountId, businessDto, queryText, queryChannel, customerId, chatHistory,
				audit, isReceivedDuringBusinessHour, businessTiming, businessProfileData, livechatAnonymousConversationDataDto);
	}

	@Override
	public List<Map<String, String>> getHistoryMessageForAI(Integer businessId, Integer accountId, Integer customerId,
			Integer mcId) {
		if (Objects.isNull(mcId)) {
			MessengerContact messengerContact = messengerContactService.findByCustomerId(customerId);
			if (!ObjectUtils.isEmpty(messengerContact)) {
				mcId = messengerContact.getId();
			}
		}
		if (Objects.isNull(mcId)) {
			log.info("[getHistoryMessageForAI] No messengerContact found for :{}", mcId);
			return Collections.emptyList();
		}
		MessengerFilter esQueryData = new MessengerFilter();
		esQueryData.setConversationId(mcId);
		esQueryData.setStartIndex(0);
		esQueryData.setQueryFile(Constants.Elastic.GPT_POWERED_ROBIN_MESSAGE_HISTORY);
		esQueryData.setCount(100);
		esQueryData.setAccountId(accountId);

		ElasticData messageData = messengerContactService.getMessageData(esQueryData);
		if (!messageData.isSucceeded()) {
			log.info("[getHistoryMessageForAI] No history messages found for GPT mcId: {}", mcId);
			return Collections.emptyList();
		}

		List<MessageDocument> messageDocuments = (List<MessageDocument>) messageData.getResults();
		List<Map<String, String>> chatHistory = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(messageDocuments)) {
			messageDocuments = messageDocuments.stream().map(messageDocument -> {
				messageDocument.setMsg_body(MessengerUtil.decryptMessage(messageDocument));
				return messageDocument;
			}).collect(Collectors.toList());

			log.info("[GPTRobinReply] : {} messageDocuments found", messageDocuments.size());
			List<RobinPlainTextRequest> robinPlainTextRequests = messageDocuments.stream()
					.filter(messageDocument -> MessageType.RICH_CONTENT_CHAT.equals(messageDocument.getMessageType()))
					.map(RobinPlainTextRequest::new)
					.filter(robinPlainTextRequest -> !MessengerUtil.checkIfAnyValueIsNull(robinPlainTextRequest.getId(),
							robinPlainTextRequest.getIntentType(), robinPlainTextRequest.getData()))
					.collect(Collectors.toList());

			Map<String, String> robinPlainTextResponseMap = getRobinPlainTextBodyForRichContentChat(
					robinPlainTextRequests, accountId, businessId);

			chatHistory = messageDocuments.stream()
					.map(messageDocument -> nlpService.buildConversationListFromMessageDocument(messageDocument,
							robinPlainTextResponseMap))
					.filter(Objects::nonNull).collect(Collectors.toList());
		}
		return chatHistory;
	}

	@Override
	@Async
	public void updateFeedback(FeedbackRequest feedbackRequest, Integer accountId) {

		boolean aiCallAllowed = isAiChatBotEnabledForTheAccount(accountId);
		if (aiCallAllowed) {
			GPTResponseFeedbackAudit existingAudit = gptResponseFeedbackService.findById(feedbackRequest.getEventId());
			if (Objects.nonNull(existingAudit)){
				log.info("Existing GPTResponseFeedbackAudit found for id :{}", feedbackRequest.getEventId());
				existingAudit.setFeedback(feedbackRequest.getValue());
				existingAudit.setUpdatedAt(new Date());
				gptResponseFeedbackService.save(existingAudit);
				return;
			}
		}

		String url = this.chatbotUrl +feedbackUrl + feedbackRequest.getEventId();
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> httpEntity = new HttpEntity<>(feedbackRequest, headers);
		ResponseEntity<Void> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Void.class);
		if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
			log.info("User feedback updated successfully for event id: {} ",feedbackRequest.getEventId());
			return;
		} else {
			log.error("Error in updating user feedback for event id: {} ",feedbackRequest.getEventId());
		}
	}

	@Override
	public Optional<ChatbotQueryResponse> getSuggestionResponse(Integer accountId, Integer businessId,
			Suggestion suggestion, QueryChannel queryChannel, Integer customerId) {
		Optional<ChatbotQueryResponse> chatbotQueryResponse = Optional.empty();
		log.info("getSuggestionResponse for accountId {} and question:{}",accountId, suggestion.getValue());

		String url = this.chatbotUrl + suggestionUrl+"?channel="+queryChannel;
		url = url+"&customerId="+customerId;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HDR_BUSINESS_ID, businessId.toString());
		headers.add(HDR_ACCT_ID, accountId.toString());

		try {
			HttpEntity<Object> chatbotQueryRequest = new HttpEntity<Object>(suggestion,headers);
			ResponseEntity<ChatbotQueryResponse> response = restTemplate.postForEntity(url, chatbotQueryRequest,
					ChatbotQueryResponse.class, headers);
			if (!response.getStatusCode().is2xxSuccessful()) {
				log.error("[ChatbotServiceImpl] getSuggestionResponse failed for queryText {} with status {} ", suggestion.getValue(),
						response.getStatusCode());
			}

			// Return empty optional if response data is empty OR a default fallback.
			if (Objects.isNull(response) || Objects.isNull(response.getBody())
					|| MapUtils.isEmpty(response.getBody().getData())) {
				log.info("Returning an empty optional for an empty response from chatbot for businessId:{} and queryText:{}", businessId, suggestion.getValue());
				return Optional.empty();
			}
			chatbotQueryResponse = Optional.ofNullable(response.getBody());
		}
		catch (Exception e) {
			log.info("[ChatbotServiceImpl] getSuggestionResponse for queryText:{} failed due to:{}", suggestion.getValue(), e.getMessage());
		}
		return chatbotQueryResponse;
	}

	@Override
	public Integer auditChatBotQuery(Suggestion suggestion, Integer accountId,Integer businessId,QueryChannel queryChannel,String intent) {
		String url = this.chatbotUrl +auditUrl+"?channel="+queryChannel+"&intent="+intent;
		Integer auditId=null;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HDR_BUSINESS_ID, businessId.toString());
		headers.add(HDR_ACCT_ID, accountId.toString());
		HttpEntity<Object> httpEntity = new HttpEntity<>(suggestion, headers);
		ResponseEntity<Integer> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Integer.class);
		if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
			log.debug("query {} audited successfully for accountId id: {} ",suggestion.getValue(),accountId);
			auditId=responseEntity.getBody();
		} else {
			log.error("query {} audited successfully for accountId id: {} ",suggestion.getValue(),accountId);
		}
		return auditId;
	}

    @Override
    public Map<String, String> getRobinPlainTextBodyForRichContentChat(
            List<RobinPlainTextRequest> robinPlainTextRequests, Integer accountId, Integer businessId) {
        log.debug(
                "getRobinPlainTextBodyForRichContentChat called with ,accountId: {} and businessId : {}",accountId, businessId);
        Map<String, String> responseBody = null;
        if (CollectionUtils.isNotEmpty(robinPlainTextRequests)) {
            String url = chatbotUrl + plainTextUrl;
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add(HDR_BUSINESS_ID, businessId.toString());
            headers.add(HDR_ACCT_ID, accountId.toString());

            HttpEntity<List<RobinPlainTextRequest>> request = new HttpEntity<>(robinPlainTextRequests, headers);

            ResponseEntity<Map<String, String>> responseEntity = restTemplate.exchange(url, HttpMethod.POST, request,
                    new ParameterizedTypeReference<Map<String, String>>() {
                    });

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.debug("getRobinPlainTextBodyForRichContentChat called successfully");
                responseBody = responseEntity.getBody();
                return responseBody;
            }

            log.error("error occurred in getRobinPlainTextBodyForRichContentChat");
        }

        return responseBody;
    }

	private Boolean isAiChatBotEnabledForTheAccount(Integer accountId){
		BusinessOptionResponse businessOptionResponse = businessService.getBusinessOptionsConfig(accountId,true);
		if(Objects.nonNull(businessOptionResponse) && Objects.nonNull(businessOptionResponse.getIsAIFaqEnabled()) && businessOptionResponse.getIsAIFaqEnabled() == 1){
			return true;
		}
		return false;
	}

	@Override
	public String getAIReplyUsingKnowledgeBase(Integer businessId, Integer accountId, String queryText, QueryChannel queryChannel, Integer customerId, Integer mcId, Boolean isReceivedDuringBusinessHour, LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto) {
		log.info("[LeadGenAgent] getQueryResponse for queryText:{}", queryText);
		Optional<ChatbotQueryResponse> gptResponse = Optional.empty();
		GPTResponseFeedbackAudit audit = gptResponseFeedbackService.audit(accountId, businessId, mcId, queryChannel.name(), queryText);
		gptResponse = tryForGPTRobinReply(businessId, accountId, queryText, queryChannel, customerId, mcId, audit, isReceivedDuringBusinessHour,livechatAnonymousConversationDataDto);
		audit.setUpdatedAt(new Date());
		gptResponseFeedbackService.save(audit);
		boolean gptToReply = gptResponse.isPresent();
		if (gptToReply) {
			boolean support = (boolean) gptResponse.get().getData().get("support");
			String code = (String) gptResponse.get().getData().get("code");
			String responseFromGpt = (String) gptResponse.get().getData().get("response");
			if (support) {
				log.info("[LeadGenAgent] support true b_id:{}, query:{}",businessId, queryText);
				gptResponse.get().setIntentType("DEFAULT_FALLBACK");
				return responseFromGpt;
			}
			if ("500".equals(code) || "506".equals(code)) {
				log.info("[LeadGenAgent] unsupported code:{}, b_id:{}, query:{}",code,businessId, queryText);
			}
			if (!support && Objects.nonNull(responseFromGpt)) {
				return responseFromGpt;
			}
		}
		return "";
		//do we need to add fallback here?
	}
}
