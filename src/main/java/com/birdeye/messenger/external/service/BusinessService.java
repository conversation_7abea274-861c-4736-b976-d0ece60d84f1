package com.birdeye.messenger.external.service;

import java.util.List;
import java.util.Map;

import com.birdeye.messenger.dto.AppUserNotificationSetting;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessDetails;
import com.birdeye.messenger.dto.BusinessHoursDTO;
import com.birdeye.messenger.dto.BusinessLite;
import com.birdeye.messenger.dto.BusinessLocationCustomFieldsTokensDto;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.ChatTranscriptBusinessUserConfig;
import com.birdeye.messenger.dto.ChatTranscriptRequest;
import com.birdeye.messenger.dto.EmailSenderDto;
import com.birdeye.messenger.dto.GetUserIdsRequest;
import com.birdeye.messenger.dto.InfoByPhoneNumberRequest;
import com.birdeye.messenger.dto.PublicDataBusinessDTO;
import com.birdeye.messenger.dto.TeamDto;
import com.birdeye.messenger.dto.UserEvent;
import com.birdeye.messenger.dto.UserIdEmailIdDTO;
import com.birdeye.messenger.dto.UserLoginMessage;
import com.birdeye.messenger.dto.UserResponse;
import com.birdeye.messenger.dto.apple.chat.AppleUploadAttachmentRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.ext.sro.GetUsersByIdsResponse.User;
import com.birdeye.messenger.external.dto.BusinessFeaturesDto;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.dto.BusinessReceptionistResponse;
import com.birdeye.messenger.external.dto.UserMessengerNotificationSetting;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.sro.GetBusinessUser;
import com.birdeye.messenger.sro.TeamDTO;

public interface BusinessService {

	Integer isMessengerEnabled(Integer businessId);

	BusinessDTO getBusinessDTO(Integer businessId);

    BusinessDTO getBusinessDTO(Integer businessId, Integer includeClosed);

    public BusinessDTO getBusinessByBusinessNumber(Long businessNumber);
	 
	Map<Integer, BusinessDTO> getBusinessDataForMessenger(UserLoginMessage userLoginMessage);
	
    Map<Integer, BusinessDTO> getBusinessDataForMessenger(List<Integer> businessIds, Integer userId, Integer accountId);

	BusinessDTO getBusinessLiteDTO(Integer businessId);
	
	BusinessDTO getBusinessLiteDTO(String key, String value) throws Exception;
	
	BusinessDTO getBusinessLiteDTOWithLocation(String key, String value) throws Exception;

    Map<Integer,BusinessDTO> getBusinessSMSEnabledLocations(Integer enterpriseId);
    
	BusinessReceptionistResponse getDataForReceptionist(Integer businessId);
	
	BusinessDTO getBusinessInfoByPhoneNumber(InfoByPhoneNumberRequest request);

	BusinessOptionResponse getBusinessOptionsConfig(Integer businessId, Boolean traverseHierarchy);

   	List<TeamDTO> getTeams(Integer accountId, Integer locationId);

	List<TeamDto> getTeamsByIds(List<Integer> teamIds);

    UserMessengerNotificationSetting getUserNotificationSettings(Integer businessId, Integer userId);

    Map<String, Map<String, Map<String, String>>> getEmailConfigOfTeamsForNewMessage(Integer businessId, Integer teamId, String type, Integer source);

    UserIdEmailIdDTO getEmailConfigOfUsersForTeamsAssignment(Integer teamId,Integer businessId);

	void removeUserTeamsMapping(UserEvent userEvent);
	
    List<Integer> getAllUsersOfTeams(Integer teamId,Integer businessId);

    UserResponse getBusinessUsers(Integer accountId, GetBusinessUser getBusinessUser);
	
	Map<String, Integer> getUserIds(GetUserIdsRequest request);

	List<BusinessLite> getBusinessLiteObjects(Integer accountId);

	EmailSenderDto getSenderEmailInfo(Integer businessId);

	ChatTranscriptBusinessUserConfig getChatTranscriptBusinessUserConfig(Integer businessId);

	BusinessHoursDTO getBusinessHours(Integer businessId);

	List<User> getUsersByIds(List<Integer> userIds);

	Integer validateMessengerEnabled(Integer businessId, boolean throwException);

    List<String> getSupportedCountryCodes(Integer businessId);

	BusinessTimingDTO getBusinessTimings(Integer businessId,boolean formatRequired);

	boolean getMessengerEnabled(Integer businessId);
	
	public Map<Integer, BizLite> getBizLite(List<Integer> businessIds, Integer accountId);

	Map<Integer, String> uploadAppleImagesInCDN(List<AppleUploadAttachmentRequest> appleUploadAttachmentRequests, Integer businessId, Channel apple);
	
	Map<String,Object> getBusinessData(Integer businessId);

	List<Integer> getBusinessSmsNumberByIds(List<Integer> businessIds);

	String getBusinessSmsNumberById(Integer businessId);

	AppUserNotificationSetting getAppUsersNotificationSettings(Integer businessId);

	String getBusinessSMSNumber(Integer businessId);

	void updateUserTranscriptConfig(ChatTranscriptRequest chatTranscriptRequest);

	String getWebsiteDomain(Integer businessId);

	PublicDataBusinessDTO getPublicBusinessData(Integer businessId);

	BusinessDTO validateWidgetApiKey(String accountId,String apiKey,boolean isSmall);

	public boolean validateWidgetApiKey(Integer accountId, String widgetApiKey);

	public TeamDTO getTeamByTeamName(Integer accountId, String teamName);

	public BusinessLocationCustomFieldsTokensDto getBusinessCustomFieldsAndTokenByBusinessID(Integer businessId);
	public BusinessProfileData getBusinessProfileData(Integer businessId);

	public BusinessDetails getBusinessDetailsByBusinessNumber(Long businessNumber);

	boolean validateOldWidgetApiKey(Integer accountId, String widgetApiKey);

	BusinessDTO getBusinessLite(String url);

	BusinessFeaturesDto getBusinessFeaturesByAccountId(Integer accountId);
}
