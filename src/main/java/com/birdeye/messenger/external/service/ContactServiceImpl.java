package com.birdeye.messenger.external.service;

import java.io.IOException;
import java.util.*;

import jakarta.transaction.Transactional;

import com.birdeye.messenger.dto.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dao.entity.FacebookMessage;
import com.birdeye.messenger.dao.entity.MessengerContact;
import com.birdeye.messenger.dao.repository.FacebookMessageRepository;
import com.birdeye.messenger.dao.repository.MessengerContactRepository;
import com.birdeye.messenger.dto.facebook.GetUserDetailsMessage;
import com.birdeye.messenger.dto.facebook.UserDetailsMessage;
import com.birdeye.messenger.enums.ContactState;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.BadRequestException;
import com.birdeye.messenger.exception.ComponentCodeEnum;
import com.birdeye.messenger.exception.ErrorMessageBuilder;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.exception.NotFoundException;
import com.birdeye.messenger.ext.sro.UpdateCustomerStatus;
import com.birdeye.messenger.external.service.KontactoRequest.LocationInfo;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContactServiceImpl implements ContactService {

	@Value("${kontacto.service.url}")
	private String kontactoUrl;

	// Consider using different rest templates.
	@Autowired
	private RestTemplate restTemplate;

	@Autowired
	private MessengerContactRepository messengerContactRepository;

	@Autowired
	private FacebookMessageRepository facebookMessageRepository;

	@Autowired
	private SocialService socialService;

	@Autowired
	private BusinessService businessService;

	@Override
	public CustomerDTO getorCreateNewCustomer(KontactoRequest request, Integer accountId, Integer userId) {

		String url = this.kontactoUrl + LOC_CUSTOMER_CREATE;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		// User Id & Account Ids will be passed as headers.
		headers.add(HEADER_ENT_ID, accountId.toString());
		headers.add(HEADER_USER_ID, userId.toString());
		HttpEntity<KontactoRequest> kontactoRequest = new HttpEntity<KontactoRequest>(request,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<CustomerDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST,kontactoRequest, CustomerDTO.class, headers);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getorCreateNewCustomer", startTime, endTime);
		if(responseEntity.getStatusCode().is2xxSuccessful()) {
			CustomerDTO dto = responseEntity.getBody();
			if(Objects.nonNull(dto) && ContactState.PRE_LEAD.equals(dto.getContactState())) {
				log.info("getOrCreateNewCustomer: PRE_LEAD state is not valid for Inbox consumption [{}] accountId [{}]", dto);
			}
			return dto;
		} else {
			log.error(new ErrorMessageBuilder(ErrorCode.CUSTOMER_NOT_FOUND, ComponentCodeEnum.CUSTOMER)
					.message("Error - {} returned in url - {}, response - {} for request - {}", 
							responseEntity.getStatusCode(), url, responseEntity.getBody(), request).build());
			throw new BadRequestException(ErrorCode.BAD_REQUEST, "Error response from contact-service");
		}
	}

	@Override
	@Cacheable(cacheNames = Constants.CUSTOMER_CACHE, key="'CID-'.concat(#customerId)", unless="#result == null")
	public CustomerDTO findById(Integer customerId) {
		Objects.requireNonNull(customerId);
		String url = this.kontactoUrl + LOC_CUSTOMER_BYID + customerId+"?sentiment=true";
		return setHeaderAndCallGetAPI(url);
	}

	@CacheEvict(value = Constants.CUSTOMER_CACHE, key = "'CID-'.concat(#customerId)")
	public void onCustomerDataChange(Integer customerId) {
		log.info("Clearing Kontacto cache for customer id {} ", customerId);
	}

	/**
	 * Such cases would still be required, since cache cleanup might be done in async path.
	 */
	@Override
	public CustomerDTO findByIdNoCaching(Integer customerId) {
		Objects.requireNonNull(customerId);
		String url = this.kontactoUrl + LOC_CUSTOMER_BYID + customerId;
		return setHeaderAndCallGetAPI(url);
	}

	private CustomerDTO setHeaderAndCallGetAPI(String url) {
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		// TODO: error handling.
        long startTime = System.currentTimeMillis();
		ResponseEntity<CustomerDTO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, null, CustomerDTO.class, headers);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getCustomerDTO", startTime, endTime);
		if(responseEntity.getStatusCode().is2xxSuccessful()) {
			CustomerDTO dto = responseEntity.getBody();
			if(Objects.nonNull(dto) && ContactState.PRE_LEAD.equals(dto.getContactState())) {
				log.info("getOrCreateNewCustomer: PRE_LEAD state is not valid for Inbox consumption [{}] accountId [{}]", dto);
			}
			return dto;
		}
		else{
			log.error("Error response from contact-service url {} {}", url, responseEntity);
			throw new NotFoundException(ErrorCode.CUSTOMER_NOT_FOUND, "Error response from contact-service");
		}
	}
	
	@Override
    @Cacheable(cacheNames = Constants.CONTACT_UPGRADE_ACCOUNTS_CACHE, key="'contact_upgrade_accounts_cache'", unless="#result == null")
    public List<Integer> getAllContactUpgradeEnabledAccounts() {
		String url = this.kontactoUrl + FIND_ALL_CONTACT_UPGRADE_ENABLED_ACCOUNTS;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity,
                String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getAllContactUpgradeEnabledAccounts", startTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("Error in fetching accountIds for which contact upgrade is enabled from kontact service");
            return null;
        }
        return ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), Integer.class);
    }

	@Override
	public Integer findCustomerIdByEcIdAndBusinessId(Integer ecid, Integer businessId) {
		Objects.requireNonNull(ecid);
		Objects.requireNonNull(businessId);
		String url = this.kontactoUrl + CUSTOMER_MAP + ecid + "/" + businessId;
		CustomerDTO customerDTO = setHeaderAndCallGetAPI(url);
		if(Objects.nonNull(customerDTO)) return customerDTO.getId();
		return null;
	}

	@Override
	public CustomerDTO mergeCustomer(CustomerDTO customerDTO) {
		String url = kontactoUrl + "/customer/merge/fb";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<CustomerDTO> request = new HttpEntity<>(customerDTO ,headers);
		ResponseEntity<CustomerDTO> response = restTemplate.exchange(url, HttpMethod.PUT, request, CustomerDTO.class, headers);
		if(response.getStatusCode().is2xxSuccessful()) {
			return response.getBody();
		}
		throw new MessengerException("Error Response from contact-service" + response.toString());
	}

	@Override
	@Transactional
	public KontactoRequest createContactoRequest(Integer messengerContactId) {
		KontactoRequest kontactoRequest = null;
		MessengerContact messengerContact = messengerContactRepository.findById(messengerContactId).orElseThrow(() -> new NotFoundException(ErrorCode.MESSENGER_CONTACT_NOT_EXIST));
//		String pageId = platformDbRepository.getFacebookPageIdByBusinessId(messengerContact.getBusinessId());
		String pageId = socialService.getFacebookPageIdByBusinessId(messengerContact.getBusinessId());
		String userId = null;
		List<FacebookMessage> facebookMessages = facebookMessageRepository.getMessageByPageIdAndBusinessId(pageId,
				messengerContact.getId());
		if (CollectionUtils.isNotEmpty(facebookMessages)) {
			FacebookMessage facebookMessage = facebookMessages.get(0);
			userId = facebookMessage.getSenderFacebookId();
			log.info("fetching user details from social for facebook user Id {}: ", userId);
			UserDetailsMessage userDetailsMessage = socialService
					.getFacebookUserInfo(new GetUserDetailsMessage(pageId, userId));
			BusinessDTO businessDTO = businessService.getBusinessDTO(messengerContact.getBusinessId());
			if(Objects.isNull(businessDTO)) {
				log.info("createContactoRequest: businessDTO returned null from core-service for messengerContact {}", messengerContact);
				return null;
			}
			kontactoRequest = createKontactoRequest(userDetailsMessage, userId, businessDTO.getCountryCode());
			kontactoRequest.setBusinessId(businessDTO.getBusinessId());
			kontactoRequest.setRoutingId(businessDTO.getRoutingId());
			return kontactoRequest;
		}
		log.info("No Facebook messages found for Messenger Contact id {} ", messengerContactId);
		return null;
	}

	private KontactoRequest createKontactoRequest(UserDetailsMessage userDetailsMessage, String facebookUserId,
			String countryCode) {
		KontactoRequest kontactoRequest = new KontactoRequest();
		String custName = userDetailsMessage.getFirst_name() + " " + userDetailsMessage.getLast_name();
		kontactoRequest.setName(custName);
		kontactoRequest.setEmailId(facebookUserId + "@fb.com");
		kontactoRequest.setSource(KontactoRequest.FACEBOOK);
		LocationInfo locationInfo = new LocationInfo();
		locationInfo.setCountryCode(countryCode);
		kontactoRequest.setLocation(locationInfo);
		return kontactoRequest;
	}

	@Override
	@Deprecated
	public void updateCustomerStatus(Integer customerId) {
		String url = this.kontactoUrl + UPDATE_CUSTOMER_STATUS;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		UpdateCustomerStatus request=new UpdateCustomerStatus(customerId, "Unsubscribed", "failure");
		HttpEntity<UpdateCustomerStatus> kontactoRequest = new HttpEntity<UpdateCustomerStatus>(request,headers);
		long startTime = System.currentTimeMillis();
		restTemplate.postForObject(url, kontactoRequest, String.class, headers);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("updateCustomerStatus", startTime, endTime);
	}

	@Override
	public Boolean unBlockEmailCommunication(Integer ecid) {
		String url = this.kontactoUrl + Constants.UNSUBSCRIBE_CUSTOMER_URL + true;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<List<Integer>> kontactoRequest = new HttpEntity<List<Integer>>(Arrays.asList(ecid), headers);
		return restTemplate.exchange(url, HttpMethod.POST, kontactoRequest, Boolean.class).getBody();
	}

	@Override
	public List<CustomerLite> getByIds(List<Integer> cIds) {
	    //JIRa https://birdeye.atlassian.net/browse/BIRDEYE-83283
		String url = this.kontactoUrl + "/customer/getByIds";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<List<Integer>> kontactoRequest = new HttpEntity<List<Integer>>(cIds, headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, kontactoRequest, String.class);
        if(!responseEntity.getStatusCode().is2xxSuccessful()) {
            log.error("contact-service error {}", responseEntity.getBody());
            throw new MessengerException("Error response from contact-service" + responseEntity.toString());
        }
        else {
            ObjectMapper mapper = new ObjectMapper();
            String body = responseEntity.getBody();
            try {
                TypeReference<List<CustomerLite>> typeReference = new TypeReference<List<CustomerLite>>() {};
                return mapper.readValue(body, typeReference);
            } catch (IOException e) {
                log.error("getByIds: error in mapping data from response string to List of Customer Lite", e);
				throw new MessengerException(ErrorCode.MICRO_SERVICE_SERVICE_COMMUNICATION_ERROR,"Response Contract violation from contact-service" + responseEntity.toString());
            }
        }
	}

	@Override
	public List<CustomFieldDto> getCustomFields(Long accountId) {
		String url = this.kontactoUrl + CUSTOM_FIELD;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HEADER_ENT_ID, accountId.toString());
		CustomFieldRequest customFieldRequest = new CustomFieldRequest();
		customFieldRequest.setSize(Constants.CUSTOM_FIELD_SIZE);
		customFieldRequest.setOrder("desc");
		customFieldRequest.setSortBy("createdAt");
		customFieldRequest.setSearchStr("");
		HttpEntity<Object> httpEntity = new HttpEntity<>(customFieldRequest, headers);
		ResponseEntity<CustomFieldResponse> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity, CustomFieldResponse.class, headers);
		if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
			List<CustomFieldDto> customFieldDtos = responseEntity.getBody().getCustomFields();
			if (Objects.nonNull(customFieldDtos)) {
				log.info("Received custom field from Kontacto api for account id - {}", accountId);
				return customFieldDtos;
			}
			else {
				log.info("No custom field received from Kontacto api for account id - {}", accountId);
				return null;
			}
		}
		else if(responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() == null) {
			log.info("No response received from Kontacto api for account id - {}", accountId);
		}
		else {
			log.error("Error response from contact-service url - {} and response - {}", url, responseEntity);
			throw new NotFoundException(ErrorCode.INTERNAL_SERVER_ERROR, "Error response from contact-service");
		}
		return null;
	}

	@Override
	public CustomerDTO findByIdWithCustomFields(Integer customerId) {
		String url = this.kontactoUrl + LOC_CUSTOMER_BYID + customerId + "?customField=true";
		return setHeaderAndCallGetAPI(url);
	}

	@Override
	public Boolean markBlockAndSpam(MarkBlockAndSpamRequestDto markBlockAndSpamRequestDto) {
		log.info("Mark Block and spam : {}", markBlockAndSpamRequestDto.getCid());
		String url = this.kontactoUrl + "/customer/update/blocked";
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> entity = new HttpEntity<>(markBlockAndSpamRequestDto,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<MarkBlockAndSpamResponseDto> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity,MarkBlockAndSpamResponseDto.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("markBlockAndSpam", startTime, endTime);
		if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity == null) {
			log.info("Error in response from Kontacto service to block and spam customer");
			return false;
		}
		return true;

	}

	@Override
	public CustomerDTO lookupCustomerForTfn(SMSMessageDTO smsMessageDTO) {
		KontactoLookupRequest request = new KontactoLookupRequest(smsMessageDTO.getFromNumber(),
				smsMessageDTO.getToNumber(), smsMessageDTO.getLastSentBusinessId());

		String url = this.kontactoUrl + LOOKUP_CUSTOMER_TFN;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<KontactoLookupRequest> kontactoRequest = new HttpEntity<KontactoLookupRequest>(request,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<CustomerDTO> responseEntity = restTemplate.exchange(url, HttpMethod.POST,kontactoRequest, CustomerDTO.class, headers);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("lookupCustomerForTfn", startTime, endTime);
		if(responseEntity.getStatusCode().is2xxSuccessful()) {
			return responseEntity.getBody();
		} else {
			log.error(new ErrorMessageBuilder(ErrorCode.CUSTOMER_NOT_FOUND, ComponentCodeEnum.CUSTOMER)
					.message("Error - {} returned in url - {}, response - {} for request - {}", 
							responseEntity.getStatusCode(), url, responseEntity.getBody(), request).build());
			return null;
		}
	
	}

	@Override
	public List<CustomerDTO> findAllCustomer(String fromNumber, String email, Integer businessId, Integer accountId) {
		FindAllCustomerRequest request = new FindAllCustomerRequest(fromNumber,null
				, businessId,accountId);

		String url = this.kontactoUrl + FIND_ALL_CUSTOMER;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("account-id", accountId.toString());
		HttpEntity<FindAllCustomerRequest> kontactoRequest = new HttpEntity<FindAllCustomerRequest>(request,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST,kontactoRequest, String.class, headers);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("lookupCustomerForTfn", startTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching teams for enterprise from core service");
            return null;
        }
        return ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), CustomerDTO.class);
	}

	@Override
	public boolean updateCustomerSubscriptionStatus(Integer cId, Integer accountId, Boolean smsUnsubscribe,String emailCategory, String smsKeyword, Boolean whatsappOptout){
		String url = this.kontactoUrl + UPDATE_CUSTOMER_SUBSCRIPTION_STATUS;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("accountId",accountId.toString());

		UpdateCustomerStatus request=new UpdateCustomerStatus(cId, smsUnsubscribe,emailCategory,smsKeyword);
		if (whatsappOptout != null) {
			request = new UpdateCustomerStatus(cId, whatsappOptout);
		}
		HttpEntity<UpdateCustomerStatus> kontactoRequest = new HttpEntity<UpdateCustomerStatus>(request,headers);
		long startTime = System.currentTimeMillis();
		try {
			ResponseEntity<Object> res = restTemplate.exchange(url, HttpMethod.POST, kontactoRequest, Object.class, headers);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("updateCustomerStatus", startTime, endTime);

			return res.getStatusCode().is2xxSuccessful();
		} catch (Exception e) {
			log.error("Error updating customer subscription status on Kontacto service", e);
			return false;
		}
	}
	
//	@Override
//	public List<MergeCustomerResponse> anonymousContactMerge(MergeCustomerRequest mergeCustomerRequest){
//		String url = this.kontactoUrl + MERGE_CUSTOMER;
//		HttpHeaders headers = new HttpHeaders();
//		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
//		headers.setContentType(MediaType.APPLICATION_JSON);
//		HttpEntity<MergeCustomerRequest> mergeRequest = new HttpEntity<>(mergeCustomerRequest,headers);
//		long startTime = System.currentTimeMillis();
//		ResponseEntity<List<MergeCustomerResponse>>  response = restTemplate.exchange(url,HttpMethod.POST,mergeRequest,new ParameterizedTypeReference<List<MergeCustomerResponse>>(){});
//		long endTime = System.currentTimeMillis();
//		LogUtil.logExecutionTime("mergeAnonymousCustomer", startTime, endTime);
//		if(response.getStatusCode().is2xxSuccessful()) {
//			return response.getBody();
//		} else {
//			log.info("merge anonymous customer failed: {}",mergeCustomerRequest);
//			return null;
//		}
//
//	}

	@Override
	public List<MergeCustomerResponse> anonymousContactMerge(MergeCustomerRequest mergeCustomerRequest) {
		String url = this.kontactoUrl + MERGE_CUSTOMER;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);

		String originalPhone2 = mergeCustomerRequest.getPhone2();
		String originalEmail2 = mergeCustomerRequest.getEmail2();

		// Case 1: Try with both phone2 and email2
		List<MergeCustomerResponse> response = attemptMerge(mergeCustomerRequest, url, headers);
		if (response != null) {
			return response;
		}

		// Case 2: Try with only phone2
		if(StringUtils.isNotBlank(originalPhone2)) {
			mergeCustomerRequest.setEmail2(null); // Set email2 to null
			mergeCustomerRequest.setPhone2(originalPhone2); // Restore phone2 just to be sure
			response = attemptMerge(mergeCustomerRequest, url, headers);
			if (response != null) {
				return response;
			}
		}

		// Case 3: Try with only email2
		if(StringUtils.isNotBlank(originalEmail2)) {
			mergeCustomerRequest.setPhone2(null); // Set phone2 to null
			mergeCustomerRequest.setEmail2(originalEmail2); // Restore email2
			response = attemptMerge(mergeCustomerRequest, url, headers);
			if (response != null) {
				return response;
			}
		}

		log.info("merge anonymous customer failed: {}", mergeCustomerRequest);
		return null;
	}

	private List<MergeCustomerResponse> attemptMerge(MergeCustomerRequest mergeCustomerRequest, String url, HttpHeaders headers) {
		HttpEntity<MergeCustomerRequest> mergeRequest = new HttpEntity<>(mergeCustomerRequest, headers);
		long startTime = System.currentTimeMillis();
		try {
			ResponseEntity<List<MergeCustomerResponse>> response = restTemplate.exchange(
					url, HttpMethod.POST, mergeRequest, new ParameterizedTypeReference<List<MergeCustomerResponse>>() {}
			);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("mergeAnonymousCustomer", startTime, endTime);

			if (response.getStatusCode().is2xxSuccessful()) {
				return response.getBody(); // Return the response body on success
			} else {
				log.info("merge anonymous customer failed: {}", mergeCustomerRequest);
			}
		} catch (Exception e) {
			log.error("Exception during merge anonymous customer call: {}", e.getMessage());
		}
		return null; 
	}
}
