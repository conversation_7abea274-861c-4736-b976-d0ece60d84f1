package com.birdeye.messenger.external.service;

import java.util.Collections;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.constant.MessengerConstants;
import com.birdeye.messenger.dto.AppointmentConfirmationRequestDto;
import com.birdeye.messenger.dto.AppointmentConfirmationResponseDto;
import com.birdeye.messenger.enums.Source;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.AppointmentApiResponseDto;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@Slf4j
public class AppointmentServiceImpl implements AppointmentService {

	@Autowired
	private RestTemplate restTemplate;

	@Value("${appointment.service.url}")
	private String appointmentUrl;

	@Override
	public AppointmentApiResponseDto getAppointmentDetails(Long appointmentId, Integer accountId) {
		String url = this.appointmentUrl + "/v2/appointments/appointment/" + appointmentId + "?enc=0";
		log.debug("Calling appointment service - getAppointmentDetails {}", url);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("accountId", String.valueOf(accountId));
		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getAppointmentDetails", startTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful()) {
			log.error("Error in fetching appointment details from appointment service {}", res);
			return null;
		}
		return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), AppointmentApiResponseDto.class);
	}

	@Override
	public AppointmentConfirmationResponseDto confirmAppointment(String altAppointmentId, String pmsAppointmentId, Integer businessId) {
		String url = this.appointmentUrl + "v2/appointments/" + altAppointmentId;
		log.debug("Calling appointment service - confirm appointment {}", altAppointmentId);
		AppointmentConfirmationRequestDto request = new AppointmentConfirmationRequestDto(null, MessengerConstants.SMS, "confirm", pmsAppointmentId, businessId);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<AppointmentConfirmationRequestDto> httpEntity = new HttpEntity<>(request,headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<AppointmentConfirmationResponseDto> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, AppointmentConfirmationResponseDto.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("confirmAppointment", startTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful()) {
			log.error("Error in confirming appointment for altAppointmentId {}", altAppointmentId);
			return null;
		}
		return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), AppointmentConfirmationResponseDto.class);
	}
}
