package com.birdeye.messenger.external.service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.exception.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.dto.apple.chat.AppleUploadAttachmentRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument.Channel;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.ext.sro.BizLite;
import com.birdeye.messenger.ext.sro.GetUsersByIdsRequest;
import com.birdeye.messenger.ext.sro.GetUsersByIdsResponse;
import com.birdeye.messenger.external.dto.BusinessFeaturesDto;
import com.birdeye.messenger.external.dto.BusinessOptionResponse;
import com.birdeye.messenger.external.dto.BusinessReceptionistResponse;
import com.birdeye.messenger.external.dto.UpdateUserTranscriptConfigRequest;
import com.birdeye.messenger.external.dto.UserMessengerNotificationSetting;
import com.birdeye.messenger.sro.BusinessTimingDTO;
import com.birdeye.messenger.sro.GetBusinessUser;
import com.birdeye.messenger.sro.GetTeamsRequest;
import com.birdeye.messenger.sro.TeamDTO;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class BusinessServiceImpl implements BusinessService {
    
    @Autowired
    @Lazy
    private BusinessService self;

    @Value("${core.service.url}")
    private String coreUrl;
    
    private String DOMAIN_ROUTE = "/v1/domain";
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Override
    public Integer isMessengerEnabled(Integer businessId) {
        BusinessOptionResponse boResponnse = self.getBusinessOptionsConfig(businessId, true);
        return boResponnse.getEnableMessenger();
    }
    
    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_CACHE, key="'BID-'.concat(#businessId)", unless="#result == null")
    public BusinessDTO getBusinessDTO(Integer businessId) {
        String url = this.coreUrl + "/v1/business/"+ businessId;
        return getBusiness(url);
    }

    /**
     *
     * @param businessId id of business table
     * @param includeClosed possible values only 0 and 1
     * @return businessDTO
     */
    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_CACHE, key="'BID-'.concat(#businessId)", unless="#result == null")
    public BusinessDTO getBusinessDTO(Integer businessId, Integer includeClosed) {
        includeClosed = Objects.nonNull(includeClosed) ? includeClosed : 0;
        String url = this.coreUrl + "/v1/business/"+ businessId + "?all=" + includeClosed;
       return getBusiness(url);
    }

    private BusinessDTO getBusiness(String url) {
        log.debug("Calling core service - getBusiness {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusiness", startTime, endTime);
        if(res.getStatusCode().is5xxServerError() || !res.getStatusCode().is2xxSuccessful()) {
            log.error("getBusinessDTO: response from core-service [ {} ] ", res);
            throw new BusinessException(ErrorCode.INTERNAL_SERVER_ERROR);
        }

        BusinessDTO businessDTO = ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessDTO.class);
        if (StringUtils.isAllEmpty(businessDTO.getTimeZoneId())) {
            log.info("Null TimeZoneId {}", businessDTO);
            businessDTO.setTimeZoneId(Constants.DEFAULT_TIMEZONE_ID);
        }
        return businessDTO;
    }


    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_CACHE, key="'BNO-'.concat(#businessNumber)", unless="#result == null")
    public BusinessDTO getBusinessByBusinessNumber(Long businessNumber) {
        String url = this.coreUrl + "/v1/business/" + businessNumber + "?isBizNumber=true";
        log.debug("Calling core service - getBusinessByBusinessNumber {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessByBusinessNumber", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching business data");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessDTO.class);
    }

    @Override
    //Platform Call -> Core API
    public Map<Integer, BusinessDTO> getBusinessDataForMessenger(UserLoginMessage userLoginMessage) {
        String url = this.coreUrl + "/v1/business/messenger-business-data";
        log.debug("calling core service to fetch business data: core service url {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        MessengerFilter filter=new MessengerFilter();
        List<Integer> businessIds=new ArrayList<>();
        businessIds.addAll(userLoginMessage.getBusinessIds());
        if(userLoginMessage.getBusinessId() != null)
        businessIds.add(userLoginMessage.getBusinessId());
        filter.setBusinessIds(businessIds);
        HttpEntity<Object> httpEntity = new HttpEntity<>(filter, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessDataForMessenger", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching business data");
            return null;
        }
        Map<Integer, BusinessDTO> result = ControllerUtil.jsonToMap(ControllerUtil.getJsonTextFromObject(res.getBody()), BusinessDTO.class);
        Map<Integer, BusinessDTO> filtered = result.entrySet().stream()
                .filter((entry) -> Objects.nonNull(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if(result.size() != filtered.size()) {
            log.info("getBusinessDataForMessenger: Filtered BusinessDTO list mismatched. Result size from core {} and filtered result {} for userId {}", result.size(), filtered.size(), userLoginMessage.getUserId());
        }
        return filtered;
    }

    @Override
    public Map<Integer, BusinessDTO> getBusinessDataForMessenger(List<Integer> businessIds, Integer userId, Integer accountId) {
        UserLoginMessage loggedInUser = new UserLoginMessage();
        loggedInUser.setBusinessIds(businessIds);
        loggedInUser.setBusinessId(accountId);
        loggedInUser.setUserId(userId);
        return getBusinessDataForMessenger(loggedInUser);
    }
    
    @Override
    public BusinessDTO getBusinessLiteDTO(Integer businessId) {
        String url = this.coreUrl + "/v1/business/getBusinessLite?key=businessId&value="+ businessId;
        return self.getBusinessLite(url);
    }

    @Override
    public BusinessDTO getBusinessLiteDTO(String key, String value) throws Exception {
        String url = this.coreUrl + "/v1/business/getBusinessLite?key="+key+"&value="+ URLEncoder.encode(value, "UTF-8");
        return self.getBusinessLite(url);
    }

    @Override
    public BusinessDTO getBusinessLiteDTOWithLocation(String key, String value) throws Exception {
        String url = this.coreUrl + "/v1/business/getBusinessLite?key="+key+"&value="+ URLEncoder.encode(value, "UTF-8")+"&location=true";
        return self.getBusinessLite(url);
    }
    
    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_LITE_CACHE, key="'URL-'.concat(#url)", unless="#result == null")
    public BusinessDTO getBusinessLite(String url) {
        log.debug("Calling core service - getBusinessLiteDTO {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessLite", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("getBusinessDTOLite : response from core-service [ {} ] for url [ {} ]", res, url);
            return null;
        }
        BusinessDTO businessDTO = ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessDTO.class);
        if (StringUtils.isAllEmpty(businessDTO.getTimeZoneId())) {
            log.info("Null TimeZoneId{}", businessDTO);
            businessDTO.setTimeZoneId(Constants.DEFAULT_TIMEZONE_ID);
        }
        return businessDTO;
    }


    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_CACHE, key="'SMS_ENABLED_BID-'.concat(#businessId)", unless="#result == null")
    public Map<Integer,BusinessDTO> getBusinessSMSEnabledLocations(Integer businessId) {
        String url = this.coreUrl + "/v1/business/dataForEnableSMS/" +businessId+ "?location=true";
        log.debug("calling core service to fetch sms enabled data: core service url {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessSMSEnabledLocations", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching smsEnabledLocations from core service");
            return null;
        }
        return ControllerUtil.jsonToMap(ControllerUtil.getJsonTextFromObject(res.getBody()), BusinessDTO.class);
    }
    
    @Override
    public List<TeamDTO> getTeams(Integer accountId, Integer locationId) {
        String url = this.coreUrl + "/v1/team/webchat/list";
        log.debug("calling core service to fetch teams for enterpriseId:{} core service url {}",accountId,url);
        GetTeamsRequest getTeamsRequest = new GetTeamsRequest();
        getTeamsRequest.setAccountId(accountId);
        if (locationId != null){
            getTeamsRequest.setLocationId(locationId);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(getTeamsRequest,headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getTeams", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching teams for enterprise");
            return null;
        }
        return ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), TeamDTO.class);
    }

    @Override
    public List<TeamDto> getTeamsByIds(List<Integer> teamIds) {
        String url = this.coreUrl + "/v1/team/get-teams";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(teamIds,headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<List<TeamDto>> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<TeamDto>>(){});
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getTeamsByIds", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching teams for teamIds from core service");
            return null;
        }
        return res.getBody();
    }

    @Override
    public UserMessengerNotificationSetting getUserNotificationSettings(Integer businessId, Integer userId) {
        Assert.notNull(businessId, "businessId cannot be null");
        Assert.notNull(userId, "userId cannot be null");
        log.debug("Received request for fetching messsenger notification settings for businessId:{}, userId:{}", businessId, userId);
        String url = this.coreUrl + "/v1/messenger/config/"+businessId+"/"+userId;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<UserMessengerNotificationSetting> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, UserMessengerNotificationSetting.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getUserNotificationSettings", starTime, endTime);
        if(!response.getStatusCode().is2xxSuccessful()) {
            log.error("Some error occured while fetching user notification settings for businessId:{}, userId:{}", businessId, userId);
            return new UserMessengerNotificationSetting();
        }
        return response.getBody();
    }

    @Override
    public Map<String, Map<String, Map<String, String>>> getEmailConfigOfTeamsForNewMessage(Integer businessId, Integer teamId, String type, Integer source) {
        try {
//            String fullUrl = coreUrl + "/v1/messenger/team-email-config/" + businessId + "/" + teamId
//                    + "?notificationType=" + URLEncoder.encode(type, "UTF-8")
//                    + "&channelId=" + source;
            StringBuilder fullUrl = new StringBuilder(coreUrl + "/v1/messenger/team-email-config/v2/" + businessId + "/" + teamId);
            List<String> params = new ArrayList<>();
            if (type != null) {
                params.add("notificationType=" + URLEncoder.encode(type, "UTF-8"));
            }
            if (source != null) {
                params.add("channelId=" + source);
            }
            if (!params.isEmpty()) {
                fullUrl.append("?").append(String.join("&", params));
            }
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<Map> responseEntity = restTemplate.exchange(fullUrl.toString(), HttpMethod.GET, httpEntity, Map.class);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                return responseEntity.getBody();
            } else {
                log.info("getBusinessUserWebChatTime: could not get valid webChat interval response from core for businessId {} ", businessId);
            }
        } catch (Exception e) {
            log.error("Error building URL or calling service", e);
        }
        return null;
    }

    @Override
    public UserIdEmailIdDTO getEmailConfigOfUsersForTeamsAssignment(Integer teamId,Integer businessId) {
        String fullUrl = coreUrl + "/v1/messenger/team-email-config-assignee/" + businessId + "/" + teamId;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<UserIdEmailIdDTO> responseEntity = restTemplate.exchange(fullUrl, HttpMethod.GET, httpEntity, UserIdEmailIdDTO.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getEmailConfigOfUsersForTeamsAssignment", starTime, endTime);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        } else {
            log.info(
                "getBusinessUserWebChatTime: could not get valid webChat interval response from core for teamId {} ",teamId);
        }
        return null;
    }

    @Override
    public List<Integer> getAllUsersOfTeams(Integer teamId,Integer businessId) {
        String fullUrl = coreUrl + "/v1/team/get-users/" + teamId + "/" + businessId;

        HttpHeaders headers = new HttpHeaders();
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<List<Integer>> responseEntity = restTemplate.exchange(fullUrl, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<List<Integer>>(){});
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getAllUsersOfTeams", startTime, endTime);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        } else {
            log.info(
                "getAllUsersOfTeams: could not get valid response from core for teamId {} ",teamId);
        }
        return null;
    }

    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_OPTIONS, key="'BO-'.concat(#businessId)", unless="#result == null")
    public BusinessOptionResponse getBusinessOptionsConfig(Integer businessId, Boolean traverseHierarchy) {
        String url = this.coreUrl + "/v1/business/businessoptions?businessId="+businessId+"&traversehierarchy="+traverseHierarchy.toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessOptionsConfig", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
                    "Error in response from Business api getBusinessOptionsConfig for businessId: " + businessId);
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()), BusinessOptionResponse.class);
    }

    @Override
    public BusinessReceptionistResponse getDataForReceptionist(Integer businessId) {
        String url = this.coreUrl + "/v1/business/dataForReceptionist/"+businessId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getDataForReceptionist", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("Error in fetching getDataForReceptionist data from core service");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessReceptionistResponse.class);
    }

    @Override
    public BusinessDTO getBusinessInfoByPhoneNumber(InfoByPhoneNumberRequest request) {
        String url = this.coreUrl + "/v1/business/infoByPhoneNumber?location=true";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(request, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessInfoByPhoneNumber", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("Error in fetching getBusinessInfoByPhoneNumber data from core service");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessDTO.class);
    }

    @Override
    public void removeUserTeamsMapping(UserEvent userEvent) {
        String url = this.coreUrl + "/v1/business/event/messenger-access-revoked";
        log.debug("calling core service to remove user's teams mapping: core service url {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(userEvent, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("removeUserTeamsMapping", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error(" Error in calling core service to remove user's teams mapping: core service url {}",url);
            return;
        }
    }

    @Override
    public UserResponse getBusinessUsers(Integer accountId, GetBusinessUser getBusinessUser) {
        String url = this.coreUrl + "/v1/user/all";
        url = url+"?accountId="+accountId+"&all=true&excludeLoggedInUSer=true&sortBy=name";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(getBusinessUser,headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<UserResponse> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, UserResponse.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessUsers", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("Get business User : response from core-service [ {} ] ", res);
            return null;
            /*throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
                    "Error in response from Business api getBusinessDTO for businessId: " + businessId);*/
        }
        return res.getBody();
    }
    
    @Override
    public Map<String,Integer>  getUserIds(GetUserIdsRequest request) {
        Integer accountId=request.getAccountId();
        String url = this.coreUrl + "/v1/user/fetchUserIds/"+accountId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(request, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<Map> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Map.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getUserIds", starTime, endTime);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return responseEntity.getBody();
        } else {
            log.error("error in feting userids for reuest: {} ",request);
        }
        return null;
    }

    
    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_LITE_CACHE, key="#accountId", unless="#result == null")
    public List<BusinessLite> getBusinessLiteObjects(Integer accountId) {
        String url = this.coreUrl + "/v1/business/locations/"+accountId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessLiteObjects", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching business lite objects for accountId: {} from core service",accountId);
            return null;
        }
        return ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), BusinessLite.class);
    }

    @Override
    public EmailSenderDto getSenderEmailInfo(Integer businessId) {
        String url = this.coreUrl + StringUtils.join("/v1/email/", businessId, "/emailSenderInfo");
        log.debug("calling core service to get emailSenderInfo : core service url {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<EmailSenderDto> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity,
                EmailSenderDto.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getSenderEmailInfo", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("Get emailSenderInfo : response from core-service [ {} ] ", res);
            return null;
        }
        return res.getBody();
    }

    @Override
    public ChatTranscriptBusinessUserConfig getChatTranscriptBusinessUserConfig(Integer businessId) {
        String url = coreUrl + "/v1/business/user/transcript-config/" + businessId + "?accountId=" + businessId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<ChatTranscriptBusinessUserConfig> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, ChatTranscriptBusinessUserConfig.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getChatTranscriptBusinessUserConfig", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("GetChatTranscriptBusinessUserConfig : response from core-service [ {} ] ", res);
            return null;
        }
        return res.getBody();
    }

    @Override
    public List<String> getSupportedCountryCodes(Integer businessId) {
        String url = this.coreUrl + "/v1/business/supportedCountry/"+businessId;
        log.debug("calling core service to fetch supported country codes: core service url {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<List> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, List.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getSupportedCountryCodes", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching supported country codes from core service");
            return null;
        }

        return res.getBody();

    }

    @Override
    public BusinessHoursDTO getBusinessHours(Integer businessId) {
        String url = coreUrl + "/v1/business/timings/" + businessId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<BusinessHoursDTO> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, BusinessHoursDTO.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessHours", starTime, endTime);
        if (Objects.isNull(res) || !res.getStatusCode().is2xxSuccessful() || Objects.isNull(res.getBody())) {
            log.error("getBusinessHours : error in fetching business hours from core-service [ {} ] ", res);
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
        return res.getBody();
    }
    
    @Override
    public List<GetUsersByIdsResponse.User> getUsersByIds(List<Integer> userIds) {
        if(CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        GetUsersByIdsRequest request = new GetUsersByIdsRequest();
        request.setUserIds(userIds);
        String url = this.coreUrl + "/v1/user/fetchUsers";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(request, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<GetUsersByIdsResponse> responseEntity  = restTemplate.exchange(url, HttpMethod.POST, httpEntity, GetUsersByIdsResponse.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getUsersByIds", starTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful() && CollectionUtils.isNotEmpty(responseEntity.getBody().getUsers())) {
            return responseEntity.getBody().getUsers();
        } else {
            log.error("error in fetching userids for request: {} ",request);
            throw new MessengerException(ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }


    @Override
    //check data change pattern
    public BusinessTimingDTO getBusinessTimings(Integer businessId,boolean formatRequired) {
//        String url = this.coreUrl + "/v1/business/timings/"+businessId+"?formatRequired="+formatRequired;
        String url = this.coreUrl + "/v1/business/data-timings/"+businessId+"?formatRequired="+formatRequired;
        log.debug("calling core service getBusinessTimings: {}",url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<BusinessTimingDTO> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, BusinessTimingDTO.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessTimings", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching timings for business from core service");
            return null;
        }
        log.info("business timing dto from core {}",res.getBody());
        return res.getBody();

    }
    
    @Override
    public Map<Integer, BizLite> getBizLite(List<Integer> businessIds, Integer accountId) {
        Map<Integer, BizLite>  response=new HashMap<Integer, BizLite>();
        if(CollectionUtils.isEmpty(businessIds)) {
            return response;
        }
//    	Object objectFromCache=redisHandler.getKeyValueFromRedis(Constants.BIZ_LITE_CACHE+":"+accountId);
//    	if(objectFromCache!=null) {
//    		return ControllerUtil.jsonToMap(ControllerUtil.getJsonTextFromObject(objectFromCache), BizLite.class);
//    	}
        String url = this.coreUrl + "/v1/business/biz-lite";
        if(null != accountId) {
            log.debug("calling core service to fetch {} bizLites for accountId: {}", businessIds.size(), accountId);
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(businessIds,headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<List<BizLite>> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<List<BizLite>>(){});
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBizLite", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching bizLites for businesses:{} from core service",businessIds);
            return response;
        }
        List<BizLite> bizLites = res.getBody();
        if (CollectionUtils.isNotEmpty(bizLites)) {
            response = bizLites.stream().collect(Collectors.toMap(BizLite::getId, v -> v));
            return response;
        }
        return response;
        // List<BizLite> bizList = res.getBody();
//        if(CollectionUtils.isNotEmpty(bizList)) {
//        	response= bizList.stream().collect(Collectors.toMap(BizLite::getId, v->v));
//        	redisHandler.setOpsForValueWithExpiry(Constants.BIZ_LITE_CACHE+":"+accountId, response, 6l, TimeUnit.HOURS);
//        }
//        return response;
    }

    @Override
    public Integer validateMessengerEnabled(Integer businessId, boolean throwException) {
        Integer messengerEnabled = self.isMessengerEnabled(businessId);
        if(throwException && (messengerEnabled==null || messengerEnabled==0)) {
            log.info("Messenger is disabled for businessId :{}",businessId);
            return null;
        }
        return messengerEnabled;
    }

    @Override
    public boolean getMessengerEnabled(Integer businessId){
        Integer messengerEnabled = self.isMessengerEnabled(businessId);
        if (messengerEnabled != null && messengerEnabled == 1) {
            return true;
        }
        return false;
    }

    @Override
    public Map<Integer, String> uploadAppleImagesInCDN(List<AppleUploadAttachmentRequest> appleUploadAttachmentRequests,
                                                       Integer businessId, Channel apple) {
        String url = this.coreUrl + "/v1/image/apple/upload/consumer?businessId=" + businessId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(appleUploadAttachmentRequests, headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<Map<Integer, String>> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity,
                new ParameterizedTypeReference<Map<Integer, String>>() {
                });
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("uploadAppleImagesInCDN", starTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("apple images uploaded to cdn :{},{}", url, responseEntity.getBody());
            return responseEntity.getBody();
        } else {
            log.error("error in uploading apple images to cdn: {} ", appleUploadAttachmentRequests);
        }
        return null;
    }

    @Override
    public Map<String, Object> getBusinessData(Integer businessId) {
        String url = this.coreUrl + "/v1/business/getData/" + businessId;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity,
                new ParameterizedTypeReference<Map<String, Object>>() {
                });
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessData", startTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            log.debug("business data fetched :{},{}", url, responseEntity.getBody());
            return responseEntity.getBody();
        } else {
            log.error("error in fetching business data for id: {} ", businessId);
            throw new MessengerException("Error fetching business data for id " + businessId);
        }
    }

    @Override
    public AppUserNotificationSetting getAppUsersNotificationSettings(Integer businessId) {
        String url = this.coreUrl + "/v1/mobile/user/details/" + businessId + "?module=inbox";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<AppUserNotificationSetting> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity,
                AppUserNotificationSetting.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getAppUsersNotificationSettings", startTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("Error in fetching appUserNotificationSetting data from core service for businessId {}",businessId);
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),
                AppUserNotificationSetting.class);

    }
    @Override
    public List<Integer> getBusinessSmsNumberByIds(List<Integer> businessIds) {
        UserLoginMessage loggedInUser = new UserLoginMessage();
        loggedInUser.setBusinessIds(businessIds);
        Map<Integer, BusinessDTO> result = getBusinessDataForMessenger(loggedInUser);
        List<BusinessDTO> businessDTOList = new ArrayList<>(result.values());
        List<Integer> businessIdList = businessDTOList.stream()
                .filter(businessDTO -> Objects.nonNull(businessDTO.getSmsPhoneNumber()) && !businessDTO.getSmsPhoneNumber().isEmpty())
                .map(BusinessDTO::getBusinessId)
                .collect(Collectors.toList());
        return businessIdList;
    }

    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_SMS_CACHE, key="#businessId", unless="#result == null")
    public String getBusinessSmsNumberById(Integer businessId) {
        BusinessReceptionistResponse phoneNumbers = getDataForReceptionist(businessId);
        if (StringUtils.isNotBlank(phoneNumbers.getData().get(0).getSmsPhoneNumber())) {
            return phoneNumbers.getData().get(0).getSmsPhoneNumber();
        }
        return null;
    }
    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_LITE_CACHE, key="'SMS_NUMBER-'.concat(#businessId)", unless="#result == null")
    public String getBusinessSMSNumber(Integer businessId) {
        String url = this.coreUrl + "/business/sms/smsNumber/" + businessId;
        log.debug("calling core service to getBusinessSMSNumber for url: {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity,
                String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessSMSNumber", startTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("Error in fetching getBusinessSMSNumber from core service for businessId {}",businessId);
            return null;
        }
        return res.getBody();
    }


    @Override
    public void updateUserTranscriptConfig(ChatTranscriptRequest chatTranscriptRequest) {
        log.debug("Update user Transcript config for request : {}",chatTranscriptRequest);
        String url = this.coreUrl + "/v1/business/user/update-transcript-config";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        UpdateUserTranscriptConfigRequest updateUserTranscriptConfigRequest = convertChatTranscriptRequest(chatTranscriptRequest);
        HttpEntity httpEntity = new HttpEntity<>(updateUserTranscriptConfigRequest,headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<Object> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Object.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("updateUserTranscriptConfig", startTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("Error in updating updateUserTranscriptConfig  for accountId {}",chatTranscriptRequest.getAccountId());
            throw new MessengerException(res.getBody().toString());
        }
    }

    private UpdateUserTranscriptConfigRequest convertChatTranscriptRequest(ChatTranscriptRequest chatTranscriptRequest){
        UpdateUserTranscriptConfigRequest updateUserTranscriptConfigRequest = new UpdateUserTranscriptConfigRequest();
        updateUserTranscriptConfigRequest.setAccountId(chatTranscriptRequest.getAccountId());
        List<UpdateUserTranscriptConfigRequest.UserTranscriptConfig> configs = new ArrayList<>();
        for (ChatTranscriptRequest.UserTranscriptConfig userTranscriptConfig : chatTranscriptRequest.getUserTranscriptConfigs()) {
            UpdateUserTranscriptConfigRequest.UserTranscriptConfig config = new UpdateUserTranscriptConfigRequest.UserTranscriptConfig();
            config.setEnableTranscript(true);
            config.setTranscriptType(userTranscriptConfig.getTranscriptType());
            config.setUserId(userTranscriptConfig.getUserId());
            configs.add(config);
        }
        updateUserTranscriptConfigRequest.setUserTranscriptConfigs(configs);
        return updateUserTranscriptConfigRequest;
    }
    
    @Override
    @Cacheable(cacheNames = Constants.WEBSITE_DOMAIN_CACHE, key="#businessId", unless="#result == null")
    public String getWebsiteDomain(Integer businessId) {
        // TODO- This data is mostly static and we should cache it at out end.
        String fullUrl = coreUrl + DOMAIN_ROUTE + "/" + businessId;
        String domain = null;
        ResponseEntity<DomainMessage> responseEntity = null;
        try {
            HttpHeaders headers = new HttpHeaders();
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            long startTime = System.currentTimeMillis();
            responseEntity = restTemplate.exchange(fullUrl, HttpMethod.GET, httpEntity, DomainMessage.class);
            long endTime = System.currentTimeMillis();
            LogUtil.logExecutionTime("getWebsiteDomain", startTime, endTime);
        } catch (Exception e) {
            log.info("Website Domain Fetch call failed" + e);
        }
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            domain = responseEntity.getBody().getDomain();
            if (responseEntity.getBody().getSecureEnabled() == 1) {
                domain = Constants.SECURE_PROTOCOL + domain;
            } else {
                domain = Constants.NON_SECURE_PROTOCOL + domain;
            }
        } else {
            domain = Constants.DEFAULT_URL;
        }
        return domain;
    }
    
    @Override
    public PublicDataBusinessDTO getPublicBusinessData(Integer businessId) {
        String url = this.coreUrl + "/v1/public/business/getData?callFromPresence=false";
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("business-id", String.valueOf(businessId));
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getPublicBusinessData", startTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            log.debug("Public business data fetched :{},{}", url, responseEntity.getBody());
            return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(responseEntity.getBody()),PublicDataBusinessDTO.class);
        } else {
            log.error("error in fetching public business data for id: {} ", businessId);
            throw new MessengerException("Error fetching public business data for id " + businessId);
        }
    }

    @Override
    public BusinessDTO validateWidgetApiKey(String accountId,String apiKey,boolean isSmall){
        String url = this.coreUrl + "/v1/partner/getBusinessIfValid?apiKey="+apiKey+"&businessId="+accountId+"&isSmallBusinessId="+isSmall;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<BusinessDTO> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, BusinessDTO.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("validateWidgetApiKey", startTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(responseEntity.getBody()),BusinessDTO.class);
        } else {
            log.error("widget api key {} is not valid for businessId {} ", apiKey,accountId);
            throw new MessengerException(ErrorCode.INVALID_API_KEY);
        }    
    }

    @Override
    public boolean validateWidgetApiKey(Integer accountId, String widgetApiKey){
        String url = this.coreUrl + "/v1/business/widget-key/validate";
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-account-id", accountId.toString());
        headers.set("x-widget-api-key", widgetApiKey);
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            String validated = responseEntity.getBody();
            return Boolean.valueOf(validated);
        }
        return false;
    }
    
    @Override
    public TeamDTO getTeamByTeamName(Integer accountId, String teamName){
        String url = this.coreUrl + "/v1/team/list?all=true&pageSize=25&sortOrder=1&startIndex=0";
        TeamDetailsByNameRequestDto request = new TeamDetailsByNameRequestDto(accountId,teamName);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity httpEntity = new HttpEntity<>(request,headers);
        long startTime = System.currentTimeMillis();
        ResponseEntity<TeamDetailsByNameResponseDto> responseEntity = restTemplate.exchange(url, HttpMethod.POST, httpEntity,TeamDetailsByNameResponseDto.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getTeamByTeamName", startTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful() && Objects.nonNull(responseEntity.getBody()) && CollectionUtils.isNotEmpty(responseEntity.getBody().getTeams())) {
            log.debug("team Dto for request:{} is : {}", request,responseEntity.getBody().getTeams());
            return responseEntity.getBody().getTeams().get(0);
        } else {
            log.error("no team found for request:{} ", request);
            throw new InputValidationException(ErrorCode.INVALID_TEAM_DETAILS);
        }
    }

    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_CUSTOM_TOKEN_CACHE, key="'BCF-'.concat(#businessId)", unless="#result == null")
    public BusinessLocationCustomFieldsTokensDto getBusinessCustomFieldsAndTokenByBusinessID(Integer businessId) {
        String url = this.coreUrl + "/api/v1/custom-fields/business-tokens/" + businessId;
        log.debug("Calling core service - getBusinessCustomFieldsAndTokenByBusinessID {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessCustomFieldsAndTokenByBusinessID", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching BusinessCustomFieldsAndTokenByBusinessID data from core service");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessLocationCustomFieldsTokensDto.class);
    }

    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_PROFILE_CACHE, key="'BCF-'.concat(#businessId)", unless="#result == null")
    public BusinessProfileData getBusinessProfileData(Integer businessId){
        String url = this.coreUrl + "/v1/business/get/profile/" + businessId;
        log.debug("Calling core service - getBusinessProfileDataForBusinessID {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessProfileDataByBusinessID", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching BusinessProfileDataByBusinessID data from core service");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessProfileData.class);
    }

    @Override
    @Cacheable(cacheNames = Constants.BUSINESS_DETAILS_CACHE, key="'BNO-'.concat(#businessNumber)", unless="#result == null")
    public BusinessDetails getBusinessDetailsByBusinessNumber(Long businessNumber) {
        String url = this.coreUrl + "/v1/business/businessDetails/" + businessNumber;
        log.debug("Calling core service - getBusinessDetailsByBusinessNumber {}", url);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
        long starTime = System.currentTimeMillis();
        ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        long endTime = System.currentTimeMillis();
        LogUtil.logExecutionTime("getBusinessByBusinessNumber", starTime, endTime);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.info("error in fetching business data from core service");
            return null;
        }
        return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),BusinessDetails.class);
    }
    
    @Override
    public boolean validateOldWidgetApiKey(Integer accountId, String widgetApiKey){
        String url = this.coreUrl + "/v1/business/widget-business-id";
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-account-id", accountId.toString());
        headers.set("x-old-api-key", widgetApiKey);
        HttpEntity<?> httpEntity = new HttpEntity<>(headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            String businessId = responseEntity.getBody();
            return businessId.equals(String.valueOf(accountId));
        }
        return false;
    }

	@Override
	@Cacheable(cacheNames = Constants.BUSINESS_FEATURES, key = "'BF-'.concat(#accountId)", unless = "#result == null")
	public BusinessFeaturesDto getBusinessFeaturesByAccountId(Integer accountId) {
		String url = coreUrl + "/v1/business/features?traverseHierarchy=false";

		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("account-id", accountId.toString());

		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long startTime = System.currentTimeMillis();

		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getBusinessFeaturesByAccountId", startTime, endTime);

		if (!response.getStatusCode().is2xxSuccessful()) {
			log.error("Failed to get business features for accountId: " + accountId);
		}
		return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(response.getBody()),
				BusinessFeaturesDto.class);
	}
}
