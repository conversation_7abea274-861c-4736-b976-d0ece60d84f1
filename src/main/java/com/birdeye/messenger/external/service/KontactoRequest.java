package com.birdeye.messenger.external.service;

import java.util.Map;

import org.springframework.util.StringUtils;

import com.birdeye.messenger.dto.payment.PaymentCustomerContextRequest;
import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Customer request object to be used for interacting with Contact Service.
 * 
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class KontactoRequest {
	private String name;
	@SecureString
	private String emailId;
	@SecureString
	private String phone;
	private Integer businessId;
	private String source;
	private Integer smsEnabled;
	private LocationInfo location;
	private Integer routingId;

	// Kontacto source constants.
	public static final String WEBCHAT = "webchat";
	public static final String FACEBOOK = "facebook";
	public static final String INSTAGRAM = "instagram";
	public static final String DASHBOARD = "dashboard";
	public static final String OTHER = "other";
	public static final String TEXT = "text";
	public static final String GOOGLE = "google";
	public static final String APPLE = "apple";
	public static final String EMAIL = "email";
	public static final String VOICECALL = "voicecall";
	public static final String CONTACT_US = "contactus";
	public static final String TWITTER = "twitter";
	public static final String WHATSAPP = "whatsapp";
	public static final String API ="api";

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class LocationInfo {
		private String countryCode;
	}
	private Map<String,String> additionalParams;
	
	public KontactoRequest(PaymentCustomerContextRequest request) {
		this.name = request.getCustomerName();
		this.emailId = request.getCustomerEmail();
		this.phone =  request.getCustomerPhone();
		this.businessId =  request.getBusinessId();
		this.additionalParams = request.getAdditionalParams();
		if(StringUtils.hasText(request.getCountryCode())){
			this.location = LocationInfo.builder().countryCode(request.getCountryCode()).build(); 
		}
	}
	
	private Map<String,String> urlParams;

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}
}
