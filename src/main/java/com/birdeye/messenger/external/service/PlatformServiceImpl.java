package com.birdeye.messenger.external.service;

import java.util.Collections;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.Base64FileMessage;
import com.birdeye.messenger.exception.MessengerException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PlatformServiceImpl implements PlatformService {

	private final RestTemplate restTemplate;
	
	@Value("${platform.service.url}")
    private String platformUrl;
	
	@Override
	public String uploadBase64FileForBusiness(Integer businessId, boolean sync, Base64FileMessage base64Input) {
	    String url = String.format("%s/v1/%d/file/base64/upload?sync=%b", platformUrl, businessId, sync);

	    try {
	        HttpHeaders headers = new HttpHeaders();
	        headers.setContentType(MediaType.APPLICATION_JSON);
	        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

	        HttpEntity<Base64FileMessage> entity = new HttpEntity<>(base64Input, headers);

	        log.info("Calling file upload API for businessId: {}", businessId);

	        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
	        
			if (responseEntity.getStatusCode().is2xxSuccessful()) {
				return responseEntity.getBody();
			} else {
				log.error("Media upload request failed with status: {}", responseEntity.getStatusCode());
				throw new MessengerException("Media upload rest call failed");
			}
	    } catch (Exception e) {
	        log.error("Error uploading base64 file for businessId: {}", businessId, e);
	        throw new RuntimeException("Media upload request failed", e);
	    }
	}
	
}
