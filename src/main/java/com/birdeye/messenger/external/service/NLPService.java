package com.birdeye.messenger.external.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.birdeye.messenger.dao.entity.GPTResponseFeedbackAudit;
import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.BusinessProfileData;
import com.birdeye.messenger.dto.CheckSpamRequest;
import com.birdeye.messenger.dto.LivechatAnonymousConversationDataDto;
import com.birdeye.messenger.dto.PhishingDetectionRequest;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.dto.leadgenagent.GetPromptBasedReplyRequestDTO;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.service.ChatbotService.QueryChannel;
import com.birdeye.messenger.sro.BusinessTimingDTO;

public interface NLPService {

	public boolean isTextProfane(String text);

	public boolean isTextSpam(String text);
	
	public boolean checkSpamForOuboundMessage(CheckSpamRequest checkSpamRequest);
	
	public boolean isTextSpamOrProfane(String text);
	
	public Optional<ChatbotQueryResponse> getGPTPoweredRobinReply(Integer accountId,BusinessDTO businessDto,String queryText,
																  QueryChannel queryChannel,Integer customerId,List<Map<String, String>> chatHistory,GPTResponseFeedbackAudit audit,Boolean isReceivedDuringBusinessHour,BusinessTimingDTO businessTiming,BusinessProfileData businessProfileData,LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto);

	Map<String, String> buildConversationListFromMessageDocument(MessageDocument messageDocument,
			Map<String, String> robinResponsesPlainTextMap);

	public Map<String, String> detectPhishingContentForOuboundMessage(PhishingDetectionRequest phishingDetectionReq);

	public String getAIReplyUsingPrompt(GetPromptBasedReplyRequestDTO request);
}

