package com.birdeye.messenger.external.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.birdeye.messenger.dto.BusinessDTO;
import com.birdeye.messenger.dto.FeedbackRequest;
import com.birdeye.messenger.dto.LivechatAnonymousConversationDataDto;
import com.birdeye.messenger.dto.RobinPlainTextRequest;
import com.birdeye.messenger.external.dto.ChatbotQueryResponse;
import com.birdeye.messenger.external.dto.Suggestion;

public interface ChatbotService {

	public static final String HDR_BUSINESS_ID = "x-birdeye-business-id";
	public static final String HDR_ACCT_ID = "x-birdeye-account-id";

	// Input queries via channel
	public static enum QueryChannel {
		LIVECHAT, LIVECHAT_BIRDEYE_PROFILE, SMS, FACEBOOK, VOICE_CALL, INSTAGRAM, GOOGLE, TWITTER, EMAIL, CONTACT_US, APPLE, WHATSAPP
	}

	Optional<ChatbotQueryResponse> getQueryResponse(Integer businessId, Integer accountId, String queryText,
			QueryChannel queryChannel, Integer customerId, Integer mcId, Boolean isReceivedDuringBusinessHour,LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto, Integer widgetAccountId);

	void updateFeedback(FeedbackRequest feedbackRequest, Integer accountId);

	Optional<ChatbotQueryResponse> getSuggestionResponse(Integer accountId, Integer businessId, Suggestion suggestion, QueryChannel queryChannel, Integer customerId);

	Integer auditChatBotQuery(Suggestion suggestion, Integer accountId, Integer businessId, QueryChannel queryChannel, String intentType);

    Map<String, String> getRobinPlainTextBodyForRichContentChat(List<RobinPlainTextRequest> robinPlainTextRequests,
            Integer accountId, Integer businessId);

	List<Map<String, String>> getHistoryMessageForAI(Integer businessId, Integer accountId, Integer customerId,
			Integer mcId);

	String getAIReplyUsingKnowledgeBase(Integer businessId, Integer accountId, String queryText, QueryChannel queryChannel, Integer customerId, Integer mcId, Boolean isReceivedDuringBusinessHour, LivechatAnonymousConversationDataDto livechatAnonymousConversationDataDto) ;

	
}
