package com.birdeye.messenger.external.service;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.birdeye.messenger.dto.*;
import com.birdeye.messenger.dto.elastic.MessageDocument;
import com.birdeye.messenger.enums.Source;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.annotations.AuditRestCall;
import com.birdeye.messenger.constant.Constants;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.BusinessException;
import com.birdeye.messenger.external.dto.UserMessengerNotificationSetting;
import com.birdeye.messenger.util.ControllerUtil;
import com.birdeye.messenger.util.LogUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RequiredArgsConstructor
@Service
@Slf4j
public class UserServiceImpl implements UserService {

	@Value("${core.service.url}")
    private String coreUrl;

	private String USER_ROUTE = "/v1/user";
	
    private final RestTemplate restTemplate;

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, List<String>> getBusinessUserWebChatTime(Integer businessId) {

        Map<String, List<String>> webChatIntervalToEmailList = new HashMap<>();
        String fullUrl = coreUrl + USER_ROUTE + "/webChat-interval/" + businessId;
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long starTime = System.currentTimeMillis();
        ResponseEntity<Map> responseEntity = restTemplate.exchange(fullUrl, HttpMethod.GET, httpEntity, Map.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getBusinessUserWebChatTime", starTime, endTime);
        if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
            Map<String, String> emailIdToWebChatInterval = responseEntity.getBody();
            if (MapUtils.isNotEmpty(emailIdToWebChatInterval)) {
                emailIdToWebChatInterval.forEach((emailId, webChatInterval) -> {
                    if (MapUtils.isEmpty(webChatIntervalToEmailList) || !webChatIntervalToEmailList.containsKey(webChatInterval)) {
                        List<String> emailList = new ArrayList<>();
                        emailList.add(emailId);
                        webChatIntervalToEmailList.put(webChatInterval, emailList);
                    } else {
                        webChatIntervalToEmailList.get(webChatInterval).add(emailId);
                    }
                });
            }
        }
        else {
            //throw new MessengerException("[getBusinessUserWebChatTime] Could not get a valid response from Core-service url" + fullUrl);
            log.info("getBusinessUserWebChatTime: could not get valid webChat interval response from core for businessId {} ", businessId);
        }
        return webChatIntervalToEmailList;
    }
	@Override
	public Map<String, Map<String,Map<String,Map<String,String>>>> getInboxUserEmailAlertSettings(Integer businessId, String type, Integer source) {
        try{
//		String fullUrl = coreUrl + "/v1/messenger/notification-config/" + businessId  + "?notificationType=" + URLEncoder.encode(type, "UTF-8")
//				+ "&channelId=" + source;
		StringBuilder fullUrl = new StringBuilder(coreUrl + "/v1/messenger/notification-config/v2/" + businessId);
		List<String> params = new ArrayList<>();
		if (type != null) {
			params.add("notificationType=" + URLEncoder.encode(type, "UTF-8"));
		}
		if (source != null) {
			params.add("channelId=" + source);
		}
		if (!params.isEmpty()) {
			fullUrl.append("?").append(String.join("&", params));
		}
//		String fullUrl = coreUrl + "/v1/messenger/notification-config/" + businessId;
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long startTime = System.currentTimeMillis();
		ResponseEntity<Map> responseEntity = restTemplate.exchange(fullUrl.toString(), HttpMethod.GET, httpEntity, Map.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getInboxUserEmailAlertSettings", startTime, endTime);

		if (responseEntity != null && responseEntity.getStatusCode().is2xxSuccessful()) {
			return responseEntity.getBody();
		} else {
			// throw new MessengerException("[getBusinessUserWebChatTime] Could not get a
			// valid response from Core-service url" + fullUrl);
			log.info(
					"getBusinessUserWebChatTime: could not get valid webChat interval response from core for businessId {} ",
					businessId);
		}
		} catch (Exception e) {
			log.error("Error building URL or calling service", e);
		}
		return null;
	}
    
    @Override
    public UserDTO getUserDTO(Integer userId) {
        if(Objects.nonNull(userId)) {
            String url = this.coreUrl + "/v1/user/"+userId;
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            headers.setContentType(MediaType.APPLICATION_JSON);
			HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
			long startTime = System.currentTimeMillis();
            ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
			long endTime = System.currentTimeMillis();
			LogUtil.logExecutionTime("getUserDTO", startTime, endTime);
			if (!res.getStatusCode().is2xxSuccessful())
                throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
                        "Error response from getUserDTO for businessUserId" + userId);
            return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),UserDTO.class);
        }
        return null;
    }
    
    @Override
    public UserMessengerNotificationSetting getUserNotificationSettings(Integer businessId, Integer userId) {
    	Assert.notNull(businessId, "businessId cannot be null");
    	Assert.notNull(userId, "userId cannot be null");
    	log.info("Received request for fetching messsenger notification settings for businessId:{}, userId:{}", businessId, userId);
    	String url = this.coreUrl + "/v1/messenger/config/"+businessId+"/"+userId;
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long starTime = System.currentTimeMillis();
    	ResponseEntity<UserMessengerNotificationSetting> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, UserMessengerNotificationSetting.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getUserNotificationSettings", starTime, endTime);
		if(!response.getStatusCode().is2xxSuccessful()) {
    		log.error("Some error occured while fetching user notification settings for businessId:{}, userId:{}", businessId, userId);
    		return new UserMessengerNotificationSetting();
    	}
    	return response.getBody();
    }

    @Override
    public HashMap<String, ArrayList<String>> getEmailSendTimeForNonInboxUsers(Integer businessId) {
        Assert.notNull(businessId, "businessId cannot be null");
        log.info("Getting email send time from business-service for non inbox lead {} ", businessId);
        String url = coreUrl + "/v1/messenger/user-review-email-settings/" + businessId;
		HttpHeaders headers = new HttpHeaders();
		HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
		long starTime = System.currentTimeMillis();
		ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getEmailSendTimeForNonInboxUsers", starTime, endTime);
		if (response.getStatusCode().is2xxSuccessful()) {
            String responseString = response.getBody();
            TypeReference<HashMap<String, ArrayList<String>>> typeReference = new TypeReference<HashMap<String, ArrayList<String>>>() {};
            ObjectMapper mapper = new ObjectMapper();
            try {
                return mapper.readValue(responseString, typeReference);
            } catch (IOException e) {
                log.error("getEmailSendTimeForNonInboxUsers: error in mapping data from response string to Map businessId " + businessId, e);
            }

        }
        return null;
    }
    
	@Override
	public Map<Integer, List<Integer>> getValidUserDTOs(Integer userId, Integer accountId, Set<Integer> businessIds) {
		String url = this.coreUrl + "/v1/user/all?accountId=" + accountId + "&all=true";
		log.info("Getting getValidUserDTOs from business-service account {} and url {}", accountId, url);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		UserFilter filter = new UserFilter();
		filter.setUserId(userId);
		filter.setAccountId(accountId);
		filter.setBusinessIds(businessIds);
		filter.setExcludeAction(198);
		filter.setBeUser(0);
		HttpEntity<Object> httpEntity = new HttpEntity<>(filter, headers);
		long starTime = System.currentTimeMillis();
		ResponseEntity<UserResponse> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, UserResponse.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getValidUserDTOs", starTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful())
			throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
					"Error response from getValidUserDTOs for businessId" + businessIds);
		List<UserDTO> users = res.getBody().getUsers();
		Map<Integer, List<Integer>> businessUserMapping = new HashMap<>();
		if (CollectionUtils.isNotEmpty(users)) {
			users.forEach(user -> {
				if (user.getId() != userId) {
					if (user.getLocationIds() != null) {
						//Enterprise
						List<Integer> locationIds = user.getLocationIds();
						locationIds.stream().forEach(location-> {
							businessUserMapping.computeIfAbsent(location, l -> new ArrayList<>()).add(user.getId());
						});
					} else {
						//SMB
						businessIds.stream().forEach(location-> {
							businessUserMapping.computeIfAbsent(location, l -> new ArrayList<>()).add(user.getId());
						});
					}
				}
			});
		}
		return businessUserMapping;
	}
    
	@Override
	public Map<Integer, TeamAssigneeDto> getValidUserDTOs(Integer userId, Integer accountId, Integer businessId) {
		String url = this.coreUrl + "/v1/user/assignees?accountId=" + accountId + "&all=true";
		log.info("Getting assignees getValidUserDTOs from business-service account {} and url {} ", accountId, url);
		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		UserFilter filter = new UserFilter();
		filter.setUserId(userId);
		filter.setAccountId(accountId);
		filter.setBusinessIds(Collections.singleton(businessId));
		filter.setExcludeAction(198);
		filter.setBeUser(0);
		HttpEntity<Object> httpEntity = new HttpEntity<>(filter, headers);
		long starTime = System.currentTimeMillis();
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, String.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getValidUserDTOs", starTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful()) {
			log.info("error in fetching teams for enterprise from core service");
			return null;
		}

		List<TeamAssigneeDto> users = ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), TeamAssigneeDto.class);
		Map<Integer, TeamAssigneeDto> result = new HashMap<>();
		if (CollectionUtils.isNotEmpty(users)) {
			users.forEach(user -> {
				if (Objects.nonNull(user)) {
					result.put(user.getValue(), user);
				}
			});
		}
		return result;
	}

	@Override
	public List<String> getEmailIdsForPaymentNotifications(Integer accountId, Integer businessId) {
		String url = this.coreUrl + USER_ROUTE + "/payment/emailIds/" + businessId;

		HttpHeaders headers = new HttpHeaders();
		headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set(Constants.ACC_ID_HEADER, String.valueOf(accountId));
		HttpEntity<Void> entity = new HttpEntity<>(null, headers);
		long starTime = System.currentTimeMillis();
		ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
		long endTime = System.currentTimeMillis();
		LogUtil.logExecutionTime("getEmailIdsForPaymentNotifications", starTime, endTime);
		if (!res.getStatusCode().is2xxSuccessful()) {
			throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR, "Error response from business-service while fetching emailIds from core " + url);

		}
		return ControllerUtil.convertJsonStringInListObject(ControllerUtil.getJsonTextFromObject(res.getBody()), String.class);
	}

    @Override
    public Map<Integer, List<Integer>> getAccessibleLocationsOfAUserForAnAccount(Integer accountId,
            List<Integer> userIds) {
        log.info("getAccessibleLocationsOfAUserForAnAccount called with accountId : {} and userIds : {}", accountId,
                userIds);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        Map<String, List<Integer>> request = new HashMap<>(1);
        request.put("userIds", userIds);
        HttpEntity<Map<String, List<Integer>>> requestEntity = new HttpEntity<>(request, headers);
        String url = coreUrl + "/v1/business/locationsAccessByUsers/" + accountId;
        ResponseEntity<Map<Integer, List<Integer>>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
                requestEntity, new ParameterizedTypeReference<Map<Integer, List<Integer>>>() {
                });
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("getAccessibleLocationsOfAUserForAnAccount fetched successfully");
            return responseEntity.getBody();
        }
        log.error("error in getAccessibleLocationsOfAUserForAnAccount");
        throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
                "Error response from business-service while fetching getAccessibleLocationsOfAUserForAnAccount from core "
                        + url);
    }

    @Override
    public List<Integer> getTeamIdsOfAUserForAnAccount(Integer accountId, Integer userId) {
        log.info("getTeamIdsOfAUserForAnAccount called with accountId : {} and userId : {}", accountId, userId);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        String url = coreUrl + "/v1/team/" + accountId + "/" + userId;
        ResponseEntity<List<Integer>> responseEntity = restTemplate.exchange(url, HttpMethod.GET,
                new HttpEntity<>(headers), new ParameterizedTypeReference<List<Integer>>() {
                });
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            log.info("getTeamIdsOfAUserForAnAccount fetched successfully");
            return responseEntity.getBody();
        }
        log.error("error in getTeamIdsOfAUserForAnAccount");
        throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
                "Error response from business-service while fetching getTeamIdsOfAUserForAnAccount from core " + url);
    }
    
	@Override
	@AuditRestCall
	public UsersResponse getUsers(List<Integer> userIds, List<String> emailIds) {
		String url = coreUrl + "/v1/user/fetchUsers";
		HttpHeaders headers = new HttpHeaders();
	    headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
	    headers.setContentType(MediaType.APPLICATION_JSON);
	    UserFilter filter = new UserFilter();	
	    filter.setUserIds( !CollectionUtils.isEmpty(userIds)? new HashSet<>(userIds) : null);
	    filter.setEmailIds( !CollectionUtils.isEmpty(emailIds) ? new HashSet<>(emailIds) : null);
	    
	    HttpEntity<Object> httpEntity = new HttpEntity<>(filter, headers);
	    
		ResponseEntity<UsersResponse> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, UsersResponse.class);
		
		if(!res.getStatusCode().is2xxSuccessful()) {
			 throw new BusinessException(ErrorCode.USER_NOT_FOUND,
		                "Error response from business-service while fetching users - " + url);
		}
		
		return res.getBody();
	}
    
	@Override
	@AuditRestCall
	public UserDTO getUserAndBusinessAssociation(Integer userId, Integer businessId) {
		if(Objects.nonNull(userId) && Objects.nonNull(businessId)) {
			log.info("checking user and business association for userId: {} and businessId: {}",userId,businessId);
			String url = coreUrl + USER_ROUTE + "/id/"+userId;
			HttpHeaders headers = new HttpHeaders();
			headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.set(Constants.BUSINESS_ID, String.valueOf(businessId));
			HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
			ResponseEntity<String> res = restTemplate.exchange(url, HttpMethod.GET, httpEntity, String.class);
			if (!res.getStatusCode().is2xxSuccessful())
				throw new BusinessException(ErrorCode.HTTP_CONNECTION_ERROR,
						"No user business association");
			return ControllerUtil.getObjectFromJsonText(ControllerUtil.getJsonTextFromObject(res.getBody()),UserDTO.class);
		}
		return null;
	}
}
