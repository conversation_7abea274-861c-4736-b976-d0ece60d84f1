package com.birdeye.messenger.external.service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import com.birdeye.messenger.dto.Base64File;
import com.birdeye.messenger.exception.MessengerException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DoupServiceImpl implements DoupService {

	private final RestTemplate restTemplate;
	
	@Value("${doup.service.url}")
    private String doupServiceUrl;

	/*
	 * This method uploads a base64 encoded file for a given account ID.
	 * and this is different from platform api because it will return
	 * the same file name in cdn link without any extra text
	 * 
	 * request param:
	 * - handleSpecialCharacters: handle special characters in file name if true
	 * - isCdn: will return aws link if false, otherwise it will return the cdn link
	 */
	@Override
	public String uploadBase64FileForBusiness(Integer accountId, Base64File base64File) {
		String url = String.format("%s/doup/upload/file/%d?handleSpecialCharacters=false&isCdn=true", doupServiceUrl, accountId);

	    try {
	        HttpHeaders headers = new HttpHeaders();
	        headers.setContentType(MediaType.APPLICATION_JSON);
	        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

	        HttpEntity<List<Base64File>> entity = new HttpEntity<>(Arrays.asList(base64File), headers);

	        log.info("Calling Doup file upload API for accountId: {}", accountId);

	        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
	        
			if (responseEntity.getStatusCode().is2xxSuccessful()) {
				return responseEntity.getBody();
			} else {
				log.error("Media upload request failed with status: {}", responseEntity.getStatusCode());
				throw new MessengerException("Media upload rest call failed");
			}
	    } catch (Exception e) {
	        log.error("Error uploading base64 file for accountId: {}", accountId, e);
	        throw new RuntimeException("Media upload request failed", e);
	    }
	}

}
