package com.birdeye.messenger.external.service;

import com.birdeye.messenger.util.SecureString;
import com.birdeye.messenger.util.SecureStringUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(value = JsonInclude.Include.NON_EMPTY)
public class KontactoLookupRequest {

	@SecureString
	String phone;
	String businessTfn;
	Integer lastSentBusinessId;

	@Override
	public String toString() {
		return SecureStringUtil.buildSecureToString(this);
	}
	
}
