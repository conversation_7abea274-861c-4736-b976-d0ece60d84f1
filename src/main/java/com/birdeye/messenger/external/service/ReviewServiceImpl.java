package com.birdeye.messenger.external.service;


import com.birdeye.messenger.dto.ReviewAttributionEvent;
import com.birdeye.messenger.enums.ErrorCode;
import com.birdeye.messenger.exception.MessengerException;
import com.birdeye.messenger.sro.ReviewEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewServiceImpl implements ReviewService {

    private final RestTemplate restTemplate;

    @Value("${review.service.endpoint}")
    private String endpoint;

    @Override
    public void attributeReviews(ReviewAttributionEvent event) {
        String reviewAttributionUrl = "/reviewer/customer/attribution/process-manual-attribution";
        String url = endpoint + reviewAttributionUrl;
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(event, headers);
        ResponseEntity<Void> res = restTemplate.exchange(url, HttpMethod.POST, httpEntity, Void.class);
        if (!res.getStatusCode().is2xxSuccessful()) {
            log.error("attributeReviews: review attribution failed, request body {}, response from review-service {}", event, res);
            throw new MessengerException(ErrorCode.REVIEW_LINK_UNLINK_FAILED);
        }
    }

    @Override
    public ReviewEvent findById(Integer reviewId) {
        String findByIdUrl = "/reviewer/customer/attribution/messenger/" + reviewId;
        String url = endpoint + findByIdUrl;
        ResponseEntity<ReviewEvent> res = restTemplate.getForEntity(url, ReviewEvent.class);
        if (!res.getStatusCode().is2xxSuccessful()) {
            String failureReason = "error response from review-service while linking";
            log.error("findReviewById: service call error/invalid request, request reviewId {}, response from review-service {}", reviewId, res);
            if(res.getStatusCode().equals(HttpStatus.BAD_REQUEST)) {
                failureReason = String.format("in case of a competitor review, this review [ %d ] should not get attributed either via reviews or via Inbox", reviewId);
                log.error("findReviewById: in case of a competitor review, this review {} should not get attributed either via reviews or via Inbox", reviewId);
            }
            if(res.getStatusCode().equals(HttpStatus.NOT_ACCEPTABLE)) {
                failureReason = String.format("invalid reviewId [ %d ] ", reviewId);
                log.error("findReviewById: invalid reviewId {}", reviewId);
            }
            throw new MessengerException(ErrorCode.REVIEW_LINK_UNLINK_FAILED + " " + failureReason);
        }
        return res.getBody();
    }
}
