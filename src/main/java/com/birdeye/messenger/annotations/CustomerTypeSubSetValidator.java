/**
 * 
 */
package com.birdeye.messenger.annotations;

import java.util.Arrays;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import com.birdeye.messenger.enums.ContactState;

/**
 * <AUTHOR>
 *
 */
public class CustomerTypeSubSetValidator implements ConstraintValidator<CustomerTypeSubset, ContactState> {
    private ContactState[] subset;

    @Override
    public void initialize(CustomerTypeSubset constraint) {
        this.subset = constraint.anyOf();
    }

    @Override
    public boolean isValid(ContactState value, ConstraintValidatorContext context) {
        return value == null || Arrays.asList(subset).contains(value);
    }
}