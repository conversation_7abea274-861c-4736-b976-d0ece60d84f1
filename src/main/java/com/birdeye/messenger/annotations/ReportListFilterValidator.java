/**
 * 
 */
package com.birdeye.messenger.annotations;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import org.apache.commons.collections4.CollectionUtils;

/**
 * <AUTHOR>
 *
 */
public class ReportListFilterValidator implements ConstraintValidator<ListValidator,List<Integer>> {

    @Override
    public boolean isValid(List<Integer> value, ConstraintValidatorContext context) {
        
        return CollectionUtils.isEmpty(value) || value.stream().filter(v->(v<0 || v>10)).collect(Collectors.toList()).size()<=0;
   
    }

}
