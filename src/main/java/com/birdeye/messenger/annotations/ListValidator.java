/**
 * 
 */
package com.birdeye.messenger.annotations;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;


/**
 * <AUTHOR>
 *
 */
@Constraint(validatedBy = ReportListFilterValidator.class)
@Retention(RetentionPolicy.RUNTIME)
public @interface ListValidator {
    String message() default "Invalid values";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
