package com.birdeye.messenger.annotations;

import com.birdeye.messenger.enums.DbType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(value = {ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataSourceType {

    DbType dataSource() default DbType.MASTER;

}
