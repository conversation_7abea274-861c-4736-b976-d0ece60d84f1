package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.sro.Identity;

/**
 * <AUTHOR>
 *
 */
public class IdentityConvertor extends Convertor<Identity, IdentityDTO> {

	public IdentityConvertor() {
		super((from)-> {
			return new IdentityDTO(from.getId(), from.getName(), from.getType(), from.getEmailId());
		}, (from)->{
			return new Identity(from.getId(), from.getName(), from.getType(), from.getEmailId());
		});
	}

}
