package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.sro.AssignmentResponseV2;

/**
 * <AUTHOR>
 *
 */
public class AssignmentResponseV2Convertor extends Convertor<ConversationAssignmentResponseDTO, AssignmentResponseV2>{

	public AssignmentResponseV2Convertor() {
		super((f)->{
			AssignmentResponseV2 response = new AssignmentResponseV2();
			response.setAssigner(f.getAssigner());
			if(f.getFrom()!=null) {
				response.setFrom(new IdentityConvertor()._convert(f.getFrom()));
			}
			if(f.getTo()!=null) {
				response.setTo(new IdentityConvertor()._convert(f.getTo()));
			}
			response.setMcId(f.getMcId());
			return response;
		}, null);
	}

}
