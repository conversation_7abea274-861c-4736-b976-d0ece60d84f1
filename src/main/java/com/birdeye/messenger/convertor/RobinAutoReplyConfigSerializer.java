package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dao.entity.robin.RobinAutoReplyConfig;
import com.birdeye.messenger.enums.Source;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class RobinAutoReplyConfigSerializer extends StdSerializer<RobinAutoReplyConfig> {
    protected RobinAutoReplyConfigSerializer() {
        super(RobinAutoReplyConfig.class);
    }

    @Override
    public void serialize(RobinAutoReplyConfig config, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        jsonGenerator.writeStartObject();
        jsonGenerator.writeNumberField("businessId", config.getBusinessId());
        jsonGenerator.writeStringField("channel", config.getChannel());
        jsonGenerator.writeBooleanField("enableRobin", config.isEnableRobin());
        // Exclude fields based on channel value
        String channel = config.getChannel();
        if (!(Source.GOOGLE.name().equals(channel) || Source.VOICE_CALL.name().equals(channel) || Source.APPLE.name().equals(channel))) {
            jsonGenerator.writeBooleanField("enableAutoReplyInsideBusinessHours", config.isEnableAutoReplyInsideBusinessHours());
            jsonGenerator.writeStringField("autoReplyInsideBusinessHours", config.getAutoReplyInsideBusinessHours());
            jsonGenerator.writeBooleanField("enableAutoReplyOutsideBusinessHours", config.isEnableAutoReplyOutsideBusinessHours());
            jsonGenerator.writeStringField("autoReplyOutsideBusinessHours", config.getAutoReplyOutsideBusinessHours());
        }
        jsonGenerator.writeObjectField("createdAt", config.getCreatedAt());
        jsonGenerator.writeObjectField("updatedAt", config.getUpdatedAt());
        jsonGenerator.writeEndObject();
    }
}
