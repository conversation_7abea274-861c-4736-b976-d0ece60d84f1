package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.dto.IdentityDTO;
import com.birdeye.messenger.sro.AssignmentRequest;

/**
 * <AUTHOR>
 *
 */
public class AssignmentRequestConvertor extends Convertor<AssignmentRequest, ConversationAssignmentRequestDTO>{

	public AssignmentRequestConvertor() {
		super(from-> {
			ConversationAssignmentRequestDTO to = new ConversationAssignmentRequestDTO();
			to.setMcId(from.getMcId());
			to.setDoNotNotify(from.isDoNotNotify());
			if(from.getFrom()!=null) {
				to.setFrom(new IdentityDTO(from.getFrom().getId(), from.getFrom().getName(), "U", from.getFrom().getEmailId()));
			}
			if(from.getTo()!=null) {
				to.setTo(new IdentityDTO(from.getTo().getId(), from.getTo().getName(), "U", from.getTo().getEmailId()));
			}
			return to;
		}, null);
	}

}
