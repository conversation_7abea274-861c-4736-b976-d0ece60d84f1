package com.birdeye.messenger.convertor;

import java.util.function.Function;

/**
 * <AUTHOR>
 *
 */
public abstract class Convertor<F,T> {
	
	private Function<F, T> forwardConvertor;
	
	private Function<T, F> backwardConvertor;
	
	public Convertor(Function<F, T> forward,Function<T, F> backward) {
		this.forwardConvertor = forward;
		this.backwardConvertor = backward;
	}
	
	public T convert(F f) {
		return forwardConvertor.apply(f);
	}
	
	public F _convert(T t) {
		return backwardConvertor.apply(t);
	}
}
