package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dto.ConversationAssignmentRequestDTO;
import com.birdeye.messenger.sro.AssignmentRequestV2;

/**
 * <AUTHOR>
 *
 */
public class AssignmentRequestV2Convertor extends Convertor<AssignmentRequestV2, ConversationAssignmentRequestDTO>{

	public AssignmentRequestV2Convertor() {
		super((from)-> {
			ConversationAssignmentRequestDTO to = new ConversationAssignmentRequestDTO();
			to.setMcId(from.getMcId());
			
			if(from.getFrom()!=null) {
				to.setFrom(new IdentityConvertor().convert(from.getFrom()));
			}
			
			if(from.getTo()!=null) {
				to.setTo(new IdentityConvertor().convert(from.getTo()));
			}
			
			to.setDoNotNotify(from.isDoNotNotify());
			
			return to;
		}, null);
	}

}
