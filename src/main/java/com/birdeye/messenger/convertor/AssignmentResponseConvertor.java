package com.birdeye.messenger.convertor;

import com.birdeye.messenger.dto.ConversationAssignmentResponseDTO;
import com.birdeye.messenger.sro.AssignmentResponse;

/**
 * <AUTHOR>
 *
 */
public class AssignmentResponseConvertor extends Convertor<ConversationAssignmentResponseDTO, AssignmentResponse>{

	public AssignmentResponseConvertor() {
		super(from -> {
			AssignmentResponse response = new AssignmentResponse();
			response.setMcId(from.getMcId());
			response.setAssignedFrom(from.getFrom()!=null?from.getFrom().getId():null);
			response.setAssignedTo(from.getTo()!=null?from.getTo().getId():null);
			response.setAssigner(from.getAssigner());
			response.setMcId(from.getMcId());
			return response;
		}, null);
	}	
}
