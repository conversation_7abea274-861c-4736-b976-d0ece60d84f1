package com.birdeye.messenger.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import jakarta.validation.Valid;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SerializationUtils;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversationRequest implements Serializable {

	private static final long serialVersionUID = -7275830774467732076L;

	Integer startIndex = 0;
	Integer count = 25;
	Integer moveConversationToTop;
	List<String> excludedConversationType;
	@Valid
	ConversationFilter filters = new ConversationFilter();
	String assignmentType;
	@JsonProperty("isWeb")
	boolean isWeb;
	private boolean excludeUnattributedConversation=false;

	private ModuleAccess access = new ModuleAccess();

	ConversationTidyUpFilter tidyUpFilters;
	private boolean reserveWithGoogle=false;

	private String pollingMcids;
	private boolean pollingCall;
	private Long 

	public ConversationRequest() {}

	public ConversationRequest(NotificationRequest request, boolean nullifyLastMessageTime) {
		this.startIndex = request.getStartIndex();
		this.count = request.getCount();
		//this.moveConversationToTop = request.getConversationId();
		this.filters = SerializationUtils.clone(request.getFilters()); //TODO : check its performance
		this.isWeb=request.isWeb();
		if(request.getTidyUpFilters()!=null) {
			tidyUpFilters = request.getTidyUpFilters();
		}
		if(nullifyLastMessageTime && this.filters!=null) {
			this.filters.setLastMessageTime(null);
		}
		access = request.getAccess();
		if(Objects.nonNull(request.getFilters()) && Objects.nonNull(request.getFilters().getInboxSnapShotTime())){
			if(CollectionUtils.isNotEmpty(request.getRenderedConversationIds())){
				this.filters.setRenderedConversationIds(request.getRenderedConversationIds());
			}
			this.filters.setInboxSnapShotTime(request.getFilters().getInboxSnapShotTime());
		}
	}

	public ConversationRequest(ConversationRequest conversationRequest) {
		this.startIndex = conversationRequest.getStartIndex();
		this.count = conversationRequest.getCount();
		this.filters = SerializationUtils.clone(conversationRequest.getFilters()); //TODO : check its performance
		this.isWeb=conversationRequest.isWeb();
		if(conversationRequest.getTidyUpFilters()!=null) {
			this.tidyUpFilters = conversationRequest.getTidyUpFilters();
		}
		this.access = conversationRequest.getAccess();
	}
}
